<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/temp" />
      <excludeFolder url="file://$MODULE_DIR$/.tmp" />
      <excludeFolder url="file://$MODULE_DIR$/tmp" />
      <excludeFolder url="file://$MODULE_DIR$/.yarn" />
      <excludeFolder url="file://$MODULE_DIR$/public/core" />
      <excludeFolder url="file://$MODULE_DIR$/public/ui" />
      <excludeFolder url="file://$MODULE_DIR$/public/js/pdf-js" />
      <excludeFolder url="file://$MODULE_DIR$/../../sw-website-services/Sources/video/games" />
      <excludeFolder url="file://$MODULE_DIR$/../../sw-website-services/Sources/video/games/sw_tigome" />
      <excludeFolder url="file://$MODULE_DIR$/public/site/styles/css" />
      <excludeFolder url="file://$MODULE_DIR$/public/styles/css" />
      <excludeFolder url="file://$MODULE_DIR$/public/styles" />
      <excludeFolder url="file://$MODULE_DIR$/test" />
      <excludePattern pattern="*.css" />
      <excludePattern pattern="*.css.map" />
      <excludePattern pattern="*.min.css" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="swiper" level="application" />
    <orderEntry type="library" name="bootstrap" level="application" />
    <orderEntry type="library" name="bootstrap" level="application" />
    <orderEntry type="library" name="bootstrap" level="application" />
  </component>
</module>