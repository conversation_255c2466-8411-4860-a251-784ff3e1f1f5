<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectTasksOptions">
    <TaskOptions isEnabled="false">
      <option name="arguments" value="$FileName$:$FileNameWithoutExtension$.css" />
      <option name="checkSyntaxErrors" value="true" />
      <option name="description" />
      <option name="exitCodeBehavior" value="ERROR" />
      <option name="fileExtension" value="scss" />
      <option name="immediateSync" value="true" />
      <option name="name" value="SCSS" />
      <option name="output" value="$FileNameWithoutExtension$.css:$FileNameWithoutExtension$.css.map" />
      <option name="outputFilters">
        <array />
      </option>
      <option name="outputFromStdout" value="false" />
      <option name="program" value="sass" />
      <option name="runOnExternalChanges" value="false" />
      <option name="scopeName" value="Current File" />
      <option name="trackOnlyRoot" value="true" />
      <option name="workingDir" value="$PROJECT_DIR$/public/landing/teller-of-tales/assets/styles" />
      <envs />
    </TaskOptions>
    <TaskOptions isEnabled="false">
      <option name="arguments" value="-i $FileName$ -o $FileNameWithoutExtension$.min.css" />
      <option name="checkSyntaxErrors" value="true" />
      <option name="description" />
      <option name="exitCodeBehavior" value="ERROR" />
      <option name="fileExtension" value="css" />
      <option name="immediateSync" value="true" />
      <option name="name" value="CSSO CSS Optimizer" />
      <option name="output" value="$FileNameWithoutExtension$.min.css" />
      <option name="outputFilters">
        <array />
      </option>
      <option name="outputFromStdout" value="false" />
      <option name="program" value="csso" />
      <option name="runOnExternalChanges" value="false" />
      <option name="scopeName" value="Project Files" />
      <option name="trackOnlyRoot" value="true" />
      <option name="workingDir" value="$FileDir$" />
      <envs />
    </TaskOptions>
  </component>
</project>