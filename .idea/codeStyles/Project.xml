<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <HTMLCodeStyleSettings>
      <option name="HTML_SPACE_INSIDE_EMPTY_TAG" value="true" />
      <option name="HTML_ENFORCE_QUOTES" value="true" />
    </HTMLCodeStyleSettings>
    <JSCodeStyleSettings version="0">
      <option name="FORCE_SEMICOLON_STYLE" value="true" />
      <option name="SPACE_BEFORE_FUNCTION_LEFT_PARENTH" value="false" />
      <option name="FORCE_QUOTE_STYlE" value="true" />
      <option name="ENFORCE_TRAILING_COMMA" value="Remove" />
      <option name="SPACES_WITHIN_OBJECT_LITERAL_BRACES" value="true" />
      <option name="SPACES_WITHIN_IMPORTS" value="true" />
    </JSCodeStyleSettings>
    <TypeScriptCodeStyleSettings version="0">
      <option name="FORCE_SEMICOLON_STYLE" value="true" />
      <option name="FIELD_PREFIX" value="" />
      <option name="SPACE_BEFORE_FUNCTION_LEFT_PARENTH" value="false" />
      <option name="FORCE_QUOTE_STYlE" value="true" />
      <option name="ENFORCE_TRAILING_COMMA" value="Remove" />
      <option name="SPACES_WITHIN_OBJECT_LITERAL_BRACES" value="true" />
      <option name="SPACES_WITHIN_IMPORTS" value="true" />
      <option name="IMPORT_PREFER_ABSOLUTE_PATH" value="TRUE" />
    </TypeScriptCodeStyleSettings>
    <VueCodeStyleSettings>
      <option name="INTERPOLATION_NEW_LINE_AFTER_START_DELIMITER" value="false" />
      <option name="INTERPOLATION_NEW_LINE_BEFORE_END_DELIMITER" value="false" />
    </VueCodeStyleSettings>
    <codeStyleSettings language="HTML">
      <option name="SOFT_MARGINS" value="120" />
      <indentOptions>
        <option name="CONTINUATION_INDENT_SIZE" value="4" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="JavaScript">
      <option name="SOFT_MARGINS" value="120" />
    </codeStyleSettings>
    <codeStyleSettings language="TypeScript">
      <option name="KEEP_FIRST_COLUMN_COMMENT" value="false" />
      <option name="KEEP_BLANK_LINES_IN_CODE" value="1" />
      <option name="ALIGN_MULTILINE_TERNARY_OPERATION" value="true" />
      <option name="ALIGN_MULTILINE_ARRAY_INITIALIZER_EXPRESSION" value="true" />
      <option name="CALL_PARAMETERS_WRAP" value="5" />
      <option name="METHOD_PARAMETERS_WRAP" value="5" />
      <option name="EXTENDS_LIST_WRAP" value="1" />
      <option name="EXTENDS_KEYWORD_WRAP" value="1" />
      <option name="METHOD_CALL_CHAIN_WRAP" value="5" />
      <option name="TERNARY_OPERATION_WRAP" value="5" />
      <option name="ARRAY_INITIALIZER_WRAP" value="5" />
      <option name="ARRAY_INITIALIZER_LBRACE_ON_NEXT_LINE" value="true" />
      <option name="ARRAY_INITIALIZER_RBRACE_ON_NEXT_LINE" value="true" />
      <option name="WRAP_COMMENTS" value="true" />
      <option name="IF_BRACE_FORCE" value="3" />
      <option name="DOWHILE_BRACE_FORCE" value="3" />
      <option name="WHILE_BRACE_FORCE" value="3" />
      <option name="FOR_BRACE_FORCE" value="3" />
      <option name="SOFT_MARGINS" value="120" />
    </codeStyleSettings>
    <codeStyleSettings language="Vue">
      <option name="SOFT_MARGINS" value="120" />
      <indentOptions>
        <option name="INDENT_SIZE" value="4" />
        <option name="TAB_SIZE" value="4" />
      </indentOptions>
    </codeStyleSettings>
  </code_scheme>
</component>