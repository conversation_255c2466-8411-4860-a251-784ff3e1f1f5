import moment from "moment";
import next from "next";
import { IncomingMessage, ServerResponse } from "http";

const { parse } = require("url");
// import express from "express";
const { createServer } = require("http");

const log = console.log;

const emailCronSchedule = "*/2 * * * * *"; // Every 2 seconds
async function emailCronJob() {
    try {
        log("email-log", moment().toISOString());
    } catch (err) {
        // error.code = 100300002;
        log(err, "An error occurred when executing email<PERSON><PERSON><PERSON><PERSON>");
        return Promise.reject(err);
    }
}

// cron.schedule(emailCronSchedule, email<PERSON>ronJob);
// log.info(`Emails cron is started with interval: ` + emailCronSchedule);

const port = parseInt(process.env.PORT as string, 10) || 3044;
const dev = process.env.NODE_ENV !== "production";
const nextServer = next({ dev });
const handle = nextServer.getRequestHandler();

nextServer.prepare().then(() => {
    // const server = express();
    //
    // server.all("*", (req, res) => {
    //     return handle(req, res);
    // });

    const server = createServer((req: IncomingMessage, res: ServerResponse) => {
        // Be sure to pass `true` as the second argument to `url.parse`.
        // This tells it to parse the query portion of the URL.
        const parsedUrl = parse(req.url, true);
        log("Server Processing Request URL", req.url);
        handle(req, res, parsedUrl);
    });

    // @ts-ignore
    server.listen(port, (err): void => {
        if (err) {
            log(err);
            throw err;
        }
        const info = `> Ready on http://localhost:${port}`;
        console.log(info);
        log(info);
    });
});
