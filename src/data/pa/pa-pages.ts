import { IProjectPages } from "models/site-pages";
import { EnumSitePathType } from "models/enum/system";
import { EnumLocale } from "locales/locales";
import { EnumPage } from "models/enum/page";

export const paPages = <IProjectPages>{
    [EnumPage.accountUserProfile]: {
        isActive: true,
        isLocalized: true,
        path: "/account/user-profile",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "User Profile" } }
    },
    [EnumPage.roadmapGames]: {
        isActive: true,
        isLocalized: true,
        path: "/roadmap",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Roadmap" } }
    },
    [EnumPage.roadmapGamesSkywind]: {
        isActive: true,
        isLocalized: true,
        path: "/roadmap/skywind",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Roadmap Skywind" } }
    },
    [EnumPage.roadmapGamesSlotFactory]: {
        isActive: true,
        isLocalized: true,
        path: "/roadmap/slot-factory",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Roadmap SlotFactory" } }
    },
    [EnumPage.login]: {
        isActive: true,
        isLocalized: true,
        path: "/login",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Login" } }
    },
    [EnumPage.register]: {
        isActive: true,
        isLocalized: true,
        path: "/sign-up",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Sign Up" } }
    },
    [EnumPage.logout]: {
        isActive: true,
        isLocalized: true,
        path: "/logout",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Logout" } }
    },
    [EnumPage.forgotPassword]: {
        isActive: true,
        isLocalized: true,
        path: "/forgot-password",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Forgot password" } }
    },
    [EnumPage.playPlayerGame]: {
        isActive: true,
        isLocalized: true,
        path: "/play/player/:gameId",
        type: EnumSitePathType.dynamic_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Plat Game" } }
    },
    [EnumPage.gamesList]: {
        isActive: true,
        isLocalized: true,
        path: "/games",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Games" } }
    },
    [EnumPage.lowRtpGames]: {
        isActive: true,
        isLocalized: true,
        path: "/low-rtp-games",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Low RTP Games" } }
    },
    [EnumPage.gameDetails]: {
        isActive: true,
        isLocalized: true,
        path: "/game/:gameId",
        type: EnumSitePathType.dynamic_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Game Profile" } }
    },
    [EnumPage.liveStudio]: {
        isActive: true,
        isLocalized: true,
        path: "/live-games",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Live Casino" } }
    },
    [EnumPage.supportDocs]: {
        isActive: true,
        isLocalized: true,
        path: "/support/docs",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Support documentation" } }
    },
    [EnumPage.tournamentLanding]: {
        isActive: true,
        isLocalized: true,
        path: "/tournament",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Tournament" } }
    },
    [EnumPage.newsList]: {
        isActive: true,
        isLocalized: true,
        path: "/news",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "News" } }
    },
    [EnumPage.newsArticle]: {
        isActive: true,
        isLocalized: true,
        path: "/news/:newsId",
        type: EnumSitePathType.dynamic_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "News Article" } }
    },
    [EnumPage.search]: {
        isActive: true,
        isLocalized: true,
        path: "/search",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Game Search" } }
    },
    [EnumPage.toolsFreeBets]: {
        isActive: true,
        isLocalized: true,
        path: "/tools/free-bets",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Free Bets" } }
    },
    [EnumPage.toolsLuckyEnvelopes]: {
        isActive: true,
        isLocalized: true,
        path: "/tools/lucky-envelopes",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Lucky Envelopes" } }
    },
    [EnumPage.toolsMustWinJackpots]: {
        isActive: true,
        isLocalized: true,
        path: "/tools/must-win-jackpot",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "MUST-WIN JACKPOT" } }
    },
    [EnumPage.toolsTournaments]: {
        isActive: true,
        isLocalized: true,
        path: "/tools/tournaments",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "IN GAME TOURNAMENTS" } }
    },
    [EnumPage.cookies]: {
        isActive: true,
        isLocalized: true,
        path: "/cookies",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Cookie Policy" } }
    },
    [EnumPage.privacy]: {
        isActive: true,
        isLocalized: true,
        path: "/policy",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Privacy Policy" } }
    },
    [EnumPage.contact]: {
        isActive: true,
        isLocalized: true,
        path: "/contact-us",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Contact Us" } }
    },
}
