import { EnumSitePathType } from "models/enum/system";
import { EnumLocale } from "locales/locales";
import { IProjectPages } from "models/site-pages";
import { EnumPage } from "models/enum/page";

export const sitePages = <IProjectPages>{
    [EnumPage.sitemap]: {
        isActive: true,
        isLocalized: false,
        path: "/sitemap.xml",
        type: EnumSitePathType.static_page,
        inSitemap: false,
        seoChangeFrequency: "never",
        seoPriority: 0.1,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "" } }
    },
    [EnumPage.home]: {
        isActive: true,
        isLocalized: true,
        path: "/home",
        type: EnumSitePathType.static_page,
        inSitemap: true,
        seoChangeFrequency: "daily",
        seoPriority: 0.9,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Best Gambling Software Provider" } }
    },
    [EnumPage.gamesList]: {
        isActive: true,
        isLocalized: true,
        path: "/games",
        type: EnumSitePathType.static_page,
        inSitemap: true,
        seoChangeFrequency: "daily",
        seoPriority: 0.9,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Best Online Casino Games to Play for Free" } }
    },
    [EnumPage.slotFactoryGamesList]: {
        isActive: true,
        isLocalized: true,
        path: "/slot-factory-games",
        type: EnumSitePathType.static_page,
        inSitemap: true,
        seoChangeFrequency: "daily",
        seoPriority: 0.9,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Best Online Casino Games to Play for Free" } }
    },
    [EnumPage.socialGamesList]: {
        isActive: true,
        isLocalized: true,
        path: "/social-games",
        type: EnumSitePathType.static_page,
        inSitemap: true,
        seoChangeFrequency: "daily",
        seoPriority: 0.9,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Best Online Casino Games to Play for Free" } }
    },
    [EnumPage.gameDetails]: {
        isActive: true,
        isLocalized: true,
        path: "/game/:gameId",
        type: EnumSitePathType.dynamic_page,
        inSitemap: true,
        seoChangeFrequency: "weekly",
        seoPriority: 0.7,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "" } }
    },
    [EnumPage.aboutUs]: {
        isActive: true,
        isLocalized: true,
        path: "/about-us",
        type: EnumSitePathType.static_page,
        inSitemap: true,
        seoChangeFrequency: "weekly",
        seoPriority: 0.7,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "About Us" } }
    },
    [EnumPage.liveStudio]: {
        isActive: true,
        isLocalized: true,
        path: "/live-studio",
        type: EnumSitePathType.static_page,
        inSitemap: true,
        seoChangeFrequency: "daily",
        seoPriority: 0.9,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Live Casino Games Online & Real Time Gaming" } }
    },
    [EnumPage.sports]: {
        isActive: true,
        isLocalized: true,
        path: "/sports",
        type: EnumSitePathType.static_page,
        inSitemap: true,
        seoChangeFrequency: "weekly",
        seoPriority: 0.7,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Best Online Sportsbook for Sports Betting" } }
    },
    [EnumPage.sw360]: {
        isActive: true,
        isLocalized: true,
        path: "/sw-360",
        type: EnumSitePathType.static_page,
        inSitemap: true,
        seoChangeFrequency: "weekly",
        seoPriority: 0.7,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Skywind 360 Online Gaming Industry" } }
    },
    [EnumPage.newsList]: {
        isActive: true,
        isLocalized: true,
        path: "/news",
        type: EnumSitePathType.static_page,
        inSitemap: true,
        seoChangeFrequency: "daily",
        seoPriority: 0.9,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Online Gaming Industry News" } }
    },
    [EnumPage.newsArticle]: {
        isActive: true,
        isLocalized: true,
        path: "/news/:newsId",
        type: EnumSitePathType.dynamic_page,
        inSitemap: true,
        seoChangeFrequency: "weekly",
        seoPriority: 0.7,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "" } }
    },
    [EnumPage.toolsLuckyEnvelopes]: {
        isActive: true,
        isLocalized: true,
        path: "/tools/lucky-envelopes",
        type: EnumSitePathType.static_page,
        inSitemap: true,
        seoChangeFrequency: "weekly",
        seoPriority: 0.7,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Lucky Envelopes in Online Gaming Industry" } }
    },
    [EnumPage.toolsTournaments]: {
        isActive: true,
        isLocalized: true,
        path: "/tools/tournaments",
        type: EnumSitePathType.static_page,
        inSitemap: true,
        seoChangeFrequency: "weekly",
        seoPriority: 0.7,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "In Game Tournament in Online Gaming Industry" } }
    },
    [EnumPage.toolsMustWinJackpots]: {
        isActive: true,
        isLocalized: true,
        path: "/tools/must-win-jackpots",
        type: EnumSitePathType.static_page,
        inSitemap: true,
        seoChangeFrequency: "weekly",
        seoPriority: 0.7,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "More opportunities. More engagement. " } }
    },
    [EnumPage.toolsFreeBets]: {
        isActive: true,
        isLocalized: true,
        path: "/tools/free-bets",
        type: EnumSitePathType.static_page,
        inSitemap: true,
        seoChangeFrequency: "weekly",
        seoPriority: 0.7,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Free Bets in Online Gaming Industry" } }
    },
    [EnumPage.toolsWuShiJackpot]: {
        isActive: true,
        isLocalized: true,
        path: "/tools/wu-shi-jackpot",
        type: EnumSitePathType.static_page,
        inSitemap: true,
        seoChangeFrequency: "weekly",
        seoPriority: 0.7,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Wu Shi Jackpot" } }
    },
    [EnumPage.contact]: {
        isActive: true,
        isLocalized: true,
        path: "/contact-us",
        type: EnumSitePathType.static_page,
        inSitemap: true,
        seoChangeFrequency: "weekly",
        seoPriority: 0.7,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Contact Us" } }
    },
    [EnumPage.cookies]: {
        isActive: true,
        isLocalized: true,
        path: "/cookies",
        type: EnumSitePathType.static_page,
        inSitemap: true,
        seoChangeFrequency: "weekly",
        seoPriority: 0.7,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Cookie Policy" } }
    },
    [EnumPage.privacy]: {
        isActive: true,
        isLocalized: true,
        path: "/policy",
        type: EnumSitePathType.static_page,
        inSitemap: true,
        seoChangeFrequency: "weekly",
        seoPriority: 0.7,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Privacy Policy" } }
    },
    [EnumPage.welcomeDealers]: {
        isActive: true,
        isLocalized: true,
        path: "/welcome-dealers",
        type: EnumSitePathType.static_page,
        inSitemap: true,
        seoChangeFrequency: "weekly",
        seoPriority: 0.7,
        title: { [EnumLocale.enUS]: { locale: EnumLocale.enUS, text: "Welcome Dealers" } }
    }
};
