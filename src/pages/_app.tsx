import Head from "next/head";
import { AppProps, NextWebVitalsMetric } from "next/app";
import * as React from "react";
import { createContext, useContext, useEffect } from "react";
import { DetectDevice } from "utils/detectDevice";
import AuthStore from "@stores/auth-store";
import AppStore from "@stores/app-store";
import { useRouter } from "next/router";
import MainLayout from "@components/layouts/mainLayout";
import { EnumLocale, getLocaleMessages } from "locales/locales";
import { withOrientationChange } from "react-device-detect";
import { observer } from "mobx-react-lite";

import "react-toastify/dist/ReactToastify.css";
import { usePageViews } from "nextjs-google-analytics";
import { SSRProvider } from "@react-aria/ssr";

interface State {
    authStore: typeof AuthStore;
    appStore: typeof AppStore;
}

export const MainAppContext = createContext<State>({
    authStore: AuthStore,
    appStore: AppStore
});

declare global {
    // noinspection JSUnusedGlobalSymbols
    interface Window {
        gtag?: any;
    }
}

export interface IAppProps extends AppProps {
    isLandscape: boolean;
    isPortrait: boolean;
}

const MainApp = ({ Component, pageProps, isLandscape, isPortrait }: IAppProps) => {
    const { authStore, appStore } = useContext(MainAppContext);

    const router = useRouter();

    const { locale } = router;
    const msg = getLocaleMessages(locale as EnumLocale).messages;

    // GA To track all pages views
    usePageViews();

    // in this case useEffect will execute only once because
    // it does not have any dependencies.
    useEffect(() => {
        let resizeTimer: number;
        // let scrollTimer: number;

        const resizeListener = () => {
            clearTimeout(resizeTimer);
            resizeTimer = window.setTimeout(function() {
                DetectDevice.setDomClasses(document.body, isLandscape, isPortrait);
            }, 250);
        };
        resizeListener();
        // set resize listener
        window.addEventListener("resize", resizeListener);

        if (window && location && location?.hostname === "localhost") {
            window.onunload = function() {
                debugger;
            };
        }
        // clean up function
        return () => {
            // remove resize listener
            window.removeEventListener("resize", resizeListener);
            // window.removeEventListener("scroll", scrollListener);
        };
    }, [appStore, isLandscape, isPortrait]);

    const siteDescription = "Skywind Group is a premium content provider for the iGaming industry, offering a multi-product portfolio that is innovative, regulated and mobile-focused";

    return (
        <>
            <Head>
                <title></title>
                <meta name="viewport"
                      content="width=device-width, initial-scale=1.0, shrink-to-fit=no, maximum-scale=5, minimum-scale=1" />
                <link rel="apple-touch-icon" sizes="180x180" href="/assets/favicon/apple-touch-icon.png" />
                <link rel="icon" type="image/png" sizes="32x32" href="/assets/favicon/favicon-32x32.png" />
                <link rel="icon" type="image/png" sizes="16x16" href="/assets/favicon/favicon-16x16.png" />
                <link rel="manifest" href="/assets/favicon/site.webmanifest" />
                <link rel="mask-icon" href="/assets/favicon/safari-pinned-tab.svg" color="#5bbad5" />
                <meta name="msapplication-TileColor" content="#da532c" />
                <meta name="theme-color" content="#ffffff" />
                <meta name="keywords" key={"meta-keywords"}
                      content="skywindgroup, gambling software,gambling software provider,best casino software,gaming provider,gaming platform,gaming software,gaming studios,gaming website,gaming technology,game provider,casino gaming software,online gaming companies,casino software companies,casino game companies,casino gaming,online gambling software,online gambling games,games provider,gambling product,gambling casino games,demo gaming,live gambling,live gaming,live casino games online,live games,online live games,real time gaming,online live casino,live casino games,gaming news,gamers news" />
                <meta name="keywords" key={"meta-description"} content={siteDescription} />
                <meta name="author" content={msg.skywindCompanyName} />
            </Head>
            <SSRProvider>
                <MainAppContext.Provider value={{ authStore, appStore }}>
                    <MainLayout>
                        <Component {...pageProps} />
                    </MainLayout>
                </MainAppContext.Provider>
            </SSRProvider>
        </>
    );
};

// Google Analytics WebVitals
// https://nextjs.org/docs/advanced-features/measuring-performance
// noinspection JSUnusedGlobalSymbols
export function reportWebVitals(metric: NextWebVitalsMetric) {
    const { id, name, value, label } = metric;
    if (typeof window !== "undefined" && typeof window.gtag !== "undefined") {
        window.gtag("event", name, {
            event_label: id,
            event_action: name,
            non_interaction: true,
            value: Math.round(name === "CLS" ? value * 1000 : value),
            event_category: label === "web-vital" ? "Web Vitals" : "Next.js custom metric"
        });
    }
    // console.log(metric);
}

export default withOrientationChange(observer(MainApp));
