import React from "react";
import { GetServerSideProps, NextPage } from "next";
import { appCdn } from "@stores/app-cdn";
import { observer } from "mobx-react-lite";
import CustomError from "@pages/custom-error";
import path from "path";
import { SanityProviderInstance } from "api/data-providers/sanity/sanityProvider";
import appServerConfig from "appServerConfig";

export interface IImagePageProps {
    //
}

const ImagePage: NextPage<IImagePageProps> = observer((/*{ ...props }*/) => {
    try {
        return null;
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (context) => {

    // const images = await appCdn.getAppImages();

    // https://cdn.sanity.io/images/usfaerfs/production/7cc52bdb43d40b0b5a1b8921c96c8800e68adac0-800x418.jpg
    // https://cdn.skywindgroup.com/cdn/file/b4/a5/b4a518deff9af35393552c9aa58a8d0516df0b8a.jpg

    const filePath: string | boolean = Array.isArray(context.query?.path) && context.query?.path.length === 1
                                       ? context.query?.path[0]
                                       : false;
    // console.log(filePath);
    if (!filePath || String(filePath).length < 40) {
        return {
            props: {},
            notFound: true
        };
    }

    const fileBasenameWithExt = path.basename(filePath);
    const fileExt = path.extname(fileBasenameWithExt);
    const fileBasename = path.basename(fileBasenameWithExt, fileExt).split("-").shift();

    if (!fileBasename || String(fileBasename).length < 40) {
        return {
            props: {},
            notFound: true
        };
    }


    const fileHash = fileBasename + fileExt;

    let redirectDestination = null;
    if (await appCdn.getAppImageSize(fileBasename)) {
        redirectDestination = "https://cdn.skywindgroup.com/cdn/sizes/" +
            fileBasename.slice(0, 2) + "/" + fileBasename.slice(2, 4) + "/" + fileBasename + "/image.webp";
    } else if (await appCdn.getAppFile(fileHash)) {
        redirectDestination = "https://cdn.skywindgroup.com/cdn/file/" +
            fileBasename.slice(0, 2) + "/" + fileBasename.slice(2, 4) + "/" + fileHash;
    } else {
        try {
            const asset = await SanityProviderInstance.getAssetById(fileBasename);
            if (asset?.url) {
                redirectDestination = asset.url;
                if (appServerConfig.cdnDomain) {
                    const url = new URL(asset.url);
                    url.host = appServerConfig.cdnDomain;
                    redirectDestination = url.href;
                }
            }
        } catch (e) {
        }
    }
    if (!redirectDestination) {
        return {
            props: {},
            notFound: true
        };
    }

    // This value is considered fresh for ten seconds (s-maxage=10).
    // If a request is repeated within the next 10 seconds, the previously
    // cached value will still be fresh. If the request is repeated before 59 seconds,
    // the cached value will be stale but still render (stale-while-revalidate=59).
    //
    // In the background, a revalidation request will be made to populate the cache
    // with a fresh value. If you refresh the page, you will see the new value.
    context.res.setHeader(
        "Cache-Control",
        `public, s-maxage=${24 * 3600}, stale-while-revalidate=${24 * 3600}`
    );

    return {
        // will be passed to the page component as props
        props: {},
        redirect: {
            destination: redirectDestination,
            permanent: true
        }
    };

};

export default ImagePage;
