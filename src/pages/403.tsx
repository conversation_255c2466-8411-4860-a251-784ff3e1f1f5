import Link from "next/link";
import React from "react";
import { GetServerSideProps } from "next";

// pages/403.js
export default function Custom403() {
    return (
        <section id="page403" className="page403">
            <div className="container-fluid">
                <div className="row">
                    <div className="col">
                        <div className="blockcenter">
                            <h2 className="title blockcenter__title" data-text="404 error">
                                Access <span className="accent">denied</span>
                            </h2>
                            <p className="blockcenter__offer">
                                Sorry, you don not have access to this page. Please contact Skywind support.
                            </p>
                            <Link href="/" passHref>
                                <a className="button button--primary blockcenter__btn">go home </a>
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    return { props: {} };
};

