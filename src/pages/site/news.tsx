import React, { useState } from "react";
import { observer } from "mobx-react-lite";
import { GetServerSideProps, NextPage } from "next";
import appConfig from "appConfig";
import { INewsExtended, IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import AppFooter from "@components/footers/app-footer";
import logger from "utils/logger";
import { AppContainer } from "@components/containers/app-container";
import AppHeader from "@components/headers/app-header";
import AppSections from "@components/sections/appSections";
import NewsList from "@components/news/newsList";
import HeaderContainer from "@components/headers/header-container";
import { SanityApi } from "utils/sanityApi";
import { ILocaleString } from "api/data-providers/sanity/schemas";
import { ProjectServiceInstance } from "api/services/projectService";
import CustomError from "@pages/custom-error";
import { useRouter } from "next/router";
import AppLoading from "@components/main/appLoading";
import { IPageProps } from "@pages/index";
import { SanityProviderInstance } from "api/data-providers/sanity/sanityProvider";
import { EnumRegionCode } from "models/enum/system";
import SeoSitePage from "@components/seo/seo-site-page";
import Head from "next/head";
import { CarouselJsonLd } from "next-seo";
import { useSeoCarouselNewsList } from "@hooks/use-seo-carousel-news-list";
import { useSiteTitle } from "@hooks/use-site-title";
import { useSitePageInfo } from "@hooks/use-site-page-info";
import { EnumPage } from "models/enum/page";

const log = logger("page--website-news");

export interface IPageSiteNewsProps extends IPageProps {
    footer: IWebsiteFooter;
    allNews: string;
}

const PageSiteWebsiteNews: NextPage<IPageSiteNewsProps> = observer((
    { footer, allNews, error, hostWithPort }) => {
    try {
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        const [news] = useState(JSON.parse(allNews) as INewsExtended[]);

        const languageCode = appConfig.systemSettings.defaultLanguageCode;

        const { pageTitle, pageUrlAbsolute, pageUrlRelative } = useSitePageInfo(EnumPage.newsList, {});

        const pageDescription = "Stay Connected! Here's what's new in Skywind. ";
        const latestNewsTitle = (Array.isArray(news) && news.length > 0 && news[0].title
                                 ? SanityApi.getLocale(news[0].title, languageCode) : "") + " " +
            (Array.isArray(news) && news.length > 0 && news[0].highlightedTitle
             ? SanityApi.getLocale(news[0].highlightedTitle, languageCode) : "");

        const headerImageUrl = Array.isArray(news) && news.length > 0 && news[0].newsPreviewImageUrl
                               ? news[0].newsPreviewImageUrl
                               : "/assets/site/images/header/banner/about-header-banner.jpg";
        const pageImage = {
            url: headerImageUrl,
            width: 1920,
            height: 1080
        };

        const newsCarousel = useSeoCarouselNewsList(news, languageCode);

        const { siteTitle } = useSiteTitle(pageTitle);

        return (
            <>
                <SeoSitePage pageTitle={pageTitle} pageDescription={pageDescription + " " + latestNewsTitle}
                             pageUrlAbsolute={pageUrlAbsolute} pageUrlRelative={pageUrlRelative}
                             pageImage={pageImage} />
                {Array.isArray(newsCarousel) && newsCarousel.length > 0 &&
                    <CarouselJsonLd
                        ofType={"movie"}
                        data={newsCarousel}
                    />
                }
                <Head>
                    <title>{siteTitle}</title>
                    <meta name="keywords"
                          content="gambling software,gambling software provider,best casino software,gaming news,gamers news" />
                </Head>
                <AppContainer>
                    {Array.isArray(news) && news.length > 0 ?
                     <AppHeader
                         className={"header-news"}
                         // title={SanityApi.getLocale(news[0].title as ILocaleString, languageCode)}
                         imageUrl={
                             news[0].newsPreviewImageUrl
                         }
                         // videoUrl={"https://eu.skywindgroup.com/cdn/file/d1/a8/d1a8564a42844f22eb5be11723abf54b6eb59edf.mp4"}
                     >
                         <HeaderContainer
                             classNameHeaderBanner={"news-header-banner-content bottom"}
                             title={SanityApi.getLocale(news[0].highlightedTitle as ILocaleString, languageCode)}
                             accentTitle={SanityApi.getLocale(news[0].title as ILocaleString, languageCode)}
                             isBottom={true}
                         />
                     </AppHeader>
                                                            : null}
                    <AppSections className={"container"}>
                        <HeaderContainer
                            classNameHeaderBanner={"header-banner-content-news-title"}
                            title={"Latest News"}
                            contentText={pageDescription}
                            isBottom={true}
                        />
                        <NewsList news={news} languageCode={languageCode} />
                    </AppSections>
                    <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                </AppContainer>
            </>
        );
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;
        const footer = await ProjectServiceInstance.getWebsiteFooterData(regionCode, languageCode);
        const allNews = await SanityProviderInstance.getNews(EnumRegionCode.eu,
            languageCode/*, String(params?.newsId)*/);
        return {
            // const footer = async

            props: { footer, allNews: JSON.stringify(allNews) } // will be passed to the page
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PageSiteWebsiteNews;
