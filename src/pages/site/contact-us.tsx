import React, { useRef } from "react";
import { observer } from "mobx-react-lite";
import { GetServerSideProps, NextPage } from "next";
import appConfig from "appConfig";
import { IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import AppFooter from "@components/footers/app-footer";
import AppImg from "@components/main/appImg";
import logger from "utils/logger";
import { AppContainer } from "@components/containers/app-container";
import AppHeader from "@components/headers/app-header";
import AppSections from "@components/sections/appSections";
import AppLink from "@components/main/appLink";
import { ProjectServiceInstance } from "api/services/projectService";
import CustomError from "@pages/custom-error";
import { useRouter } from "next/router";
import AppLoading from "@components/main/appLoading";
import { IPageProps } from "@pages/index";
import SeoSitePage from "@components/seo/seo-site-page";
import Head from "next/head";
import { useSiteTitle } from "@hooks/use-site-title";
import { useSitePageInfo } from "@hooks/use-site-page-info";
import { EnumPage } from "models/enum/page";
import AppVideo from "@components/main/appVideo";
import FormContactUs from "@components/forms/contact-us/form-contact-us";

const log = logger("page--website-contact-us");

export interface IPageSiteContactUsProps extends IPageProps {
    footer: IWebsiteFooter;
}

const PageSiteContactUs: NextPage<IPageSiteContactUsProps> = observer((
    { appServerConfig, footer, error, hostWithPort }) => {
    try {
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        const languageCode = appConfig.systemSettings.defaultLanguageCode;

        const { pageTitle, pageUrlAbsolute, pageUrlRelative } = useSitePageInfo(EnumPage.contact, {});

        const pageDescription = "For enquiries about any Skywind products and services please click on the relevant icon below to be directed to the right team.";

        const pageImage = {
            url: "/assets/site/images/header/banner/about-header-banner.jpg",
            width: 1920,
            height: 1080
        };

        const { siteTitle } = useSiteTitle(pageTitle);

        const playerRef = useRef<HTMLVideoElement>(null);

        return (
            <>
                <SeoSitePage pageTitle={pageTitle} pageDescription={pageDescription}
                             pageUrlAbsolute={pageUrlAbsolute} pageUrlRelative={pageUrlRelative}
                             pageImage={pageImage} />
                <Head>
                    <title>{siteTitle}</title>
                </Head>
                <AppContainer>
                    <AppHeader
                        className={"empty"}
                        // title={"Contact"}
                        // accentTitle={"Us"}
                        // imageUrl={
                        //     "https://cdn.sanity.io/images/usfaerfs/production/098d82dfd156e700f19b9ca711760376ed82279f-1920x990.jpg"
                        // }
                        // videoUrl={"https://cdn.skywindgroup.com/cdn/file/d1/a8/d1a8564a42844f22eb5be11723abf54b6eb59edf.mp4"}
                    >
                        {/*<HeaderContainer accentTitle={"Contact Us"} isBottom={true} />*/}
                    </AppHeader>
                    <div className="banner-video">
                        <AppVideo className="is-tablet-up"
                                  videoUrl="https://cdn.sanity.io/files/usfaerfs/production/ab3efd7b9aad55389d964bbc0e65615f7df9e8c8.mp4"
                                  autoPlay={true} loop={true} videoRef={playerRef} title={""} />
                        <AppVideo className="is-mobile"
                                  videoUrl="https://cdn.sanity.io/files/usfaerfs/production/4e0ad527be608d364d93f2a5f4664917e102168c.mp4"
                                  autoPlay={true} loop={true} videoRef={playerRef} title={""} />
                    </div>
                    <AppSections className={"container"}>
                        <section className="section-contact-us">
                            {/*<div className={"section-title"}>*/}
                            {/*    Contact <span className={"accent"}>Us</span>*/}
                            {/*</div>*/}
                            <div className="wrap">
                                <div className={"sw-products"}>
                                    {/*<div className={"sw-products-title"}>Skywind products and services</div>*/}
                                    <div className={"sw-section-description"}>
                                        For enquiries about any Skywind products and services please click on the
                                        relevant
                                        icon
                                        below to be directed to the right team.
                                    </div>
                                    <div className={"sw-products-items"}>
                                        <AppLink href={"mailto:<EMAIL>?subject=Skywind sales"}>
                                            <AppImg src={"/assets/site/images/products/sales.svg"} alt={"Sales"} />
                                            <div className={"item-title"}>Sales</div>
                                        </AppLink>
                                        <AppLink href={"mailto:<EMAIL>?subject=skywind 360"}>
                                            <AppImg src={"/assets/site/images/products/sw360.svg"}
                                                    alt={"SW 360 Ventures"} />
                                            <div className={"item-title"}>SW 360 Ventures</div>
                                        </AppLink>
                                        <AppLink href={"mailto:<EMAIL>"}>
                                            <AppImg src={"/assets/site/images/products/careers.svg"} alt={"Careers"} />
                                            <div className={"item-title"}>Careers</div>
                                        </AppLink>
                                        <AppLink href={"mailto:<EMAIL>?subject=Skywind games"}>
                                            <AppImg src={"/assets/site/images/products/games.svg"} alt={"Games"} />
                                            <div className={"item-title"}>Games</div>
                                        </AppLink>
                                        <AppLink href={"mailto:<EMAIL>?subject=Press/Media"}>
                                            <AppImg src={"/assets/site/images/products/press.svg"}
                                                    alt={"Press/Media"} />
                                            <div className={"item-title"}>Press/Media</div>
                                        </AppLink>
                                        <AppLink href={"mailto:<EMAIL>?subject=Live"}>
                                            <AppImg src={"/assets/site/images/products/live.svg"} alt={"Live Casino"} />
                                            <div className={"item-title"}>Live Casino</div>
                                        </AppLink>
                                        <AppLink href={"mailto:<EMAIL>?subject=Skywind sports"}>
                                            <AppImg src={"/assets/site/images/products/sports.svg"} alt={"Sports"} />
                                            <div className={"item-title"}>Sports</div>
                                        </AppLink>
                                        <AppLink href={"mailto:<EMAIL>?subject=Skywind Support"}>
                                            <AppImg
                                                src={"/assets/site/images/products/engagement.svg"}
                                                alt={"Support"}
                                            />
                                            <div className={"item-title"}>Support</div>
                                        </AppLink>
                                        <AppLink href={"mailto:<EMAIL>?subject=Affiliates"}>
                                            <AppImg src={"/assets/site/images/products/affiliates.svg"}
                                                    alt={"Affiliates"} />
                                            <div className={"item-title"}>Affiliates</div>
                                        </AppLink>
                                    </div>
                                </div>
                                <div className={"sw-contact-us"}>
                                    {/*
                            <div className={"sw-section-description"}>
                                For general enquiries about Skywind please fill in your details and request below:
                            </div>
                            <div className={"sw-contact-us-form-area"}>
*/}
                                <FormContactUs
                                    appServerConfig={appServerConfig}
                                    className={"sw-contact-us-form"}
                                    id={"sw-contact-us-form"}
                                    submitButtonName={"Send"}
                                />
                                    {/*
                            </div>
*/}
                                </div>
                            </div>
                        </section>
                    </AppSections>
                    <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                </AppContainer>
            </>
        );
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        const projectService = ProjectServiceInstance;
        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;
        const footer = await projectService.getWebsiteFooterData(regionCode, languageCode);
        return {
            // const footer = async

            props: { footer } // will be passed to the page
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

// noinspection JSUnusedGlobalSymbols
export default PageSiteContactUs;
