import React from "react";
import { GetServerSideProps, NextPage } from "next";
import { IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import appConfig from "appConfig";
import AppFooter from "@components/footers/app-footer";
import logger from "utils/logger";
import { AppContainer } from "@components/containers/app-container";
import AppHeader from "@components/headers/app-header";
import GamesModal from "@components/games-modal/games-modal";
import AppSections from "@components/sections/appSections";
import HeaderContainer from "@components/headers/header-container";
import { observer } from "mobx-react-lite";
import SectionLiveCasino from "@components/sections/live-casino/sectionLiveCasino";
// import { Swiper, SwiperSlide } from "swiper/react/swiper-react";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/scrollbar";
import { EnumShowGameRTP } from "models/enum/game";
import SectionSwiperTopGames from "@components/sections/swiper-games/section-swiper-top-games";
import { ProjectServiceInstance } from "api/services/projectService";
import CustomError from "@pages/custom-error";
import { useRouter } from "next/router";
import AppLoading from "@components/main/appLoading";
import { IPageProps } from "@pages/index";
import SeoSitePage from "@components/seo/seo-site-page";
import { CarouselJsonLd } from "next-seo";
import Head from "next/head";
import { ISwiperTopGameCard } from "@components/sections/swiper-games/swiper-top-game-card";
import { useSeoCarouselLiveGames } from "@hooks/use-seo-carousel-live-games";
import { useSiteTitle } from "@hooks/use-site-title";
import { useSitePageInfo } from "@hooks/use-site-page-info";
import { EnumPage } from "models/enum/page";

const log = logger("page--website-live-studio");

export interface ILiveStudioSlider {
    games: ISwiperTopGameCard[],
    slider: { id: string; title: string };
}

export interface IPageSiteLiveStudioProps extends IPageProps {
    data: ILiveStudioSlider[];
    footer: IWebsiteFooter;
}

const PageSiteLiveStudio: NextPage<IPageSiteLiveStudioProps> = observer((
    { data, footer, error, hostWithPort }) => {
    try {

        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        const languageCode = appConfig.systemSettings.defaultLanguageCode;

        const { pageTitle, pageUrlAbsolute, pageUrlRelative } = useSitePageInfo(EnumPage.liveStudio, {});

        const bannerImageUrl = "/assets/site/images/header/banner/leve-studio-header-banner.png";

        const pageDescription =
            "Take a seat at one of our many tables to enjoy a premium casino experience. " +
            "We have classic and exclusive games all hosted by our charismatic team and supported with the latest technology.";

        const pageImage = {
            url: bannerImageUrl,
            width: 1920,
            height: 990
        };

        const gamesCarousel = useSeoCarouselLiveGames( data );

        const { siteTitle } = useSiteTitle(pageTitle);

        return (
            <>
                <SeoSitePage pageTitle={pageTitle} pageDescription={pageDescription}
                             pageUrlAbsolute={pageUrlAbsolute} pageUrlRelative={pageUrlRelative}
                             pageImage={pageImage} />
                {Array.isArray(gamesCarousel) && gamesCarousel.length > 0 &&
                    <CarouselJsonLd
                        ofType={"movie"}
                        data={gamesCarousel}
                    />
                }
                <Head>
                    <title>{siteTitle}</title>
                    <meta name="keywords"
                          content="live gambling,live gaming,live casino games online,live games,online live games,real time gaming,online live casino,live casino games" />
                </Head>
                <AppContainer>
                    <AppHeader imageUrl={bannerImageUrl}>
                        <HeaderContainer title={"Live Casino"} contentText={pageDescription} isBottom={true} />
                    </AppHeader>
                    <AppSections className={"container"}>
                        {Array.isArray(data) && data.length > 0 && data.map(slider =>
                            <SectionSwiperTopGames
                                key={slider.slider.id}
                                className={"sw-section sw-section-swiper-games swiper-live-games"}
                                sectionTitle={slider.slider.title}
                                data={slider.games}
                            />
                        )}
                        <SectionLiveCasino />
                        <GamesModal languageCode={languageCode} />
                    </AppSections>
                    <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                </AppContainer>
            </>
        );
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

// noinspection JSUnusedGlobalSymbols
export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;

        // const data = await SanityProviderInstance.getSectionGroupSliderGames("live-games-" + regionCode,
        //     regionCode,
        //     languageCode);

        const footer = await ProjectServiceInstance.getWebsiteFooterData(regionCode, languageCode);

        const data: ILiveStudioSlider[] = [
            {
                slider: { id: "game_show", title: "Game Show" },
                games: [
                    {
                        gameTitle: "Joker Wheel",
                        gameCode: "sw_ro_jw",
                        gameRibbon: { "type": "soon", "title": "Exclusive" },
                        gamePosterUrl: "/assets/site/images/swiper/studio/joker-wheel-435x662.jpg",
                        showGameRTP: EnumShowGameRTP.from_bi,
                        rtp: [],
                        volatility: null,
                        showGameInfo: false
                    },
                    {
                        gameTitle: "Atom Roulette",
                        gameCode: "sw_live_erol_atom",
                        gameRibbon: { type: "", title: "" },
                        gamePosterUrl: "https://cdn.sanity.io/images/usfaerfs/production/0644c04eb2fae4ffe6d26ad8b83338d081252e81-435x662.jpg",
                        showGameRTP: EnumShowGameRTP.from_bi,
                        rtp: [],
                        volatility: null,
                        showGameInfo: false
                    },
                    {
                        gameTitle: "Rush Roulette",
                        gameCode: "sw_live_erol_rush",
                        gameRibbon: { "type": "", "title": "" },
                        gamePosterUrl: "/assets/site/images/swiper/studio/rush-roulette-435x662.jpg",
                        showGameRTP: EnumShowGameRTP.from_bi,
                        rtp: [],
                        volatility: null,
                        showGameInfo: false
                    }
                ]
            },
            {
                slider: { id: "live_casino", title: "Live Casino" },
                games: [
                    {
                        gameTitle: "Live Black Jack",
                        gameCode: "sw_ro_a01bj",
                        gameRibbon: { type: "", title: "" },
                        gamePosterUrl: "/assets/site/images/swiper/studio/live-black-jack-435x662.jpg",
                        showGameRTP: EnumShowGameRTP.from_bi,
                        rtp: [],
                        volatility: null,
                        showGameInfo: false
                    },
                    {
                        gameTitle: "Live Roulette",
                        gameCode: "sw_ro_a01ro",
                        gameRibbon: { type: "", title: "" },
                        gamePosterUrl: "/assets/site/images/swiper/studio/live-roulette-435x662.jpg",
                        showGameRTP: EnumShowGameRTP.from_bi,
                        rtp: [],
                        volatility: null,
                        showGameInfo: false
                    },
                    {
                        gameTitle: "Live Baccarat",
                        gameCode: "sw_ro_a01bac",
                        gameRibbon: { type: "", title: "" },
                        gamePosterUrl:
                            "https://cdn.sanity.io/images/usfaerfs/production/0e36d400aca52446be21f4e958b7dae18b107c49-435x662.jpg",
                        showGameRTP: EnumShowGameRTP.from_bi,
                        rtp: [],
                        volatility: null,
                        showGameInfo: false
                    },
                    {
                        gameTitle: "Live Blackjack Max",
                        gameCode: "sw_ro_bjmax",
                        gameRibbon: { type: "", title: "" },
                        gamePosterUrl:
                            "/assets/site/images/swiper/studio/live-blackjack-max-435x662.jpg",
                        showGameRTP: EnumShowGameRTP.from_bi,
                        rtp: [],
                        volatility: null,
                        showGameInfo: false
                    }
                ]
            }
        ];

        if (/*!data ||*/!footer) {
            return {
                notFound: true
            };
        }

        return {
            props: { data, footer }
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

// noinspection JSUnusedGlobalSymbols
export default PageSiteLiveStudio;
