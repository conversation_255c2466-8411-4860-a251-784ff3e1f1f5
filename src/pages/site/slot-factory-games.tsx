import React from "react";

import { GetServerSideProps, NextPage } from "next";
import { IGameExtended,
    ISectionGroupSliderGamesResponse,
    IWebsiteFooter
} from "api/data-providers/sanity/interfaces/iSanityProvider";
import appConfig from "appConfig";
import AppFooter from "@components/footers/app-footer";
import logger from "utils/logger";
import { AppContainer } from "@components/containers/app-container";
import AppHeader from "@components/headers/app-header";
import AppSections from "@components/sections/appSections";
import SectionGGames from "@components/sections/games/section-games";
import GamesModal from "@components/games-modal/games-modal";
import GamesPopover from "@components/games-popover/games-popover";
import HeaderContainer from "@components/headers/header-container";
import { observer } from "mobx-react-lite";
import { SanityProviderInstance } from "api/data-providers/sanity/sanityProvider";
import { ProjectServiceInstance } from "api/services/projectService";
import CustomError from "@pages/custom-error";
import { useRouter } from "next/router";
import AppLoading from "@components/main/appLoading";
import { IPageProps } from "@pages/index";
import { Project } from "utils/project";
import { EnumRegionCode } from "models/enum/system";
import SeoSitePage from "@components/seo/seo-site-page";
import Head from "next/head";
import { CarouselJsonLd } from "next-seo";
import { useSeoCarouselSliderGames } from "@hooks/use-seo-carousel-slider-games";
import { useSiteTitle } from "@hooks/use-site-title";
import { useSitePageInfo } from "@hooks/use-site-page-info";
import { EnumPage } from "models/enum/page";
import { Game } from "utils/game";

const log = logger("page--website-games");

export interface IPageSiteSlotFactoryGamesProps extends IPageProps {
    data: ISectionGroupSliderGamesResponse;
    footer: IWebsiteFooter;
}

const PageSiteSlotFactoryGames: NextPage<IPageSiteSlotFactoryGamesProps> = observer((
    { data, footer, error, hostWithPort }) => {
    try {
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        const languageCode = appConfig.systemSettings.defaultLanguageCode;

        const { pageTitle, pageUrlAbsolute, pageUrlRelative } = useSitePageInfo(EnumPage.gamesList, {});
        const pageDescription =
            "More than 500 games on our portfolio, games that are visually stunning, easy to understand and endlessly engaging.";
        const pageImage = {
            url: "https://cdn.sanity.io/images/usfaerfs/production/79d68c8e4a909ada239687f13a70a3535e9b64cc-1920x990.jpg",
            width: 1920,
            height: 990
        };

        const gamesCarousel = useSeoCarouselSliderGames(data?.data?.sliderGames, languageCode);

        const { siteTitle } = useSiteTitle(pageTitle);

        // @ts-ignore
        return (
            <>
                <SeoSitePage pageTitle={pageTitle} pageDescription={pageDescription}
                             pageUrlAbsolute={pageUrlAbsolute} pageUrlRelative={pageUrlRelative}
                             pageImage={pageImage} />
                {Array.isArray(gamesCarousel) && gamesCarousel.length > 0 &&
                <CarouselJsonLd
                    ofType={"movie"}
                    data={gamesCarousel}
                />
                }
                <Head>
                    <title>{siteTitle}</title>
                    <meta name="keywords"
                          content="gaming provider,gaming platform,gaming software,gaming studios,gaming website,gaming technology,game provider,casino gaming software,online gaming companies,casino software companies,casino game companies,casino gaming,online gambling software,online gambling games,games provider,gambling product,gambling casino games,demo gaming" />
                </Head>
                <AppContainer className={"base no-padding"}>
                    <AppHeader
                        // imageUrl={"/assets/site/images/header/banner/about-header-banner.png"}
                        videoUrl={
                            "https://cdn.sanity.io/files/usfaerfs/production/9c12e5594d276596d9699f1183933be5d6910da7.mp4"
                        }
                    >
                        <HeaderContainer
                            title={"Slot Factory Games"}
                            contentText={pageDescription}
                            isBottom={true}
                        />
                    </AppHeader>
                    <AppSections className={"base no-padding"}>
                        {data?.data?.sliderGames?.map((slider) => {
                            slider.games = slider.games ? slider.games.filter(game => {
                                // @ts-ignore
                                return game && Game.getFirstGameCode(game).indexOf("itg_") === 0;
                            }) : slider.games;
                            return (
                                        <SectionGGames
                                            slider={slider}
                                            languageCode={languageCode}
                                            key={slider._id}
                                            providerGames={data?.providerGames}
                                            swiperLoop={false}
                                        />)
                            }
                        )}
                        <GamesModal languageCode={languageCode} />
                        <GamesPopover languageCode={languageCode} />
                    </AppSections>
                    <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                </AppContainer>
            </>
        );
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

// noinspection JSUnusedGlobalSymbols
export const getServerSideProps: GetServerSideProps = async (context) => {
    try {
        const hostWithPort = context.req.headers.host;
        const project = Project.getProjectByHost(String(hostWithPort));

        const sanity = SanityProviderInstance;
        const projectService = ProjectServiceInstance;

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = project?.code.substring("website_".length) as EnumRegionCode;
        const footer = await projectService.getWebsiteFooterData(regionCode, languageCode);

        const data = await sanity.getSectionGroupSliderGames("games-" + regionCode, regionCode, languageCode);

        if (!data || !footer) {
            return {
                notFound: true
            };
        }

        return {
            props: { data, footer }
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PageSiteSlotFactoryGames;
