import React, { useContext } from "react";

import { observer } from "mobx-react-lite";

import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Keyboard, Mousewheel, Navigation, Pagination, Scrollbar } from "swiper";

import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/scrollbar";
import "swiper/css/autoplay";
import { GetServerSideProps, NextPage } from "next";
import appConfig from "appConfig";
import { IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import AppFooter from "@components/footers/app-footer";
import AppImg from "@components/main/appImg";
import logger from "utils/logger";
import { AppContainer } from "@components/containers/app-container";
import AppHeader from "@components/headers/app-header";
import AppSections from "@components/sections/appSections";
import HeaderContainer from "@components/headers/header-container";
import { ProjectServiceInstance } from "api/services/projectService";
import SectionSwiperCards from "@components/sections/swiper-cards/section-swiper-cards";
import CustomError from "@pages/custom-error";
import { useRouter } from "next/router";
import AppLoading from "@components/main/appLoading";
import { IPageProps } from "@pages/index";
import SeoSitePage from "@components/seo/seo-site-page";
import Head from "next/head";
import { useSiteTitle } from "@hooks/use-site-title";
import { useSitePageInfo } from "@hooks/use-site-page-info";
import { EnumPage } from "models/enum/page";
import { MainAppContext } from "@pages/_app";
import { Project } from "utils/project";

const log = logger("page--website-tournaments");

export interface IPageSiteTournamentsProps extends IPageProps {
    footer: IWebsiteFooter;
}

const PageSiteTournaments: NextPage<IPageSiteTournamentsProps> = observer((
    { footer, error, hostWithPort }) => {
    try {
        const { appStore } = useContext(MainAppContext);
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        const languageCode = appConfig.systemSettings.defaultLanguageCode;

        const dataEngTools = [
            {
                linkUrl: Project.getPageUrl(appStore, EnumPage.toolsLuckyEnvelopes),
                imageUrl: "/assets/site/images/swiper/eng-tools/lucky-envelopes-435x662.jpg"
            }, {
                linkUrl: Project.getPageUrl(appStore, EnumPage.toolsMustWinJackpots),
                imageUrl: "/assets/site/images/swiper/eng-tools/mwjp-435x662.jpg"
            }, {
                linkUrl: Project.getPageUrl(appStore, EnumPage.toolsFreeBets),
                imageUrl: "/assets/site/images/swiper/eng-tools/free-bets-435x662.jpg"
            }, {
                linkUrl: Project.getPageUrl(appStore, EnumPage.toolsWuShiJackpot),
                imageUrl: "/assets/site/images/swiper/eng-tools/wushi-435x662.jpg"
            }
        ];

        const { pageTitle, pageUrlAbsolute, pageUrlRelative } = useSitePageInfo(EnumPage.toolsTournaments, {});
        const { siteTitle } = useSiteTitle(pageTitle);
        const pageDescription = "More thrills and opportunities.";

        const headerImageUrl = "/assets/site/images/header/banner/tournaments.png";
        const pageImage = {
            url: headerImageUrl,
            width: 1920,
            height: 1080
        };

        return (
            <>
                <SeoSitePage pageTitle={pageTitle} pageDescription={pageDescription}
                             pageUrlAbsolute={pageUrlAbsolute} pageUrlRelative={pageUrlRelative}
                             pageImage={pageImage} />
                <Head>
                    <title>{siteTitle}</title>
                </Head>
                <AppContainer className="page-egt">
                    <AppHeader imageUrl={headerImageUrl}>
                        <HeaderContainer
                            title={"In Game Tournament"}
                            contentText={pageDescription}
                            imageUrl={"/assets/site/images/header/logo/tournaments.png"}
                            isStatic={true}
                            btnText={"Try It"}
                            btnLinkUrl={"https://skywindpromotions.com/"}
                        />
                    </AppHeader>
                    <AppSections className={"container"}>
                        <section className="engagement-swiper-section">
                            <Swiper
                                modules={[Navigation, Pagination, Mousewheel, Keyboard, Scrollbar, Autoplay]}
                                slidesPerView={"auto"}
                                spaceBetween={0}
                                grabCursor={true}
                                // https://github.com/nolimits4web/swiper/issues/3855
                                // ref={swiperRef}
                                // mousewheel={true}
                                keyboard={true}
                                // watchSlidesProgress={true}
                                // watchSlidesVisibility={true}
                                // scrollbar={true}
                                loop={true}
                                autoplay={{
                                    delay: 10000,
                                    disableOnInteraction: false,
                                    pauseOnMouseEnter: true
                                }}
                                navigation={{
                                    nextEl: ".swiper-button-next",
                                    prevEl: ".swiper-button-prev"
                                }}
                                className={"swiper engagement-swiper"}
                            >
                                <SwiperSlide>
                                    <div className="swiper-slide-figure">
                                        <AppImg
                                            src="/assets/site/images/swiper/engagement/jokers-luck.png"
                                            className="swiper-slide-figure-img"
                                        />
                                    </div>
                                    <div className="swiper-slide-content">
                                        <div className="swiper-slide-title">In-Game Display</div>
                                        <div className="swiper-slide-text spaced">
                                            Give your players everything they need, in a single glance. In game
                                            tournament
                                            displays mean that your players effortlessly know where they stand all
                                            times.
                                        </div>
                                        <div className="swiper-slide-subtitle">Maintaining Immersion</div>
                                        <div className="swiper-slide-text">
                                            No fuss, no muss. Players do not have to put in extra effort or leave the
                                            game
                                            the
                                            came for, to be part of the extra fun.
                                        </div>
                                    </div>
                                </SwiperSlide>
                                <SwiperSlide>
                                    <div className="swiper-slide-figure">
                                        <AppImg
                                            src="/assets/site/images/swiper/engagement/ranked-tournaments.png"
                                            className="swiper-slide-figure-img"
                                        />
                                    </div>
                                    <div className="swiper-slide-content">
                                        <div className="swiper-slide-title">Ranked Τournaments</div>
                                        <div className="swiper-slide-text spaced">
                                            This is responsible gaming at its best. Creating an even playing field for
                                            players
                                            who want a chance at unique prizes while play the game they Ιοve the way
                                            they
                                            love
                                            to play it.
                                        </div>
                                        <div className="swiper-slide-subtitle">Create An Even Playing Field</div>
                                        <div className="swiper-slide-text">
                                            Reward players for their actual gameplay and prize Contribution. Set up
                                            prizes
                                            and
                                            schedules that match your players gaming style
                                        </div>
                                    </div>
                                </SwiperSlide>
                                <SwiperSlide>
                                    <div className="swiper-slide-figure">
                                        <AppImg
                                            src="/assets/site/images/swiper/engagement/flexible-reward-system.png"
                                            className="swiper-slide-figure-img"
                                        />
                                    </div>
                                    <div className="swiper-slide-content">
                                        <div className="swiper-slide-title">Flexible Reward System</div>
                                        <div className="swiper-slide-text spaced">
                                            Νot Everyone is made the same, so Why should the schedules and prizes be?
                                            Define
                                            your prize schedules sizes, and how many of each you want to give.
                                        </div>
                                        <div className="swiper-slide-subtitle">Define Your Own Bet Ranges</div>
                                        <div className="swiper-slide-text">
                                            Yoυ know your players best, Define bet ranges that make the most sense for
                                            you
                                            and
                                            your players. Εach bet range can have its own prize size, with its οwn
                                            schedule.
                                        </div>
                                    </div>
                                </SwiperSlide>
                                <SwiperSlide>
                                    <div className="swiper-slide-figure">
                                        <AppImg
                                            src="/assets/site/images/swiper/engagement/easy-integration.png"
                                            className="swiper-slide-figure-img"
                                        />
                                    </div>
                                    <div className="swiper-slide-content">
                                        <div className="swiper-slide-title">Easy Integration</div>
                                        <div className="swiper-slide-text">
                                            It is always our top priority to make our tools intuitive to use and easy
                                            integrate.
                                            Whether it means using a single AΡΙ or a dedicated backend that allows our
                                            tools
                                            to
                                            modify for your needs with ease.
                                        </div>
                                    </div>
                                </SwiperSlide>
                                <div className="swiper-button swiper-button-prev" />
                                <div className="swiper-button swiper-button-next" />
                            </Swiper>
                        </section>
                        <section className="engagement-swiper-section container">
                            <div className="section-title">More Engagement Tools</div>
                            <SectionSwiperCards
                                className={"sw-section sw-section-swiper-eng-tools swiper-centered"}
                                data={dataEngTools} />
                        </section>
                    </AppSections>
                    <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                </AppContainer>
            </>
        );
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;
        const footer = await ProjectServiceInstance.getWebsiteFooterData(regionCode, languageCode);
        return {
            // const footer = async

            props: { footer } // will be passed to the page
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PageSiteTournaments;
