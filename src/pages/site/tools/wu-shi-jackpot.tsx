import logger from "utils/logger";
import { observer } from "mobx-react-lite";
import AppFooter from "@components/footers/app-footer";
import { IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import React, { useContext } from "react";
import { AppContainer } from "@components/containers/app-container";
import AppSections from "@components/sections/appSections";
import { GetServerSideProps, NextPage } from "next";
import appConfig from "appConfig";
import { ComingSoon } from "@components/sections/coming-soon/comingSoon";
import MainSiteToolbar from "@components/toolbars/main-site-toolbar";
import { ProjectServiceInstance } from "api/services/projectService";
import CustomError from "@pages/custom-error";
import { useRouter } from "next/router";
import AppLoading from "@components/main/appLoading";
import { IPageProps } from "@pages/index";
import SeoSitePage from "@components/seo/seo-site-page";
import Head from "next/head";
import { useSiteTitle } from "@hooks/use-site-title";
import { useSitePageInfo } from "@hooks/use-site-page-info";
import { EnumPage } from "models/enum/page";
import { Project } from "utils/project";
import { MainAppContext } from "@pages/_app";

const log = logger("page--website-tournaments");

export interface IPageSiteWuShiJackpotsProps extends IPageProps {
    footer: IWebsiteFooter;
}

const PageSiteWuShiJackpots: NextPage<IPageSiteWuShiJackpotsProps> = observer((
    { footer, error, hostWithPort }) => {
    try {
        const { appStore } = useContext(MainAppContext);
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        const languageCode = appConfig.systemSettings.defaultLanguageCode;

        const homePageUrl = Project.getPageUrl(appStore, EnumPage.home);

        const { pageTitle, pageUrlAbsolute, pageUrlRelative } = useSitePageInfo(EnumPage.toolsTournaments, {});
        const { siteTitle } = useSiteTitle(pageTitle);
        const pageDescription = "Coming soon";

        const pageImage = {
            url: "/assets/site/images/header/banner/about-header-banner.jpg",
            width: 1920,
            height: 1080
        };

        return (
            <>
                <SeoSitePage pageTitle={pageTitle} pageDescription={pageDescription}
                             pageUrlAbsolute={pageUrlAbsolute} pageUrlRelative={pageUrlRelative}
                             pageImage={pageImage} />
                <Head>
                    <title>{siteTitle}</title>
                </Head>
                <AppContainer className="page-egt">
                    <MainSiteToolbar />
                    <AppSections className={"container"}>
                        <ComingSoon gotoUrl={homePageUrl} className={"page404"} />
                    </AppSections>
                    <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                </AppContainer>
            </>
        );
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;
        const footer = await ProjectServiceInstance.getWebsiteFooterData(regionCode, languageCode);
        return {
            // const footer = async

            props: { footer } // will be passed to the page
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

// noinspection JSUnusedGlobalSymbols
export default PageSiteWuShiJackpots;
