import React, { useContext } from "react";
import { observer } from "mobx-react-lite";

import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Keyboard, Mousewheel, Navigation, Pagination, Scrollbar } from "swiper";

import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/scrollbar";
import "swiper/css/autoplay";

import { GetServerSideProps, NextPage } from "next";
import appConfig from "appConfig";
import { IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import AppFooter from "@components/footers/app-footer";
import AppImg from "@components/main/appImg";
import logger from "utils/logger";
import { AppContainer } from "@components/containers/app-container";
import AppHeader from "@components/headers/app-header";
import AppSections from "@components/sections/appSections";
import HeaderContainer from "@components/headers/header-container";
import { ProjectServiceInstance } from "api/services/projectService";
import SectionSwiperCards from "@components/sections/swiper-cards/section-swiper-cards";
import { IPageProps } from "@pages/index";
import CustomError from "@pages/custom-error";
import AppLoading from "@components/main/appLoading";
import { useRouter } from "next/router";
import SeoSitePage from "@components/seo/seo-site-page";
import Head from "next/head";
import { useSiteTitle } from "@hooks/use-site-title";
import { useSitePageInfo } from "@hooks/use-site-page-info";
import { EnumPage } from "models/enum/page";
import { Project } from "utils/project";
import { MainAppContext } from "@pages/_app";

const log = logger("page--website-lucky-envelopes");

export interface IPageSiteLuckyEnvelopesProps extends IPageProps {
    footer: IWebsiteFooter;
}

const PageSiteLuckyEnvelopes: NextPage<IPageSiteLuckyEnvelopesProps> = observer((
    { footer, error, hostWithPort }) => {
    try {
        const { appStore } = useContext(MainAppContext);
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        const languageCode = appConfig.systemSettings.defaultLanguageCode;

        const dataEngTools = [
            {
                linkUrl: Project.getPageUrl(appStore, EnumPage.toolsMustWinJackpots),
                imageUrl: "/assets/site/images/swiper/eng-tools/mwjp-435x662.jpg"
            }, {
                linkUrl: Project.getPageUrl(appStore, EnumPage.toolsTournaments),
                imageUrl: "/assets/site/images/swiper/eng-tools/tournaments-435x662.jpg"
            }, {
                linkUrl: Project.getPageUrl(appStore, EnumPage.toolsFreeBets),
                imageUrl: "/assets/site/images/swiper/eng-tools/free-bets-435x662.jpg"
            }, {
                linkUrl: Project.getPageUrl(appStore, EnumPage.toolsWuShiJackpot),
                imageUrl: "/assets/site/images/swiper/eng-tools/wushi-435x662.jpg"
            }
        ];

        const { pageTitle, pageUrlAbsolute, pageUrlRelative } = useSitePageInfo(EnumPage.toolsLuckyEnvelopes, {});
        const { siteTitle } = useSiteTitle(pageTitle);
        const pageDescription = "Give your players an engaging experience with a chance to win lucky prizes with any qualifying bet.";

        const headerImageUrl = "/assets/site/images/header/banner/lucky-envelopes.png";
        const pageImage = {
            url: headerImageUrl,
            width: 1920,
            height: 1080
        };

        return (
            <>
                <SeoSitePage pageTitle={pageTitle} pageDescription={pageDescription}
                             pageUrlAbsolute={pageUrlAbsolute} pageUrlRelative={pageUrlRelative}
                             pageImage={pageImage} />
                <Head>
                    <title>{siteTitle}</title>
                </Head>
                <AppContainer className="page-egt">
                    <AppHeader imageUrl={headerImageUrl}>
                        <HeaderContainer
                            contentText={pageDescription}
                            imageUrl={"/assets/site/images/header/logo/luck-envelopes.png"}
                            isStatic={true}
                            btnText={"Try It"}
                            btnLinkUrl={"https://skywindpromotions.com/"}
                        />
                    </AppHeader>
                    <AppSections>
                        <section className="engagement-swiper-section container">
                            <Swiper
                                modules={[Navigation, Pagination, Mousewheel, Keyboard, Scrollbar, Autoplay]}
                                slidesPerView={"auto"}
                                spaceBetween={0}
                                grabCursor={true}
                                // https://github.com/nolimits4web/swiper/issues/3855
                                // ref={swiperRef}
                                // mousewheel={true}
                                keyboard={true}
                                loop={true}
                                autoplay={{
                                    delay: 10000,
                                    disableOnInteraction: false,
                                    pauseOnMouseEnter: true
                                }}
                                // watchSlidesProgress={true}
                                // watchSlidesVisibility={true}
                                // scrollbar={true}
                                navigation={{
                                    nextEl: ".swiper-button-next",
                                    prevEl: ".swiper-button-prev"
                                }}
                                className={"swiper engagement-swiper"}
                            >
                                <SwiperSlide>
                                    <div className="swiper-slide-figure">
                                        <AppImg
                                            src="/assets/site/images/swiper/engagement/le-device.png"
                                            className="swiper-slide-figure-img"
                                        />
                                    </div>
                                    <div className="swiper-slide-content">
                                        <div className="swiper-slide-title">In-Game Experience Across any Device</div>
                                        <div className="swiper-slide-text">
                                            Keep players engaged with in-game UI that shows all relevant promotion
                                            information in an immersive way, without leaving the games.
                                        </div>
                                    </div>
                                </SwiperSlide>
                                <SwiperSlide>
                                    <div className="swiper-slide-figure">
                                        <AppImg
                                            src="/assets/site/images/swiper/engagement/le-device-2.png"
                                            className="swiper-slide-figure-img"
                                        />
                                    </div>
                                    <div className="swiper-slide-content">
                                        <div className="swiper-slide-title">Great Rewards</div>
                                        <div className="swiper-slide-text">
                                            Reward players for their actual gameplay and prize contribution. Set up
                                            prizes
                                            and
                                            schedule promotions that match your players’ gaming style.
                                        </div>
                                    </div>
                                </SwiperSlide>
                                <SwiperSlide>
                                    <div className="swiper-slide-figure">
                                        <AppImg
                                            src="/assets/site/images/swiper/engagement/le-device-3.png"
                                            className="swiper-slide-figure-img"
                                        />
                                    </div>
                                    <div className="swiper-slide-content">
                                        <div className="swiper-slide-title">Flexible Prizes</div>
                                        <div className="swiper-slide-text">
                                            Players will love small and frequent prizes with many chances to win. Lucky
                                            Envelops
                                            give your players a chance at unique prizes while playing the games they
                                            love
                                            the
                                            way they want to play them.
                                        </div>
                                    </div>
                                </SwiperSlide>
                                <div className="swiper-button swiper-button-prev" />
                                <div className="swiper-button swiper-button-next" />
                            </Swiper>
                        </section>
                        <section className="engagement-swiper-section container">
                            <div className="section-title">More Engagement Tools</div>
                            <SectionSwiperCards
                                className={"sw-section sw-section-swiper-eng-tools swiper-centered"}
                                data={dataEngTools} />
                        </section>
                    </AppSections>
                    <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                </AppContainer>
            </>
        );
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;
        const footer = await ProjectServiceInstance.getWebsiteFooterData(regionCode, languageCode);
        return {
            // const footer = async

            props: { footer } // will be passed to the page
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }

};

export default PageSiteLuckyEnvelopes;
