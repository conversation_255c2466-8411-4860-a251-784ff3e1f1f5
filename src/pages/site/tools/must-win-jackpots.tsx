import React, { useContext } from "react";
import { observer } from "mobx-react-lite";

import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Keyboard, Mousewheel, Navigation, Pagination, Scrollbar } from "swiper";

import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/scrollbar";
import "swiper/css/autoplay";

import { GetServerSideProps, NextPage } from "next";
import appConfig from "appConfig";
import { IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import AppFooter from "@components/footers/app-footer";
import AppImg from "@components/main/appImg";
import logger from "utils/logger";
import { AppContainer } from "@components/containers/app-container";
import AppHeader from "@components/headers/app-header";
import AppSections from "@components/sections/appSections";
import HeaderContainer from "@components/headers/header-container";
import { ProjectServiceInstance } from "api/services/projectService";
import SectionSwiperCards from "@components/sections/swiper-cards/section-swiper-cards";
import { IPageProps } from "@pages/index";
import CustomError from "@pages/custom-error";
import { useRouter } from "next/router";
import AppLoading from "@components/main/appLoading";
import SeoSitePage from "@components/seo/seo-site-page";
import Head from "next/head";
import { useSiteTitle } from "@hooks/use-site-title";
import { useSitePageInfo } from "@hooks/use-site-page-info";
import { EnumPage } from "models/enum/page";
import { Project } from "utils/project";
import { MainAppContext } from "@pages/_app";

const log = logger("page--website-must-win-jackpot");

export interface IPageSiteMustWinJackpotsProps extends IPageProps {
    footer: IWebsiteFooter;
}

const PageSiteMustWinJackpots: NextPage<IPageSiteMustWinJackpotsProps> = observer((
    { footer, error, hostWithPort }) => {
    try {
        const { appStore } = useContext(MainAppContext);

        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        const languageCode = appConfig.systemSettings.defaultLanguageCode;

        const dataEngTools = [
            {
                linkUrl: Project.getPageUrl(appStore, EnumPage.toolsLuckyEnvelopes),
                imageUrl: "/assets/site/images/swiper/eng-tools/lucky-envelopes-435x662.jpg"
            }, {
                linkUrl: Project.getPageUrl(appStore, EnumPage.toolsTournaments),
                imageUrl: "/assets/site/images/swiper/eng-tools/tournaments-435x662.jpg"
            }, {
                linkUrl: Project.getPageUrl(appStore, EnumPage.toolsFreeBets),
                imageUrl: "/assets/site/images/swiper/eng-tools/free-bets-435x662.jpg"
            }, {
                linkUrl: Project.getPageUrl(appStore, EnumPage.toolsWuShiJackpot),
                imageUrl: "/assets/site/images/swiper/eng-tools/wushi-435x662.jpg"
            }
        ];

        const { pageTitle, pageUrlAbsolute, pageUrlRelative } = useSitePageInfo(EnumPage.toolsMustWinJackpots, {});
        const { siteTitle } = useSiteTitle(pageTitle);
        const pageDescription = "Shared Wins Jackpot. Time Based Jackpots. Amount Based Jackpots";

        const headerImageUrl = "/assets/site/images/header/banner/mwjp.png";
        const pageImage = {
            url: headerImageUrl,
            width: 1920,
            height: 1080
        };

        return (
            <>
                <SeoSitePage pageTitle={pageTitle} pageDescription={pageDescription}
                             pageUrlAbsolute={pageUrlAbsolute} pageUrlRelative={pageUrlRelative}
                             pageImage={pageImage} />
                <Head>
                    <title>{siteTitle}</title>
                </Head>
                <AppContainer className="page-egt">
                    <AppHeader imageUrl={headerImageUrl}>
                        <HeaderContainer
                            contentText={"More opportunities. More engagement."}
                            imageUrl={"/assets/site/images/header/logo/mwjp.png"}
                            isStatic={true}
                            btnText={"Try It"}
                            btnLinkUrl={"https://skywindpromotions.com/"}
                        />
                    </AppHeader>
                    <AppSections>
                        <section className="engagement-swiper-section container">
                            <Swiper
                                modules={[Navigation, Pagination, Mousewheel, Keyboard, Scrollbar, Autoplay]}
                                slidesPerView={"auto"}
                                spaceBetween={0}
                                grabCursor={true}
                                // https://github.com/nolimits4web/swiper/issues/3855
                                // ref={swiperRef}
                                // mousewheel={true}
                                keyboard={true}
                                // watchSlidesProgress={true}
                                // watchSlidesVisibility={true}
                                // scrollbar={true}
                                autoplay={{
                                    delay: 10000,
                                    disableOnInteraction: false,
                                    pauseOnMouseEnter: true
                                }}
                                loop={true}
                                navigation={{
                                    nextEl: ".swiper-button-next",
                                    prevEl: ".swiper-button-prev"
                                }}
                                className={"swiper engagement-swiper"}
                            >
                                <SwiperSlide>
                                    <div className="swiper-slide-figure">
                                        <AppImg
                                            src="/assets/site/images/swiper/engagement/mwjp-1.png"
                                            className="swiper-slide-figure-img"
                                        />
                                    </div>
                                    <div className="swiper-slide-content">
                                        <div className="swiper-slide-title">Shared Wins Jackpot</div>
                                        <div className="swiper-slide-text spaced">
                                            Split the prize between the first-place winner and your most loyal players
                                            to
                                            create
                                            more winners.
                                        </div>
                                        <div className="swiper-slide-subtitle">Strengthening Community Bonds</div>
                                        <div className="swiper-slide-text">
                                            Players who take part can feel seen and appreciated with this new split
                                            prize
                                            system.
                                        </div>
                                    </div>
                                </SwiperSlide>
                                <SwiperSlide>
                                    <div className="swiper-slide-figure">
                                        <AppImg
                                            src="/assets/site/images/swiper/engagement/time-based.png"
                                            className="swiper-slide-figure-img"
                                        />
                                    </div>
                                    <div className="swiper-slide-content">
                                        <div className="swiper-slide-title">Time Based Jackpots</div>
                                        <div className="swiper-slide-text spaced">
                                            It is no secret now, players know when a jackpot is about to pay out. Set up
                                            the
                                            anticipation, take control of the event
                                        </div>
                                        <div className="swiper-slide-subtitle">Molding User Experience</div>
                                        <div className="swiper-slide-text">
                                            Create an atmosphere of opportunity and anticipation by creating winners,
                                            and
                                            letting players know it. Let them in on it, in advance where and when
                                            opportunities
                                            abound.
                                        </div>
                                    </div>
                                </SwiperSlide>
                                <SwiperSlide>
                                    <div className="swiper-slide-figure">
                                        <AppImg
                                            src="/assets/site/images/swiper/engagement/amount-based.png"
                                            className="swiper-slide-figure-img"
                                        />
                                    </div>
                                    <div className="swiper-slide-content">
                                        <div className="swiper-slide-title">Amount Based Jackpots</div>
                                        <div className="swiper-slide-text spaced">
                                            Set the goal and let your community of players know. Will you set a huge
                                            long-
                                            awaited mega prize Or will you set the goal for a lower prize , quickly
                                            achievable ,
                                            and create a community of winners?
                                        </div>
                                        <div className="swiper-slide-subtitle">Setting Up Expectations</div>
                                        <div className="swiper-slide-text">
                                            Make every spin more rewarding as it adds to the jackpot.
                                            Make spins more exciting as the becomes imminent
                                        </div>
                                    </div>
                                </SwiperSlide>
                                <div className="swiper-button swiper-button-prev" />
                                <div className="swiper-button swiper-button-next" />
                            </Swiper>
                        </section>
                        <section className="engagement-swiper-section container">
                            <div className="section-title">More Engagement Tools</div>
                            <SectionSwiperCards
                                className={"sw-section sw-section-swiper-eng-tools swiper-centered"}
                                data={dataEngTools} />
                        </section>
                    </AppSections>
                    <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                </AppContainer>
            </>
        );
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;
        const footer = await ProjectServiceInstance.getWebsiteFooterData(regionCode, languageCode);
        return {
            // const footer = async

            props: { footer } // will be passed to the page
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

// noinspection JSUnusedGlobalSymbols
export default PageSiteMustWinJackpots;
