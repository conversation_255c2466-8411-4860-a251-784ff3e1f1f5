import React from "react";
import { observer } from "mobx-react-lite";
import { GetServerSideProps, NextPage } from "next";
import { IPageAboutUsExtended, IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import appConfig from "appConfig";
import logger from "utils/logger";
import AppFooter from "@components/footers/app-footer";
import { AppContainer } from "@components/containers/app-container";
import AppHeader from "@components/headers/app-header";
import AppSections from "@components/sections/appSections";
import SectionAwards from "@components/sections/awards/sectionAwards";
import SectionOffices from "@components/sections/offices/sectionOffices";
import SectionLicenses from "@components/sections/licenses/sectionLicenses";
import SectionPartners from "@components/sections/partners/sectionPartners";
import HeaderContainer from "@components/headers/header-container";
import { SanityProviderInstance } from "api/data-providers/sanity/sanityProvider";
import { ProjectServiceInstance } from "api/services/projectService";
import AppLink from "@components/main/appLink";
import CustomError from "@pages/custom-error";
import { useRouter } from "next/router";
import AppLoading from "@components/main/appLoading";
import { IPageProps } from "@pages/index";
import SeoSitePage from "@components/seo/seo-site-page";
import Head from "next/head";
import { useSiteTitle } from "@hooks/use-site-title";
import { useSitePageInfo } from "@hooks/use-site-page-info";
import { EnumPage } from "models/enum/page";

const log = logger("page--website-about-us");

export interface IPageSiteAboutUsProps extends IPageProps {
    data: string;
    footer: IWebsiteFooter;
}

const PageSiteAboutUs: NextPage<IPageSiteAboutUsProps> = observer((
    { data, footer, error, hostWithPort }) => {
    try {
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const dataInfo: IPageAboutUsExtended = JSON.parse(data);

        const { pageTitle, pageUrlAbsolute, pageUrlRelative } = useSitePageInfo(EnumPage.aboutUs, {});
        const { siteTitle } = useSiteTitle(pageTitle);

        const pageDescription = "Skywind was founded in 2012 by a group of highly respected industry professionals, experienced in producing premium content and innovative iGaming solutions." +
            "Since then, Skywind has grown its portfolio to include over 200 video slots, Live Casino games, unique player engagement tools, all hosted on its own high-performance platform, to offer an exceptional and complete gaming experience." +
            "Skywind is presently made up of over 600 professionals, in locations all across the world; UK, Ukraine, Malta, Romania, Belarus, Cyprus, Philippines and Australia.";

        const pageImage = {
            url: "/assets/site/images/header/banner/about-header-banner.jpg",
            width: 1920,
            height: 1080
        };

        return (
            <>
                <SeoSitePage pageTitle={pageTitle}  pageDescription={pageDescription}
                             pageUrlAbsolute={pageUrlAbsolute} pageUrlRelative={pageUrlRelative}
                             pageImage={pageImage} />
                <Head>
                    <title>{siteTitle}</title>
                </Head>
                <AppContainer>
                    <AppHeader imageUrl={"/assets/site/images/header/banner/about-header-banner.jpg"}>
                        <HeaderContainer title={"About Skywind"} isBottom={true} />
                    </AppHeader>
                    <AppSections className={"container"}>
                        <section className="sw-section">
                            {/*<div className="sw-section-title">Skywind Group</div>*/}
                            <div className="sw-section-description">
                                <p>Skywind was founded in 2012 by a group of highly respected industry professionals,
                                    experienced in producing premium content and innovative iGaming solutions.</p>
                                <p>Since then, Skywind has grown its portfolio to include over 200 video slots, Live
                                    Casino
                                    games, unique player engagement tools, all hosted on its own high-performance
                                    platform,
                                    to
                                    offer an exceptional and complete gaming experience.</p>
                                <p>Skywind is presently made up of over 600 professionals, in locations all across the
                                    world;
                                    UK, Ukraine, Malta, Romania, Belarus, Cyprus, Philippines and Australia.</p>
                            </div>
                            <div className="sw-about-tbl">
                                <div className="sw-about-tbl-item">
                                    <div className="sw-about-tbl-val">600+</div>
                                    <div className="sw-about-tbl-label">Employees</div>
                                </div>
                                <div className="sw-about-tbl-item">
                                    <div className="sw-about-tbl-val">9</div>
                                    <div className="sw-about-tbl-label">Skywind<br /> Offices</div>
                                </div>
                                <div className="sw-about-tbl-item">
                                    <div className="sw-about-tbl-val">200+</div>
                                    <div className="sw-about-tbl-label">Games</div>
                                </div>
                                <div className="sw-about-tbl-item">
                                    <div className="sw-about-tbl-val">19</div>
                                    <div className="sw-about-tbl-label">Regulated<br /> Markets</div>
                                </div>
                                <div className="sw-about-tbl-item">
                                    <div className="sw-about-tbl-val">10+</div>
                                    <div className="sw-about-tbl-label">years<br /> Est. 2012</div>
                                </div>
                                <div className="sw-about-tbl-item">
                                    <div className="sw-about-tbl-val">250+</div>
                                    <div className="sw-about-tbl-label">Customers</div>
                                </div>
                                <div className="sw-about-tbl-item">
                                    <div className="sw-about-tbl-val">1000+</div>
                                    <div className="sw-about-tbl-label">Operators<br /> Worldwide</div>
                                </div>
                                <div className="sw-about-tbl-item">
                                    <div className="sw-about-tbl-val">7</div>
                                    <div className="sw-about-tbl-label">Licenses</div>
                                </div>
                            </div>
                        </section>
                        <SectionAwards />
                        <div className="sw-section">
                            <div className="sw-section-title">Responsible Gaming</div>
                            <div className="sw-section-description">
                                <p>Even though Skywind is a B2B software provider that is not engaged with the players
                                    directly,
                                    we consider responsible gaming as a highest priority of our social responsibility
                                    program.</p>
                                <p>Our company focuses on safety and harm prevention in our game development process in
                                    order to
                                    reduce any risk of game-related issues. We also commit ourselves to supporting our
                                    clients
                                    (online casino operators) with the tools and services towards responsible gaming
                                    requirements.</p>
                                <p>Please visit <AppLink
                                    href={"http://begambleaware.org"}
                                    title={"begambleaware.org"}
                                    target={"blank"}
                                >
                                    begambleaware.org
                                </AppLink> for more information.</p>
                            </div>
                        </div>
                        <SectionLicenses licenses={dataInfo?.licenses} title={"Our Licenses"}
                                         languageCode={languageCode} />
                        <SectionPartners partners={dataInfo?.partners} title={"Our Partners"}
                                         languageCode={languageCode} />
                        <SectionOffices offices={dataInfo?.offices} title={"Our Locations"}
                                        languageCode={languageCode} />
                    </AppSections>
                    <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                </AppContainer>
            </>
        );
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {
        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;

        const data = await SanityProviderInstance.getAboutUsPage(regionCode, languageCode);

        const footer = await ProjectServiceInstance.getWebsiteFooterData(regionCode, languageCode);

        if (!data || !footer) {
            return {
                notFound: true
            };
        }

        return {
            props: { data: JSON.stringify(data), footer }
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

// noinspection JSUnusedGlobalSymbols
export default PageSiteAboutUs;
