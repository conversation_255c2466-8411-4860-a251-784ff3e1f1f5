import React from "react";
import { observer } from "mobx-react-lite";
import { But<PERSON> } from "react-bootstrap";
import { GetServerSideProps, NextPage } from "next";
import appConfig from "appConfig";
import { IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import AppFooter from "@components/footers/app-footer";
import logger from "utils/logger";
import { AppContainer } from "@components/containers/app-container";
import AppHeader from "@components/headers/app-header";
import AppSections from "@components/sections/appSections";
import HeaderContainer from "@components/headers/header-container";
import { ProjectServiceInstance } from "api/services/projectService";
import CustomError from "@pages/custom-error";
import { useRouter } from "next/router";
import AppLoading from "@components/main/appLoading";
import { IPageProps } from "@pages/index";
import SeoSitePage from "@components/seo/seo-site-page";
import Head from "next/head";
import { useSiteTitle } from "@hooks/use-site-title";
import { useSitePageInfo } from "@hooks/use-site-page-info";
import { EnumPage } from "models/enum/page";

const log = logger("page--website-sports");

export interface IPageSiteSportsProps extends IPageProps {
    footer: IWebsiteFooter;
}

const PageSiteSports: NextPage<IPageSiteSportsProps> = observer((
    { footer, error, hostWithPort }) => {
    try {
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const { pageTitle, pageUrlAbsolute, pageUrlRelative } = useSitePageInfo(EnumPage.sports, {});

        const pageDescription =
            "We are pleased to offer all our partners an extensive sportsbook, that can be designed to " +
            "suit your players needs and is super-fast to set up with our integration system.";

        const headerImageUrl = "/assets/site/images/header/banner/sw360-header-banner.jpg";

        const pageImage = {
            url: headerImageUrl,
            width: 1920,
            height: 1080
        };

        const { siteTitle } = useSiteTitle(pageTitle);

        return (
            <>
                <SeoSitePage pageTitle={pageTitle} pageDescription={pageDescription}
                             pageUrlAbsolute={pageUrlAbsolute} pageUrlRelative={pageUrlRelative}
                             pageImage={pageImage} />
                <Head>
                    <title>{siteTitle}</title>
                </Head>
                <AppContainer>
                    <AppHeader imageUrl={"/assets/site/images/header/banner/sports-header-banner.jpg"}>
                        <HeaderContainer title={"Sports Betting"}
                                         contentText={
                                             "With live odds and live streams, online and in retail locations"
                                         }
                                         isBottom={true} />
                    </AppHeader>
                    <AppSections className={"container"}>
                        <section className={"section-sw360"}>
                            <div className={"wrap"}>
                                <div className={"sw-360"}>
                                    <div className={"sw-360-description"}>
                                        <p>
                                            We are pleased to offer all our partners an extensive sportsbook, that can
                                            be
                                            designed to suit your players needs and is super-fast to set up with our
                                            integration
                                            system.
                                        </p>
                                        <p>
                                            All the world’s favourite sports, leagues and tournaments are available and
                                            supported with automatic fixture creation and flexible odds generation. Live
                                            content
                                            and virtual feeds can be integrated, alongside special features and widgets
                                            for
                                            live
                                            betting.
                                        </p>
                                        <p>
                                            Skywind’s sportsbook not only lives online but can also operate within like
                                            betting
                                            shops through self-service betting terminals, giving any brand the
                                            opportunity
                                            to
                                            reach a wider number of players.
                                        </p>
                                    </div>
                                </div>
                                <div className={"sw-360-provides"}>
                                    <div className={"sw-360-description"}>
                                        <p>
                                            Our sports betting also includes the option of incorporating all
                                            international
                                            lottery draws and fast keno games with both fixed-odds and pool models.
                                        </p>
                                        <p>
                                            Present regulated markets Skywind Sports Betting is available in:
                                        </p>
                                        <p>Italy, Spain, Colombia, Belgium, Africa, with many more planned.</p>
                                    </div>
                                    <Button href={"mailto:<EMAIL>"}>REQUEST DEMO</Button>
                                </div>
                            </div>
                        </section>
                    </AppSections>
                    <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                </AppContainer>
            </>
        );
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;
        const footer = await ProjectServiceInstance.getWebsiteFooterData(regionCode, languageCode);
        return {
            // const footer = async

            props: { footer } // will be passed to the page
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PageSiteSports;
