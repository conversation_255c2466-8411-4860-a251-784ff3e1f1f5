import React from "react";
import { observer } from "mobx-react-lite";
import { AppContainer } from "@components/containers/app-container";
import AppSections from "@components/sections/appSections";
import { GetServerSideProps, NextPage } from "next";
import appConfig from "appConfig";
import logger from "utils/logger";
import { IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import AppFooter from "@components/footers/app-footer";
import AppHeader from "@components/headers/app-header";
import SectionCookiePolicy from "@components/sections/cookie-policy/sectionCookiePolicy";
import { ProjectServiceInstance } from "api/services/projectService";
import CustomError from "@pages/custom-error";
import { useRouter } from "next/router";
import AppLoading from "@components/main/appLoading";
import { IPageProps } from "@pages/index";
import SeoSitePage from "@components/seo/seo-site-page";
import Head from "next/head";
import { useSiteTitle } from "@hooks/use-site-title";
import { useSitePageInfo } from "@hooks/use-site-page-info";
import { EnumPage } from "models/enum/page";

const log = logger("page--website-cookies");

export interface IPageSiteCookiesProps extends IPageProps {
    footer: IWebsiteFooter;
}

const PageSiteCookies: NextPage<IPageSiteCookiesProps> = observer((
    { footer, error, hostWithPort }) => {
    try {
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        const languageCode = appConfig.systemSettings.defaultLanguageCode;

        const { pageTitle, pageUrlAbsolute, pageUrlRelative } = useSitePageInfo(EnumPage.cookies, {});

        const pageDescription = "This Cookies Policy is issued by Skywind Holdings Limited (collectively referred to as “we”, “us”, “our” or ”Skywind” in this Cookies Policy).";

        const pageImage = {
            url: "/assets/site/images/header/banner/about-header-banner.jpg",
            width: 1920,
            height: 1080
        };

        const { siteTitle } = useSiteTitle(pageTitle);

        return (
            <>
                <SeoSitePage pageTitle={pageTitle} pageDescription={pageDescription}
                             pageUrlAbsolute={pageUrlAbsolute} pageUrlRelative={pageUrlRelative}
                             pageImage={pageImage} />
                <Head>
                    <title>{siteTitle}</title>
                </Head>
                <AppContainer>
                    <AppHeader className={"header-cookies"} />
                    <AppSections className={"container"}>
                        <SectionCookiePolicy />
                    </AppSections>
                    <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                </AppContainer>
            </>
        );
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        const projectService = ProjectServiceInstance;
        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;
        const footer = await projectService.getWebsiteFooterData(regionCode, languageCode);
        return {
            // const footer = async

            props: { footer } // will be passed to the page
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PageSiteCookies;