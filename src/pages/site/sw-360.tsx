import React from "react";
import { observer } from "mobx-react-lite";
import { But<PERSON> } from "react-bootstrap";
import { GetServerSideProps, NextPage } from "next";
import appConfig from "appConfig";
import { IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import AppFooter from "@components/footers/app-footer";
import logger from "utils/logger";
import { AppContainer } from "@components/containers/app-container";
import AppHeader from "@components/headers/app-header";
import AppSections from "@components/sections/appSections";
import HeaderContainer from "@components/headers/header-container";
import { ProjectServiceInstance } from "api/services/projectService";
import CustomError from "@pages/custom-error";
import { useRouter } from "next/router";
import AppLoading from "@components/main/appLoading";
import { IPageProps } from "@pages/index";
import SeoSitePage from "@components/seo/seo-site-page";
import Head from "next/head";
import { useSiteTitle } from "@hooks/use-site-title";
import { useSitePageInfo } from "@hooks/use-site-page-info";
import { EnumPage } from "models/enum/page";

const log = logger("page--website-sw-360");

export interface IPageSiteSw360Props extends IPageProps {
    footer: IWebsiteFooter;
}

const PageSiteSw360: NextPage<IPageSiteSw360Props> = observer((
    { footer, error, hostWithPort }) => {
    try {
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const { pageTitle, pageUrlAbsolute, pageUrlRelative } = useSitePageInfo(EnumPage.sw360, {});

        const pageDescription =
            "The Joint power behind the Venture works together with established gaming companies " +
            "with market presence to create tailored products to take their business to the next level.";

        const headerImageUrl = "/assets/site/images/header/banner/sw360-header-banner.jpg";

        const pageImage = {
            url: headerImageUrl,
            width: 1920,
            height: 1080
        };

        const { siteTitle } = useSiteTitle(pageTitle);

        return (
            <>
                <SeoSitePage pageTitle={pageTitle} pageDescription={pageDescription}
                             pageUrlAbsolute={pageUrlAbsolute} pageUrlRelative={pageUrlRelative}
                             pageImage={pageImage} />
                <Head>
                    <title>{siteTitle}</title>
                </Head>
                <AppContainer>
                    <AppHeader imageUrl={headerImageUrl}>
                        <HeaderContainer isBottom={true} title={"Skywind 360"}
                                         contentText={"The Joint power behind the Venture"} />
                    </AppHeader>
                    <AppSections className={"container"}>
                        <section className={"section-sw360"}>
                            <div className={"wrap"}>
                                <div className={"sw-360"}>
                                    {/*<div className={"sw-360-title"}>Skywind 360</div>*/}
                                    <div className={"sw-360-description"}>
                                        <p>
                                            works together with established gaming companies with market presence to
                                            create
                                            tailored products to take their business to the next level.
                                        </p>
                                        <p>
                                            Skywind brings a decade of industry expertise and product development to
                                            each
                                            joint
                                            venture to enhance and grow the SW360 partner’s existing company model,
                                            customer
                                            base and revenue.
                                        </p>
                                    </div>
                                </div>
                                <div className={"sw-360-provides"}>
                                    <div className={"sw-360-title"}>Skywind 360 provides:</div>
                                    <div className={"sw-360-description"}>
                                        <ul>
                                            <li> Unparalleled gaming knowledge</li>
                                            <li> Innovative technology and products</li>
                                            <li> Secure and stable hosting</li>
                                            <li> Marketing and promotional services</li>
                                            <li> Investment and financial support</li>
                                        </ul>
                                    </div>
                                    <Button href={"mailto:<EMAIL>"}>REQUEST DEMO</Button>
                                </div>
                            </div>
                        </section>
                    </AppSections>
                    <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                </AppContainer>
            </>
        );
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;
        const footer = await ProjectServiceInstance.getWebsiteFooterData(regionCode, languageCode);
        return {
            // const footer = async

            props: { footer } // will be passed to the page
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

// noinspection JSUnusedGlobalSymbols
export default PageSiteSw360;
