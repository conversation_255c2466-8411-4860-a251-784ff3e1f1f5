import React from "react";

import { GetServerSideProps, NextPage } from "next";

import { ISectionGroupSliderGamesResponse, IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import appConfig from "appConfig";
import logger from "utils/logger";
import { AppContainer } from "@components/containers/app-container";
import AppHeader from "@components/headers/app-header";
import AppSections from "@components/sections/appSections";
import { useRouter } from "next/router";
import AppFooter from "@components/footers/app-footer";
import { SanityProviderInstance } from "api/data-providers/sanity/sanityProvider";
import { ProjectServiceInstance } from "api/services/projectService";

import "swiper/css";
import "swiper/css/autoplay";
import "swiper/css/navigation";
import "swiper/css/pagination";
import CustomError from "@pages/custom-error";
import AppLoading from "@components/main/appLoading";
import { IPageProps } from "@pages/index";
import Head from "next/head";
import SeoSitePage from "@components/seo/seo-site-page";
import { useSiteTitle } from "@hooks/use-site-title";
import { observer } from "mobx-react-lite";
import { EnumPage } from "models/enum/page";
import { useSitePageInfo } from "@hooks/use-site-page-info";
import { Game } from "utils/game";
import SectionGGames from "@components/sections/games/section-games";
import GamesModal from "@components/games-modal/games-modal";
import GamesPopover from "@components/games-popover/games-popover";
import HeaderContainer from "@components/headers/header-container";
import { ClientApp } from "client/app/clientApp";
import AppLink from "@components/main/appLink";
import AppImg from "@components/main/appImg";

const log = logger("page--website-home");

export interface IPageSiteHomeProps extends IPageProps {
    dataGames: ISectionGroupSliderGamesResponse;
    footer: IWebsiteFooter;
}

const PageSiteHome: NextPage<IPageSiteHomeProps> = observer(({ dataGames, footer, error }) => {
    try {
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        const { pageTitle, pageUrlAbsolute, pageUrlRelative } = useSitePageInfo(EnumPage.home, {});

        const languageCode = appConfig.systemSettings.defaultLanguageCode;

        const pageDescription = "Skywind provides the very best online gaming experience for our partners and players. " +
            "Skywind brings together the industry’s best talent to create world-class games, back-office and content management systems, hosting solutions, infrastructure, a proprietary deep learning engine and more." +
            "Skywind is a team of innovative thinkers and creators whose aim is to develop the next generation of products and services in the gaming industry.";

        const pageImage = {
            url: "/assets/site/images/header/banner/about-header-banner.jpg",
            width: 1920,
            height: 1080
        };

        const { siteTitle } = useSiteTitle(pageTitle);
        const videoUrl = "https://cdn.sanity.io/files/usfaerfs/production/88401f049e72aa771cf6be20b04c81036d2a969a.mp4";

        return (
            <>
                <SeoSitePage pageTitle={pageTitle} pageDescription={pageDescription}
                             pageUrlAbsolute={pageUrlAbsolute} pageUrlRelative={pageUrlRelative}
                             pageImage={pageImage} />
                <Head>
                    <title>{siteTitle}</title>
                </Head>
                <AppContainer className={"base no-padding"}>
                    <div className="container overflow-hidden no-padding">
                        <AppHeader
                            videoUrl={videoUrl}>
                            <HeaderContainer
                                title={"Skywind Slot Games"}
                                contentText={pageDescription}
                                isBottom={true}
                            >
                                <AppLink
                                    href={videoUrl}
                                    onClick={async (event) => {
                                        event.preventDefault();
                                        event.stopPropagation();
                                        ClientApp.dialog.openVideo(
                                            "Discover Skywind",
                                            videoUrl
                                        );
                                    }}
                                    className="header-banner-btn"
                                    style={{ justifyContent: "end" }}
                                >
                                    <AppImg
                                        style={{ maxHeight: "14vw" }}
                                        src="/assets/site/images/home/<USER>"
                                        alt={"Discover Skywind"}
                                        title={"Discover Skywind"}
                                        className="header-banner-content-figure-img"
                                    />
                                </AppLink>
                            </HeaderContainer>
                        </AppHeader>
                    </div>
                    <AppSections className={"container"}>
                        {dataGames?.data?.sliderGames?.map((slider) => {
                                slider.games = slider.games ? slider.games.filter(game => {
                                    // @ts-ignore
                                    return game && Game.getFirstGameCode(game).indexOf("sw_") === 0;
                                }) : slider.games;
                                return (
                                    <SectionGGames
                                        slider={slider}
                                        languageCode={languageCode}
                                        key={slider._id}
                                        providerGames={dataGames?.providerGames}
                                        swiperLoop={false}
                                    />);
                            }
                        )}
                        <GamesModal languageCode={languageCode} />
                        <GamesPopover languageCode={languageCode} />
                    </AppSections>
                    <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                </AppContainer>
            </>
        );
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async () => {
    try {
        const { defaultLanguageCode, defaultRegionCode } = appConfig.systemSettings;
        const dataGames = await SanityProviderInstance.getSectionGroupSliderGames("games-" + defaultRegionCode,
            defaultRegionCode,
            defaultLanguageCode);

        const footer = await ProjectServiceInstance.getWebsiteFooterData(defaultRegionCode, defaultLanguageCode);

        return {
            props: {
                footer,
                dataGames
            } as IPageSiteHomeProps
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PageSiteHome;
