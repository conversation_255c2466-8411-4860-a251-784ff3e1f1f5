import { GetServerSideProps, NextPage } from "next";
import logger from "utils/logger";
import { observer } from "mobx-react-lite";
import CustomError from "@pages/custom-error";
import React from "react";
import { IPageProps } from "@pages/index";
import { Project } from "utils/project";
import { getServerSideSitemap, ISitemapField } from "next-sitemap";
import { EnumPage } from "models/enum/page";
import { EnumProjectCode, EnumServerEnv, EnumSitePathType } from "models/enum/system";
import { WebSiteApiFactory } from "api/data-providers/web-services/client-api";
import appServerConfig from "appServerConfig";
import { EnumLocale } from "locales/locales";

const log = logger("page--website-sitemap.xml");

export interface IPageSitemapXmlProps extends IPageProps {
}

const PageSitemapXml: NextPage<IPageSitemapXmlProps> = observer(({ error, hostWithPort }) => {
    if (error) {
        return <CustomError errorMessage={String(error)} />;
    }
    return null;
});

/**
 * https://www.sitemaps.org/protocol.html
 * @param context
 */
export const getServerSideProps: GetServerSideProps = async (context) => {
    try {
        const hostWithPort = context.req.headers.host;
        const project = Project.getProjectByHost(String(hostWithPort));
        const domain = Project.getProjectDomainByHost(String(hostWithPort));
        const locale = context.locale as EnumLocale;
        const sitePages = Project.getSitePages(project?.code as EnumProjectCode);

        if (!project) {
            const error = hostWithPort + "/sitemap.xml not found";
            console.error(error);
            log.error(error);
            return {
                props: {},
                notFound: true
            };
        }
        const host = domain?.hostname as string;

        const p = Project.getSitePages(project.code);

        const fields: { [key: string]: ISitemapField } = {};

        const pk = Object.keys(p);
        pk.map((k) => {
            const pageInfo = p[k as EnumPage];
            if (pageInfo.inSitemap && pageInfo.isActive && pageInfo.type === EnumSitePathType.static_page) {
                fields[k] = {
                    loc: Project.getPageLocaleUrl(locale, pageInfo.path, {}, { hostname: host, schema: "https://" }),
                    priority: pageInfo.seoPriority,
                    changefreq: pageInfo.seoChangeFrequency
                };
            }
        });

        const webServerDomain = appServerConfig.serverEnv === EnumServerEnv.local ? "http://" + hostWithPort : "https://" + host;

        const websiteApi = WebSiteApiFactory(undefined, webServerDomain + appServerConfig.server.webServicesApi.url);
        const dynamicPages = await websiteApi.webSiteControllerSitemapPages();
        if (dynamicPages.status === 200) {
            let games = dynamicPages.data.successObj?.data.gamesObj.itemsArr;
            if (Array.isArray(games) && games.length > 0) {
                for (let i = 0; i < games.length; i++) {
                    const gameInfo = games[i];
                    fields["game_" + gameInfo.gameId] = {
                        loc: Project.getPageLocaleUrl(locale, sitePages[EnumPage.gameDetails].path,
                            { gameId: gameInfo.gameId }, { hostname: host, schema: "https://" }),
                        priority: sitePages[EnumPage.gameDetails].seoPriority,
                        changefreq: sitePages[EnumPage.gameDetails].seoChangeFrequency,
                        lastmod: gameInfo.updatedAt
                    };
                }
            }
            let news = dynamicPages.data.successObj?.data.newsObj.itemsArr;
            if (Array.isArray(news) && news.length > 0) {
                for (let i = 0; i < news.length; i++) {
                    const articleInfo = news[i];
                    fields["game_" + articleInfo.id] = {
                        loc: Project.getPageLocaleUrl(locale, sitePages[EnumPage.newsArticle].path,
                            { newsId: articleInfo.id }, { hostname: host, schema: "https://" }),
                        priority: sitePages[EnumPage.newsArticle].seoPriority,
                        changefreq: sitePages[EnumPage.newsArticle].seoChangeFrequency,
                        lastmod: articleInfo.updatedAt
                    };
                }
            }
        }

        return getServerSideSitemap(context, Object.values(fields));

    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PageSitemapXml;