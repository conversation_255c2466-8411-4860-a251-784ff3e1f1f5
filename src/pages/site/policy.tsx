import React from "react";
import { observer } from "mobx-react-lite";
import { AppContainer } from "@components/containers/app-container";
import AppSections from "@components/sections/appSections";
import { IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import AppFooter from "@components/footers/app-footer";
import appConfig from "appConfig";
import { GetServerSideProps, NextPage } from "next";
import logger from "utils/logger";
import AppHeader from "@components/headers/app-header";
import SectionPrivacyPolicy from "@components/sections/privacy-policy/sectionPrivacyPolicy";
import { ProjectServiceInstance } from "api/services/projectService";
import CustomError from "@pages/custom-error";
import { useRouter } from "next/router";
import AppLoading from "@components/main/appLoading";
import { IPageProps } from "@pages/index";
import SeoSitePage from "@components/seo/seo-site-page";
import Head from "next/head";
import { useSiteTitle } from "@hooks/use-site-title";
import { useSitePageInfo } from "@hooks/use-site-page-info";
import { EnumPage } from "models/enum/page";

const log = logger("page--website-policy");

export interface IPageSitePrivacyPolicyProps extends IPageProps {
    footer: IWebsiteFooter;
}

const PageSiteWebsitePrivacyPolicy: NextPage<IPageSitePrivacyPolicyProps> = observer((
    { footer, error, hostWithPort }) => {
    try {
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        const languageCode = appConfig.systemSettings.defaultLanguageCode;

        const { pageTitle, pageUrlAbsolute, pageUrlRelative } = useSitePageInfo(EnumPage.privacy, {});

        const pageDescription =
            "Skywind Holdings Limited (“Skywind”, “us”, “we” or “our”) respects the privacy of every individual whose " +
            "personal information it handles. This Privacy Policy explains how Skywind collects, stores and uses personal information.";

        const pageImage = {
            url: "/assets/site/images/header/banner/about-header-banner.jpg",
            width: 1920,
            height: 1080
        };

        const { siteTitle } = useSiteTitle(pageTitle);

        return (
            <>
                <SeoSitePage pageTitle={pageTitle} pageDescription={pageDescription}
                             pageUrlAbsolute={pageUrlAbsolute} pageUrlRelative={pageUrlRelative}
                             pageImage={pageImage} />
                <Head>
                    <title>{siteTitle}</title>
                </Head>
                <AppContainer>
                    <AppHeader
                        className={"header-privacy-policy"}
                    />
                    <AppSections className={"container"}>
                        <SectionPrivacyPolicy />
                    </AppSections>
                    <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                </AppContainer>
            </>
        );
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;
        const footer = await ProjectServiceInstance.getWebsiteFooterData(regionCode, languageCode);
        return {
            // const footer = async

            props: { footer } // will be passed to the page
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PageSiteWebsitePrivacyPolicy;