import React from "react";
import logger from "utils/logger";
import { GetServerSideProps, NextPage } from "next";
import { observer } from "mobx-react-lite";
import { IPageProps } from "@pages/index";
import { IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { useRouter } from "next/router";
import AppLoading from "@components/main/appLoading";
import CustomError from "@pages/custom-error";
import appConfig from "appConfig";
import { ProjectServiceInstance } from "api/services/projectService";
import { useSitePageInfo } from "@hooks/use-site-page-info";
import { EnumPage } from "models/enum/page";
import { useSiteTitle } from "@hooks/use-site-title";
import SeoSitePage from "@components/seo/seo-site-page";
import Head from "next/head";
import { AppContainer } from "@components/containers/app-container";
import AppSections from "@components/sections/appSections";
import AppHeader from "@components/headers/app-header";
import AppFooter from "@components/footers/app-footer";
import FormRegisterDealer from "@components/forms/form-register-dealer/form-register-dealer";

const log = logger("page--welcome-dealers");

export interface IPageWelcomeDealersProps extends IPageProps {
    footer: IWebsiteFooter;
}

const PageWelcomeDealers: NextPage<IPageWelcomeDealersProps> = observer((
    { appServerConfig, footer, error, hostWithPort }) => {
    try {
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const { pageTitle, pageUrlAbsolute, pageUrlRelative } = useSitePageInfo(EnumPage.welcomeDealers, {});

        const pageDescription =
            "Echipa noastră este ceea ce ne face unici. De aceea, Skywind Live România începe recrutarea pentru o nouă generație de colegi, împreună cu care să ne atingem obiectivele și să ne depășim așteptările. " +
            "Dacă îți dorești să creștem împreună, ia legătura cu noi prin formularul de mai jos. " +
            "Abia așteptăm să te cunoaștem!"

        const headerImageUrl = "/assets/site/images/header/banner/about-header-banner.jpg";

        const pageImage = {
            url: headerImageUrl,
            width: 1920,
            height: 1080
        };

        const { siteTitle } = useSiteTitle(pageTitle);

        return (
            <>
                <SeoSitePage pageTitle={pageTitle} pageDescription={pageDescription}
                             pageUrlAbsolute={pageUrlAbsolute} pageUrlRelative={pageUrlRelative}
                             pageImage={pageImage} />
                <Head>
                    <title>{siteTitle}</title>
                </Head>
                <AppContainer className="welcome-dealers-page">
                    <AppHeader className={"empty"} />
                    <div className="welcome-dealers-banner">
                        <img src="assets/site/images/header/banner/banner-1920x200.png" alt=""
                             className="welcome-dealers-banner-img is-tablet-up" />
                        <img src="assets/site/images/header/banner/banner-600x140.png" alt=""
                             className="welcome-dealers-banner-img is-mobile" />
                    </div>
                    <AppSections className={"container welcome-dealers-container"}>
                        <section className="section-register-dealer">
                            <div className="welcome-dealers-headline">
                                <h2 className="welcome-dealers-headline-title">GAME PRESENTER</h2>
                                <p className="welcome-dealers-headline-text">Echipa noastră este ceea ce ne face unici.
                                    De aceea, Skywind Live România începe recrutarea pentru o nouă generație de colegi,
                                    împreună cu care să ne atingem obiectivele și să ne depășim așteptările.<br /><br />
                                    Dacă îți dorești să creștem împreună, ia legătura cu noi prin formularul de mai jos.<br />
                                    Abia așteptăm să te cunoaștem!<br /></p>
                            </div>
                            <FormRegisterDealer appServerConfig={appServerConfig} id={"register-dealer"}
                                                className={"form-register-dealer"}
                                                submitButtonName={"Aplică acum"} description={""} />
                        </section>
                    </AppSections>
                    <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                </AppContainer>
            </>
        );
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;
        const footer = await ProjectServiceInstance.getWebsiteFooterData(regionCode, languageCode);
        return {
            // const footer = async

            props: { footer } // will be passed to the page
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PageWelcomeDealers;