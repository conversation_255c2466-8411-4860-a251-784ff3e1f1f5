import React, { useContext } from "react";
import { GetServerSideProps, NextPage } from "next";
import { Game } from "utils/game";
import appConfig from "appConfig";
import { useRouter } from "next/router";
import { IProviderGame } from "api/data-providers/db/mysql/interfaces/iDBProvider";
import { IGameExtended, IMarketExtended, IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import logger from "utils/logger";
import AppFooter from "@components/footers/app-footer";
import { AppContainer } from "@components/containers/app-container";
import AppHeader from "@components/headers/app-header";
import AppSections from "@components/sections/appSections";
import AppLoading from "@components/main/appLoading";
import GameFeatures from "@components/sections/games/game-features";
import moment from "moment";
import FlipCountDown from "@components/flip-countdown/flip-count-down";
import { isMobile } from "react-device-detect";
import { observer } from "mobx-react-lite";
import GamePageHeader from "@components/headers/game-page/game-page-header";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination } from "swiper";
import { Anchor } from "react-bootstrap";
import AppImg from "@components/main/appImg";

import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { SanityProviderInstance } from "api/data-providers/sanity/sanityProvider";
import { ProjectServiceInstance } from "api/services/projectService";
import { DBProviderInstance } from "api/data-providers/db/dbProvider";
import liveGames from "data/live-games.json";
import { Project } from "utils/project";
import CustomError from "@pages/custom-error";
import { IPageProps } from "@pages/index";
import validator from "validator";
import SectionJackpotTicker from "@components/sections/jackpot-ticker/section-jackpot-ticker";
import Head from "next/head";
import SeoSiteGameDetails from "@components/seo/seo-site-game-details";
import { JsUtils } from "utils/js-utils";
import { useSiteTitle } from "@hooks/use-site-title";
import { EnumPage } from "models/enum/page";
import { MainAppContext } from "@pages/_app";

const log = logger("page--website-game-profile");

export interface IPageSiteGameProfileProps extends IPageProps {
    game: IGameExtended;
    footer: IWebsiteFooter;
    providerGame: IProviderGame | null;
}

const PageSiteGameProfile: NextPage<IPageSiteGameProfileProps> = observer((
    { hostWithPort, appServerConfig, project, game, footer, providerGame, error }) => {
    try {
        const { appStore } = useContext(MainAppContext);

        const router = useRouter();

        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        if (!JsUtils.isObjectAndNotEmpty(game)) {
            return <CustomError errorMessage={"Game not found"} />;
        }

        const languageCode = appConfig.systemSettings.defaultLanguageCode;

        const projectType = appStore.getAppProject().type;

        const headerTicker = game?.gameVideo?.headerTicker;

        const isJackpotGame = providerGame?.isJackpot;

        const gameInfo = Game.parseGame(game,
            languageCode,
            projectType,
            Project.getPageUrl(appStore, EnumPage.gameDetails, { gameId: String(game?.gameId?.current) })
        );

        if (!gameInfo) {
            return <CustomError errorMessage={"Game not found"} />;
        }

        const { gameName, gameCode, gamePageUrlRelative, gameDescription, isLiveGameType } = gameInfo;

        const pageTitle = `${gameName} | Demo Free Play`;

        const { siteTitle } = useSiteTitle(pageTitle);

        return (
            <>
                <SeoSiteGameDetails pageTitle={pageTitle} gameName={gameName} gameDescription={gameDescription}
                                    gamePageUrlRelative={gamePageUrlRelative}
                                    gamePosterUrl={Project.replaceSanityCdn(game?.gamePosterUrl)}
                                    gameHDPosterUrl={Project.replaceSanityCdn(game?.gameHDPosterUrl)} />
                <Head>
                    <title>{siteTitle}</title>
                    <meta name="robots" content="noodp" />
                    <meta name="description" content={gameDescription} key={"meta-description"} />
                </Head>
                <AppContainer>
                    <AppHeader>
                        <GamePageHeader game={game} languageCode={languageCode} providerGame={providerGame} />
                        {headerTicker?.isActive === true && moment(headerTicker?.tickerDate).isValid() && (
                            <FlipCountDown
                                hideYear
                                endAtZero
                                titlePosition="bottom"
                                theme="dark"
                                dayTitle="Days"
                                hourTitle="Hours"
                                minuteTitle="Minutes"
                                secondTitle="Seconds"
                                {...((isMobile && { size: "small" }) || { size: "medium" })}
                                endAt={moment(headerTicker?.tickerDate)
                                    .local(true)
                                    .format("YYYY-MM-DD HH:mm:ss")} // Date/Time
                            />
                        )}
                    </AppHeader>

                    <AppSections className={"base container"}>
                        <GameFeatures game={game} languageCode={languageCode} />
                        {/*<GameMetadata game={game} languageCode={languageCode} />*/}
                        {isLiveGameType &&
                            <section className="swiper-section">
                                <div className="section-title">
                                    More Live Casino Games
                                </div>
                                <Swiper
                                    modules={[Navigation, Pagination]}
                                    slidesPerView={isMobile ? 6.3 : 6.1}
                                    slidesPerGroup={6}
                                    freeMode={true}
                                    cssMode={isMobile}
                                    spaceBetween={0}
                                    grabCursor={true}
                                    loop={false}
                                    loopedSlides={6}
                                    loopAdditionalSlides={6}
                                    breakpoints={{
                                        0: {
                                            slidesPerView: isMobile ? 3.3 : 3.1,
                                            slidesPerGroup: 3
                                        },
                                        576: {
                                            slidesPerView: isMobile ? 4.3 : 4.1,
                                            slidesPerGroup: 4
                                        },
                                        768: {
                                            slidesPerView: isMobile ? 5.3 : 5.1,
                                            slidesPerGroup: 5
                                        },
                                        976: {
                                            slidesPerView: isMobile ? 6.3 : 6.1,
                                            slidesPerGroup: 6
                                        }
                                        // 1280: {
                                        //     slidesPerView: 7.2,
                                        //     slidesPerGroup: 7
                                        // },
                                    }}
                                    navigation={{
                                        nextEl: ".swiper-games .swiper-button-next",
                                        prevEl: ".swiper-games .swiper-button-prev",
                                        disabledClass: "swiper-button-disabled"
                                    }}
                                    className={"swiper-container swiper-games centered spaced"}
                                >
                                    {liveGames.map(gi => gi.gameCode !== gameCode &&
                                        <SwiperSlide key={gi.gameCode}>
                                            <Anchor href={gi.comingSoon
                                                          ? "#"
                                                          : Project.getPageUrl(appStore, EnumPage.gameDetails,
                                                    { gameId: gi.gameCode, gameTitle: gi.gameTitle }
                                                )}
                                                // className="swiper-slide"
                                            >
                                                {/*<div className="swiper-slide-title">{gi.gameTitle}</div>*/}
                                                <div className="swiper-slide-figure">
                                                    <AppImg
                                                        src={gi.gamePosterUrl}
                                                        className="swiper-slide-figure-img"
                                                    />
                                                </div>
                                            </Anchor>
                                        </SwiperSlide>
                                    )}
                                    <div className="swiper-button swiper-button-prev" />
                                    <div className="swiper-button swiper-button-next" />
                                </Swiper>
                            </section>
                        }
                        {isJackpotGame === 1 &&
                            <SectionJackpotTicker
                                appServerConfig={appServerConfig}
                                project={project}
                                className={"section sw-section-total-jackpot"}
                                title={"Game Jackpot"}
                                languageCode={languageCode}
                                gameCode={gameCode} />}

                    </AppSections>
                    <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                </AppContainer>
            </>
        );
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (context) => {
    try {

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;

        const isActive = context?.query?.isActive;
        const isActiveFlag = validator.isBoolean(String(isActive));
        const gameId = Array.isArray(context?.query?.path) ? context?.query?.path.pop() : false;
        let game: IGameExtended | null = null;

        if (typeof gameId === "string") {
            game = await SanityProviderInstance.getGameProfile(
                gameId,
                languageCode,
                isActiveFlag ? isActive === "true" : true
            );
        }

        if (!game) {
            log.error("Game Not found", game, context?.query);
            return {
                notFound: true
            };
        }
        const gameCode = Game.getFirstGameCode(game);

        const providerGame = (await DBProviderInstance.getProviderGames(regionCode))?.find((g) => g.gameCode === gameCode);

        game = Game.getGameCertificates(
            game,
            languageCode,
            game.allGameMarkets as IMarketExtended[],
            game.biggestMarkets as IMarketExtended[]
        );

        const footer = await ProjectServiceInstance.getWebsiteFooterData(regionCode, languageCode);

        return {
            props: {
                footer,
                game,
                providerGame: providerGame || null
            }
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PageSiteGameProfile;
