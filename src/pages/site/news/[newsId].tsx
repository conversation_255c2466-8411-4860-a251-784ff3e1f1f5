import React, { useContext } from "react";
import { GetServerSideProps, NextPage } from "next";
import { useRouter } from "next/router";
import appConfig from "appConfig";
import logger from "utils/logger";
import { EnumRegionCode } from "models/enum/system";
import { INewsExtended, IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import AppFooter from "@components/footers/app-footer";
import AppLoading from "@components/main/appLoading";
import { AppContainer } from "@components/containers/app-container";
import AppSections from "@components/sections/appSections";
import NewsArticle from "@components/news/newsArticle";
import AppHeader from "@components/headers/app-header";
import { SanityProviderInstance } from "api/data-providers/sanity/sanityProvider";
import { ProjectServiceInstance } from "api/services/projectService";
import moment from "moment/moment";
import CustomError from "@pages/custom-error";
import { IPageProps } from "@pages/index";
import { observer } from "mobx-react-lite";
import Head from "next/head";
import { useSiteTitle } from "@hooks/use-site-title";
import SeoNewsArticle from "@components/seo/seo-news-article";
import { SanityApi } from "utils/sanityApi";
import { EnumPage } from "models/enum/page";
import { MainAppContext } from "@pages/_app";
import { Project } from "utils/project";

const log = logger("page--website-news-profile");

export interface IPageSiteNewsArticleProps extends IPageProps {
    prevArticleId?: string | null;
    currentArticle: string;
    nextArticleId?: string | null;
    moreNewsArticles: string;
    footer: IWebsiteFooter;
}

const PageSiteNewsArticle: NextPage<IPageSiteNewsArticleProps> = observer((
    {
        appServerConfig, error, prevArticleId, currentArticle, nextArticleId, moreNewsArticles,
        footer, hostWithPort
    }) => {
    try {
        const { appStore } = useContext(MainAppContext);
        const router = useRouter();

        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }
        const languageCode = appConfig.systemSettings.defaultLanguageCode;

        const newsInfoData: INewsExtended = JSON.parse(currentArticle);
        const moreNewsArticlesData: INewsExtended[] = JSON.parse(moreNewsArticles);

        const title = SanityApi.getLocale(newsInfoData.title, languageCode);
        const highlightedTitle = SanityApi.getLocale(newsInfoData.highlightedTitle, languageCode);
        const newsArticleTitle = (highlightedTitle + " " || "") + (title || "");

        const newsListPageUrl = Project.getPageUrl(appStore, EnumPage.newsList);

        const { siteTitle } = useSiteTitle(newsArticleTitle);

        return (
            <>
                <SeoNewsArticle newsArticle={newsInfoData} />
                <Head>
                    <title>{siteTitle}</title>
                    <meta name="robots" content="noodp" />
                </Head>
                <AppContainer>
                    <AppHeader
                        className={"header-news"}
                        imageUrl={newsInfoData?.newsPreviewImageUrl}
                        videoUrl={newsInfoData?.headerPromoVideo?.videoUrl}
                    />
                    <AppSections className={"container"}>
                        <NewsArticle article={newsInfoData} prevArticleId={prevArticleId} nextArticleId={nextArticleId}
                                     languageCode={languageCode} moreNewsArticles={moreNewsArticlesData}
                                     newsListUrl={newsListPageUrl} appServerConfig={appServerConfig} />
                    </AppSections>
                    <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                </AppContainer>
            </>
        );
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (context) => {
    try {
        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;

        const { params } = context;

        const allNews = await SanityProviderInstance.getNews(EnumRegionCode.eu,
            languageCode/*, String(params?.newsId)*/);

        const newsId = String(params?.newsId);
        let prevArticleId = null;
        let currentArticle = null;
        let nextArticleId = null;
        let moreNewsArticles: INewsExtended[];
        const latestNews: number[] = [];
        if (Array.isArray(allNews)) {
            for (let i = 0; i < allNews.length; i++) {
                if (allNews[i]._id === newsId) {
                    prevArticleId = i > 0 ? allNews[i - 1] : null;
                    currentArticle = allNews[i];
                    nextArticleId = allNews[i + 1] ? allNews[i + 1] : null;
                } else {
                    const date = moment(allNews[i].newsDate, "YYYY-MM-DD");
                    if (moment().diff(date, "days") <= 90) {
                        latestNews.push(i);
                    }
                }
            }
        }

        moreNewsArticles = latestNews.sort(() => Math.random() - Math.random()) // random
            .slice(0, 5) // cut
            .sort((a, b) => a - b) // sort in desc
            .map((i) => allNews[i]);

        // if (!Array.isArray(newsInfo) || currentArticle === null) {
        //     log.error("News Not Found", newsInfo);
        //     return {
        //         notFound: true
        //     };
        // }
        const footer = await ProjectServiceInstance.getWebsiteFooterData(regionCode, languageCode);

        return {
            props: {
                footer,
                // prevArticleId: prevArticleId?._id || null,
                currentArticle: JSON.stringify(currentArticle),
                moreNewsArticles: JSON.stringify(moreNewsArticles)
                // nextArticleId: nextArticleId?._id || null
            } as IPageSiteNewsArticleProps
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PageSiteNewsArticle;
