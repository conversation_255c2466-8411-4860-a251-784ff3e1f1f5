import { NextApiRequest, NextApiResponse } from "next";
import NextCors from "utils/middleware";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    // Run the cors middleware
    // nextjs cors uses the cors package, so we invite you to check the documentation https://github.com/expressjs/cors
    await NextCors(req, res, {
        // Options
        methods: "GET,HEAD,PUT,PATCH,POST,DELETE",
        preflightContinue: false,
        optionsSuccessStatus: 204,
    });

    // Rest of the API logic
    res.json({ message: "Hello Everyone!" });
}
