import logger from "utils/logger";
import { NextApiRequest, NextApiResponse } from "next";

const log = logger("controller--api");

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    try {
        log.info();
        res.status(200).json({ status: "OK" });
    } catch (e) {
        log.error(e);
        res.status(400).json({ error: (e as Error).message, code: (e as Error).name });
    }
}
