import { NextPageContext } from "next";
import React from "react";
import AppLink from "@components/main/appLink";

function Error({ statusCode }: { statusCode: string }) {

    return (
        <section id="pageMaintenance" className="pageMaintenance">
            <div className="container-fluid">
                <div className="row">
                    <div className="col">
                        <div className="blockcenter">
                            <h2 className="title blockcenter__title" data-text="error">
                                An error <span className="accent">occurred</span>
                            </h2>
                            <p className="blockcenter__offer blockcenter__offer--maintenance">
                                {statusCode ?
                                 `An error ${statusCode} occurred on server` :
                                 "An error occurred on client"}
                            </p>
                            <AppLink href="/"  className="button button--primary blockcenter__btn">go home</AppLink>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );

}

Error.getInitialProps = ({ res, err }: NextPageContext) => {
    const statusCode = res ? res.statusCode : err ? err.statusCode : 200; // 404
    return { statusCode };
};

export default Error;
