import React from "react";
import { GetStaticProps } from "next";
import AppLink from "@components/main/appLink";

export const getStaticProps: GetStaticProps = async (/*context: GetStaticPropsContext*/) => {
    return {
        props: {}
    };
};

// pages/404.js
export default function Custom404() {

    return (
        <section id="page404" className="page404">
            <div className="container-fluid">
                <div className="row">
                    <div className="col">
                        <div className="blockcenter">
                            <h2 className="title blockcenter__title" data-text="404 error">
                                Page not <span className="accent">found</span>
                            </h2>
                            <p className="blockcenter__offer">
                                The page you are looking for might have been removed or had its name changed.
                            </p>
                            <AppLink href="/"  className="button button--primary blockcenter__btn">go home</AppLink>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}