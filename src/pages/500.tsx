import React from "react";
import { GetStaticProps } from "next";

export const getStaticProps: GetStaticProps = async (/*context: GetStaticPropsContext*/) => {
    return { props: {} };
};

export default function Custom500() {

    return (
        <section id="pageMaintenance" className="pageMaintenance">
            <div className="container-fluid">
                <div className="row">
                    <div className="col">
                        <div className="blockcenter">
                            <h2 className="title blockcenter__title" data-text="working">
                                Maintenance <span className="accent">mode</span>
                            </h2>
                            <p className="blockcenter__offer blockcenter__offer--maintenance">
                                Our website is on maintenance mode and will be back soon
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}