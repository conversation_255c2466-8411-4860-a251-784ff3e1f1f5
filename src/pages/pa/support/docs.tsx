import React from "react";
import { observer } from "mobx-react-lite";
import SupportDocs from "@components/skywind/docs/supportDocs";
import { useRouter } from "next/router";
import { EnumDBUserPermissions } from "models/enum/db";
import AppLoading from "@components/main/appLoading";
import usePaAuth from "@hooks/use-pa-auth";
import MainPaToolbar from "@components/toolbars/main-pa-toolbar";
import { AppContainer } from "@components/containers/app-container";
import { GetServerSideProps, NextPage } from "next";
import logger from "utils/logger";
import CustomError from "@pages/custom-error";
import { IPageProps } from "@pages/index";
import Head from "next/head";
import { usePaPageInfo } from "@hooks/use-pa-page-info";
import { EnumPage } from "models/enum/page";
import { usePaTitle } from "@hooks/use-pa-title";

const log = logger("page--partner-logs");

export interface IPagePaDocsProps extends IPageProps {
}

const PagePaDocs: NextPage<IPagePaDocsProps> = observer(({ error }) => {
    try {
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        const { authStore } = usePaAuth();
        const user = authStore.getUser();
        const userPermissions = user?.userPermissions?.permissions;

        const { pageTitle } = usePaPageInfo(EnumPage.supportDocs);
        const { headTitle } = usePaTitle(pageTitle);

        if (authStore.isAuthorized && userPermissions?.includes(EnumDBUserPermissions.paCanViewSwDocs)) {
            return (
                <>
                    <Head><title>{headTitle}</title></Head>
                    <MainPaToolbar />
                    <AppContainer>
                        <SupportDocs />
                    </AppContainer>
                </>
            );
        }
        return <AppLoading />;
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        return { props: {} };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PagePaDocs;
