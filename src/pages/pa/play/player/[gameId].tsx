import React, { useEffect, useState } from "react";
import { GetServerSideProps, NextPage } from "next";
import logger from "utils/logger";
import clientApi from "client/services/http";
import Router, { useRouter } from "next/router";
import { Container } from "react-bootstrap";
import { AxiosResponse } from "axios";
import { IResponseSuccess } from "api/interfaces/response";
import AppLoading from "@components/main/appLoading";
import usePaAuth from "@hooks/use-pa-auth";
import appConfig from "appConfig";
import { EnumLocalStorage } from "models/enum/local-storage";
import { IHttpResponseError } from "api/controllers/interfaces/iHttp";
import { EnumServerEnv } from "models/enum/system";
import ClientUsersService from "client/services/clientUsersService";
import { EnumUserClientAction } from "models/enum/user";
import ClientPlayersService from "client/services/clientPlayersService";
import { Tournaments } from "utils/tournamets";
import { ITournamentModel } from "iAppConfig";
import FormPlayerCode from "@components/forms/player-code/formPlayerCode";
import { AppContainer } from "@components/containers/app-container";
import CustomError from "@pages/custom-error";
import { IPageProps } from "@pages/index";
import { observer } from "mobx-react-lite";
import appServerConfig from "appServerConfig";
import Head from "next/head";
import { usePaPageInfo } from "@hooks/use-pa-page-info";
import { EnumPage } from "models/enum/page";
import { usePaTitle } from "@hooks/use-pa-title";

const log = logger("page--partner-play-player");

export interface IPagePaPlayRealGameProps extends IPageProps {
    gameId: string;
    tournament: ITournamentModel;
}

const PagePaPlayRealGame: NextPage<IPagePaPlayRealGameProps> = observer((
    { tournament, error }) => {
    try {
        const { authStore, appStore } = usePaAuth();
        const [showFormPlayerCode, setShowFormPlayerCode] = useState(false);

        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        // const { gameId } = props;

        useEffect(() => {
            setTimeout(async () => {
                if (authStore.isAuthorized) {
                    try {
                        localStorage.removeItem(EnumLocalStorage.redirectAfterLogin);
                        const resp = await ClientPlayersService.getPlayerCode();

                        const playerCode = resp.playerCode;
                        if (!resp.isValid) {
                            setShowFormPlayerCode(true);
                        } else {
                            setShowFormPlayerCode(false);
                            const urlResp = await clientApi.get<AxiosResponse<IResponseSuccess<string>>>(
                                appConfig.client.endpoints.api.games.playPromoGame
                            );
                            await ClientUsersService.clientAction(
                                EnumUserClientAction.getGameUrlTournament,
                                location.pathname,
                                {
                                    gameCode: tournament.gameCode,
                                    isActive: tournament.isActive,
                                    responseStatus: urlResp.status,
                                    playerCode
                                },
                                "playGenerateGameUrl"
                            );
                            if (urlResp && urlResp.status === 200 && urlResp?.data?.data) {
                                await Router.push(String(urlResp?.data?.data));
                            } else {
                                if (urlResp?.status === 400) {
                                    const err = urlResp?.data as any as IHttpResponseError;
                                    appStore.addNotificationError(err?.message, "Error: " + err?.code);
                                    appStore.showNotifications();
                                }
                                await Router.push(authStore.getBasicEndpoint());
                            }
                        }
                    } catch (e) {
                        appStore.addNotificationError(String(e));
                        appStore.showNotifications();
                        console.log(e);
                    }
                }
            }, 2000);
        });

        const { pageTitle } = usePaPageInfo(EnumPage.playPlayerGame);
        const { headTitle } = usePaTitle(pageTitle);

        if (showFormPlayerCode) {
            return (
                <>
                    <Head><title>{headTitle}</title></Head>

                    <AppContainer>
                    <Container fluid={"md"}>
                        <FormPlayerCode
                            id={"form-player-code"}
                            className={"form-set-player-code"}
                            submitButtonName={"Save Code"}
                            redirectOnSuccess={router.asPath}
                        />
                    </Container>
                </AppContainer>
                    </>
            );
        }

        return <AppLoading />;
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

// noinspection JSUnusedGlobalSymbols
export const getServerSideProps: GetServerSideProps = async (context) => {

    let tournament = null;
    try {
        const tournaments = Tournaments.getCurrentlyUsed({});
        tournament = tournaments.find((t) => t.gameCode === String(context.query["gameId"]));

        if (!tournament || (!Tournaments.isEnabled() && appServerConfig.serverEnv === EnumServerEnv.production)) {
            return {
                notFound: true
            };
        }

        return { props: { gameId: context.query["gameId"], tournament } };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PagePaPlayRealGame;
