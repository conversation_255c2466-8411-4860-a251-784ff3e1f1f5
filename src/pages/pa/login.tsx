import React, { useEffect } from "react";
import Router, { useRouter } from "next/router";
import usePaAuth from "@hooks/use-pa-auth";
import AppLoading from "@components/main/appLoading";
import SectionLogin from "@components/sections/login/sectionLogin";
import { GetServerSideProps, NextPage } from "next";
import logger from "utils/logger";
import CustomError from "@pages/custom-error";
import { observer } from "mobx-react-lite";
import { IPageProps } from "@pages/index";
import Head from "next/head";
import { usePaPageInfo } from "@hooks/use-pa-page-info";
import { EnumPage } from "models/enum/page";
import { usePaTitle } from "@hooks/use-pa-title";

const log = logger("page--partner-login");

export interface IPagePaLoginProps extends IPageProps {
}

const PagePaLogin: NextPage<IPagePaLoginProps> = observer(({ error }) => {
    try {
        const router = useRouter();

        const { authStore } = usePaAuth();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }
        const isAuthorised = authStore.isAuthorized;

        const { pageTitle } = usePaPageInfo(EnumPage.login);
        const { headTitle } = usePaTitle(pageTitle);

        if (!isAuthorised) {
            return <>
                <Head><title>{headTitle}</title></Head>
                <SectionLogin />
            </>;
        }

        return <AppLoading />;
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        return { props: {} };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PagePaLogin;
