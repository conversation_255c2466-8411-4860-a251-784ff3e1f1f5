import React, { useEffect } from "react";
import Router, { useRouter } from "next/router";
import { observer } from "mobx-react-lite";
import { EnumDBUserGroupCodes, EnumDBUserPermissions } from "models/enum/db";
import AppLoading from "@components/main/appLoading";
import usePaAuth from "@hooks/use-pa-auth";
import { Tournaments } from "utils/tournamets";
import MainPaToolbar from "@components/toolbars/main-pa-toolbar";
import TournamentEnded from "@components/tournaments/teller-of-tales/tournamentEnded";
import { AppContainer } from "@components/containers/app-container";
import Custom403 from "pages/403";
import TournamentLanding from "@components/tournaments/teller-of-tales/tournamentLanding";
import { GetServerSideProps, NextPage } from "next";
import logger from "utils/logger";
import CustomError from "@pages/custom-error";
import { IPageProps } from "@pages/index";
import { EnumPage } from "models/enum/page";
import Head from "next/head";
import { usePaPageInfo } from "@hooks/use-pa-page-info";
import { usePaTitle } from "@hooks/use-pa-title";

const log = logger("page--partner-tournament");

export interface IPagePaTournamentProps extends IPageProps {
}

const PagePaTournament: NextPage<IPagePaTournamentProps> = observer(({ error }) => {
    try {
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }
        const { authStore } = usePaAuth();

        const user = authStore.getUser();
        const userPermissions = user?.userPermissions?.permissions;
        const tournaments = Tournaments.getCurrentlyUsed({ filter: { isActive: true } });

        if (user?.userPermissions?.groups?.includes(EnumDBUserGroupCodes.partnerThirdParty)) {
            return <Custom403 />;
        }

        useEffect(() => {
            if (!Tournaments.isEnabled() && tournaments.length === 0) {
                Router.push(authStore.getBasicEndpoint());
            }
        }, [authStore, tournaments]);

        const { pageTitle } = usePaPageInfo(EnumPage.tournamentLanding);
        const { headTitle } = usePaTitle(pageTitle);

        if (
            authStore.isAuthorized &&
            userPermissions?.includes(EnumDBUserPermissions.paCanViewPageTournaments)
        ) {
            return (
                <>
                    <Head>
                        <title>{headTitle}</title>
                    </Head>
                    <MainPaToolbar />
                    <AppContainer className={"sw-tournament"} fluid={true}>
                        {tournaments.length === 1 &&
                            (Tournaments.isTournamentEnabled(tournaments[0])
                             ? <TournamentLanding />
                             : <TournamentEnded />)}
                    </AppContainer>
                </>
            );
        }
        return <AppLoading />;
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        return { props: {} };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PagePaTournament;
