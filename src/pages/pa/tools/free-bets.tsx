import React from "react";
import logger from "utils/logger";
import { observer } from "mobx-react-lite";
import { GetServerSideProps, NextPage } from "next";
import AppFooter from "@components/footers/app-footer";
import { IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { EnumDBUserPermissions } from "models/enum/db";
import Custom403 from "pages/403";
import { useRouter } from "next/router";
import AppLoading from "@components/main/appLoading";
import usePaAuth from "@hooks/use-pa-auth";
import appConfig from "appConfig";
import MainPaToolbar from "@components/toolbars/main-pa-toolbar";
import { AppContainer } from "@components/containers/app-container";
import { ComingSoon } from "@components/sections/coming-soon/comingSoon";
import { ProjectServiceInstance } from "api/services/projectService";
import CustomError from "@pages/custom-error";
import { IPageProps } from "@pages/index";
import Head from "next/head";
import { usePaPageInfo } from "@hooks/use-pa-page-info";
import { EnumPage } from "models/enum/page";
import { usePaTitle } from "@hooks/use-pa-title";

const log = logger("page--partner-free-bets");

export interface IPagePaFreeBetsProps extends IPageProps {
    footer: IWebsiteFooter;
}

const PagePaFreeBets: NextPage<IPagePaFreeBetsProps> = observer((
    { footer, error }) => {
    try {
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        const { authStore } = usePaAuth();
        const url = appConfig.client.endpoints.pa;
        const languageCode = appConfig.systemSettings.defaultLanguageCode;

        const { pageTitle } = usePaPageInfo(EnumPage.toolsFreeBets);
        const { headTitle } = usePaTitle(pageTitle);

        if (authStore.isAuthorized) {
            const user = authStore.getUser();
            const userPermissions = user?.userPermissions?.permissions;
            if (!userPermissions?.includes(EnumDBUserPermissions.paCanViewPageFreeBets)) {
                return <Custom403 />;
            }

            return (
                <>
                    <Head><title>{headTitle}</title></Head>
                    <MainPaToolbar />
                    <AppContainer>
                        <ComingSoon gotoUrl={url.default} className={"page404"} />
                        <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                    </AppContainer>
                </>
            );
        }
        return <AppLoading />;
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;

        const footer = await ProjectServiceInstance.getWebsiteFooterData(regionCode, languageCode);
        return {
            props: { footer }
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PagePaFreeBets;
