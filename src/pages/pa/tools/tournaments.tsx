import React from "react";
import logger from "utils/logger";
import { observer } from "mobx-react-lite";
import { GetServerSideProps, NextPage } from "next";
import AppFooter from "@components/footers/app-footer";
import AppImg from "@components/main/appImg";
import { IPageToolsTournamentExtended, IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { EnumDBUserPermissions } from "models/enum/db";
import Custom403 from "pages/403";
import { useRouter } from "next/router";
import AppLoading from "@components/main/appLoading";
import usePaAuth from "@hooks/use-pa-auth";
import appConfig from "appConfig";
import EngagementSliders from "@components/tools/engagement-sliders";
import MainPaToolbar from "@components/toolbars/main-pa-toolbar";
import { AppContainer } from "@components/containers/app-container";
import { SanityProviderInstance } from "api/data-providers/sanity/sanityProvider";
import { ProjectServiceInstance } from "api/services/projectService";
import CustomError from "@pages/custom-error";
import { IPageProps } from "@pages/index";
import Head from "next/head";
import { usePaPageInfo } from "@hooks/use-pa-page-info";
import { EnumPage } from "models/enum/page";
import { usePaTitle } from "@hooks/use-pa-title";

const log = logger("page--partner-tournaments");

export interface IPagePaTournamentsProps extends IPageProps {
    page: string;
    footer: IWebsiteFooter;
}

const PagePaTournaments: NextPage<IPagePaTournamentsProps> = observer((
    { page, footer, error }) => {
    try {
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }
        const { authStore } = usePaAuth();

        const pageData: IPageToolsTournamentExtended = JSON.parse(page);

        const languageCode = appConfig.systemSettings.defaultLanguageCode;

        const { pageTitle } = usePaPageInfo(EnumPage.toolsTournaments);
        const { headTitle } = usePaTitle(pageTitle);

        if (authStore.isAuthorized) {
            const user = authStore.getUser();
            const userPermissions = user?.userPermissions?.permissions;
            if (!userPermissions?.includes(EnumDBUserPermissions.paCanViewPageTournaments)) {
                return <Custom403 />;
            }

            return (
                <>
                    <Head><title>{headTitle}</title></Head>
                    <MainPaToolbar />
                    <AppContainer>
                        <header className="header-tools header--tools">
                            <div className="tools-bg">
                                <div className="tools-bg--desktop">
                                    <AppImg
                                        src="/assets/pa/images/tools/tools_tournaments.jpg"
                                        alt=""
                                        className="tools-bg__tournam"
                                    />
                                </div>
                                <div
                                    className="tools-bg--mobile tools-bg--mobile__tournam"
                                    style={{
                                        backgroundImage: "url('/assets/images/tools/mob/tournaments_background.png')"
                                    }}
                                />
                            </div>
                            <div className="tools-header-text tools-header-text__tournam">
                                <span>in–game display</span>
                                <span>category prizes, per bet range</span>
                                <span>flexible rewards and schedules</span>
                                <span>Easy to integrate</span>
                            </div>
                            <div className="wrap">
                                <div className="container-fluid">
                                    <div className="header-desc">
                                        <div className="row">
                                            <div className="col">
                                                <h1 className="header-desc__title title title--h1">
                                                    IN GAME <span className="accent">&nbsp;TOURNAMENTS</span>
                                                </h1>
                                            </div>
                                        </div>
                                        <div className="row">
                                            <div className="col-lg-6 col-md-12">
                                                <p className="header-desc__subtitle">More thrills and opportunities</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="header-bottom">
                                        <div className="row">
                                            <div className="col text-center">
                                                <a href="#" className="scroll" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </header>
                        <main>
                            <div className="wrap">
                                <div className="container-fluid">
                                    <div className="toolpage-offer">
                                        <div className="toolpage-offer__big toolpage-offer__big--tournam">Tournaments
                                        </div>
                                        <div className="toolpage-offer__same">
                                            <div className="row">
                                                <div className="col-lg-9 col-md-12">
                                                    Our in-game tournaments provide your players a social experience
                                                    that
                                                    melds into their game experience. With in-game tournaments, players
                                                    enjoy full immersion while relishing the social, competitive spirit.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <section className="section" style={{ paddingTop: 80 }}>
                                <EngagementSliders sliders={pageData.engagementSliders} languageCode={languageCode} />
                            </section>
                        </main>
                        <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                    </AppContainer>
                </>
            );
        }
        return <AppLoading />;
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

// noinspection JSUnusedGlobalSymbols
export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;
        //
        // const webSiteConfig = await sanityService.getWebsiteConfig(regionCode, languageCode);

        const page = await SanityProviderInstance.getPageToolsTournament(regionCode, languageCode);
        const footer = await ProjectServiceInstance.getWebsiteFooterData(regionCode, languageCode);

        if (!page || !footer) {
            return {
                notFound: true
            };
        }

        return { props: { page: JSON.stringify(page), footer } };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PagePaTournaments;
