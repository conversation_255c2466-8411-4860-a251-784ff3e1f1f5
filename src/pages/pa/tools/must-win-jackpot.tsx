import React from "react";
import logger from "utils/logger";
import { observer } from "mobx-react-lite";
import { GetServerSideProps, NextPage } from "next";
import AppFooter from "@components/footers/app-footer";
import AppImg from "@components/main/appImg";
import { IPageToolsJackpotExtended, IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { EnumDBUserPermissions } from "models/enum/db";
import Custom403 from "pages/403";
import { useRouter } from "next/router";
import AppLoading from "@components/main/appLoading";
import usePaAuth from "@hooks/use-pa-auth";
import appConfig from "appConfig";
import JackpotFeatures from "@components/tools/jackpot-features";
import EngagementSliders from "@components/tools/engagement-sliders";
import MainPaToolbar from "@components/toolbars/main-pa-toolbar";
import { AppContainer } from "@components/containers/app-container";
import { SanityProviderInstance } from "api/data-providers/sanity/sanityProvider";
import { ProjectServiceInstance } from "api/services/projectService";
import CustomError from "@pages/custom-error";
import { IPageProps } from "@pages/index";
import Head from "next/head";
import { usePaPageInfo } from "@hooks/use-pa-page-info";
import { EnumPage } from "models/enum/page";
import { usePaTitle } from "@hooks/use-pa-title";

const log = logger("page--partner-must-win-jackpot");

export interface IPagePaMustWinJackpotProps extends IPageProps {
    page: string;
    footer: IWebsiteFooter;
}

const PagePaMustWinJackpot: NextPage<IPagePaMustWinJackpotProps> = observer((
    { page, footer, error }) => {
    try {
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }
        const { authStore } = usePaAuth();

        const pageData: IPageToolsJackpotExtended = JSON.parse(page);

        const languageCode = appConfig.systemSettings.defaultLanguageCode;

        const { pageTitle } = usePaPageInfo(EnumPage.toolsMustWinJackpots);
        const { headTitle } = usePaTitle(pageTitle);

        // If the page is not yet generated, this will be displayed
        // initially until getStaticProps() finishes running
        if (authStore.isAuthorized) {
            const user = authStore.getUser();
            const userPermissions = user?.userPermissions?.permissions;
            if (!userPermissions?.includes(EnumDBUserPermissions.paCanViewPageMustWinJackpot)) {
                return <Custom403 />;
            }

            return (
                <>
                    <Head><title>{headTitle}</title></Head>
                    <MainPaToolbar />
                    <AppContainer>
                        <header className="header-tools header--tools">
                            <div className="tools-bg">
                                <div className="tools-bg--desktop">
                                    <AppImg
                                        src="/assets/pa/images/tools/tools_jackpots.jpg"
                                        alt=""
                                        className="tools-bg__jp"
                                    />
                                </div>
                                <div
                                    className="tools-bg--mobile tools-bg--mobile__jp"
                                    style={{ backgroundImage: "url('/assets/images/tools/mob/jp_background.jpg')" }}
                                />
                            </div>
                            <div className="tools-header-text tools-header-text__jp">
                                <span>Fully configurable Branded Panel</span>
                                <span>Engaged players</span>
                                <span>Easy to integrate</span>
                                <div
                                    className="tools-header-text__jp--mobile eng-header-total-jp"
                                    style={{ display: "none" }}
                                >
                                    <div className="jp-odometer">0.00</div>
                                    <div>Time Based Jackpot</div>
                                </div>
                            </div>
                            <div className="wrap">
                                <div className="container-fluid">
                                    <div className="header-desc">
                                        <div className="row">
                                            <div className="col">
                                                <h1 className="header-desc__title title title--h1">
                                                    MUST-WIN <span className="accent">JACKPOT</span>
                                                </h1>
                                            </div>
                                        </div>
                                        <div className="row">
                                            <div className="col-lg-6 col-md-12">
                                                <p className="header-desc__subtitle">
                                                    More opportunities. More engagement.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="header-bottom">
                                        <div className="row">
                                            <div className="col text-center" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </header>
                        <main>
                            <div className="wrap">
                                <div className="container-fluid">
                                    <div className="toolpage-offer">
                                        <div className="toolpage-offer__big toolpage-offer__big--tournam">Jackpots</div>
                                        <div className="toolpage-offer__same">
                                            <div className="row">
                                                <div className="col-lg-9 col-md-12">
                                                    Time Based Jackpots. Amount Based Jackpots. Shared Wins Jackpots.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <section className="section" style={{ paddingTop: 80 }}>
                                <EngagementSliders sliders={pageData.engagementSliders} languageCode={languageCode} />
                            </section>
                            <JackpotFeatures features={pageData.jackpotsInfo} languageCode={languageCode} />
                        </main>
                        <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                    </AppContainer>
                </>
            );
        }
        return <AppLoading />;
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {
        const languageCode = appConfig.systemSettings.defaultLanguageCode;

        const regionCode = appConfig.systemSettings.defaultRegionCode;

        const page = await SanityProviderInstance.getPageToolsJackpot(regionCode, languageCode);
        const footer = await ProjectServiceInstance.getWebsiteFooterData(regionCode, languageCode);

        if (!page || !footer) {
            return {
                notFound: true
            };
        }

        return { props: { page: JSON.stringify(page), footer } };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

// noinspection JSUnusedGlobalSymbols
export default PagePaMustWinJackpot;
