import React from "react";
import { observer } from "mobx-react-lite";
import { GetServerSideProps, NextPage } from "next";
import logger from "utils/logger";
import { Game } from "utils/game";
import { useRouter } from "next/router";
import { isMobile } from "react-device-detect";
import moment from "moment";
import { IProviderGame } from "api/data-providers/db/mysql/interfaces/iDBProvider";
import { IGameExtended, IMarketExtended, IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { EnumDBUserPermissions } from "models/enum/db";
import Custom403 from "pages/403";
import AppLoading from "@components/main/appLoading";
import usePaAuth from "@hooks/use-pa-auth";
import appConfig from "appConfig";
import AppFooter from "@components/footers/app-footer";
import FlipCountDown from "@components/flip-countdown/flip-count-down";
import { AppContainer } from "@components/containers/app-container";
import GameFeatures from "@components/sections/games/game-features";
import GamePageHeader from "@components/headers/game-page/game-page-header";
import { SanityProviderInstance } from "api/data-providers/sanity/sanityProvider";
import { ProjectServiceInstance } from "api/services/projectService";
import { DBProviderInstance } from "api/data-providers/db/dbProvider";
import CustomError from "@pages/custom-error";
import { IPageProps } from "@pages/index";
import validator from "validator";
import MainPaToolbar from "@components/toolbars/main-pa-toolbar";
import Head from "next/head";
import { usePaPageInfo } from "@hooks/use-pa-page-info";
import { EnumPage } from "models/enum/page";
import { usePaTitle } from "@hooks/use-pa-title";
import { SanityApi } from "utils/sanityApi";

const log = logger("page--partner-game-profile");

export interface IPagePaGameProfileProps extends IPageProps {
    game: string;
    footer: IWebsiteFooter;
    providerGame: string;
}

const PagePaGameProfile: NextPage<IPagePaGameProfileProps> = observer((
    { game, footer, providerGame, error }) => {
    try {
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        const { authStore } = usePaAuth();

        const languageCode = appConfig.systemSettings.defaultLanguageCode;

        const { pageTitle } = usePaPageInfo(EnumPage.gameDetails);
        const { headTitle } = usePaTitle(pageTitle);

        // If the page is not yet generated, this will be displayed
        // initially until getStaticProps() finishes running
        if (authStore.isAuthorized && game && footer && providerGame) {
            const user = authStore.getUser();
            const userPermissions = user?.userPermissions?.permissions;
            if (!userPermissions?.includes(EnumDBUserPermissions.paCanViewPageGameInfo)) {
                return <Custom403 />;
            }

            const gameData = JSON.parse(game) as IGameExtended;
            const headerTicker = gameData.gameVideo?.headerTicker;

            return (
                <>
                    <Head><title>{SanityApi.getLocale(gameData.gameName, languageCode) + " | " + headTitle}</title></Head>
                    <MainPaToolbar />
                    <AppContainer>
                        <div className={"base"}>
                            <div className="header-banner">
                                <GamePageHeader
                                    game={gameData}
                                    languageCode={languageCode}
                                    providerGame={JSON.parse(providerGame) as IProviderGame}
                                />
                                {headerTicker?.isActive === true && moment(headerTicker?.tickerDate).isValid() && (
                                    <FlipCountDown
                                        hideYear
                                        endAtZero
                                        titlePosition="bottom"
                                        theme="dark"
                                        dayTitle="Days"
                                        hourTitle="Hours"
                                        minuteTitle="Minutes"
                                        secondTitle="Seconds"
                                        {...((isMobile && { size: "small" }) || { size: "medium" })}
                                        endAt={moment(headerTicker?.tickerDate)
                                            .local(true)
                                            .format("YYYY-MM-DD HH:mm:ss")} // Date/Time
                                    />
                                )}
                            </div>
                        </div>
                        <div className="overflow-hidden container">
                            <GameFeatures game={gameData} languageCode={languageCode} />
                        </div>
                        {/*<GameMetadata game={game} languageCode={languageCode} />*/}
                        <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                    </AppContainer>
                </>
            );
        }
        return <AppLoading />;
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (context) => {
    try {

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;

        const isActive = context?.query?.isActive;
        const isActiveFlag = validator.isBoolean(String(isActive));
        const gameId = Array.isArray(context?.query?.path) ? context?.query?.path.pop() : false;
        let game = null;

        if(typeof gameId === "string") {
            game = await SanityProviderInstance.getGameProfile(
                gameId,
                languageCode,
                isActiveFlag ? isActive === "true" : true
            );
        }

        if (!game) {
            log.error("Game Not found", game, context?.query);
            return {
                notFound: true
            };
        }
        const gameInfo = Game.getGameInfoFromBI(game);
        if (!gameInfo?.release_date && Array.isArray(game.roadmapGame) && game.roadmapGame.length) {
            let marketId = null;
            if (Array.isArray(game.allGameMarkets)) {
                for (const allGameMarket of game.allGameMarkets) {
                    if (allGameMarket.code?.current === "mga") {
                        marketId = allGameMarket._id;
                        break;
                    }
                }
            }
            if (marketId) {
                for (const roadmapGameElement of game.roadmapGame) {
                    if (roadmapGameElement.game._ref === game._id) {
                        for (const roadmapGameMarketElement of roadmapGameElement.roadmapGameMarket) {
                            if (marketId === roadmapGameMarketElement.gameMarket._ref) {
                                game.marketReleaseDate = roadmapGameMarketElement.startDate;
                                break;
                            }
                        }
                    }
                }
            }
            // const roadmapGameMarket = game.roadmapGame?.game?._ref;
        }
        delete game.roadmapGame;

        const gameCode = Game.getFirstGameCode(game);

        const providerGame = (await DBProviderInstance.getProviderGames(regionCode))?.find((g) => g.gameCode === gameCode);

        game = Game.getGameCertificates(
            game,
            languageCode,
            game.allGameMarkets as IMarketExtended[],
            game.biggestMarkets as IMarketExtended[]
        );

        const footer = await ProjectServiceInstance.getWebsiteFooterData(regionCode, languageCode);

        return {
            props: {
                footer,
                game: JSON.stringify(game),
                providerGame: JSON.stringify(providerGame || null)
            }
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PagePaGameProfile;
