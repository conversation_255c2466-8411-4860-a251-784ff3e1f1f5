import React from "react";
import { observer } from "mobx-react-lite";
import { GetServerSideProps, NextPage } from "next";
import logger from "utils/logger";
import appConfig from "appConfig";
import AppFooter from "@components/footers/app-footer";
import BannersCarousel from "@components/banners/bannersCarousel";
import { IBannersCarouselExtended, IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { EnumDBUserPermissions } from "models/enum/db";
import Custom403 from "pages/403";
import { useRouter } from "next/router";
import usePaAuth from "@hooks/use-pa-auth";
import RoadmapGrid from "@components/roadmap-grid/roadmapGrid";
import MainPaToolbar from "@components/toolbars/main-pa-toolbar";
import AppLoading from "@components/main/appLoading";
import { AppContainer } from "@components/containers/app-container";
import { ProjectServiceInstance } from "api/services/projectService";
import { SanityProviderInstance } from "api/data-providers/sanity/sanityProvider";
import CustomError from "@pages/custom-error";
import { IPageProps } from "@pages/index";
import Head from "next/head";
import { usePaPageInfo } from "@hooks/use-pa-page-info";
import { EnumPage } from "models/enum/page";
import { usePaTitle } from "@hooks/use-pa-title";
import { GamesBrand } from "@components/roadmap-grid/roadmapInterfaces";

const log = logger("page--partner-roadmap");

export interface IPagePaRoadmapSkywindProps extends IPageProps {
    footer: IWebsiteFooter;
    pageBanners: string;
}

const PagePaRoadmapSkywind: NextPage<IPagePaRoadmapSkywindProps> = observer(({ footer, pageBanners, error }) => {
    try {
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }
        const { authStore } = usePaAuth();

        const { pageTitle } = usePaPageInfo(EnumPage.roadmapGames);
        const { headTitle } = usePaTitle(pageTitle);

        if (authStore.isAuthorized) {
            const user = authStore.getUser();
            const userPermissions = user?.userPermissions?.permissions;
            if (!userPermissions?.includes(EnumDBUserPermissions.paCanViewPageRoadmap)) {
                return <Custom403 />;
            }

            const languageCode = appConfig.systemSettings.defaultLanguageCode;

            const bannersCarousel: IBannersCarouselExtended = JSON.parse(pageBanners);

            return (
                <>
                    <Head><title>{headTitle}</title></Head>
                    <MainPaToolbar />
                    <AppContainer fixed>
                        <BannersCarousel className={"roadmap-banner"} bannersCarousel={bannersCarousel} />
                        <RoadmapGrid id="roadmap-games-grid" className={"roadmap-games"} brand={GamesBrand.SKYWIND}/>
                    </AppContainer>
                    <AppFooter footerData={footer} languageCode={languageCode} fixed={true} />
                </>
            );
        }

        return <AppLoading />;
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;

        const footer = await ProjectServiceInstance.getWebsiteFooterData(regionCode, languageCode);

        const pageBanners = await SanityProviderInstance.pageBannersCarousel(languageCode, "pageRoadmap-partner-area");

        return {
            props: { footer, pageBanners: JSON.stringify(pageBanners) }
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PagePaRoadmapSkywind;
