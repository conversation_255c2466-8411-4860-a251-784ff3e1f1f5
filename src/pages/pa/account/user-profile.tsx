// noinspection JSUnusedGlobalSymbols

import React from "react";
import { observer } from "mobx-react-lite";
import { Container } from "react-bootstrap";
import { EnumDBUserPermissions } from "models/enum/db";
import Custom403 from "pages/403";
import { useRouter } from "next/router";
import AppLoading from "@components/main/appLoading";
import usePaAuth from "@hooks/use-pa-auth";
import FormPlayerCode from "@components/forms/player-code/formPlayerCode";
import MainPaToolbar from "@components/toolbars/main-pa-toolbar";
import { AppContainer } from "@components/containers/app-container";
import { GetServerSideProps, NextPage } from "next";
import logger from "utils/logger";
import CustomError from "@pages/custom-error";
import { IPageProps } from "@pages/index";
import FormChangeUserPassword from "@components/forms/change-password/form-change-user-password";
import Head from "next/head";
import { usePaPageInfo } from "@hooks/use-pa-page-info";
import { EnumPage } from "models/enum/page";
import { usePaTitle } from "@hooks/use-pa-title";

const log = logger("page--partner-user-profile");

export interface IPagePaUserProfileProps extends IPageProps {
}

const PagePaUserProfile: NextPage<IPagePaUserProfileProps> = observer(({ error }) => {
    try {
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        const { authStore } = usePaAuth();

        const { pageTitle } = usePaPageInfo(EnumPage.accountUserProfile);
        const { headTitle } = usePaTitle(pageTitle);

        if (authStore.isAuthorized) {
            const user = authStore.getUser();
            const userPermissions = user?.userPermissions?.permissions;
            if (!userPermissions?.includes(EnumDBUserPermissions.paCanViewPageAccount)) {
                return <Custom403 />;
            }

            return (
                <>
                    <Head><title>{headTitle}</title></Head>
                    <MainPaToolbar />
                    <AppContainer>
                        <Container fluid="xl" style={{ maxWidth: 640 }}>
                            <h4 style={{ paddingTop: "30px" }}>My Profile</h4>
                            {/*<FormProfile id={"my-profile"} className={"my-profile"} submitButtonName={"Save"} />*/}
                            {userPermissions?.includes(EnumDBUserPermissions.paCanUseTournamentPlayer) && (
                                <h4 style={{ paddingTop: "30px" }}>Player Code</h4>
                            )}
                            {userPermissions?.includes(EnumDBUserPermissions.paCanUseTournamentPlayer) && (
                                <FormPlayerCode
                                    id={"form-player-code"}
                                    className={"form-set-player-code"}
                                    submitButtonName={"Save Code"}
                                />
                            )}
                            {userPermissions?.includes(EnumDBUserPermissions.paCanRestorePassword) && (
                                <>
                                    <h4 style={{ paddingTop: "30px" }}>Change password</h4>
                                    <FormChangeUserPassword
                                        className={"form-reset-user-password"}
                                        id={"form-reset-user-password"}
                                        submitButtonName={"Change password"}
                                    />
                                </>
                            )}
                            {/*        <Typography variant="h5" component="div" gutterBottom sx={{ paddingTop: "20px" }}>*/}
                            {/*            Reset password*/}
                            {/*        </Typography>*/}
                            {/*        <FormChangeUserPassword />*/}
                        </Container>
                    </AppContainer>
                </>
            );
        }
        return <AppLoading />;
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        return { props: {} };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PagePaUserProfile;
