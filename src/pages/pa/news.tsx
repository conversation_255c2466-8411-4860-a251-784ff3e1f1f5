import React, { useEffect, useState } from "react";
import { observer } from "mobx-react-lite";
import appConfig from "appConfig";
import { GetServerSideProps, NextPage } from "next";
import NewsList from "@components/news/newsList";
import ClientProjectService from "client/services/clientProjectService";
import logger from "utils/logger";
import AppFooter from "@components/footers/app-footer";
import BannersCarousel from "@components/banners/bannersCarousel";
import {
    IBannersCarouselExtended,
    INewsExtended,
    IWebsiteFooter
} from "api/data-providers/sanity/interfaces/iSanityProvider";
import { EnumDBUserPermissions } from "models/enum/db";
import Custom403 from "pages/403";
import { useRouter } from "next/router";
import AppLoading from "../../client/components/main/appLoading";
import usePaAuth from "@hooks/use-pa-auth";
import MainPaToolbar from "@components/toolbars/main-pa-toolbar";
import { AppContainer } from "@components/containers/app-container";
import { ProjectServiceInstance } from "api/services/projectService";
import { SanityProviderInstance } from "api/data-providers/sanity/sanityProvider";
import CustomError from "@pages/custom-error";
import { IPageProps } from "@pages/index";
import Head from "next/head";
import { usePaPageInfo } from "@hooks/use-pa-page-info";
import { EnumPage } from "models/enum/page";
import { usePaTitle } from "@hooks/use-pa-title";

const log = logger("page--news-list");

export interface IPagePaNewsProps extends IPageProps {
    footer: IWebsiteFooter;
    pageBanner: string;
}

const PagePaNews: NextPage<IPagePaNewsProps> = observer(({ footer, pageBanner, error }) => {
    try {
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }
        const { authStore } = usePaAuth();
        const user = authStore.getUser();
        const userPermissions = user?.userPermissions?.permissions;

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const [news, setNews] = useState([] as INewsExtended[]);

        useEffect(() => {
            if (userPermissions?.includes(EnumDBUserPermissions.paCanViewPageNews)) {
                (async () => {
                    setNews((await ClientProjectService.getNewsList()) as INewsExtended[]);
                })();
            }
        }, [userPermissions]);

        const { pageTitle } = usePaPageInfo(EnumPage.newsList);
        const { headTitle } = usePaTitle(pageTitle);

        if (authStore.isAuthorized) {
            if (!userPermissions?.includes(EnumDBUserPermissions.paCanViewPageNews)) {
                return <Custom403 />;
            }

            const bannersCarousel: IBannersCarouselExtended = JSON.parse(pageBanner);
            return (
                <>
                    <Head><title>{headTitle}</title></Head>
                    <MainPaToolbar />
                    <AppContainer fixed>
                        <BannersCarousel className={"roadmap-banner"} bannersCarousel={bannersCarousel} />
                        <NewsList news={news} languageCode={languageCode} />
                    </AppContainer>
                    <AppFooter footerData={footer} languageCode={languageCode} fixed={true} />
                </>
            );
        }

        return <AppLoading />;
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;

        const footer = await ProjectServiceInstance.getWebsiteFooterData(regionCode, languageCode);

        const pageBanner = await SanityProviderInstance.pageBannersCarousel(languageCode, "pageNews-partner-area");

        return {

            props: { footer, pageBanner: JSON.stringify(pageBanner) }
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PagePaNews;
