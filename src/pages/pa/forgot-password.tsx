import React, { useContext, useEffect } from "react";
import Router, { useRouter } from "next/router";
import { Card } from "react-bootstrap";
import AppLink from "@components/main/appLink";
import { observer } from "mobx-react-lite";
import { SwLogo } from "@components/main/sw-logo/swLogo";
import FormForgotPassword from "@components/forms/forgot-password/form-forgot-password";
import { GetServerSideProps, NextPage } from "next";
import usePaAuth from "@hooks/use-pa-auth";
import logger from "utils/logger";
import CustomError from "@pages/custom-error";
import AppLoading from "@components/main/appLoading";
import { IPageProps } from "@pages/index";
import { MainAppContext } from "@pages/_app";
import { Project } from "utils/project";
import { EnumPage } from "models/enum/page";
import Head from "next/head";
import { usePaPageInfo } from "@hooks/use-pa-page-info";
import { usePaTitle } from "@hooks/use-pa-title";

const log = logger("page--partner-forgot-password");

export interface IPagePaForgotPasswordProps extends IPageProps {
}

const PagePaForgotPassword: NextPage<IPagePaForgotPasswordProps> = observer(({ error }) => {
    try {
        const { appStore } = useContext(MainAppContext);

        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }
        const { authStore } = usePaAuth();

        useEffect(() => {
            if (authStore.isAuthorized) {
                Router.push(authStore.getBasicEndpoint()).then();
            }
        });

        const { pageTitle } = usePaPageInfo(EnumPage.forgotPassword);
        const { headTitle } = usePaTitle(pageTitle);

        if (!authStore.isAuthorized) {
            return (
                <>
                    <Head><title>{headTitle}</title></Head>

                    <div className="row justify-content-center align-items-center">
                        <Card style={{ minWidth: 320, maxWidth: 480 }}>
                            <Card.Body>
                                <div className={"form-logo"}>
                                    <SwLogo className={""} />
                                </div>
                                <Card.Text className={"text-h6"}>
                                    Lost your password? Please enter your login or email address. You will receive a
                                    link to
                                    create a new password via email.
                                </Card.Text>
                                <FormForgotPassword
                                    className={"form-forgot-password"}
                                    id={"form-forgot-password"}
                                    submitButtonName={"Reset password"}
                                />
                                <hr className="line-divider" />
                                <div className="sign-in-footer">
                                    <p className="">
                                        <AppLink href={Project.getPageUrl(appStore, EnumPage.login)}>Sign in</AppLink>
                                    </p>
                                    <p className="">
                                        <AppLink href={Project.getPageUrl(appStore, EnumPage.register)}>
                                            Do not have an account? Sign up
                                        </AppLink>
                                    </p>
                                </div>
                            </Card.Body>
                        </Card>
                    </div>
                </>
            );
        }
        return <AppLoading />;
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        return { props: {} };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

// noinspection JSUnusedGlobalSymbols
export default PagePaForgotPassword;
