import React, { useContext } from "react";
import { observer } from "mobx-react-lite";
import { GetServerSideProps, NextPage } from "next";
import logger from "utils/logger";
import AppSections from "@components/sections/appSections";
import NewsArticle from "@components/news/newsArticle";
import { SanityApi } from "utils/sanityApi";
import { useRouter } from "next/router";
import { ILocaleString } from "api/data-providers/sanity/schemas";
import { INewsExtended, IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { EnumDBUserPermissions } from "models/enum/db";
import Custom403 from "pages/403";
import AppLoading from "@components/main/appLoading";
import usePaAuth from "@hooks/use-pa-auth";
import appConfig from "appConfig";
import AppFooter from "@components/footers/app-footer";
import HeaderNews from "@components/headers/header-news";
import { AppContainer } from "@components/containers/app-container";
import { ProjectServiceInstance } from "api/services/projectService";
import { SanityProviderInstance } from "api/data-providers/sanity/sanityProvider";
import { EnumRegionCode } from "models/enum/system";
import moment from "moment/moment";
import CustomError from "@pages/custom-error";
import { IPageProps } from "@pages/index";
import MainPaToolbar from "@components/toolbars/main-pa-toolbar";
import { MainAppContext } from "@pages/_app";
import { EnumPage } from "models/enum/page";
import { Project } from "utils/project";
import Head from "next/head";
import { usePaPageInfo } from "@hooks/use-pa-page-info";
import { usePaTitle } from "@hooks/use-pa-title";

const log = logger("page--partner-news-profile");

export interface IPagePaNewsArticleProps extends IPageProps {
    prevArticleId?: string | null;
    currentArticle: string;
    nextArticleId?: string | null;
    moreNewsArticles: string;
    footer: IWebsiteFooter;
}

const PagePaNewsArticle: NextPage<IPagePaNewsArticleProps> = observer((
    {
        appServerConfig, footer, prevArticleId, currentArticle, nextArticleId, moreNewsArticles,
        error
    }) => {
    try {
        const { appStore } = useContext(MainAppContext);
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }

        const { authStore } = usePaAuth();
        const user = authStore.getUser();
        const userPermissions = user?.userPermissions?.permissions;

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const newsInfoData: INewsExtended = JSON.parse(currentArticle);
        // noinspection JSUnusedLocalSymbols
        const moreNewsArticlesData: INewsExtended[] = JSON.parse(moreNewsArticles);

        const { pageTitle } = usePaPageInfo(EnumPage.newsArticle);

        const title = SanityApi.getLocale(newsInfoData.title, languageCode);
        const highlightedTitle = SanityApi.getLocale(newsInfoData.highlightedTitle, languageCode);
        const newsArticleTitle = (highlightedTitle + " " || "") + (title || "");
        const { headTitle } = usePaTitle(newsArticleTitle + " | " + pageTitle);

        // If the page is not yet generated, this will be displayed
        // initially until getStaticProps() finishes running
        if (authStore.isAuthorized && userPermissions?.includes(EnumDBUserPermissions.paCanViewPageNews)) {
            const newsTitle = SanityApi.getLocale(newsInfoData.title as ILocaleString, languageCode);

            const user = authStore.getUser();
            const userPermissions = user?.userPermissions?.permissions;
            if (!userPermissions?.includes(EnumDBUserPermissions.paCanViewPageNewsInfo)) {
                return <Custom403 />;
            }

            return (
                <>
                    <Head><title>{headTitle}</title></Head>
                    <MainPaToolbar />
                    <AppContainer>
                        <HeaderNews
                            className={"header header-news"}
                            video={newsInfoData.headerPromoVideo}
                            imageUrl={newsInfoData.newsPreviewImageUrl}
                            title={newsTitle}
                            languageCode={languageCode}
                        />
                        <AppSections className={"container"}>
                            <NewsArticle article={newsInfoData} prevArticleId={prevArticleId}
                                         nextArticleId={nextArticleId}
                                         languageCode={languageCode} moreNewsArticles={[]}
                                         newsListUrl={Project.getPageUrl(appStore, EnumPage.newsList)}
                                         appServerConfig={appServerConfig} />
                        </AppSections>
                    </AppContainer>
                    <AppFooter footerData={footer} languageCode={languageCode} fixed={false} />
                </>
            );
        }
        return <AppLoading />;
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (context) => {
    try {
        const { params } = context;
        // const sanity = dataProviders.Sanity;
        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;

        // const newsInfo = await sanity.getNews(EnumRegionCode.eu, languageCode, String(params?.newsId));
        //
        // const webSiteConfig = await sanity.getWebsiteConfig(EnumRegionCode.eu, languageCode);
        const newsInfo = await SanityProviderInstance.getNews(EnumRegionCode.eu,
            languageCode/*, String(params?.newsId)*/);

        const newsId = String(params?.newsId);
        let prevArticleId = null;
        let currentArticle = null;
        let nextArticleId = null;
        let moreNewsArticles: INewsExtended[];
        const latestNews: number[] = [];
        if (Array.isArray(newsInfo)) {
            for (let i = 0; i < newsInfo.length; i++) {
                if (newsInfo[i]._id === newsId) {
                    prevArticleId = i > 0 ? newsInfo[i - 1] : null;
                    currentArticle = newsInfo[i];
                    nextArticleId = newsInfo[i + 1] ? newsInfo[i + 1] : null;
                    break;
                } else {
                    const date = moment(newsInfo[i].newsDate, "YYYY-MM-DD");
                    if (moment().diff(date, "days") <= 90) {
                        latestNews.push(i);
                    }
                }
            }

        }

        moreNewsArticles = latestNews.sort(() => Math.random() - Math.random()) // random
            .slice(0, 5) // cut
            .sort((a, b) => a - b) // sort in desc
            .map((i) => newsInfo[i]);

        const footer = await ProjectServiceInstance.getWebsiteFooterData(regionCode, languageCode);
        return {
            props: {
                // prevArticleId: prevArticleId?._id || null,
                // newsId: params?.newsId,
                currentArticle: JSON.stringify(currentArticle),
                // nextArticleId: nextArticleId?._id || null,
                moreNewsArticles: JSON.stringify(moreNewsArticles),
                footer
            } as IPagePaNewsArticleProps
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PagePaNewsArticle;
