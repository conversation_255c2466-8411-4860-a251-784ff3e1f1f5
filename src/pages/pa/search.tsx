import React from "react";
import { observer } from "mobx-react-lite";
import appConfig from "appConfig";
import { GetServerSideProps, NextPage } from "next";
import logger from "utils/logger";
import AppFooter from "@components/footers/app-footer";
import { IBannersCarouselExtended, IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { EnumDBUserPermissions } from "models/enum/db";
import Custom403 from "pages/403";
import { useRouter } from "next/router";
import usePaAuth from "@hooks/use-pa-auth";
import MainPaToolbar from "@components/toolbars/main-pa-toolbar";
import AppLoading from "@components/main/appLoading";
import { AppContainer } from "@components/containers/app-container";
import { ProjectServiceInstance } from "api/services/projectService";
import { SanityProviderInstance } from "api/data-providers/sanity/sanityProvider";
import GamesSearchTable from "@components/games-search/games-search-table";
import CustomError from "@pages/custom-error";
import { IPageProps } from "@pages/index";
import { usePaPageInfo } from "@hooks/use-pa-page-info";
import { EnumPage } from "models/enum/page";
import { usePaTitle } from "@hooks/use-pa-title";
import Head from "next/head";

const log = logger("page--partner-search");

export interface IPagePaSearchProps extends IPageProps {
    footer: IWebsiteFooter;
    pageBanner: string;
}

const PagePaSearch: NextPage<IPagePaSearchProps> = observer(({ footer, pageBanner, error }) => {
    try {
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }
        const { authStore } = usePaAuth();

        const { pageTitle } = usePaPageInfo(EnumPage.search);
        const { headTitle } = usePaTitle(pageTitle);

        if (authStore.isAuthorized) {
            const user = authStore.getUser();
            const userPermissions = user?.userPermissions?.permissions;
            if (!userPermissions?.includes(EnumDBUserPermissions.paCanViewPageSearch)) {
                return <Custom403 />;
            }

            const languageCode = appConfig.systemSettings.defaultLanguageCode;
            const regionCode = appConfig.systemSettings.defaultRegionCode;

            const bannersCarousel: IBannersCarouselExtended = JSON.parse(pageBanner);
            return (
                <>
                    <Head><title>{headTitle}</title></Head>
                    <MainPaToolbar />
                    <AppContainer fixed>
                        <GamesSearchTable
                            id="partners-search"
                            languageCode={languageCode}
                            regionCode={regionCode}
                            bannersCarousel={bannersCarousel}
                        />
                    </AppContainer>
                    <AppFooter footerData={footer} languageCode={languageCode} fixed={true} />
                </>
            );
        }
        return <AppLoading />;
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        const languageCode = appConfig.systemSettings.defaultLanguageCode;
        const regionCode = appConfig.systemSettings.defaultRegionCode;

        const footer = await ProjectServiceInstance.getWebsiteFooterData(regionCode, languageCode);
        const pageBanner = await SanityProviderInstance.pageBannersCarousel(languageCode,
            "pageGameSearch-partner-area");

        return {
            props: { footer, pageBanner: JSON.stringify(pageBanner) }
        };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PagePaSearch;
