import React from "react";
import { GetServerSideProps, NextPage } from "next";
import logger from "utils/logger";
import { observer } from "mobx-react-lite";
import CustomError from "@pages/custom-error";
import { useRouter } from "next/router";
import AppLoading from "@components/main/appLoading";
import { IPageProps } from "@pages/index";

const log = logger("page--partner-sign-up");

export interface IPagePaSignUpProps extends IPageProps {
}

const PagePaSignUp: NextPage<IPagePaSignUpProps> = observer(({ error }) => {
    try {
        const router = useRouter();
        if (router.isFallback) {
            return <AppLoading />;
        }
        if (error) {
            return <CustomError errorMessage={String(error)} />;
        }
        // const { authStore } = useContext(AppContext);
        // const isAuthorised = authStore.isAuthorized;

        return null;
        /*return (
            <div className="row justify-content-center align-items-center">
                <Card style={{ minWidth: 320, maxWidth: 480 }}>
                    <Card.Body>
                        <div className={"form-logo"}>
                            <Logo className={""} />
                        </div>
                        <Card.Text className={"text-h6"}>Create your account in Partner Area. Please form below.</Card.Text>
                        <FormRegister className={"form-sign-up"} id={"form-sign-up"} submitButtonName={"Create account"} />
                        <hr className="line-divider" />
                        <div className="sign-in-footer">
                            <p className="">
                                <AppLink href={config.client.endpoints.forgotPassword}>Forgot password?</AppLink>
                            </p>
                            <p className="">
                                <AppLink href={config.client.endpoints.login}>Sign in</AppLink>
                            </p>
                        </div>
                    </Card.Body>
                </Card>
            </div>
        );*/
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

export const getServerSideProps: GetServerSideProps = async (/*context*/) => {
    try {

        return { props: {} };
    } catch (error) {
        console.error(error);
        log.error(error);
        return {
            props: {
                error: 500
            }
        };
    }
};

export default PagePaSignUp;
