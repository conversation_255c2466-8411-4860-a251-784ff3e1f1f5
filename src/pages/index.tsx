import React from "react";
import AppLoading from "@components/main/appLoading";
import { EnumProjectCode, EnumProjectType } from "models/enum/system";
import { Project } from "utils/project";
import { GetServerSideProps, NextPage } from "next";
import { IConfigProject } from "iAppConfig";
import { observer } from "mobx-react-lite";
import appServerConfig, { IAppServerConfig } from "appServerConfig";
import { EnumLocale } from "locales/locales";
import { EnumPage } from "models/enum/page";

export interface IPageProps {
    hostWithPort: string;
    project: IConfigProject | null;
    error?: number;
    appServerConfig: IAppServerConfig;
}

const PageIndex: NextPage<IPageProps> = observer(() => {
    return <AppLoading />;
});

// noinspection JSUnusedGlobalSymbols
export const getServerSideProps: GetServerSideProps = async (context) => {
    const project = Project.getProjectByHost(String(context.req.headers.host));
    const locale = context.locale as EnumLocale;

    if (project?.type === EnumProjectType.site) {
        const sitePages = Project.getSitePages(project?.code as EnumProjectCode);
        return {
            props: { project, appServerConfig },
            redirect: {
                destination: Project.getPageLocaleUrl(locale, sitePages[EnumPage.home].path),
                permanent: false
            }
        };
    } else if (project?.type === EnumProjectType.pa) {
        const paPages = Project.getPaPages();
        return {
            props: { project, appServerConfig },
            redirect: {
                destination: Project.getPageLocaleUrl(locale, paPages[EnumPage.login].path),
                permanent: false
            }
        };
    }
    return {
        props: { project, appServerConfig }
    };
};

export default PageIndex;
