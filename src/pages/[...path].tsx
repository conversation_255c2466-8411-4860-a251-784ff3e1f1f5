import { observer } from "mobx-react-lite";
import AppLoading from "@components/main/appLoading";
import { useRouter } from "next/router";
import { GetServerSideProps, NextPage } from "next";
import { Project } from "utils/project";
import CustomError from "@pages/custom-error";
import React, { useContext, useEffect } from "react";
import appConfig from "appConfig";
import { EnumProjectType, EnumServerEnv } from "models/enum/system";
import Custom404 from "@pages/404";
import PagePaLogin, { getServerSideProps as ssPaLoginProps, IPagePaLoginProps } from "@pages/pa/login";
import PagePaNews, { getServerSideProps as ssPaNewsProps, IPagePaNewsProps } from "@pages/pa/news";
import PagePaRoadmap, { getServerSideProps as ssPaRoadmapProps, IPagePaRoadmapProps } from "@pages/pa/roadmap";
import PagePaRoadmap<PERSON><PERSON><PERSON>, { getServerSideProps as ssPaRoadmapSkywindProps, IPagePaRoadmapSkywindProps } from "@pages/pa/roadmap/skywind";
import PagePaRoadmapSlotFactory, { getServerSideProps as ssPaRoadmapSlotFactoryProps, IPagePaRoadmapSlotFactoryProps } from "@pages/pa/roadmap/slot-factory";
import PagePaLiveGames, { getServerSideProps as ssPaLiveGamesProps, IPagePaLiveGamesProps } from "@pages/pa/live-games";
import PagePaSearch, { getServerSideProps as ssPaSearchProps, IPagePaSearchProps } from "@pages/pa/search";
import PagePaSignUp, { getServerSideProps as ssPaSignUpProps, IPagePaSignUpProps } from "@pages/pa/sign-up";
import PagePaTournament, {
    getServerSideProps as ssPaTournamentProps,
    IPagePaTournamentProps
} from "@pages/pa/tournament";
import PagePaFreeBets, {
    getServerSideProps as ssPaFreeBetsProps,
    IPagePaFreeBetsProps
} from "@pages/pa/tools/free-bets";
import PagePaLuckyEnvelopes, {
    getServerSideProps as ssPaLuckyEnvelopesProps,
    IPagePaLuckyEnvelopesProps
} from "@pages/pa/tools/lucky-envelopes";
import PagePaMustWinJackpot, {
    getServerSideProps as ssPaMustWinJackpotProps,
    IPagePaMustWinJackpotProps
} from "@pages/pa/tools/must-win-jackpot";
import PagePaTournaments, {
    getServerSideProps as ssPaTournamentsProps,
    IPagePaTournamentsProps
} from "@pages/pa/tools/tournaments";
import PagePaDocs, { getServerSideProps as ssPaDocsProps, IPagePaDocsProps } from "@pages/pa/support/docs";
import PagePaPlayRealGame, {
    getServerSideProps as ssPaPlayRealGameProps,
    IPagePaPlayRealGameProps
} from "@pages/pa/play/player/[gameId]";
import PagePaNewsArticle, {
    getServerSideProps as ssPaNewsArticleProps,
    IPagePaNewsArticleProps
} from "@pages/pa/news/[newsId]";
import PagePaGameProfile, {
    getServerSideProps as ssPaGameProfileProps,
    IPagePaGameProfileProps
} from "@pages/pa/game/[gameId]";
import PagePaUserProfile, {
    getServerSideProps as ssPaUserProfileProps,
    IPagePaUserProfileProps
} from "@pages/pa/account/user-profile";

import PageSiteHome, { getServerSideProps as ssSiteHomeProps, IPageSiteHomeProps } from "@pages/site/home";
import PageSiteAboutUs, { getServerSideProps as ssSiteAboutProps, IPageSiteAboutUsProps } from "@pages/site/about-us";
import PageSiteContactUs, {
    getServerSideProps as ssSiteContactProps,
    IPageSiteContactUsProps
} from "@pages/site/contact-us";
import PageSiteCookies, { getServerSideProps as ssSiteCookiesProps, IPageSiteCookiesProps } from "@pages/site/cookies";
import PageSiteGames, { getServerSideProps as ssSiteGamesProps, IPageSiteGamesProps } from "@pages/site/games";
import PageSiteLiveStudio, {
    getServerSideProps as ssSiteLiveStudioProps,
    IPageSiteLiveStudioProps
} from "@pages/site/live-studio";
import PageSiteNews, { getServerSideProps as ssSiteNewsProps, IPageSiteNewsProps } from "@pages/site/news";
import PageSitePrivacyPolicy, {
    getServerSideProps as ssSitePrivacyPolicyProps,
    IPageSitePrivacyPolicyProps
} from "@pages/site/policy";
import PageSiteSports, { getServerSideProps as ssSiteSportsProps, IPageSiteSportsProps } from "@pages/site/sports";
import PageSiteSw360, { getServerSideProps as ssSiteSw360Props, IPageSiteSw360Props } from "@pages/site/sw-360";
import PageSiteFreeBets, {
    getServerSideProps as ssSiteFreeBetsProps,
    IPageSiteFreeBetsProps
} from "@pages/site/tools/free-bets";
import PageSiteLuckyEnvelopes, {
    getServerSideProps as ssLuckyEnvelopesProps,
    IPageSiteLuckyEnvelopesProps
} from "@pages/site/tools/lucky-envelopes";
import PageSiteMustWinJackpots, {
    getServerSideProps as ssSiteMustWinJackpotsProps,
    IPageSiteMustWinJackpotsProps
} from "@pages/site/tools/must-win-jackpots";
import PageSiteTournaments, {
    getServerSideProps as ssSiteTournamentsProps,
    IPageSiteTournamentsProps
} from "@pages/site/tools/tournaments";
import PageSiteWuShiJackpots, {
    getServerSideProps as ssSiteWuShiJackpotsProps,
    IPageSiteWuShiJackpotsProps
} from "@pages/site/tools/wu-shi-jackpot";
import PageSiteGameProfile, {
    getServerSideProps as ssSiteGameProfileProp,
    IPageSiteGameProfileProps
} from "@pages/site/game/[gameId]";
import PageSiteNewsArticle, {
    getServerSideProps as ssSiteNewsArticleProps,
    IPageSiteNewsArticleProps
} from "@pages/site/news/[newsId]";
import { IPageProps } from "@pages/index";
import { GetServerSidePropsContext, GetServerSidePropsResult } from "next/types";
import appServerConfig from "appServerConfig";
import PageSitemapXml, {
    getServerSideProps as ssSitePageSitemapXml,
    IPageSitemapXmlProps
} from "@pages/site/sitemap.xml";
import PageWelcomeDealers, {
    getServerSideProps as ssSitePageWelcomeDealers,
    IPageWelcomeDealersProps
} from "@pages/site/welcome-dealers";
import { MainAppContext } from "@pages/_app";
import { JsUtils } from "utils/js-utils";
import { EnumLocale, getLocaleMessages } from "locales/locales";
import { IConfigProject } from "iAppConfig";
import { EnumPage } from "models/enum/page";
import Head from "next/head";
import { DefaultSeo, OrganizationJsonLd } from "next-seo";
import { GoogleAnalytics } from "nextjs-google-analytics";
import ScrollToTop from "@components/scroll-to-top/scrollToTop";
import { ToastContainer } from "react-toastify";
import CookieDisclaimer from "@components/tools/cookie-disclaimer";
import AgeVerification from "@components/tools/age-verification";
import GamesSearchFullScreen from "@components/games-search/games-search-full-screen";
import PageSiteSlotFactoryGames, { getServerSideProps as ssSiteSlotFactoryGamesProps, IPageSiteSlotFactoryGamesProps }  from "@pages/site/slot-factory-games";
import PagePaLowRtpGames, { getServerSideProps as ssPaLowRtpGamesProps, IPagePaLowRtpGamesProps } from "@pages/pa/low-rtp-games";
import PageSiteSocialGames, { getServerSideProps as ssSiteSocialGamesProps, IPageSiteSocialGamesProps } from "@pages/site/social-games";

export interface IDynamicPageProps extends IPageProps {
    health?: object;
}

const DynamicPage: NextPage<IDynamicPageProps> = observer(({ ...props }) => {
    try {
        const { hostWithPort, project, appServerConfig } = props;
        const { appStore } = useContext(MainAppContext);

        const router = useRouter();

        const locale = router.locale as EnumLocale;
        const msg = getLocaleMessages(locale as EnumLocale).messages;

        if (!project || !hostWithPort || !locale || router.isFallback) {
            return <AppLoading />;
        }

        useEffect(() => {
            appStore.setAppLocale(locale);
            appStore.setAppProject(project as IConfigProject);
            appStore.setHostWithPort(hostWithPort);
            appStore.setAppServerConfig(appServerConfig);
            appStore.setAppProjectPages(
                project.type === EnumProjectType.pa
                ? Project.getPaPages()
                : Project.getSitePages(project.code));
        }, [appServerConfig, appStore, hostWithPort, locale, project]);

        if (!hostWithPort || hostWithPort.length === 0) {
            return null; // <CustomError errorMessage={"Unable detect host on server side"} />;
        }

        if (!project || !JsUtils.isObjectAndNotEmpty(project)) {
            return null; // <CustomError errorMessage={"Unable find valid project for host " + hostWithPort} />;
        }

        if (props.error) {
            return <CustomError errorMessage={String(props.error)} />;
        }

        if (props.health) {
            return <pre>{JSON.stringify(props.health, null, 4)}</pre>;
        }

        const currentPath = router.asPath.split("?")[0].split("#")[0];

        const gameId = Project.getGameIdFromPath(currentPath, router.locale);
        const newsId = Project.getNewsIdFromPath(currentPath, router.locale);
        const playerGameId = Project.getPlayerGameIdFromPath(currentPath, router.locale);

        const projectDomain = Project.getProjectDomainByHost(appStore.getHostName());

        let page = null;

        if (props.project?.type === EnumProjectType.pa) {
            const p = Project.getPaPages();

            // if (!appStore.getUserFirstEntranceAgreement() || appStore.getUserPasswordChangeStatus()) {
            //     return null;
            // }
            //
            if (Project.comparePath(locale, currentPath, p[EnumPage.login].path)) {
                page = <PagePaLogin {...props as IPagePaLoginProps} />;
            } else if (Project.comparePath(locale, currentPath, p[EnumPage.newsList].path)) {
                page = <PagePaNews {...props as IPagePaNewsProps} />;
            } else if (Project.comparePath(locale, currentPath, p[EnumPage.roadmapGames].path)) {
                page = <PagePaRoadmap {...props as IPagePaRoadmapProps} />;
            } else if (Project.comparePath(locale, currentPath, p[EnumPage.roadmapGamesSkywind].path)) {
                page = <PagePaRoadmapSkywind {...props as IPagePaRoadmapSkywindProps} />;
            } else if (Project.comparePath(locale, currentPath, p[EnumPage.roadmapGamesSlotFactory].path)) {
                page = <PagePaRoadmapSlotFactory {...props as IPagePaRoadmapSlotFactoryProps} />;
            } else if (Project.comparePath(locale, currentPath, p[EnumPage.liveStudio].path)) {
                page = <PagePaLiveGames {...props as IPagePaLiveGamesProps} />;
            } else if (Project.comparePath(locale, currentPath, p[EnumPage.lowRtpGames].path)) {
                page = <PagePaLowRtpGames {...props as IPagePaLowRtpGamesProps} />;
            } else if (Project.comparePath(locale, currentPath, p[EnumPage.search].path)) {
                page = <PagePaSearch {...props as IPagePaSearchProps} />;
            } else if (Project.comparePath(locale, currentPath, p[EnumPage.register].path)) {
                page = <PagePaSignUp {...props as IPagePaSignUpProps} />;
            } else if (Project.comparePath(locale, currentPath, p[EnumPage.tournamentLanding].path)) {
                page = <PagePaTournament {...props as IPagePaTournamentProps} />;
            } else if (Project.comparePath(locale, currentPath, p[EnumPage.toolsFreeBets].path)) {
                page = <PagePaFreeBets {...props as IPagePaFreeBetsProps} />;
            } else if (Project.comparePath(locale, currentPath, p[EnumPage.toolsLuckyEnvelopes].path)) {
                page = <PagePaLuckyEnvelopes {...props as IPagePaLuckyEnvelopesProps} />;
            } else if (Project.comparePath(locale, currentPath, p[EnumPage.toolsMustWinJackpots].path)) {
                page = <PagePaMustWinJackpot {...props as IPagePaMustWinJackpotProps} />;
            } else if (Project.comparePath(locale, currentPath, p[EnumPage.toolsTournaments].path)) {
                page = <PagePaTournaments {...props as IPagePaTournamentsProps} />;
            } else if (Project.comparePath(locale, currentPath, p[EnumPage.supportDocs].path)) {
                page = <PagePaDocs {...props as IPagePaDocsProps} />;
            } else if (Project.comparePath(locale, currentPath, p[EnumPage.accountUserProfile].path)) {
                page = <PagePaUserProfile {...props as IPagePaUserProfileProps} />;
            } else if (gameId) {
                page = <PagePaGameProfile {...props as IPagePaGameProfileProps} />;
            } else if (newsId) {
                page = <PagePaNewsArticle {...props as IPagePaNewsArticleProps} />;
            } else if (playerGameId) {
                page = <PagePaPlayRealGame {...props as IPagePaPlayRealGameProps} />;
            }

            return page
                   ? <>
                       {page}
                       {projectDomain?.environment === EnumServerEnv.production &&
                           <GoogleAnalytics gaMeasurementId={project.googleAnalyticsTracingCode}
                                            strategy={"afterInteractive"} />}
                       <ScrollToTop />
                       <ToastContainer />
                   </>
                   : <Custom404 />;
        } else if (props.project?.type === EnumProjectType.site) {
            const s = Project.getSitePages(props.project.code);

            const isWelcomeDealers = Project.comparePath(locale, currentPath, s[EnumPage.welcomeDealers].path);

            if (currentPath === "" || currentPath === "/" || Project.comparePath(locale,
                currentPath,
                s[EnumPage.home].path)) {
                page = <PageSiteHome {...props as IPageSiteHomeProps} />;
            } else if (Project.comparePath(locale, currentPath, s[EnumPage.aboutUs].path)) {
                page = <PageSiteAboutUs {...props as IPageSiteAboutUsProps} />;
            } else if (Project.comparePath(locale, currentPath, s[EnumPage.contact].path)) {
                page = <PageSiteContactUs {...props as IPageSiteContactUsProps} />;
            } else if (Project.comparePath(locale, currentPath, s[EnumPage.cookies].path)) {
                page = <PageSiteCookies {...props as IPageSiteCookiesProps} />;
            } else if (Project.comparePath(locale, currentPath, s[EnumPage.gamesList].path)) {
                page = <PageSiteGames {...props as IPageSiteGamesProps} />;
            } else if (Project.comparePath(locale, currentPath, s[EnumPage.slotFactoryGamesList].path)) {
                page = <PageSiteSlotFactoryGames {...props as IPageSiteSlotFactoryGamesProps} />;
            } else if (Project.comparePath(locale, currentPath, s[EnumPage.socialGamesList].path)) {
                page = <PageSiteSocialGames {...props as IPageSiteSocialGamesProps} />;
            } else if (Project.comparePath(locale, currentPath, s[EnumPage.liveStudio].path)) {
                page = <PageSiteLiveStudio {...props as IPageSiteLiveStudioProps} />;
            } else if (Project.comparePath(locale, currentPath, s[EnumPage.newsList].path)) {
                page = <PageSiteNews {...props as IPageSiteNewsProps} />;
            } else if (Project.comparePath(locale, currentPath, s[EnumPage.privacy].path)) {
                page = <PageSitePrivacyPolicy {...props as IPageSitePrivacyPolicyProps} />;
            } else if (Project.comparePath(locale, currentPath, s[EnumPage.sports].path)) {
                page = <PageSiteSports {...props as IPageSiteSportsProps} />;
            } else if (Project.comparePath(locale, currentPath, s[EnumPage.sw360].path)) {
                page = <PageSiteSw360 {...props as IPageSiteSw360Props} />;
            } else if (Project.comparePath(locale, currentPath, s[EnumPage.toolsFreeBets].path)) {
                page = <PageSiteFreeBets {...props as IPageSiteFreeBetsProps} />;
            } else if (Project.comparePath(locale, currentPath, s[EnumPage.toolsLuckyEnvelopes].path)) {
                page = <PageSiteLuckyEnvelopes {...props as IPageSiteLuckyEnvelopesProps} />;
            } else if (Project.comparePath(locale, currentPath, s[EnumPage.toolsMustWinJackpots].path)) {
                page = <PageSiteMustWinJackpots {...props as IPageSiteMustWinJackpotsProps} />;
            } else if (Project.comparePath(locale, currentPath, s[EnumPage.toolsTournaments].path)) {
                page = <PageSiteTournaments {...props as IPageSiteTournamentsProps} />;
            } else if (Project.comparePath(locale, currentPath, s[EnumPage.toolsWuShiJackpot].path)) {
                page = <PageSiteWuShiJackpots {...props as IPageSiteWuShiJackpotsProps} />;
            } else if (Project.comparePath(locale, currentPath, s[EnumPage.sitemap].path)) {
                page = <PageSitemapXml {...props as IPageSitemapXmlProps} />;
            } else if (isWelcomeDealers) {
                page = <PageWelcomeDealers {...props as IPageWelcomeDealersProps} />;
            } else if (gameId) {
                page = <PageSiteGameProfile {...props as IPageSiteGameProfileProps} />;
            } else if (newsId) {
                page = <PageSiteNewsArticle {...props as IPageSiteNewsArticleProps} />;
            }

            return page
                   ? <>
                       <Head>
                           <title></title>
                           <DefaultSeo
                               titleTemplate={`${msg.skywindCompanyName} | %s`}
                               title={msg.skywindCompanyName}
                               defaultTitle={msg.skywindCompanyName}
                               // additionalMetaTags={["skywind","skywind
                               // group","gaming","igaming","company","slots","table","casino","jackpot","tournament"]}
                               openGraph={{
                                   type: "website",
                                   locale: locale,
                                   url: `https://${project.type === EnumProjectType.site ?
                                                   "" :
                                                   "partnerarea."}skywindgroup.com/`,
                                   site_name: msg.skywindCompanyName
                               }}
                           />
                           <OrganizationJsonLd
                               logo={"https://skywindgroup.com/assets/site/images/sw_logo.png"}
                               url={"https://skywindgroup.com/"}
                               name={String(msg.skywindCompanyName)} />
                       </Head>
                       {page}
                       <ScrollToTop />
                       <ToastContainer />
                       <CookieDisclaimer />
                       {!isWelcomeDealers && <AgeVerification />}
                       <GamesSearchFullScreen id={"games-search-site"} />
                       <div dangerouslySetInnerHTML={{ __html: `<!-- ${JSON.stringify(projectDomain)} -->` }} />

                       {projectDomain?.environment === EnumServerEnv.production &&
                           <GoogleAnalytics gaMeasurementId={project.googleAnalyticsTracingCode}
                                            strategy={"afterInteractive"} />}
                   </>
                   : <Custom404 />;
        }

        return <AppLoading />;
    } catch (clientError) {
        console.error(clientError);
        return <CustomError errorMessage={"Client Error"} />;
    }
});

function setResponse(context: GetServerSidePropsContext,
                     resp: any): Promise<GetServerSidePropsResult<object>> {
    const hostWithPort = context.req.headers.host;
    const project = Project.getProjectByHost(String(hostWithPort));

    const p = resp.props || {};

    return new Promise((resolve) => resolve({
        ...resp,
        props: { ...p, project, hostWithPort, appServerConfig }
    }));
}

export const getServerSideProps: GetServerSideProps = async (context) => {
    const hostWithPort = context.req.headers.host;
    const project = Project.getProjectByHost(String(hostWithPort));

    const projectConfig = project?.type === EnumProjectType.pa
                          ? appConfig.client.revalidation.projects.pa
                          : appConfig.client.revalidation.projects.site;
    // This value is considered fresh for ten seconds (s-maxage=10).
    // If a request is repeated within the next 10 seconds, the previously
    // cached value will still be fresh. If the request is repeated before 59 seconds,
    // the cached value will be stale but still render (stale-while-revalidate=59).
    //
    // In the background, a revalidation request will be made to populate the cache
    // with a fresh value. If you refresh the page, you will see the new value.
    context.res.setHeader(
        "Cache-Control",
        `public, s-maxage=${projectConfig.default.sMaxAgeSeconds}, ` +
        `stale-while-revalidate=${projectConfig.default.staleWhileRevalidate}`
    );

    const props: IPageProps = {
        appServerConfig,
        hostWithPort: String(hostWithPort),
        project
    };

    const pageUrl = context.resolvedUrl.split("?")[0];
    const locale = context.locale;

    const gameId = Project.getGameIdFromPath(pageUrl, locale);
    const newsId = Project.getNewsIdFromPath(pageUrl, locale);

    if (project?.type === EnumProjectType.pa) {
        const p = Project.getPaPages();

        const playerGameId = Project.getPlayerGameIdFromPath(pageUrl, locale);

        if (Project.comparePath(locale, pageUrl, p[EnumPage.login].path)) {
            return setResponse(context, await ssPaLoginProps(context));
        } else if (Project.comparePath(locale, pageUrl, p[EnumPage.newsList].path)) {
            return setResponse(context, await ssPaNewsProps(context));
        } else if (Project.comparePath(locale, pageUrl, p[EnumPage.roadmapGames].path)) {
            return setResponse(context, await ssPaRoadmapProps(context));
        } else if (Project.comparePath(locale, pageUrl, p[EnumPage.roadmapGamesSkywind].path)) {
            return setResponse(context, await ssPaRoadmapSkywindProps(context));
        } else if (Project.comparePath(locale, pageUrl, p[EnumPage.roadmapGamesSlotFactory].path)) {
            return setResponse(context, await ssPaRoadmapSlotFactoryProps(context));
        } else if (Project.comparePath(locale, pageUrl, p[EnumPage.liveStudio].path)) {
            return setResponse(context, await ssPaLiveGamesProps(context));
        } else if (Project.comparePath(locale, pageUrl, p[EnumPage.lowRtpGames].path)) {
            return setResponse(context, await ssPaLowRtpGamesProps(context));
        } else if (Project.comparePath(locale, pageUrl, p[EnumPage.search].path)) {
            return setResponse(context, await ssPaSearchProps(context));
        } else if (Project.comparePath(locale, pageUrl, p[EnumPage.register].path)) {
            return setResponse(context, await ssPaSignUpProps(context));
        } else if (Project.comparePath(locale, pageUrl, p[EnumPage.tournamentLanding].path)) {
            return setResponse(context, await ssPaTournamentProps(context));
        } else if (Project.comparePath(locale, pageUrl, p[EnumPage.toolsFreeBets].path)) {
            return setResponse(context, await ssPaFreeBetsProps(context));
        } else if (Project.comparePath(locale, pageUrl, p[EnumPage.toolsLuckyEnvelopes].path)) {
            return setResponse(context, await ssPaLuckyEnvelopesProps(context));
        } else if (Project.comparePath(locale, pageUrl, p[EnumPage.toolsMustWinJackpots].path)) {
            return setResponse(context, await ssPaMustWinJackpotProps(context));
        } else if (Project.comparePath(locale, pageUrl, p[EnumPage.toolsTournaments].path)) {
            return setResponse(context, await ssPaTournamentsProps(context));
        } else if (Project.comparePath(locale, pageUrl, p[EnumPage.supportDocs].path)) {
            return setResponse(context, await ssPaDocsProps(context));
        } else if (Project.comparePath(locale, pageUrl, p[EnumPage.accountUserProfile].path)) {
            return setResponse(context, await ssPaUserProfileProps(context));
        } else if (gameId) {
            context.params = context?.params || {};
            context.params["gameId"] = gameId;
            return setResponse(context, await ssPaGameProfileProps(context));
        } else if (newsId) {
            context.params = context?.params || {};
            context.params["newsId"] = newsId;
            return setResponse(context, await ssPaNewsArticleProps(context));
        } else if (playerGameId) {
            context.params = context?.params || {};
            context.params["gameId"] = playerGameId;
            return setResponse(context, await ssPaPlayRealGameProps(context));
        }

    } else if (project?.type === EnumProjectType.site) {
        const sitePages = Project.getSitePages(project.code);

        if (Project.comparePath(locale, pageUrl, sitePages[EnumPage.home].path)) {
            return setResponse(context, await ssSiteHomeProps(context));
        } else if (Project.comparePath(locale, pageUrl, sitePages[EnumPage.aboutUs].path)) {
            return setResponse(context, await ssSiteAboutProps(context));
        } else if (Project.comparePath(locale, pageUrl, sitePages[EnumPage.contact].path)) {
            return setResponse(context, await ssSiteContactProps(context));
        } else if (Project.comparePath(locale, pageUrl, sitePages[EnumPage.cookies].path)) {
            return setResponse(context, await ssSiteCookiesProps(context));
        } else if (Project.comparePath(locale, pageUrl, sitePages[EnumPage.gamesList].path)) {
            return setResponse(context, await ssSiteGamesProps(context));
        } else if (Project.comparePath(locale, pageUrl, sitePages[EnumPage.slotFactoryGamesList].path)) {
            return setResponse(context, await ssSiteSlotFactoryGamesProps(context));
        } else if (Project.comparePath(locale, pageUrl, sitePages[EnumPage.socialGamesList].path)) {
            return setResponse(context, await ssSiteSocialGamesProps(context));
        } else if (Project.comparePath(locale, pageUrl, sitePages[EnumPage.liveStudio].path)) {
            return setResponse(context, await ssSiteLiveStudioProps(context));
        } else if (Project.comparePath(locale, pageUrl, sitePages[EnumPage.newsList].path)) {
            return setResponse(context, await ssSiteNewsProps(context));
        } else if (Project.comparePath(locale, pageUrl, sitePages[EnumPage.privacy].path)) {
            return setResponse(context, await ssSitePrivacyPolicyProps(context));
        } else if (Project.comparePath(locale, pageUrl, sitePages[EnumPage.sports].path)) {
            return setResponse(context, await ssSiteSportsProps(context));
        } else if (Project.comparePath(locale, pageUrl, sitePages[EnumPage.sw360].path)) {
            return setResponse(context, await ssSiteSw360Props(context));
        } else if (Project.comparePath(locale, pageUrl, sitePages[EnumPage.toolsFreeBets].path)) {
            return setResponse(context, await ssSiteFreeBetsProps(context));
        } else if (Project.comparePath(locale, pageUrl, sitePages[EnumPage.toolsLuckyEnvelopes].path)) {
            return setResponse(context, await ssLuckyEnvelopesProps(context));
        } else if (Project.comparePath(locale, pageUrl, sitePages[EnumPage.toolsMustWinJackpots].path)) {
            return setResponse(context, await ssSiteMustWinJackpotsProps(context));
        } else if (Project.comparePath(locale, pageUrl, sitePages[EnumPage.toolsTournaments].path)) {
            return setResponse(context, await ssSiteTournamentsProps(context));
        } else if (Project.comparePath(locale, pageUrl, sitePages[EnumPage.toolsWuShiJackpot].path)) {
            return setResponse(context, await ssSiteWuShiJackpotsProps(context));
        } else if (Project.comparePath(locale, pageUrl, sitePages[EnumPage.sitemap].path)) {
            return setResponse(context, await ssSitePageSitemapXml(context));
        } else if (Project.comparePath(locale, pageUrl, sitePages[EnumPage.welcomeDealers].path)) {
            return setResponse(context, await ssSitePageWelcomeDealers(context));
        } else if (gameId) {
            context.params = context?.params || {};
            context.params["gameId"] = gameId;
            return setResponse(context, await ssSiteGameProfileProp(context));
        } else if (newsId) {
            context.params = context?.params || {};
            context.params["newsId"] = newsId;
            return setResponse(context, await ssSiteNewsArticleProps(context));
        }
    }

    return {
        // will be passed to the page component as props
        props,
        notFound: true
    };
};

export default DynamicPage;
