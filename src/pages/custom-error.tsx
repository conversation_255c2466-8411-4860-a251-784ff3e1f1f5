import React from "react";
import AppLink from "@components/main/appLink";

export default function CustomError({ errorMessage }: { errorMessage: string }) {

    return (
        <section id="pageMaintenance" className="pageMaintenance">
            <div className="container-fluid">
                <div className="row">
                    <div className="col">
                        <div className="blockcenter">
                            <h2 className="title blockcenter__title" data-text="error">
                                An error <span className="accent">occurred</span>
                            </h2>
                            <p className="blockcenter__offer blockcenter__offer--maintenance">
                                {errorMessage ?
                                 `An error ${errorMessage} occurred on server` :
                                 "An error occurred on client"}
                            </p>
                            <AppLink href="/"  className="button button--primary blockcenter__btn">go home</AppLink>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}
