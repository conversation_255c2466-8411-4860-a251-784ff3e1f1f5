import { EnumError, EnumMessage, IEnumLocale, ILocaleObject } from "./enumLocale";

export const enUSMessages = {
    errors: {
        [EnumError.userPermissionDenied]: "User permission denied",
        [EnumError.anAccountWithThisEmailAlreadyExists]: "An account with this email already exists",
        [EnumError.userIsNotFound]: "User is not found",
        [EnumError.userInAccessToken]: "Unable to decode access token",
        [EnumError.userNotFoundOrDisabled]: "User is not found or user account is not activated",
        [EnumError.playerCodeAlreadySet]: "Player code already set for current user",
        [EnumError.cantConnectToGameServer]:
            "Now it is impossible to connect to the game server. Please try again later",
        [EnumError.invalidPlayerCode]: "Invalid player code format - try changing the value",
        [EnumError.playerCodeExists]:
            "Player code already exists on the system and cannot be used. Set different value for player code.",
        [EnumError.impossibleSetPlayerBalance]:
            "For technical reasons, we are unable to set a balance for this player. Contact Support",
        [EnumError.tournamentDisabledOrEndedOrNotStarted]: "Tournament disabled, not started, or ended",
        [EnumError.cantGenerateGameUrl]:
            "For technical reasons, it is currently not possible to get a link to the game. Try again later or contact support",
        [EnumError.userNotActivated]: "User is not activated",
        [EnumError.userNotFoundOrDisabledOrNoPlayerCode]: "The user must be active, logged in and have a player code.",
        [EnumError.internalServerError]:
            "Internal server error. We apologize for the inconvenience, but we are unable to process your request at this time. Our engineers have been notified of this problem and will work to resolve it.",
        [EnumError.wrongPassword]: "Wrong password",
        [EnumError.userActivationNotFound]: "Your session has timed out. Please login again.",
        [EnumError.userRefreshTokenNotFound]: "Sorry, your session was expired. Please login again.",
        [EnumError.newPasswordDoesntMatchCurrent]: "The provided current password doesn't match our records",
        [EnumError.gameNotFound]: "The game was not found",
        [EnumError.contentNotFound]: "Requested content not found",
        [EnumError.marketingMaterialsNotFound]: "No marketing materials were found for the specified game",
        [EnumError.gitbookSpaceNotFound]: "Gitbook space not found",
    } as IEnumLocale,
    messages: {
        [EnumMessage.skywindCompanyName]: "SkywindGroup Holdings LTD",
        [EnumMessage.headerMenuGames]: "Games",
        [EnumMessage.headerMenuRoadMap]: "Roadmap",
        [EnumMessage.headerMenuAllGames]: "All Games",
        [EnumMessage.headerMenuRoadMapSkywind]: "Skywind Games",
        [EnumMessage.headerMenuRoadMapSlotFactory]: "Slot Factory Games",
        [EnumMessage.headerMenuLiveGames]: "Live Casino",
        [EnumMessage.headerMenuProfile]: "My account",
        [EnumMessage.headerMenuNews]: "News",
        [EnumMessage.headerMenuSearch]: "Search",
        [EnumMessage.headerMenuLogout]: "Logout",
        [EnumMessage.notApplicable]: "N/A",

        [EnumMessage.gameRTP]: "RTP",
        [EnumMessage.gameVolatility]: "Volatility",

        [EnumMessage.buttonAllGames]: "All Games",
        [EnumMessage.buttonLearnMore]: "learn More",
        [EnumMessage.buttonComingSoon]: "Coming Soon",

        [EnumMessage.titlePlayGame]: "Play Game: ",
    } as IEnumLocale,
    components: {
        search: {
            inputPlaceHolder: "Search Games...",
            filterMarketTitle: "Countries",
            filterTagsTitle: "Tags",
            filterRTPTitle: "RTP",
            filterCertificationsTitle: "Certifications",
            filterClearTitle: "Clear",
            filterJackpotsTitle: "Jackpots",
            filterRankTitle: "Rank",
            filterReleaseYearTitle: "Release Year",
            filterTypeTitle: "Type",
            filterVolatilityTitle: "Volatility",
        },
    },
} as ILocaleObject;
