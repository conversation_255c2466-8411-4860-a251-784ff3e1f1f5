import { enUSMessages } from "./en-US";
import { ILocaleObject } from "./enumLocale";
import { EnumLanguageCode } from "models/enum/system";

// https://www.unicode.org/reports/tr35/tr35-59/tr35.html#Identifiers
export enum EnumLocale {
    enUS = "en-US",
    zhCN = "zh-CN",
}

export interface IAppLocale {
    key: string;
    code: string;
    title: string;
    languageCode: EnumLanguageCode;
    isActive: boolean;
}

export interface IAppLocales {
    [localeKey: string]: IAppLocale;
}

export const AppLocales: IAppLocales = {
    zhCn: { key: "zhCn", code: EnumLocale.zhCN, title: "Chinese", languageCode: EnumLanguageCode.zh, isActive: false },
    enUs: { key: "enUs", code: EnumLocale.enUS, title: "English", languageCode: EnumLanguageCode.en, isActive: true }
};

export function getLocaleMessages(locale: EnumLocale): ILocaleObject {
    if (locale === EnumLocale.enUS) {
        return enUSMessages;
    }
    return enUSMessages;
}
