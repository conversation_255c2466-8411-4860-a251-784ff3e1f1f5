import { AxiosResponse } from "axios";

import appConfig from "appConfig";
import appStore from "@stores/app-store";
import { INewsExtended, IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { IResponseSuccess } from "api/interfaces/response";
import clientApi from "client/services/http";
import { ClientApiResponse } from "utils/serverResponse";

export default class ClientProjectService {
    // /**
    //  * @see ProjectController.getGitbookSpaceUrl
    //  * @see ProjectService.getGitbookSpaceUrl
    //  * */
    // static async getGitbookApiRequest(
    //     space: string
    // ): Promise<AxiosResponse<IResponseSuccess<IGitBookAuthData | undefined>>> {
    //     return await clientApi.get<IResponseSuccess<IGitBookAuthData>>(
    //         appConfig.client.endpoints.api.project.gitbookSpaceUrl.replace(":space", space),
    //         {
    //             withCredentials: true,
    //         }
    //     );
    // }
    //
    // static async getGitbookApi(space: string): Promise<IGitBookAuthData | unknown> {
    //     let resp: IGitBookAuthData = {} as IGitBookAuthData;
    //     try {
    //         const response = new ClientApiResponse(await ClientProjectService.getGitbookApiRequest(space)).response;
    //
    //         response
    //             .onError((err, skipExecuteParent = false) => {
    //                 appStore.onClientResponseError(err, false);
    //             })
    //             .onSuccess((success, skipExecuteParent = false) => {
    //                 resp = success.data as IGitBookAuthData;
    //             });
    //     } catch (e) {
    //         appStore.catchClientError(e as Error);
    //     }
    //
    //     return resp;
    // }

    /**
     * @see ProjectController.getNewsList
     * @see ProjectService.getNewsList
     * */
    static async getNewsListRequest(): Promise<AxiosResponse<IResponseSuccess<INewsExtended[] | undefined>>> {
        return await clientApi.get<IResponseSuccess<INewsExtended[]>>(appConfig.client.endpoints.api.project.newsList, {
            withCredentials: true,
        });
    }

    /**
     * @see ProjectController.getNewsInfo
     * @see ProjectService.getNewsArticle
     * */
    static async getNewsInfoRequest(
        newsId: string
    ): Promise<AxiosResponse<IResponseSuccess<INewsExtended | undefined>>> {
        return await clientApi.get<IResponseSuccess<INewsExtended>>(
            appConfig.client.endpoints.api.project.newsArticle.replace(":newsId", newsId),
            {
                withCredentials: true,
            }
        );
    }

    /**
     * @see ProjectController.getNewsList
     * @see ProjectService.getNewsList
     * */
    static async getNewsList(): Promise<INewsExtended[] | unknown> {
        let newsResponse: INewsExtended[] = [];
        try {
            const response = new ClientApiResponse(await ClientProjectService.getNewsListRequest()).response;

            response
                .onError((err) => {
                    appStore.onClientResponseError(err, false);
                })
                .onSuccess((success) => {
                    newsResponse = success.data as INewsExtended[];
                });
        } catch (e) {
            appStore.catchClientError(e as Error);
        }

        return newsResponse;
    }

    static async getNewsInfo(newsId: string): Promise<INewsExtended | unknown> {
        let newsResponse: INewsExtended = {} as INewsExtended;
        try {
            const response = new ClientApiResponse(await ClientProjectService.getNewsInfoRequest(newsId)).response;

            response
                .onError((err) => {
                    appStore.onClientResponseError(err, false);
                })
                .onSuccess((success) => {
                    newsResponse = success.data as INewsExtended;
                });
        } catch (e) {
            appStore.catchClientError(e as Error);
        }

        return newsResponse;
    }

    /**
     * @see ProjectController.getWebsiteFooter
     * @see ProjectService.getWebsiteFooter
     * */
    static async getWebsiteFooterRequest(): Promise<AxiosResponse<IResponseSuccess<IWebsiteFooter>>> {
        return await clientApi.get<IResponseSuccess<IWebsiteFooter>>(
            appConfig.client.endpoints.api.project.websiteFooter,
            {
                withCredentials: true,
            }
        );
    }

    static async getWebsiteFooter(): Promise<IWebsiteFooter | null> {
        let footer: IWebsiteFooter | null = null;
        try {
            const response = new ClientApiResponse(await ClientProjectService.getWebsiteFooterRequest()).response;

            response
                .onError((err) => {
                    appStore.onClientResponseError(err, false);
                })
                .onSuccess((success) => {
                    footer = success.data as IWebsiteFooter;
                });
        } catch (e) {
            appStore.catchClientError(e as Error);
        }

        return footer;
    }
}
