import { AxiosResponse } from "axios";
import clientApi from "./http";

import appConfig from "appConfig";
import {
    IUserChangePasswordRequest,
    IUserLoginRequest,
    IUserLoginResponse,
    IUserRefreshTokensResponse,
    IUserRegisterResponse,
} from "api/services/interfaces/iUsersService";
import appStore from "@stores/app-store";
import { IUserClientActionParams } from "api/controllers/interfaces/iUsersController";
import { EnumUserClientAction, EnumUserClientArea } from "models/enum/user";
import { IResponseSuccess } from "api/interfaces/response";
import { ClientApiResponse, ServerResponse } from "utils/serverResponse";
import { IMonth } from "@components/roadmap-grid/roadmapInterfaces";
import { EnumProjectType } from "models/enum/system";

export default class ClientUsersService {
    /**
     * @see UsersController.login
     * @see UsersService.login
     * */
    static async loginRequest(params: IUserLoginRequest): Promise<AxiosResponse<IResponseSuccess<IUserLoginResponse>>> {
        return await clientApi.post<IUserLoginRequest, AxiosResponse<IResponseSuccess<IUserLoginResponse>>>(
            appConfig.client.endpoints.api.users.login,
            params
        );
    }

    /**
     * @see UsersController.refresh
     * @see UsersService.refreshUserToken
     * */
    static async refreshRequest(): Promise<AxiosResponse<IResponseSuccess<IUserRefreshTokensResponse>>> {
        return await clientApi.get<IResponseSuccess<IUserRefreshTokensResponse>>(
            appConfig.client.endpoints.api.users.refresh,
            {
                withCredentials: true,
            }
        );
    }

    static async refreshToken(): Promise<ServerResponse | void> {
        try {
            return new ClientApiResponse(await ClientUsersService.refreshRequest()).response;
        } catch (e) {
            appStore.catchClientError(e as Error);
        }
    }

    static async registrationRequest(
        login: string,
        password: string
    ): Promise<AxiosResponse<IResponseSuccess<IUserRegisterResponse>>> {
        return await clientApi.post<unknown, AxiosResponse<IResponseSuccess<IUserRegisterResponse>>>(
            appConfig.client.endpoints.api.users.register,
            { login, password }
        );
    }

    static async logoutRequest(): Promise<void> {
        return await clientApi.delete(appConfig.client.endpoints.api.users.logout, {
            withCredentials: true,
        });
    }

    /**
     * @see UsersController.changePassword
     * @see UsersService.changePassword
     * */
    static async changePasswordRequest(
        params: IUserChangePasswordRequest
    ): Promise<AxiosResponse<IResponseSuccess<IUserLoginResponse>>> {
        return await clientApi.put<IUserChangePasswordRequest, AxiosResponse<IResponseSuccess<IUserLoginResponse>>>(
            appConfig.client.endpoints.api.users.changePassword,
            params,
            {
                withCredentials: true,
            }
        );
    }

    /**
     * @see UsersController.saveUserClientAction
     * @see UsersService.registerUserClientAction
     */
    static async clientActionRequest(
        params: IUserClientActionParams
    ): Promise<AxiosResponse<IResponseSuccess<boolean>>> {
        return await clientApi.post<IUserChangePasswordRequest, AxiosResponse<IResponseSuccess<boolean>>>(
            appConfig.client.endpoints.api.users.clientAction,
            params,
            {
                withCredentials: true,
            }
        );
    }

    static async clientAction(
        action: EnumUserClientAction,
        fromPageUrl: string,
        params: {
            isSite?: boolean;
            gameCode?: string | null | undefined;
            title?: string;
            isActive?: boolean;
            responseStatus?: number | string | undefined;
            playerCode?: string | null | undefined;
            gameUrl?: string;
            isVideo?: boolean;
            isImage?: boolean;
            isDesktop?: boolean;
            isMobile?: boolean;
            url?: string;
            gameId?: string;
            code?: string;
            months?: IMonth[];
            newsId?: string;
            marketCode?: string;
            button?: string;
            link?: string;
            searchString?: string;
            filters?: any;
            results?: any;
            projectType?: EnumProjectType;
        },
        fromArea?: EnumUserClientArea | string
    ): Promise<void> {
        if (typeof params === "object" && params !== null && params.isSite === true) {
            return;
        }
        try {
            const response = new ClientApiResponse(
                await ClientUsersService.clientActionRequest({
                    action,
                    fromPageUrl,
                    params,
                    fromArea,
                })
            ).response;

            response.onError((err) => {
                appStore.onClientResponseError(err, false);
            });
        } catch (e) {
            appStore.catchClientError(e as Error);
        }
    }
}
