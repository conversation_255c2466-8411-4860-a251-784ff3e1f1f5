import { AxiosResponse } from "axios";

import appConfig from "appConfig";
import appStore from "@stores/app-store";
import clientApi from "client/services/http";
import { ClientApiResponse } from "utils/serverResponse";
import { IUserPlayerCodeResponse } from "api/services/interfaces/iUsersService";

export default class ClientPlayersService {
    /**
     * @see UsersController.getPlayerCode
     * @see GamesService.getPlayerCode
     * */
    static async getPlayerCodeRequest(): Promise<AxiosResponse<IUserPlayerCodeResponse | undefined>> {
        return await clientApi.get<IUserPlayerCodeResponse>(appConfig.client.endpoints.api.users.playerCode, {
            withCredentials: true,
        });
    }

    static async getPlayerCode(): Promise<IUserPlayerCodeResponse> {
        let resp = { playerCode: null, isValid: false };
        try {
            const response = new ClientApiResponse(await ClientPlayersService.getPlayerCodeRequest()).response;

            response
                .onError((err) => {
                    appStore.onClientResponseError(err, false);
                })
                .onSuccess((success) => {
                    resp = success.data;
                });
        } catch (e) {
            appStore.catchClientError(e as Error);
        }

        return resp;
    }

    /**
     * @see UsersController.setPlayerCode
     * @see GamesService.setPlayerCode
     * */
    static async setPlayerCodeRequest(params: { playerCode: string }): Promise<AxiosResponse<IUserPlayerCodeResponse>> {
        return await clientApi.post<IUserPlayerCodeResponse>(
            appConfig.client.endpoints.api.users.playerCode,
            { playerCode: params.playerCode },
            {
                withCredentials: true,
            }
        );
    }

    static async setPlayerCode(params: { playerCode: string }): Promise<IUserPlayerCodeResponse> {
        let resp = { playerCode: null, isValid: false };
        try {
            const response = new ClientApiResponse(await ClientPlayersService.setPlayerCodeRequest(params)).response;

            response
                .onError((err) => {
                    appStore.onClientResponseError(err, false);
                })
                .onSuccess((success) => {
                    resp = success.data;
                });
        } catch (e) {
            appStore.catchClientError(e as Error);
        }

        return resp;
    }
}
