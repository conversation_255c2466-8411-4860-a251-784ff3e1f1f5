import axios, { AxiosRequestConfig } from "axios";
import appConfig from "appConfig";
import { IUserRefreshTokensResponse } from "api/services/interfaces/iUsersService";
import appStore from "@stores/app-store";
import { EnumLocalStorage } from "models/enum/local-storage";
import authStore from "@stores/auth-store";
import { IResponseSuccess } from "api/interfaces/response";
import { ClientRequest } from "utils/clientRequest";
import { Project } from "utils/project";
import { EnumPage } from "models/enum/page";

export const API_URL = appConfig.client.endpoints.api.path;

const clientApi = axios.create({
    withCredentials: true,
    baseURL: API_URL,
});

clientApi.interceptors.request.use((cfg) => {
    if (cfg.url !== appConfig.client.endpoints.api.users.clientAction) {
        appStore.setAppLoading(true);
    }
    cfg.headers = cfg.headers || {};
    if (localStorage) {
        cfg.headers.Authorization = `Bearer ${localStorage.getItem(EnumLocalStorage.accessToken)}`;
    }
    cfg.validateStatus = (status) => {
        return status < 500; // Resolve only if the status code is less than 500
    };
    return cfg;
});

const authorise = async (originalRequest: AxiosRequestConfig): Promise<void> => {
    const paLoginUrl = Project.getPageUrl(appStore, EnumPage.login);
    try {
        const accessToken = localStorage.getItem(EnumLocalStorage.accessToken);
        if (accessToken) {
            const response = await axios.get<IResponseSuccess<IUserRefreshTokensResponse>>(
                `${API_URL}${appConfig.client.endpoints.api.users.refresh}`,
                {
                    withCredentials: true,
                    headers: {
                        Authorization: `Bearer ${accessToken}`,
                    },
                }
            );
            if (response?.data?.data?.accessToken) {
                authStore.authorise(response?.data?.data);
                return clientApi.request(originalRequest);
            }
        }
        authStore.reset();
        ClientRequest.redirect(paLoginUrl);
    } catch (e) {
        ClientRequest.redirect(paLoginUrl);
    }
};

clientApi.interceptors.response.use(
    async (response) => {
        appStore.setAppLoading(false);
        const originalRequest = response.config;
        if (response.status === 401 && response?.config?.url !== appConfig.client.endpoints.api.users.refresh) {
            return await authorise(originalRequest);
        }
        return response;
    },
    async (error) => {
        appStore.setAppLoading(false);
        const originalRequest = error.config;
        if (
            error.response.status === 401 &&
            error.response?.config?.url !== appConfig.client.endpoints.api.users.refresh &&
            error.config &&
            !error.config._isRetry
        ) {
            originalRequest._isRetry = true;
            return await authorise(originalRequest);
        }
        throw error;
    }
);

export default clientApi;
