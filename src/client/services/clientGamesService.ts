import { AxiosResponse } from "axios";
import appConfig from "appConfig";
import {
    IAvailableGamesResponse,
    IGamesGridRow,
    IMarketingKitResponse,
    IRoadmapResponse
} from "api/services/interfaces/iGamesService";
import appStore from "@stores/app-store";
import { IResponseSuccess } from "api/interfaces/response";
import clientApi from "client/services/http";
import { ClientApiResponse } from "utils/serverResponse";
import { ISearchData } from "@components/games-search/_interfaces";

export default class ClientGamesService {
    /**
     * @see GamesController.getRoadmap
     * @see GamesService.getRoadmapGames
     * */
    static async getRoadmapRequest(): Promise<AxiosResponse<IResponseSuccess<IRoadmapResponse>>> {
        return await clientApi.get<IResponseSuccess<IRoadmapResponse>>(appConfig.client.endpoints.api.games.roadmap, {
            withCredentials: true,
        });
    }

    static async getRoadmapGames(): Promise<IRoadmapResponse> {
        let roadmapResponse: IRoadmapResponse = {} as IRoadmapResponse;
        try {
            const response = new ClientApiResponse(await ClientGamesService.getRoadmapRequest()).response;

            response
                .onError((err) => {
                    appStore.onClientResponseError(err, false);
                })
                .onSuccess((success) => {
                    roadmapResponse = success.data as IRoadmapResponse;
                });
        } catch (e) {
            appStore.catchClientError(e as Error);
        }

        return roadmapResponse;
    }

    /**
     * @see GamesController.getAvailable
     * @see GamesService.getAvailableGames
     * */
    static async getAvailableRequest(): Promise<AxiosResponse<IResponseSuccess<IGamesGridRow[]>>> {
        return await clientApi.get<IResponseSuccess<IGamesGridRow[]>>(appConfig.client.endpoints.api.games.available, {
            withCredentials: true,
        });
    }

    static async getAvailableGames(): Promise<IAvailableGamesResponse> {
        let gamesResponse: IAvailableGamesResponse = {  } as IAvailableGamesResponse;
        try {
            const response = new ClientApiResponse(await ClientGamesService.getAvailableRequest()).response;

            response
                .onError((err) => {
                    appStore.onClientResponseError(err, false);
                })
                .onSuccess((success) => {
                    gamesResponse = success.data as IAvailableGamesResponse;
                });
        } catch (e) {
            appStore.catchClientError(e as Error);
        }

        return gamesResponse;
    }

    /**
     * @see GamesController.getMarketingKit
     * @see GamesService.getMarketingKit
     * */
    static async getMarketingKitRequest(
        gameId: string
    ): Promise<AxiosResponse<IResponseSuccess<IMarketingKitResponse>>> {
        return await clientApi.get<IResponseSuccess<IMarketingKitResponse>>(
            appConfig.client.endpoints.api.games.getMarketingMaterials.replace(":gameId", gameId),
            {
                withCredentials: true,
            }
        );
    }

    static async getMarketingKit(gameId: string): Promise<IMarketingKitResponse> {
        let marketingKitResponse: IMarketingKitResponse = {} as IMarketingKitResponse;
        try {
            const response = new ClientApiResponse(await ClientGamesService.getMarketingKitRequest(gameId)).response;

            response
                .onError((err) => {
                    appStore.onClientResponseError(err, false);
                })
                .onSuccess((success) => {
                    marketingKitResponse = success.data as IMarketingKitResponse;
                });
        } catch (e) {
            appStore.catchClientError(e as Error);
        }

        return marketingKitResponse;
    }

    /**
     * @see GamesController.getSearch
     * @see GamesService.getSearchGames
     * */
    static async getSearchGamesRequest(): Promise<AxiosResponse<IResponseSuccess<ISearchData>>> {
        return await clientApi.get<IResponseSuccess<ISearchData>>(appConfig.client.endpoints.api.games.search, {
            withCredentials: true,
        });
    }

    static async getSearchGames(): Promise<ISearchData> {
        let gamesResponse: ISearchData = {} as ISearchData;
        try {
            const response = new ClientApiResponse(await ClientGamesService.getSearchGamesRequest()).response;

            response
                .onError((err) => {
                    appStore.onClientResponseError(err, false);
                })
                .onSuccess((success) => {
                    gamesResponse = success.data as ISearchData;
                });
        } catch (e) {
            appStore.catchClientError(e as Error);
        }

        return gamesResponse;
    }
}
