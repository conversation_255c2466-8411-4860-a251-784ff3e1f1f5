import { MovieJsonLdProps } from "next-seo/lib/jsonld/carousel";
import { useRouter } from "next/router";
import { EnumLocale, getLocaleMessages } from "locales/locales";
import { Project } from "utils/project";
import { useContext } from "react";
import { ILiveStudioSlider } from "@pages/site/live-studio";
import { MainAppContext } from "@pages/_app";
import { EnumPage } from "models/enum/page";

export function useSeoCarouselLiveGames(sliderGames: ILiveStudioSlider[]): MovieJsonLdProps[] {

    const { appStore } = useContext(MainAppContext);
    const router = useRouter();
    const locale = router.locale as EnumLocale;

    const msg = getLocaleMessages(locale as EnumLocale).messages;

    const entities: { [gameCode: string]: MovieJsonLdProps } = {};
    if (Array.isArray(sliderGames) && sliderGames.length > 0) {
        for (let i = 0; i < sliderGames.length; i++) {
            const slider = sliderGames[i];
            if (Array.isArray(slider?.games) && slider?.games?.length > 0) {
                for (let j = 0; j < slider?.games.length; j++) {
                    const game = slider?.games[j];
                    const gamePageUrl = Project.getPageUrl(appStore, EnumPage.gameDetails,
                        { gameId: game?.gameCode, gameTitle: game?.gameTitle }
                    );
                    entities[game?.gameCode] = {
                        url: gamePageUrl,
                        name: game?.gameTitle,
                        image: Project.replaceSanityCdn(game?.gamePosterUrl),
                        director: {
                            name: String(msg.skywindCompanyName)
                        }
                    };
                }
            }
        }
    }
    return Object.values(entities);
}