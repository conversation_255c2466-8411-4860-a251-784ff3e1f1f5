import { useContext, useEffect } from "react";
import Router from "next/router";
// import authStore from "../stores/authStore";
import { toJS } from "mobx";
import { MainAppContext } from "pages/_app";

export default function useUser({ redirectTo }: { redirectTo: string }) {
    const { authStore /*, appStore*/ } = useContext(MainAppContext);
    const user = toJS(authStore.getUser());

    useEffect(() => {
        if (!redirectTo || !user) {
            return;
        }

        if (redirectTo && !authStore?.isAuthorized) {
            Router.push(redirectTo);
        }
    }, [user, redirectTo, authStore?.isAuthorized]);

    return { user: authStore?.isAuthorized ? user : null };
}
