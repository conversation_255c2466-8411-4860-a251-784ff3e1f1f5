import { useContext, useEffect, useRef } from "react";
import { EnumLocalStorage } from "models/enum/local-storage";
import Router, { useRouter } from "next/router";
import { MainAppContext } from "pages/_app";
import { EnumPage } from "models/enum/page";
import { Project } from "utils/project";
import { EnumLocale } from "locales/locales";

const usePaAuth = () => {
    const { authStore, appStore } = useContext(MainAppContext);
    const router = useRouter();
    const locale = router.locale as EnumLocale;

    const strictModeRef = useRef<boolean>(false);

    useEffect(() => {
        const loginPageUrl = Project.getPageUrl(appStore, EnumPage.login);

        const redirectAfterLogin = localStorage.getItem(EnumLocalStorage.redirectAfterLogin);
        // console.log(locale, router.asPath, loginPageUrl, Project.comparePath(locale, router.asPath, loginPageUrl));
        if (router.asPath !== loginPageUrl) {
            if (!redirectAfterLogin) {
                localStorage.setItem(EnumLocalStorage.redirectAfterLogin, router.asPath);
            }
        }

        if (localStorage.getItem(EnumLocalStorage.accessToken)) {
            if (!authStore.isAuthorized || !authStore.getUser()) {
                if (!strictModeRef.current) {
                    strictModeRef.current = true;
                    // http://localhost:3044/play/player/sw_wiwicaou_partner
                    authStore
                        .checkAuth(async () => {
                            strictModeRef.current = false;
                            await Router.push(loginPageUrl);
                        }, () => {
                            strictModeRef.current = false;
                        })
                        .then();
                }
            }
        } else if (router.asPath !== loginPageUrl) {
            Router.push(loginPageUrl).then();
        }
    }, [authStore, router.asPath, router.pathname, router.route, authStore.isAuthorized, locale, appStore]);

    return { authStore, appStore };
};

export default usePaAuth;
