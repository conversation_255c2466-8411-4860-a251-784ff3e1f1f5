import { EnumPage } from "models/enum/page";
import { useRouter } from "next/router";
import { IProjectPageInfo } from "models/site-pages";
import { ILocaleObject } from "locales/enumLocale";
import { useContext } from "react";
import { MainAppContext } from "@pages/_app";
import { EnumLocale, getLocaleMessages } from "locales/locales";
import { Project } from "utils/project";

export interface IUsePAPageInfo {
    pageUrlRelative: string;
    pageUrlAbsolute: string;
    pageTitle: string;
    pageInfo: IProjectPageInfo;
    appLocaleMessages: ILocaleObject;
}

export function usePaPageInfo(page: EnumPage, pageParams?: { [name: string]: string }): IUsePAPageInfo {
    const router = useRouter();
    const { appStore } = useContext(MainAppContext);
    const hostname = appStore.getHostName();
    const schema = appStore.getHttpSchema();
    const locale = router.locale as EnumLocale;

    const sitePages = Project.getPaPages();

    return {
        pageUrlRelative: Project.getPageUrl(appStore, page, pageParams),
        pageUrlAbsolute: Project.getPageUrl(appStore, page, pageParams, { hostname, schema }),
        pageTitle: Project.getSitePageTitle(locale, sitePages[page]),
        pageInfo: sitePages[page],
        appLocaleMessages: getLocaleMessages(locale)
    };
}