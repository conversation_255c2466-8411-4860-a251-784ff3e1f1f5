import { useContext } from "react";
import { MovieJsonLdProps } from "next-seo/lib/jsonld/carousel";
import { Game } from "utils/game";
import { ISectionSliderGamesExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { EnumLanguageCode } from "models/enum/system";
import { useRouter } from "next/router";
import { EnumLocale, getLocaleMessages } from "locales/locales";
import { Project } from "utils/project";
import { MainAppContext } from "@pages/_app";
import { EnumPage } from "models/enum/page";

export function useSeoCarouselSliderGames(
    sliderGames: ISectionSliderGamesExtended[],
    languageCode: EnumLanguageCode): MovieJsonLdProps[] {
    const { appStore } = useContext(MainAppContext);

    const router = useRouter();
    const locale = router.locale as EnumLocale;

    const msg = getLocaleMessages(locale).messages;
    const projectType = appStore.getAppProject().type;

    const entities: { [gameCode: string]: MovieJsonLdProps } = {};
    if (Array.isArray(sliderGames) && sliderGames.length > 0) {
        for (let i = 0; i < sliderGames.length; i++) {
            const slider = sliderGames[i];
            if (Array.isArray(slider?.games) && slider?.games?.length > 0) {
                for (let j = 0; j < slider?.games.length; j++) {
                    const game = slider?.games[j];
                    const gameInfo = Game.parseGame(game,
                        languageCode,
                        projectType,
                        Project.getPageUrl(appStore, EnumPage.gameDetails,
                            { gameId: String(game?.gameId?.current) }));
                    if (gameInfo) {
                        const { gamePageUrlRelative, gameName, gameCode } = gameInfo;
                        entities[gameCode] = {
                            url: gamePageUrlRelative,
                            name: gameName,
                            image: Project.replaceSanityCdn(game?.gamePosterUrl),
                            director: {
                                name: String(msg.skywindCompanyName)
                            }
                        };
                    }
                }
            }
        }
    }
    return Object.values(entities);
}