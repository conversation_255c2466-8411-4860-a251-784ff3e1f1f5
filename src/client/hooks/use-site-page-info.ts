import { ILocaleObject } from "locales/enumLocale";
import { useRouter } from "next/router";
import { EnumLocale, getLocaleMessages } from "locales/locales";
import { useContext } from "react";
import { MainAppContext } from "@pages/_app";
import { IProjectPageInfo } from "models/site-pages";
import { Project } from "utils/project";
import { EnumPage } from "models/enum/page";

export interface IUseSitePageInfo {
    pageUrlRelative: string;
    pageUrlAbsolute: string;
    pageTitle: string;
    pageInfo: IProjectPageInfo;
    appLocaleMessages: ILocaleObject;
}

export function useSitePageInfo(page: EnumPage, pageParams?: { [name: string]: string }): IUseSitePageInfo {
    const router = useRouter();
    const { appStore } = useContext(MainAppContext);
    const project = appStore.getAppProject();
    const hostname = appStore.getHostName();
    const schema = appStore.getHttpSchema();
    const locale = router.locale as EnumLocale;

    const sitePages = Project.getSitePages(project.code);

    return {
        pageUrlRelative: Project.getPageUrl(appStore, page, pageParams),
        pageUrlAbsolute: Project.getPageUrl(appStore, page, pageParams, { hostname, schema }),
        pageTitle: Project.getSitePageTitle(locale, sitePages[page]),
        pageInfo: sitePages[page],
        appLocaleMessages: getLocaleMessages(locale)
    };
}