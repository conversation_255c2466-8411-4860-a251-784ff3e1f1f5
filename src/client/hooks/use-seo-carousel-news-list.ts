import { EnumLanguageCode } from "models/enum/system";
import { MovieJsonLdProps } from "next-seo/lib/jsonld/carousel";
import { useRouter } from "next/router";
import { EnumLocale, getLocaleMessages } from "locales/locales";
import { Project } from "utils/project";
import { useContext } from "react";
import { INewsExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { SanityApi } from "utils/sanityApi";
import { MainAppContext } from "@pages/_app";
import { EnumPage } from "models/enum/page";

export function useSeoCarouselNewsList(news: INewsExtended[], languageCode: EnumLanguageCode): MovieJsonLdProps[] {

    const { appStore } = useContext(MainAppContext);
    const router = useRouter();
    const locale = router.locale as EnumLocale;

    const msg = getLocaleMessages(locale).messages;

    const entities: MovieJsonLdProps[] = [];

    if (Array.isArray(news) && news.length > 0) {
        for (let j = 0; j < news.length; j++) {
            const newsArticle = news[j];
            const newsArticleUrl = Project.getPageUrl(appStore, EnumPage.newsArticle, {
                newsId: newsArticle._id as string
            });
            const newsArticleTitle = SanityApi.getLocale(newsArticle?.title, languageCode);
            entities.push({
                url: newsArticleUrl,
                name: newsArticleTitle,
                image: Project.replaceSanityCdn(newsArticle.newsPreviewImageUrl),
                director: {
                    name: String(msg.skywindCompanyName)
                }
            });
        }
    }
    return entities;
}