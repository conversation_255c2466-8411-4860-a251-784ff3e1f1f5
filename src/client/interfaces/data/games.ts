import { IGame, IGameCode } from "api/data-providers/sanity/schemas";

export interface IGameData {
    cat: string[];
    cert: string[];
    code: string;
    date: string;
    id: string;
    image: string;
    jackpot: string;
    mk: string[];
    play: string;
    rtp: number;
    tag: string[];
    title: string;
    type: string;
    url: string;
    vol: string;
    year: string;
}

export interface IGamesResponse {
    games: IGameData[];
    search: {
        cert: string[];
        rtp: number[];
        tag: string[];
        type: string[];
        vol: string[];
        year: string[];
    };
}

export interface IGameGL extends IGame {
    gameTowerPosterUrl?: string;
    gamePosterUrl?: string;
    gameVideoUrl?: string;
    gameCodes?: IGameCode[];
}
