import React, { useState } from "react";
import { Button } from "react-bootstrap";
import { observer } from "mobx-react-lite";
import { IMonth } from "./roadmapInterfaces";
import { EnumUserClientAction } from "models/enum/user";
import { getSelectedMonths } from "./roadmapGrid";
import ClientUsersService from "client/services/clientUsersService";

const MonthToolbar = observer(
    (props: { months: IMonth[]; onAfterClick: (nm: IMonth[], selected: number[], fullRoadMap: boolean) => void }) => {
        const { months, onAfterClick } = props;
        const [fullRoadMap, setFullRoadmap] = useState<boolean>(true);

        const handleToolbarClick = async (el: MouseEvent, selectedMonth: number) => {
            if (
                el &&
                (selectedMonth === 0 ||
                    months.filter((month) => Number(month.id) === Number(selectedMonth)).length === 1)
            ) {
                const selectedMonths = getSelectedMonths(months);
                const nm = [...months];
                const selected = [];
                for (let mi = 0; mi < months.length; mi++) {
                    nm[mi].selected = nm[mi].id === selectedMonth || selectedMonth === 0;
                    if (nm[mi].selected) {
                        selected.push(nm[mi].id);
                    }
                }
                if (
                    selectedMonths.length === selected.length &&
                    selectedMonths.length === 1 &&
                    selectedMonths[0] === selected[0]
                ) {
                    return;
                }
                setFullRoadmap(selectedMonth === 0 || selected.length === 0);
                onAfterClick(nm, selected, fullRoadMap);
                await ClientUsersService.clientAction(
                    EnumUserClientAction.roadmapUpcomingMonth,
                    location.pathname,
                    { months: nm },
                    "roadmapToolbar"
                );
            }
        };

        return (
            <div key={"secondToolbar"} className={"toolbar-root"}>
                {Object.values(months).map((month) => {
                    return (
                        <Button
                            key={"month-" + month.id}
                            onClick={(event) => {
                                handleToolbarClick(event as never, month.id);
                            }}
                            variant="outlined-primary"
                            className={month.selected ? "active" : ""}
                        >
                            {month.name}
                        </Button>
                    );
                })}
                {!fullRoadMap && (
                    <Button
                        key={"full-roadmap"}
                        onClick={(event) => {
                            handleToolbarClick(event as never, 0);
                        }}
                        variant="outlined-primary"
                        className={fullRoadMap ? "active" : ""}
                    >
                        Full Roadmap
                    </Button>
                )}
            </div>
        );
    }
);

export default MonthToolbar;
