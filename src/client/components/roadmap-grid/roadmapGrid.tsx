/* eslint-disable @next/next/no-img-element */
import React, { useEffect, useRef, useState } from "react";
import { IGamesGridRow, IMarketWithReleaseDate, IRoadmapResponse } from "api/services/interfaces/iGamesService";
import { observer } from "mobx-react-lite";
import {
    EnumRoadmapToolbar,
    GamesBrand,
    IMonth,
    IRoadmapGamesGrid,
    IRoadmapToolbar,
    IRoadmapToolbarButton
} from "./roadmapInterfaces";
import RoadmapToolbar from "./roadmapToolbar";
import MonthToolbar from "./monthToolbar";
import { EnumUserClientAction } from "models/enum/user";
import { EnumDBUserPermissions } from "models/enum/db";
import ClientGamesService from "client/services/clientGamesService";
import ClientUsersService from "client/services/clientUsersService";
import GameTable from "@components/games-table/gameTable";
import dayjs from "dayjs";
import { useRouter } from "next/router";

let roadmapInfo: IRoadmapResponse = {
    showPerMonthToolbar: true,
    defaultMarketCode: "",
    availableMarkets: {},
    biggestMarkets: {},
    roadmapMonths: {},
    roadmapMarkets: {},
    games: {},
    limit: {
        latestReleasesMonth: 6,
        upcomingMonthThirdParty: 2,
    },
};

const getMonthTitles = (months: Array<number>): { [month: number]: string } => {
    return months.map((m) => {
        return dayjs("2000-" + m + "-01").format("MMM");
    });
};

// ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] started from 0
const monthTitles = getMonthTitles(Array.from({ length: 12 }, (_, i) => i + 1));

const allMarketsCode = "all_markets";

export const getSelectedMonths = (months: IMonth[]): number[] => {
    const selected = [];
    for (let mi = 0; mi < months.length; mi++) {
        if (months[mi].selected) {
            selected.push(months[mi].id);
        }
    }
    return selected;
};

const RoadmapGrid = observer((props: IRoadmapGamesGrid) => {
    const {brand = GamesBrand.ALL} = props;
    // const { authStore /*, appStore*/ } = useContext(AppContext);
    // const user = authStore.getUser();
    // const userGroups = user?.userPermissions?.groups;
    const virtuoso = useRef(null);

    const [rows, setRows] = useState<IGamesGridRow[]>([]);
    const [virtuosoAlign] = useState("start");
    const [virtuosoBehavior] = useState("auto");

    const getAvailableGames = async () => {
        return await ClientGamesService.getAvailableGames();
    };

    const getRoadmapGames = async () => {
        return await ClientGamesService.getRoadmapGames();
    };

    const [toolbar, setToolbar] = useState<IRoadmapToolbar>({
        id: "app-roadmap-toolbar",
        buttons: [
            {
                key: EnumRoadmapToolbar.availableNow,
                text: "Available Now",
                hasMenu: false,
                menu: null,
                visible: true,
                permissions: [
                    EnumDBUserPermissions.paCanViewPageRoadmap,
                    EnumDBUserPermissions.paCanViewPageRoadmapAvailable,
                ],
            },
            {
                key: EnumRoadmapToolbar.latestReleases,
                text: "Latest Releases",
                hasMenu: false,
                menu: null,
                visible: true,
                permissions: [
                    EnumDBUserPermissions.paCanViewPageRoadmap,
                    EnumDBUserPermissions.paCanViewPageRoadmapLatest,
                ],
            },
            {
                key: EnumRoadmapToolbar.fullRoadMap,
                text: "Upcoming Releases",
                hasMenu: false,
                menu: null,
                visible: true,
                permissions: [
                    EnumDBUserPermissions.paCanViewPageRoadmap,
                    EnumDBUserPermissions.paCanViewPageRoadmapUpcoming,
                ],
            },
            {
                key: EnumRoadmapToolbar.byMarket,
                text: "By Market",
                hasMenu: false,
                menu: null,
                value: null,
                values: {},
                visible: false,
                permissions: [
                    EnumDBUserPermissions.paCanViewPageRoadmap,
                    EnumDBUserPermissions.paCanViewPageRoadmapUpcoming,
                ],
            },
        ],
        activeButton: EnumRoadmapToolbar.fullRoadMap,
        visible: true,
        months: [],
    });

    const getMonthsToolbarData = (data: IRoadmapResponse, marketCode: string): IMonth[] => {
        let rows = (marketCode && data.games && data.games[marketCode] && Object.values(data.games[marketCode])) || [];
        const marketMonths: { [month: number]: IMonth } = {};

        for (const game of rows) {
            const releaseDates = Object.keys(game.marketsWithReleaseDate || { [game.releaseDate]: game.markets });
            releaseDates.forEach(releaseDate => {
                const gMonth = Number(dayjs(releaseDate).format("MM"));
                const gYearMonth = Number(dayjs(releaseDate).format("YYYYMM"));
                if (!marketMonths[gYearMonth]) {
                    marketMonths[gYearMonth] = { name: monthTitles[gMonth - 1], id: gMonth, selected: true };
                }
            })
        }

        return Object.values(marketMonths);
    };

    const getMarketCode = (): string => {
        const buttonMarket = toolbar.buttons.find((b) => b.key === EnumRoadmapToolbar.byMarket);
        return buttonMarket?.value || allMarketsCode;
    };

    const setRoadmapRows = (data: IRoadmapResponse, marketCode: string, selectedMonths?: number[]) => {
        let rows = (marketCode && data.games && data.games[marketCode] && Object.values(data.games[marketCode])) || [];

        rows.forEach(row => {
            const allMarkets = row.markets.split(",").map(market => market.trim());
            row.marketsWithReleaseDate = {};
            const auxMarketsWithReleaseDate: IMarketWithReleaseDate = {};
            allMarkets.forEach(market => {
                const marketKey = Object.keys(data.availableMarkets).find(key => data.availableMarkets[key] === market) || marketCode;
                const releaseDate = data.games[marketKey][row.id]?.releaseDate;

                if (releaseDate) {
                    if (auxMarketsWithReleaseDate && auxMarketsWithReleaseDate[releaseDate]) {
                        auxMarketsWithReleaseDate[releaseDate] += `, ${market}`;
                    } else {
                        const dateParsed = dayjs(releaseDate);
                        if ((dayjs().isBefore(dateParsed) || dayjs().isSame(dateParsed)) && (!selectedMonths || selectedMonths.some(month => month - 1 <= dateParsed.month()))) {
                            // @ts-ignore
                            auxMarketsWithReleaseDate[releaseDate] = market;
                        }
                    }
                }
            })
            Object.keys(auxMarketsWithReleaseDate)
                .sort((date1, date2) => dayjs(date1).isBefore(dayjs(date2)) ? -1 : 1)
                // @ts-ignore
                .forEach(key => row.marketsWithReleaseDate[key] = auxMarketsWithReleaseDate[key]);

        })

        setRows(
            rows.filter((game) => {
                const releaseDates = Object.keys(game.marketsWithReleaseDate || { [game.releaseDate]: game.markets });

                return !!releaseDates.find(releaseDate => {
                    const date = dayjs(releaseDate, "YYYY-M-D");

                    return (
                        date.isValid() &&
                        dayjs(date).diff(dayjs(), "day") >= -1 &&
                        (!Array.isArray(selectedMonths) || selectedMonths.includes(Number(date.format("M"))))
                    );
                })
            }).sort((game1, game2) => {
                const releaseDatesGame1 = Object.keys(game1.marketsWithReleaseDate || { [game1.releaseDate]: game1.markets })
                const releaseDatesGame2 = Object.keys(game2.marketsWithReleaseDate || { [game2.releaseDate]: game2.markets })

                const releaseDateGame1 = releaseDatesGame1.reduce((acc, curr) => {
                    if (!curr) {
                        return acc;
                    }
                    if (dayjs(acc).isBefore(dayjs(curr))) {
                        return acc;
                    }
                    return curr;
                }, "")

                const releaseDateGame2 = releaseDatesGame2.reduce((acc, curr) => {
                    if (!curr) {
                        return acc;
                    }
                    if (dayjs(acc).isBefore(dayjs(curr))) {
                        return acc;
                    }
                    return curr;
                }, "")



                return dayjs(releaseDateGame1).isBefore(dayjs(releaseDateGame2)) ? -1 : 1;
            }).filter(game => {
                if (brand === GamesBrand.SKYWIND) {
                    return game.gameCode[0].indexOf("sw_") === 0;
                } else if (brand === GamesBrand.SLOTFACTORY) {
                    return game.gameCode[0].indexOf("itg_") === 0;
                } else {
                    return true;
                }
            })
        );
    };

    const switchToAvailableNow = async () => {
        await getAvailableGames().then(async (response) => {
            const now = +dayjs();
            const rows = response.games;
            rows.forEach(row => {
                row.marketsWithReleaseDate = {
                    [row.releaseDate]: row.markets
                };
            })
            setRows(
                rows.filter((game) => {
                    return +dayjs(game.releaseDate) < now;
                }).filter(game => {
                    if (brand === GamesBrand.SKYWIND) {
                        return game.gameCode[0].indexOf("sw_") === 0;
                    } else if (brand === GamesBrand.SLOTFACTORY) {
                        return game.gameCode[0].indexOf("itg_") === 0;
                    } else {
                        return true;
                    }
                })
            );
            const toolbarButtons = toolbar.buttons.map((btn) => {
                if (btn.key === EnumRoadmapToolbar.byMarket) {
                    btn.visible = false;
                    btn.value = null;
                }
                return btn;
            });
            setToolbar((state) => {
                return {
                    ...state,
                    buttons: toolbarButtons,
                    months: [],
                    activeButton: EnumRoadmapToolbar.availableNow,
                };
            });
        });
    };

    const switchToLatestReleases = async () => {
        await getAvailableGames().then(async (response) => {
            const rows = response.games;
            rows.forEach(row => {
                row.marketsWithReleaseDate = {
                    [row.releaseDate]: row.markets
                };
            })
            const now = +dayjs();
            setRows(
                rows.filter((game) => {
                    const month = dayjs().diff(game.releaseDate, "months", true);
                    return +dayjs(game.releaseDate) < now && month <= roadmapInfo.limit.latestReleasesMonth;
                }).filter(game => {
                    if (brand === GamesBrand.SKYWIND) {
                        return game.gameCode[0].indexOf("sw_") === 0;
                    } else if (brand === GamesBrand.SLOTFACTORY) {
                        return game.gameCode[0].indexOf("itg_") === 0;
                    } else {
                        return true;
                    }
                })
            );
            const toolbarButtons = toolbar.buttons.map((btn) => {
                if (btn.key === EnumRoadmapToolbar.byMarket) {
                    btn.visible = false;
                    btn.value = null;
                }
                return btn;
            });
            setToolbar((state) => {
                return {
                    ...state,
                    buttons: toolbarButtons,
                    months: [],
                    activeButton: EnumRoadmapToolbar.latestReleases,
                };
            });
        });
    };

    const switchToFullRoadmap = async () => {
        const response = await getRoadmapGames();
        const marketCode = allMarketsCode; /*roadmapInfo.defaultMarketCode*/
        roadmapInfo = response;

        const toolbarButtons = toolbar.buttons.map((btn) => {
            if (
                btn.key === EnumRoadmapToolbar.byMarket &&
                roadmapInfo.roadmapMarkets &&
                Object.values(roadmapInfo.roadmapMarkets).length > 0
            ) {
                btn.hasMenu = true;
                btn.menu = [
                    { key: allMarketsCode, text: "All Markets", iconUrl: "/assets/pa/images/markets/all_markets.png" },
                    ...(roadmapInfo.availableMarkets
                        ? ((am: any, rm) => {
                              const m = [];
                              for (const key in am) {
                                  if (key in rm) {
                                      m.push({ key, text: am[key], iconUrl: rm[key].iconUrl });
                                  }
                              }
                              return m;
                          })(roadmapInfo.availableMarkets, roadmapInfo.roadmapMarkets)
                        : Object.values(roadmapInfo.roadmapMarkets)),
                ];
                btn.values = roadmapInfo.roadmapMarkets;
                btn.visible = true;
            }
            return btn;
        });

        await setRoadmapRows(response, marketCode);

        const months = getMonthsToolbarData(response, marketCode);

        setToolbar((state) => {
            return { ...state, buttons: toolbarButtons, months, activeButton: EnumRoadmapToolbar.fullRoadMap };
        });
    };

    const handlerToolbarClick = async (event: MouseEvent, button: IRoadmapToolbarButton, value?: string) => {
        if (event && button && button.key && Object.values(EnumRoadmapToolbar).includes(button.key)) {
            if (button.key === EnumRoadmapToolbar.availableNow) {
                await switchToAvailableNow().then(async () => {
                    await ClientUsersService.clientAction(
                        EnumUserClientAction.roadmapAvailable,
                        location.pathname,
                        {},
                        "roadmapToolbar"
                    );
                });
            } else if (button.key === EnumRoadmapToolbar.latestReleases) {
                await switchToLatestReleases().then(async () => {
                    await ClientUsersService.clientAction(
                        EnumUserClientAction.roadmapLatest,
                        location.pathname,
                        {},
                        "roadmapToolbar"
                    );
                });
            } else if (button.key === EnumRoadmapToolbar.fullRoadMap) {
                await switchToFullRoadmap().then(async () => {
                    await ClientUsersService.clientAction(
                        EnumUserClientAction.roadmapUpcoming,
                        location.pathname,
                        {},
                        "roadmapToolbar"
                    );
                });
            } else if (button.key === EnumRoadmapToolbar.byMarket) {
                setToolbar((state) => {
                    const buttonIndex = state.buttons.findIndex((b) => b.key === EnumRoadmapToolbar.byMarket);
                    state.buttons[buttonIndex].value = value;

                    const marketCode = value || allMarketsCode;
                    setRoadmapRows(roadmapInfo, marketCode);

                    return { ...state, buttons: state.buttons, months: getMonthsToolbarData(roadmapInfo, marketCode) };
                });
                // @ts-ignore
                virtuoso?.current?.scrollToIndex({ index: 0, align: virtuosoAlign, behavior: virtuosoBehavior });

                const marketCode = getMarketCode();
                await ClientUsersService.clientAction(
                    EnumUserClientAction.roadmapUpcomingMarket,
                    location.pathname,
                    {
                        marketCode,
                        months: getMonthsToolbarData(roadmapInfo, marketCode),
                    },
                    "roadmapToolbar"
                );
            }
            // @ts-ignore
            virtuoso?.current?.scrollToIndex({
                index: 0,
                align: virtuosoAlign,
                behavior: virtuosoBehavior,
            });
        }
    };

    const strictModeRef = useRef<boolean>(false);
    const router = useRouter();
    useEffect(() => {
        if (!strictModeRef.current) {
            strictModeRef.current = true;
            (async () => {
                //await switchToFullRoadmap();
                if (brand === GamesBrand.SKYWIND) {
                    await switchToAvailableNow();
                } else if (brand === GamesBrand.SLOTFACTORY) {
                    await switchToAvailableNow();
                } else {
                    await switchToFullRoadmap();
                }
            })();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);
    // useEffect(() => {
    //     console.log(rows);
    //     if (brand == GamesBrand.SKYWIND) {
    //         setRows(
    //             rows.filter((game) => {
    //                 return game.gameCode[0].indexOf("sw_") === 0;
    //             })
    //         );
    //
    //     } else if (brand == GamesBrand.SLOTFACTORY) {
    //         setRows(
    //             rows.filter((game) => {
    //                 return game.gameCode[0].indexOf("itg_") === 0;
    //             })
    //         );
    //     }
    // }, []);

    // Removed as requested by Niv Gold = 06.07.2022
    // useEffect(() => {
    //     filterByTwoMarkets(rows);
    //     // eslint-disable-next-line react-hooks/exhaustive-deps
    // }, [toolbar]);

    return (
        <div id={props.id} className={props.className}>
            <div className={"roadmap-toolbar"} id={toolbar.id}>
                <RoadmapToolbar toolbar={toolbar} handlerOnClick={handlerToolbarClick} />
                {toolbar.activeButton === EnumRoadmapToolbar.fullRoadMap &&
                    Array.isArray(toolbar.months) &&
                    toolbar.months.length > 0 && (
                        <MonthToolbar
                            months={toolbar.months}
                            onAfterClick={(months: IMonth[], selected: number[]) => {
                                setToolbar((state) => {
                                    return { ...state, months };
                                });
                                setRoadmapRows(roadmapInfo, getMarketCode(), selected);
                                // @ts-ignore
                                virtuoso?.current?.scrollToIndex({
                                    index: 0,
                                    align: virtuosoAlign,
                                    behavior: virtuosoBehavior,
                                });
                            }}
                        />
                    )}
            </div>
            <GameTable games={rows} fixed={true} virtuoso={virtuoso} />
        </div>
    );
});
export default RoadmapGrid;
