import React, { useContext } from "react";
import { IRoadmapToolbar, IRoadmapToolbarButton } from "./roadmapInterfaces";
import { Button, DropdownButton } from "react-bootstrap";
import DropdownItem from "react-bootstrap/DropdownItem";
import { observer } from "mobx-react-lite";
import { MainAppContext } from "pages/_app";
import AppImg from "@components/main/appImg";

const RoadmapToolbar = observer(
    ({
        toolbar,
        handlerOnClick,
    }: {
        toolbar: IRoadmapToolbar;
        handlerOnClick: (event: MouseEvent, button: IRoadmapToolbarButton, value?: string) => void;
    }) => {
        const { authStore /*, appStore*/ } = useContext(MainAppContext);
        const user = authStore.getUser();
        const userPermissions = user?.userPermissions?.permissions;

        return (
            (Array.isArray(toolbar?.buttons) && toolbar?.buttons.length && (
                <div key={"topToolbar"} className={"toolbar-root"}>
                    {toolbar.buttons.map((button) => {
                        const buttonValue = button.value;
                        const buttonObject = buttonValue && button?.values && button?.values[buttonValue];

                        if (
                            button.visible &&
                            button.permissions.filter((bp) => userPermissions?.includes(bp)).length ===
                                button.permissions.length
                        ) {
                            if (!button.hasMenu) {
                                return (
                                    <Button
                                        key={"top-menu-button-" + button.key}
                                        onClick={(event) => handlerOnClick(event as never, button)}
                                        variant="outlined-primary"
                                        className={button.key === toolbar?.activeButton ? "active" : ""}
                                    >
                                        {buttonObject ? buttonObject.text : button.text}
                                    </Button>
                                );
                            } else if (button.hasMenu && Array.isArray(button?.menu) && button.menu.length > 0) {
                                return (
                                    <DropdownButton
                                        id={"top-menu-key-" + button.key}
                                        key={"top-menu-key-" + button.key}
                                        variant="outlined-primary"
                                        title={
                                            buttonObject ? (
                                                <>
                                                    {buttonObject.iconUrl ? (
                                                        <AppImg
                                                            src={buttonObject.iconUrl}
                                                            alt={buttonObject.text}
                                                            // layout={"intrinsic"}
                                                            width={30}
                                                            height={30}
                                                            style={{ marginRight: 4 }}
                                                        />
                                                    ) : (
                                                        ""
                                                    )}
                                                    <span>{buttonObject.text}</span>
                                                </>
                                            ) : (
                                                <span>{button.text}</span>
                                            )
                                        }
                                        className={buttonObject ? "active" : ""}
                                    >
                                        {button?.menu.map((menu) => {
                                            return (
                                                <DropdownItem
                                                    key={menu.key}
                                                    onClick={(event) => {
                                                        handlerOnClick(event as never, button, menu.key);
                                                    }}
                                                    className={buttonObject && menu.key === buttonValue ? "active" : ""}
                                                >
                                                    {menu.iconUrl && (
                                                        <AppImg
                                                            // layout={"intrinsic"}
                                                            src={menu.iconUrl as string}
                                                            // width={30}
                                                            // height={30}
                                                            alt={menu.text}
                                                        />
                                                    )}
                                                    {menu.text}
                                                </DropdownItem>
                                            );
                                        })}
                                    </DropdownButton>
                                );
                            }
                        }
                    })}
                </div>
            )) || <div key={"topToolbar"} className={"toolbar-root"} />
        );
    }
);

export default RoadmapToolbar;
