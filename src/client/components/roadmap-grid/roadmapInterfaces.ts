import { EnumDBUserPermissions } from "models/enum/db";
import { StaticImageData } from "next/image";

export enum GamesBrand {
    SKYWIND= "SKYWIND",
    SLOTFACTORY = "SLOTFACTORY",
    ALL = "ALL"
}
export interface IRoadmapGamesGrid {
    id: string;
    className: string;
    brand?: GamesBrand;
}

export interface ITopButtonMenu {
    key: string;
    text: string;
    iconUrl?: string | StaticImageData;
}

export interface IRoadmapToolbarButton {
    key: EnumRoadmapToolbar;
    text: string;
    hasMenu: boolean;
    menu: ITopButtonMenu[] | null;
    value?: string | null;
    values?: { [key: string]: { iconUrl?: string; text: string } } | null;
    visible: boolean;
    permissions: EnumDBUserPermissions[];
}

export enum EnumRoadmapToolbar {
    availableNow = "availableNow",
    latestReleases = "latestReleases",
    fullRoadMap = "fullRoadMap",
    byMarket = "byMarket",
    byBrand = "byBrand",
}

export interface IRoadmapToolbar {
    id: string;
    buttons: IRoadmapToolbarButton[];
    activeButton: EnumRoadmapToolbar;
    visible: boolean;
    months: IMonth[];
}

export interface IMonth {
    name: string;
    id: number;
    selected: boolean;
}
