import React, { useContext, useEffect } from "react";
import { Modal } from "react-bootstrap";
import { SanityApi } from "utils/sanityApi";
import { EnumLanguageCode } from "models/enum/system";
import { Game } from "utils/game";
import { observer } from "mobx-react-lite";
import AppImg from "@components/main/appImg";
import GamesModalStore, { IGamesModalStore } from "@components/games-modal/games-modal-store";
import { EnumPage } from "models/enum/page";
import { Project } from "utils/project";
import { MainAppContext } from "@pages/_app";

export interface ICmpGamesModelProps {
    languageCode: EnumLanguageCode;
}

const GamesModal = observer(({ languageCode }: ICmpGamesModelProps) => {
        const { appStore } = useContext(MainAppContext);

        const modalStore = useContext<IGamesModalStore>(GamesModalStore);
        const data = modalStore?.expandAllGames;

        useEffect(() => {
            if (data?.show) {
                document.body.classList.add("no-scroll");
                document.documentElement.classList.add("no-scroll");
            } else {
                document.body.classList.remove("no-scroll");
                document.documentElement.classList.remove("no-scroll");
            }
        }, [data?.show]);

        return (
            <Modal
                // className={"modal-explorer-all"}
                size="lg"
                aria-labelledby="contained-modal-title-vcenter"
                centered
                show={data?.show}
                onHide={() => {
                    modalStore.closeExpandAllModal();
                }}
            >
                <Modal.Header closeButton>
                    {data?.logoUrl && <AppImg
                        className="swiper-section-title-flag-icon"
                        style={{ height: "2em" }}
                        src={data?.logoUrl}
                        alt={data?.title}
                        title={data?.title}
                    />} <Modal.Title>{data?.title}</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <div className="swiper-list">
                        <section style={{ display: "flex", flexWrap: "wrap", justifyContent: "space-around" }}>
                            {data?.games.map((game) => {
                                const gameName = SanityApi.getLocale(game.gameName, languageCode);
                                const gameCode = Game.getFirstGameCode(game);
                                return (
                                    <div key={"modal--" + game._id} style={{ margin: 10 }}>
                                        <a href={Project.getPageUrl(appStore, EnumPage.gameDetails,
                                            { gameId: String(gameCode) })}>
                                            <AppImg
                                                src={game.gamePosterUrl}
                                                alt={gameName}
                                                title={gameName}
                                                style={{ zoom: 0.5 }}
                                            />
                                        </a>
                                    </div>
                                );
                            })}
                        </section>
                    </div>
                </Modal.Body>
            </Modal>
        );
    }
);

export default GamesModal;
