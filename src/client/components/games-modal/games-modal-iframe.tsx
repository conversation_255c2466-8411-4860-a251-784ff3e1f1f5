import React, { useEffect, useRef, useState } from "react";
import { Modal } from "react-bootstrap";
import { EnumLanguageCode } from "models/enum/system";
import FileViewer from "@components/file-viewer/file-viewer";
import { IGamesModalIframe } from "@components/games-modal/interfaces";
import { Project } from "utils/project";
import AppVideo from "@components/main/appVideo";

const GamesModalIframe = ({}: /*languageCode*/ { languageCode: EnumLanguageCode }) => {
    const [gamesModalIframe, setGamesModalIframe] = useState<IGamesModalIframe>({
        show: false,
        title: "",
        src: null,
        isVideo: false,
    });

    useEffect(() => {
        if (gamesModalIframe.show) {
            document.body.classList.add("no-scroll");
            document.documentElement.classList.add("no-scroll");
        } else {
            document.body.classList.remove("no-scroll");
            document.documentElement.classList.remove("no-scroll");
        }
    }, [gamesModalIframe.show]);

    const videoSrc = Project.replaceSanityCdn(gamesModalIframe.src as string);
    const playerRef = useRef<HTMLVideoElement>(null);

    return (
        <Modal
            // className={"modal-explorer-all"}
            size="xl"
            aria-labelledby="contained-modal-title-vcenter"
            centered
            autoFocus={true}
            show={gamesModalIframe.show}
            onHide={() => {
                setGamesModalIframe({
                    show: false,
                    title: "",
                    src: null,
                    isVideo: false,
                });
            }}
            className={"dialog-modal-area"}
        >
            <Modal.Header closeButton>
                <Modal.Title>{gamesModalIframe.title}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                {gamesModalIframe.src &&
                    ((gamesModalIframe.isVideo && (
                        <AppVideo videoRef={playerRef} videoUrl={videoSrc} controls autoPlay className={""}/>
                    )) || (
                        <FileViewer id={"VideoIframe"} src={gamesModalIframe.src} className={"games-modal-iframe"} />
                    ))}
            </Modal.Body>
        </Modal>
    );
};

export default GamesModalIframe;
