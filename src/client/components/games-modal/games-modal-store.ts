import { createContext } from "react";
import { IGameExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { makeAutoObservable } from "mobx";
import { ISectionGamesModalExpand } from "@components/games-modal/interfaces";

export interface IGamesModalStore {
    expandAllGames: ISectionGamesModalExpand;

    openExpandAllModal(title: string, logoUrl: string | null, games: IGameExtended[]): void;

    closeExpandAllModal(): void;
}

export class GamesModalStore implements IGamesModalStore {
    constructor() {
        makeAutoObservable(this);
    }

    public expandAllGames = <ISectionGamesModalExpand>{
        show: false,
        title: "",
        logoUrl: null,
        games: [] as IGameExtended[],
    };

    public openExpandAllModal = (title: string, logoUrl: string | null, games: IGameExtended[]) => {
        this.expandAllGames = { show: true, logoUrl, title, games };
    };

    public closeExpandAllModal = () => {
        this.expandAllGames = {
            show: false,
            logoUrl: null,
            title: "",
            games: [],
        };
    };
}

export default createContext(new GamesModalStore());
