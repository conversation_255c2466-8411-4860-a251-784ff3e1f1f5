import React, { ReactNode, useContext, useEffect, useRef } from "react";
import { MainAppContext } from "pages/_app";
import { observer } from "mobx-react-lite";
import { Container } from "react-bootstrap";
import moment from "moment";
import DialogIframe from "@components/dialogs/dialog-iframe";
import DialogMarketingKit from "@components/dialogs/dialog-marketing-kit";
import DialogImages from "@components/dialogs/dialog-images";
import DialogFirstEntrance from "@components/dialogs/dialog-first-entrance";
import DialogPasswordChange from "@components/dialogs/dialog-password-change";
import DialogVideo from "@components/dialogs/dialog-video";
import DialogCertificates from "@components/dialogs/dialog-certificates";
import { ClientApp } from "client/app/clientApp";
import { EnumProjectType } from "models/enum/system";

export interface ICmpAppContainerProps {
    className?: string;
    fixed?: boolean;
    fluid?: boolean;
    children: ReactNode;
}

export const AppContainer = observer(
    ({ className, children, fixed, fluid }: ICmpAppContainerProps) => {
        const { authStore, appStore } = useContext(MainAppContext);

        const fe = appStore.getUserFirstEntranceAgreement();
        const pc = appStore.getUserPasswordChangeStatus();
        const project = appStore.getAppProject();
        const projectType = project?.type as EnumProjectType;

        const strictModeRef = useRef<boolean>(false);
        useEffect(() => {
            if (!strictModeRef.current) {
                strictModeRef.current = true;
                if (projectType === EnumProjectType.pa) {
                    const user = authStore.getUser();

                    appStore.setUserFirstEntranceAgreement(
                        authStore.isAuthorized &&
                        typeof user?.firstEntranceAgreementAt === "string" &&
                        moment(String(user?.firstEntranceAgreementAt)).isValid()
                    );

                    appStore.setUserPasswordChangeStatus(
                        authStore.isAuthorized &&
                        (typeof user?.passwordUpdatedAt !== "string" ||
                            !moment(String(user?.passwordUpdatedAt)).isValid())
                    );
                } else {
                    appStore.setUserFirstEntranceAgreement(true);
                    appStore.setUserPasswordChangeStatus(false);
                }
            }
            // eslint-disable-next-line
        }, []);

        const target = React.useRef(null);
        const id = "main-container";

        useEffect(() => {
            if (authStore.isAuthorized) {
                if (!fe) {
                    ClientApp.dialog.openFirstEntrance("");
                } else if (pc) {
                    ClientApp.dialog.openPasswordChange("");
                }
            }
        }, [fe, pc, authStore.isAuthorized]);

        return (
            <Container
                {...(((fluid || fluid === undefined) && { fluid: true }))}
                className={[id, ...(className ? [className] : []), ...(fixed ? ["window-fixed"] : [])].join(" ")}
                id={id} ref={target}
            >
                {children}
                <DialogFirstEntrance id={"first-entrance"} />
                <DialogPasswordChange id={"change-password-after-login"} />
                <DialogVideo id={"video-dialog"} />
                <DialogImages id={"images-dialog"} />
                <DialogCertificates id={"certificates-dialog"} />
                <DialogIframe id={"dialog-product-sheet"} className={"dialog-iframe"} />
                <DialogMarketingKit id={"dialog-marketing-kit"} className={"dialog-marketing-Kit"} />
            </Container>
        );
    }
);
