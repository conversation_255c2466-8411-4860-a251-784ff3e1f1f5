import React from "react";
import AppLink from "@components/main/appLink";

export const ComingSoon = ({ gotoUrl, className }: { gotoUrl: string, className: string }) => {
    return (
        <section className={className}>
            <div className="container-fluid">
                <div className="row">
                    <div className="col">
                        <div className="blockcenter">
                            <h2 className="title blockcenter__title">
                                Coming <span className="accent">soon</span>
                            </h2>
                            <p className="blockcenter__offer">
                                The page you are looking currently in development.
                            </p>
                            <AppLink href={gotoUrl} className="button button--primary blockcenter__btn">
                                go home
                            </AppLink>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};