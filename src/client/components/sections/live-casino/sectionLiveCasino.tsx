import { Swiper, SwiperSlide } from "swiper/react";
import { Keyboard, Mousewheel, Navigation, Pagination, Scrollbar } from "swiper";

import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/scrollbar";
import AppImg from "@components/main/appImg";
import React from "react";

const SectionLiveCasino = () => {
    return (
        <section className="sw-section section-live-casino">
            <div className="lc-top-wrap">
                <div className="lc-top-wrap__inner">
                    <div className="lc-top-text">
                        <div className="sw-section-title">
                            Live Casino Lobby
                        </div>
                        <div className="sw-section-description"><p>Engage with live casinos like never before.</p></div>
                        {/*<div className="lc-top-text__desc">*/}
                        {/*    /!*With a strong focus on technological capabilities we’ve lowered costs while granting higher*!/*/}
                        {/*    /!*margins and a wider array of betting possibilities.*!/*/}
                        {/*</div>*/}
                    </div>
                </div>
                <div className="lc-top-wrap__inner">
                    <Swiper
                        modules={[Navigation, Pagination, Mousewheel, Keyboard, Scrollbar]}
                        slidesPerView={"auto"}
                        spaceBetween={24}
                        observer={true}
                        observeParents={true}
                        grabCursor={true}
                        keyboard={true}
                        loop={true}
                        speed={500}
                        navigation={{
                            nextEl: ".swiper-button-next",
                            prevEl: ".swiper-button-prev",
                            disabledClass: "swiper-button-disabled"
                        }}
                        breakpoints={{
                            0: {
                                slidesPerView: 1.25,
                                spaceBetween: 16,
                            },
                            480: {
                                // slidesPerView: 2.15,
                                slidesPerView: 'auto',
                                spaceBetween: 16,
                            },
                            768: {
                                // slidesPerView: 2.5,
                                slidesPerView: 'auto',
                                spaceBetween: 30,
                            },
                            1024: {
                                // slidesPerView: 3.5,
                                slidesPerView: 'auto',
                                spaceBetween: 40,
                            },
                        }}
                        className={"lc-top-slider swiper-container"}
                    >
                        <SwiperSlide className="lc-top-slider__slide">
                            <div className="lc-top-slide">
                                <div className="lc-top-slide__top">
                                    <h3>The Lobby</h3>
                                    <AppImg
                                        src="https://cdn.skywindgroup.com/cdn/file/00/26/0026a8052a6216333c62c92abaa1f191ac24a27f.svg"
                                        className="img"
                                        alt="The Hub"
                                    />
                                    <AppImg
                                        src="https://cdn.skywindgroup.com/cdn/file/8b/fd/8bfdac2e00f50d666497a2a371de86fb3be71335.svg"
                                        className="img-hover"
                                        alt="The Hub"
                                    />
                                </div>
                                <div className="lc-top-slide__offer">
                                    A next generation lobby that facilitates unlimited branded opportunities and a
                                    phenomenal marketing suite.
                                </div>
                            </div>
                        </SwiperSlide>
                        <SwiperSlide className="lc-top-slider__slide">
                            <div className="lc-top-slide">
                                <div className="lc-top-slide__top">
                                    <h3>World Class Games</h3>
                                    <AppImg
                                        src="https://cdn.skywindgroup.com/cdn/file/03/64/0364c7dcabf67af606405230e223b5dc41116981.svg"
                                        className="img"
                                        alt="World Class Games"
                                    />
                                    <AppImg
                                        src="https://cdn.skywindgroup.com/cdn/file/b3/98/b398a86578f302500fbcb60725763fe4d8f7bbea.svg"
                                        className="img-hover"
                                        alt="World Class Games"
                                    />
                                </div>
                                <div className="lc-top-slide__offer">
                                    We can personalize top-performing games according to your needs{/* and provide access
                                    to one of the largest suites of branded content in the iGaming world*/}.
                                </div>
                            </div>
                        </SwiperSlide>
                        <SwiperSlide className="lc-top-slider__slide">
                            <div className="lc-top-slide">
                                <div className="lc-top-slide__top">
                                    <h3>Rich User Experience</h3>
                                    <AppImg
                                        src="https://cdn.skywindgroup.com/cdn/file/b3/cb/b3cb0d9d6d1ac28f9ddf379fb3eef44a7f4415a4.svg"
                                        className="img"
                                        alt="Rich User Experience"
                                    />
                                    <AppImg
                                        src="https://cdn.skywindgroup.com/cdn/file/09/44/09442c6515a3224d4d21a1667b59619e288a5860.svg"
                                        className="img-hover"
                                        alt="Rich User Experience"
                                    />
                                </div>
                                <div className="lc-top-slide__offer">
                                    Our platform provides intuitive gameplay with live interactions and real-time stats.
                                    Launch tables directly from the lobby and increase engagement with side bet options
                                    like Egalite bets!
                                </div>
                            </div>
                        </SwiperSlide>
                        <SwiperSlide className="lc-top-slider__slide">
                            <div className="lc-top-slide">
                                <div className="lc-top-slide__top">
                                    <h3>Localize</h3>
                                    <AppImg
                                        src="https://cdn.skywindgroup.com/cdn/file/f4/dd/f4ddad268a3d82d8d2544e8e9cde412e60700316.svg"
                                        className="img"
                                        alt="Localize"
                                    />
                                    <AppImg
                                        src="https://cdn.skywindgroup.com/cdn/file/de/93/de93408df50ccad0dc99b46c9f383a2db49c518d.svg"
                                        className="img-hover"
                                        alt="Localize"
                                    />
                                </div>
                                <div className="lc-top-slide__offer">
                                    All games can be localized and tailored to the needs of your markets. Reach directly
                                    to your audience with dealers that speak their language.
                                </div>
                            </div>
                        </SwiperSlide>
                        <SwiperSlide className="lc-top-slider__slide">
                            <div className="lc-top-slide">
                                <div className="lc-top-slide__top">
                                    <h3>Seamless Integration</h3>
                                    <AppImg
                                        src="https://cdn.skywindgroup.com/cdn/file/11/ad/11ad191d1650017c94e6a9876cde89115ebcb26d.svg"
                                        className="img"
                                        alt="Seamless Integration"
                                    />
                                    <AppImg
                                        src="https://cdn.skywindgroup.com/cdn/file/af/af/afaf22af7d08ecedc435b1e0fac004fcc040f391.svg"
                                        className="img-hover"
                                        alt="Seamless Integration"
                                    />
                                </div>
                                <div className="lc-top-slide__offer">
                                    Integrate into Skywind Group once, and gain access to our live hub as well as to our
                                    games.
                                </div>
                            </div>
                        </SwiperSlide>
                        {/*<div className="swiper-button swiper-button-prev" />*/}
                        <div className="swiper-button swiper-button-next" />
                    </Swiper>
                </div>
            </div>
        </section>
    );
};

export default SectionLiveCasino;
