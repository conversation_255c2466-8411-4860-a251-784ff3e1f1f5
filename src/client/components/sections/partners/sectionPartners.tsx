import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Grid, Keyboard, Mousewheel, Navigation, Pagination, Scrollbar } from "swiper";

import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/scrollbar";
import "swiper/css/grid";
import { EnumLanguageCode } from "models/enum/system";
import { SanityApi } from "utils/sanityApi";
import { IPartnerExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import AppLink from "@components/main/appLink";
import AppImg from "@components/main/appImg";

const SectionPartners = ({
    partners,
    title,
    languageCode,
}: {
    partners: IPartnerExtended[] | undefined | null;
    title: string;
    languageCode: EnumLanguageCode;
}) => {
    if (!Array.isArray(partners) || partners.length === 0) {
        return null;
    }
    return (
        <section className="sw-section">
            <div className={"section-header"}>
                <div className="sw-section-title">{title}</div>
            </div>
            <div className="container">
                <Swiper
                    modules={[Navigation, Pagination, Mousewheel, Keyboard, Scrollbar, Grid]}
                    observer={false}
                    observeParents={false}
                    threshold={5}
                    // grid={{
                    //     rows: 2,
                    //     fill: "column",
                    // }}
                    // slidesPerView={16}
                    //                                slidesPerGroup={6}
                    spaceBetween={20}
                    pagination={{
                        el: ".swiper-pagination",
                        type: "bullets",
                        clickable: true,
                        // dynamicBullets: false,
                        renderBullet: function (index, className) {
                            return '<span class="' + className + '">' + (index + 1) + "</span>";
                        },
                    }}
                    // grabCursor={true}
                    // keyboard={true}
                    loop={false}
                    navigation={{
                        nextEl: ".swiper-button-next",
                        prevEl: ".swiper-button-prev",
                    }}
                    className={"swiper-partner"}
                    breakpoints={{
                        300: {
                            grid: {
                                rows: 3,
                                fill: "column",
                            },
                            spaceBetween: 10,
                            slidesPerView: 2,
                            slidesPerGroup: 2,
                            pagination: {
                                el: ".swiper-pagination",
                                type: "fraction",
                            },
                        },
                        500: {
                            grid: {
                                rows: 3,
                                fill: "column",
                            },
                            spaceBetween: 10,
                            slidesPerView: 3,
                            slidesPerGroup: 3,
                            pagination: {
                                el: ".swiper-pagination",
                                type: "fraction",
                            },
                        },
                        768: {
                            grid: {
                                rows: 4,
                                fill: "column",
                            },
                            slidesPerView: 4,
                            slidesPerGroup: 4,
                            pagination: {
                                el: ".swiper-pagination",
                                type: "bullets",
                                clickable: true,
                                // renderBullet: function (index, className) {
                                //     return '<span class="' + className + '">' + (index + 1) + "</span>";
                                // },
                            },
                        },
                    }}
                >
                    {partners?.map((partner) => {
                        const url = partner.partnerUrl || "#";
                        const name = SanityApi.getLocale(partner.partnerName, languageCode);
                        return (
                            <SwiperSlide key={partner._id}>
                                <AppLink href={url} target="_blank" rel="noreferrer">
                                    <AppImg src={partner.partnerImageUrl} alt={name} title={name} />
                                </AppLink>
                            </SwiperSlide>
                        );
                    })}
                    <div className="swiper-nav">
                        <div className="swiper-button-prev" />
                        <div className="swiper-pagination" />
                        <div className="swiper-button-next" />
                    </div>
                </Swiper>
            </div>
        </section>
    );
};

export default SectionPartners;
