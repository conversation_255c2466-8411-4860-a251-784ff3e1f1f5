import "swiper/css";
import "swiper/css/navigation";

import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper";
import { ISwiperTopGameCard } from "@components/sections/swiper-games/swiper-top-game-card";
import { Button } from "react-bootstrap";
import NewsCard from "@components/news/newsCard";
import { observer } from "mobx-react-lite";

interface ISectionSwiperNewsProps {
    className?: string;
    sectionTitle: string;
    sectionButtonUrl?: string;
    sectionButtonText?: string;
    data: ISwiperTopGameCard[] | any[];
}

const SectionSwiperNews = observer((
    {
        className,
        sectionTitle,
        sectionButtonText,
        sectionButtonUrl,
        data
    }: ISectionSwiperNewsProps) => {
    return (
        <section className={className}>
            <div className="section-header">
                {sectionTitle && <div className={"sw-section-title"}>{sectionTitle}</div>}
                {sectionButtonText && sectionButtonUrl &&
                    <Button href={sectionButtonUrl} variant={"dark"}
                            className={"sw-btn-white-font"}>{sectionButtonText}</Button>}
            </div>
            <Swiper
                modules={[Navigation]}
                slidesPerView={"auto"}
                loop={false}
                speed={700}
                spaceBetween={14}
                className={"swiper-top-games sw-news-grid"}
                breakpoints={{
                    0: {
                        slidesPerView: "auto",
                        centeredSlides: true
                    },
                    576: {
                        slidesPerView: 3,
                        centeredSlides: false
                    },
                    768: {
                        slidesPerView: 4,
                        centeredSlides: false
                    },
                    976: {
                        slidesPerView: 5,
                        centeredSlides: false
                    }
                }}
            >
                {data.map((n) => {
                    return (
                        <SwiperSlide
                            className={"swiper-top-games__box box-slider"}
                            style={{ cursor: "pointer" }}
                            key={n._id}
                        >
                            <NewsCard data={n} className={"news-card-home"} />
                        </SwiperSlide>
                    );
                })}
            </Swiper>
        </section>
    );
});

export default SectionSwiperNews;
