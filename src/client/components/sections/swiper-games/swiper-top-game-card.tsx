import React, { useContext } from "react";
import AppImg from "@components/main/appImg";
import AppLink from "@components/main/appLink";
import { Game } from "utils/game";
import { IRTPType } from "api/services/interfaces/iGamesService";
import { EnumShowGameRTP } from "models/enum/game";
import { useRouter } from "next/router";
import { EnumLocale, getLocaleMessages } from "locales/locales";
import { EnumPage } from "models/enum/page";
import { observer } from "mobx-react-lite";
import { MainAppContext } from "@pages/_app";
import { Project } from "utils/project";

export interface ISwiperTopGameCard {
    className?: string;
    gameTitle: string;
    gameCode: string;
    gamePosterUrl: string;
    gameRibbon?: { type: string; title: string } | null;
    rtp?: IRTPType[];
    showGameRTP?: EnumShowGameRTP | undefined;
    volatility?: string | null;
    showGameInfo?: boolean;
    comingSoon?: boolean;
}

const SwiperTopGameCard = observer((
    {
        className, gameTitle, gameCode, gamePosterUrl, gameRibbon, rtp, showGameRTP,
        volatility, showGameInfo = true, comingSoon
    }: ISwiperTopGameCard) => {
    const { appStore } = useContext(MainAppContext);
    const router = useRouter();
    const locale = router.locale;
    const msg = getLocaleMessages(locale as EnumLocale).messages;

    const gamePageUrl = Project.getPageUrl(appStore, EnumPage.gameDetails, { gameId: gameCode });

    return (
        <AppLink
            href={comingSoon ? "#" : gamePageUrl}
            title={gameTitle}
            className={className}
            onClick={(e) => {
                if (comingSoon) {
                    e.stopPropagation();
                    e.preventDefault();
                }
                return true;
            }}
        >
            <div className={"game-label-wrap"}>
                <AppImg className="swiper-top-games__img" src={gamePosterUrl} alt={gameTitle} title={gameTitle} />
                {gameRibbon && <span className={"game-label game-label--" + gameRibbon?.type}>{gameRibbon.title}</span>}
                {showGameInfo && (
                    <div className="game-shadow"></div>
                )}
            </div>
            <div className="game-hover">
                {showGameInfo
                 ? (
                     <div className="game-hover__text">
                         <div className="game-hover__text--rtp">
                             <span>{Game.formatRTP(rtp as IRTPType[], showGameRTP, msg)}</span>
                             <span>{msg.gameRTP}</span>
                         </div>
                         <div className="game-hover__text--var">
                             <span>{volatility || msg.notApplicable}</span>
                             <span>{msg.gameVolatility}</span>
                         </div>
                     </div>)
                 : null
                }
                {/*<Button className="game-hover__btn">{comingSoon ? msg.buttonComingSoon : msg.buttonLearnMore}</Button>*/}
            </div>
        </AppLink>
    );
});

export default SwiperTopGameCard;
