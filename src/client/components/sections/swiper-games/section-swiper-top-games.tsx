import "swiper/css";
import "swiper/css/navigation";

import React, { useContext } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper";
import SwiperTopGameCard, { ISwiperTopGameCard } from "@components/sections/swiper-games/swiper-top-game-card";
import { Button } from "react-bootstrap";
import { Project } from "utils/project";
import { isDesktop, isMobile } from "react-device-detect";
import { observer } from "mobx-react-lite";
import { MainAppContext } from "@pages/_app";
import { EnumPage } from "models/enum/page";

export interface ICmpSectionSwiperTopGamesProps {
    className?: string;
    sectionTitle: string;
    sectionButtonUrl?: string;
    sectionButtonText?: string;
    data: ISwiperTopGameCard[] | any[];
}

const SectionSwiperTopGames = observer((
    {
        className, sectionTitle, sectionButtonText, sectionButtonUrl, data
    }: ICmpSectionSwiperTopGamesProps) => {
    const { appStore } = useContext(MainAppContext);
    return (
        <section className={className}>
            <div className="section-header">
                {sectionTitle && <div className={"sw-section-title"}>{sectionTitle}</div>}
                {sectionButtonText && sectionButtonUrl &&
                    <Button href={sectionButtonUrl} variant={"dark"}
                            className={"sw-btn-white-font"}>{sectionButtonText}</Button>}
            </div>
            <Swiper
                modules={[Navigation]}
                speed={700}
                freeMode={true}
                cssMode={isMobile}
                loop={false}
                onImagesReady={(swiper) => {
                    if (isDesktop) {
                        swiper.slideTo(0, 0, false);
                    }
                }}
                {...(isDesktop ? {
                    className: "swiper-top-games",
                    spaceBetween: 14,
                    breakpoints: {
                        0: {
                            slidesPerView: "auto",
                            centeredSlides: true
                        },
                        576: {
                            slidesPerView: 4.2,
                            centeredSlides: false
                        },
                        976: {
                            slidesPerView: 5.2,
                            centeredSlides: false
                        },
                        1024: {
                            slidesPerView: 5,
                            centeredSlides: false
                        }
                    }
                } : {
                    className: "swiper-games",
                    slidesPerView: 3,
                    spaceBetween: 10,
                    breakpoints: {
                        0: {
                            slidesPerView: 3.2,
                            slidesPerGroup: 1
                        },
                        576: {
                            slidesPerView: 4.2,
                            slidesPerGroup: 1
                        },
                        768: {
                            slidesPerView: 5.2,
                            slidesPerGroup: 1
                        }
                    }
                })}

            >
                {data.map((g) => (
                    <SwiperSlide
                        className={isDesktop ? "swiper-top-games__box box-slider" : "swiper-slide"}
                        key={g.gameCode}
                        style={{ cursor: "pointer" }}
                        onClick={(e) => {
                            if (g.comingSoon) {
                                e.stopPropagation();
                                e.preventDefault();
                                return false;
                            } else {
                                window.open(Project.getPageUrl(appStore, EnumPage.gameDetails, { gameId: g.gameCode }),
                                    "_blank");
                            }
                            return true;
                        }}
                    >
                        <SwiperTopGameCard
                            gameTitle={g.gameTitle}
                            gameCode={g.gameCode}
                            gameRibbon={g.gameRibbon}
                            gamePosterUrl={g.gamePosterUrl}
                            showGameRTP={g.showGameRTP}
                            rtp={g.rtp}
                            volatility={g.volatility}
                            comingSoon={g.comingSoon}
                            showGameInfo={g.showGameInfo}
                        />
                    </SwiperSlide>
                ))}
            </Swiper>
        </section>
    );
});

export default SectionSwiperTopGames;
