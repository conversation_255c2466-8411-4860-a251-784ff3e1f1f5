import React from "react";
import { EnumLanguageCode } from "models/enum/system";
import { SanityApi } from "utils/sanityApi";
import { ILicenseExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import AppLink from "@components/main/appLink";
import AppImg from "@components/main/appImg";

const SectionLicenses = ({
    licenses,
    title,
    languageCode,
}: {
    licenses: ILicenseExtended[] | undefined | null;
    title: string;
    languageCode: EnumLanguageCode;
}) => {
    if (!Array.isArray(licenses) || licenses.length === 0) {
        return null;
    }
    return (
        <section className="sw-section">
            <div className="section-header">
                <div className="sw-section-title">{title}</div>
            </div>
            <div className="t-response">
                <table className="table-licenses">
                    <thead className="table-licenses__headline">
                        <tr>
                            <th>JURISDICTION</th>
                            <th>REGULATOR</th>
                            <th>LICENCE NO.</th>
                            <th />
                        </tr>
                    </thead>
                    <tbody className="table-licenses__body">
                        {licenses.map((license) => {
                            const description = SanityApi.getLocale(license?.licenseDescription, languageCode);
                            const jurisdiction = SanityApi.getLocale(license.jurisdiction, languageCode);
                            const regulator = SanityApi.getLocale(license.regulator, languageCode);
                            return (
                                <tr key={"dl" + license._id}>
                                    <td>{jurisdiction}</td>
                                    <td>{regulator}</td>
                                    <td className="table-licenses__link">
                                        {license?.licenseUrl ? (
                                            <AppLink href={license.licenseUrl} rel={"noreferrer"} target={"_blank"}>
                                                {license.licenseNo}
                                            </AppLink>
                                        ) : (
                                             license.licenseNo
                                         )}
                                    </td>
                                    <td className="table-licenses__img">
                                        {license?.licenseUrl && license.licensePhotoUrl ? (
                                            <AppLink href={license.licenseUrl} rel={"noreferrer"} target={"_blank"}>
                                                <AppImg
                                                    src={license.licensePhotoUrl}
                                                    alt={description}
                                                    title={description}
                                                />
                                            </AppLink>
                                        ) : (
                                             <AppImg
                                                 src={license.licensePhotoUrl}
                                                 alt={description}
                                                 title={description}
                                             />
                                         )}
                                    </td>
                                </tr>
                            );
                        })}
                    </tbody>
                </table>
                <table className="table-licenses-mobile">
                    <tbody>
                    <tr>
                        {licenses.map((license) => {
                            const description = SanityApi.getLocale(license?.licenseDescription, languageCode);
                            return (
                                <td className="table-licenses__img" key={"mii" + license._id}>
                                    {license.licensePhotoUrl && (
                                        <AppImg
                                            src={license.licensePhotoUrl}
                                            alt={description}
                                            title={description}
                                        />
                                    )}
                                </td>
                            );
                        })}
                        </tr>
                        <tr>
                            {licenses.map((license) => {
                                const jurisdiction = SanityApi.getLocale(license.jurisdiction, languageCode);

                                return (
                                    <td
                                        className="table-data-label"
                                        data-label="JURISDICTION"
                                        key={"mji" + license._id}
                                    >
                                        <div>{jurisdiction}</div>
                                    </td>
                                );
                            })}
                        </tr>
                        <tr>
                            {licenses.map((license) => {
                                const regulator = SanityApi.getLocale(license.regulator, languageCode);
                                return (
                                    <td className="table-data-label" data-label="REGULATOR" key={"mri" + license._id}>
                                        <div>{regulator}</div>
                                    </td>
                                );
                            })}
                        </tr>
                        <tr>
                            {licenses.map((license) => {
                                return (
                                    <td className="table-data-label" data-label="LICENCE NO." key={"mli" + license._id}>
                                        <div className="table-licenses__link">
                                            {license?.licenseUrl ? (
                                                <AppLink href={license.licenseUrl} rel={"noreferrer"} target={"_blank"}>
                                                    {license.licenseNo}
                                                </AppLink>
                                            ) : (
                                                license.licenseNo
                                            )}
                                        </div>
                                    </td>
                                );
                            })}
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>
    );
};

export default SectionLicenses;
