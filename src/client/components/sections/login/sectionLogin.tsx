import React from "react";
import { Card } from "react-bootstrap";
import FormLogin from "@components/forms/login/form-login";
import { SwLogo } from "@components/main/sw-logo/swLogo";

const SectionLogin = () => {
    return (
        <div className="row justify-content-center align-items-center">
            <Card style={{ minWidth: 320, maxWidth: 480 }}>
                <Card.Body>
                    <div className={"form-logo"}>
                        <SwLogo className={""} />
                    </div>
                    <Card.Title className={"text-h5"}>
                        <span className={"accent"}>Welcome</span> to the Skywind Partner Area!
                    </Card.Title>
                    <Card.Text className={"text-h6"}>
                        Where you have access to our upcoming games, latest releases and all of our marketing materials.
                    </Card.Text>
                    <FormLogin className={"form-login"} id={"form-login"} submitButtonName={"Sign In"} />
                    <hr className="line-divider" />
                    {/*<div className="sign-in-footer">*/}
                    {/*    <p className="">*/}
                    {/*        <Anchor href={config.client.endpoints.forgotPassword}>Forgot password?</Anchor>*/}
                    {/*    </p>*/}
                    {/*    <p className="">*/}
                    {/*        <Anchor href={config.client.endpoints.register}>Do not have an account? Sign up</Anchor>*/}
                    {/*    </p>*/}
                    {/*</div>*/}
                </Card.Body>
            </Card>
        </div>
    );
};

export default SectionLogin;
