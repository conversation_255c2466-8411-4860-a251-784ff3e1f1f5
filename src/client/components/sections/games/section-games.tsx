import React, { useContext, useEffect, useRef } from "react";

import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper";

import "swiper/css";
import "swiper/css/navigation";
import { EnumLanguageCode } from "models/enum/system";
import { SanityApi } from "utils/sanityApi";
import { Game } from "utils/game";
import { EnumSwiperGamesLayout } from "models/enum/swiper";
import { IProviderGame } from "api/data-providers/db/mysql/interfaces/iDBProvider";
import { ISectionSliderGamesExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import AppLink from "@components/main/appLink";
import AppImg from "@components/main/appImg";
import { observer } from "mobx-react-lite";
import GamesModalStore, { IGamesModalStore } from "@components/games-modal/games-modal-store";
import { isMobile } from "react-device-detect";
import { EnumSliderSortGames } from "@components/sections/games/enums";
import { EnumPage } from "models/enum/page";
import { Project } from "utils/project";
import { MainAppContext } from "@pages/_app";
import { exec } from "child_process";

export interface ISectionGamesProps {
    slider: ISectionSliderGamesExtended;
    languageCode: EnumLanguageCode;
    showTitle?: boolean;
    showDescription?: boolean;
    providerGames: IProviderGame[] | null;
    swiperLoop?: boolean;
    swiperClassName?: string;
}

const SectionGames = observer(
    ({
         slider,
         languageCode,
         showTitle,
         showDescription,
         /*providerGames,*/
         swiperLoop,
         swiperClassName
     }: ISectionGamesProps) => {
        const modalStore = useContext<IGamesModalStore>(GamesModalStore);
        const { appStore } = useContext(MainAppContext);
        // const popoverStore = useContext<IGamesPopoverStore>(GamesPopoverStore);
        if (!Array.isArray(slider.games) || slider.games.length === 0) {
            return null;
        }
        // if (slider.games.length < 10) {
        //     slider.games = slider.games.concat(slider.games);
        // }


        const scrolledRef = useRef(false);
        try {
            const hash = location.hash;

            useEffect(() => {
                if (hash && !scrolledRef.current && typeof document !== "undefined") {
                    const id = hash.replace('#', '');
                    const element = document.getElementById(id);
                    if (element) {
                        scrolledRef.current = true;
                        setTimeout(() => {
                            const headerOffset = 90;
                            const elementPosition = element.getBoundingClientRect().top;
                            const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                            window.scrollTo({
                                top: offsetPosition,
                                behavior: "smooth"
                            });
                        }, 2000)
                    }
                }
            });
        } catch (e) {
            //best effort
        }

        swiperLoop = swiperLoop || false;

        const title = SanityApi.getLocale(slider.sectionName, languageCode);

        let games = slider.games;

        if (slider.sortGames === EnumSliderSortGames.reverse) {
            games = [...slider.games].reverse();
        } else if (slider.sortGames === EnumSliderSortGames.asc || slider.sortGames === EnumSliderSortGames.desc) {
            games = [...slider.games].sort((ga, gb) => {
                const titleGA = SanityApi.getLocale(ga.gameName, languageCode);
                const titleGB = SanityApi.getLocale(gb.gameName, languageCode);
                if (slider.sortGames === EnumSliderSortGames.asc) {
                    return titleGA.localeCompare(titleGB);
                }
                return titleGB.localeCompare(titleGA);
            });
        }

        return (
            <section id={title.replaceAll(" ", "-")} className="swiper-section container">
                <h2 className="swiper-section-title">
                    <div
                        className="swiper-section-title-link"
                        onClick={() => {
                            modalStore?.openExpandAllModal(title, slider.sectionLogoUrl || null, slider.games);
                        }}
                    >
                        {slider.isVisibleSectionLogo && slider.sectionLogoUrl &&
                            <AppImg
                                className="swiper-section-title-flag-icon"
                                src={slider.sectionLogoUrl}
                                alt={title}
                                title={title}
                            />}

                        <div className="swiper-section-title-text">{title}</div>
                        <div className="swiper-section-title-expand-all">Explore All</div>
                        <div className="swiper-section-title-expand-arrow" />
                    </div>
                </h2>
                <Swiper
                    modules={[Navigation]}
                    slidesPerView={isMobile ? 6.3 : 6.1}
                    slidesPerGroup={isMobile ? 1 : 6}
                    freeMode={true}
                    cssMode={isMobile}
                    loop={swiperLoop}
                    // loop={swiperLoop}
                    // loopFillGroupWithBlank={false}
                    // loopedSlides={12}
                    // loopPreventsSlide={true}
                    // loopAdditionalSlides={12}
                    // mousewheel={true}
                    className={"swiper-container swiper-games " + (swiperClassName || "")}
                    navigation={{
                        nextEl: ".swiper-button-next",
                        prevEl: ".swiper-button-prev",
                        disabledClass: "swiper-button-disabled"
                    }}
                    // onInit={(swiper) => {
                    //     swiper.slideTo(0, 0, false);
                    // }}
                    breakpoints={{
                        0: {
                            slidesPerView: isMobile ? 3.3 : 3.1,
                            slidesPerGroup: isMobile ? 1 : 3
                        },
                        576: {
                            slidesPerView: isMobile ? 4.3 : 4.1,
                            slidesPerGroup: isMobile ? 1 : 4
                        },
                        768: {
                            slidesPerView: isMobile ? 5.3 : 5.1,
                            slidesPerGroup: isMobile ? 1 : 5
                        },
                        976: {
                            slidesPerView: isMobile ? 6.3 : 6.1,
                            slidesPerGroup: isMobile ? 1 : 6
                        }
                        // 1280: {
                        //     slidesPerView: 7.2,
                        //     slidesPerGroup: 7
                        // },
                    }}
                >
                    {games.map((game, index) => {
                        const gameName = SanityApi.getLocale(game.gameName, languageCode);
                        const gameDescription = SanityApi.getLocale(game.siteDescription, languageCode);
                        const gameCode = Game.getFirstGameCode(game);
                        // const providerGame = providerGames?.find((game) => game.gameCode === gameCode) || null;
                        return (
                            <SwiperSlide
                                className="swiper-slide"
                                data-game-code={gameCode}
                                data-game-id={game._id}
                                key={slider._id + "--" + game._id + "-" + index}
                                virtualIndex={index}
                                // onMouseOver={(e) => {
                                    // e.preventDefault();
                                    // e.stopPropagation();
                                    /*
                                                                        if (e.currentTarget.className.includes("swiper-slide-visible")) {
                                                                            console.log(1000);
                                    */
                                    // popoverStore.setPopover({
                                    //     show: true,
                                    //     game,
                                    //     target: e.currentTarget,
                                    //     layout: slider.layout as EnumSwiperGamesLayout,
                                    //     gameUrl: Game.getGamePlayUrl(
                                    //         gameCode,
                                    //         game?.paUrl,
                                    //         appConfig.systemSettings.defaultRegionCode,
                                    //         providerGame !== null
                                    //     )
                                    // });
                                    /*
                                                                        }
                                    */
                                // }}
                            >
                                <AppLink
                                    href={Project.getPageUrl(appStore, EnumPage.gameDetails, {gameId: String(gameCode)})}
                                    target={"_blank"}
                                    rel="noreferrer"
                                >
                                    <AppImg
                                        src={
                                            slider.layout === EnumSwiperGamesLayout.large || isMobile
                                            ? game.gameTowerPosterUrl
                                            : game.gamePosterUrl
                                        }
                                        alt={gameName}
                                        title={gameName}
                                        defaults={
                                            slider.layout === EnumSwiperGamesLayout.large || isMobile
                                            ? { width: 428, height: 550, title: gameName }
                                            : { width: 428, height: 268, title: gameName }
                                        }
                                    />
                                    {showTitle && <div className={"game-title"}>{gameName}</div>}
                                    {showDescription && <div className={"game-desc"}>{gameDescription}</div>}
                                </AppLink>
                            </SwiperSlide>
                        );
                    })}

                    <div className="swiper-button swiper-button-prev" />
                    <div className="swiper-button swiper-button-next" />
                </Swiper>
            </section>
        )
            ;
    }
);

export default SectionGames;
