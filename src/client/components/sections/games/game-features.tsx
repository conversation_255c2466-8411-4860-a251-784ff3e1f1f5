import React from "react";
import { observer } from "mobx-react-lite";
import { EnumLanguageCode } from "models/enum/system";

import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/scrollbar";

import { Swiper, SwiperSlide } from "swiper/react";
import { Keyboard, Mousewheel, Navigation, Pagination, Scrollbar } from "swiper";
import { SanityApi } from "utils/sanityApi";
import { IGameExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { ILocaleString } from "api/data-providers/sanity/schemas";
import AppImg from "@components/main/appImg";

const GameFeatures = observer((props: { game: IGameExtended; languageCode: EnumLanguageCode }) => {
    const { game, languageCode } = props;
    // const { authStore, appStore } = useContext(AppContext);

    // const swiperRef = React.useRef(null);

    // const router = useRouter();
    // const locale = router.locale;
    // const msg = getLocaleMessages(locale as EnumLocales).messages;

    const isFeatures = game.gameFeatures && Array.isArray(game.gameFeatures);
    const featuresLength = isFeatures ? game.gameFeatures.length : 0;

    if (featuresLength > 0) {
        return (
            <section className="section">
                <h2 className="section-title">
                    Game Features
                </h2>
                <Swiper
                    modules={[Navigation, Pagination, Mousewheel, Keyboard, Scrollbar]}
                    slidesPerView={"auto"}
                    spaceBetween={0}
                    grabCursor={true}
                    // https://github.com/nolimits4web/swiper/issues/3855
                    // ref={swiperRef}
                    // mousewheel={true}
                    keyboard={true}
                    // watchSlidesProgress={true}
                    // watchSlidesVisibility={true}
                    // scrollbar={true}
                    centeredSlides={featuresLength === 1}
                    loop={false}
                    navigation={true}
                    className={"features-swiper features-slider"}
                >
                    {game.gameFeatures.map((feature) => {
                        const fTitle = SanityApi.getLocale(feature.gameFeatureTitle as ILocaleString, languageCode);
                        const fText = SanityApi.getLocale(
                            feature.gameFeatureDescription as ILocaleString,
                            languageCode
                        );
                        if (feature.gameFeaturePosterUrl) {
                            return (
                                <SwiperSlide className={"features-slide"} key={feature._key}>
                                    <div className="features-slider-title">{fTitle}</div>
                                    {/*<a href="#0" className="features-slider-figure">*/}
                                    <div className="features-slider-figure">
                                        <AppImg
                                            src={feature.gameFeaturePosterUrl}
                                            title={fTitle}
                                            alt={fTitle}
                                            className="features-slider-figure-img"
                                        />
                                    </div>
                                    {/*</a>*/}
                                    <div className="features-slider-text">{fText}</div>
                                </SwiperSlide>
                            );
                        }
                    })}
                    {/*<div className="swiper-pagination features-slider__pagination" />*/}
                    {/*<div className="features-slider-nav">*/}
                    {/*    <div*/}
                    {/*        className="features-slider-nav-btn sw-button-prev"*/}
                    {/*    />*/}
                    {/*    <div*/}
                    {/*        className="features-slider-nav-btn sw-button-next"*/}
                    {/*    />*/}
                    {/*</div>*/}
                </Swiper>
            </section>
        );
    } else {
        return null;
    }
});

export default GameFeatures;
