import React, { useEffect, useRef, useState } from "react";
import { observer } from "mobx-react-lite";
import JackpotTicker from "@components/jackpot-ticker/jackpot-ticker";
import { IConfigProject } from "iAppConfig";
import { EnumCurrencyCode, EnumProjectCode, EnumServerEnv } from "models/enum/system";
import { IAppServerConfig } from "appServerConfig";
import { JackpotsApiFactory } from "api/data-providers/web-services/client-api";

export interface ICmpSectionJackpotTickerProps {
    appServerConfig: IAppServerConfig;
    className: string;
    title: string;
    languageCode: string;
    project: IConfigProject | null;
    gameCode?: string | null;
}

const SectionJackpotTicker = observer((
    { appServerConfig, project, className, title, gameCode }: ICmpSectionJackpotTickerProps) => {
    const [amount, setAmount] = useState<number>(0);

    const duration = 5 * 60 * 1000;

    const projectCode = (project?.code !== EnumProjectCode.website_asia && project?.code !== EnumProjectCode.website_eu) ?
                        EnumProjectCode.website_eu :
                        project?.code;
    const currencyCode = projectCode === EnumProjectCode.website_asia/* || projectCode === EnumProjectCode.website_china*/
                         ? EnumCurrencyCode.CNY
                         : EnumCurrencyCode.EUR;

    const updateJackpots = (): void => {

        const webServerDomain = appServerConfig.serverEnv === EnumServerEnv.local
                                ? "http://" + location.host
                                : "https://" + location.host;

        const jackpotsApi = JackpotsApiFactory(undefined, webServerDomain + appServerConfig.server.webServicesApi.url);

        if (gameCode) {
            jackpotsApi.jackpotControllerGetGameJackpot(projectCode, currencyCode, gameCode).then((response) => {
                const result = response.data;
                setAmount(typeof result === "object" && result?.successObj?.data?.jackpotObj?.amount
                          ? parseFloat(String(result?.successObj?.data?.jackpotObj?.amount))
                          : 0);
            });
        } else {
            jackpotsApi.jackpotControllerGetTotalJackpot(projectCode, currencyCode).then((response) => {
                const result = response.data;
                setAmount(typeof result === "object" && result?.successObj?.data?.jackpotObj?.amount
                          ? parseFloat(String(result?.successObj?.data?.jackpotObj?.amount))
                          : 0);
            });
        }
    };

    const strictModeRef = useRef<boolean>(false);
    useEffect(() => {
        if (!strictModeRef.current) {
            strictModeRef.current = true;
            setInterval(updateJackpots, duration);
            updateJackpots();
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (amount > 0
            ? <section className={className || ""}>
                {gameCode
                 ? <div className="section-title">{title}</div>
                 : <div className={"section-header"}>
                     <div className="sw-section-title">{title}</div>
                 </div>}

                <JackpotTicker
                    projectCode={project?.code as EnumProjectCode}
                    currencyCode={currencyCode}
                    amount={amount} duration={duration}
                    className={"sw-jackpot-ticker"} />
            </section>
            : null
    );
});

export default SectionJackpotTicker;
