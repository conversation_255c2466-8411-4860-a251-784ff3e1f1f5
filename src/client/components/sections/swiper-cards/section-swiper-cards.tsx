import "swiper/css";
import "swiper/css/navigation";

import React from "react";
import { Navigation } from "swiper";
import { isDesktop, isMobile } from "react-device-detect";
import { Swiper, SwiperSlide } from "swiper/react";
import AppLink from "@components/main/appLink";
import AppImg from "@components/main/appImg";
import { Button } from "react-bootstrap";

export interface ISwiperCards {
    linkUrl?: string,
    imageUrl: string
}

const SectionSwiperCards = (
    {
        className,
        sectionTitle,
        sectionButtonText,
        sectionButtonUrl,
        data
    }: {
        className?: string;
        sectionTitle?: string,
        sectionButtonUrl?: string;
        sectionButtonText?: string;
        data?: ISwiperCards[]
    }
) => {
    if (!data) {
        return null;
    }
    return (
        <section className={className}>
            {sectionTitle &&
                <div className="section-header">
                    <div className={"sw-section-title"}>{sectionTitle}</div>
                    {sectionButtonText && sectionButtonUrl &&
                        <Button href={sectionButtonUrl} variant={"dark"}
                                className={"sw-btn-white-font"}>{sectionButtonText}</Button>}
                </div>
            }
            <Swiper
                modules={[Navigation]}
                freeMode={true}
                cssMode={isMobile}
                loop={false}
                onImagesReady={(swiper) => {
                    if (isDesktop) {
                        swiper.slideTo(0, 0, false);
                    }
                }}
                {...(isDesktop ? {
                    className: "swiper-top-games",
                    slidesPerView: "auto",
                    spaceBetween: 14,
                    breakpoints: {
                        0: {
                            slidesPerView: "auto",
                            centeredSlides: true
                        },
                        576: {
                            slidesPerView: 4.2,
                            centeredSlides: false
                        },
                        976: {
                            slidesPerView: 5.2,
                            centeredSlides: false
                        },
                        1024: {
                            slidesPerView: 5,
                            centeredSlides: false
                        }
                    }
                } : {
                    className: "swiper-games",
                    slidesPerView: 3,
                    spaceBetween: 10,
                    breakpoints: {
                        0: {
                            slidesPerView: 3.2,
                            slidesPerGroup: 1
                        },
                        576: {
                            slidesPerView: 4.2,
                            slidesPerGroup: 1
                        },
                        768: {
                            slidesPerView: 5.2,
                            slidesPerGroup: 1
                        }
                    }
                })}
            >
                {data.map((v, i) => (
                    <SwiperSlide
                        className={isDesktop ? "swiper-top-games__box box-slider" : "swiper-slide"}
                        style={{ cursor: "pointer" }}
                        onClick={() => {
                            if (!v.linkUrl) {
                                return false;
                            }
                            location.assign(v.linkUrl);
                        }}
                        key={"et" + i}
                    >
                        {v.linkUrl
                         ? <AppLink href={v.linkUrl} className={"game-label-wrap box-link"}>
                             <AppImg
                                 className={"swiper-top-games__img"}
                                 src={v.imageUrl}
                             />
                         </AppLink>
                         : <AppImg
                             className={"swiper-top-games__img"}
                             src={v.imageUrl}
                         />
                        }
                    </SwiperSlide>
                ))}
            </Swiper>
        </section>
    );
};

export default SectionSwiperCards;
