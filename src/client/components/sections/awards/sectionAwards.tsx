import React, { useMemo } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination } from "swiper";

import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/scrollbar";
import AppImg from "@components/main/appImg";
import { observer } from "mobx-react-lite";

const SectionAwards = observer(() => {

    const navigationProps = useMemo(() => {
        return {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev"
        };
    }, []);

    const slidersData = useMemo(() => [
        { assesUrl: "/assets/site/images/about/compliance.png" },
        { assesUrl: "/assets/site/images/about/game-of-the-year.png" },
        { assesUrl: "/assets/site/images/about/Winner-one-to-watch-GIA.png" },
        { assesUrl: "/assets/site/images/about/EGR-innovation-in-slot-provision.png" }
    ], []);

    return (
        <section className="sw-section section-swiper-awards">
            <div className={"sw-section-title"}>Our Awards</div>
            <Swiper
                modules={[Navigation, Pagination]}
                slidesPerView={"auto"}
                spaceBetween={0}
                grabCursor={true}
                keyboard={true}
                loop={false}
                navigation={navigationProps}
                className={"swiper-container"}
            >
                {slidersData.map((slide, index) => (
                    <SwiperSlide key={"sa" + index}>
                        <AppImg src={slide.assesUrl} />
                    </SwiperSlide>
                ))}
                <div className="swiper-button swiper-button-prev" />
                <div className="swiper-button swiper-button-next" />
            </Swiper>
        </section>
    );
});

export default SectionAwards;
