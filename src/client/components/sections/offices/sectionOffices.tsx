import React, { useState } from "react";
import { EnumLanguageCode } from "models/enum/system";
import { Swiper, SwiperSlide } from "swiper/react";
import { Keyboard, Mousewheel, Navigation, Pagination, Scrollbar } from "swiper";

import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/scrollbar";
// import "swiper/css/effect-coverflow";
import { SanityApi } from "utils/sanityApi";
import { Swiper as SwiperClass } from "swiper/types";
import { IOfficeExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import AppImg from "@components/main/appImg";
import { IAppDialogImage } from "@components/dialogs/iDialogs";

const SectionOffices = ({
                            offices,
                            title,
                            languageCode
                        }: {
    offices: IOfficeExtended[] | undefined | null;
    title: string;
    languageCode: EnumLanguageCode;
}) => {
    // const { appStore } = useContext(AppContext);
    const [swiperRef, setSwiperRef] = useState<SwiperClass>();

    if (!Array.isArray(offices) || offices.length === 0) {
        return null;
    }

    const setActiveSlide = (swiper: SwiperClass) => {
        const realIndex = swiper.realIndex + 1;
        const el1 = document.querySelectorAll(".sw-offices-locations-pin");
        el1.forEach((el) => {
            el.classList.remove("active");
        });
        document.querySelector(".sw-offices-locations-pin[data-pin=\"" + realIndex + "\"]")?.classList.add("active");
    };

    const slideTo = (index: number) => {
        if (swiperRef) {
            swiperRef.slideTo(index - 1, 500);
        }
    };

    return (
        <section className="sw-section">
            <div className="section-header">
                <div className="sw-section-title">
                    {/*{offices.length} {title}*/}
                    {title}
                </div>
            </div>
            <div className="sw-offices-map">
                <AppImg
                    src="/assets/site/images/offices/map.jpg"
                    className={"sw-offices-map-img"}
                />
                {/* <div className="sw-offices-locations">
                    {offices.map((office, index) => {
                        const officeName = SanityApi.getLocale(office.officeName, languageCode);
                        if (parseInt(String(office.leftOnMap)) < 1 || parseInt(String(office.topOnMap)) < 1) {
                            return null;
                        }
                        return (
                            <div
                                key={office._id}
                                className="sw-offices-locations-pin"
                                data-location={office.code?.current?.toLowerCase()}
                                data-pin={index + 1}
                                style={{
                                    left: office.leftOnMap + "%",
                                    top: office.topOnMap + "%",
                                    cursor: "pointer",
                                }}
                                onClick={() => {
                                    slideTo(index + 1);
                                }}
                            >
                                <div className="sw-offices-locations-title">{officeName}</div>
                            </div>
                        );
                    })}
                </div>*/}
                <div className="sw-offices-locations">
                    {offices.map((office, index) => {
                        const officeName = SanityApi.getLocale(office.officeName, languageCode);
                        const countryName = SanityApi.getLocale(office.country, languageCode);
                        return (
                            <div className="sw-offices-locations-pin"
                                 key={"mi" + office._id}
                                 data-location={countryName}
                                 style={{ left: office.leftOnMap + "%", top: office.topOnMap + "%" }}
                                 onClick={() => {
                                     slideTo(index + 1);
                                 }}
                                 data-pin={index + 1}
                            >
                                <div className="sw-offices-locations-title">{officeName}</div>
                            </div>
                        );
                    })}
                </div>
            </div>
            <Swiper
                modules={[Navigation, Pagination, Mousewheel, Keyboard, Scrollbar/*, EffectCoverflow*/]}
                onSwiper={setSwiperRef}
                slidesPerView={"auto"}
                spaceBetween={12}
                // observer={true}
                // observeParents={true}
                grabCursor={true}
                keyboard={true}
                centeredSlides={true}
                loop={true}
                className={"sw-offices-slider swiper-container"}
                onInit={setActiveSlide}
                onSlideChange={setActiveSlide}
                // effect={"coverflow"}
                // coverflowEffect={{
                //     rotate: 10,
                //     stretch: 0,
                //     depth: 100,
                //     modifier: 1,
                //     slideShadows: true,
                // }}
            >
                {offices.map((office) => {
                    const officeName = SanityApi.getLocale(office.officeName, languageCode);
                    const countryName = SanityApi.getLocale(office.country, languageCode);
                    const address = SanityApi.getLocale(office.address, languageCode);
                    const officeImages: IAppDialogImage[] = office.largePhotosExt?.map((i) => {
                        return { imageUrl: i.imageUrl, title: officeName, info: address };
                    });
                    return (
                        <SwiperSlide
                            className="sw-offices-slider__slide swiper-slide"
                            key={office._id}
                            {...(officeImages?.length > 0 && {
                                /*
                                                                onClick: () => {
                                                                    appStore.openDialog({
                                                                        title: officeName,
                                                                        type: EnumDialogType.images,
                                                                        attrs: { images: officeImages } as IAppDialogImagesAttrs,
                                                                    });
                                                                },
                                                                style: { cursor: "pointer" },
                                */
                            })}
                        >
                            <div className="sw-offices-slide">
                                <AppImg src={office.officePhotoUrl} />
                                {/*<h2>{officeName}</h2>*/}
                                <div className="sw-offices-slide__address">
                                    {address}
                                    <hr />
                                    {office.phone1 && <a href={"tel:" + office.phone1}>{office.phone1}</a>}
                                    {office.phone2 && <a href={"tel:" + office.phone2}>{office.phone2}</a>}
                                </div>
                            </div>
                            <div className={"sw-offices-name"}>{countryName}</div>
                            <div className={"sw-offices-country"}>{officeName}</div>
                        </SwiperSlide>
                    );
                })}
            </Swiper>
            <div className="sw-offices-slider__pagination swiper-pagination-bullets" />
        </section>
    );
};

export default SectionOffices;
