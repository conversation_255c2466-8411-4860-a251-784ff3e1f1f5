import React from "react";
import { observer } from "mobx-react-lite";

interface IFileViewerProps {
    className: string;
    id: string;
    src: string;
    page?: number;
}

// https://www.pdftron.com/blog/react/how-to-build-a-react-pdf-viewer/
const FileViewer = observer(({ className, id, src, page }: IFileViewerProps) => {
    const tSrc = src.trim();
    const urlExt = tSrc.split(".").pop();
    const gotoPage = typeof page === "number" ? page : 0;
    const url = urlExt === "pdf"
                ? `/js/pdf-js/web/viewer.html?listenToURL="true"&file=${tSrc}#page=${gotoPage}`
                : tSrc;

    return (
        <iframe id={id} className={className} src={url} />
    );
});

export default FileViewer;
