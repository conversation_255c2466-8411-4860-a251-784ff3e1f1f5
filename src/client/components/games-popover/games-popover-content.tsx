import React, { useContext, useEffect, useRef, useState } from "react";
import { SanityApi } from "utils/sanityApi";
import { EnumLanguageCode } from "models/enum/system";
import { Game } from "utils/game";
import { getElCoordinates, getWindowSize } from "utils/dom";
import { getSwiperGamesModalEl } from "client/components/games-popover/games-popover";
import { useRouter } from "next/router";
import { EnumLocale, getLocaleMessages } from "locales/locales";
import AppImg from "@components/main/appImg";
import { observer } from "mobx-react-lite";
import { IGamesModalIframe } from "@components/games-modal/interfaces";
import { ISectionGamePopover } from "@components/games-popover/interfaces";
import GamesPopoverStore, { IGamesPopoverStore } from "@components/games-popover/games-popover-store";
import { EnumPage } from "models/enum/page";
import { MainAppContext } from "@pages/_app";
import { Project } from "utils/project";
import AppVideo from "@components/main/appVideo";

// We need to wrap component in `forwardRef` in order to gain
// access to the ref object that is assigned using the `ref` prop.
// This ref is passed as the second parameter to the function component.
// eslint-disable-next-line react/display-name
const GamesPopoverContent = observer(({ languageCode }: { languageCode: EnumLanguageCode }) => {
    const { appStore } = useContext(MainAppContext);

    const popoverStore = useContext<IGamesPopoverStore>(GamesPopoverStore);

    const [currentPopover, setCurrentPopover] = useState<ISectionGamePopover>(popoverStore.getDefaultPopover());
    // noinspection JSUnusedLocalSymbols
    const [gamesModalIframe, setGamesModalIframe] = useState<IGamesModalIframe>({
        show: false,
        title: "",
        src: null,
        isVideo: false
    });
    const [isVisible, setIsVisible] = useState<boolean>(false);

    const router = useRouter();
    const locale = router.locale;
    const msg = getLocaleMessages(locale as EnumLocale).messages;

    const timeout = 500;
    const modalMaxWidth = 420;
    const modalMaxHeight = 346;

    const getInfo = (el: HTMLElement) => {
        const modalEl = getSwiperGamesModalEl();
        const elPos = getElCoordinates(el);

        let modalWidth = modalEl.clientWidth,
            modalHeight = modalEl.clientHeight,
            modalLeft = elPos.left + el.clientWidth / 2 - modalMaxWidth / 2,
            modalTop = elPos.top + el.clientHeight / 2 - modalMaxHeight / 2,
            windowSize = getWindowSize();

        // if (window.screenTop > modalTop) {
        //     modalTop = window.screenTop + 10 + 68;
        // } else if (modalTop > window.screenTop + windowSize.height - modalHeight - 10) {
        //     modalTop = window.screenTop + windowSize.height - modalHeight - 10;
        // }

        let modalPosLeft = modalLeft - elPos.left;
        let modalPosTop = modalTop - elPos.top;

        if (modalLeft < 0) {
            modalPosLeft = 0;
        } else if (modalLeft > windowSize.width - modalWidth - 60) {
            modalPosLeft = windowSize.width - modalWidth - 60;
        }

        return {
            el,
            modalEl,
            elPos,
            modalLeft,
            modalTop,
            modalWidth,
            modalHeight,
            modalPosLeft,
            modalPosTop
        };
    };

    const hideModal = () => {
        if (isVisible && currentPopover?.show) {
            const info = getInfo(currentPopover.target as HTMLElement);

            const wrapper = info.modalEl.parentElement as HTMLElement;

            wrapper.style.width = "initial";
            wrapper.style.height = "initial";

            info.modalEl.style.opacity = "0";
            info.modalEl.style.width = parseInt(String(info.elPos.width)) + "px";
            info.modalEl.style.transform = `translateX(0px) translateY(0px)`;
            info.modalEl.style.transition = `all ${timeout}ms`;
            setTimeout(() => {
                setCurrentPopover(() => popoverStore.getDefaultPopover());
                info.modalEl.style.visibility = "hidden";
                setIsVisible(() => false);
            }, timeout);
        }
    };

    const showModal = () => {
        // let curTimeOut = timeout;
        //
        // if (currentPopover.show) {
        //     hideModal();
        //     curTimeOut += timeout;
        // }

        let el = currentPopover.target;

        if (!el) {
            return;
        }

        const info = getInfo(el as HTMLElement);

        // modal.style.display = "block";
        // modal.style.position = "absolute";

        const wrapper = info.modalEl.parentElement as HTMLElement;

        wrapper.style.width = "100%";
        wrapper.style.height = "100%";

        info.modalEl.style.width = parseInt(String(info.elPos.width)) + "px";
        info.modalEl.style.top = parseInt(String(info.elPos.top)) + "px";
        info.modalEl.style.left = parseInt(String(info.elPos.left)) + "px";
        info.modalEl.style.transformOrigin = "center center";
        info.modalEl.style.zIndex = "99";
        // modal.style.willChange = "opacity";
        setTimeout(() => {
            info.modalEl.style.width = "420px";
            info.modalEl.style.visibility = "visible";
            info.modalEl.style.opacity = "1";
            info.modalEl.style.transform = `translateX(${parseInt(String(info.modalPosLeft))}px) translateY(${parseInt(
                String(info.modalPosTop)
            )}px)`;
            info.modalEl.style.transition = `all ${timeout}ms`;

            setIsVisible(() => true);
        }, timeout);
    };

    const strictModeRef = useRef<boolean>(false);

    useEffect(() => {
        let resizeTimer: number;
        let scrollTimer: number;
        const scrollListener = () => {
            clearTimeout(scrollTimer);
            scrollTimer = window.setTimeout(function() {
                hideModal();
            }, 250);
        };
        const resizeListener = () => {
            clearTimeout(resizeTimer);
            resizeTimer = window.setTimeout(function() {
                hideModal();
            }, 250);
        };
        if (!strictModeRef.current) {
            strictModeRef.current = true;

            scrollListener();
            window.addEventListener("scroll", scrollListener);

            resizeListener();
            window.addEventListener("resize", resizeListener);
        }

        return () => {
            window.removeEventListener("scroll", scrollListener);
            window.removeEventListener("resize", resizeListener);
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // The component instance will be extended
    // with whatever you return from the callback passed
    // as the second argument
    // useImperativeHandle(
    //     ref,
    //     () => ({
    //         hideModal
    //     }),
    //     // eslint-disable-next-line react-hooks/exhaustive-deps
    //     []
    // );

    useEffect(() => {
        if (isVisible) {
            hideModal();
        }
        setTimeout(
            () => {
                setCurrentPopover(() => popoverStore.popover);
            },
            isVisible ? timeout + 100 : 0
        );
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [popoverStore.popover]);

    useEffect(() => {
        if (!isVisible && currentPopover.show) {
            showModal();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [currentPopover]);

    //     console.log(currentPopover);

    if (!currentPopover || !currentPopover.show) {
        return null;
    }

    const gameCode = Game.getFirstGameCode(currentPopover.game);
    const gameName = SanityApi.getLocale(currentPopover.game.gameName, languageCode);
    const gameInfo = Game.getGameInfoFromBI(currentPopover.game);
    const gameRTP = Game.formatRTPSingle(gameInfo?.theoretical_rtp, msg);
    const gameVolatility = Game.formatVolatility(gameInfo?.player_volatility, msg);
    const gameLayout = Game.formatLayout(gameInfo.grid, msg);

    const gamePageUrl = Project.getPageUrl(appStore, EnumPage.gameDetails, {gameId: String(gameCode)});

    const videoSrc = Project.replaceSanityCdn(currentPopover.game.gameVideo?.videoUrl as string)
    const playerRef = useRef<HTMLVideoElement>(null);

    return (
        <div className="swiper-modal-container" onMouseLeave={() => hideModal()}>
            <div className="swiper-modal-figure">
                {(currentPopover.game.gameVideo?.videoUrl && (
                    <AppVideo videoUrl={videoSrc} videoRef={playerRef} autoPlay className={""} />
                )) || <AppImg src={currentPopover.game.gamePosterUrl} alt={gameName} title={gameName} />}
            </div>
            <div className="swiper-modal-content">
                <div className="swiper-modal-actions-list">
                    {currentPopover.game.gameVideo?.videoUrl && (
                        <a
                            href={currentPopover.game.gameVideo?.videoUrl}
                            className="swiper-modal-actions-list-item"
                            onClick={(e) => {
                                e.stopPropagation();
                                e.preventDefault();
                                setGamesModalIframe({
                                    show: true,
                                    title: gameName,
                                    src: e.currentTarget?.href,
                                    isVideo: true
                                });
                            }}
                        >
                            <AppImg
                                src="/assets/site/images/hover-icon-video.png"
                                alt="video"
                                className="swiper-modal-actions-list-img"
                            />
                        </a>
                    )}

                    {currentPopover.gameUrl && (
                        <a
                            href={currentPopover.gameUrl as string}
                            className="swiper-modal-actions-list-item"
                            onClick={(e) => {
                                e.stopPropagation();
                                e.preventDefault();
                                setGamesModalIframe({
                                    show: true,
                                    title: gameName,
                                    src: e.currentTarget?.href,
                                    isVideo: false
                                });
                            }}
                        >
                            <AppImg
                                src="/assets/site/images/hover-icon-play.png"
                                alt="play"
                                className="swiper-modal-actions-list-img"
                            />
                        </a>
                    )}

                    <a
                        href={gamePageUrl}
                        target="_blank"
                        className="swiper-modal-actions-list-item"
                        rel="noreferrer"
                    >
                        <AppImg
                            src="/assets/site/images/hover-icon-info.png"
                            alt="info"
                            className="swiper-modal-actions-list-img"
                        />
                    </a>
                </div>
                <div className="swiper-modal-info-list">
                    <div className="swiper-modal-info-list-item">
                        <div className="swiper-modal-info-list-label">RTP</div>
                        <div className="swiper-modal-info-list-val">{gameRTP}</div>
                    </div>
                    <div className="swiper-modal-info-list-item">
                        <div className="swiper-modal-info-list-label">GRID</div>
                        <div className="swiper-modal-info-list-val">{gameLayout}</div>
                    </div>
                    <div className="swiper-modal-info-list-item">
                        <div className="swiper-modal-info-list-label">VOLATILITY</div>
                        <div className="swiper-modal-info-list-val">{gameVolatility}</div>
                    </div>
                </div>
            </div>
        </div>
    );
});

export default GamesPopoverContent;
