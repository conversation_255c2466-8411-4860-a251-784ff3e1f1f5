import React from "react";
import { EnumLanguageCode } from "models/enum/system";
import GamesPopoverContent from "client/components/games-popover/games-popover-content";
import { observer } from "mobx-react-lite";
import GamesModalIframe from "@components/games-modal/games-modal-iframe";

export const getSwiperGamesModalEl = () => {
    return document.querySelector(".swiper-modal-mini") as HTMLDivElement;
};

const GamesPopover = observer(({ languageCode }: { languageCode: EnumLanguageCode }) => {
    // In order to gain access to the child component instance,
    // you need to assign it to a `ref`, so we call `useRef()` to get one
    //    const childRef = useRef();

    return (
        <div className={"swiper-modal-wrapper"} tabIndex={-1}>
            <div className="swiper-modal-mini">
                <GamesPopoverContent languageCode={languageCode}/* ref={childRef} */ />
                <GamesModalIframe languageCode={languageCode} />
            </div>
        </div>
    );
});

export default GamesPopover;
