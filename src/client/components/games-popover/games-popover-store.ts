import { createContext } from "react";
import { IGameExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { makeAutoObservable } from "mobx";
import { ISectionGamePopover } from "@components/games-popover/interfaces";
import { EnumSwiperGamesLayout } from "models/enum/swiper";

export interface IGamesPopoverStore {
    popover: ISectionGamePopover;

    setPopover(popover: ISectionGamePopover): void;

    getDefaultPopover(): ISectionGamePopover;

    clearPopover(): void;
}

export class GamesPopoverStore implements IGamesPopoverStore {
    constructor() {
        makeAutoObservable(this);
    }

    public popover = <ISectionGamePopover>{
        show: false,
        game: {} as IGameExtended,
        target: null,
        layout: EnumSwiperGamesLayout.standard,
        gameUrl: null
    };

    public setPopover = (popover: ISectionGamePopover): void => {
        this.popover = popover;
    };

    public getDefaultPopover = (): ISectionGamePopover => {
        return {
            show: false,
            game: {} as IGameExtended,
            target: null,
            layout: EnumSwiperGamesLayout.standard,
            gameUrl: null
        };
    };

    public clearPopover = (): void => {
        this.popover = this.getDefaultPopover();
    };

}

export default createContext(new GamesPopoverStore());
