import React, { useState } from "react";
import { observer } from "mobx-react-lite";
import AppLink from "@components/main/appLink";
import FileViewer from "@components/file-viewer/file-viewer";

const SupportDocs = observer(() => {
    const defaultDoc = "/sw-docs/Skywind-Seamless-Integration-Guide_EU-V.4.84.pdf";
    const seamlessAsiaDoc = "/sw-docs/Skywind-Seamless-Integration-Guide-V.4.84.pdf";
    const [iframeSrc, setIframeSrc] = useState<string>(defaultDoc);
    const [page, setPage] = useState<number>(0);

    const [activeEl, setActiveEl] = useState<string>("seamless-eu");

    // const loadAvailableDocumentation = async () => {};
    //
    // useEffect(() => {
    //     (async () => {
    //         await loadAvailableDocumentation();
    //     })();
    // }, []);

    const elements = [
        {
            url: defaultDoc,
            id: "seamless-eu",
            title: "Seamless EU Environment",
        },
        {
            url: seamlessAsiaDoc,
            id: "seamless-asia",
            title: "Seamless APAC Environment",
        },
        {
            url: "/sw-docs/Skywind-Live-Lobby_Integration-1.2.pdf",
            id: "live-lobby-integration",
            title: "Live Games Integration guide",
        },
        {
            url: "/sw-docs/Skywind-Unified-Back-Office-V.1.1.pdf",
            id: "sw-ubo-manual",
            title: "Skywind UBO manual",
        },
        {
            url: defaultDoc,
            id: "m-t-i-eu",
            title: "Marketing Tools Integration EU",
            page: 26
        },
        {
            url: seamlessAsiaDoc,
            id: "m-t-i-asia",
            title: "Marketing Tools Integration Asia",
            page: 26
        },
    ];

    return (
        <div className={"skywind-docs"}>
            {/*<div className={"sw-toolbar"}>*/}
            {/*    <Button key={"upload"} variant="outlined-primary">*/}
            {/*        Upload PDF*/}
            {/*    </Button>*/}
            {/*    <Button key={"users"} variant="outlined-primary">*/}
            {/*        Users*/}
            {/*    </Button>*/}
            {/*    <Button key={"add-user"} variant="outlined-primary">*/}
            {/*        Add user*/}
            {/*    </Button>*/}
            {/*</div>*/}
            <div className={"sw-area"}>
                <ul className="list-group sw-list">
                    {elements.map((e) => {
                        return (
                            <li key={e.id} className={"list-group-item"}>
                                <AppLink
                                    id={e.id}
                                    href={e.url}
                                    className={activeEl === e.id ? " active" : ""}
                                    onClick={async (event) => {
                                        event.preventDefault();
                                        event.stopPropagation();
                                        const url = e.url;
                                        // if (e.id.startsWith("gitbook-")) {
                                        //     const resp = (await ClientProjectService.getGitbookApi(
                                        //         e.url
                                        //     )) as IGitBookAuthData;
                                        //     if (resp?.url) {
                                        //         // setIframeSrc(resp?.url);
                                        //         window.open(resp?.url, "_blank");
                                        //     }
                                        // } else {
                                        setIframeSrc(url);
                                        // }
                                        setActiveEl(url);
                                        setPage(e.page ? e.page: 0)
                                    }}
                                >
                                    {e.title}
                                </AppLink>
                            </li>
                        );
                    })}
                </ul>
                <FileViewer id={"sw-docs"} className={"sw-iframe"} src={iframeSrc} page={page} />
            </div>
        </div>
    );
});

export default SupportDocs;
