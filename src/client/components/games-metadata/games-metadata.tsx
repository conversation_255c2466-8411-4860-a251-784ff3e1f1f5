import { observer } from "mobx-react-lite";
import { useEffect, useRef } from "react";
import { useRouter } from "next/router";
import slugify from "slugify";
import { EnumLocale, getLocaleMessages } from "locales/locales";
import { ILocaleString } from "api/data-providers/sanity/schemas";
import { Game } from "utils/game";
import { IGameExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { EnumLanguageCode } from "models/enum/system";
import { SanityApi } from "utils/sanityApi";

const GamesMetadata = observer((props: { game: IGameExtended; languageCode: EnumLanguageCode }) => {
    const { game, languageCode } = props;
    // const { authStore, appStore } = useContext(AppContext);

    const router = useRouter();
    const locale = router.locale;
    const msg = getLocaleMessages(locale as EnumLocale).messages;

    const strictModeRef = useRef<boolean>(false);

    useEffect(() => {
        if (!strictModeRef.current) {
            strictModeRef.current = true;
            const metadataTitles = document.querySelectorAll(".metadata .metadata__item .metadata__title");
            if (metadataTitles && metadataTitles.length > 0) {
                for (let i = 0; i < metadataTitles.length; i++) {
                    metadataTitles[i].addEventListener("click", () => {
                        const pe = metadataTitles[i].parentElement;
                        if (pe) {
                            pe.classList.toggle("open");
                        }
                    });
                }
            }
        }
    }, []);

    const gameCode = Game.getFirstGameCode(game);
    const gameInfo = Game.getGameInfoFromBI(game);
    const gameName = SanityApi.getLocale(game.gameName as ILocaleString, languageCode);
    const gameRTP = Game.formatRTPSingle(gameInfo?.theoretical_rtp, msg);
    const gameVolatility = Game.formatVolatility(gameInfo?.player_volatility, msg);
    const gameWays = Game.formatWays(gameInfo?.max_number_of_lines, msg);
    const gameLayout = Game.formatLayout(gameInfo.grid, msg);
    const gameReleaseDate = Game.formatReleaseDate(gameInfo.release_date, msg, "YYYY-MM-DD", "MMM, YYYY");

    // delete gameInfo.release_date_per_market['asia'];

    return (
        <section className="metadata">
            <div className="wrap">
                <div className="container-fluid">
                    <div className="row">
                        <div className="col text-center">
                            <h2 className="title title--h2 h2a" data-text="metadata">
                                game <span className="accent">metadata</span>
                            </h2>
                        </div>
                    </div>
                    <div className="metadata__container">
                        <div className="metadata__item">
                            <div className="metadata__title">
                                General
                                <div className="metadata__title-icon" />
                            </div>
                            <div className="metadata__description">
                                <table className="metadata-table">
                                    <tbody>
                                    <tr>
                                        <td>Game Name</td>
                                        <td>{gameName}</td>
                                    </tr>
                                    {gameInfo.target_region?.trim() !== "" && (
                                        <tr>
                                            <td>Target Region</td>
                                            <td>{gameInfo.target_region}</td>
                                        </tr>
                                    )}
                                    {gameInfo.type?.trim() !== "" && (
                                        <tr>
                                            <td>Game Type</td>
                                            <td>{gameInfo.type}</td>
                                        </tr>
                                    )}
                                    <tr>
                                        <td>Ways</td>
                                        <td>{gameWays}</td>
                                    </tr>
                                    <tr>
                                        <td>Layout</td>
                                        <td>{gameLayout}</td>
                                    </tr>
                                    <tr>
                                        <td>Theoretical RTP</td>
                                        <td>{gameRTP}</td>
                                    </tr>
                                    {gameInfo.wrapper_version?.trim() !== "" && (
                                        <tr>
                                            <td>Wrapper version</td>
                                            <td>{gameInfo.wrapper_version?.trim()}</td>
                                        </tr>
                                    )}
                                    {Number(gameInfo.hit_rate) > 0 && (
                                        <tr>
                                            <td>Hit Rate</td>
                                            <td>{gameInfo.hit_rate}</td>
                                        </tr>
                                    )}
                                    <tr>
                                        <td>Player Volatility</td>
                                        <td>{gameVolatility}</td>
                                    </tr>
                                    <tr>
                                        <td>Game Code</td>
                                        <td style={{ textTransform: "lowercase" }}>{gameCode}</td>
                                    </tr>
                                    {gameInfo.progressive_jackpot?.trim() !== "" && (
                                        <tr>
                                            <td>Progressive Jackpot</td>
                                            <td>{gameInfo.progressive_jackpot?.trim()}</td>
                                        </tr>
                                    )}
                                    <tr>
                                        <td>Release Date</td>
                                        <td>{gameReleaseDate}</td>
                                    </tr>
                                    {gameInfo.branded?.trim() !== "" && (
                                        <tr>
                                            <td>Branded</td>
                                            <td>{gameInfo.branded?.trim()}</td>
                                        </tr>
                                    )}
                                    {gameInfo.supported_languages && (
                                        <tr>
                                            <td>Supported Languages</td>
                                            <td>{gameInfo.supported_languages}</td>
                                        </tr>
                                    )}
                                    {gameInfo.supported_currencies && (
                                        <tr>
                                            <td>Supported Currencies</td>
                                            <td>{gameInfo.supported_currencies}</td>
                                        </tr>
                                    )}
                                    {gameInfo.grid && (
                                        <tr>
                                            <td>Grid</td>
                                            <td>{gameInfo.grid}</td>
                                        </tr>
                                    )}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div className="metadata__item">
                            <div className="metadata__title">
                                Tech
                                <div className="metadata__title-icon" />
                            </div>
                            <div className="metadata__description">
                                <table className="metadata-table">
                                    <tbody>
                                    <tr>
                                        <td>Technology</td>
                                        <td>HTML5</td>
                                    </tr>
                                    <tr>
                                        <td>Platform</td>
                                        <td>Desktop,mobile</td>
                                    </tr>
                                    {gameInfo.supports_portrait?.trim() !== "" && (
                                        <tr>
                                            <td>Supports Portrait</td>
                                            <td>{gameInfo.supports_portrait}</td>
                                        </tr>
                                    )}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div className="metadata__item">
                            <div className="metadata__title">
                                Paytable & Limits
                                <div className="metadata__title-icon" />
                            </div>
                            <div className="metadata__description">
                                <table className="metadata-table">
                                    <tbody>
                                    {Number(gameInfo.max_exposure) > 0 && (
                                        <tr>
                                            <td>
                                                Max Exposure on a single spin w/all multipliers/bonuses activated
                                            </td>
                                            <td>{Number(gameInfo.max_exposure).toPrecision(2)} euro</td>
                                        </tr>
                                    )}
                                    <tr>
                                        <td>Max Number Of Lines</td>
                                        <td>{gameInfo.max_number_of_lines}</td>
                                    </tr>
                                    {Number(gameInfo.default_bet) > 0 && (
                                        <tr>
                                            <td>Default Bet</td>
                                            <td>{Number(gameInfo.default_bet).toPrecision(2)} euro</td>
                                        </tr>
                                    )}
                                    {Number(gameInfo.min_bet) > 0 && (
                                        <tr>
                                            <td>Min Bet</td>
                                            <td>{Number(gameInfo.min_bet).toPrecision(2)} euro</td>
                                        </tr>
                                    )}
                                    {Number(gameInfo.max_bet) > 0 && (
                                        <tr>
                                            <td>Max Bet</td>
                                            <td>{Number(gameInfo.max_bet).toPrecision(2)} euro</td>
                                        </tr>
                                    )}
                                    {Number(gameInfo.max_win_multiplier) > 0 && (
                                        <tr>
                                            <td>Max win multiplier</td>
                                            <td>{gameInfo.max_win_multiplier} x Coin bet</td>
                                        </tr>
                                    )}
                                    {Number(gameInfo.combined_feature_frequency) > 0 && (
                                        <tr>
                                            <td>Combined feature frequency</td>
                                            <td>{gameInfo.combined_feature_frequency}</td>
                                        </tr>
                                    )}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        {gameInfo.game_features && typeof gameInfo.game_features === "object" && (
                            <div className="metadata__item">
                                <div className="metadata__title">
                                    Promo Tool Support
                                    <div className="metadata__title-icon" />
                                </div>
                                <div className="metadata__description">
                                    <table className="metadata-table">
                                        <tbody>
                                        {Object.keys(gameInfo.game_features).map((key) => (
                                            <tr key={slugify(key)}>
                                                <td>{key}</td>
                                                <td>{gameInfo.game_features[key as never]}</td>
                                            </tr>
                                        ))}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        )}
                        {gameInfo.release_date_per_market && (
                            <div className="metadata__item">
                                <div className="metadata__title">
                                    Markets & Localization
                                    <div className="metadata__title-icon" />
                                </div>
                                <div className="metadata__description">
                                    <table className="metadata-table">
                                        <tbody>
                                        <tr>
                                            <td>Certified in markets</td>
                                            <td>
                                                    <span>
                                                        {Object.keys(gameInfo.release_date_per_market).join(", ")}
                                                    </span>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </section>
    );
});

export default GamesMetadata;
