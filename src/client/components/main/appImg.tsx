import React from "react";
import appConfig from "appConfig";
import { Project } from "utils/project";
import { IAppCdnImageInfo } from "@stores/_interfaces";

export interface ICmpAppImgProps {
    width: number;
    height: number;
    alt?: string;
    title?: string;
    appCdnImage?: IAppCdnImageInfo;
}

class AppImg extends React.Component<{ defaults?: ICmpAppImgProps } & React.ImgHTMLAttributes<HTMLImageElement>,
    {}> {

    render() {
        const d = this.props.defaults;
        const title = this.props?.title || this.props?.alt || "";
        const src = Project.replaceSanityCdn(this.props.src as string);

        return (
            <img
                {...this.props}
                alt={String(this.props?.alt || "")}
                title={title}
                src={
                    src ||
                    (d &&
                        `https://via.placeholder.com/${d.width}x${d.height}.png${
                            d.title ? "?text=" + d.title + ` (${d.width}x${d.height})` : ""
                        }`) ||
                    "data:image/png;base64," + appConfig.dataImage.i1x1
                }
            />
        );
    } // 428 × 550
}

export default AppImg;
