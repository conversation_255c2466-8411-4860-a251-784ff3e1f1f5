import Link from "next/link";
import React from "react";
import { LinkProps } from "next/dist/client/link";

interface IProps extends React.AnchorHTMLAttributes<HTMLAnchorElement>, LinkProps {
    // allow both static and dynamic routes
    href: string;
    children: React.ReactNode;
    prefetch?: boolean;
    onMouseEnter?: (e: any) => void;
    onTouchStart?: (e: any) => void;
    onClick?: (e: any) => void;
}

const AppLink = (props: IProps) => {
    const { href, prefetch, children, ...exProps } = props;
    if (href === "") {
        return <>{children}</>;
    }
    return (
        <Link href={String(href)} passHref prefetch={prefetch || false} {...(exProps.as && { as: exProps.as })}>
            <a {...exProps}>{children}</a>
        </Link>
    );
};

export default AppLink;
