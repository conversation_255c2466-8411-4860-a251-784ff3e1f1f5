import React, { useEffect, useRef, useState } from "react";
import { FaChevronUp } from "@react-icons/all-files/fa/FaChevronUp";

const ScrollToTheTop = () => {
    const [isVisible, setIsVisible] = useState(false);

    const toggleVisibility = () => {
        if (window.pageYOffset > 300) {
            setIsVisible(true);
        } else {
            setIsVisible(false);
        }
    };

    const scrollToTop = () => {
        window.scrollTo({
            top: 0,
            behavior: "smooth"
        });
    };

    const strictModeRef = useRef<boolean>(false);

    useEffect(() => {
        if (!strictModeRef.current) {
            strictModeRef.current = true;
            window.addEventListener("scroll", toggleVisibility);
        }
        // clean up function
        return () => {
            // remove resize listener
            window.removeEventListener("scroll", toggleVisibility);
        };
    }, []);

    return (
        <div className="app-scroll-to-top">
            {isVisible && (
                <div
                    id="back-to-top"
                    className="btn btn-light btn-lg back-to-top"
                    role="button"
                    style={{
                        position: "fixed",
                        bottom: 25,
                        right: 25
                    }}
                    onClick={scrollToTop}
                >
                    <FaChevronUp />
                </div>
            )}
        </div>
    );
};

export default ScrollToTheTop;
