import * as React from "react";

import classes from "./swLogo.module.css";

export class SwLogoName extends React.Component<{ className: any }> {
    render() {
        let { className } = this.props;
        return (
            <svg
                version="1.1"
                id="logo"
                xmlns="http://www.w3.org/2000/svg"
                x="0px"
                y="0px"
                viewBox="0 0 100 22"
                className={classes.stB + " " + className}
                xmlSpace="preserve"
            >
                <g id="logo_1_">
                    <path
                        className={classes.st6}
                        d="M56.8,15.5h2.8c0.1,0,0.1,0,0.1,0h0.2c0.7,0,1.1,0.1,1.2,0.4c0.2,0.3,0.2,0.6,0.2,1.1c0,0.7-0.2,1.1-0.5,1.2
c-0.4,0.1-0.9,0.2-1.7,0.2h-2.4L56.8,15.5L56.8,15.5z M56.8,21.9v-2.8h2.6c0.1,0,0.1,0,0.2,0s0.1,0,0.2,0h0c0.4,0,0.7,0,1.1-0.1
s0.7-0.2,1-0.4c0.2-0.1,0.3-0.3,0.4-0.5c0.1-0.2,0.1-0.4,0.2-0.6c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2,0-0.3c0-0.1,0-0.1,0-0.2
c0-0.1,0-0.2,0-0.2c0-0.3-0.1-0.5-0.2-0.8c-0.1-0.3-0.3-0.5-0.5-0.6c-0.2-0.1-0.4-0.2-0.6-0.3c-0.2-0.1-0.4-0.1-0.6-0.1
c-0.1,0-0.2,0-0.3,0h-4.4v7.1L56.8,21.9L56.8,21.9z M54,19.8c0,0.6-0.2,1.1-0.5,1.2c-0.3,0.2-0.9,0.3-1.6,0.3h-1
c-0.7,0-1.3-0.1-1.5-0.3c-0.3-0.2-0.4-0.6-0.4-1.2v-5h-1v5c0,1,0.3,1.6,0.9,1.9c0.6,0.2,1.5,0.3,2.6,0.3c0.2,0,0.3,0,0.5,0
s0.3,0,0.5,0c1,0,1.7-0.2,2-0.5c0.3-0.3,0.5-0.9,0.5-1.6v-5h-1L54,19.8L54,19.8z M41.3,17.2c0-0.8,0.2-1.3,0.4-1.4
c0.3-0.2,0.8-0.3,1.6-0.3h0.3c1,0,1.6,0.1,2,0.2c0.4,0.2,0.6,0.7,0.6,1.5v0.6v0.6c0,0.2,0,0.4,0,0.6c0,0.2,0,0.4,0,0.6
c0,0.9-0.2,1.4-0.7,1.5c-0.4,0.2-1.1,0.3-2.1,0.3c-0.9,0-1.5-0.1-1.7-0.3s-0.4-0.7-0.4-1.4l0-1.1L41.3,17.2z M40.3,19.2
c0,0.2,0,0.5,0,0.7c0.1,0.9,0.4,1.5,0.9,1.7c0.5,0.3,1.3,0.4,2.3,0.4h0.2c0.1,0,0.1,0,0.2,0h0.1H44h0.1h0.1c1.1,0,1.9-0.2,2.3-0.5
c0.4-0.3,0.6-1,0.7-1.8c0-0.4,0-0.8,0-1.3v-1.3c0-1.1-0.3-1.8-0.9-2c-0.6-0.2-1.5-0.4-2.7-0.4h-0.4c-1.4,0-2.3,0.3-2.5,0.9
c-0.2,0.6-0.4,1.4-0.4,2.4v0.4L40.3,19.2L40.3,19.2z M33.7,15.5h3.2c0.7,0,1.2,0.1,1.3,0.4c0.2,0.3,0.2,0.6,0.2,1.2
c0,0.5-0.2,0.9-0.5,1.1c-0.3,0.2-0.8,0.3-1.5,0.3h-2.9L33.7,15.5L33.7,15.5z M33.7,21.9v-2.9h3c0.6,0,1,0.1,1.3,0.3
c0.3,0.2,0.4,0.5,0.4,1v1.6h1v-1.8c0-0.4-0.1-0.7-0.3-1c-0.2-0.2-0.6-0.4-1.1-0.4c0.8-0.1,1.2-0.3,1.4-0.7s0.2-0.7,0.2-1.2
c0-0.8-0.2-1.3-0.6-1.6c-0.4-0.3-1.1-0.5-2-0.5h-4.2v7.1L33.7,21.9L33.7,21.9z M28.1,19.1H31v0.6c0,0.7-0.1,1.1-0.4,1.3
c-0.3,0.2-0.8,0.3-1.7,0.3h-0.5H28c-0.1,0-0.2,0-0.4,0c-0.1,0-0.2,0-0.4,0c-0.1,0-0.2,0-0.4,0c-0.6-0.1-1-0.3-1.1-0.7
c-0.1-0.4-0.2-0.8-0.2-1.2l0-1l0-0.9c0-0.8,0.2-1.4,0.4-1.6c0.2-0.2,0.8-0.3,1.8-0.3c0.3,0,0.6,0,0.9,0s0.6,0,0.9,0
c0.6,0,1,0.2,1.1,0.4c0.1,0.2,0.2,0.6,0.2,1h1v-0.3c0-0.6-0.2-1-0.5-1.3c-0.3-0.3-0.8-0.5-1.6-0.5c-0.2,0-0.4,0-0.5,0
c-0.2,0-0.4,0-0.5,0c-0.2,0-0.4,0-0.5,0h-0.5c-1.2,0-2,0.2-2.4,0.6c-0.4,0.4-0.7,1.1-0.7,2v0.5v0.5c0,0.2,0,0.3,0,0.5v0.5
c0,1.1,0.3,1.8,0.8,2.1c0.5,0.3,1.5,0.5,2.9,0.5c0.3,0,0.6,0,0.9,0c0.3,0,0.6,0,0.9-0.1c0.8-0.1,1.3-0.3,1.5-0.8
c0.2-0.4,0.3-0.9,0.3-1.5v-0.5v-0.4c0-0.1,0-0.3,0-0.4h-3.8L28.1,19.1L28.1,19.1z"
                    />
                    <path
                        className={classes.st6}
                        d="M91.9,1.7h4.3c0.8,0,1.5,0.1,2,0.3c0.5,0.2,0.9,0.7,1,1.4c0.1,0.4,0.1,0.8,0.1,1.2c0,0.4,0,0.8,0,1.2v0.5
c0,0.2,0,0.3,0,0.5c0,0.2,0,0.3,0,0.5c0,0.3-0.1,0.7-0.2,1C99,8.5,98.9,8.7,98.7,9c-0.2,0.2-0.5,0.4-0.8,0.4
c-0.3,0.1-0.6,0.1-1,0.2c-0.2,0-0.3,0-0.5,0h-0.5h-4L91.9,1.7L91.9,1.7z M96.6,10.5c1.9,0,3.1-0.5,3.5-1.4c0.4-1,0.6-2,0.6-3.3v-1
c0-1.4-0.3-2.4-0.9-3.1s-1.8-1.1-3.6-1.1h-5.7v9.9H96.6z M88,0.7v9l-6.9-9h-2.2v9.9h1.3V1.6l6.9,8.9h2.2V0.7L88,0.7L88,0.7z
M76.3,10.5h1.4V0.7h-1.4V10.5z M73.9,0.7l-3.5,9l-3.3-9h-2l-3.3,9l-3.4-9h-1.4l3.8,9.9h2.1l3.3-8.9l3.2,8.9h2.1l3.9-9.9H73.9z
M54.7,0.7l-3.7,4.6l-3.7-4.6h-1.6l4.5,5.7v4.1h1.4V6.4l4.7-5.7H54.7z M35.1,0.7v9.9h1.4V6h1.2l5.6,4.6h1.9l-6.3-5.1l5.6-4.8h-1.8
l-5,4.3h-1.2V0.7H35.1z M33.8,3.3c0-1.3-0.4-2-1.3-2.3c-0.8-0.2-1.9-0.4-3.2-0.4H29c-1.3,0-2.3,0.2-3.1,0.4s-1.1,1-1.1,2.2
c0,1.3,0.5,2.1,1.4,2.3c1,0.3,2.1,0.4,3.4,0.4c0.2,0,0.4,0,0.6,0c0.2,0,0.4,0,0.6,0c0.8,0,1.3,0.2,1.6,0.4c0.2,0.2,0.4,0.6,0.4,1.3
c0,0.8-0.2,1.4-0.6,1.5c-0.4,0.2-1.1,0.3-2.1,0.3h-1.2c-1.1,0-1.9-0.1-2.2-0.4c-0.3-0.3-0.5-0.9-0.5-1.8h-1.3c0,0,0,0.1,0,0.1
c0,0,0,0.1,0,0.1c0,0,0,0.1,0,0.1v0.1c0,1.2,0.4,2,1.3,2.3c0.8,0.3,1.9,0.4,3.2,0.4h0.6c0.2,0,0.4,0,0.6,0c0.2,0,0.4,0,0.6,0
c0.2,0,0.4,0,0.6,0c1.1-0.1,1.8-0.4,2.1-0.9C34,9,34.1,8.4,34.1,7.6c0-1.3-0.4-2.1-1.3-2.3C31.9,5.1,30.9,5,29.7,5
c-0.3,0-0.5,0-0.8,0c-0.3,0-0.5,0-0.8,0c-0.8,0-1.3-0.2-1.5-0.5c-0.2-0.3-0.3-0.7-0.3-1.2c0-0.1,0-0.1,0-0.2s0-0.2,0-0.2
c0-0.2,0.1-0.3,0.2-0.5c0.1-0.2,0.2-0.3,0.4-0.4c0.2-0.1,0.4-0.1,0.6-0.2c0.2,0,0.5-0.1,0.7-0.1c0.2,0,0.4,0,0.6,0
c0.2,0,0.4,0,0.5,0h0.7c1,0,1.6,0.1,1.9,0.4c0.3,0.2,0.5,0.7,0.5,1.5h1.4L33.8,3.3L33.8,3.3z"
                    />
                </g>
            </svg>
        );
    }
}
