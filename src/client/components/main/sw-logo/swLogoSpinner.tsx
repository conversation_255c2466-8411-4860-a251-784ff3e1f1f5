import * as React from "react";

import classes from "./swLogo.module.css";

export class SwLogoSpinner extends React.Component<{ className: any }> {
    render() {
        let { className } = this.props;
        return (
            <svg
                version="1.1"
                id="logo"
                xmlns="http://www.w3.org/2000/svg"
                x="0px"
                y="0px"
                viewBox="0 0 22 22"
                className={classes.stB + " " + className}
                xmlSpace="preserve"
            >
                <g id="logo_1_">
                    <path
                        className={classes.st0}
                        d="M12.1,6.2c5.2-1.5,9.3,2.3,9.6,8.2c0.8-5.3-1.2-9.3-3.5-11.2c-1.1-0.1-2.4-0.2-3.5-0.3
C14.1,3.6,12.7,5.5,12.1,6.2"
                    />
                    <path
                        className={classes.st1}
                        d="M21.1,7.6c-2.4-4.7-6.7-2.6-9-1.1c-4.8,3.2-7.3-3.8-1.6-5.6C13.9-0.2,19.9,2.8,21.1,7.6"
                    />
                    <path
                        className={classes.st2}
                        d="M14.3,14.3c-1.2,5.2-6.5,6.9-11.9,4.3c4.3,3.2,8.8,3.5,11.7,2.4c0.6-0.9,1.3-2,1.9-2.9
C15.6,17.3,14.6,15.2,14.3,14.3"
                    />
                    <path
                        className={classes.st3}
                        d="M8.7,21.4c5.4,0.2,5.6-4.6,5.4-7.2c-0.5-5.7,6.9-4.5,5.7,1.3C19.1,19,13.5,22.7,8.7,21.4"
                    />
                    <path
                        className={classes.st4}
                        d="M5.9,12.3C2,8.5,3.4,3.1,8.5,0c-5,1.9-7.7,5.5-8.3,8.5c0.4,1,1,2.1,1.5,3.1C2.6,11.8,5,12.1,5.9,12.3"
                    />
                    <path
                        className={classes.st5}
                        d="M2.8,3.8c-3,4.3,1,7.1,3.3,8.3c5.1,2.6,0.2,8.2-4.2,4.1C-0.7,13.8-0.9,7.2,2.8,3.8"
                    />
                </g>
            </svg>
        );
    }
}
