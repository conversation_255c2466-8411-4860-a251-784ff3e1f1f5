import React, { RefObject } from "react";
import VideoHlsPlayer from "@components/videos/videoHlsPlayer";
import { Video } from "utils/video";
import { Project } from "utils/project";

export interface ICmpAppVideoProps {
    videoRef: RefObject<HTMLVideoElement>;
    videoUrl: string | undefined;
    title?: string | undefined;
    className: string;
    posterUrl?: string;
    loop?: boolean;
    muted?: boolean;
    controls?: boolean;
    autoPlay?: boolean;
}

const AppVideo = ({
    videoRef, videoUrl, title, className, posterUrl, loop, muted, controls, autoPlay }: ICmpAppVideoProps) => {
    if (typeof videoUrl !== "string") {
        return null;
    }
    const videoSrc = Project.replaceSanityCdn(videoUrl as string);
    const posterSrc = Project.replaceSanityCdn(posterUrl as string);
    const isHls = Video.isUrlVideoHls(videoSrc);
    return (
        (videoUrl && !isHls && (
            <video
                className={className}
                playsInline={true}
                {...(autoPlay && { autoPlay: true })}
                {...(muted && { muted: true })}
                {...(loop && { loop: true })}
                {...(posterUrl && { poster: posterSrc })}
                {...(controls && { controls: true })}
                src={videoSrc}
                {...(title && { title })}
            />
        )) ||
        (videoSrc && isHls && (
            <VideoHlsPlayer
                {...(loop && { loop: true })}
                width="1920"
                height="auto"
                className={className}
                {...(autoPlay && { autoPlay: true })}
                {...(muted && { muted: true })}
                {...(loop && { loop: true })}
                {...(posterUrl && { poster: posterSrc })}
                {...(controls && { controls: true })}
                {...(title && { title })}
                playerRef={videoRef}
                src={videoSrc}
                playsInline={true}
            />
        )) ||
        null
    );
};

export default AppVideo;
