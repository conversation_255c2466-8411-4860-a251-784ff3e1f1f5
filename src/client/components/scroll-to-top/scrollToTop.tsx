import React, { useState, useEffect, useRef } from "react";

const ScrollToTop = () => {
    const [showTopBtn, setShowTopBtn] = useState(false);

    const strictModeRef = useRef<boolean>(false);

    useEffect(() => {
        if (!strictModeRef.current) {
            strictModeRef.current = true;
            window.addEventListener("scroll", () => {
                if (window.scrollY > 400) {
                    setShowTopBtn(true);
                } else {
                    setShowTopBtn(false);
                }
            });
        }
    }, []);

    const goToTop = () => {
        window.scrollTo({
            top: 0,
            behavior: "smooth",
        });
    };

    return (
        <div className="back-to-top">
            {" "}
            {showTopBtn && (
                <div className="back-to-top-icon" onClick={goToTop}> </div>
            )}{" "}
        </div>
    );
};
export default ScrollToTop;
