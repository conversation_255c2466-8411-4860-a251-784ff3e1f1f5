import React, { useState } from "react";
import { NavDropdown } from "react-bootstrap";
import { useRouter } from "next/router";
import { observer } from "mobx-react-lite";
import { Project } from "utils/project";
import { EnumLocale } from "locales/locales";
import AppLink from "@components/main/appLink";

export interface ICmpToolbarDropDownItemProps {
    menu: {
        title: string;
        url: string | null;
        id: string;
        dropDown: {
            title: string;
            url: string | null;
            id: string;
            onClick?: () => void
            target?: string
        }[]
    };
}

const ToolbarItemDropDown = observer(({ menu }: ICmpToolbarDropDownItemProps) => {
    const router = useRouter();
    const locale = router.locale as EnumLocale;

    const [show, setShow] = useState<boolean>(false);
    let ddTimer: NodeJS.Timeout | undefined;
    const showDropdown = () => {
        clearTimeout(ddTimer);
        setShow(true);
    };
    const hideDropdown = () => {
        ddTimer = setTimeout(() => {
            setShow(false);
        }, 1500);
    };

    return (
        <NavDropdown title={menu.title}
                     id={menu.id}
                     renderMenuOnMount={true}
                     show={show}
                     onMouseEnter={showDropdown}
                     onMouseLeave={hideDropdown}
        >
            {menu.dropDown.map(m => {
                    const isCurrentPath = Project.comparePath(locale, m.url || "#", router.asPath);
                    return <AppLink key={m.id}
                                    href={m.url || "#"}
                                    {...(m.target && { target: m.target })}
                                    onMouseEnter={showDropdown}
                                    onMouseLeave={hideDropdown}
                                    className={"dropdown-item" + (isCurrentPath ? " active" : "")}>
                        {m.title}
                    </AppLink>;
                }
            )}
        </NavDropdown>
    );
});

export default ToolbarItemDropDown;
