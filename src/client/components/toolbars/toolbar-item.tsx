import React, { ReactNode } from "react";
import { useRouter } from "next/router";
import { observer } from "mobx-react-lite";
import { Project } from "utils/project";
import { EnumLocale } from "locales/locales";
import AppLink from "@components/main/appLink";

interface ICmpToolbarItemProps {
    title: string | ReactNode;
    url: string | null;
    id: string;
    className?: string;
    target?: string;
    onClick?: (e: any) => void;
}

const ToolbarItem = observer(({ title, url, id, className, target, onClick }: ICmpToolbarItemProps) => {
    const router = useRouter();
    const locale = router.locale as EnumLocale;
    const isCurrentPath = Project.comparePath(locale, url || "#", router.asPath);

    return <AppLink key={id}
                    href={url || "#"}
                    className={"nav-link" + (className ? " " + className : "") + (isCurrentPath ? " active" : "")}
                    {...(target && { target })}
                    onClick={onClick}>
        {title}
    </AppLink>;
});

export default ToolbarItem;
