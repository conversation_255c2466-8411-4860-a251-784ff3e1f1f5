import React, { useCallback, useContext, useState } from "react";
import { Container, Nav, Navbar } from "react-bootstrap";
import appConfig from "appConfig";
import { EnumLocale, getLocaleMessages } from "locales/locales";
import { useRouter } from "next/router";
import AppImg from "@components/main/appImg";
import { MainAppContext } from "@pages/_app";
import { observer } from "mobx-react-lite";
import { FaSearch } from "@react-icons/all-files/fa/FaSearch";
import ToolbarItemDropDown from "@components/toolbars/toolbar-item-dropdown";
import ToolbarItem from "@components/toolbars/toolbar-item";
import { EnumPage } from "models/enum/page";
import { Project } from "utils/project";

const urlExternal = appConfig.client.endpoints.external;

const MainSiteToolbar = observer(() => {
    const [expanded, setExpanded] = useState<boolean>(false);

    const { appStore } = useContext(MainAppContext);
    const router = useRouter();
    const locale = router.locale as EnumLocale;

    // const [eToolsState, setEToolsState] = useState<boolean>(false);

    const msg = getLocaleMessages(locale).messages;

    const handleClickSearch = useCallback((event: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => {
        event.preventDefault();
        setExpanded(false);
        appStore.setGamesSearchFullScreen({ isVisible: true });
        return false;
    }, [appStore]);

    const menuData = [
        { id: "home", url: Project.getPageUrl(appStore, EnumPage.home), title: "Home" },
        // { id: "games", url: Project.getPageUrl(appStore, EnumPage.gamesList), title: "Skywind Games" },
        {
            id: "slot-factory-games",
            url: Project.getPageUrl(appStore, EnumPage.slotFactoryGamesList),
            title: "Slot Factory Games"
        },
        { id: "live-casino", url: Project.getPageUrl(appStore, EnumPage.liveStudio), title: "Live Casino" },
        {
            id: "nav-dropdown", url: null, title: "Engagement tools", dropDown: [
                {
                    id: "lucky-env",
                    url: Project.getPageUrl(appStore, EnumPage.toolsLuckyEnvelopes),
                    title: "Lucky envelopes"
                },
                {
                    id: "m-w-jp",
                    url: Project.getPageUrl(appStore, EnumPage.toolsMustWinJackpots),
                    title: "Must win Jackpots"
                },
                {
                    id: "tournaments",
                    url: Project.getPageUrl(appStore, EnumPage.toolsTournaments),
                    title: "Tournaments"
                },
                { id: "free-bets", url: Project.getPageUrl(appStore, EnumPage.toolsFreeBets), title: "Free bets" },
                {
                    id: "wu-shi-jackpots",
                    url: Project.getPageUrl(appStore, EnumPage.toolsWuShiJackpot),
                    title: "Wu Shi Jackpots"
                }
            ]
        },
        // { id: "sw-360", url: Project.getPageUrl(appStore, EnumPage.sw360), title: "SW 360" },
        // { id: "sports", url: Project.getPageUrl(appStore, EnumPage.sports), title: "Sports" },
        // { id: "news", url: Project.getPageUrl(appStore, EnumPage.newsList), title: "News" },
        {
            id: "careers", url: null, title: "Careers", dropDown: [
                {
                    id: "welcome-dealers",
                    url: Project.getPageUrl(appStore, EnumPage.welcomeDealers),
                    title: "Game Presenter"
                },
                {
                    id: "all-careers",
                    url: urlExternal.skywind.careers,
                    title: "All Careers",
                    target: "_blank"
                }
            ]
        },
        { id: "about-us", url: Project.getPageUrl(appStore, EnumPage.aboutUs), title: "About us" },
        { id: "contact", url: Project.getPageUrl(appStore, EnumPage.contact), title: "Contact us" },
        { id: "partner-area", url: urlExternal.skywind.partnerArea, title: "Partner area", target: "_blank" },
        {
            id: "search",
            url: Project.getPageUrl(appStore, EnumPage.home),
            title: <FaSearch />,
            onClick: handleClickSearch
        }
    ];

    return (
        <Navbar className="container header-navbar" fixed={"top"} bg="dark" variant="dark" expand="lg"
                expanded={expanded}>
            <Container>
                <Navbar.Brand className={"header-navbar-logo"} href={Project.getPageUrl(appStore, EnumPage.home)}>
                    <AppImg
                        src="/assets/site/images/skywind_white.svg"
                        className="header-navbar-logo-img"
                        alt={msg.skywindCompanyName}
                    />
                </Navbar.Brand>
                <Navbar.Toggle aria-controls="responsive-navbar-nav"
                               onClick={() => setExpanded((prevState) => !prevState)} />
                <Navbar.Collapse id="responsive-navbar-nav" className="justify-content-start">
                    <Nav className="me-auto header-navbar-list">
                        {menuData.map(m => {
                            return m.dropDown
                                   ? <ToolbarItemDropDown key={"tdd" + m.id} menu={m} />
                                   : <ToolbarItem
                                       url={m.url} id={m.id} key={"tm" + m.id} title={m.title}
                                       {...(m.target && { target: m.target })}
                                       {...(m.onClick && { onClick: m.onClick })}
                                   />;
                        })}
                    </Nav>
                </Navbar.Collapse>
            </Container>
        </Navbar>
    );
});

export default MainSiteToolbar;
