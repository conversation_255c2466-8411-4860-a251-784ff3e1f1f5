import * as React from "react";
import { useContext } from "react";

import Router, { useRouter } from "next/router";
import { CgGames } from "@react-icons/all-files/cg/CgGames";
import { CgProfile } from "@react-icons/all-files/cg/CgProfile";
import { IoLogOutOutline } from "@react-icons/all-files/io5/IoLogOutOutline";
import { Nav, Navbar } from "react-bootstrap";
import { BsNewspaper } from "@react-icons/all-files/bs/BsNewspaper";
import { ImSearch } from "@react-icons/all-files/im/ImSearch";
import Link from "next/link";
import { Project } from "utils/project";
import { AppLocales, EnumLocale, getLocaleMessages } from "locales/locales";
import { MainAppContext } from "pages/_app";
import appConfig from "appConfig";
import { observer } from "mobx-react-lite";
import { EnumUserClientAction } from "models/enum/user";
import { CgSupport } from "@react-icons/all-files/cg/CgSupport";
import { EnumDBUserGroupCodes, EnumDBUserPermissions } from "models/enum/db";
import ClientUsersService from "client/services/clientUsersService";
import { Tournaments } from "utils/tournamets";
import { SwLogo } from "@components/main/sw-logo/swLogo";
import { EnumLocalStorage } from "models/enum/local-storage";
import { EnumPage } from "models/enum/page";
import { Dropdown } from "react-bootstrap";
import { ClientApp } from "client/app/clientApp";
import { EnumProjectType } from "models/enum/system";

const MainPaToolbar = observer(() => {
    const { authStore, appStore } = useContext(MainAppContext);
    const target = React.useRef(null);

    const id = "app-main-header";

    const fromArea = "mainToolbar";

    const user = authStore.getUser();
    const userPermissions = user?.userPermissions?.permissions;

    const router = useRouter();
    const locale = router.locale as EnumLocale;

    const msg = getLocaleMessages(locale).messages;

    const isActive = (path: string) => {
        // const pathName = router.pathname.substring(0, 3) === "/pa" ? router.pathname.substring(3) : router.pathname;
        return Project.comparePath(locale, path, router.asPath) ? "active" : "";
        // const pathName = router.asPath;
        // return pathName === path || pathName.substring(0, path.length) === path ? "active" : "";
    };

    const pageRoadmapUrl = Project.getPageUrl(appStore, EnumPage.roadmapGames);
    const pageRoadmapSkywindUrl = Project.getPageUrl(appStore, EnumPage.roadmapGamesSkywind);
    const pageRoadmapSlotFactoryUrl = Project.getPageUrl(appStore, EnumPage.roadmapGamesSlotFactory);
    const pageLiveGamesUrl = Project.getPageUrl(appStore, EnumPage.liveStudio);
    const pageNewsUrl = Project.getPageUrl(appStore, EnumPage.newsList);
    const pageSearchUrl = Project.getPageUrl(appStore, EnumPage.search);
    const pageUserProfileUrl = Project.getPageUrl(appStore, EnumPage.accountUserProfile);
    const pageSupportDocsUrl = Project.getPageUrl(appStore, EnumPage.supportDocs);
    const pageTournamentLandingUrl = Project.getPageUrl(appStore, EnumPage.tournamentLanding);
    const pageLoginUrl = Project.getPageUrl(appStore, EnumPage.login);

    return (
        <>
            <Navbar className={"container"} id={id} bg={"dark"} fixed={"top"} expand="md" variant="dark" ref={target}>
                <Navbar.Brand
                    className={"toolbar-logo"}
                    href={appConfig.client.endpoints.external.skywind.mainWebsite}
                    target={"_blank"}
                >
                    <SwLogo className={""} />
                </Navbar.Brand>
                <Navbar.Toggle aria-controls="responsive-navbar-nav" />
                <Navbar.Collapse id="header-navbar-nav" className="justify-content-start header-main-toolbar">
                    <Nav className="mr-auto">
                        {userPermissions?.includes(EnumDBUserPermissions.paCanViewPageRoadmap) && (
                            <Link href={pageRoadmapUrl} passHref >
                                <Nav.Link
                                    className={"button-main " + isActive(pageRoadmapUrl)}
                                    key={"nb-roadmap"}
                                    onClick={async () => {
                                        await ClientUsersService.clientAction(
                                            EnumUserClientAction.pageRoadmap,
                                            location.pathname,
                                            {},
                                            fromArea
                                        );
                                    }}
                                >
                                    <CgGames /> {msg.headerMenuRoadMap}
                                </Nav.Link>
                            </Link>
                        )}
                        {/*{userPermissions?.includes(EnumDBUserPermissions.paCanViewPageRoadmap) && (*/}
                        {/*    <>*/}
                        {/*        <Dropdown className={"dropdown-container-roadmap"}>*/}
                        {/*            <Dropdown.Toggle className={"dropdown-toggle-roadmap "  + isActive(pageRoadmapUrl)}>*/}
                        {/*                <Link href={"#"} passHref >*/}
                        {/*                    <Nav.Link*/}
                        {/*                        className={"button-main " + isActive(pageRoadmapUrl)}*/}
                        {/*                        key={"nb-roadmap"}*/}
                        {/*                        onClick={async () => {*/}
                        {/*                            await ClientUsersService.clientAction(*/}
                        {/*                                EnumUserClientAction.pageRoadmap,*/}
                        {/*                                location.pathname,*/}
                        {/*                                {},*/}
                        {/*                                fromArea*/}
                        {/*                            );*/}
                        {/*                        }}*/}
                        {/*                    >*/}
                        {/*                        <CgGames /> {msg.headerMenuRoadMap}*/}
                        {/*                    </Nav.Link>*/}
                        {/*                </Link>*/}
                        {/*            </Dropdown.Toggle>*/}
                        {/*            <Dropdown.Menu>*/}
                        {/*                <Dropdown.Item>*/}
                        {/*                    <Link href={pageRoadmapSkywindUrl} passHref>*/}
                        {/*                        <Nav.Link*/}
                        {/*                            className={"button-main " + isActive(pageRoadmapSkywindUrl)}*/}
                        {/*                            key={"nb-roadmap"}*/}
                        {/*                            onClick={async () => {*/}
                        {/*                                await ClientUsersService.clientAction(*/}
                        {/*                                    EnumUserClientAction.pageRoadmapSkywind,*/}
                        {/*                                    location.pathname,*/}
                        {/*                                    {},*/}
                        {/*                                    fromArea*/}
                        {/*                                );*/}
                        {/*                            }}*/}
                        {/*                        >*/}
                        {/*                            <CgGames /> {msg.headerMenuRoadMapSkywind}*/}
                        {/*                        </Nav.Link>*/}
                        {/*                    </Link>*/}
                        {/*                </Dropdown.Item>*/}
                        {/*                <Dropdown.Item>*/}
                        {/*                    <Link href={pageRoadmapSlotFactoryUrl} passHref>*/}
                        {/*                        <Nav.Link*/}
                        {/*                            className={"button-main " + isActive(pageRoadmapSlotFactoryUrl)}*/}
                        {/*                            key={"nb-roadmap"}*/}
                        {/*                            onClick={async () => {*/}
                        {/*                                await ClientUsersService.clientAction(*/}
                        {/*                                    EnumUserClientAction.pageRoadmapSlotFactory,*/}
                        {/*                                    location.pathname,*/}
                        {/*                                    {},*/}
                        {/*                                    fromArea*/}
                        {/*                                );*/}
                        {/*                            }}*/}
                        {/*                        >*/}
                        {/*                            <CgGames /> {msg.headerMenuRoadMapSlotFactory}*/}
                        {/*                        </Nav.Link>*/}
                        {/*                    </Link>*/}
                        {/*                </Dropdown.Item>*/}
                        {/*                <Dropdown.Item>*/}
                        {/*                    <Link href={pageRoadmapUrl} passHref >*/}
                        {/*                        <Nav.Link*/}
                        {/*                            className={"button-main " + isActive(pageRoadmapUrl)}*/}
                        {/*                            key={"nb-roadmap"}*/}
                        {/*                            onClick={async () => {*/}
                        {/*                                await ClientUsersService.clientAction(*/}
                        {/*                                    EnumUserClientAction.pageRoadmap,*/}
                        {/*                                    location.pathname,*/}
                        {/*                                    {},*/}
                        {/*                                    fromArea*/}
                        {/*                                );*/}
                        {/*                            }}*/}
                        {/*                        >*/}
                        {/*                            <CgGames /> {msg.headerMenuAllGames}*/}
                        {/*                        </Nav.Link>*/}
                        {/*                    </Link>*/}
                        {/*                </Dropdown.Item>*/}
                        {/*            </Dropdown.Menu>*/}
                        {/*        </Dropdown>*/}
                        {/*    </>*/}
                        {/*)}*/}
                        {userPermissions?.includes(EnumDBUserPermissions.paCanViewPageLiveGames) && (
                            <Link href={pageLiveGamesUrl} passHref>
                                <Nav.Link
                                    className={"button-main " + isActive(pageLiveGamesUrl)}
                                    key={"nb-live-games"}
                                    onClick={async () => {
                                        await ClientUsersService.clientAction(
                                            EnumUserClientAction.pageLiveGames,
                                            location.pathname,
                                            {},
                                            fromArea
                                        );
                                    }}
                                >
                                    <CgGames /> {msg.headerMenuLiveGames}
                                </Nav.Link>
                            </Link>
                        )}
                        {userPermissions?.includes(EnumDBUserPermissions.paCanViewPageNews) && (
                            <Link href={pageNewsUrl} passHref>
                                <Nav.Link
                                    className={"button-main " + isActive(pageNewsUrl)}
                                    key={"nb-news"}
                                    onClick={async () => {
                                        await ClientUsersService.clientAction(
                                            EnumUserClientAction.pageNews,
                                            location.pathname,
                                            {},
                                            fromArea
                                        );
                                    }}
                                >
                                    <BsNewspaper /> {msg.headerMenuNews}
                                </Nav.Link>
                            </Link>
                        )}
                        {userPermissions?.includes(EnumDBUserPermissions.paCanViewPageSearch) && (
                            <Link href={pageSearchUrl} passHref>
                                <Nav.Link
                                    className={"button-main " + isActive(pageSearchUrl)}
                                    key={"nb-search"}
                                    onClick={async () => {
                                        await ClientUsersService.clientAction(
                                            EnumUserClientAction.pageSearch,
                                            location.pathname,
                                            {},
                                            fromArea
                                        );
                                    }}
                                >
                                    <ImSearch /> {msg.headerMenuSearch}
                                </Nav.Link>
                            </Link>
                        )}
                        {userPermissions?.includes(EnumDBUserPermissions.paCanViewPageAccount) && (
                            <Link href={pageUserProfileUrl} passHref>
                                <Nav.Link
                                    className={"button-main " + isActive(pageUserProfileUrl)}
                                    key={"nb-user-profile"}
                                    onClick={async () => {
                                        await ClientUsersService.clientAction(
                                            EnumUserClientAction.pageMyAccount,
                                            location.pathname,
                                            {},
                                            fromArea
                                        );
                                    }}
                                >
                                    <CgProfile />{" "}
                                    {!user?.firstName ||
                                     user?.firstName?.length < 1 ||
                                     !user?.lastName ||
                                     user?.lastName?.length < 1
                                     ? msg.headerMenuProfile
                                     : user?.firstName + " " + user?.lastName}
                                </Nav.Link>
                            </Link>
                        )}
                        {userPermissions?.includes(EnumDBUserPermissions.paCanViewSwDocs) && (
                            <Link href={pageSupportDocsUrl} passHref>
                                <Nav.Link
                                    className={"button-main " + isActive(pageSupportDocsUrl)}
                                    key={"nb-support-docs"}
                                    onClick={async () => {
                                        await ClientUsersService.clientAction(
                                            EnumUserClientAction.pageSupportDocs,
                                            location.pathname,
                                            {},
                                            fromArea
                                        );
                                    }}
                                >
                                    <CgSupport /> {"Integration"}
                                </Nav.Link>
                            </Link>
                        )}
                        {/*{Tournaments.isEnabled() &&
                            userPermissions?.includes(EnumDBUserPermissions.paCanViewPageTournaments) &&
                            !user?.userPermissions?.groups?.includes(EnumDBUserGroupCodes.partnerThirdParty) && (
                                <Link href={pageTournamentLandingUrl} passHref>
                                    <Nav.Link
                                        className={"button-main highlight " + isActive(pageTournamentLandingUrl)}
                                        key={"nb-tournament"}
                                        onClick={async () => {
                                            const tournaments = Tournaments.getCurrentlyUsed({});
                                            if (tournaments.length === 1) {
                                                const tournament = tournaments[0];
                                                await ClientUsersService.clientAction(
                                                    EnumUserClientAction.pageTournament,
                                                    location.pathname,
                                                    {
                                                        code: tournament.code,
                                                        gameCode: tournament.gameCode,
                                                        isActive: tournament.isActive
                                                    },
                                                    fromArea
                                                );
                                            }
                                        }}
                                    >
                                        <CgSupport /> {"Tournament"}
                                    </Nav.Link>
                                </Link>
                            )}*/}
                        {/*{userPermissions?.includes(EnumDBUserPermissions.paCanViewPageUsersList) && (
                            <Link href={Project.getPageUrl(locale, url.users.list)} passHref>
                                <Nav.Link
                                    className={"button-main " + isActive(url.users.list)}
                                    key={"nb-users-list"}
                                    onClick={async () => {
                                        await ClientUsersService.clientAction(
                                            EnumUserClientAction.pageUsersList,
                                            location.pathname,
                                            {},
                                            fromArea
                                        );
                                    }}
                                >
                                    <FiUsers /> {"Users"}
                                </Nav.Link>
                            </Link>
                        )}*/}
                        {authStore.isAuthorized && (
                            <Nav.Link
                                key={"nb-user-logout"}
                                style={{ cursor: "pointer" }}
                                className={"button-main"}
                                onClick={async () => {
                                    await ClientUsersService.clientAction(
                                        EnumUserClientAction.userLogout,
                                        location.pathname,
                                        {},
                                        fromArea
                                    );
                                    await authStore.logout().then(() => {
                                        localStorage.removeItem(EnumLocalStorage.redirectAfterLogin);
                                        Router.push(pageLoginUrl);
                                    });
                                    return false;
                                }}
                            >
                                <IoLogOutOutline /> {msg.headerMenuLogout}
                            </Nav.Link>
                        )}
                    </Nav>
                </Navbar.Collapse>
            </Navbar>
        </>
    );
});

export default MainPaToolbar;
