import React, { useContext } from "react";
import { EnumDBUserPermissions } from "models/enum/db";
import { EnumUserClientArea } from "models/enum/user";
import { MainAppContext } from "pages/_app";
import { EnumProjectType } from "models/enum/system";
import { ClientApp } from "client/app/clientApp";
import AppLink from "@components/main/appLink";
import { observer } from "mobx-react-lite";

interface IActionPlayGameProps {
    playUrl: string | null;
    gameCode: string | null;
    gameName: string;
    className?: string;
}

const ActionPlayGame = observer(( { playUrl, gameCode, gameName, className }: IActionPlayGameProps) => {
    const { authStore, appStore } = useContext(MainAppContext);

    const user = authStore.getUser();
    const userPermissions = user?.userPermissions?.permissions;

    const project = appStore.getAppProject();
    const projectType = project?.type as EnumProjectType;

    const playGameHandler = async (event: React.MouseEvent<HTMLAnchorElement, MouseEvent>) => {
        event.stopPropagation();
        event.preventDefault();
        if (playUrl) {
            await ClientApp.action.playGameInDialogIframe(
                gameCode,
                gameName,
                playUrl,
                EnumUserClientArea.gamePage,
                projectType === EnumProjectType.pa
            );
        }
    };

    return (
        (playUrl &&
            gameCode &&
            (projectType === EnumProjectType.site ||
                userPermissions?.includes(EnumDBUserPermissions.paCanPlayGames)) && (
                <AppLink href={"#"} className={className || ""} onClick={playGameHandler}>
                    Play Game
                </AppLink>
            )) ||
        null
    );
});

export default ActionPlayGame;
