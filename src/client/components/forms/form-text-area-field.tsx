import React from "react";
import { Form } from "react-bootstrap";
import { IForm<PERSON>ield } from "client/components/forms/models/form-model";
import { observer } from "mobx-react-lite";

export interface ICmpFormTextAreaFieldProps {
    fieldModel: IFormField;
    value: string | string[] | number;
    handleBlur: (...args: any) => void;
    handleChange: (e: React.ChangeEvent<any>) => void;
    isError: boolean;
    errorText: string;
}

const FormTextAreaField = observer((
    { fieldModel, value, handleBlur, handleChange, isError, errorText }: ICmpFormTextAreaFieldProps) => {

    return (
        <Form.Group className="mb-3" controlId={fieldModel.id}>
            <Form.Label>{fieldModel.label}</Form.Label>
            <Form.Control
                name={fieldModel.id}
                as="textarea"
                placeholder={fieldModel.placeholder}
                {...(fieldModel.required && { required: true })}
                type={fieldModel.type}
                value={value}
                onBlur={handleBlur}
                onChange={handleChange}
                autoComplete={"off"}
                rows={6}
                // onKeyPress={(e: React.KeyboardEvent) => {
                //     handleKeyPress(e, field);
                // }}
                isInvalid={isError}
            />
            {errorText && errorText !== "" && <Form.Control.Feedback type="invalid">{errorText}</Form.Control.Feedback>}
            {fieldModel.description && fieldModel.description !== "" && (
                <Form.Text className="text-muted">{fieldModel.description}</Form.Text>
            )}
        </Form.Group>
    );
});

export default FormTextAreaField;
