import React, { useState } from "react";
import { observer } from "mobx-react-lite";
import { IFormField } from "@components/forms/models/form-model";
import { Form } from "react-bootstrap";
import { FormCheckType } from "react-bootstrap/FormCheck";

export interface IcmpFormCheckboxFieldProps {
    fieldModel: IFormField;
    checked: boolean;
    handleChange: (e: React.ChangeEvent<any>) => void;
    isError: boolean;
    errorText: string;
}

const FormCheckboxField = observer((
    { fieldModel, checked, handleChange, isError, errorText }: IcmpFormCheckboxFieldProps) => {
    const [checkedState, setCheckedState] = useState(checked);

    const handleOnChange = (e: React.ChangeEvent<any>): void => {
        setCheckedState(e.target.checked);
        handleChange(e);
    };

    return (
        <Form.Group className="mb-3" controlId={fieldModel.id}>
            <Form.Check
                name={fieldModel.id}
                placeholder={fieldModel.placeholder}
                {...(fieldModel.required && { required: true })}
                type={fieldModel.type as FormCheckType}
                checked={checkedState}
                onChange={handleOnChange}
                // onChange={handleChange}
                // onKeyPress={(e: React.KeyboardEvent) => {
                //     handleKeyPress(e, field);
                // }}
                isInvalid={isError}
                label={fieldModel.label}
            />
            {errorText && errorText !== "" && <Form.Control.Feedback type="invalid">{errorText}</Form.Control.Feedback>}
            {fieldModel.description && fieldModel.description !== "" && (
                <Form.Text className="text-muted">{fieldModel.description}</Form.Text>
            )}
        </Form.Group>
    );
});

export default FormCheckboxField;