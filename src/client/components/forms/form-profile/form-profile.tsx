import React, { useContext } from "react";
import { observer } from "mobx-react-lite";
import { useRouter } from "next/router";
import { EnumUserClientAction } from "models/enum/user";
import { MainAppContext } from "pages/_app";
import AppForm from "@components/forms/app-form";
import { FormField } from "@components/forms/models/form-model";
import ClientUsersService from "client/services/clientUsersService";

export interface ICmpFormProfileProps { className: string; id: string; description?: string; submitButtonName: string }

const FormProfile = observer(
    ({ id, className, description, submitButtonName }: ICmpFormProfileProps) => {

        const { authStore } = useContext(MainAppContext);

        const router = useRouter();

        return (
            <AppForm
                className={className}
                id={id}
                submitButtonName={submitButtonName}
                onSubmit={async (values) => {
                    await authStore.login(values, async (/*success*/) => {
                        await ClientUsersService.clientAction(
                            EnumUserClientAction.userLogin,
                            location.pathname,
                            {}
                        ).then(async () => {
                            await router.push(authStore.getBasicEndpoint());
                        });
                    });
                }}
                description={description}
                formFields={[
                    FormField.firstName,
                    FormField.lastName,
                    FormField.email,
                    FormField.country,
                    FormField.company,
                    FormField.companyPosition,
                    FormField.login,
                ]}
            />
        );
    }
);

export default FormProfile;
