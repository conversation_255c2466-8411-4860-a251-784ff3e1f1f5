import React from "react";
import { observer } from "mobx-react-lite";
import AppForm from "@components/forms/app-form";
import { FormField } from "@components/forms/models/form-model";

export interface ICmpFormRegisterProps {
    className: string;
    id: string;
    description?: string;
    submitButtonName: string;
}

const FormRegister = observer(
    ({ id, className, description, submitButtonName }: ICmpFormRegisterProps) => {
        return (
            <AppForm
                className={className}
                id={id}
                submitButtonName={submitButtonName}
                onSubmit={
                    async (/*values*/) => {
                        // await authStore.login(values, async (success) => {
                        //     await ClientUsersService.clientAction(
                        //         EnumUserClientAction.userLogin,
                        //         location.pathname,
                        //         {}
                        //     ).then(async () => {
                        //         await router.push(authStore.getBasicEndpoint());
                        //     });
                        // });
                    }
                }
                description={description}
                formFields={[
                    FormField.firstName,
                    FormField.lastName,
                    FormField.email,
                    FormField.company,
                    FormField.companyPosition,
                    FormField.country,
                    FormField.login,
                    FormField.newPassword,
                    FormField.repeatPassword
                ]}
            />
        );
    }
);

export default FormRegister;
