import React, { useContext, useState } from "react";
import { observer } from "mobx-react-lite";
import AppForm from "@components/forms/app-form";
import { commonInputTextField, FormField } from "@components/forms/models/form-model";
import { validator } from "models/validatorSchemas";
import { event as GAEvent } from "nextjs-google-analytics";
import { IHSRWebsiteNewDealerRequestResponseDto, StorageApiFactory } from "api/data-providers/web-services/client-api";
import { EnumServerEnv } from "models/enum/system";
import { IAppServerConfig } from "appServerConfig";
import { MainAppContext } from "@pages/_app";
import { AxiosError } from "axios";

export interface IFormRegisterDealerProps {
    appServerConfig: IAppServerConfig;
    className: string;
    id: string;
    description?: string;
    submitButtonName: string;
}

export interface IFormRegisterDealerFields {
    name: string;
    email: string;
    phone: string;
    iAgreeWithPrivacy: string[],
    iConfirmIam18: string[],
}

const FormRegisterDealer = observer((
    { appServerConfig, id, className, description, submitButtonName }: IFormRegisterDealerProps) => {
    const { appStore } = useContext(MainAppContext);

    const [success, setSuccess] = useState(false);

    const handleSubmit = async (values: IFormRegisterDealerFields/*, formikHelpers: FormikHelpers<any>*/) => {

        const webServerDomain = appServerConfig.serverEnv === EnumServerEnv.local
                                ? "http://" + location.host
                                : "https://" + location.host;

        const storageApi = StorageApiFactory(undefined, webServerDomain + appServerConfig.server.webServicesApi.url);

        storageApi.storageControllerPostWebsiteNewDealerRequest(values).then((response) => {
            const result = response.data;

            if (typeof result === "object" && result?.successObj?.data && result.codesObj.responseStatusCode === 200) {
                GAEvent("welcome-dealers", {
                    category: `Registration Form website_ndr`,
                    label: "User name: " + values.name
                });
                setSuccess(true);
            }

        }).catch((response: AxiosError<IHSRWebsiteNewDealerRequestResponseDto>) => {
            const result = response.response?.data;
            if (result?.errorObj && result?.messagesObj && Array.isArray(result?.messagesObj.itemsArr) && result?.messagesObj.itemsArr.length > 0) {
                appStore.addNotificationError(JSON.parse(result?.messagesObj.itemsArr[0].message).message);
                appStore.showNotifications();
            }
        });
    };

    return (
        success ?
        <div className={"welcome-dealers-headline"}>
            <div className={"welcome-dealers-headline-title"} style={{ color: "yellow" }}>
                Thanks for applying with us!
            </div>
            <div className={"welcome-dealers-headline-text"} style={{ color: "yellow" }}>
                We will review your application and get back to you.
            </div>
        </div>
                :
        <AppForm
            className={className}
            id={id}
            submitButtonName={submitButtonName}
            onSubmit={handleSubmit}
            description={description}
            formFields={[
                {
                    ...commonInputTextField,
                    id: "name",
                    label: "Nume",
                    placeholder: "Scrie-ți numele complet",
                    validate: validator.user.name.required()
                },
                { ...FormField.email, label: "Adresa de email", placeholder: "Adresa ta de email" },
                { ...FormField.phone, label: "Număr de telefon", placeholder: "Numărul tău de telefon" },
                { ...FormField.iAgreeWithPrivacy },
                { ...FormField.iConfirmIam18, label: "Confirm că am peste 18 ani" }
            ]}
        />
    );
});

export default FormRegisterDealer;