import React from "react";
import { Button } from "react-bootstrap";
import { observer } from "mobx-react-lite";

interface ICmpFormSubmitButtonProps {
    isDisabled: boolean;
    buttonName: string;
}

const FormSubmitButton = observer(({ buttonName, isDisabled }: ICmpFormSubmitButtonProps) => {
    return (
        <Button variant="primary" type="submit" className={"btn-default btn-block w-100"} disabled={isDisabled}>
            {buttonName}
        </Button>
    );
});

export default FormSubmitButton;
