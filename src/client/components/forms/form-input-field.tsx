import React from "react";
import { Form } from "react-bootstrap";
import { IFormField } from "client/components/forms/models/form-model";
import { observer } from "mobx-react-lite";
import "react-phone-number-input/style.css";
import PhoneInput from "react-phone-number-input";
import { useField } from "formik";

export interface ICmpFormInputFieldProps {
    fieldModel: IFormField;
    value: string | string[] | number;
    handleBlur: (...args: any) => void;
    handleChange: (e: React.ChangeEvent<any>) => void;
    isError: boolean;
    errorText: string;
}



const FormInputField = observer((
    { fieldModel, value, handleBlur, handleChange, isError, errorText }: ICmpFormInputFieldProps) => {

    const [field, _, helpers] = useField(fieldModel.id);

    const isPhoneInput = fieldModel.attrs?.isPhoneInput || fieldModel.type === "tel";

    return (
        <Form.Group className="mb-3" controlId={fieldModel.id}>
            <Form.Label>{fieldModel.label}</Form.Label>
            {isPhoneInput
             ? <PhoneInput
                 {...field}
                 className={"form-control"}
                 placeholder={fieldModel.placeholder}
                 value={value as any}
                 onChange={(value) => {
                     helpers.setValue(value);
                 }}
             />
             : <Form.Control
                 name={fieldModel.id}
                 placeholder={fieldModel.placeholder}
                 {...(fieldModel.required && { required: true })}
                 type={fieldModel.type}
                 value={value}
                 onBlur={handleBlur}
                 autoComplete={"off"}
                 onChange={handleChange}
                 // onKeyPress={(e: React.KeyboardEvent) => {
                 //     handleKeyPress(e, field);
                 // }}
                 isInvalid={isError}
             />
            }
            {errorText && errorText !== "" && <Form.Control.Feedback type="invalid">{errorText}</Form.Control.Feedback>}
            {fieldModel.description && fieldModel.description !== "" && (
                <Form.Text className="text-muted">{fieldModel.description}</Form.Text>
            )}
        </Form.Group>
    );
});

export default FormInputField;
