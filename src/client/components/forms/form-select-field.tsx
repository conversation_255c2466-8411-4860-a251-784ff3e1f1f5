import React from "react";
import { Form } from "react-bootstrap";
import { IForm<PERSON>ield } from "client/components/forms/models/form-model";
import { observer } from "mobx-react-lite";

export interface ICmpFormSelectFieldProps {
    fieldModel: IFormField;
    value: string | string[] | number;
    handleBlur: (...args: any) => void;
    handleChange: (e: React.ChangeEvent<any>) => void;
    isError: boolean;
    errorText: string;
}

const FormSelectField = observer((
    { fieldModel, value, handleBlur, handleChange, isError, errorText }: ICmpFormSelectFieldProps) => {

    return (
        <Form.Group className="mb-3" controlId={fieldModel.id}>
            <Form.Label>{fieldModel.label}</Form.Label>
            <Form.Select
                name={fieldModel.id}
                placeholder={fieldModel.placeholder}
                aria-label={fieldModel.placeholder}
                value={value}
                onBlur={handleBlur}
                onChange={handleChange}
                isInvalid={isError}
                {...(fieldModel.required && { required: true })}
            >
                <option value={"..."} key={"..."}>
                    {"Please select..."}
                </option>
                {typeof fieldModel.values === "object" &&
                    Object.keys(fieldModel.values).map((key) => {
                        if (fieldModel.values?.hasOwnProperty(key)) {
                            return (
                                <option value={key} key={key}>
                                    {fieldModel.values[key]}
                                </option>
                            );
                        }
                    })}
            </Form.Select>
            {errorText && errorText !== "" && <Form.Control.Feedback type="invalid">{errorText}</Form.Control.Feedback>}
            {fieldModel.description && fieldModel.description !== "" && (
                <Form.Text className="text-muted">{fieldModel.description}</Form.Text>
            )}
        </Form.Group>
    );
});

export default FormSelectField;
