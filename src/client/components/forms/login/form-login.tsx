import React, { useContext } from "react";
import { observer } from "mobx-react-lite";
import { useRouter } from "next/router";
import { EnumUserClientAction } from "models/enum/user";
import { MainAppContext } from "pages/_app";
import AppForm from "@components/forms/app-form";
import { FormField } from "@components/forms/models/form-model";
import ClientUsersService from "client/services/clientUsersService";

export interface IFormLoginProps {
    className: string;
    id: string;
    description?: string;
    submitButtonName: string;
}

const FormLogin = observer(({ id, className, description, submitButtonName }: IFormLoginProps) => {
        const { authStore } = useContext(MainAppContext);

        const router = useRouter();

        return (
            <AppForm
                className={className}
                id={id}
                submitButtonName={submitButtonName}
                onSubmit={async (values) => {
                    await authStore.login(values, async (/*success*/) => {
                        await ClientUsersService.clientAction(
                            EnumUserClientAction.userLogin,
                            location.pathname,
                            {}
                        ).then(async () => {
                            await router.push(authStore.getBasicEndpoint());
                        });
                    });
                }}
                description={description}
                formFields={[FormField.login, FormField.password]}
            />
        );
    }
);

export default FormLogin;
