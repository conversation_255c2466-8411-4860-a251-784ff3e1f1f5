import React from "react";
import AppForm from "@components/forms/app-form";
import { FormField } from "@components/forms/models/form-model";
import { observer } from "mobx-react-lite";

export interface ICmpFormForgotPasswordProps {
    className: string;
    id: string;
    description?: string;
    submitButtonName: string;
}

const FormForgotPassword = observer((
    { id, className, description, submitButtonName }: ICmpFormForgotPasswordProps) => {

    return (
        <AppForm
            className={className}
            id={id}
            submitButtonName={submitButtonName}
            onSubmit={
                async (/*values*/) => {
                    // await authStore.login(values, async (success) => {
                    //     await ClientUsersService.clientAction(
                    //         EnumUserClientAction.userLogin,
                    //         location.pathname,
                    //         {}
                    //     ).then(async () => {
                    //         await router.push(authStore.getBasicEndpoint());
                    //     });
                    // });
                }
            }
            description={description}
            formFields={[FormField.loginOrEmail]}
        />
    );
});

export default FormForgotPassword;
