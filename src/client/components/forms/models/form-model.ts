import { validator } from "models/validatorSchemas";
import countries from "data/countries.json";
import { FormCheckType } from "react-bootstrap/FormCheck";

export interface IFormField {
    autoComplete?: string;
    description?: string;
    id: string;
    isError: boolean;
    label: string;
    placeholder: string;
    required?: boolean;
    type: string | FormCheckType;
    validate: unknown | false;
    value: string;
    values?: { [key: string]: string };
    view: EnumFieldView;
    attrs?: { isPhoneInput: boolean };
}

export enum EnumFieldView {
    checkbox = "checkbox",
    inputLine = "inputLine",
    textArea = "textArea",
    select = "select",
}

export const commonInputPasswordField = {
    autoComplete: "off",
    isError: false,
    required: true,
    view: EnumFieldView.inputLine,
    type: "password",
    value: ""
};

export const commonInputTextField = {
    autoComplete: "off",
    isError: false,
    required: true,
    type: "text",
    view: EnumFieldView.inputLine,
    value: ""
};

export const commonInputFileField = {
    autoComplete: "off",
    isError: false,
    required: true,
    type: "file",
    view: EnumFieldView.inputLine,
    value: ""
};

export const commonTextAreaField = {
    autoComplete: "off",
    isError: false,
    required: true,
    type: "text",
    view: EnumFieldView.textArea,
    value: ""
};

export const commonCheckboxField = {
    isError: false,
    required: true,
    type: "checkbox",
    view: EnumFieldView.checkbox,
    value: ""
};

const commonInputDropDownField = {
    autoComplete: "off",
    isError: false,
    required: true,
    type: "text",
    view: EnumFieldView.select,
    value: ""
};

export const FormField: { [key: string]: IFormField } = {
    firstName: {
        ...commonInputTextField,
        id: "firstName",
        label: "First Name",
        placeholder: "Enter first name",
        validate: validator.user.firstName.required()
    },
    lastName: {
        ...commonInputTextField,
        id: "lastName",
        label: "Last Name",
        placeholder: "Enter last name",
        validate: validator.user.lastName.required()
    },
    email: {
        ...commonInputTextField,
        id: "email",
        label: "Email",
        type: "email",
        placeholder: "Enter email",
        validate: validator.user.email.required()
    },
    file: {
        ...commonInputFileField,
        id: "file",
        label: "Upload File",
        placeholder: "Upload file",
        validate: false
    },
    company: {
        ...commonInputTextField,
        id: "company",
        label: "Company",
        placeholder: "Enter company name",
        validate: validator.user.company.required()
    },
    companyPosition: {
        ...commonInputTextField,
        id: "companyPosition",
        label: "Company position",
        placeholder: "Enter company position",
        validate: validator.user.companyPosition.required()
    },
    country: {
        ...commonInputDropDownField,
        id: "country",
        label: "Country",
        placeholder: "Select country",
        validate: validator.user.countryCode.required(),
        values: countries
    },
    loginOrEmail: {
        ...commonInputTextField,
        id: "loginOrEmail",
        label: "Login or email",
        placeholder: "Enter login or email",
        validate: validator.user.login.required()
    },
    login: {
        ...commonInputTextField,
        id: "login",
        label: "Login",
        placeholder: "Enter login",
        validate: validator.user.login.required()
    },
    phone: {
        ...commonInputTextField,
        type: "tel",
        id: "phone",
        label: "Phone",
        placeholder: "Enter your phone number",
        validate: validator.user.phone.required(),
        attrs: { isPhoneInput: true }
    },
    password: {
        ...commonInputPasswordField,
        id: "password",
        label: "Password",
        placeholder: "Enter password",
        validate: validator.user.currentPassword.required()
    },
    currentPassword: {
        ...commonInputPasswordField,
        id: "currentPassword",
        label: "Current Password",
        placeholder: "Enter current password",
        validate: validator.user.currentPassword.required()
    },
    newPassword: {
        ...commonInputPasswordField,
        id: "newPassword",
        label: "New Password",
        placeholder: "Enter new password",
        validate: validator.user.newPassword.required()
    },
    repeatPassword: {
        ...commonInputPasswordField,
        id: "repeatPassword",
        label: "Repeat Password",
        placeholder: "Repeat new password",
        validate: validator.user.repeatPassword.required()
    },
    messageArea: {
        ...commonTextAreaField,
        id: "message",
        label: "Message",
        placeholder: "Enter your message",
        validate: validator.contactUs.message.required()
    },
    playerCode: {
        ...commonInputTextField,
        id: "playerCode",
        label: "Player ID",
        placeholder: "Enter player id",
        validate: validator.user.playerCode.required()
    },
    iAgreeWithPrivacy: {
        ...commonCheckboxField,
        id: "iAgreeWithPrivacy",
        label: "Înțeleg și sunt de acord cu Politica de Confidențialitate",
        placeholder: "",
        validate: false
    },
    iConfirmIam18: {
        ...commonCheckboxField,
        id: "iConfirmIam18",
        label: "I confirm i am 18+",
        placeholder: "",
        validate: false
    }
};
