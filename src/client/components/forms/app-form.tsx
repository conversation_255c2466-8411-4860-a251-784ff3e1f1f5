import React, { useState } from "react";
import { Formik } from "formik";
import * as Yup from "yup";
import { observer } from "mobx-react-lite";
import { FormikHelpers } from "formik/dist/types";
import { Form } from "react-bootstrap";
import FormInput<PERSON>ield from "@components/forms/form-input-field";
import { EnumFieldView, IFormField } from "@components/forms/models/form-model";
import FormSubmitButton from "@components/forms/form-submit-button";
import FormCard from "@components/forms/form-card";
import FormSelectField from "@components/forms/form-select-field";
import FormTextAreaField from "@components/forms/form-text-area-field";
import FormCheckboxField from "@components/forms/form-checkbox-field";

interface ICmpVerticalFormProps {
    className: string;
    id: string;
    description?: string;
    submitButtonName: string;
    onSubmit: (values: any, formikHelpers: FormikHelpers<any>) => void;
    formFields: IForm<PERSON>ield[];
}

const AppForm = observer(
    ({ id, className, description, submitButtonName, onSubmit, formFields }: ICmpVerticalFormProps) => {
        const [fieldModels] = useState<IFormField[]>(formFields);

        const Schema = Yup.object().shape(
            fieldModels.reduce((o, field) => ({ ...o, ...(field.validate ? { [field.id]: field.validate } : {}) }), {})
        );

        return (
            <Formik
                initialValues={fieldModels.reduce((o, field) => ({ ...o, [field.id]: field.value }), {})}
                validationSchema={Schema}
                onSubmit={async (values, actions) => {
                    await onSubmit(values, actions);
                }}
            >
                {({ values, errors, handleChange, handleBlur, isSubmitting }) => {

                    return (
                        <FormCard className={className} id={id} description={description}>
                            {fieldModels.map((fieldModel) => {
                                const isError =
                                    errors && fieldModel.id in errors && typeof errors[fieldModel.id as never] === "string";

                                const handleChangeValue = (e: React.ChangeEvent<HTMLInputElement>) => {
                                    // if (field.onChange) {
                                    //     console.log(field.onChange(e), values)
                                    //     setFieldValue(field.id, field.onChange(e));
                                    // }
                                    handleChange(e);
                                };

                                if (fieldModel.view === EnumFieldView.inputLine) {
                                    return <FormInputField
                                        key={fieldModel.id}
                                        fieldModel={fieldModel}
                                        value={values[fieldModel.id as never]}
                                        handleBlur={handleBlur}
                                        handleChange={handleChangeValue}
                                        isError={isError}
                                        errorText={errors[fieldModel.id as never]}
                                    />;
                                } else if (fieldModel.view === EnumFieldView.select) {
                                    return <FormSelectField
                                        key={fieldModel.id}
                                        fieldModel={fieldModel}
                                        value={values[fieldModel.id as never]}
                                        handleBlur={handleBlur}
                                        handleChange={handleChangeValue}
                                        isError={isError}
                                        errorText={errors[fieldModel.id as never]}
                                    />;
                                } else if (fieldModel.view === EnumFieldView.textArea) {
                                    return <FormTextAreaField
                                        key={fieldModel.id}
                                        fieldModel={fieldModel}
                                        value={values[fieldModel.id as never]}
                                        handleBlur={handleBlur}
                                        handleChange={handleChangeValue}
                                        isError={isError}
                                        errorText={errors[fieldModel.id as never]}
                                    />;
                                } else if (fieldModel.view === EnumFieldView.checkbox) {
                                    return <FormCheckboxField
                                        key={fieldModel.id}
                                        fieldModel={fieldModel}
                                        checked={values[fieldModel.id as never]}
                                        handleChange={handleChangeValue}
                                        isError={isError}
                                        errorText={errors[fieldModel.id as never]} />;
                                }
                            })}
                            <Form.Group className="mb-3">
                                <FormSubmitButton isDisabled={isSubmitting} buttonName={submitButtonName} />
                            </Form.Group>
                        </FormCard>
                    );
                }}
            </Formik>
        );
    }
);

export default AppForm;
