import React, { useContext } from "react";
import { observer } from "mobx-react-lite";
import { FormField } from "client/components/forms/models/form-model";
import AppForm from "client/components/forms/app-form";
import { EnumUserClientAction } from "models/enum/user";
import { MainAppContext } from "pages/_app";
import ClientUsersService from "client/services/clientUsersService";

export interface ICmpFormChangeUserPasswordProps {
    className: string;
    id: string;
    description?: string;
    submitButtonName: string;
}

const FormChangeUserPassword = observer(
    ({ id, className, description, submitButtonName }: ICmpFormChangeUserPasswordProps) => {

        const { authStore, appStore } = useContext(MainAppContext);

        return (
            <AppForm
                className={className}
                id={id}
                submitButtonName={submitButtonName}
                onSubmit={(values, actions) => {
                    authStore.changePassword.call(authStore, values, async () => {
                        appStore.addNotificationSuccess("Password changed successfully");
                        appStore.showNotifications();
                        appStore.setUserPasswordChangeStatus(false);
                        actions.resetForm();
                        await ClientUsersService.clientAction(
                            EnumUserClientAction.userChangePassword,
                            location.pathname,
                            {}
                        );
                    });
                }}
                description={description}
                formFields={[FormField.currentPassword, FormField.newPassword, FormField.repeatPassword]}
            />
        );
    });

export default FormChangeUserPassword;
