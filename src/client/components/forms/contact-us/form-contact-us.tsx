import React, { useContext, useState } from "react";
import { observer } from "mobx-react-lite";
import AppForm from "client/components/forms/app-form";
import { FormField } from "client/components/forms/models/form-model";
import { IAppServerConfig } from "appServerConfig";
import { MainAppContext } from "@pages/_app";
import { EnumServerEnv } from "models/enum/system";
import {
    IHSRWebsiteContactUsResponseDto,
    StorageApiFactory
} from "api/data-providers/web-services/client-api";
import { event as GAEvent } from "nextjs-google-analytics/dist/interactions/event";
import { AxiosError } from "axios";

export interface ICmpFormContactUsProps { appServerConfig: IAppServerConfig; className: string; id: string; description?: string; submitButtonName: string }

export interface IFormContactUsFields {
    firstName: string;
    lastName: string;
    company: string;
    email: string;
    message: string;
}

const FormContactUs = observer(
    ({ appServerConfig, id, className, description, submitButtonName }: ICmpFormContactUsProps) => {
        const { appStore } = useContext(MainAppContext);

        const [success, setSuccess] = useState(false);

        const handleSubmit = async (values: IFormContactUsFields/*, formikHelpers: FormikHelpers<any>*/) => {

            const webServerDomain = appServerConfig.serverEnv === EnumServerEnv.local
                                    ? "http://" + location.host
                                    : "https://" + location.host;

            console.log(webServerDomain);
            const storageApi = StorageApiFactory(undefined, webServerDomain + appServerConfig.server.webServicesApi.url);

            storageApi.storageControllerPostWebsiteContactUs(values).then((response) => {
                const result = response.data;

                if (typeof result === "object" && result?.successObj?.data && result.codesObj.responseStatusCode === 200) {
                    GAEvent("welcome-dealers", {
                        category: `Contact Us Form website_cur`,
                        label: "First name: " + values.firstName + " Last Name: " + values.lastName
                    });
                    setSuccess(true);
                }

            }).catch((response: AxiosError<IHSRWebsiteContactUsResponseDto>)=>{
                const result = response.response?.data;
                if (result?.errorObj && result?.messagesObj && Array.isArray(result?.messagesObj.itemsArr) && result?.messagesObj.itemsArr.length > 0) {
                    appStore.addNotificationError(JSON.parse(result?.messagesObj.itemsArr[0].message).message);
                    appStore.showNotifications();
                }
            });
        };

        return (
            success ?
            <div className={"welcome-dealers-headline"}>
                <div className={"welcome-dealers-headline-title"} style={{ color: "yellow" }}>
                    We look forward to speaking with you soon!
                </div>
            </div>
                    :
            <AppForm
                className={className}
                id={id}
                submitButtonName={submitButtonName}
                onSubmit={handleSubmit}
                description={description}
                formFields={[
                    FormField.firstName,
                    FormField.lastName,
                    FormField.company,
                    FormField.email,
                    FormField.messageArea,
                ]}
            />
        );
    }
);

export default FormContactUs;
