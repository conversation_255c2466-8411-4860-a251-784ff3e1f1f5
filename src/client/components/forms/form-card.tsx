import React, { ReactNode } from "react";
import { Card } from "react-bootstrap";
import { Form } from "formik";
import { observer } from "mobx-react-lite";

interface ICmpFormCardProps {
    id: string;
    className: string;
    children: ReactNode;
    description?: string;
}

const FormCard = observer(({ id, className, children, description }: ICmpFormCardProps) => {
    return (
        <Card className={className} id={id}>
            <Card.Body>
                {description && <Card.Text className={"text-h6"}>{description}</Card.Text>}
                <Form>{children}</Form>
            </Card.Body>
        </Card>
    );
});

export default FormCard;
