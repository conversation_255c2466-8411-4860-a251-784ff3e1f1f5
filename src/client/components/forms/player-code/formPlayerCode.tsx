import React, { useEffect, useState } from "react";
import { Card } from "react-bootstrap";
import Router from "next/router";
import ClientPlayersService from "../../../services/clientPlayersService";
import ClientUsersService from "client/services/clientUsersService";
import { EnumUserClientAction } from "models/enum/user";
import { FormField } from "@components/forms/models/form-model";
import AppForm from "@components/forms/app-form";
import usePaAuth from "@hooks/use-pa-auth";
import { Tournaments } from "utils/tournamets";

const FormPlayerCode = (props: {
    className: string;
    id: string;
    description?: string;
    submitButtonName: string;
    redirectOnSuccess?: string;
}) => {
    const { id, className, description, submitButtonName, redirectOnSuccess } = props;

    const { authStore } = usePaAuth();
    const user = authStore.getUser();

    const tournament = Tournaments.getCurrentlyUsed({ filter: { isActive: true } });

    const [playerCode, setPlayerCode] = useState(user?.playerCode);
    const [isValid, setIsValid] = useState(false);
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        (async () => {
            if (tournament.length === 1) {
                const resp = await ClientPlayersService.getPlayerCode();
                setPlayerCode(resp.isValid ? resp.playerCode : null);
                setIsValid(resp.isValid);
                setIsVisible(true);
            }
        })();
    }, [tournament]);

    return (
        <Card>
            <Card.Body>
                <Card.Text className={"text-h6"} style={{ fontSize: "1em" }}>
                    {tournament.length === 1 ? (
                        <span>
                            Your player ID is your nickname and will be displayed to other users when participating in
                            promotional activities such as Tournaments & Lucky Envelopes. Your player ID should be at
                            least 6 characters long and should contain only letters, numbers, underscore and dash. The
                            use of profanity is strictly prohibited and any player ID*s that are found to be in breach
                            of this will be blocked and you will be asked to change your *player ID to one that is
                            deemed suitable.
                        </span>
                    ) : (
                        <span>
                            There are no active tournaments at this time. Player ID will be available only on active
                            tournament.
                        </span>
                    )}
                </Card.Text>
                {isValid && isVisible && <Card.Text className={"h4"}>Your player ID: {playerCode}</Card.Text>}
                {!isValid && isVisible && (
                    <AppForm
                        className={className}
                        id={id}
                        submitButtonName={submitButtonName}
                        onSubmit={async (values) => {
                            await ClientUsersService.clientAction(
                                EnumUserClientAction.setPlayerCode,
                                location.pathname,
                                { code: values.playerCode }
                            );
                            const resp = await ClientPlayersService.setPlayerCode({
                                playerCode: values.playerCode,
                            });
                            setIsValid(resp.isValid);
                            if (resp.isValid) {
                                setPlayerCode(resp.playerCode);
                                if (redirectOnSuccess) {
                                    Router.push(redirectOnSuccess);
                                }
                            }
                        }}
                        description={description}
                        formFields={[FormField.playerCode]}
                    />
                )}
            </Card.Body>
        </Card>
    );
};

export default FormPlayerCode;
