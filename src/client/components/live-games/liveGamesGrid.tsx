import React, { useEffect, useRef, useState } from "react";
import { observer } from "mobx-react-lite";
import { ILiveGamesGridProps } from "@components/live-games/interfaces";
import { IGamesGridRow } from "api/services/interfaces/iGamesService";
import ClientGamesService from "@services/clientGamesService";
import GameTable from "@components/games-table/gameTable";

const LiveGamesGrid = observer((props: ILiveGamesGridProps) => {

    const virtuoso = useRef(null);

    const [rows, setRows] = useState<IGamesGridRow[]>([]);

    const getLiveGames = async () => {
        return await ClientGamesService.getAvailableGames();
    };

    const switchToLiveGames = async () => {
        await getLiveGames().then(async (response) => {
            const games = response.games;
            games.forEach(game => game.marketsWithReleaseDate = {[game.releaseDate]: game.markets})
            setRows(
                games.filter((game) => {
                    return game.types?.includes("live");
                })
            );
        });
    };

    const strictModeRef = useRef<boolean>(false);

    useEffect(() => {
        if (!strictModeRef.current) {
            strictModeRef.current = true;
            (async () => {
                await switchToLiveGames();
            })();
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <div id={props.id} className={props.className}>
            <GameTable games={rows} fixed={true} virtuoso={virtuoso} />
        </div>
    );
});

export default LiveGamesGrid;