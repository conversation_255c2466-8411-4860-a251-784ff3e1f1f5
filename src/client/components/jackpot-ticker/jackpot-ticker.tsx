import React from "react";
import { observer } from "mobx-react-lite";
import { EnumCurrencyCode, EnumProjectCode } from "models/enum/system";
import CountUp from "react-countup";

export interface ICmpJackpotTickerProps {
    className: string;
    currencyCode: EnumCurrencyCode;
    projectCode: EnumProjectCode;
    amount: number;
    duration: number;
}

const JackpotTicker = observer(({ className, currencyCode, amount, duration }: ICmpJackpotTickerProps) => {
    let totalDelimiter = 1.05;
    let totalDecimals = 2;

    const currencySymbol = currencyCode === EnumCurrencyCode.EUR
                           ? "€"
                           : currencyCode === EnumCurrencyCode.CNY ? "¥" : "$";
    return (
        <div className={"totalJP__inner " + className || ""}>
            <div className="total text-center">
                <div
                    className="total__bignum jp-ticker-total">
                    <span className="accent jp-odometer">
                        <CountUp
                            start={amount / totalDelimiter}
                            decimals={totalDecimals}
                            duration={duration}
                            useEasing={true}
                            useGrouping={true}
                            separator=","
                            decimal="."
                            prefix={currencySymbol}
                            end={amount} />
                    </span>
                </div>
                <div className="total__desc">growing every <span
                    className="accent">moment</span>
                </div>
            </div>
        </div>
    );
});

export default JackpotTicker;
