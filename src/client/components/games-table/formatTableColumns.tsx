import moment from "moment/moment";
import slugify from "slugify";
import { Button, ButtonProps } from "react-bootstrap";
import React, { FC } from "react";
import { IGamesGridRow, IMarketCertificate } from "api/services/interfaces/iGamesService";
import { Game } from "utils/game";
import { EnumLanguageCode } from "models/enum/system";
import { EnumUserClientAction, EnumUserClientArea } from "models/enum/user";
import { ClientRequest } from "utils/clientRequest";
import AppLink from "@components/main/appLink";
import { EnumDBUserPermissions } from "models/enum/db";
import AppImg from "@components/main/appImg";
import ClientUsersService from "@services/clientUsersService";
import { EnumDialogType } from "@components/dialogs/enums";
import {
    IAppDialogCertificatesAttrs,
    IAppDialogIframeAttrs,
    IAppDialogMarketingKitAttrs,
    IAppDialogVideoAttrs
} from "@components/dialogs/iDialogs";
import { ClientApp } from "client/app/clientApp";
import AppStore from "@stores/app-store";
import AuthStore from "@stores/auth-store";
import { Project } from "utils/project";
import { EnumPage } from "models/enum/page";

const ColButton: FC<ButtonProps> = ({ children, ...props }) => {
    return (
        <Button {...props} variant="secondary" size="sm" target={"_blank"}>
            {children}
        </Button>
    );
};

export const formatReleaseDate = (releaseDate: string | undefined, format = "DD-MM-YYYY") => {
    if(format === "coming-soon") {
        return (<div className={"markets"}>{"Coming soon"}</div>);
    }
    const date = moment(releaseDate, "YYYY-MM-DD");
    return date.isValid() ? <div className={"markets"}>{date.format(format || "DD-MM-YYYY")}</div> : <div className={"markets"}>{"N/A"}</div>;
};



export const formatReleaseDates = (row: IGamesGridRow, format = "DD-MM-YYYY") => {
    const marketsWithReleaseDate = row.marketsWithReleaseDate || {};
    const releaseDates = Object.keys(marketsWithReleaseDate);
    const elements = releaseDates.map(releaseDate => {
            if(format === "coming-soon") {
                return (<span key={`makets-coming-soon-${releaseDate}`} className={"markets"}>{"Coming soon"}</span>);
            }
            const date = moment(releaseDate, "YYYY-MM-DD");
            return date.isValid() ?
                   <span key={`makets-release=date-${releaseDate}`} className={"markets"}>{date.format(format || "DD-MM-YYYY")}</span> :
                   <span key={`makets-not-available-${releaseDate}`}  className={"markets"}>{"N/A"}</span>;
        }
    );
    return releaseDates.length > 0 ? elements : ([<span key={`gameNotAvailable`}>{"N/A"}</span>]);
}

export const formatMarkets = (row: IGamesGridRow) => {
    const markets = String(row.markets).split(",");
    return row.markets && markets && Array.isArray(markets) && markets.length > 0 ? (
        <span className={"markets"}>{markets.join(",")}</span>
    ) : (
               "N/A"
           );
};

export const formatMarketsAndReleaseDate = (row: IGamesGridRow, releaseDate: string | undefined, format = "DD-MM-YYYY") => {
    const marketsWithReleaseDate = row.marketsWithReleaseDate || {};
    const releaseDates = Object.keys(marketsWithReleaseDate);
    const elements = releaseDates.map(releaseDate => {
            if(format === "coming-soon") {
                // @ts-ignore
                return (<div className={"markets"} key={`${row.marketsWithReleaseDate[releaseDate]}_${releaseDate}`}>
                    <div className={`markets_subtitle markets_subtitle_release_date`}>
                        <span>{"Coming soon"}</span></div>
                    <div className={`markets_subtitle markets_subtitle_markets`}><span>
                    {
                        // @ts-ignore
                        `${row.marketsWithReleaseDate[releaseDate]}`
                    }
                    </span></div>
                </div>)
            }
            const date = moment(releaseDate, "YYYY-MM-DD");
            // @ts-ignore
            return (<div className={"markets"} key={`${row.marketsWithReleaseDate[releaseDate]}_${releaseDate}`}>
                <div className={`markets_subtitle markets_subtitle_release_date`}>
                    <span>{`${date.format(format || "DD-MM-YYYY")}`}</span></div>
                <div className={`markets_subtitle markets_subtitle_markets`}><span>
                    {
                        // @ts-ignore
                        `${row.marketsWithReleaseDate[releaseDate]}`
                    }
                    </span></div>
            </div>)
        }
    );
    return releaseDates.length > 0 ? elements : ([<span key={`gameNotAvailable`}>{"N/A"}</span>]);
}

export const formatFeatures = (row: IGamesGridRow) => {
    const features = String(row.features).split(",");
    // <ul className="features">
    // <li key={"feature-" + feature}>{feature.trim()}</li>
    return row.features && features && Array.isArray(features) && features.length > 0
           ? features.map((feature) => (
            <p className={"p-feature"} key={"feature-" + slugify(feature)}>
                {feature.trim()}
            </p>
        ))
           : "";
    // </ul>
};

export const formatMarketingKit = (
    row: IGamesGridRow,
    appStore: typeof AppStore,
    authStore: typeof AuthStore,
    className: string = "action-col action-col-btns"
) => {
    const user = authStore.getUser();
    if (!user?.userPermissions?.permissions.includes(EnumDBUserPermissions.paCanViewMarketingKit)) {
        return null;
    }
    const downloadUrl = Game.getMarketingDownloadAllUrl(
        Game.getPlayGameCode(row.gameCode),
        row.languageCode as EnumLanguageCode
    );
    return (
        <div className={className}>
            {formatMarketingBrowseUrl(row, appStore, authStore)}
            {user?.userPermissions?.permissions.includes(EnumDBUserPermissions.paCanDownloadMarketingKit) && (
                <ColButton
                    href={downloadUrl}
                    onClick={
                        async (/*e*/) => {
                            await ClientUsersService.clientAction(
                                EnumUserClientAction.gameMarketingDownload,
                                location.pathname,
                                {
                                    gameCode: Game.getPlayGameCode(row.gameCode),
                                    title: row.gameName,
                                    url: downloadUrl
                                }
                            );
                        }
                    }
                    // onClick={async (e) => {
                    //     e.preventDefault();
                    //     e.stopPropagation();
                    //     const AppLink = e.target as HTMLAnchorElement;
                    //     const response = await axios.head(AppLink?.href);
                    //     if (response.status === 200) {
                    //         ClientRequest.redirect(AppLink?.href);
                    //     } else {
                    //         appStore.addNotificationSuccess(
                    //             `Download kit for game "${row.gameName}" not available right now. Coming soon.`
                    //         );
                    //     }
                    //     return false;
                    // }}
                >
                    Download Kit
                </ColButton>
            )}
            {formatProductSheet(row, appStore, authStore)}
            {formatCertificates(row, appStore, authStore)}
        </div>
    );
};

export const formatMarketingBrowseUrl = (row: IGamesGridRow,
                                         appStore: typeof AppStore,
                                         authStore: typeof AuthStore) => {
    const user = authStore.getUser();
    if (!user?.userPermissions?.permissions.includes(EnumDBUserPermissions.paCanViewMarketingKit)) {
        return null;
    }
    const gameCode = Game.getPlayGameCode(row.gameCode);
    return (
        <Button
            variant="secondary"
            size="sm"
            href={gameCode}
            onClick={async (event) => {
                event.stopPropagation();
                event.preventDefault();
                appStore.openDialog({
                    title: "Marketing Kit: " + row.gameName,
                    type: EnumDialogType.marketingKit,
                    attrs: { gameId: gameCode } as IAppDialogMarketingKitAttrs
                });
                await ClientUsersService.clientAction(EnumUserClientAction.gameMarketingBrowse, location.pathname, {
                    gameCode: Game.getPlayGameCode(row.gameCode),
                    title: row.gameName
                });
            }}
        >
            Browse Kit
        </Button>
    );
};

export const formatDemo = (row: IGamesGridRow, appStore: typeof AppStore, authStore: typeof AuthStore) => {
    const user = authStore.getUser();
    if (!user?.userPermissions?.permissions.includes(EnumDBUserPermissions.paCanPlayGames)) {
        return null;
    }
    if (!row.demoUrl) {
        return null;
    }

    return (
        <Button
            variant="secondary"
            size="sm"
            className={"accent"}
            href={row.demoUrl}
            onClick={async (event) => {
                event.stopPropagation();
                event.preventDefault();
                await ClientApp.action.playGameInDialogIframe(
                    Game.getPlayGameCode(row.gameCode),
                    row.gameName,
                    row.demoUrl,
                    EnumUserClientArea.gamesTable,
                    true
                );
            }}
        >
            Play Game
        </Button>
    );
};

export const formatPromoVideo = (row: IGamesGridRow, appStore: typeof AppStore, authStore: typeof AuthStore) => {
    const user = authStore.getUser();
    if (!user?.userPermissions?.permissions.includes(EnumDBUserPermissions.paCanViewPromoVideo)) {
        return null;
    }
    if (!Array.isArray(row.videos) || row.videos.length === 0) {
        return null;
    }

    const videoUrl = row.videos[0].videoUrl;
    return (
        <Button
            variant="secondary"
            size="sm"
            href={videoUrl}
            onClick={async (event) => {
                event.preventDefault();
                event.stopPropagation();
                appStore.openDialog({
                    title: "Promo Video: " + row.gameName,
                    type: EnumDialogType.video,
                    attrs: { videoUrl, videos: row.videos } as IAppDialogVideoAttrs
                });
                await ClientUsersService.clientAction(EnumUserClientAction.gamePromoVideo, location.pathname, {
                    gameCode: Game.getPlayGameCode(row.gameCode),
                    title: row.gameName,
                    url: videoUrl
                });
            }}
            target={"_blank"}
        >
            Promo Video
        </Button>
    );
};

export const formatCertificates = (row: IGamesGridRow, appStore: typeof AppStore, authStore: typeof AuthStore) => {
    const user = authStore.getUser();
    if (!user?.userPermissions?.permissions.includes(EnumDBUserPermissions.paCanViewCertificates)) {
        return null;
    }
    if (!row.certificates) {
        return null;
    }
    return (
        <Button
            variant="secondary"
            size="sm"
            href={"#"}
            onClick={async (event) => {
                event.preventDefault();
                event.stopPropagation();
                appStore.openDialog({
                    title: "Certificates: " + row.gameName,
                    type: EnumDialogType.certificates,
                    attrs: { certificates: row.certificates as IMarketCertificate[] } as IAppDialogCertificatesAttrs
                });
                await ClientUsersService.clientAction(EnumUserClientAction.gameCertificates, location.pathname, {
                    gameCode: Game.getPlayGameCode(row.gameCode),
                    title: row.gameName
                });
            }}
        >
            Certificates
        </Button>
    );
};

export const formatProductSheet = (row: IGamesGridRow, appStore: typeof AppStore, authStore: typeof AuthStore) => {
    const user = authStore.getUser();
    if (!user?.userPermissions?.permissions.includes(EnumDBUserPermissions.paCanViewProductSheet)) {
        return null;
    }
    if (!row.productSheetUrl) {
        return null;
    }
    return (
        <Button
            variant="secondary"
            size="sm"
            href={"#"}
            onClick={async (event) => {
                event.preventDefault();
                event.stopPropagation();
                appStore.openDialog({
                    title: "Product Sheet: ",
                    type: EnumDialogType.iframe,
                    attrs: { url: row.productSheetUrl } as IAppDialogIframeAttrs
                });
                await ClientUsersService.clientAction(EnumUserClientAction.gameProductSheet, location.pathname, {
                    gameCode: Game.getPlayGameCode(row.gameCode),
                    title: row.gameName,
                    url: row.productSheetUrl
                });
            }}
        >
            Product Sheet
        </Button>
    );
};

export const formatInfoSheet = (row: IGamesGridRow, appStore: typeof AppStore, authStore: typeof AuthStore) => {
    const user = authStore.getUser();
    if (!user?.userPermissions?.permissions.includes(EnumDBUserPermissions.paCanViewInfoSheet)) {
        return null;
    }
    if (!row.infoSheetUrl) {
        return null;
    }
    return (
        <Button
            variant="secondary"
            size="sm"
            href={"#"}
            onClick={async (event) => {
                event.preventDefault();
                event.stopPropagation();
                appStore.openDialog({
                    title: "Info Sheet: ",
                    type: EnumDialogType.iframe,
                    attrs: { url: row.infoSheetUrl } as IAppDialogIframeAttrs
                });
                await ClientUsersService.clientAction(EnumUserClientAction.gameInfoSheet, location.pathname, {
                    gameCode: Game.getPlayGameCode(row.gameCode),
                    title: row.gameName,
                    url: row.productSheetUrl
                });
            }}
        >
            Info Sheet
        </Button>
    );
};

export const formatGamePageUrl = (row: IGamesGridRow, appStore: typeof AppStore, authStore: typeof AuthStore) => {
    const user = authStore.getUser();
    if (!user?.userPermissions?.permissions.includes(EnumDBUserPermissions.paCanViewPageGameInfo)) {
        return null;
    }

    const gamePageUrl = Project.getPageUrl(appStore, EnumPage.gameDetails, { gameId: row.id });
    return (
        <Button
            variant="secondary"
            size="sm"
            href={gamePageUrl}
            target={"_blank"}
            onClick={async (e) => {
                e.stopPropagation();
                e.preventDefault();
                await ClientUsersService.clientAction(EnumUserClientAction.gamePage, location.pathname, {
                    gameId: row.id,
                    gameCode: Game.getPlayGameCode(row.gameCode),
                    title: row.gameName,
                    url: gamePageUrl
                });
                ClientRequest.openInNewTab(gamePageUrl);
            }}
        >
            Game Page
        </Button>
    );
};

export const formatGameLogo = (row: IGamesGridRow, appStore: typeof AppStore) => {
    const gamePageUrl = Project.getPageUrl(appStore, EnumPage.gameDetails, { gameId: row.id });

    return row.gamePoster ? (
        <AppLink
            href={gamePageUrl}
            target={"_blank"}
            rel="noreferrer"
            onClick={async (e) => {
                e.stopPropagation();
                e.preventDefault();
                await ClientUsersService.clientAction(
                    EnumUserClientAction.gamePage,
                    location.pathname,
                    {
                        gameId: row.id,
                        gameCode: Game.getPlayGameCode(row.gameCode),
                        title: row.gameName,
                        url: gamePageUrl
                    },
                    "gameLogo"
                );
                ClientRequest.openInNewTab(gamePageUrl);
            }}
        >
            <AppImg
                width={"100%"}
                src={row.gamePoster as never}
                alt={row.gameName}
                title={row.gameName}
                className={"animation-fade-in-1s"}
            />
        </AppLink>
    ) : undefined;
};

export const formatGameCode = (row: IGamesGridRow) => {
    return Game.formatGameCodes(row.gameCode, row.options.showGameCode)
        .split(" ")
        .map((v) => <span className="game-code" key={v}><span>{v}</span></span>);
};

export const formatActionColumn = (row: IGamesGridRow, appStore: typeof AppStore, authStore: typeof AuthStore) => {
    return (
        <div className={"action-col"}>
            {formatDemo(row, appStore, authStore)}
            {formatInfoSheet(row, appStore, authStore)}
            {formatPromoVideo(row, appStore, authStore)}
            {formatGamePageUrl(row, appStore, authStore)}
        </div>
    );
};

export const getBrandLogo = (gameCode: string) => {
    const brandName = gameCode.indexOf("itg_") > -1 ? "slotfactory" : "skywind";
    return (
        <img src={`/assets/site/images/${brandName}.png`} width="105px"/>
    )
}
