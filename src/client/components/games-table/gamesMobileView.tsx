import React, { MutableRefObject, useContext, useState } from "react";
import { Card, Table as BTable } from "react-bootstrap";
import {
    formatDemo,
    formatFeatures,
    formatGameCode,
    formatGamePageUrl,
    formatInfoSheet,
    formatMarketingKit,
    formatMarkets,
    formatPromoVideo,
    formatReleaseDate, formatReleaseDates, getBrandLogo
} from "client/components/games-table/formatTableColumns";
import { MobileView } from "react-device-detect";
import { IGamesGridRow } from "api/services/interfaces/iGamesService";
import { Game } from "utils/game";
import { MainAppContext } from "@pages/_app";
import { Virtuoso } from "react-virtuoso";
import AppLink from "@components/main/appLink";
import { useRouter } from "next/router";
import { EnumLocale, getLocaleMessages } from "locales/locales";
import { Project } from "utils/project";
import { observer } from "mobx-react-lite";
import { EnumPage } from "models/enum/page";

export interface ICmpGamesMobileViewProps {
    games: IGamesGridRow[];
    virtuoso: MutableRefObject<null>;
}

const GamesMobileView = observer(({ games, virtuoso }: ICmpGamesMobileViewProps) => {
    const { authStore, appStore } = useContext(MainAppContext);

    const router = useRouter();
    const locale = router.locale as EnumLocale;
    const msg = getLocaleMessages(locale).messages;

    const target = React.useRef(null);

    const id = "games-info";
    const [openedGameId, setOpenedGameId] = useState<string | null>(null);

    return (
        <MobileView className={"mobile-view"}>
            <div className={"games-table-mob"}>
                <div className={"games-table-mob-header"}>
                    <div className={"gr-row thead"}>
                        <div className="th">Game Logo</div>
                        <div className="th">Game Name</div>
                        <div className="th">Release Date</div>
                        <div className="th">Game Codes</div>
                        <div className="th">RTP Versions</div>
                    </div>
                </div>
                <div className={"games-table-mob-data"} id={id} ref={target}>
                    <Virtuoso
                        ref={virtuoso}
                        totalCount={games.length}
                        itemContent={(index) => {
                            const game = games[index];
                            const isOpened = openedGameId === game.id;

                            return (
                                <Card key={"game-" + game.id}>
                                    <div
                                        className="games-table-mob"
                                        onClick={() => {
                                            setOpenedGameId(isOpened ? null : game.id);
                                        }}
                                    >
                                        <div className="gr-row">
                                            <div className="td p-0">
                                                <Card.Img
                                                    variant="top"
                                                    className={" "}
                                                    src={game.gamePoster}
                                                    title={game.gameName}
                                                />
                                            </div>
                                            <div className="td">{game.gameName}</div>
                                            <div className="td td-release-dates">{formatReleaseDates(game,
                                                game.options.formatReleaseDate)}</div>
                                            <div className="td justify-content-start">
                                                <div className="text-truncate">
                                                    {formatGameCode(game)}
                                                </div>
                                            </div>
                                            <div className={"td mobile-card-column d-flex flex-column"}>
                                                {Game.formatRTP(game.rtp, game.options.showGameRTP, msg)
                                                    .split(" ")
                                                    .map((v) => {
                                                        return <span key={v}>{v}</span>;
                                                    })}
                                            </div>
                                        </div>
                                    </div>
                                    <div
                                        className={isOpened ? "open" : ""}
                                        style={{ display: isOpened ? "block" : "none" }}
                                    >
                                        <AppLink
                                            href={Project.getPageUrl(appStore, EnumPage.gameDetails,
                                                { gameId: game.id })}
                                            target={"_blank"}
                                            rel="noreferrer"
                                            className={"mobile-card-href"}
                                        >
                                            <Card.Img
                                                variant="top"
                                                className={"mobile-card-poster animation-fade-in"}
                                                src={game.gamePoster}
                                                title={game.gameName}
                                            />
                                        </AppLink>
                                        <Card.Body>
                                            <Card.Title className={"mobile-card-title"}>{game.gameName}</Card.Title>
                                            <BTable aria-label="simple table" key={"game-row-" + game.id}>
                                                <tbody>
                                                <tr key={"game-tbl-demo-" + game.id}>
                                                    {/*<td align="left" className={"mobile-card-header"}>*/}
                                                    {/*    {" "}*/}
                                                    {/*</td>*/}
                                                    <td align="center" colSpan={2}>
                                                        <div className="action-col action-col-btns">
                                                            {formatDemo(game, appStore, authStore)}
                                                            {game.videos &&
                                                                formatPromoVideo(game, appStore, authStore)}
                                                            {formatInfoSheet(game, appStore, authStore)}
                                                            {formatGamePageUrl(game, appStore, authStore)}
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr key={"game-tbl-demo-more-" + game.id}>
                                                    <td align="center" colSpan={2}>
                                                        {formatMarketingKit(
                                                            game,
                                                            appStore,
                                                            authStore,
                                                            "action-col action-col-btns"
                                                        )}
                                                    </td>
                                                </tr>
                                                <tr key={"game-tbl-bl-" + game.id}>
                                                    <td align="left" className={"mobile-card-header"}>
                                                        <div>Brand</div>
                                                    </td>
                                                    <td
                                                        align="left"
                                                        className={"mobile-card-column d-flex flex-column"}
                                                    >
                                                        {getBrandLogo(game.gameCode[0])}
                                                    </td>
                                                </tr>
                                                <tr key={"game-tbl-gc-" + game.id}>
                                                    <td align="left" className={"mobile-card-header"}>
                                                        <div>Game Codes</div>
                                                    </td>
                                                    <td
                                                        align="left"
                                                        className={"mobile-card-column d-flex flex-column"}
                                                    >
                                                        {formatGameCode(game)}
                                                    </td>
                                                </tr>
                                                <tr key={"game-tbl-rtp-" + game.id}>
                                                    <td align="left" className={"mobile-card-header"}>
                                                        <div>RTP versions</div>
                                                    </td>
                                                    <td
                                                        align="left"
                                                        className={"mobile-card-column d-flex flex-column"}
                                                    >
                                                        {Game.formatRTP(game.rtp, game.options.showGameRTP, msg)
                                                            .split(" ")
                                                            .map((v) => {
                                                                return <span key={v}>{v}</span>;
                                                            })}
                                                    </td>
                                                </tr>
                                                <tr key={"game-tbl-volatility-" + game.id}>
                                                    <td align="left" className={"mobile-card-header"}>
                                                        <div>Volatility</div>
                                                    </td>
                                                    <td align="left">{Game.formatVolatility(game.vol, msg)}</td>
                                                </tr>
                                                <tr key={"game-tbl-features-" + game.id}>
                                                    <td align="left" className={"mobile-card-header  td-markets"}>
                                                        <div>Features</div>
                                                    </td>
                                                    <td align="left" className={"td-markets"}>{formatFeatures(game)}</td>
                                                </tr>
                                                {Object.keys(game.marketsWithReleaseDate || {}).map(releaseDate => {
                                                    return (<>
                                                                <tr key={"game-tbl-releaseDate-" + game.id}>
                                                                    <td align="left" className={"mobile-card-header td-release-dates"}>
                                                                        <div>Release Date</div>
                                                                    </td>
                                                                    <td align="left" className={"td-release-dates"}>{formatReleaseDate(releaseDate,
                                                                        game.options.formatReleaseDate)}</td>
                                                                </tr>
                                                                <tr key={"game-tbl-markets-" + game.id}>
                                                                    <td align="left" className={"mobile-card-header td-markets"}>
                                                                        <div>Markets</div>
                                                                    </td>
                                                                    <td className={"td-markets"} align="left">{
                                                                        formatMarkets({markets: game.marketsWithReleaseDate?.[releaseDate]} as IGamesGridRow)
                                                                    }</td>
                                                                </tr>
                                                            </>)
                                                })}
                                                </tbody>
                                            </BTable>
                                        </Card.Body>
                                    </div>
                                </Card>
                            );
                        }}
                    />
                </div>
            </div>
        </MobileView>
    );
});

export default GamesMobileView;
