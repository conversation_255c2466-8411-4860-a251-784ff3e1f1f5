import React, { MutableRefObject, useContext, useState } from "react";
import { BrowserView } from "react-device-detect";
import {
    formatActionColumn,
    formatFeatures,
    formatGameCode,
    formatGameLogo,
    formatMarketingKit,
    formatMarketsAndReleaseDate, getBrandLogo
} from "client/components/games-table/formatTableColumns";
import { IGamesGridRow } from "api/services/interfaces/iGamesService";
import { EnumLocale, getLocaleMessages } from "locales/locales";
import { useRouter } from "next/router";
import { Game } from "utils/game";
import { MainAppContext } from "@pages/_app";
import { Virtuoso } from "react-virtuoso";

const GamesBrowserView = (props: { games: IGamesGridRow[]; virtuoso: MutableRefObject<null> }) => {
    const { games, virtuoso } = props;

    const router = useRouter();
    const locale = router.locale;
    const msg = getLocaleMessages(locale as EnumLocale).messages;

    const { authStore, appStore } = useContext(MainAppContext);

    interface IColumn {
        name: string;
        title: string;
        getCellValue: (...args: any) => JSX.Element | JSX.Element[] | undefined | string | null;
    }

    const [columns] = useState<IColumn[]>([
        { name: "gamePoster", title: "Game Logo", getCellValue: (row: IGamesGridRow) => formatGameLogo(row, appStore) },
        { name: "gameName", title: "Game Name", getCellValue: (row: IGamesGridRow) => row.gameName },
        { name: "gameName", title: "Brand", getCellValue: (row: IGamesGridRow) => getBrandLogo(row.gameCode[0]) },
        {
            name: "action",
            title: " ",
            getCellValue: (row: IGamesGridRow) => formatActionColumn(row, appStore, authStore)
        },
        {
            name: "marketingKit",
            title: " ",
            getCellValue: (row: IGamesGridRow) => formatMarketingKit(row, appStore, authStore)
        },
        { name: "gameCode", title: "Game Codes", getCellValue: formatGameCode },
        {
            name: "rtp",
            title: "RTP Versions",
            getCellValue: (row: IGamesGridRow) => Game.formatRTP(row.rtp, row.options.showGameRTP, msg)
        },
        // {
        //     name: "releaseDate",
        //     title: "Release Date",
        //     getCellValue: (row: IGamesGridRow) => formatReleaseDate(row.releaseDate, row.options?.formatReleaseDate)
        // },
        // { name: "markets", title: "Markets", getCellValue: formatMarkets },
        {
            name: "marketsAndReleaseDate",
            title: "Release Date / Markets",
            getCellValue: (row: IGamesGridRow) => formatMarketsAndReleaseDate(row, row.releaseDate, row.options?.formatReleaseDate)
        },
        { name: "features", title: "Features", getCellValue: formatFeatures },
        {
            name: "volatility",
            title: "Volatility",
            getCellValue: (row: IGamesGridRow) => Game.formatVolatility(row.vol, msg)
        }
    ]);
    /*
    const [tableColumnExtensions] = useState([
        { columnName: "gameName", width: "7%", wordWrapEnabled: true },
        { columnName: "gameCode", width: 90, wordWrapEnabled: true },
        { columnName: "gamePoster", width: 200, wordWrapEnabled: false },
        { columnName: "releaseDate", width: 80 },
        { columnName: "rtp", width: 60 },
        { columnName: "volatility", width: 100 },
        { columnName: "marketingKit", width: 120 },
        { columnName: "action", width: 120 },
    ]);*/

    const target = React.useRef(null);
    const targetThead = React.useRef(null);

    const id = "games-info";

    const theadId = "games-thead";

    return (
        <BrowserView className={"games-table"}>
            <div id={theadId} ref={targetThead} className={"games-table-header"}>
                <div className={"gr-row thead"}>
                    {columns.map((col) => {
                        // const info = tableColumnExtensions.find((c) => c.columnName === col.name);
                        // const style = info ? { width: info.width } : {};
                        return (
                            <div className={"th"} key={col.name}>
                                {col.title.split("/").map(subTitle => <span className={`${col.name}_subtitle`} key={`${col.name}_subtitle`}>{subTitle}</span>)}
                            </div>
                        );
                    })}
                    <div />
                </div>
            </div>
            <div className={"games-table-data"} id={id} ref={target}>
                <Virtuoso
                    ref={virtuoso}
                    className={"gt-virtual"}
                    totalCount={games.length}
                    itemContent={(index) => {
                        const game = games[index];

                        return (
                            <div className={"gr-row"}>
                                {columns.map((col) => {
                                    // const info = tableColumnExtensions.find((c) => c.columnName === col.name);
                                    // const style = info ? { width: info.width } : {};
                                    const val = col.getCellValue ? col.getCellValue(game, col.name) : "";
                                    return (
                                        // <th key={col.name} scope="col" style={style}>
                                        <div className={"games-table-data-" + col.name} key={col.name}>{val}</div>
                                        // </th>
                                    );
                                })}
                            </div>
                        );
                    }}
                />
            </div>
        </BrowserView>
    );
};

export default GamesBrowserView;
