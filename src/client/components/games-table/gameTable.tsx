import React, { MutableRefObject } from "react";
import { observer } from "mobx-react-lite";
import { IGamesGridRow } from "api/services/interfaces/iGamesService";
import { isDesktop } from "react-device-detect";
import GamesMobileView from "client/components/games-table/gamesMobileView";
import GamesBrowserView from "client/components/games-table/gamesBrowserView";

const GameTable = observer(
    ({ /*fixed,*/ games, virtuoso }: { games: IGamesGridRow[]; fixed: boolean; virtuoso: MutableRefObject<null> }) => {
        if (isDesktop) {
            return <GamesBrowserView games={games} virtuoso={virtuoso} />;
        } else {
            return <GamesMobileView games={games} virtuoso={virtuoso} />;
        }
    }
);

export default GameTable;
