import React, { ReactNode, useContext } from "react";
import { observer } from "mobx-react-lite";
import { Modal, Spinner } from "react-bootstrap";
import { MainAppContext } from "pages/_app";

const MainLayout = observer(({ children }: { children: ReactNode }) => {
    const { /*authStore,*/ appStore } = useContext(MainAppContext);
    const handleLoading = (status: boolean) => {
        appStore.setAppLoading(status);
    };

    return (
        <div>
            {children}
            <Modal
                show={appStore.getAppLoading()}
                onHide={() => {
                    handleLoading(false);
                }}
                backdrop="static"
                keyboard={false}
                // centered={true}
            >
                <div className="text-center loading-spinner">
                    <Spinner
                        as="span"
                        className={"pa-spinner"}
                        animation="border"
                        size="sm"
                        role="status"
                        aria-hidden="true"
                    />
                    <span className={"pa-spinner-text"}>Loading...</span>
                </div>
            </Modal>
        </div>
    );
});

export default MainLayout;
