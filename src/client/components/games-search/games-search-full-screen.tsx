import React, { useContext, useEffect, useRef, useState } from "react";
import AppLink from "@components/main/appLink";
import AppImg from "@components/main/appImg";
import { observer } from "mobx-react-lite";
import { MainAppContext } from "@pages/_app";
import { isMobile, MobileView } from "react-device-detect";
import { GamesSearchClass } from "@components/games-search/games-search-class";
import GamesSearchStore from "@components/games-search/games-search-store";
import { EnumLanguageCode, EnumRegionCode } from "models/enum/system";
import { EnumSearchButton, EnumSearchFilter, EnumSearchPluginEvents } from "@components/games-search/_enums";
import { useRouter } from "next/router";
import { EnumLocale, getLocaleMessages } from "locales/locales";
import SearchInputElement from "@components/games-search/elements/search-input-element";
import SearchButtonClose from "@components/games-search/elements/search-button-close";
import SearchFilterSubmenuItem from "@components/games-search/elements/search-filter-submenu-item";
import SubmenuItemType from "@components/games-search/elements/submenu-item-type";
import SubmenuItemVol from "@components/games-search/elements/submenu-item-vol";
import SubmenuItemRtp from "@components/games-search/elements/submenu-item-rtp";
import SubmenuItemCert from "@components/games-search/elements/submenu-item-cert";
import SubmenuItemTag from "@components/games-search/elements/submenu-item-tag";
import SubmenuItemJackpot from "@components/games-search/elements/submenu-item-jackpot";
import SubmenuItemYear from "@components/games-search/elements/submenu-item-year";
import SearchSubmenuClose from "@components/games-search/elements/search-submenu-close";
import SearchFilterButton from "@components/games-search/elements/search-filter-button";
import SearchFilterClearButton from "@components/games-search/elements/search-filter-clear-button";
import { Game } from "utils/game";
import { Virtuoso } from "react-virtuoso";
import { ICmpGamesSearchFullScreenProps } from "@components/games-search/_interfaces";
import { EnumPage } from "models/enum/page";
import { Project } from "utils/project";

let search: GamesSearchClass | null = null;

const GamesSearchFullScreen = observer(({ id }: ICmpGamesSearchFullScreenProps) => {
    const { appStore } = useContext(MainAppContext);
    const router = useRouter();

    const [showFilters, setShowFilters] = useState<boolean>(!isMobile);

    const virtuoso = useRef(null);
    const [virtuosoAlign /*, setVirtuosoAlign*/] = useState("start");
    const [virtuosoBehavior /*, setVirtuosoBehavior*/] = useState("auto");

    const gsf = appStore.getGamesSearchFullScreen();

    const strictModeRef = useRef<boolean>(false);

    useEffect(() => {
        if (!strictModeRef.current) {
            strictModeRef.current = true;
            if (search?.resetSearch) {
                search?.resetSearch();
            }
        }
    }, []);

    useEffect(() => {
        if (gsf.isVisible) {
            document.body.classList.add("no-scroll");
            document.documentElement.classList.add("no-scroll");
        } else {
            document.body.classList.remove("no-scroll");
            document.documentElement.classList.remove("no-scroll");
        }
    }, [gsf]);

    const searchInputElement = useRef<HTMLInputElement>(null);

    const searchStore = useContext(GamesSearchStore);

    if (!gsf.isVisible) {
        return null;
    }

    if (!search) {
        search = new GamesSearchClass(
            searchInputElement,
            {
                DEBUG: false,
                currentLanguageCode: EnumLanguageCode.en,
                currentRegionCode: EnumRegionCode.eu,
                partnerArea: false,
                saveClientAction: false
            },
            searchStore
        );

        search.events.on(EnumSearchPluginEvents.afterFilterChanged, (/*event: CustomEvent*/): void => {
            setTimeout(() => {
                // @ts-ignore
                virtuoso?.current?.scrollToIndex({
                    index: 0,
                    align: virtuosoAlign,
                    behavior: virtuosoBehavior
                });
            }, 500);
        });
    }

    const locale = router.locale;
    const msgSearch = getLocaleMessages(locale as EnumLocale).components.search;
    const msg = getLocaleMessages(locale as EnumLocale).messages;

    const submenuCloseBtn = searchStore.getButton(EnumSearchButton.submenuCloseButton);
    const filtersClearBtn = searchStore.getButton(EnumSearchButton.clearFiltersButton);

    const closeButton = searchStore.getButton(EnumSearchButton.closeButton);
    if (closeButton.isVisible === false) {
        searchStore.setButton(EnumSearchButton.closeButton, { isVisible: true });
    }

    return (
        <div id={id} className={"sw-search games-search-site"}>
            <div className={"sw-search-container"}>
                <div className={"sw-search-container__menu"}>
                    <div className="sw-search-container search-col">
                        <div className="sw-search-container__menu">
                            <div className="sw-search-control">
                                <SearchInputElement
                                    searchInputRef={searchInputElement}
                                    store={searchStore}
                                    inputElClassName={"sw-search__input"}
                                    className={"sw-search__input-label"}
                                    placeHolder={msgSearch.inputPlaceHolder}
                                    onChange={(event) => {
                                        search?.handleInputChange(event);
                                    }}
                                />
                                <MobileView>
                                    <div
                                        className="btn btn-light btn-sm f_filters_btn"
                                        role="button"
                                        onClick={() => {
                                            setShowFilters(!showFilters);
                                        }}
                                    >
                                        {showFilters ? "Hide" : "Show"} Filters
                                    </div>
                                </MobileView>
                                <SearchButtonClose
                                    store={searchStore}
                                    className={"sw-search__close"}
                                    onClick={(event) => {
                                        event.preventDefault();
                                        appStore.setGamesSearchFullScreen({ isVisible: false });
                                    }} />
                            </div>

                            {showFilters && (
                                <>
                                    <div className={"sw-search-submenu" + (submenuCloseBtn?.isVisible ? " open" : "")}>
                                        <div className="sw-search-submenu__types">
                                            <SearchFilterSubmenuItem store={searchStore} type={EnumSearchFilter.type}>
                                                <SubmenuItemType
                                                    store={searchStore}
                                                    onClick={(event) => {
                                                        search?.handleSubmenuItemClickEvent(
                                                            event,
                                                            EnumSearchFilter.type
                                                        );
                                                    }}
                                                />
                                            </SearchFilterSubmenuItem>
                                            <SearchFilterSubmenuItem store={searchStore} type={EnumSearchFilter.vol}>
                                                <SubmenuItemVol
                                                    store={searchStore}
                                                    onClick={(event) => {
                                                        search?.handleSubmenuItemClickEvent(
                                                            event,
                                                            EnumSearchFilter.vol
                                                        );
                                                    }}
                                                />
                                            </SearchFilterSubmenuItem>
                                            <SearchFilterSubmenuItem store={searchStore} type={EnumSearchFilter.rtp}>
                                                <SubmenuItemRtp
                                                    store={searchStore}
                                                    onAfterChange={(value) => {
                                                        search?.handleSubmenuRangeChange(value, EnumSearchFilter.rtp);
                                                    }}
                                                />
                                            </SearchFilterSubmenuItem>
                                            <SearchFilterSubmenuItem store={searchStore} type={EnumSearchFilter.cert}>
                                                <SubmenuItemCert
                                                    store={searchStore}
                                                    onClick={(event) => {
                                                        search?.handleSubmenuItemClickEvent(
                                                            event,
                                                            EnumSearchFilter.cert
                                                        );
                                                    }}
                                                />
                                            </SearchFilterSubmenuItem>
                                            <SearchFilterSubmenuItem store={searchStore} type={EnumSearchFilter.tag}>
                                                <SubmenuItemTag
                                                    store={searchStore}
                                                    onChange={(event) => {
                                                        search?.handleSubmenuTagsChangeEvent(
                                                            event,
                                                            EnumSearchFilter.tag
                                                        );
                                                    }}
                                                />
                                            </SearchFilterSubmenuItem>
                                            <SearchFilterSubmenuItem
                                                store={searchStore}
                                                type={EnumSearchFilter.jackpot}
                                            >
                                                <SubmenuItemJackpot
                                                    store={searchStore}
                                                    onChange={(event) => {
                                                        search?.handleSubmenuRadioChangeEvent(
                                                            event,
                                                            EnumSearchFilter.jackpot
                                                        );
                                                    }}
                                                />
                                            </SearchFilterSubmenuItem>
                                            <SearchFilterSubmenuItem store={searchStore} type={EnumSearchFilter.year}>
                                                <SubmenuItemYear
                                                    store={searchStore}
                                                    onClick={(event) => {
                                                        search?.handleSubmenuItemClickEvent(
                                                            event,
                                                            EnumSearchFilter.year
                                                        );
                                                    }}
                                                />
                                            </SearchFilterSubmenuItem>
                                        </div>
                                        <SearchSubmenuClose
                                            store={searchStore}
                                            className="sw-search-submenu__close"
                                            onClick={(event) => {
                                                search?.handleSubmenuCloseClickEvent(event);
                                            }}
                                        />
                                    </div>

                                    <div className="sw-search-filter">
                                        <div
                                            style={{
                                                position: "absolute",
                                                fontFamily: "inherit",
                                                fontSize: 14,
                                                fontWeight: 600,
                                                left: 22,
                                                top: 4,
                                                opacity: 0.5,
                                                textTransform: "uppercase"
                                            }}
                                        >
                                            Filter By:
                                        </div>
                                        <SearchFilterButton
                                            title={msgSearch.filterTypeTitle}
                                            type={EnumSearchFilter.type}
                                            className={"adv-" + EnumSearchFilter.type}
                                            store={searchStore}
                                            iconSrc={"/assets/pa/images/search/type.png"}
                                            onClick={(event) => {
                                                search?.handleFilterButtonClickEvent(event, EnumSearchFilter.type);
                                            }}
                                            resetClick={(event) => {
                                                search?.handleFilterResetClickEvent(event, EnumSearchFilter.type);
                                            }}
                                        />
                                        <SearchFilterButton
                                            title={msgSearch.filterVolatilityTitle}
                                            type={EnumSearchFilter.vol}
                                            className={"adv-" + EnumSearchFilter.vol}
                                            store={searchStore}
                                            iconSrc={"/assets/pa/images/search/volatility.png"}
                                            onClick={(event) => {
                                                search?.handleFilterButtonClickEvent(event, EnumSearchFilter.vol);
                                            }}
                                            resetClick={(event) => {
                                                search?.handleFilterResetClickEvent(event, EnumSearchFilter.vol);
                                            }}
                                        />
                                        <SearchFilterButton
                                            title={msgSearch.filterRTPTitle}
                                            type={EnumSearchFilter.rtp}
                                            className={"adv-" + EnumSearchFilter.rtp}
                                            store={searchStore}
                                            iconSrc={"/assets/pa/images/search/rtp.png"}
                                            onClick={(event) => {
                                                search?.handleFilterButtonClickEvent(event, EnumSearchFilter.rtp);
                                            }}
                                            resetClick={(event) => {
                                                search?.handleFilterResetClickEvent(event, EnumSearchFilter.rtp);
                                            }}
                                        />
                                        <SearchFilterButton
                                            title={msgSearch.filterCertificationsTitle}
                                            type={EnumSearchFilter.cert}
                                            className={"adv-" + EnumSearchFilter.cert}
                                            store={searchStore}
                                            iconSrc={"/assets/pa/images/search/certifications.png"}
                                            onClick={(event) => {
                                                search?.handleFilterButtonClickEvent(event, EnumSearchFilter.cert);
                                            }}
                                            resetClick={(event) => {
                                                search?.handleFilterResetClickEvent(event, EnumSearchFilter.cert);
                                            }}
                                        />
                                        <SearchFilterButton
                                            title={msgSearch.filterTagsTitle}
                                            type={EnumSearchFilter.tag}
                                            className={"adv-" + EnumSearchFilter.tag}
                                            store={searchStore}
                                            iconSrc={"/assets/pa/images/search/tags.png"}
                                            onClick={(event) => {
                                                search?.handleFilterButtonClickEvent(event, EnumSearchFilter.tag);
                                            }}
                                            resetClick={(event) => {
                                                search?.handleFilterResetClickEvent(event, EnumSearchFilter.tag);
                                            }}
                                        />
                                        <SearchFilterButton
                                            title={msgSearch.filterJackpotsTitle}
                                            type={EnumSearchFilter.jackpot}
                                            className={"adv-" + EnumSearchFilter.jackpot}
                                            store={searchStore}
                                            iconSrc={"/assets/pa/images/search/jackpots.png"}
                                            onClick={(event) => {
                                                search?.handleFilterButtonClickEvent(event, EnumSearchFilter.jackpot);
                                            }}
                                            resetClick={(event) => {
                                                search?.handleFilterResetClickEvent(event, EnumSearchFilter.jackpot);
                                            }}
                                        />
                                        <SearchFilterButton
                                            title={msgSearch.filterReleaseYearTitle}
                                            type={EnumSearchFilter.year}
                                            className={"adv-" + EnumSearchFilter.year}
                                            store={searchStore}
                                            iconSrc={"/assets/pa/images/search/release-year.png"}
                                            onClick={(event) => {
                                                search?.handleFilterButtonClickEvent(event, EnumSearchFilter.year);
                                            }}
                                            resetClick={(event) => {
                                                search?.handleFilterResetClickEvent(event, EnumSearchFilter.year);
                                            }}
                                        />
                                        <SearchFilterClearButton
                                            store={searchStore}
                                            className={
                                                "sw-search-filter__item clear-all-filter" +
                                                (filtersClearBtn?.isActive ? " active" : "")
                                            }
                                            title={msgSearch.filterClearTitle}
                                            iconSrc={"/assets/pa/images/search/close.png"}
                                            onClick={(event) => {
                                                search?.handleFilterClearButtonClickEvent(event);
                                            }}
                                        />
                                    </div>
                                </>
                            )}
                        </div>
                        {/*<div className="sw-search-filter-values">[{searchStore.searchResultsText}]</div>*/}
                    </div>
                </div>
                <div className={"sw-search-container__result"}>
                    <div className={"sw-search-result"}>
                        <div className="sw-search-result__title">
                            <strong><span>{searchStore.searchResults.length}</span>&nbsp;<span
                                data-result-text="results">results</span></strong></div>
                        <div className={"sw-search-result__data"}>
                            <Virtuoso
                                ref={virtuoso}
                                totalCount={searchStore.searchResults.length}
                                itemContent={(index) => {
                                    const g = searchStore.searchResults[index];
                                    const gameCode = Game.getPlayGameCode(g.gameCode);
                                    const gameUrl = Project.getPageUrl(appStore,
                                        EnumPage.gameDetails,
                                        { gameId: gameCode });

                                    return (
                                        <AppLink
                                            key={g.id}
                                            href={gameUrl}
                                            className="sw-search-result-item" onClick={(e) => {
                                            e.preventDefault();
                                            appStore.setGamesSearchFullScreen({ isVisible: false });
                                            location.assign(gameUrl);
                                        }}>
                                            <div className="sw-search-result-item__img">
                                                <AppImg
                                                    src={g.gamePoster}
                                                    title={g.gameName} alt={g.gameName}
                                                    className="sw-search-result-image" /></div>
                                            <div className="sw-search-result-item__desc">
                                                <div className="search-result-text">
                                                    <div className="search-result-text__title">{g.gameName}</div>
                                                    <div className="search-result-text__tags">
                                                        {!g.features || <span>{g.features || ""}</span>}
                                                        <span>RTP: {Game.formatRTP(g.rtp,
                                                            g.options.showGameRTP,
                                                            msg)}</span>
                                                        {g.cert && Array.isArray(g.cert) && g.cert.length > 1 &&
                                                            <span>Certification: {g.cert.join(", ")
                                                                .toUpperCase()}</span>
                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                        </AppLink>
                                    );
                                }} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
});

export default GamesSearchFullScreen;