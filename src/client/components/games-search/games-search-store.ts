import { makeAutoObservable } from "mobx";
import { createContext } from "react";
import { IGamesGridRow } from "api/services/interfaces/iGamesService";
import { EnumSearchButton, EnumSearchButtonState, EnumSearchFilter } from "@components/games-search/_enums";
import {
    ISearchButtons,
    ISearchFilterButton,
    ISearchFilterKeyValue,
    ISearchFilters, ISearchToolbarButton, TSearchFilterValue
} from "@components/games-search/_interfaces";

export const GamesSearchStore = makeAutoObservable({
    isToolbarVisible: true,

    searchResultsText: "",

    searchFilterValues: {} as { [key: string]: any },

    searchResults: [] as IGamesGridRow[],

    buttons: {} as ISearchButtons,

    filters: {} as ISearchFilters,

    values: {
        searchInput: "",
        filters: {} as ISearchFilterKeyValue
    },

    subMenu: {
        openFilter: null as null | EnumSearchFilter
    },

    initialData: {
        filters: {} as ISearchFilterKeyValue
    },

    resetValues(): void {
        this.values = {
            searchInput: "",
            filters: {} as ISearchFilterKeyValue
        };
    },

    setSearchResults(rows: IGamesGridRow[]): void {
        rows.forEach(row => row.marketsWithReleaseDate = {[row.releaseDate]: row.markets})
        this.searchResults = rows;
    },

    setSearchInput(value: string): void {
        this.values.searchInput = value;
    },

    setSearchResultsText(text: string, filterValues: { [key: string]: any }): void {
        this.searchResultsText = text;
        this.searchFilterValues = filterValues;
    },

    setSearchButton(params: ISearchToolbarButton): void {
        this.buttons.searchButton = params;
    },

    setCloseButton(params: ISearchToolbarButton): void {
        this.buttons.closeButton = params;
    },

    setClearFiltersButton(params: ISearchToolbarButton): void {
        this.buttons.closeButton = params;
    },

    setFilterButton(params: ISearchFilterButton): void {
        this.filters[params.id] = params;
    },

    setFilterButtonState(id: EnumSearchFilter, state: EnumSearchButtonState): void {
        this.filters[id] = { ...this.filters[id], state };
    },

    setSubmenuCloseButton(params: ISearchToolbarButton): void {
        this.buttons.submenuCloseButton = params;
    },

    setSubMenu(subMenu: object) {
        this.subMenu = {...this.subMenu, ...subMenu}
    },

    getSubMenu() {
        return this.subMenu;
    },

    setButton(button: EnumSearchButton, buttonProps: ISearchToolbarButton) {
        this.buttons[button] = {...this.buttons[button], ...buttonProps}
    },

    getButton(button: EnumSearchButton) {
        return this.buttons[button];
    },

    setFilter(filter: EnumSearchFilter, filterProps: ISearchFilterButton) {
        this.filters[filter] = {...this.filters[filter], ...filterProps}
    },

    getFilter(filter: EnumSearchFilter) {
        return this.filters[filter];
    },

    getFilters() {
        return this.filters;
    },

    setValueSearchInput(searchInputValue: string) {
        this.values.searchInput = searchInputValue;
    },

    getValueSearchInput() {
        return this.values.searchInput;
    },

    setValueFilter(filter: EnumSearchFilter, value: TSearchFilterValue){
        this.values.filters[filter] = value;
    },

    getValueFilter(filter: EnumSearchFilter) {
        return this.values.filters[filter];
    },

    setInitialDateFilter(filter: EnumSearchFilter, value: TSearchFilterValue){
        this.initialData.filters[filter] = value;
    },

    getInitialDateFilter(filter: EnumSearchFilter) {
        return this.initialData.filters[filter];
    }

});

export type TGamesSearchStore = typeof GamesSearchStore;

export default createContext(GamesSearchStore);
