export default class GamesSearchEvents {
    private events: { [index: string]: EventListenerOrEventListenerObject } = {};

    /**
     * Initializes an object
     *
     * @param target  DOM interface implemented by objects that can receive events and may have listeners for them.
     */
    constructor(private target: EventTarget = document) {}

    /**
     * Sets up a function that will be called whenever the specified event is triggered
     *
     * @param eventName  Custom event name to listen for
     * @param callback  Callback function accepts a parameter: Event object describing the event which has occurred
     */
    public on(eventName: string, callback: EventListenerOrEventListenerObject) {
        this.events[eventName] = callback;
        this.target.addEventListener(eventName, callback);
    }

    /**
     * Removes an event listener previously registered with `SimpleCustomEvent.on()`
     *
     * @param eventName  Custom event name for which to remove an event listener
     */
    public off(eventName: string) {
        this.target.removeEventListener(eventName, this.events[eventName]);
        delete this.events[eventName];
    }

    /**
     * Initializes and dispatches an custom Event, invoking the affected Listeners
     *
     * @param eventName  Custom event name
     * @param detail  Any data passed when triggering the event
     */
    public trigger(eventName: string, detail: any = null) {
        /**
         * Polyfill the CustomEvent() constructor functionality in Internet Explorer 9 and higher
         * based on: https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent/CustomEvent#Polyfill
         */
        if (typeof (window as any).CustomEvent !== "function") {
            (window as any).CustomEvent = (type: string, params: CustomEvent) => {
                const defaultParams: object = { bubbles: false, cancelable: false, detail: undefined };
                return  new CustomEvent(type, { ...defaultParams, ...params });
            };
        }
        const event = new CustomEvent(eventName, { detail });
        this.target.dispatchEvent(event);
    }
}
