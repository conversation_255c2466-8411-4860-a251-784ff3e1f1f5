import { EnumLanguageCode, EnumRegionCode } from "models/enum/system";
import { IBannersCarouselExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { IGamesGridRow } from "api/services/interfaces/iGamesService";
import { EnumSearchButtonState, EnumSearchFilter } from "@components/games-search/_enums";

export interface ICmpGamesSearchFullScreenProps {
    id: string;
}

export interface ICmpGamesSearchTableProps {
    id: string;
    regionCode: EnumRegionCode;
    languageCode: EnumLanguageCode;
    bannersCarousel?: IBannersCarouselExtended;
}

export interface IPluginTexts {
    tagsPlaceholder: string;
    gotoTop: string;
    results: string;
    rtp: string;
    certification: string;
}

export interface ISearchGames {
    cat: string[];
    cert: string[];
    code: string;
    id: string;
    image: string;
    js: boolean;
    rtp: number;
    tag: string[];
    title: string;
    type: string;
    url: string;
    vol: string;
    year: string;
    date: string;
    play: string;
    mk: string[];
    market?: string[];
    rank?: string[];
    jackpot?: string[];
}

export interface searchSearch {
    [key: string]: Array<string | number>;
}

export interface ISearchGameCode2Market {
    [gameCode: string]: { [countryName: string]: number };
}

export interface ISearchData {
    games: IGamesGridRow[];
    search: ISearchGames;
    countries?: { [countryCode: string]: string };
    gameCode2Market?: ISearchGameCode2Market;
    markets?: string[];
}

export interface ISearchOptions {
    DEBUG: boolean;
    currentLanguageCode: EnumLanguageCode;
    currentRegionCode: EnumRegionCode;
    partnerArea: boolean;
    saveClientAction: boolean;
}


export interface ISearchToolbarButton {
    isVisible?: boolean;
    isActive?: boolean;
}

export interface ISearchButtons {
    searchButton: ISearchToolbarButton;
    closeButton: ISearchToolbarButton;
    clearFiltersButton: ISearchToolbarButton;
    submenuCloseButton: ISearchToolbarButton;
}

export interface ISearchFilterButton {
    id: EnumSearchFilter;
    state: EnumSearchButtonState;
}

export interface ISearchFilters {
    [EnumSearchFilter.market]: ISearchFilterButton;
    [EnumSearchFilter.rank]: ISearchFilterButton;
    [EnumSearchFilter.type]: ISearchFilterButton;
    [EnumSearchFilter.vol]: ISearchFilterButton;
    [EnumSearchFilter.rtp]: ISearchFilterButton;
    [EnumSearchFilter.cert]: ISearchFilterButton;
    [EnumSearchFilter.tag]: ISearchFilterButton;
    [EnumSearchFilter.jackpot]: ISearchFilterButton;
    [EnumSearchFilter.year]: ISearchFilterButton;
}

export type TSearchFilterValue = Array<number | string | boolean>;

export interface ISearchFilterKeyValue {
    [id: string]: TSearchFilterValue;
}
