import React from "react";
import { observer } from "mobx-react-lite";
import { TGamesSearchStore } from "@components/games-search/games-search-store";

interface ISearchButtonCloseProps {
    store: TGamesSearchStore;
    className: string;
    onClick?: (event: React.MouseEvent<HTMLDivElement>) => void;
}

const SearchButtonClose = observer(({ store, className, onClick }: ISearchButtonCloseProps) => {

    if (store.buttons?.closeButton?.isVisible) {
        return (
            <span className={className} onClick={onClick}>
                <svg xmlns="http://www.w3.org/2000/svg" width="26px" height="23px">
                    <text
                        fillRule="evenodd"
                        fill="#1E1D1D"
                        transform="matrix( 3.01491354393803, 0, 0, 2.15267071966281,-0.378678851225, 23.3396889581454)"
                    >
                        X
                    </text>
                </svg>
            </span>
        );
    } else {
        return <div style={{ width: 4 }} />;
    }
});

export default SearchButtonClose;
