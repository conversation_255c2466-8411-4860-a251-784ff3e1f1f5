import React from "react";
import { observer } from "mobx-react-lite";
import { TSearchFilterValue } from "@components/games-search/_interfaces";

export interface ICmpTypeButtonsProps {
    className?: string;
    onClick: (event: React.MouseEvent<HTMLDivElement>) => void;
    data: TSearchFilterValue;
    values: TSearchFilterValue;
}

const TypeButtons = observer(({ className, onClick, data, values }: ICmpTypeButtonsProps) => {
    return (
        <>
            {data.map((val) => {
                return (
                    <div
                        className={
                            "sw-search-tag sw-search-tag--small" +
                            (values.includes(val) ? " active" : "") +
                            (className ? " " + className : "")
                        }
                        data-val={val}
                        key={String(val)}
                        onClick={onClick}
                    >
                        {val}
                    </div>
                );
            })}
        </>
    );
});

export default TypeButtons;
