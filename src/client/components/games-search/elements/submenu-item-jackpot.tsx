import { observer } from "mobx-react-lite";
import React from "react";
import { EnumSearchFilter } from "@components/games-search/_enums";
import { TGamesSearchStore } from "@components/games-search/games-search-store";

export interface ICmpSubmenuItemJackpotProps {
    store: TGamesSearchStore;
    className?: string;
    onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const SubmenuItemJackpot = observer(({ store, className, onChange }: ICmpSubmenuItemJackpotProps) => {
    const values = store.getValueFilter(EnumSearchFilter.jackpot);

    return (
        <div className={"sw-search-checkboxes-group adv-jackpot" + (className ? " " + className : "")}>
            <div className="sw-search-checkbox">
                <input
                    type="radio"
                    name="submenuJackpot"
                    value="true"
                    key={"input-jackpot-true"}
                    id="input-jackpot-true"
                    checked={Array.isArray(values) && values.length > 0 && values[0] === "true"}
                    onChange={onChange}
                />
                <label id="label-jackpot-true" htmlFor="input-jackpot-true">
                    With jackpot
                </label>
            </div>

            <div className="sw-search-checkbox">
                <input
                    type="radio"
                    name="submenuJackpot"
                    value="false"
                    key={"input-jackpot-false"}
                    id="input-jackpot-false"
                    checked={Array.isArray(values) && values.length > 0 && values[0] === "false"}
                    onChange={onChange}
                />
                <label id="label-jackpot-false" htmlFor="input-jackpot-false">
                    Without jackpot
                </label>
            </div>
        </div>
    );
});

export default SubmenuItemJackpot;
