import React, { RefObject } from "react";
import { observer } from "mobx-react-lite";
import { TGamesSearchStore } from "@components/games-search/games-search-store";
import { EnumSearchButton } from "@components/games-search/_enums";

export interface ICmpSearchInputElementProps {
    store: TGamesSearchStore;
    placeHolder: string;
    inputElClassName: string;
    className: string;
    searchInputRef: RefObject<HTMLInputElement>;
    onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const SearchInputElement = observer((
    { store, placeHolder, inputElClassName, className, searchInputRef, onChange }: ICmpSearchInputElementProps) => {

    const button = store.getButton(EnumSearchButton.searchButton);
    const searchInputValue = store.getValueSearchInput();

    return (
        <label className={className}>
            <input
                ref={searchInputRef}
                type="text"
                placeholder={placeHolder}
                className={inputElClassName}
                value={searchInputValue}
                onChange={onChange}
            />
            {button?.isVisible && (
                <span className="sw-search__btn">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px">
                            <path
                                fillRule="evenodd"
                                fill="rgb(255, 255, 255)"
                                d="M23.539,21.301 L17.282,15.044 C18.392,13.486 19.044,11.581 19.044,9.523 C19.044,4.263 14.781,0.000 9.522,0.000 C4.263,0.000 -0.000,4.263 -0.000,9.523 C-0.000,14.783 4.263,19.047 9.522,19.047 C11.580,19.047 13.485,18.394 15.042,17.284 L21.298,23.542 C21.910,24.153 22.902,24.151 23.512,23.541 L23.539,23.515 C24.150,22.903 24.155,21.917 23.539,21.301 ZM9.522,16.806 C5.501,16.806 2.240,13.545 2.240,9.523 C2.240,5.501 5.501,2.241 9.522,2.241 C13.544,2.241 16.804,5.501 16.804,9.523 C16.804,13.545 13.544,16.806 9.522,16.806 Z"
                            />
                        </svg>
                    </span>
            )}
        </label>
    );
});

export default SearchInputElement;
