import { observer } from "mobx-react-lite";
import React, { ReactNode, useMemo } from "react";
import { EnumSearchFilter } from "@components/games-search/_enums";
import Slider from "rc-slider";
import { TGamesSearchStore } from "@components/games-search/games-search-store";

import "rc-slider/assets/index.css";

export interface ICmpSubmenuItemRtpProps {
    store: TGamesSearchStore;
    className?: string;
    onAfterChange: (value: number | number[]) => void;
}

export interface IMarks {
    [id: number]:
        | React.ReactNode
        | {
        style?: React.CSSProperties;
        label?: ReactNode;
    }
}

const SubmenuItemRtp = observer(({ store, className, onAfterChange }: ICmpSubmenuItemRtpProps) => {
    const data = store.getInitialDateFilter(EnumSearchFilter.rtp);
    const values = store.getValueFilter(EnumSearchFilter.rtp);

    const marks: IMarks = useMemo(() => {
        let m = {} as IMarks;
        for (const i of data) {
            m[i as number] = {
                style: {
                    color: "#000"
                },
                label: <strong>{i}</strong>
            };
        }
        return m;
    }, [data]);

    /* https://slider-react-component.vercel.app/ */
    return (
        <div className={"sw-search-range adv-rtp" + (className ? " " + className : "")}>
            <div className="sw-search-range__slider">
                <Slider
                    allowCross={false}
                    // draggableTrack
                    pushable={true}
                    range={true}
                    // dots={true}
                    step={null}
                    included={true}
                    min={parseFloat(String(data[0]))}
                    max={parseFloat(String(data[data.length - 1]))}
                    {...(Array.isArray(values) &&
                        values.length == 2 && { defaultValue: values.map((v) => parseFloat(String(v))) })}
                    marks={marks as any}
                    onChange={onAfterChange}
                    railStyle={{ backgroundColor: "#000", height: 6 }}
                    dotStyle={{ backgroundColor: "#000", height: 10, border: "none" }}
                    activeDotStyle={{ height: 12 }}
                />
            </div>
        </div>
    );
});

export default SubmenuItemRtp;
