import { observer } from "mobx-react-lite";
import React from "react";
import TypeButtons from "@components/games-search/elements/type-buttons";
import { TGamesSearchStore } from "@components/games-search/games-search-store";
import { EnumSearchFilter } from "@components/games-search/_enums";

export interface IcmpSubmenuItemYearProps {
    store: TGamesSearchStore;
    className?: string;
    onClick: (event: React.MouseEvent<HTMLDivElement>) => void;
}

const SubmenuItemYear = observer(({ store, className, onClick }: IcmpSubmenuItemYearProps) => {
    const data = store.getInitialDateFilter(EnumSearchFilter.year);
    const values = store.getValueFilter(EnumSearchFilter.year);

    return (
        <div className={"sw-search-tags adv-year" + (className ? " " + className : "")}>
            <TypeButtons data={data} values={values} onClick={onClick} />
        </div>
    );
});

export default SubmenuItemYear;
