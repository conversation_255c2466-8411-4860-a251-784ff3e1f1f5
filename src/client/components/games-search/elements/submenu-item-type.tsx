import { observer } from "mobx-react-lite";
import React from "react";
import TypeButtons from "@components/games-search/elements/type-buttons";
import { TGamesSearchStore } from "@components/games-search/games-search-store";
import { EnumSearchFilter } from "@components/games-search/_enums";

export interface ICmpSubmenuItemTypeProps {
    store: TGamesSearchStore;
    className?: string;
    onClick: (event: React.MouseEvent<HTMLDivElement>) => void;
}

const SubmenuItemType = observer(({ store, className, onClick }: ICmpSubmenuItemTypeProps) => {
    const data = store.getInitialDateFilter(EnumSearchFilter.type);
    const values = store.getValueFilter(EnumSearchFilter.type);

    return (
        <div className={"sw-search-tags adv-type" + (className ? " " + className : "")}>
            <TypeButtons data={data} values={values} onClick={onClick} />
        </div>
    );
});

export default SubmenuItemType;
