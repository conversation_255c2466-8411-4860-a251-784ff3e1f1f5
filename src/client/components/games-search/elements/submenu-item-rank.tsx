import { observer } from "mobx-react-lite";
import React from "react";
import { EnumSearchFilter } from "@components/games-search/_enums";
import { TGamesSearchStore } from "@components/games-search/games-search-store";

export interface ICmpSubmenuItemRankProps {
    store: TGamesSearchStore;
    className?: string;
    onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const SubmenuItemRank = observer(({ store, className, onChange }: ICmpSubmenuItemRankProps) => {
    const values = store.getValueFilter(EnumSearchFilter.rank);
    return (
        <div
            className={"sw-search-checkboxes-group adv-rank" + (className ? " " + className : "")}
            onChange={onChange}
        >
            <div className="sw-search-checkbox">
                <input
                    type="radio"
                    name="submenuRank"
                    value="10"
                    id="input-rank-10"
                    key="input-rank-10"
                    checked={Array.isArray(values) && values[0] === "10"}
                    onChange={onChange}
                />
                <label htmlFor="input-rank-10" id="label-rank-10">
                    Top 10 ranked games
                </label>
            </div>

            <div className="sw-search-checkbox">
                <input
                    type="radio"
                    name="submenuRank"
                    value="20"
                    id="input-rank-20"
                    key="input-rank-20"
                    checked={Array.isArray(values) && values[0] === "20"}
                    onChange={onChange}
                />
                <label htmlFor="input-rank-20" id="label-rank-20">
                    Top 20 ranked games
                </label>
            </div>

            <div className="sw-search-checkbox">
                <input
                    type="radio"
                    name="submenuRank"
                    value="all"
                    id="input-rank-all"
                    key="input-rank-all"
                    checked={Array.isArray(values) && values[0] === "all"}
                    onChange={onChange}
                />
                <label htmlFor="input-rank-all" id="label-rank-all">
                    All (ordered)
                </label>
            </div>
        </div>
    );
});

export default SubmenuItemRank;
