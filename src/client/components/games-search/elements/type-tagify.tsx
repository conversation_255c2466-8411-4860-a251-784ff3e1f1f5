import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { observer } from "mobx-react-lite";
import Tags from "@yaireo/tagify/dist/react.tagify"; // React-wrapper file
import "@yaireo/tagify/dist/tagify.css"; // Tagify CSS
import { TSearchFilterValue } from "@components/games-search/_interfaces";

interface ITypeTagifyProps {
    className?: string;
    data: TSearchFilterValue;
    values: TSearchFilterValue;
    placeholder: string;
    onChange: (event: unknown) => void;
}

const TypeTagify = observer(
    ({ className, data, values, placeholder, onChange }: ITypeTagifyProps) => {

        const [tagProps, setProps] = useState({});

        const ref = useRef();

        const settings = useMemo(() => ({
            whitelist: data as string[],
            placeholder,
            duplicates: false,
            trim: true,
            maxTags: 6,
            dropdown: {
                // position: "input",
                // maxTags: 5,
                // maxTags: 6,
                maxItems: 500,
                enabled: 0 // always opens dropdown when input gets focus
            }
        }), [data, placeholder]);

        const onChangeHandler = useCallback((e: unknown) => {
            onChange(e);
            // eslint-disable-next-line
        }, []);

        useEffect(() => {
            setProps((lastProps) => ({
                ...lastProps,
                whitelist: data,
                loading: false
            }));
        }, [data]);

        return (
            <Tags
                tagifyRef={ref}
                onChange={onChangeHandler}
                className={className}
                // inner-methods
                settings={settings} // tagify settings object
                defaultValue={values.join(",")}
                showDropdown={true}
                autoFocus={true}
                {...tagProps}
                // {...tagifyProps}   // dynamic props such as "loading", "showDropdown:'abc'", "value"
                // onChange={onChange}
            />
        );
    }
);

export default TypeTagify;
