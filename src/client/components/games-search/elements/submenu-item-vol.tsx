import { observer } from "mobx-react-lite";
import React from "react";
import TypeButtons from "@components/games-search/elements/type-buttons";
import { EnumSearchFilter } from "@components/games-search/_enums";
import { TGamesSearchStore } from "@components/games-search/games-search-store";

export interface ICmpSubmenuItemVolProps {
    store: TGamesSearchStore;
    className?: string;
    onClick: (event: React.MouseEvent<HTMLDivElement>) => void;
}

const SubmenuItemVol = observer(({ store, className, onClick }: ICmpSubmenuItemVolProps) => {
    const data = store.getInitialDateFilter(EnumSearchFilter.vol);
    const values = store.getValueFilter(EnumSearchFilter.vol);

    return (
        <div className={"sw-search-tags adv-vol" + (className ? " " + className : "")}>
            <TypeButtons data={data} values={values} onClick={onClick} />
        </div>
    );
});

export default SubmenuItemVol;
