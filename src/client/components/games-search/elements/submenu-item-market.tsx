import { observer } from "mobx-react-lite";
import React from "react";
import TypeTagify from "@components/games-search/elements/type-tagify";
import { TGamesSearchStore } from "@components/games-search/games-search-store";
import { EnumSearchFilter } from "@components/games-search/_enums";

export interface ICmpSubmenuItemMarketProps {
    store: TGamesSearchStore;
    className?: string;
    onChange: (event: any) => void;
}

const SubmenuItemMarket = observer(({ store, className, onChange }: ICmpSubmenuItemMarketProps) => {
    const data = store.getInitialDateFilter(EnumSearchFilter.market);
    const values = store.getValueFilter(EnumSearchFilter.market);

    return (
        <div className={"sw-search-tags adv-market" + (className ? " " + className : "")}>
            <TypeTagify
                values={values}
                data={data}
                placeholder={"choose from list or start typing"}
                onChange={onChange}
            />
        </div>
    );
});

export default SubmenuItemMarket;
