import React from "react";
import { observer } from "mobx-react-lite";
import { TGamesSearchStore } from "@components/games-search/games-search-store";
import { EnumSearchButton } from "@components/games-search/_enums";

export interface ICmpSearchSubmenuCloseProps {
    store: TGamesSearchStore;
    className: string;
    onClick: (event: React.MouseEvent<HTMLDivElement>) => void;
}

const SearchSubmenuClose = observer(({ store, className, onClick }: ICmpSearchSubmenuCloseProps) => {

    const button = store.getButton(EnumSearchButton.submenuCloseButton);

    if (button?.isVisible) {
        return (
            <div className={className} onClick={onClick}>
                <div>Close</div>
            </div>
        );
    } else {
        return null;
    }
});

export default SearchSubmenuClose;
