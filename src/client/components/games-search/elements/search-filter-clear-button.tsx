import React from "react";
import { observer } from "mobx-react-lite";
import AppImg from "@components/main/appImg";
import { TGamesSearchStore } from "@components/games-search/games-search-store";
import { EnumSearchButton } from "@components/games-search/_enums";

export interface ICmpSearchFilterClearButtonProps {
    store: TGamesSearchStore;
    className: string;
    title: string;
    iconSrc: string;
    onClick: (event: React.MouseEvent<HTMLDivElement>) => void;
}

const SearchFilterClearButton = observer((
    { store, className, title, iconSrc, onClick }: ICmpSearchFilterClearButtonProps) => {
    const button = store.getButton(EnumSearchButton.searchButton);

    if (button?.isVisible) {
        return (
            <div className={className} key={"clearButton"} onClick={onClick}>
                <div className="sw-search-filter__item--logo">
                    <AppImg src={iconSrc} alt={title} />
                </div>
                <div className="sw-search-filter__item--text">{title}</div>
            </div>
        );
    } else {
        return null;
    }
});

export default SearchFilterClearButton;
