import { observer } from "mobx-react-lite";
import React, { ReactNode } from "react";
import { TGamesSearchStore } from "@components/games-search/games-search-store";
import { EnumSearchFilter } from "@components/games-search/_enums";

export interface ICmpSearchFilterSubmenuItemProps {
    store: TGamesSearchStore;
    className?: string;
    type: EnumSearchFilter;
    children: ReactNode;
}

const SearchFilterSubmenuItem = observer(({ store, className, type, children }: ICmpSearchFilterSubmenuItemProps) => {
    const subMenu = store.getSubMenu();

    if (subMenu?.openFilter === type) {
        return (
            <div
                className={"sw-search-submenu-inner open" + (className ? " " + className : "")}
                data-subsearch={"type"}
            >
                {children}
            </div>
        );
    } else {
        return null;
    }
});

export default SearchFilterSubmenuItem;
