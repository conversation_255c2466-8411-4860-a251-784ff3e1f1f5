import React from "react";
import { observer } from "mobx-react-lite";
import { EnumSearchButtonState, EnumSearchFilter } from "client/components/games-search/_enums";
import AppImg from "@components/main/appImg";
import { TGamesSearchStore } from "@components/games-search/games-search-store";
import { toJS } from "mobx";

export interface ICmpSearchFilterButtonProps {
    store: TGamesSearchStore;
    type: EnumSearchFilter;
    className: string;
    title: string;
    iconSrc: string;
    onClick: (event: React.MouseEvent<HTMLDivElement>) => void;
    resetClick: (event: React.MouseEvent<HTMLDivElement>) => void;
}

const SearchFilterButton = observer(
    ({ store, className, title, type, iconSrc, onClick, resetClick }: ICmpSearchFilterButtonProps) => {

        const filters = toJS(store.getFilters());

        if (type in filters && filters[type].state !== EnumSearchButtonState.hidden) {
            return (
                <div
                    className={
                        "sw-search-filter__item adv-button" +
                        (className ? " " + className : "") +
                        (filters[type].state === EnumSearchButtonState.active ? " active" : "") +
                        (filters[type].state === EnumSearchButtonState.disabled ? " search-item-disabled" : "")
                    }
                    data-adv-type={type}
                    key={type}
                    onClick={onClick}
                >
                    <div className="sw-search-filter__item--logo">
                        <AppImg src={iconSrc} alt={title} />
                    </div>
                    <div className="sw-search-filter__item--text">{title}</div>
                    <div className="sw-search-filter__item--close" onClick={resetClick}>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px">
                            <path
                                fill="#8dd363"
                                d="M9.766,0.469 C15.159,0.469 19.531,4.841 19.531,10.234 C19.531,15.628 15.159,20.000 9.766,20.000 C4.372,20.000 0.000,15.628 0.000,10.234 C0.000,4.841 4.372,0.469 9.766,0.469 Z"
                            />
                            <text fill="#1E1D1D" fontWeight="bold" transform="matrix(1.3, 0, 0, 1,4.5, 14)">
                                x
                            </text>
                        </svg>
                    </div>
                </div>
            );
        } else {
            return null;
        }
    }
);

export default SearchFilterButton;
