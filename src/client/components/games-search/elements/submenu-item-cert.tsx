import { observer } from "mobx-react-lite";
import React from "react";
import TypeButtons from "@components/games-search/elements/type-buttons";
import { EnumSearchFilter } from "@components/games-search/_enums";
import { TGamesSearchStore } from "@components/games-search/games-search-store";

export interface ICmpSubmenuItemCertProps {
    store: TGamesSearchStore;
    className?: string;
    onClick: (event: React.MouseEvent<HTMLDivElement>) => void;
}

const SubmenuItemCert = observer(({ store, className, onClick }: ICmpSubmenuItemCertProps) => {
    const data = store.getInitialDateFilter(EnumSearchFilter.cert);
    const values = store.getValueFilter(EnumSearchFilter.cert);

    return (
        <div className={"sw-search-tags adv-certifications" + (className ? " " + className : "")}>
            <TypeButtons data={data} values={values} onClick={onClick} />
        </div>
    );
});

export default SubmenuItemCert;
