import { observer } from "mobx-react-lite";
import React from "react";
import TypeTagify from "@components/games-search/elements/type-tagify";
import { EnumSearchFilter } from "@components/games-search/_enums";
import { TGamesSearchStore } from "@components/games-search/games-search-store";

export interface ICmpSubmenuItemTagProps {
    store: TGamesSearchStore;
    className?: string;
    onChange: (event: unknown) => void;
}

const SubmenuItemTag = observer(({ store, className, onChange }: ICmpSubmenuItemTagProps) => {
    const data = store.getInitialDateFilter(EnumSearchFilter.tag);
    const values = store.getValueFilter(EnumSearchFilter.tag);

    return (
        <div className={"sw-search-tags adv-tags" + (className ? " " + className : "")}>
            <TypeTagify
                values={values}
                data={data}
                placeholder={"choose from list or start typing"}
                onChange={onChange}
            />
        </div>
    );
});

export default SubmenuItemTag;
