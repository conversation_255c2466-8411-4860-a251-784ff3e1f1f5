import React, { RefObject } from "react";
import { EnumLanguageCode, EnumRegionCode } from "models/enum/system";
import { IGamesGridRow } from "api/services/interfaces/iGamesService";
import { EnumUserClientAction } from "models/enum/user";
import ClientGamesService from "@services/clientGamesService";
import ClientUsersService from "@services/clientUsersService";
import { Game } from "utils/game";
import { EnumShowGameRTP } from "models/enum/game";
import { EnumLocale, getLocaleMessages } from "locales/locales";
import { ISearchData, ISearchOptions } from "@components/games-search/_interfaces";
import { TGamesSearchStore } from "@components/games-search/games-search-store";
import {
    EnumFilterRankValue,
    EnumSearchButton,
    EnumSearchButtonState,
    EnumSearchFilter,
    EnumSearchFilterType,
    EnumSearchPluginEvents
} from "@components/games-search/_enums";
import { SearchFilter } from "@components/games-search/search-filter";
import GamesSearchEvents from "@components/games-search/games-search-events";

export class GamesSearchClass {
    public options: any;

    public _searchData: ISearchData;
    public _searchStore: TGamesSearchStore;

    public _inputEl: RefObject<HTMLInputElement>;

    public _timeout: NodeJS.Timeout | null = null;

    public events: GamesSearchEvents;

    public filters: { [filterName: string]: SearchFilter } = {};

    // public markets: string[];
    // public gameCode2Market: ISearchGameCode2Market;

    constructor(
        // searchData: ISearchData,
        searchInputElement: RefObject<HTMLInputElement>,
        options: ISearchOptions,
        searchStore: TGamesSearchStore
        // markets: string[],
        // gameCode2Market: ISearchGameCode2Market
    ) {
        const self = this;
        options = options || {};

        this.events = new GamesSearchEvents();

        this._inputEl = searchInputElement;

        this.options = {
            ...{
                DEBUG: false,
                currentLanguageCode: EnumLanguageCode.en,
                currentRegionCode: EnumRegionCode.eu,
                partnerArea: false,
                saveClientAction: false
            } as ISearchOptions,
            ...options
        };

        this._searchData = {} as ISearchData;
        this._searchStore = searchStore;

        const esf = EnumSearchFilter;
        const esb = EnumSearchButtonState;

        searchStore.setFilterButton({ id: esf.market, state: esb.visible });
        searchStore.setFilterButton({ id: esf.rank, state: esb.visible });
        searchStore.setFilterButton({ id: esf.type, state: esb.visible });
        searchStore.setFilterButton({ id: esf.vol, state: esb.visible });
        searchStore.setFilterButton({ id: esf.rtp, state: esb.visible });
        searchStore.setFilterButton({ id: esf.cert, state: esb.visible });
        searchStore.setFilterButton({ id: esf.tag, state: esb.visible });
        searchStore.setFilterButton({ id: esf.jackpot, state: esb.visible });
        searchStore.setFilterButton({ id: esf.year, state: esb.visible });

        searchStore.setClearFiltersButton({ isVisible: true, isActive: false });
        searchStore.setSubmenuCloseButton({ isVisible: false });
        searchStore.setSearchButton({ isVisible: true });
        searchStore.setCloseButton({ isVisible: false });

        // this._text = {
        //     tagsPlaceholder: self.searchEl.dataset("tagsPlaceholder") ?? "choose from list or start typing",
        //     gotoTop: self.searchEl.data("gotoTop") ?? "Go to top",
        //     results: self.searchEl.data("results") ?? "result(s)",
        //     rtp: self.searchEl.data("rtp") ?? "RTP:",
        //     certification: self.searchEl.data("certification") ?? "Certification:",
        // };

        // @ts-ignore
        self.events.on(EnumSearchPluginEvents.beforeFilterChanged, (/*event: CustomEvent*/): void => {
            // const filter: SearchFilter = event.detail;
        });
        // @ts-ignore
        self.events.on(EnumSearchPluginEvents.afterFilterChanged, (event: CustomEvent): void => {
            // const filter: SearchFilter = event.detail;
            const selfFilter: SearchFilter = event.detail;
            const self = selfFilter.scope;

            if (self.options.partnerArea === true) {
                const marketFilter = self.filters[EnumSearchFilter.market];
                const rankFilter = self.filters[EnumSearchFilter.rank];
                if (marketFilter && marketFilter.isActivated()) {
                    if (!rankFilter.isActivated()) {
                        rankFilter.setValue(EnumFilterRankValue.all);
                    }
                } else if (rankFilter.isActivated()) {
                    rankFilter.reset();
                }
                const rankValue = rankFilter.getValues();
                for (const fi in self.filters) {
                    const fo = self.filters[fi];
                    const filterName = fo.getFilterName();
                    if (filterName !== EnumSearchFilter.market && filterName !== EnumSearchFilter.rank) {
                        if (
                            Array.isArray(rankValue) &&
                            rankValue.length === 1 &&
                            rankValue[0] !== EnumFilterRankValue.all
                        ) {
                            if (fo.isActivated()) {
                                fo.reset();
                            }
                            fo.disable();
                        } else {
                            fo.updateState();
                        }
                    }
                }
            }
            self.checkClearFiltersButton();

            clearInterval(self._timeout as NodeJS.Timeout);
            self._timeout = setTimeout(self.showResults.bind(this), 150);
        });

        self.resetSearch();

        self.loadSearchData().then(self.showResults.bind(self));

        return self;
    }

    public arrayEquals(a: Array<any>, b: Array<any>): boolean {
        return (
            Array.isArray(a) && Array.isArray(b) && a.length === b.length && a.every((val, index) => val === b[index])
        );
    }

    public checkClearFiltersButton() {
        this._searchStore.setButton(EnumSearchButton.clearFiltersButton, { isActive: this.isFiltersEnabled() });
    }

    public resetSearch() {
        const self = this;
        this._searchStore.resetValues();
        if (Object.keys(self.filters).length === 0) {
            self.filters = this.getFiltersDefaults();
        } else {
            this.resetFilters();
        }
    }

    public resetFilters() {
        const self = this;
        self.events.trigger(EnumSearchPluginEvents.beforeResetSearch);
        for (const filter in self.filters) {
            if (self.filters.hasOwnProperty(filter)) {
                self.filters[filter].reset();
            }
        }
        this.events.trigger(EnumSearchPluginEvents.afterResetSearch);
    }

    public compareValues(key: string, order = "asc") {
        return function innerSort(a: { [key: string]: string }, b: { [key: string]: string }) {
            if (!a.hasOwnProperty(key) || !b.hasOwnProperty(key)) {
                // property doesn't exist on either object
                return 0;
            }

            const varA = typeof a[key] === "string" ? a[key].toUpperCase() : a[key];
            const varB = typeof b[key] === "string" ? b[key].toUpperCase() : b[key];

            let comparison = 0;
            if (varA > varB) {
                comparison = 1;
            } else if (varA < varB) {
                comparison = -1;
            }

            return order === "desc" ? comparison * -1 : comparison;
        };
    }

    public getFiltersDefaults() {
        const n = EnumSearchFilter;
        const t = EnumSearchFilterType;
        return {
            [n.market]: new SearchFilter(n.market, "Countries", t.multiply, this),
            [n.rank]: new SearchFilter(n.rank, "Rank", t.boolean, this),
            [n.type]: new SearchFilter(n.type, "Type", t.multiply, this),
            [n.vol]: new SearchFilter(n.vol, "Volatility", t.multiply, this),
            [n.rtp]: new SearchFilter(n.rtp, "RTP", t.range, this),
            [n.cert]: new SearchFilter(n.cert, "Certifications", t.multiply, this),
            [n.tag]: new SearchFilter(n.tag, "Tags", t.multiply, this),
            [n.jackpot]: new SearchFilter(n.jackpot, "Jackpot", t.boolean, this),
            [n.year]: new SearchFilter(n.year, "Release year", t.multiply, this)
        };
    }

    public openSearch() {
        if (!this.isToolbarVisible()) {
            this.events.trigger(EnumSearchPluginEvents.beforeOpenSearch);
            this.resetSearch();
            this._searchStore = { ...this._searchStore, isToolbarVisible: true };
            this._inputEl.current?.focus();
            // bodyScrollLock.disableBodyScroll(this.searchEl[0]);
            this.events.trigger(EnumSearchPluginEvents.afterOpenSearch);
        }
    }

    public closeSubMenu() {
        this._searchStore.setSubMenu({ openFilter: null });
        this._searchStore.setButton(EnumSearchButton.submenuCloseButton, { isVisible: false });
    }

    public openSubMenu(type: EnumSearchFilter) {
        this._searchStore.setSubMenu({ openFilter: type });
        this._searchStore.setButton(EnumSearchButton.submenuCloseButton, { isVisible: true });
    }

    public handleSubmenuRadioChangeEvent(
        event: React.ChangeEvent<HTMLInputElement>,
        filterName: EnumSearchFilter
    ): void {
        event.stopPropagation();
        //        event.preventDefault();
        const val = String(event.target.value);
        this.filters[filterName].setValue(val, true);
    }

    public handleInputChange(event: React.ChangeEvent<HTMLInputElement>): void {
        this._searchStore.setSearchInput(event.target.value);
        this.showResults();
    }

    public async handleSubmenuRangeChange(value: number | number[], filterName: EnumSearchFilter): Promise<void> {
        this.filters[filterName].setValue(value, true);
        this.showResults();
    }

    public async handleSubmenuTagsChangeEvent(
        event: any,
        filterName: EnumSearchFilter
    ): Promise<void> {
        // event.stopPropagation();
        //        event.preventDefault();
        let v = [];
        let tV;
        try {
            tV = JSON.parse(event.detail.value);
        } catch (e) {
        }
        if (Array.isArray(tV) && tV.length > 0) {
            v = tV;
        }
        const val = v.map((v) => v.value);
        this.filters[filterName].setValue(val, true);
    }

    public handleSubmenuItemClickEvent(event: React.MouseEvent<HTMLDivElement>, filterName: EnumSearchFilter): void {
        event.stopPropagation();
        // event.preventDefault();
        const val = String(event.currentTarget.dataset.val);
        if (this._searchStore.getValueFilter(filterName).indexOf(val) === -1) {
            this.filters[filterName].setValue(val);
        } else {
            this.filters[filterName].deleteValue(val);
        }
    }

    public handleFilterClearButtonClickEvent(event: React.MouseEvent<HTMLDivElement>): void {
        event.stopPropagation();
        event.preventDefault();
        this.resetFilters();
    }

    public handleFilterResetClickEvent(event: React.MouseEvent<HTMLDivElement>, filterName: EnumSearchFilter): void {
        event.stopPropagation();
        event.preventDefault();
        const filter = this.filters[filterName];

        if (filter) {
            filter.reset();
        }
    }

    public handleFilterButtonClickEvent(event: React.MouseEvent<HTMLDivElement>, filterName: EnumSearchFilter): void {
        event.stopPropagation();
        event.preventDefault();
        const filter = this._searchStore.getFilter(filterName);

        if (filter.state == EnumSearchButtonState.visible || filter.state == EnumSearchButtonState.active) {
            this.openSubMenu(filterName);
        }
    }

    public handleSubmenuCloseClickEvent(event: React.MouseEvent<HTMLDivElement>): void {
        event.stopPropagation();
        event.preventDefault();
        this.closeSubMenu();
    }

    public handleCloseSearchClickEvent(event: React.MouseEvent<HTMLDivElement>): void {
        event.stopPropagation();
        event.preventDefault();
        this.closeSubMenu();
        this.closeSearch();
    }

    public closeSearch() {
        if (this.isToolbarVisible()) {
            this.events.trigger(EnumSearchPluginEvents.beforeCloseSearch);
            this._searchStore = { ...this._searchStore, isToolbarVisible: false };
            // bodyScrollLock.clearAllBodyScrollLocks();
            this.events.trigger(EnumSearchPluginEvents.afterCloseSearch);
        }
    }

    public isToolbarVisible() {
        return this._searchStore.isToolbarVisible;
    }

    public isFiltersEnabled() {
        for (let filterName in this.filters) {
            if (this.filters.hasOwnProperty(filterName) && this.filters[filterName].isActivated()) {
                return true;
            }
        }
        return false;
    }

    public getFilters() {
        return this.filters;
    }

    public async loadSearchData() {
        const self = this;

        self._searchData = await ClientGamesService.getSearchGames();

        if (self._searchData?.hasOwnProperty("search")) {
            if (self.options?.partnerArea === true) {
                // let markets: string[] = [];
                // self._searchData.games.map((g) => {
                //     const m = String(g.markets)
                //         .trim()
                //         .split(",")
                //         .map((c) => {
                //             if (c.trim() !== "") {
                //                 return c.trim();
                //             }
                //         });
                //     // @ts-ignore
                //     markets = Array.from(new Set(m.filter((c) => c !== "null").concat(markets)));
                // });
                // markets.sort();
                self._searchData.search[EnumSearchFilter.market] = this._searchData?.markets;
                self._searchData.search[EnumSearchFilter.rank] = Object.values(EnumFilterRankValue);
            }
            self._searchData.search[EnumSearchFilter.jackpot] = ["true", "false"];

            for (const name in self.filters) {
                if (self.filters.hasOwnProperty(name) && self._searchData.search.hasOwnProperty(name)) {
                    self.filters[name].initFilter(self._searchData.search[name as never]);
                }
            }
            if (self.options.partnerArea === true) {
                //self.filters[EnumSearchFilter.market].setValue("Canada");
                //     self.filters[EnumSearchFilter.rank].setValue(EnumFilterRankValue.v10);
            }
        }
    }

    public showResults() {
        const self = this;

        const gc2Market = this._searchData?.gameCode2Market;

        const msg = getLocaleMessages(EnumLocale.enUS).messages;

        if (self.options.partnerArea === true) {
            // $(".sw-search-filter-values").text("");
            // $('.sw-search.search-partners').height(
            //     $('#partners-search .sw-search-container__result').height() + 200
            // );
            // const StickySidebar = $('#partners-search').data('StickySidebar');
            //
            // if (StickySidebar) {
            //     StickySidebar.updateSticky();
            // }
        }

        if (!self._searchData || !("games" in self._searchData)) {
            return;
        }

        const values = this._searchStore.values;

        const searchInputValue = this._searchStore.getValueSearchInput().toString().trim();
        const tempResults = [] as IGamesGridRow[];

        for (let game of self._searchData.games) {
            let item = null;
            if (searchInputValue.length > 0) {
                const needleRegExp = new RegExp(searchInputValue, "i");
                if (game.gameName.match(needleRegExp)) {
                    item = game;
                } else {
                    continue;
                }
            }
            if (self.isFiltersEnabled()) {
                let founded = true;
                item = game as IGamesGridRow;
                item["rank"] = 0.00001;
                for (const name in self.filters) {
                    if (self.filters.hasOwnProperty(name) && self.filters[name].isActivated()) {
                        let filter: SearchFilter = self.filters[name];
                        const filterName = filter.filterName;
                        const filterValues = values.filters[filterName];
                        if (filter.type === EnumSearchFilterType.multiply) {
                            if (
                                filterName === EnumSearchFilter.type ||
                                filterName === EnumSearchFilter.vol ||
                                filterName === EnumSearchFilter.year
                            ) {
                                if (
                                    !game[filterName] ||
                                    filterValues.indexOf(
                                        String(game[filterName]) + (filterName === EnumSearchFilter.vol ? "/5" : "")
                                    ) === -1
                                ) {
                                    founded = false;
                                    break;
                                }
                            }
                            const gameCode = Game.getPlayGameCode(game.gameCode);
                            if (filter.filterName === EnumSearchFilter.market && gc2Market && gc2Market[gameCode]) {
                                const filterData = Object.keys(gc2Market[gameCode]);
                                if (filterData.length > 0) {
                                    for (let val of filterValues) {
                                        const itemRank = parseFloat(String(gc2Market[gameCode][String(val)]));
                                        if (!item?.rank || itemRank > item.rank) {
                                            item.rank = itemRank;
                                        }
                                        if (filterData.indexOf(val.toString()) === -1) {
                                            founded = false;
                                            break;
                                        }
                                    }
                                }
                            }
                            if (filterName === EnumSearchFilter.tag || filterName === EnumSearchFilter.cert) {
                                if (Array.isArray(game[filterName]) && game[filterName].length > 0) {
                                    for (let val of filterValues) {
                                        if (game[filterName].indexOf(String(val).toLowerCase()) === -1) {
                                            founded = false;
                                            break;
                                        }
                                    }
                                } else {
                                    founded = false;
                                    break;
                                }
                            }
                        } else if (
                            filter.type === EnumSearchFilterType.range &&
                            filterValues.length === 2 &&
                            filterValues[0] !== null &&
                            filterValues[1] !== null
                        ) {
                            let gameFloat = null;
                            let rangeData = [];
                            if (filter.filterName === EnumSearchFilter.rtp) {
                                gameFloat = Game.formatRTP(game.rtp,
                                    game.options.showGameRTP as EnumShowGameRTP,
                                    msg,
                                    " ",
                                    "");
                                rangeData = gameFloat.split(" ");
                            } else {
                                gameFloat = parseFloat(String(game[filter.filterName as never]));
                                gameFloat = gameFloat < 1 ? gameFloat * 100 : gameFloat;
                                rangeData = [gameFloat];
                            }
                            founded = false;
                            for (const gameFloat2 of rangeData) {
                                gameFloat = parseFloat(gameFloat2.toString());
                                founded = founded || (
                                    gameFloat >= parseFloat(filterValues[0].toString()) &&
                                    gameFloat <= parseFloat(filterValues[1].toString())
                                );
                            }
                        } else if (
                            filter.type === EnumSearchFilterType.boolean &&
                            filterName === EnumSearchFilter.jackpot &&
                            filterValues.length === 1 &&
                            typeof filterValues[0] === "string"
                        ) {
                            if (game[filterName] !== filterValues[0]) {
                                founded = false;
                                break;
                            }
                        }
                    }
                }
                if (!founded) {
                    item = null;
                }
            }
            if (item !== null) {
                tempResults.push(item);
            }
        }
        if (searchInputValue.length === 0 && !self.isFiltersEnabled()) {
            self._searchStore.setSearchResults(self._searchData.games);
        } else {
            self._searchStore.setSearchResults(tempResults);
        }

        if (self.options.partnerArea === true) {
            // const results = self._flexSearch.find(value, 20);
            const rankNumbers = {
                [EnumFilterRankValue.v10]: 10,
                [EnumFilterRankValue.v20]: 20,
                [EnumFilterRankValue.all]: 100000
            };
            if (tempResults.length > 0 && this.filters[EnumSearchFilter.market].isActivated()) {
                tempResults.sort(self.compareValues("rank", "desc") as never);
            }
            if (tempResults.length > 0 && self.filters[EnumSearchFilter.rank].isActivated()) {
                const rankValue = self._searchStore.getValueFilter(EnumSearchFilter.rank)[0];
                if (rankNumbers[rankValue as never]) {
                    tempResults.splice(rankNumbers[rankValue as never]);
                }
            }

            const filtersText = [];
            const filterValues: { [key: string]: any } = {};
            for (const fName in self.filters) {
                if (
                    self.filters.hasOwnProperty(fName) &&
                    self.filters[fName].isActivated() &&
                    Array.isArray(self._searchStore.getValueFilter(fName as EnumSearchFilter))
                ) {
                    const cFilter = self.filters[fName];
                    const text = cFilter.title.toUpperCase();
                    const fValue = JSON.parse(JSON.stringify(self._searchStore.getValueFilter(fName as EnumSearchFilter)));
                    filterValues[fName] = fValue;
                    // if (self.filter.filters[fName].type === EnumFilterType.boolean) {
                    //     const $booleanLabel = $("#label-" + fName + "-" + fValue[0]);
                    //     if ($booleanLabel && $booleanLabel.length === 1) {
                    //         fValue[0] = $booleanLabel.text();
                    //     }
                    // }
                    filtersText.push(`${text}: ${fValue.join(",")}`);
                }
            }
            filtersText.push(`Found: ${self._searchStore.searchResults.length} Games`);
            self._searchStore.setSearchResultsText(filtersText.join(", "), filterValues);

            if (this.options.saveClientAction) {
                (async () => {
                    await ClientUsersService.clientAction(EnumUserClientAction.search, location.pathname, {
                        searchString: self._searchStore.getValueSearchInput(),
                        filters: self._searchStore.searchFilterValues,
                        results: self._searchStore.searchResultsText
                    });
                })();
            }
        }

        // if (tempResults.length > 0 && self._searchResults.length === tempResults.length) {
        //     let changes = false;
        //     for (let ind = 0; ind < tempResults.length; ind++) {
        //         if (tempResults[ind].gameCode !== self._searchResults[ind].gameCode) {
        //             changes = true;
        //         }
        //     }
        //     if (!changes) {
        //         return;
        //     }
        // }

        // if (self.options.partnerArea === true) {
        // $('.sw-search.search-partners').height(
        //     $('#partners-search .sw-search-container__result').height() + 500
        // );
        // const StickySidebar = $('#partners-search').data('StickySidebar');
        // $([document.documentElement, document.body]).animate({
        //     scrollTop: $('.search-partners .sw-search-container').offset().top - 300
        // }, 200);
        // if (StickySidebar) {
        // StickySidebar.updateSticky();
        // }
        // }
    }
}
