import React, { useContext, useEffect, useRef, useState } from "react";
import { useRouter } from "next/router";
import { observer } from "mobx-react-lite";
import "rc-slider/assets/index.css";
import { EnumSearchButtonState, EnumSearchFilter, EnumSearchPluginEvents } from "client/components/games-search/_enums";
import { EnumLocale, getLocaleMessages } from "locales/locales";
import { EnumLanguageCode, EnumRegionCode } from "models/enum/system";
import { isMobile, MobileView } from "react-device-detect";
import BannersCarousel from "@components/banners/bannersCarousel";
import GameTable from "@components/games-table/gameTable";
import { GamesSearchClass } from "@components/games-search/games-search-class";
import GamesSearchStore from "@components/games-search/games-search-store";
import SearchButtonClose from "@components/games-search/elements/search-button-close";
import SearchFilterClearButton from "@components/games-search/elements/search-filter-clear-button";
import SearchSubmenuClose from "@components/games-search/elements/search-submenu-close";
import SearchInputElement from "@components/games-search/elements/search-input-element";
import SearchFilterButton from "@components/games-search/elements/search-filter-button";
import SearchFilterSubmenuItem from "./elements/search-filter-submenu-item";
import SubmenuItemMarket from "./elements/submenu-item-market";
import SubmenuItemRank from "./elements/submenu-item-rank";
import SubmenuItemType from "./elements/submenu-item-type";
import SubmenuItemVol from "./elements/submenu-item-vol";
import SubmenuItemRtp from "./elements/submenu-item-rtp";
import SubmenuItemCert from "./elements/submenu-item-cert";
import SubmenuItemTag from "./elements/submenu-item-tag";
import SubmenuItemJackpot from "./elements/submenu-item-jackpot";
import SubmenuItemYear from "./elements/submenu-item-year";
import { ICmpGamesSearchTableProps } from "@components/games-search/_interfaces";

let search: GamesSearchClass | null = null;

const GamesSearchTable = observer(({ bannersCarousel, id }: ICmpGamesSearchTableProps) => {
    const [showFilters, setShowFilters] = useState<boolean>(!isMobile);

    const virtuoso = useRef(null);
    const [virtuosoAlign /*, setVirtuosoAlign*/] = useState("start");
    const [virtuosoBehavior /*, setVirtuosoBehavior*/] = useState("auto");

    const strictModeRef = useRef<boolean>(false);

    useEffect(() => {
        if (!strictModeRef.current) {
            strictModeRef.current = true;
            if (search?.resetSearch) {
                search?.resetSearch();
            }
        }
    }, []);

    const searchInputElement = useRef<HTMLInputElement>(null);

    const searchStore = useContext(GamesSearchStore);
    if (!search) {
        search = new GamesSearchClass(
            searchInputElement,
            {
                DEBUG: false,
                currentLanguageCode: EnumLanguageCode.en,
                currentRegionCode: EnumRegionCode.eu,
                partnerArea: true,
                saveClientAction: true
            },
            searchStore
        );

        search.events.on(EnumSearchPluginEvents.afterFilterChanged, (/*event: CustomEvent*/): void => {
            setTimeout(() => {
                // @ts-ignore
                virtuoso?.current?.scrollToIndex({
                    index: 0,
                    align: virtuosoAlign,
                    behavior: virtuosoBehavior
                });
            }, 500);
        });
    }

    const router = useRouter();
    const locale = router.locale;
    const msg = getLocaleMessages(locale as EnumLocale).components.search;

    const submenuCloseBtn = searchStore.buttons.submenuCloseButton;
    const filtersClearBtn = searchStore.buttons.clearFiltersButton;

    return (
        <div id={id}>

            <div
                className="sw-search search-partners"
            >
                <div className="sw-search-container search-col">
                    <div className="sw-search-container__menu">
                        <div className="sw-search-control">
                            <SearchInputElement
                                searchInputRef={searchInputElement}
                                store={searchStore}
                                inputElClassName={"sw-search__input"}
                                className={"sw-search__input-label"}
                                placeHolder={msg.inputPlaceHolder}
                                onChange={(event) => {
                                    search?.handleInputChange(event);
                                }}
                            />
                            <SearchButtonClose store={searchStore} className={"sw-search__close"} />
                            <MobileView>
                                <div
                                    className="btn btn-light btn-sm f_filters_btn"
                                    role="button"
                                    onClick={() => {
                                        setShowFilters(!showFilters);
                                    }}
                                >
                                    {showFilters ? "Hide" : "Show"} Filters
                                </div>
                            </MobileView>
                        </div>

                        {showFilters && (
                            <>
                                <div className={"sw-search-submenu" + (submenuCloseBtn?.isVisible ? " open" : "")}>
                                    <div className="sw-search-submenu__types">
                                        <SearchFilterSubmenuItem store={searchStore} type={EnumSearchFilter.market}>
                                            <SubmenuItemMarket
                                                store={searchStore}
                                                onChange={(event) => {
                                                    search?.handleSubmenuTagsChangeEvent(
                                                        event,
                                                        EnumSearchFilter.market
                                                    );
                                                }}
                                            />
                                        </SearchFilterSubmenuItem>
                                        <SearchFilterSubmenuItem store={searchStore} type={EnumSearchFilter.rank}>
                                            <SubmenuItemRank
                                                store={searchStore}
                                                onChange={(event) => {
                                                    search?.handleSubmenuRadioChangeEvent(
                                                        event,
                                                        EnumSearchFilter.rank
                                                    );
                                                }}
                                            />
                                        </SearchFilterSubmenuItem>
                                        <SearchFilterSubmenuItem store={searchStore} type={EnumSearchFilter.type}>
                                            <SubmenuItemType
                                                store={searchStore}
                                                onClick={(event) => {
                                                    search?.handleSubmenuItemClickEvent(
                                                        event,
                                                        EnumSearchFilter.type
                                                    );
                                                }}
                                            />
                                        </SearchFilterSubmenuItem>
                                        <SearchFilterSubmenuItem store={searchStore} type={EnumSearchFilter.vol}>
                                            <SubmenuItemVol
                                                store={searchStore}
                                                onClick={(event) => {
                                                    search?.handleSubmenuItemClickEvent(
                                                        event,
                                                        EnumSearchFilter.vol
                                                    );
                                                }}
                                            />
                                        </SearchFilterSubmenuItem>
                                        <SearchFilterSubmenuItem store={searchStore} type={EnumSearchFilter.rtp}>
                                            <SubmenuItemRtp
                                                store={searchStore}
                                                onAfterChange={(value) => {
                                                    search?.handleSubmenuRangeChange(value, EnumSearchFilter.rtp);
                                                }}
                                            />
                                        </SearchFilterSubmenuItem>
                                        <SearchFilterSubmenuItem store={searchStore} type={EnumSearchFilter.cert}>
                                            <SubmenuItemCert
                                                store={searchStore}
                                                onClick={(event) => {
                                                    search?.handleSubmenuItemClickEvent(
                                                        event,
                                                        EnumSearchFilter.cert
                                                    );
                                                }}
                                            />
                                        </SearchFilterSubmenuItem>
                                        <SearchFilterSubmenuItem store={searchStore} type={EnumSearchFilter.tag}>
                                            <SubmenuItemTag
                                                store={searchStore}
                                                onChange={(event) => {
                                                    search?.handleSubmenuTagsChangeEvent(
                                                        event,
                                                        EnumSearchFilter.tag
                                                    );
                                                }}
                                            />
                                        </SearchFilterSubmenuItem>
                                        <SearchFilterSubmenuItem
                                            store={searchStore}
                                            type={EnumSearchFilter.jackpot}
                                        >
                                            <SubmenuItemJackpot
                                                store={searchStore}
                                                onChange={(event) => {
                                                    search?.handleSubmenuRadioChangeEvent(
                                                        event,
                                                        EnumSearchFilter.jackpot
                                                    );
                                                }}
                                            />
                                        </SearchFilterSubmenuItem>
                                        <SearchFilterSubmenuItem store={searchStore} type={EnumSearchFilter.year}>
                                            <SubmenuItemYear
                                                store={searchStore}
                                                onClick={(event) => {
                                                    search?.handleSubmenuItemClickEvent(
                                                        event,
                                                        EnumSearchFilter.year
                                                    );
                                                }}
                                            />
                                        </SearchFilterSubmenuItem>
                                    </div>
                                    <SearchSubmenuClose
                                        store={searchStore}
                                        className="sw-search-submenu__close"
                                        onClick={(event) => {
                                            search?.handleSubmenuCloseClickEvent(event);
                                        }}
                                    />
                                </div>

                                <div className="sw-search-filter">
                                    <div
                                        style={{
                                            position: "absolute",
                                            fontFamily: "inherit",
                                            fontSize: 14,
                                            fontWeight: 600,
                                            left: 22,
                                            top: 4,
                                            opacity: 0.5,
                                            textTransform: "uppercase"
                                        }}
                                    >
                                        Filter By:
                                    </div>
                                    <SearchFilterButton
                                        title={msg.filterMarketTitle}
                                        type={EnumSearchFilter.market}
                                        className={"adv-" + EnumSearchFilter.market}
                                        store={searchStore}
                                        iconSrc={"/assets/pa/images/search/countries.png"}
                                        onClick={(event) => {
                                            search?.handleFilterButtonClickEvent(event, EnumSearchFilter.market);
                                        }}
                                        resetClick={(event) => {
                                            search?.handleFilterResetClickEvent(event, EnumSearchFilter.market);
                                        }}
                                    />
                                    {searchStore.filters[EnumSearchFilter.market].state ===
                                        EnumSearchButtonState.active && (
                                            <SearchFilterButton
                                                title={msg.filterRankTitle}
                                                type={EnumSearchFilter.rank}
                                                className={"adv-" + EnumSearchFilter.rank}
                                                store={searchStore}
                                                iconSrc={"/assets/pa/images/search/rank.png"}
                                                onClick={(event) => {
                                                    search?.handleFilterButtonClickEvent(event,
                                                        EnumSearchFilter.rank);
                                                }}
                                                resetClick={(event) => {
                                                    search?.handleFilterResetClickEvent(event,
                                                        EnumSearchFilter.rank);
                                                }}
                                            />
                                        )}
                                    <SearchFilterButton
                                        title={msg.filterTypeTitle}
                                        type={EnumSearchFilter.type}
                                        className={"adv-" + EnumSearchFilter.type}
                                        store={searchStore}
                                        iconSrc={"/assets/pa/images/search/type.png"}
                                        onClick={(event) => {
                                            search?.handleFilterButtonClickEvent(event, EnumSearchFilter.type);
                                        }}
                                        resetClick={(event) => {
                                            search?.handleFilterResetClickEvent(event, EnumSearchFilter.type);
                                        }}
                                    />
                                    <SearchFilterButton
                                        title={msg.filterVolatilityTitle}
                                        type={EnumSearchFilter.vol}
                                        className={"adv-" + EnumSearchFilter.vol}
                                        store={searchStore}
                                        iconSrc={"/assets/pa/images/search/volatility.png"}
                                        onClick={(event) => {
                                            search?.handleFilterButtonClickEvent(event, EnumSearchFilter.vol);
                                        }}
                                        resetClick={(event) => {
                                            search?.handleFilterResetClickEvent(event, EnumSearchFilter.vol);
                                        }}
                                    />
                                    <SearchFilterButton
                                        title={msg.filterRTPTitle}
                                        type={EnumSearchFilter.rtp}
                                        className={"adv-" + EnumSearchFilter.rtp}
                                        store={searchStore}
                                        iconSrc={"/assets/pa/images/search/rtp.png"}
                                        onClick={(event) => {
                                            search?.handleFilterButtonClickEvent(event, EnumSearchFilter.rtp);
                                        }}
                                        resetClick={(event) => {
                                            search?.handleFilterResetClickEvent(event, EnumSearchFilter.rtp);
                                        }}
                                    />
                                    <SearchFilterButton
                                        title={msg.filterCertificationsTitle}
                                        type={EnumSearchFilter.cert}
                                        className={"adv-" + EnumSearchFilter.cert}
                                        store={searchStore}
                                        iconSrc={"/assets/pa/images/search/certifications.png"}
                                        onClick={(event) => {
                                            search?.handleFilterButtonClickEvent(event, EnumSearchFilter.cert);
                                        }}
                                        resetClick={(event) => {
                                            search?.handleFilterResetClickEvent(event, EnumSearchFilter.cert);
                                        }}
                                    />
                                    <SearchFilterButton
                                        title={msg.filterTagsTitle}
                                        type={EnumSearchFilter.tag}
                                        className={"adv-" + EnumSearchFilter.tag}
                                        store={searchStore}
                                        iconSrc={"/assets/pa/images/search/tags.png"}
                                        onClick={(event) => {
                                            search?.handleFilterButtonClickEvent(event, EnumSearchFilter.tag);
                                        }}
                                        resetClick={(event) => {
                                            search?.handleFilterResetClickEvent(event, EnumSearchFilter.tag);
                                        }}
                                    />
                                    <SearchFilterButton
                                        title={msg.filterJackpotsTitle}
                                        type={EnumSearchFilter.jackpot}
                                        className={"adv-" + EnumSearchFilter.jackpot}
                                        store={searchStore}
                                        iconSrc={"/assets/pa/images/search/jackpots.png"}
                                        onClick={(event) => {
                                            search?.handleFilterButtonClickEvent(event, EnumSearchFilter.jackpot);
                                        }}
                                        resetClick={(event) => {
                                            search?.handleFilterResetClickEvent(event, EnumSearchFilter.jackpot);
                                        }}
                                    />
                                    <SearchFilterButton
                                        title={msg.filterReleaseYearTitle}
                                        type={EnumSearchFilter.year}
                                        className={"adv-" + EnumSearchFilter.year}
                                        store={searchStore}
                                        iconSrc={"/assets/pa/images/search/release-year.png"}
                                        onClick={(event) => {
                                            search?.handleFilterButtonClickEvent(event, EnumSearchFilter.year);
                                        }}
                                        resetClick={(event) => {
                                            search?.handleFilterResetClickEvent(event, EnumSearchFilter.year);
                                        }}
                                    />
                                    <SearchFilterClearButton
                                        store={searchStore}
                                        className={
                                            "sw-search-filter__item clear-all-filter" +
                                            (filtersClearBtn?.isActive ? " active" : "")
                                        }
                                        title={msg.filterClearTitle}
                                        iconSrc={"/assets/pa/images/search/close.png"}
                                        onClick={(event) => {
                                            search?.handleFilterClearButtonClickEvent(event);
                                        }}
                                    />
                                </div>
                            </>
                        )}
                    </div>
                    <div className="sw-search-filter-values">[{searchStore.searchResultsText}]</div>
                </div>
                {bannersCarousel && (
                    <BannersCarousel
                        className={"roadmap-banner search-col search-banner"}
                        bannersCarousel={bannersCarousel}
                    />
                )}
            </div>

            <GameTable games={searchStore.searchResults} fixed={true} virtuoso={virtuoso} />
        </div>
    );
});

export default GamesSearchTable;
