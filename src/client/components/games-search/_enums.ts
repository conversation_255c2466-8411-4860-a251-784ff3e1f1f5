export enum EnumSearchFilterType {
    boolean = "boolean",
    range = "range",
    multiply = "multiply",
}

export enum EnumFilterRankValue {
    v10 = "10",
    v20 = "20",
    all = "all",
}

export enum EnumSearchFilter {
    market = "market",
    rank = "rank",
    type = "type",
    vol = "vol",
    rtp = "rtp",
    cert = "cert",
    tag = "tag",
    jackpot = "jackpot",
    year = "year",
}

export enum EnumSearchButton {
    searchButton = "searchButton",
    closeButton = "closeButton",
    clearFiltersButton = "clearFiltersButton",
    submenuCloseButton = "submenuCloseButton"
}

export enum EnumSearchPluginEvents {
    beforeFilterChanged = "beforeFilterValueChanged",
    afterFilterChanged = "afterFilterValueChanged",
    beforeFilterReset = "beforeFilterReset",
    afterFilterReset = "afterFilterReset",

    beforeOpenSearch = "beforeOpenSearch",
    afterOpenSearch = "afterOpenSearch",

    beforeCloseSearch = "beforeCloseSearch",
    afterCloseSearch = "afterCloseSearch",

    beforeResetSearch = "beforeResetSearch",
    afterResetSearch = "afterResetSearch",
}

export enum EnumSearchButtonState {
    hidden = "hidden",
    visible = "visible",
    disabled = "disabled",
    active = "active",
}