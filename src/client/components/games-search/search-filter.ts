import {
    EnumSearchButtonState,
    EnumSearchFilter,
    EnumSearchFilterType,
    EnumSearchPluginEvents
} from "client/components/games-search/_enums";
import { GamesSearchClass } from "@components/games-search/games-search-class";

export class SearchFilter {
    public type: EnumSearchFilterType;
    public filterName: EnumSearchFilter;
    public scope: GamesSearchClass;
    public title: string;

    constructor(name: EnumSearchFilter, title: string, type: EnumSearchFilterType, scope: GamesSearchClass) {
        const self = this;

        this.scope = scope;

        this.title = title;
        this.type = type;
        this.filterName = name;
        this.scope._searchStore.setFilterButtonState(name, EnumSearchButtonState.visible);
        this.scope._searchStore.setValueFilter(name, self.getDefaultValue());
        this.scope._searchStore.setInitialDateFilter(name, self.getDefaultValue());
        // @ts-ignore
        this.scope.events.on(EnumSearchPluginEvents.afterFilterChanged, (event: CustomEvent): void => {
            const filter: SearchFilter = event.detail;

            if (filter && filter.getFilterName() === self.filterName) {
                self.updateItems();
            }
        });
    }

    public updateItems() {
        const self = this;
        self.updateState();
    }

    public getDefaultValue(): Array<number | string | boolean> {
        return [];
    }

    public isActivated(): boolean {
        return this.scope._searchStore.filters[this.filterName].state === EnumSearchButtonState.active;
    }

    public isEnabled(): boolean {
        const state = this.scope._searchStore.filters[this.filterName].state;
        return state !== EnumSearchButtonState.disabled && state !== EnumSearchButtonState.hidden;
    }

    public enable() {
        this.scope._searchStore.setFilterButtonState(this.filterName, EnumSearchButtonState.visible);
    }

    public disable() {
        this.scope._searchStore.setFilterButtonState(this.filterName, EnumSearchButtonState.disabled);
    }

    public activate() {
        this.scope._searchStore.setFilterButtonState(this.filterName, EnumSearchButtonState.active);
    }

    public updateState() {
        const self = this;
        let isActivated: boolean = false;
        const values = this.scope._searchStore.getValueFilter(this.filterName);

        if (Array.isArray(values)) {
            if (self.type === EnumSearchFilterType.range) {
                isActivated = values.length == 2 && values[0] !== null && values[1] !== null;
            } else {
                isActivated = values.length > 0;
            }
        }
        if (isActivated) {
            self.activate();
        } else {
            self.enable();
        }
    }

    public deleteValue(delValue?: string) {
        const self = this;
        const value = self.getValues();
        if (!delValue || (Array.isArray(value) && value.indexOf(delValue.toString()) !== -1)) {
            self.scope.events.trigger(EnumSearchPluginEvents.beforeFilterChanged, this);
            if (self.type === EnumSearchFilterType.boolean) {
                this.scope._searchStore.setValueFilter(this.filterName, []);
            } else if (self.type === EnumSearchFilterType.range) {
                this.scope._searchStore.setValueFilter(this.filterName, []);
            } else {
                const v = [...this.scope._searchStore.getValueFilter(this.filterName)];
                this.scope._searchStore.setValueFilter(this.filterName, v.splice(v.indexOf(String(delValue)), 1));
            }
            self.updateState();
            self.scope.events.trigger(EnumSearchPluginEvents.afterFilterChanged, self);
        }
    }

    public setValue(
        value: number | string | boolean | Array<number | string | boolean>,
        eraseBeforeSet = false
    ): SearchFilter {
        const self = this;
        if (eraseBeforeSet) {
            this.scope._searchStore.setValueFilter(this.filterName,[]);
        }
        const addValue = (v: number | string | boolean | Array<number | string | boolean>): void => {
            const initialData = this.scope._searchStore.getInitialDateFilter(this.filterName);
            const values = this.scope._searchStore.getValueFilter(this.filterName);
            if (self.type === EnumSearchFilterType.boolean) {
                if (["string", "number", "boolean"].includes(typeof v)) {
                    this.scope._searchStore.setValueFilter(this.filterName, [String(v)]);
                }
            } else {
                if (Array.isArray(v)) {
                    v.map((nv) => addValue(nv));
                } else if (["string", "number", "boolean"].includes(typeof v)) {
                    if (initialData.indexOf(String(v)) !== -1 && values.indexOf(String(v)) === -1) {
                        const vf = [...this.scope._searchStore.getValueFilter(this.filterName)];
                        vf.push(String(v))
                        this.scope._searchStore.setValueFilter(this.filterName, vf);
                    }
                }
            }
        };

        self.scope.events.trigger(EnumSearchPluginEvents.beforeFilterChanged, this);
        addValue(value);
        self.updateState();
        self.scope.events.trigger(EnumSearchPluginEvents.afterFilterChanged, self);
        return self;
    }

    public getFilterName(): string {
        return this.filterName;
    }

    public getValues(): Array<number | string | boolean> {
        return this.scope._searchStore.getValueFilter(this.filterName);
    }

    public reset() {
        const self = this;
        self.scope.events.trigger(EnumSearchPluginEvents.beforeFilterReset, this);
        self.scope.events.trigger(EnumSearchPluginEvents.beforeFilterChanged, this);
        this.scope._searchStore.setValueFilter(this.filterName, this.getDefaultValue());
        self.updateState();
        self.scope.events.trigger(EnumSearchPluginEvents.afterFilterChanged, self);
        self.scope.events.trigger(EnumSearchPluginEvents.afterFilterReset, this);
    }

    public initFilter(data: string[]) {
        const self = this;
        data = data.map(function(s) {
            return s.toString();
        });
        self.scope._searchStore.setInitialDateFilter(self.filterName, data);
        // if (self.type === EnumSearchFilterType.range) {
        // self.scope._rtpSlider.reset();
        // if (Array.isArray(data) && data.length > 0) {
        //     const rtp = {
        //         min: data[0],
        //         max: data[data.length - 1],
        //         from: 0,
        //         to: data.length - 1,
        //         grid_num: 1,
        //         values: data,
        //     };
        //     self.scope._rtpSlider.update(rtp);
        // }
        // } else if (self.type === EnumSearchFilterType.multiply) {
        // self.$el.empty();
        //     if (Array.isArray(data)) {
        //         if (data.length > 9) {
        /*const $el = $("<input>").addClass("tagify--outside").appendTo(self.$el);
                    // @ts-ignore
                    self.plugin = new Tagify($el[0], {
                        whitelist: data,
                        placeholder: self.scope._text.tagsPlaceholder,
                        dropdown: {
                            position: "input",
                            maxTags: 5,
                            enabled: 0, // always opens dropdown when input gets focus
                        },
                    })
                        .on("add", function (e) {
                            /!*console.log(e.detail)*!/
                            self.setValue(e.detail.data.value);
                            if (self.plugin.value.length >= 5) {
                                self.$el.find(".tagify__input").hide();
                            } else {
                                self.$el.find(".tagify__input").show();
                            }
                        })
                        .on("remove", function (e) {
                            /!*console.log(e.detail)*!/
                            if (self.plugin.value.length >= 5) {
                                self.$el.find(".tagify__input").hide();
                            } else {
                                self.$el.find(".tagify__input").show();
                            }
                            self.deleteValue(e.detail.data.value);
                        });*/
        // $el.data("data-plugin", self.plugin);
        //         } else {
        //             for (let i = 0; i < data.length; i++) {
        // const $el = $("<div>" + data[i] + "</div>")
        //     .addClass(self.elementCss)
        //     .appendTo(self.$el);
        // $el.attr("data-val", data[i]);
        // $el.on("click", function () {
        //     const $this = $(this),
        //         val = $this.data("val");
        //     $this.toggleClass("active");
        //     if ($this.hasClass("active")) {
        //         self.setValue(val);
        //     } else {
        //         self.deleteValue(val);
        //     }
        // });
        //              }
        //          }
        //     }
        // } else if (self.type === EnumSearchFilterType.boolean) {
        // self.$el
        //     .find("input")
        //     .on("click", function () {
        //         const $this = $(this),
        //             val = $this.val();
        //         if ($(this).is(":checked")) {
        //             self.setValue(val);
        //         }
        //     })
        //     .prop("checked", false);
        // }
    }
}
