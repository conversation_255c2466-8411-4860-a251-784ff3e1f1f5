import React, { useState } from "react";
import { Controller, Keyboard, Mousewheel, Navigation, Parallax, Scrollbar } from "swiper";
import { Swiper, SwiperSlide } from "swiper/react";
import AppImg from "@components/main/appImg";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/scrollbar";
import { Swiper as SwiperClass } from "swiper/types";
import { IAppDialogImage } from "@components/dialogs/iDialogs";

const TripleSlider = ({ images }: { images: IAppDialogImage[] }) => {
    const [prevSwiper, setPrevSwiper] = useState<SwiperClass>();
    const [mainSwiper, setMainSwiper] = useState<SwiperClass>();
    const [nextSwiper, setNextSwiper] = useState<SwiperClass>();
/*
    const slideTo = (index: number) => {
        if (mainSwiper) {
            mainSwiper.slideTo(index - 1, 0);
        }
    };*/

    return (
        <div className={"triple-slider"}>
            {[{ name: "prev" }, { name: "next" }, { name: "main" }].map((s) => {
                const name = "triple-slider-" + s.name;

                return (
                    <Swiper
                        {...(s.name === "prev" && { allowTouchMove: false, onSwiper: setPrevSwiper })}
                        {...(s.name === "main" && {
                            grabCursor: true,
                            controller: { control: [prevSwiper as SwiperClass, nextSwiper as SwiperClass] },
                            onSwiper: setMainSwiper,
                            on: {
                                destroy() {
                                    prevSwiper?.destroy();
                                    nextSwiper?.destroy();
                                },
                            },
                        })}
                        {...(s.name === "next" && { allowTouchMove: false, onSwiper: setNextSwiper })}
                        key={name}
                        modules={[Navigation, Mousewheel, Keyboard, Scrollbar, Parallax, Controller]}
                        className={name}
                        speed={600}
                        loop={true}
                        parallax={true}
                        // pagination={true}
                    >
                        {images.map((i, index) => {
                            return (
                                <SwiperSlide key={index}>
                                    <AppImg src={i.imageUrl} />
                                    <div
                                        className="slide-content"
                                        data-swiper-parallax-x="-50%"
                                        data-swiper-parallax-duration={1000}
                                    >
                                        <h2>{i.title}</h2>
                                        <p>{i.info}</p>
                                    </div>
                                </SwiperSlide>
                            );
                        })}
                    </Swiper>
                );
            })}
        </div>
    );
};

export default TripleSlider;
