import React, { useContext } from "react";
import { EnumDialogType } from "@components/dialogs/enums";
import MarketingKit from "@components/marketing-kit/marketingKit";
import DialogModal from "@components/dialogs/dialog-modal";
import { MainAppContext } from "pages/_app";
import { IAppDialogMarketingKitAttrs } from "@components/dialogs/iDialogs";
import { observer } from "mobx-react-lite";

const DialogMarketingKit = observer(({ id, className }: { id: string; className: string }) => {
    const { appStore } = useContext(MainAppContext);
    const dialog = appStore.getDialog();
    const dialogAttrs = dialog.attrs as IAppDialogMarketingKitAttrs;

    if (dialog.type !== EnumDialogType.marketingKit) {
        return null;
    }

    return (
        <DialogModal id={id} className={className}>
            <MarketingKit gameId={dialogAttrs.gameId} />
        </DialogModal>
    );
});

export default DialogMarketingKit;
