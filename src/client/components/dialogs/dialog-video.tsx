import React, { useContext, useEffect, useRef, useState } from "react";

import { observer } from "mobx-react-lite";
import { MainAppContext } from "pages/_app";
import { Button, ListGroup } from "react-bootstrap";
import AppVideo from "@components/main/appVideo";
import { EnumDialogType } from "@components/dialogs/enums";
import { IAppDialogVideoAttrs } from "@components/dialogs/iDialogs";
import DialogModal from "@components/dialogs/dialog-modal";
import { EnumProjectType } from "models/enum/system";
import AppLink from "@components/main/appLink";
import { AppLocales } from "locales/locales";

const DialogVideo = observer(({ id }: { id: string }) => {
    const { appStore } = useContext(MainAppContext);
    const playerRef = useRef<HTMLVideoElement>(null);
    const dialog = appStore.getDialog();
    const dialogAttrs = dialog.attrs as IAppDialogVideoAttrs;

    const projectType = appStore.getAppProject().type;

    const [videoSrc, setVideoSrc] = useState<string>("");
    const [activeItem, setActiveItem] = useState<string>(
        /*Array.isArray(certificates) && certificates.length > 0 ? certificates[0].marketCode : */ ""
    );

    const strictModeRef = useRef<boolean>(false);

    useEffect(() => {
        if (!strictModeRef.current) {
            strictModeRef.current = true;

            if (Array.isArray(dialogAttrs.videos) && dialogAttrs.videos.length > 0) {
                const firstEl = dialogAttrs.videos.shift();
                setVideoSrc(String(firstEl?.videoUrl));
                setActiveItem(String(firstEl?.videoUrl));
            } else if (dialogAttrs.videoUrl) {
                setVideoSrc(dialogAttrs.videoUrl);
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    if (dialog.type !== EnumDialogType.video) {
        return null;
    }

    return (
        <DialogModal
            id={id}
            className={"dialog-video"}
            footerNode={projectType === EnumProjectType.pa && (
                <Button variant="primary" href={videoSrc || dialogAttrs?.videoUrl} target={"_blank"}>
                    Download
                </Button>)}
        >

            {Array.isArray(dialogAttrs.videos) && dialogAttrs.videos.length > 1 && (
                <ListGroup>
                    {dialogAttrs.videos.map((v, index) => {
                        const language = Object.values(AppLocales).filter((l) => l.key === v.languageCode);
                        return <ListGroup.Item key={"v" + index}>
                            <AppLink
                                href={"#"}
                                rel="noreferrer"
                                onClick={(event) => {
                                    event.preventDefault();
                                    event.stopPropagation();
                                    setVideoSrc(v.videoUrl || "");
                                    setActiveItem(v.videoUrl);
                                }}
                                className={
                                    (v.videoUrl === activeItem ? "active" : "")
                                }
                            ><span className={"video-title"}>
                                {Array.isArray(language) && language.length === 1
                                 ? language[0].title
                                 : v.languageCode}
                            </span></AppLink>
                        </ListGroup.Item>;
                    })}
                </ListGroup>
            )}
            <div className={"video-wrapper"}>
                <AppVideo
                    videoRef={playerRef}
                    videoUrl={videoSrc || dialogAttrs?.videoUrl}
                    controls
                    autoPlay
                    className={"app-dialog__video"}
                />
            </div>
        </DialogModal>
    );
});

export default DialogVideo;
