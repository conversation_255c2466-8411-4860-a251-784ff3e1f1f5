import React, { useContext } from "react";

import { observer } from "mobx-react-lite";
import { MainAppContext } from "pages/_app";
import DialogModal from "@components/dialogs/dialog-modal";
import TripleSlider from "@components/sliders/triple-slider";
import { IAppDialogImagesAttrs } from "@components/dialogs/iDialogs";
import { EnumDialogType } from "@components/dialogs/enums";

const DialogImages = observer((props: { id: string }) => {
    const { id } = props;
    const { appStore } = useContext(MainAppContext);
    const dialog = appStore.getDialog();
    const dialogAttrs = dialog.attrs as IAppDialogImagesAttrs;

    if (dialog.type !== EnumDialogType.images) {
        return null;
    }

    return (
        <DialogModal id={id} className={"dialog-images"}>
            <TripleSlider images={dialogAttrs.images} />
        </DialogModal>
    );
});

export default DialogImages;
