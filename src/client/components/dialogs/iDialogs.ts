import { EnumDialogType } from "@components/dialogs/enums";
import { IMarketCertificate } from "api/services/interfaces/iGamesService";
import { IGameVideosExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";

export interface IAppDialog {
    title: string;
    isOpen?: boolean;
    type: EnumDialogType;
    attrs:
        | object
        | IAppDialogMarketingKitAttrs
        | IAppDialogIframeAttrs
        | IAppDialogVideoAttrs
        | IAppDialogImagesAttrs
        | IAppDialogCertificatesAttrs;
}

export interface IAppDialogMarketingKitAttrs {
    gameId: string;
}

export interface IAppDialogIframeAttrs {
    url: string;
}

export interface IAppDialogVideoAttrs {
    videoUrl: string;
    videos?: IGameVideosExtended[];
}

export interface IAppDialogImage {
    imageUrl: string;
    title?: string;
    info?: string;
}

export interface IAppDialogImagesAttrs {
    images: IAppDialogImage[];
}

export interface IAppDialogCertificatesAttrs {
    certificates: IMarketCertificate[];
}
