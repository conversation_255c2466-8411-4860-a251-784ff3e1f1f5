import React, { CSSProperties, useContext } from "react";

import { observer } from "mobx-react-lite";
import { MainAppContext } from "pages/_app";
import FormChangeUserPassword from "@components/forms/change-password/form-change-user-password";
import DialogModal from "@components/dialogs/dialog-modal";
import { EnumDialogType } from "@components/dialogs/enums";

const DialogPasswordChange = observer((props: { id: string }) => {
    const { id } = props;
    const { appStore } = useContext(MainAppContext);
    const dialog = appStore.getDialog();

    const pStyle = { fontSize: "1rem", textIndent: "20px", textAlign: "justify" } as CSSProperties;

    if (dialog.type !== EnumDialogType.userPasswordChange) {
        return null;
    }

    return (
        <DialogModal id={id} className={"dialog-password-change"} isShow={appStore.getUserPasswordChangeStatus()}>
            <p style={pStyle}>
                The password you received was a factory-default, for security you are promoted to set a new password
                immediately.
            </p>
            <FormChangeUserPassword
                className={"form-reset-user-password"}
                id={"form-reset-user-password"}
                submitButtonName={"Change password"}
            />
        </DialogModal>
    );
});

export default DialogPasswordChange;
