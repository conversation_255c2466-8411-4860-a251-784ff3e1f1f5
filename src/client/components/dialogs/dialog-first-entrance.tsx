import React, { CSSProperties, useContext } from "react";

import { observer } from "mobx-react-lite";
import { <PERSON><PERSON>, Modal } from "react-bootstrap";
import { MainAppContext } from "pages/_app";
import appConfig from "appConfig";
import { <PERSON>UserAttrs, IUserLoginResponse } from "api/services/interfaces/iUsersService";
import { ClientRequest } from "utils/clientRequest";
import { AxiosResponse } from "axios";
import { ClientApiResponse } from "utils/serverResponse";
import moment from "moment";
import { EnumDBUserGroupCodes } from "models/enum/db";
import clientApi from "client/services/http";
import { EnumDialogType } from "@components/dialogs/enums";

const DialogFirstEntrance = observer((props: { id: string }) => {
    const { id } = props;
    const { authStore, appStore } = useContext(MainAppContext);
    const user = authStore.getUser();
    const dialog = appStore.getDialog();

    if (dialog.type !== EnumDialogType.userFirstEntrance) {
        return null;
    }

    const handleClose = () => {
        ClientRequest.redirect(appConfig.client.endpoints.external.skywind.mainWebsite);
    };

    const handleAgree = async (e: React.MouseEvent | React.KeyboardEvent) => {
        e.stopPropagation();
        e.preventDefault();
        const response = new ClientApiResponse(
            await clientApi.put<AxiosResponse<boolean>>(
                appConfig.client.endpoints.api.users.saveAttribute,
                { firstEntranceAgreement: true } as IUserAttrs,
                {
                    withCredentials: true,
                }
            )
        ).response;

        response
            .onError((err) => {
                const messages = err.getMessages();
                appStore.addNotificationError(messages.join(". "));
                appStore.showNotifications();
            })
            .onSuccess((success) => {
                const user = success.data as IUserLoginResponse;
                appStore.setUserFirstEntranceAgreement(moment(String(user.firstEntranceAgreementAt)).isValid());
                authStore.setUser(user);
            });
    };

    const pStyle = { fontSize: "1rem", textIndent: "20px", textAlign: "justify" } as CSSProperties;

    return (
        <Modal
            id={id}
            size="lg"
            aria-labelledby="contained-modal-title-vcenter"
            centered
            show={!appStore.getUserFirstEntranceAgreement() && dialog.isOpen}
            onHide={handleClose}
        >
            <Modal.Header>
                <Modal.Title id="contained-modal-title-vcenter">Consent form</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                {!user?.userPermissions?.groups?.includes(EnumDBUserGroupCodes.partnerThirdParty) ? (
                    <>
                        <p style={pStyle}>
                            By accessing this portal, you agree and accept that (on behalf of yourself and anyone in
                            your organisation) any data you access is confidential and must be treated as such and with
                            the same standards of care with which your or your organisation’s confidential data is
                            treated.
                        </p>
                        <p style={pStyle}>
                            You may only use the data we provide that is relevant to your organisation and for due
                            diligence purposes only.
                        </p>
                        <p style={pStyle}>
                            Such data may only be shared with persons within your organisation and no-one else on a
                            strict need to know basis provided they keep the data confidential too. We reserve the right
                            to suspend or terminate your access to the portal at any time, at our absolute discretion.
                        </p>
                        <p style={pStyle}>
                            The obligations of confidentiality will survive such suspension and termination.
                        </p>
                    </>
                ) : (
                    <>
                        <p style={pStyle}>
                            By accessing this portal, you agree and accept that (on behalf of yourself and anyone in
                            your organisation) any data you access is confidential and must be treated as such and with
                            the same standards of care with which your organisation’s confidential data is treated.
                        </p>
                        <p style={pStyle}>
                            You may only use the data we provide that is relevant to your organisation and for due
                            diligence purposes only.
                        </p>
                        <p style={pStyle}>
                            Such data may only be shared with persons within your organisation and on a strict need to
                            know basis. We reserve the right to suspend or terminate your access to the portal at any
                            time , at our absolute discretion.
                        </p>
                        <p style={pStyle}>
                            The obligations of confidentiality will survive such suspension and termination.
                        </p>
                        <p style={pStyle}>
                            These obligations augment and form part of the contractual obligations that exist between us
                            and your organisation and specifically the obligations in connection with confidentiality;
                            should any conflict between them arise, the obligations set out above will prevail.
                        </p>
                    </>
                )}
            </Modal.Body>
            <Modal.Footer>
                <Button variant="primary" onClick={handleAgree}>
                    I agree
                </Button>
            </Modal.Footer>
        </Modal>
    );
});

export default DialogFirstEntrance;
