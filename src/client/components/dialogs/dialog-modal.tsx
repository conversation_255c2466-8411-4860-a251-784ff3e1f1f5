import React, { ReactNode, useContext, useEffect } from "react";
import { Modal } from "react-bootstrap";
import { observer } from "mobx-react-lite";
import { MainAppContext } from "pages/_app";

const DialogModal = observer(
    ({
         id,
         className,
         children,
         footerNode,
         isShow
     }: {
        id: string;
        className: string;
        children?: ReactNode;
        footerNode?: ReactNode;
        isShow?: boolean;
    }) => {
        const { appStore } = useContext(MainAppContext);

        const dialog = appStore.getDialog();

        useEffect(() => {
            if (isShow !== undefined ? isShow : dialog.isOpen) {
                document.body.classList.add("no-scroll");
                document.documentElement.classList.add("no-scroll");
            } else {
                document.body.classList.remove("no-scroll");
                document.documentElement.classList.remove("no-scroll");
            }
        }, [dialog.isOpen, isShow]);

        return (
            <Modal
                id={id}
                size="lg"
                aria-labelledby="contained-modal-title-vcenter"
                centered
                autoFocus={true}
                show={isShow !== undefined ? isShow : dialog.isOpen}
                onHide={appStore.closeDialog.bind(appStore)}
                className={"dialog-modal-area " + className}
            >
                <Modal.Header closeButton>
                    <Modal.Title id="contained-modal-title-vcenter">{dialog.title}</Modal.Title>
                </Modal.Header>
                <Modal.Body>{children}</Modal.Body>
                <Modal.Footer>{footerNode}</Modal.Footer>
            </Modal>
        );
    }
);

export default DialogModal;
