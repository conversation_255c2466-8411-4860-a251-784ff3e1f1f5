import React, { useContext, useEffect, useState } from "react";
import { observer } from "mobx-react-lite";
import { ListGroup } from "react-bootstrap";
import { HiOutlineDocument } from "@react-icons/all-files/hi/HiOutlineDocument";
import AppLink from "@components/main/appLink";
import { MainAppContext } from "pages/_app";
import { IMarketCertificate } from "api/services/interfaces/iGamesService";
import AppImg from "@components/main/appImg";
import DialogModal from "@components/dialogs/dialog-modal";
import FileViewer from "@components/file-viewer/file-viewer";
import { EnumDialogType } from "@components/dialogs/enums";
import { IAppDialogCertificatesAttrs } from "@components/dialogs/iDialogs";
import { Project } from "utils/project";

const DialogCertificates = observer((props: { id: string }) => {
    const { id } = props;
    const { appStore } = useContext(MainAppContext);
    const dialog = appStore.getDialog();
    const dialogAttrs = dialog.attrs as IAppDialogCertificatesAttrs;

    const certificates: IMarketCertificate[] = dialogAttrs?.certificates;

    const [iframeSrc, setIframeSrc] = useState<string>("");
    const [activeItem, setActiveItem] = useState<string>(
        /*Array.isArray(certificates) && certificates.length > 0 ? certificates[0].marketCode : */ ""
    );

    useEffect(() => {
        if (Array.isArray(certificates) && certificates.length > 0) {
            for (const c of certificates) {
                if (c.fileUrl) {
                    setActiveItem(c.marketCode);
                    setIframeSrc(Project.replaceSanityCdn(c.fileUrl) || "");
                    break;
                }
            }
        }
    }, [certificates]);

    if (dialog.type !== EnumDialogType.certificates) {
        return null;
    }

    return (
        <DialogModal id={id} className={"dialog-certificates"}>
            <div className={"certificates-info"}>
                <div className={"certificates-area"}>
                    <ListGroup>
                        {(Array.isArray(certificates) ? certificates : []).map((crt: IMarketCertificate) => {
                            const comingSoon = crt.comingSoon === true || !crt.fileUrl;
                            if(comingSoon) {
                                return null
                            }
                            const marketName =
                                crt.marketName +
                                (crt.translated ? " translated" : "") +
                                (comingSoon ? " (coming soon)" : "") +
                                (crt.id.endsWith("_2") ? " sm_result" : "");
                            return (
                                <ListGroup.Item key={crt.id} className={comingSoon ? " coming-soon" : ""}>
                                    <AppLink
                                        href={"#"}
                                        rel="noreferrer"
                                        onClick={(event) => {
                                            event.preventDefault();
                                            event.stopPropagation();
                                            setIframeSrc(Project.replaceSanityCdn(crt.fileUrl as string) || "");
                                            setActiveItem(crt.id);
                                        }}
                                        className={
                                            (comingSoon ? " coming-soon" : "") +
                                            (crt.id === activeItem ? " active" : "")
                                        }
                                    >
                                        {crt.iconUrl && (
                                            <AppImg
                                                src={crt.iconUrl}
                                                alt={marketName}
                                                title={marketName}
                                                className={"market-icon"}
                                            />
                                        )}
                                        {!crt.iconUrl && (
                                            <HiOutlineDocument
                                                color={"white"}
                                                style={{ color: "#8dd363", fontSize: "1.5em" }}
                                            />
                                        )}
                                        <span className={"market-title"}>{" " + marketName}</span>
                                    </AppLink>
                                </ListGroup.Item>
                            );
                        })}
                    </ListGroup>
                    {iframeSrc !== "" && (
                        <FileViewer id={"PDFViewer"} src={iframeSrc} className={"certificate-iframe"} />
                    )}
                    {iframeSrc === "" && <div className={"coming-soon-area"}>Coming soon</div>}
                </div>
            </div>
        </DialogModal>
    );
});

export default DialogCertificates;
