import React, { useContext } from "react";
import { EnumDialogType } from "@components/dialogs/enums";
import FileViewer from "@components/file-viewer/file-viewer";
import DialogModal from "@components/dialogs/dialog-modal";
import { MainAppContext } from "pages/_app";
import { IAppDialogIframeAttrs } from "@components/dialogs/iDialogs";
import { observer } from "mobx-react-lite";
import { Project } from "utils/project";

const DialogIframe = observer(({ id, className }: { id: string; className: string }) => {
    const { appStore } = useContext(MainAppContext);
    const dialog = appStore.getDialog();
    const dialogAttrs = dialog.attrs as IAppDialogIframeAttrs;

    if (dialog.type !== EnumDialogType.iframe) {
        return null;
    }

    return (
        <DialogModal id={id} className={className}>
            {dialogAttrs?.url !== "" && (
                <FileViewer id={"file-product-sheet"} className={"dialog-iframe-tag"} src={Project.replaceSanityCdn(dialogAttrs?.url)} />
            )}
        </DialogModal>
    );
});

export default DialogIframe;
