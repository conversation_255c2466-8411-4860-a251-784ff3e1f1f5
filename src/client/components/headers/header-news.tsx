import React, { useContext, useRef } from "react";
import { EnumLanguageCode } from "models/enum/system";
import { observer } from "mobx-react-lite";
import { MainAppContext } from "pages/_app";
import { IVideoExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import AppImg from "@components/main/appImg";
import AppVideo from "@components/main/appVideo";

const HeaderNews = observer(
    (props: {
        video: IVideoExtended;
        imageUrl: string;
        title: string;
        languageCode: EnumLanguageCode;
        className: string;
    }) => {
        const { video, imageUrl, title, /*languageCode, */ className } = props;
        const {
            /*authStore, appStore*/
        } = useContext(MainAppContext);

        /*const router = useRouter();
        const locale = router.locale;*/

        const isVideo = video && video.videoUrl && video.posterUrl && video.posterVerticalUrl;

        const playerRef = useRef<HTMLVideoElement>(null);

        return (
            <header className={className}>
                {/*<div className="header-nav">*/}
                {/*    <Anchor href="" className="header-logo">*/}
                {/*        <AppImg src="images/header/sw-logo.png" alt="" className="header-logo-img" />*/}
                {/*    </Anchor>*/}
                {/*</div>*/}
                <div className="header-banner">
                    <figure className="header-banner-figure">
                        {isVideo && (
                            <div className="header-banner-figure-video">
                                <AppVideo
                                    className="header-banner-figure-video"
                                    autoPlay
                                    muted
                                    loop
                                    videoRef={playerRef}
                                    posterUrl={video?.posterUrl}
                                    videoUrl={video?.videoUrl}
                                />
                            </div>
                        )}
                        {!isVideo && (
                            <AppImg src={imageUrl} alt={title} className="header-banner-figure-img is-tablet-up" />
                        )}
                        {!isVideo && (
                            <AppImg src={imageUrl} alt={title} className="header-banner-figure-img is-mobile" />
                        )}
                        <div className="header-banner-shadow" />
                    </figure>
                </div>
            </header>
        );
    }
);

export default HeaderNews;
