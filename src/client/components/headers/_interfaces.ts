import { ReactNode } from "react";
import { SwiperSlideProps } from "swiper/react/swiper-react";

export interface IHeaderLink {
    href: string;
    title: string;
}

export interface IHeaderMedia {
    imageUrl?: string;
    videoUrl?: string;
    isShadow?: boolean;
}

export interface IHeaderContainer {
    title?: string;
    accentTitle?: string;
    contentText?: string;
    linkUrl?: string;
    linkText?: string;
    imageUrl?: string;
    isBottom?: boolean;
    isStatic?: boolean;
    classNameHeaderBanner?: string;
    children?: ReactNode;
    btnText?: string;
    btnLinkUrl?: string;
}

export interface IHeaderSlider {
    children: { link: IHeaderLink; media?: IHeaderMedia, container?: IHeaderContainer },
    sliderProps?: SwiperSlideProps
    dataProps?: { swiperAutoplay: number }
}
