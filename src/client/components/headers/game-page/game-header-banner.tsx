import React, { useRef } from "react";
import AppVideo from "@components/main/appVideo";
import AppImg from "@components/main/appImg";
import { Video } from "utils/video";

const GameHeaderBanner = ({
    isVideo,
    videoUrl,
    videoPosterUrl,
    landscapeImageUrl,
    portraitImageUrl,
}: {
    isVideo: boolean;
    videoUrl?: string;
    videoPosterUrl?: string;
    landscapeImageUrl: string;
    portraitImageUrl: string;
}) => {
    const playerRef = useRef<HTMLVideoElement>(null);

    const isHls = Video.isUrlVideoHls(videoUrl);

    return (
        <figure className="header-banner-figure">
            {isVideo && (
                <div className="header-banner-figure-video">
                    <AppVideo
                        videoRef={playerRef}
                        videoUrl={videoUrl}
                        className={isHls ? "header-banner-figure-video hls" : "header-banner-figure-video"}
                        posterUrl={videoPosterUrl}
                        autoPlay={true}
                        muted={true}
                        loop={true}
                    />
                </div>
            )}
            {!isVideo && <AppImg src={landscapeImageUrl} className="header-banner-figure-img is-tablet-up" />}
            {!isVideo && <AppImg src={portraitImageUrl} className="header-banner-figure-img is-mobile" />}
            <div className="header-banner-shadow" />
        </figure>
    );
};

export default GameHeaderBanner;
