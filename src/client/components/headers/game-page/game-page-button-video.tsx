import React, { useContext } from "react";
import { observer } from "mobx-react-lite";
import { IGameVideosExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { EnumProjectType } from "models/enum/system";
import { ClientApp } from "client/app/clientApp";
import ClientUsersService from "@services/clientUsersService";
import { EnumUserClientAction } from "models/enum/user";
import AppLink from "@components/main/appLink";
import { Dropdown } from "react-bootstrap";
import { AppLocales } from "locales/locales";
import { MainAppContext } from "@pages/_app";

export interface IGamePageButtonVideoProps {
    gameVideos: IGameVideosExtended[];
    gameCode: string | null;
    gameName: string;
}

const GamePageButtonVideo = observer(({ gameVideos, gameCode, gameName }: IGamePageButtonVideoProps) => {
    const { appStore } = useContext(MainAppContext);

    const projectType = appStore.getAppProject().type;

    if (!Array.isArray(gameVideos) || gameVideos.length === 0) {
        return null;
    }

    if (gameVideos.length === 1) {
        return (<AppLink
            href={gameVideos[0].videoUrl}
            onClick={async (event) => {
                event.preventDefault();
                event.stopPropagation();
                ClientApp.dialog.openVideo(
                    "Promo video: " + gameName,
                    gameVideos[0]?.videoUrl as string
                );
                await ClientUsersService.clientAction(
                    EnumUserClientAction.gamePromoVideo,
                    location.pathname,
                    {
                        isSite: projectType === EnumProjectType.site,
                        gameCode: gameCode,
                        title: gameName,
                        url: gameVideos[0]?.videoUrl
                    },
                    "gamePage"
                );
            }}
            className="header-banner-btn"
        >
            Promo video
        </AppLink>);
    } else {
        return <Dropdown>
            <Dropdown.Toggle className="header-banner-btn">
                Promo video
            </Dropdown.Toggle>
            <Dropdown.Menu>
                {gameVideos.map((v, index) => {
                        const language = Object.values(AppLocales).filter(l => l.code === v.languageCode);
                        return (<Dropdown.Item
                            key={"v" + index}
                            onClick={async (event) => {
                                event.preventDefault();
                                event.stopPropagation();
                                ClientApp.dialog.openVideo(
                                    "Promo video: " + gameName,
                                    v?.videoUrl as string
                                );
                                await ClientUsersService.clientAction(
                                    EnumUserClientAction.gamePromoVideo,
                                    location.pathname,
                                    {
                                        isSite: projectType === EnumProjectType.site,
                                        gameCode: gameCode,
                                        title: gameName,
                                        url: v?.videoUrl
                                    },
                                    "gamePage"
                                );
                            }}
                        >
                            {Array.isArray(language) && language.length === 1 ? language[0].title : v.languageCode}
                        </Dropdown.Item>);
                    }
                )}
            </Dropdown.Menu>
        </Dropdown>;
    }
});

export default GamePageButtonVideo;