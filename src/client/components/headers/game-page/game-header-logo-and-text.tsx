import React, { useEffect, useState } from "react";
import AppImg from "@components/main/appImg";

const GameHeaderLogoAndText = ({
    gameLogoUrl,
    gameName,
    gameDescription,
}: {
    gameLogoUrl?: string;
    gameName: string;
    gameDescription?: string;
}) => {
    // 0 - initial state, 1 - show, -1 - hide
    const [textVisibility, setTextVisibility] = useState<number>(0);

    const time = 9000;

    const handleResize = () => {
        const af = document.querySelector(".header-banner-content-figure.animation-figure") as HTMLDivElement;
        const at = document.querySelector(".header-banner-content-text.animation-text") as HTMLDivElement;

        const hideFigure = (af: HTMLDivElement, time: number) => {
            if (af) {
                let bannerLogo = document.querySelector(".animation-figure");
                let bannerLogoHeight = bannerLogo?.clientHeight;
                let bannerContainerHeight = document.querySelector(".animation-container")?.clientHeight;
                if (bannerContainerHeight && bannerLogoHeight) {
                    let bannerLogoShift = (bannerContainerHeight - bannerLogoHeight - 25).toFixed();
                    af.style.transform = `translateY(${bannerLogoShift}px) scale(.9)`;
                    setTimeout(() => {
                        af.style.transitionDuration = "unset";
                    }, time);
                }
            }
        };
        const hideText = (at: HTMLDivElement, time: number) => {
            if (at) {
                at.style.transform = "translateY(100%)";
                at.style.opacity = "0";
                at.style.visibility = "hidden";
                setTimeout(() => {
                    at.style.transitionDuration = "unset";
                }, time);
            }
        };

        const showFigure = (af: HTMLDivElement) => {
            af?.removeAttribute("style");
        };

        const showText = (at: HTMLDivElement) => {
            at?.removeAttribute("style");
        };

        if (textVisibility === 0) {
            if (window.window.innerWidth > 768) {
                // show text and hide with animation
                hideFigure(af, time);
                hideText(at, time);
                setTextVisibility(-1);
            } else {
                // show text
                showFigure(af);
                showText(at);
                setTextVisibility(1);
            }
        } else if (textVisibility === 1) {
            // show text
            showFigure(af);
            showText(at);
        } else if (textVisibility === -1) {
            // hide text
            hideFigure(af, 1000);
            hideText(at, 1000);
        }
    };

    useEffect(() => {
        if (textVisibility === 0) {
            setTimeout(() => {
                handleResize();
            }, time);
        } else {
            handleResize();
        }

        let resizeTimer: number;
        window.addEventListener("resize", () => {
            clearTimeout(resizeTimer);
            resizeTimer = window.setTimeout(function () {
                handleResize();
            }, 150);
        });

        return () => window.removeEventListener("resize", handleResize);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [textVisibility]);

    return (
        <div className="animation-container">
            <figure className="header-banner-content-figure animation-figure">
                {gameLogoUrl && (
                    <AppImg
                        src={gameLogoUrl}
                        alt={gameName}
                        className="header-banner-content-figure-img"
                        onClick={() => {
                            setTextVisibility(textVisibility === 1 ? -1 : 1);
                        }}
                    />
                )}
            </figure>
            <p className="header-banner-content-text animation-text">{gameDescription}</p>
        </div>
    );
};

export default GameHeaderLogoAndText;
