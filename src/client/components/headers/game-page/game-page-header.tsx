import React, { useContext } from "react";
import { EnumLanguageCode, EnumProjectType } from "models/enum/system";
import { Game } from "utils/game";
import { useRouter } from "next/router";
import { EnumLocale, getLocaleMessages } from "locales/locales";
import { observer } from "mobx-react-lite";
import { IMarketCertificate } from "api/services/interfaces/iGamesService";
import { MainAppContext } from "pages/_app";
import { Dropdown } from "react-bootstrap";
import { EnumUserClientAction } from "models/enum/user";
import { ClientRequest } from "utils/clientRequest";
import { SanityApi } from "utils/sanityApi";
import { IProviderGame } from "api/data-providers/db/mysql/interfaces/iDBProvider";
import { ILocaleString } from "api/data-providers/sanity/schemas";
import { IGameExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { EnumDBUserPermissions } from "models/enum/db";
import ClientUsersService from "client/services/clientUsersService";
import AppImg from "@components/main/appImg";
import AppLink from "@components/main/appLink";
import { EnumShowGameCode, EnumShowGameRTP } from "models/enum/game";
import { formatReleaseDate } from "@components/games-table/formatTableColumns";
import { Project } from "utils/project";
import ActionPlayGame from "@components/actions/actionPlayGame";
import { ClientApp } from "client/app/clientApp";
import GameHeaderBanner from "@components/headers/game-page/game-header-banner";
import GameHeaderLogoAndText from "@components/headers/game-page/game-header-logo-and-text";
import GamePageButtonVideo from "@components/headers/game-page/game-page-button-video";
import { EnumPage } from "models/enum/page";

export interface ICmpGamePageHeaderProps {
    game: IGameExtended;
    languageCode: EnumLanguageCode;
    providerGame: IProviderGame | null;
}

const GamePageHeader = observer(
    ({ game, languageCode, providerGame }: ICmpGamePageHeaderProps) => {
        const { authStore, appStore } = useContext(MainAppContext);

        const toolsTournamentsUrl = Project.getPageUrl(appStore, EnumPage.toolsTournaments);
        const toolsFreeBetsUrl = Project.getPageUrl(appStore, EnumPage.toolsFreeBets);
        const toolsLuckyEnvelopesUrl = Project.getPageUrl(appStore, EnumPage.toolsLuckyEnvelopes);
        const toolsMustWinJackpotsUrl = Project.getPageUrl(appStore, EnumPage.toolsMustWinJackpots);

        const user = authStore.getUser();
        const userPermissions = user?.userPermissions?.permissions;

        const router = useRouter();
        const locale = router.locale;
        const msg = getLocaleMessages(locale as EnumLocale).messages;
        const projectType = appStore.getAppProject().type;

        const isVideo = !!(game.header !== "image" && game?.gameVideo?.videoUrl && game?.gameVideo?.posterUrl);

        const gameCode = Game.getFirstGameCode(game);
        const gameInfoFromBI = Game.getGameInfoFromBI(game);
        const gameInfo = Game.getGameInfo(game.gameInfo, languageCode);

        const gameName = SanityApi.getLocale(game.gameName as ILocaleString, languageCode);

        const isLiveGameType = Array.isArray(game.gameTypes) && game.gameTypes.filter((g) => g.gameTypeCode?.current === "live").length === 1;

        const gameRTPs = Game.prepareRTPs(
            gameInfoFromBI.theoretical_rtp,
            gameInfo.rtp,
            Game.getGameRTPs(game)
        ); /*.map((v) => Game.formatRTPSingle(Number(v.gameRTP), msg, "%"));*/
        const gameRTP = Game.formatRTP(gameRTPs, game.showGameRTP as EnumShowGameRTP, msg, " ", "%")
            .split(" ")
            .map((v) => <span key={v}>{v}</span>);

        const gameCodes = Game.formatGameCodes(
            game.gameCodes.map(c => String(c.gameCode)),
            game.showGameCode as EnumShowGameCode
        )
            .split(" ")
            .map((v) => <span key={v}>{v}</span>);

        const gameVolatility = Game.formatVolatility(
            gameInfoFromBI?.player_volatility || String(gameInfo?.volatility || ""),
            msg
        );
        const gameWays = Game.formatWays(gameInfoFromBI?.max_number_of_lines && gameInfoFromBI?.max_number_of_lines !== "0" ?
                                         gameInfoFromBI?.max_number_of_lines :
                                         String(gameInfo.ways || ""), msg);
        const gameLayout = Game.formatLayout(gameInfoFromBI?.grid || String(gameInfo.layout || ""), msg);
        const gameReleaseDate = formatReleaseDate(
            gameInfoFromBI.release_date || gameInfoFromBI.release_date_mga || gameInfo.releaseDate,
            game.gameInfo?.formatOfReleaseDate || undefined
        );

        const playUrl = Game.getGamePlayUrl(
            gameCode,
            // game.paUrl,
            // appConfig.systemSettings.defaultRegionCode,
            providerGame !== null
        );

        const gameVideos = Game.getGameVideos(game);

        return (
            <header className="show-shadow header-banner-game">
                <GameHeaderBanner
                    isVideo={isVideo}
                    videoUrl={game.gameVideo?.videoUrl}
                    videoPosterUrl={game.gameVideo?.posterUrl}
                    landscapeImageUrl={game.gameHDPosterUrl}
                    portraitImageUrl={game.gameTowerPosterUrl}
                />
                <div className="header-banner-container">
                    <div className="header-banner-content bottom">
                        <GameHeaderLogoAndText
                            gameLogoUrl={game.gameLogoUrl}
                            gameName={gameName}
                            gameDescription={game.siteDescription?.en}
                        />
                        <div className="header-banner-btn-list">
                            <div className="header-banner-btn-container">
                                {!isLiveGameType && (
                                    <ActionPlayGame
                                        playUrl={playUrl}
                                        gameCode={gameCode}
                                        gameName={gameName}
                                        className={"header-banner-btn accent"}
                                    />
                                )}
                                {(projectType === EnumProjectType.site || userPermissions?.includes(
                                    EnumDBUserPermissions.paCanViewPromoVideo)) && (
                                    <GamePageButtonVideo
                                        gameVideos={gameVideos}
                                        gameCode={gameCode}
                                        gameName={gameName} />
                                )}
                                {game.infoSheetUrl &&
                                    projectType === EnumProjectType.pa &&
                                    userPermissions?.includes(EnumDBUserPermissions.paCanViewInfoSheet) && (
                                        <AppLink
                                            href={"#"}
                                            className="header-banner-btn"
                                            rel="noreferrer"
                                            onClick={async (event) => {
                                                event.stopPropagation();
                                                event.preventDefault();
                                                ClientApp.dialog.openIframe(
                                                    "Info sheet: " + gameName,
                                                    game.infoSheetUrl
                                                );
                                                await ClientUsersService.clientAction(
                                                    EnumUserClientAction.gameInfoSheet,
                                                    location.pathname,
                                                    {
                                                        isSite: false,
                                                        gameCode: gameCode,
                                                        title: gameName,
                                                        url: game.infoSheetUrl
                                                    },
                                                    "gamePage"
                                                );
                                            }}
                                        >
                                            Info sheet
                                        </AppLink>
                                    )}
                                {game.productSheetUrl &&
                                    projectType === EnumProjectType.pa &&
                                    userPermissions?.includes(EnumDBUserPermissions.paCanViewProductSheet) && (
                                        <AppLink
                                            href={"#"}
                                            className="header-banner-btn"
                                            rel="noreferrer"
                                            onClick={async (event) => {
                                                event.stopPropagation();
                                                event.preventDefault();
                                                ClientApp.dialog.openIframe(
                                                    "Product sheet: " + gameName,
                                                    game.productSheetUrl
                                                );
                                                await ClientUsersService.clientAction(
                                                    EnumUserClientAction.gameProductSheet,
                                                    location.pathname,
                                                    {
                                                        isSite: false,
                                                        gameCode: gameCode,
                                                        title: gameName,
                                                        url: game.productSheetUrl
                                                    },
                                                    "gamePage"
                                                );
                                            }}
                                        >
                                            Product sheet
                                        </AppLink>
                                    )}
                                {projectType === EnumProjectType.pa &&
                                    userPermissions?.includes(EnumDBUserPermissions.paCanViewMarketingKit) && (
                                        <Dropdown>
                                            <Dropdown.Toggle className="header-banner-btn">
                                                Marketing kit
                                            </Dropdown.Toggle>

                                            <Dropdown.Menu>
                                                <Dropdown.Item
                                                    key={"browse"}
                                                    onClick={async (event) => {
                                                        event.preventDefault();
                                                        event.stopPropagation();
                                                        ClientApp.dialog.openMarketingKit(
                                                            "Marketing Kit: " + gameName,
                                                            String(game.gameId?.current)
                                                        );
                                                        await ClientUsersService.clientAction(
                                                            EnumUserClientAction.gameMarketingBrowse,
                                                            location.pathname,
                                                            {
                                                                isSite: false,
                                                                gameCode: gameCode,
                                                                title: gameName
                                                            },
                                                            "gamePage"
                                                        );
                                                    }}
                                                >
                                                    Browse Kit
                                                </Dropdown.Item>
                                                <Dropdown.Item
                                                    key={"download"}
                                                    href={Game.getMarketingDownloadAllUrl(
                                                        gameCode,
                                                        languageCode as EnumLanguageCode
                                                    )}
                                                    onClick={async (e) => {
                                                        e.stopPropagation();
                                                        e.preventDefault();
                                                        const downloadUrl = Game.getMarketingDownloadAllUrl(
                                                            gameCode,
                                                            languageCode as EnumLanguageCode
                                                        );
                                                        await ClientUsersService.clientAction(
                                                            EnumUserClientAction.gameMarketingDownload,
                                                            location.pathname,
                                                            {
                                                                isSite: false,
                                                                gameCode: gameCode,
                                                                title: gameName,
                                                                url: downloadUrl
                                                            },
                                                            "gamePage"
                                                        );
                                                        ClientRequest.redirect(downloadUrl);
                                                    }}
                                                >
                                                    Download All
                                                </Dropdown.Item>
                                            </Dropdown.Menu>
                                        </Dropdown>
                                    )}
                                {game.certificates &&
                                    projectType === EnumProjectType.pa &&
                                    userPermissions?.includes(EnumDBUserPermissions.paCanViewCertificates) && (
                                        <AppLink
                                            href={"#"}
                                            onClick={async (event) => {
                                                event.preventDefault();
                                                event.stopPropagation();
                                                ClientApp.dialog.openCertificates(
                                                    "Certificates: " + gameName,
                                                    game.certificates as IMarketCertificate[]
                                                );
                                                await ClientUsersService.clientAction(
                                                    EnumUserClientAction.gameCertificates,
                                                    location.pathname,
                                                    {
                                                        isSite: false,
                                                        gameCode: gameCode,
                                                        title: gameName
                                                    },
                                                    "gamePage"
                                                );
                                            }}
                                            className="header-banner-btn"
                                        >
                                            Certificates
                                        </AppLink>
                                    )}
                            </div>
                        </div>
                    </div>
                    <div className="header-banner-info">
                        <div className="header-banner-info-list-container">
                            <div className="header-banner-info-list is-mobile">
                                <div className="header-banner-info-item">
                                    <div className="header-banner-info-label">Game Name</div>
                                    <div className="header-banner-info-val">{gameName}</div>
                                </div>
                                {projectType === EnumProjectType.pa && (
                                    <div className="header-banner-info-item">
                                        <div className="header-banner-info-label">Game Code</div>
                                        <div className="header-banner-info-val">{gameCode}</div>
                                    </div>
                                )}
                            </div>
                            <div className="header-banner-info-divider is-mobile"></div>
                            <div className="header-banner-info-list">
                                <div className="header-banner-info-item is-tablet-up">
                                    <div className="header-banner-info-label">Game Name</div>
                                    <div className="header-banner-info-val">{gameName}</div>
                                </div>
                                <div className="header-banner-info-item">
                                    <div className="header-banner-info-label">RTP</div>
                                    <div className="header-banner-info-val d-flex flex-column">{gameRTP}</div>
                                </div>
                                {projectType === EnumProjectType.pa && (
                                    <div className="header-banner-info-item is-desktop">
                                        <div className="header-banner-info-label">Game Code</div>
                                        <div className="header-banner-info-val d-flex flex-column">{gameCodes}</div>
                                    </div>
                                )}
                                {!isLiveGameType && (
                                    <div className="header-banner-info-item">
                                        <div className="header-banner-info-label">Volatility</div>
                                        <div className="header-banner-info-val">{gameVolatility}</div>
                                    </div>
                                )}
                                {!isLiveGameType && (
                                    <div className="header-banner-info-item">
                                        <div className="header-banner-info-label">Ways/Lines</div>
                                        <div className="header-banner-info-val">{gameWays}</div>
                                    </div>
                                )}
                                {!isLiveGameType && (
                                    <div className="header-banner-info-item">
                                        <div className="header-banner-info-label">Layout</div>
                                        <div className="header-banner-info-val">{gameLayout}</div>
                                    </div>
                                )}
                                <div className="header-banner-info-item">
                                    <div className="header-banner-info-label">Release Date</div>
                                    <div className="header-banner-info-val">{gameReleaseDate}</div>
                                </div>
                            </div>
                            <div className="header-banner-info-divider is-mobile"></div>
                        </div>
                        <div className="header-banner-tools">
                            {Object.values(gameInfo.engTools).find(v => v) === true && (
                                <div className="header-banner-tools-title">Engagement Tools Support</div>
                            )}

                            {projectType === EnumProjectType.site && (
                                <div className="header-banner-tools-list">
                                    {gameInfo.engTools.isFreeBets && (
                                        <div className="header-banner-tools-item">
                                            <figure className="header-banner-tools-figure">
                                                <AppLink href={toolsFreeBetsUrl}>
                                                    <AppImg
                                                        src="/assets/pa/images/tools/free-bets.png"
                                                        alt={"Free Bets"}
                                                        title={"Free Bets"}
                                                        className="header-banner-tools-figure-img"
                                                    />
                                                </AppLink>
                                            </figure>
                                        </div>
                                    )}
                                    {gameInfo.engTools.isMustWinJackpot && (
                                        <div className="header-banner-tools-item">
                                            <figure className="header-banner-tools-figure">
                                                <AppLink href={toolsMustWinJackpotsUrl}>
                                                    <AppImg
                                                        src="/assets/pa/images/tools/must-win-jackpot.png"
                                                        alt={"Must Win Jackpot"}
                                                        title={"Must Win Jackpot"}
                                                        className="header-banner-tools-figure-img"
                                                    />
                                                </AppLink>
                                            </figure>
                                        </div>
                                    )}
                                    {gameInfo.engTools.isTournaments && (
                                        <div className="header-banner-tools-item">
                                            <figure className="header-banner-tools-figure">
                                                <AppLink href={toolsTournamentsUrl}>
                                                    <AppImg
                                                        src="/assets/pa/images/tools/tournaments.png"
                                                        alt={"Tournaments"}
                                                        title={"Tournaments"}
                                                        className="header-banner-tools-figure-img"
                                                    />
                                                </AppLink>
                                            </figure>
                                        </div>
                                    )}
                                    {gameInfo.engTools.isLuckyEnvelopes && (
                                        <div className="header-banner-tools-item">
                                            <figure className="header-banner-tools-figure">
                                                <AppLink href={toolsLuckyEnvelopesUrl}>
                                                    <AppImg
                                                        src="/assets/pa/images/tools/lucky-envelopes.png"
                                                        alt={"Lucky Envelopes"}
                                                        title={"Lucky Envelopes"}
                                                        className="header-banner-tools-figure-img"
                                                    />
                                                </AppLink>
                                            </figure>
                                        </div>
                                    )}
                                </div>
                            )}
                            {projectType === EnumProjectType.pa && (
                                <div className="header-banner-tools-list">
                                    {gameInfo.engTools.isFreeBets && userPermissions?.includes(EnumDBUserPermissions.paCanViewPageFreeBets) && (
                                        <div className="header-banner-tools-item">
                                            <figure className="header-banner-tools-figure">
                                                <AppLink
                                                    href={Project.getPageUrl(appStore, EnumPage.toolsFreeBets)}
                                                    onClick={async (e) => {
                                                        e.stopPropagation();
                                                        e.preventDefault();
                                                        const url = Project.getPageUrl(appStore,
                                                            EnumPage.toolsFreeBets);
                                                        await ClientUsersService.clientAction(
                                                            EnumUserClientAction.pageFreeBets,
                                                            location.pathname,
                                                            {
                                                                isSite: false,
                                                                gameCode: gameCode,
                                                                title: gameName,
                                                                url
                                                            },
                                                            "gamePage"
                                                        );
                                                        ClientRequest.redirect(url);
                                                    }}
                                                >
                                                    <AppImg
                                                        src="/assets/pa/images/tools/free-bets.png"
                                                        alt={"Free Bets"}
                                                        title={"Free Bets"}
                                                        className="header-banner-tools-figure-img"
                                                    />
                                                </AppLink>
                                            </figure>
                                        </div>
                                    )}
                                    {gameInfo.engTools.isMustWinJackpot && userPermissions?.includes(
                                        EnumDBUserPermissions.paCanViewPageMustWinJackpot
                                    ) && (
                                        <div className="header-banner-tools-item">
                                            <figure className="header-banner-tools-figure">
                                                <AppLink
                                                    href={Project.getPageUrl(appStore, EnumPage.toolsMustWinJackpots)}
                                                    onClick={async (e) => {
                                                        e.stopPropagation();
                                                        e.preventDefault();
                                                        const url = Project.getPageUrl(appStore,
                                                            EnumPage.toolsMustWinJackpots);
                                                        await ClientUsersService.clientAction(
                                                            EnumUserClientAction.pageMustWinJackpot,
                                                            location.pathname,
                                                            {
                                                                isSite: false,
                                                                gameCode: gameCode,
                                                                title: gameName,
                                                                url
                                                            },
                                                            "gamePage"
                                                        );
                                                        ClientRequest.redirect(url);
                                                    }}
                                                >
                                                    <AppImg
                                                        src="/assets/pa/images/tools/must-win-jackpot.png"
                                                        alt={"Must Win Jackpot"}
                                                        title={"Must Win Jackpot"}
                                                        className="header-banner-tools-figure-img"
                                                    />
                                                </AppLink>
                                            </figure>
                                        </div>
                                    )}
                                    {gameInfo.engTools.isTournaments && userPermissions?.includes(EnumDBUserPermissions.paCanViewPageTournaments) && (
                                        <div className="header-banner-tools-item">
                                            <figure className="header-banner-tools-figure">
                                                <AppLink
                                                    href={Project.getPageUrl(appStore, EnumPage.toolsTournaments)}
                                                    onClick={async (e) => {
                                                        e.stopPropagation();
                                                        e.preventDefault();
                                                        const url = Project.getPageUrl(appStore,
                                                            EnumPage.toolsTournaments);
                                                        await ClientUsersService.clientAction(
                                                            EnumUserClientAction.pageTournaments,
                                                            location.pathname,
                                                            {
                                                                isSite: false,
                                                                gameCode: gameCode,
                                                                title: gameName,
                                                                url
                                                            },
                                                            "gamePage"
                                                        );
                                                        ClientRequest.redirect(url);
                                                    }}
                                                >
                                                    <AppImg
                                                        src="/assets/pa/images/tools/tournaments.png"
                                                        alt={"Tournaments"}
                                                        title={"Tournaments"}
                                                        className="header-banner-tools-figure-img"
                                                    />
                                                </AppLink>
                                            </figure>
                                        </div>
                                    )}
                                    {gameInfo.engTools.isLuckyEnvelopes && userPermissions?.includes(
                                        EnumDBUserPermissions.paCanViewPageLuckyEnvelopes
                                    ) && (
                                        <div className="header-banner-tools-item">
                                            <figure className="header-banner-tools-figure">
                                                <AppLink
                                                    href={Project.getPageUrl(appStore, EnumPage.toolsLuckyEnvelopes)}
                                                    onClick={async (e) => {
                                                        e.stopPropagation();
                                                        e.preventDefault();
                                                        const url = Project.getPageUrl(appStore,
                                                            EnumPage.toolsLuckyEnvelopes);
                                                        await ClientUsersService.clientAction(
                                                            EnumUserClientAction.pageLuckyEnvelopes,
                                                            location.pathname,
                                                            {
                                                                isSite: false,
                                                                gameCode: gameCode,
                                                                title: gameName,
                                                                url
                                                            },
                                                            "gamePage"
                                                        );
                                                        ClientRequest.redirect(url);
                                                    }}
                                                >
                                                    <AppImg
                                                        src="/assets/pa/images/tools/lucky-envelopes.png"
                                                        alt={"Lucky Envelopes"}
                                                        title={"Lucky Envelopes"}
                                                        className="header-banner-tools-figure-img"
                                                    />
                                                </AppLink>
                                            </figure>
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </header>
        );
    }
);

export default GamePageHeader;
