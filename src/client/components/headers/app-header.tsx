import React, { ReactNode } from "react";
import MainSiteToolbar from "@components/toolbars/main-site-toolbar";
import HeaderMedia from "@components/headers/header-media";
import { observer } from "mobx-react-lite";

export interface ICmpAppHeaderProps {
    className?: string;
    imageUrl?: string;
    videoUrl?: string;
    isShadow?: boolean;
    children?: ReactNode;
}

const AppHeader = observer(({ className, imageUrl, videoUrl, isShadow = true, children }: ICmpAppHeaderProps) => {
    return (
        <header className={"header" + (className ? " " + className : "")}>
            <MainSiteToolbar />
            <div className="header-navbar-spacer" />
            <div className="header-banner">
                <HeaderMedia imageUrl={imageUrl} videoUrl={videoUrl} isShadow={isShadow} />
                {children && children}
            </div>
        </header>
    );
});

export default AppHeader;
