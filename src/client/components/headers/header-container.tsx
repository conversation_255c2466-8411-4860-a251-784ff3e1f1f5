import React from "react";
import AppImg from "@components/main/appImg";
import { IHeaderContainer } from "@components/headers/_interfaces";
import AppLink from "@components/main/appLink";

const HeaderContainer = (
    {
        title,
        accentTitle,
        contentText,
        imageUrl,
        linkUrl,
        linkText = "",
        isBottom = false,
        isStatic = false,
        btnText,
        btnLinkUrl,
        classNameHeaderBanner = "header-banner-container",
        children
    }: IHeaderContainer) => {

    return (
        <div className={classNameHeaderBanner}>
            <div className={"header-banner-content" + (isStatic ? " static" : "") + (isBottom ? " bottom" : "")}>
                {imageUrl && (
                    <figure className="header-banner-content-figure">
                        <AppImg
                            src={imageUrl}
                            alt={title || accentTitle || ""}
                            title={title || accentTitle || ""}
                            className="header-banner-content-figure-img"
                        />
                    </figure>
                )}
                {(title || accentTitle) && (
                    <div className={"header-banner-content-title"}>
                        {title} <span className="accent">{accentTitle}</span>
                    </div>
                )}
                {contentText && <p className="header-banner-content-text">{contentText}</p>}

                {linkUrl && (
                    <AppLink href={linkUrl} className="header-banner-description-link">
                        {linkText}
                    </AppLink>
                )}
                {btnText && btnLinkUrl &&(
                    <div className="header-banner-btn-container">
                        <AppLink href={btnLinkUrl} className="header-banner-btn accent" target="_blank" rel="noreferrer">
                            {btnText}
                        </AppLink>
                    </div>
                )}
            </div>
            {children && children}
        </div>
    );
};

export default HeaderContainer;
