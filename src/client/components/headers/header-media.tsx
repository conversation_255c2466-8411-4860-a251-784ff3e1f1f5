import React, { useRef } from "react";
import AppImg from "@components/main/appImg";
import AppVideo from "@components/main/appVideo";
import { IHeaderMedia } from "@components/headers/_interfaces";
import { observer } from "mobx-react-lite";

const HeaderMedia = observer(( { imageUrl, videoUrl, isShadow = true }: IHeaderMedia) => {
    const playerRef = useRef<HTMLVideoElement>(null);
    if (!imageUrl && !videoUrl) {
        return null;
    }
    return (
        <figure className="header-banner-figure">
            {videoUrl && (
                <AppVideo
                    className="header-banner-figure-video"
                    autoPlay
                    loop
                    muted
                    {...(imageUrl && { poster: imageUrl })}
                    videoRef={playerRef}
                    videoUrl={videoUrl}
                />
            )}
            {imageUrl && !videoUrl && <AppImg src={imageUrl} className={"header-banner-figure-img"} />}
            {isShadow && <div className="header-banner-shadow" />}
        </figure>
    );
});

export default HeaderMedia;
