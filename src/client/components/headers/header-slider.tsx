import React, { useMemo } from "react";
import { Swiper, SwiperProps, SwiperSlide } from "swiper/react";
import { Autoplay, Mousewheel, Navigation, Pagination } from "swiper";

import "swiper/css";
import "swiper/css/autoplay";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/mousewheel";
import { observer } from "mobx-react-lite";
import { AutoplayOptions, PaginationOptions } from "swiper/types";
import { IHeaderSlider } from "@components/headers/_interfaces";
import HeaderLink from "@components/headers/header-link";
import HeaderMedia from "@components/headers/header-media";
import HeaderContainer from "@components/headers/header-container";

export interface ICmpHeaderSliderProps {
    swiperProps?: SwiperProps,
    data: IHeaderSlider[]
}

const HeaderSlider = observer(({ swiperProps, data }: ICmpHeaderSliderProps) => {

    const autoPlayProps = useMemo(() => {
        return {
            delay: 4000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true
        };
    }, []) as AutoplayOptions;

    const paginationProps = useMemo(() => {
        return {
            clickable: true,
            type: "bullets"
        };
    }, []) as PaginationOptions;

    return (
        <Swiper
            allowTouchMove={true}
            modules={[Autoplay, Navigation, Pagination, Mousewheel]}
            spaceBetween={0}
            grabCursor={true}
            watchSlidesProgress={true}
            mousewheel={false}
            loop={true}
            speed={500}
            autoplay={autoPlayProps}
            centeredSlides={true}
            pagination={paginationProps}
            className={"swiper-container swiper-header"}
            {...swiperProps && swiperProps}
        >
            {data.map((slider, index) => {
                return <SwiperSlide
                    key={"hs" + index}
                    className={"header-banner"}
                    {...slider.children.link.href && {
                        onClick: () => {
                            location.assign(slider.children.link.href);
                        }
                    }}
                    {...slider.sliderProps}
                    {...slider.dataProps && slider.dataProps.swiperAutoplay && { "data-swiper-autoplay": slider.dataProps.swiperAutoplay }}
                >
{/*                    {({ isActive, isVisible, isNext }) =>
                        (isActive || isVisible || isNext || !Video.isUrlVideoHls(slider.children.media?.videoUrl)
                         ? <>
                             <HeaderLink {...slider.children.link} />
                             <HeaderMedia {...slider.children.media} />
                             <HeaderContainer {...slider.children.container} />
                         </>
                         : null
                        )}*/}
                    <>
                        <HeaderLink {...slider.children.link} />
                        <HeaderMedia {...slider.children.media} />
                        <HeaderContainer {...slider.children.container} />
                    </>
                </SwiperSlide>;
            })}
            <div className="header-banner-shadow" />
        </Swiper>
    );
});

export default HeaderSlider;