import React from "react";
import { EnumLanguageCode } from "models/enum/system";
import { INewsExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import NewsCard from "@components/news/newsCard";

const NewsList = ({ news/*, languageCode*/ }: { news: INewsExtended[]; languageCode: EnumLanguageCode }) => {
    const target = React.useRef(null);

    const id = "app-news-list";

    return (
        <div id={id} className={"sw-news__items"} ref={target}>
            <div className={"sw-news-grid"}>
                {
                    Array.isArray(news) &&
                        news.map((n) => {
                            // <VirtuosoGrid
                            //     totalCount={props.news.length}
                            //     listClassName={"sw-news-grid"}
                            //     itemClassName={"news-div"}
                            //     components={
                            //         {
                            //             // Item: ItemContainer,
                            //             // List: ListContainer,
                            //             // ScrollSeekPlaceholder: ({ height, width, index }) => (
                            //             //     <ItemContainer>
                            //             //         <ItemWrapper>{"--"}</ItemWrapper>
                            //             //     </ItemContainer>
                            //             // ),
                            //         }
                            //     }
                            //     itemContent={(index) => {
                            // const n = props.news[index];
                            // if (!n) {
                            //     return <div></div>;
                            // }
                            // return <ItemWrapper>Item {index}</ItemWrapper>;
                            //
                            return <NewsCard data={n} key={n._id} />;
                            //     }}
                            // />
                        })
                    // })}
                }
            </div>
        </div>
    );
    /*<div id={id} className={"sw-news__items grid"} ref={target}>
        {Array.isArray(props.news) && (
            <Virtuoso
                totalCount={props.news.length}
                itemContent={(index) => {
                    const n = props.news[index];
                    const highlightedTitle = getLocale(n.highlightedTitle as ILocaleString, props.languageCode);
                    const title = getLocale(n.title as ILocaleString, props.languageCode);
                    const categories = n.newsCategories;
                    const date = moment(n.newsDate);
                    return (
                        <AppLink
                            href={Project.getUrl(locale, config.client.endpoints.news.info, {
                                newsId: n._id as string,
                            })}
                            key={n._id}
                            className={`news-item date-${date.format("x")} month-${date.format("MM")}`}
                            onClick={async (e) => {
                                const AppLink = e.currentTarget as HTMLAnchorElement;
                                e.stopPropagation();
                                e.preventDefault();
                                await ClientUsersService.clientAction(
                                    EnumUserClientAction.pageNewsInfo,
                                    location.pathname,
                                    { newsId: n._id as string, title: highlightedTitle + " " + title }
                                );
                                ClientRequest.redirect(AppLink.href);
                            }}
                        >
                            <div className="news-item__img">
                                <AppImg src={n.newsPreviewImageUrl} alt={highlightedTitle + " " + title} />
                            </div>
                            <div className="news-item__info">
                                <div className="news-item__label-list">
                                    {categories
                                        ?.map((c) => {
                                            const categoryTitle = getLocale(c?.newsCategoryTitle as ILocaleString, props.languageCode).toLowerCase();
                                            return (
                                                <>
                                                    <div className={"news-item__label " + categoryTitle}>{categoryTitle}</div>
                                                </>
                                            );
                                        })
                                    }
                                </div>
                                <div className="news-item__title">
                                    {highlightedTitle} {title}
                                </div>
                                <time className="news-item__date">{date.format("MMMM DD, YYYY")}</time>
                            </div>
                        </AppLink>
                    );
                }}
            />
        )}
    </div>*/
};

export default NewsList;
