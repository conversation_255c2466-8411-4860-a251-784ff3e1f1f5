import React, { useContext } from "react";
import moment from "moment/moment";
import { SanityApi } from "utils/sanityApi";
import { ILocaleString } from "api/data-providers/sanity/schemas";
import { EnumLanguageCode, EnumProjectType } from "models/enum/system";
import { IGameExtended, INewsExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { Button } from "react-bootstrap";
import { Game } from "utils/game";
import ClientUsersService from "../../services/clientUsersService";
import { EnumUserClientAction } from "models/enum/user";
import { Project } from "utils/project";
import AppLink from "@components/main/appLink";
import SectionSwiperNews from "@components/sections/swiper-news/section-swiper-news";
import { IAppServerConfig } from "appServerConfig";
import { MainAppContext } from "@pages/_app";
import { EnumPage } from "models/enum/page";

const BlockContent = require("@sanity/block-content-to-react");

interface ICmpNewsArticleProps {
    appServerConfig: IAppServerConfig;
    article: INewsExtended;
    prevArticleId?: string | null;
    nextArticleId?: string | null;
    moreNewsArticles?: INewsExtended[] | null;
    languageCode: EnumLanguageCode;
    newsListUrl: string;
}

const NewsArticle = (
    {
        appServerConfig, article, prevArticleId, nextArticleId, moreNewsArticles, languageCode, newsListUrl
    }: ICmpNewsArticleProps) => {
    const { appStore } = useContext(MainAppContext);
    const project = appStore.getAppProject();
    const projectType = project.type;

    const newsHighlightedTitle = SanityApi.getLocale(article.highlightedTitle as ILocaleString, languageCode);
    const newsTitle = SanityApi.getLocale(article.title as ILocaleString, languageCode);
    const newsDate = moment(article.newsDate);
    const newsText = SanityApi.getLocale(article.text as ILocaleString, languageCode);
    const newsBlocks = SanityApi.getLocale(article.block as ILocaleString, languageCode);
    const categories = article.newsCategories;

    const gameId = article.game?.gameId?.current;
    const gameCode = Game.getFirstGameCode(article.game as IGameExtended);

    const gamePageUrl = gameId ? Project.getPageUrl(appStore, EnumPage.gameDetails, { gameId }) : "";

    // if (news.newsType === "partnerships") {
    return (
        <div className={"news-article"}>
            <div className="news-article__headline">
                <time className="news-article__date">{newsDate.format("MMMM DD, YYYY")}</time>
                {categories?.map((c) => {
                    const categoryTitle = SanityApi.getLocale(
                        c?.newsCategoryTitle as ILocaleString,
                        languageCode
                    ).toLowerCase();
                    return (
                        <>
                            <div className="news-article__divider" />
                            <div className={"news-article__label"}>{categoryTitle}</div>
                        </>
                    );
                })}
            </div>
            <h3 className="news-article__title">
                {newsHighlightedTitle} {newsTitle}
            </h3>
            <div className="news-article__desc">
                {article.block && (
                    <BlockContent
                        blocks={newsBlocks}
                        projectId={appServerConfig.sanity.client.projectId}
                        dataset={appServerConfig.sanity.client.dataset}
                    />
                )}
                {article.text && newsText}

                {gameId && (
                    <Button
                        href={gamePageUrl}
                        target={"_blank"}
                        style={{ color: "#000" }}
                        onClick={async () => {
                            // e.stopPropagation();
                            // e.preventDefault();
                            await ClientUsersService.clientAction(EnumUserClientAction.gamePage, location.pathname, {
                                gameCode,
                                isSite: projectType === EnumProjectType.site,
                                url: gamePageUrl
                            });
                            // ClientRequest.openInNewTab(gamePageUrl);
                            return true
                        }}
                    >
                        Go to game page
                    </Button>
                )}
            </div>
            {prevArticleId || nextArticleId &&
                <div className="news-article__pagination">
                    {prevArticleId &&
                        <AppLink href={Project.getPageUrl(appStore, EnumPage.newsArticle, {
                            newsId: prevArticleId
                        })} className="news-article__pagination-btn-prev">
                            <div className="news-article__pagination-label">Previous</div>
                        </AppLink>}
                    {nextArticleId &&
                        <AppLink href={Project.getPageUrl(appStore, EnumPage.newsArticle, {
                            newsId: nextArticleId
                        })} className="news-article__pagination-btn-next">
                            <div className="news-article__pagination-label">Next</div>
                        </AppLink>
                    }
                </div>
            }
            {Array.isArray(moreNewsArticles) && moreNewsArticles.length > 0 &&
                <SectionSwiperNews
                    className={"sw-section sw-news__items news-home"}
                    sectionTitle={"More News"}
                    data={moreNewsArticles}
                    sectionButtonText={"All News"}
                    sectionButtonUrl={newsListUrl} />
            }
        </div>
    );
    // }

    // return null;
};

export default NewsArticle;
