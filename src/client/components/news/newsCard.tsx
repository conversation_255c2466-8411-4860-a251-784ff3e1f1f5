import React, { useContext } from "react";
import { SanityApi } from "utils/sanityApi";
import { ILocaleString } from "api/data-providers/sanity/schemas";
import moment from "moment/moment";
import AppLink from "@components/main/appLink";
import { Project } from "utils/project";
import { EnumProjectType } from "models/enum/system";
import appConfig from "appConfig";
import ClientUsersService from "client/services/clientUsersService";
import { EnumUserClientAction } from "models/enum/user";
import AppImg from "@components/main/appImg";
import { INewsExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { observer } from "mobx-react-lite";
import { EnumPage } from "models/enum/page";
import { MainAppContext } from "@pages/_app";

export interface ICmpNewsCardProps {
    data: INewsExtended;
    className?: string;
}

const NewsCard = observer(({ data, className }: ICmpNewsCardProps) => {
    const { appStore } = useContext(MainAppContext);

    const projectType = appStore.getAppProject().type;

    const languageCode = appConfig.systemSettings.defaultLanguageCode;

    const highlightedTitle = SanityApi.getLocale(data.highlightedTitle as ILocaleString, languageCode);
    const title = SanityApi.getLocale(data.title as ILocaleString, languageCode);
    const categories = data.newsCategories;
    // const categoriesTitle = getLocale(n.newsCategoryTitle as ILocaleString,
    // props.languageCode);
    const date = moment(data.newsDate);

    const newsCardPageUrl = Project.getPageUrl(appStore, EnumPage.newsArticle, { newsId: data._id as string });

    return (
        <div className={"news-div" + (className ? " " + className : "")} key={data._id}>
            <AppLink
                href={newsCardPageUrl}
                onClick={async () => {
                    if (projectType === EnumProjectType.pa) {
                        await ClientUsersService.clientAction(
                            EnumUserClientAction.pageNewsInfo,
                            location.pathname,
                            {
                                newsId: data._id,
                                isSite: false
                            },
                            "newsList"
                        );
                    }
                }}
                key={data._id}
                id={data._id}
                className={`news-item date-${date.format("x")} month-${date.format("MM")}`}
            >
                <div className="news-item__img">
                    <AppImg src={data.newsPreviewImageUrl} alt={highlightedTitle + " " + title} />
                </div>
                <div className="news-item__info">
                    <div className="news-item__label-list">
                        {categories?.map((c) => {
                            const categoryTitle = SanityApi.getLocale(
                                c?.newsCategoryTitle as ILocaleString,
                                languageCode
                            ).toLowerCase();
                            return (
                                <div key={c?._id} className={"news-item__label " + categoryTitle}>
                                    {categoryTitle}
                                </div>
                            );
                        })}
                    </div>
                    <div className="news-item__title">
                        {highlightedTitle} {title}
                    </div>
                    <time className="news-item__date">{date.format("MMMM DD, YYYY")}</time>
                </div>
            </AppLink>
        </div>
    );
});

export default NewsCard;
