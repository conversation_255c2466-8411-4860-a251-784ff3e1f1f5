import React, { useContext } from "react";
import appConfig from "appConfig";
import { BreadcrumbJsonLd, NextSeo, WebPageJsonLd } from "next-seo";
import { useRouter } from "next/router";
import { EnumLocale, getLocaleMessages } from "locales/locales";
import { observer } from "mobx-react-lite";
import AppH1 from "@components/main/appH1";
import { useSitePageInfo } from "@hooks/use-site-page-info";
import { EnumPage } from "models/enum/page";
import { Project } from "utils/project";
import { Seo } from "utils/seo";
import { MainAppContext } from "@pages/_app";

export interface ICmpSeoSitePageProps {
    pageTitle: string;
    pageUrlAbsolute: string;
    pageUrlRelative: string;
    pageDescription: string;
    pageImage: { url: string, width: number, height: number };
}

const SeoSitePage = observer((
    { pageTitle, pageUrlAbsolute, pageUrlRelative, pageDescription, pageImage }: ICmpSeoSitePageProps) => {
    const { appStore } = useContext(MainAppContext);
    const project = appStore.getAppProject();
    const router = useRouter();

    const locale = router.locale as EnumLocale;
    const msg = getLocaleMessages(locale).messages;

    const pageTitleExt = pageTitle + " | " + msg.skywindCompanyName;

    const {pageUrlAbsolute: homePageUrl} = useSitePageInfo(EnumPage.home);

    return (
        <>
            <AppH1 title={pageTitleExt} />
            <NextSeo
                robotsProps={{
                    maxImagePreview: "large"
                }}
                title={pageTitleExt}
                titleTemplate={pageTitleExt}
                defaultTitle={pageTitleExt}
                description={pageDescription}
                canonical={"https://" + Seo.getCanonicalDomain(project) + pageUrlRelative}
                openGraph={{
                    type: "website",
                    locale: appConfig.systemSettings.defaultLanguageCode,
                    url: pageUrlAbsolute,
                    site_name: msg.skywindCompanyName,
                    title: pageTitleExt,
                    description: pageDescription,
                    images: [
                        ...(pageImage ? [
                            {
                                url: Project.replaceSanityCdn(pageImage.url),
                                width: pageImage.width,
                                height: pageImage.height,
                                alt: pageTitleExt
                            }
                        ] : [])
                    ]
                }} />
            <WebPageJsonLd id={pageUrlAbsolute} description={pageDescription} />
            <BreadcrumbJsonLd
                itemListElements={[
                    {
                        position: 1,
                        name: msg.skywindCompanyName,
                        item: homePageUrl
                    },
                    {
                        position: 2,
                        name: pageTitle,
                        item: pageUrlAbsolute
                    }
                ]}
            />
        </>
    );
});

export default SeoSitePage;