import React, { useContext } from "react";
import { BreadcrumbJsonLd, NextSeo, VideoGameJsonLd } from "next-seo";
import { observer } from "mobx-react-lite";
import appConfig from "appConfig";
import { useRouter } from "next/router";
import { EnumLocale, getLocaleMessages } from "locales/locales";
import AppH1 from "@components/main/appH1";
import { useSitePageInfo } from "@hooks/use-site-page-info";
import { EnumPage } from "models/enum/page";
import { Seo } from "utils/seo";
import { MainAppContext } from "@pages/_app";

export interface ICmpSeoSiteGameDetailsProps {
    pageTitle: string;
    gameName: string;
    gameDescription: string;
    gamePageUrlRelative: string;
    gamePosterUrl: string;
    gameHDPosterUrl: string;
}

const SeoSiteGameDetails = observer((
    {
        pageTitle, gameName, gameDescription, gamePageUrlRelative, gamePosterUrl, gameHDPosterUrl
    }: ICmpSeoSiteGameDetailsProps) => {

    const { appStore } = useContext(MainAppContext);
    const project = appStore.getAppProject();

    const router = useRouter();

    const locale = router.locale as EnumLocale;
    const msg = getLocaleMessages(locale).messages;

    const { pageUrlAbsolute: homePageUrl } = useSitePageInfo(EnumPage.home);
    const { pageUrlAbsolute: gamesPageUrl, pageTitle: gamesPageTitle } = useSitePageInfo(EnumPage.gamesList);

    return (
        <>
            <AppH1 title={pageTitle} />
            <NextSeo
                robotsProps={{
                    maxImagePreview: "large"
                }}
                title={pageTitle}
                titleTemplate={gameName}
                defaultTitle={gameName}
                description={gameDescription}
                canonical={"https://" + Seo.getCanonicalDomain(project) + gamePageUrlRelative}
                openGraph={{
                    type: "website",
                    locale: appConfig.systemSettings.defaultLanguageCode,
                    url: gamePageUrlRelative,
                    site_name: msg.skywindCompanyName,
                    title: pageTitle,
                    description: gameDescription,
                    images: [
                        ...(gamePosterUrl ? [
                            {
                                url: gamePosterUrl,
                                width: 428,
                                height: 268,
                                alt: gameName
                            }
                        ] : []),
                        ...(gameHDPosterUrl ? [
                            {
                                url: gameHDPosterUrl,
                                width: 1920,
                                height: 1080,
                                alt: gameName
                            }
                        ] : [])
                    ]
                }} />
            <VideoGameJsonLd name={gameName} description={gameDescription}
                             url={gamePageUrlRelative}
                             authorName={msg.skywindCompanyName}
                             image={gamePosterUrl || gameHDPosterUrl}
                             applicationCategory={"Game"}
                             operatingSystemName={"Multi-platform"}
                             platformName={["Microsoft Windows", "OSX", "Android"]}
                             languageName={appConfig.systemSettings.defaultLanguageCode}
            />
            <BreadcrumbJsonLd
                itemListElements={[
                    {
                        position: 1,
                        name: msg.skywindCompanyName,
                        item: homePageUrl
                    },
                    {
                        position: 2,
                        name: gamesPageTitle,
                        item: gamesPageUrl
                    },
                    {
                        position: 3,
                        name: gameName,
                        item: gamePageUrlRelative
                    }
                ]}
            />
        </>
    );
});

export default SeoSiteGameDetails;