import React, { useContext } from "react";
import { observer } from "mobx-react-lite";
import AppH1 from "@components/main/appH1";
import appConfig from "appConfig";
import { BreadcrumbJsonLd, NewsArticleJsonLd, NextSeo } from "next-seo";
import { useRouter } from "next/router";
import { EnumLocale, getLocaleMessages } from "locales/locales";
import { INewsExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { JsUtils } from "utils/js-utils";
import { SanityApi } from "utils/sanityApi";
import { useSiteTitle } from "@hooks/use-site-title";
import { ILocaleString } from "api/data-providers/sanity/schemas";
import moment from "moment";
import { useSitePageInfo } from "@hooks/use-site-page-info";
import { EnumPage } from "models/enum/page";
import { Project } from "utils/project";
import { MainAppContext } from "@pages/_app";
import { Seo } from "utils/seo";

export interface ICmpSeoNewsArticleProps {
    newsArticle: INewsExtended;
}

const SeoNewsArticle = observer(({ newsArticle }: ICmpSeoNewsArticleProps) => {

    const { appStore } = useContext(MainAppContext);
    const project = appStore.getAppProject();
    const router = useRouter();

    if (!JsUtils.isObjectAndNotEmpty(newsArticle) || !newsArticle._id) {
        return null;
    }

    const locale = router.locale as EnumLocale;
    const msg = getLocaleMessages(locale).messages;
    const languageCode = appConfig.systemSettings.defaultLanguageCode;

    const { pageUrlAbsolute: homePageUrl } = useSitePageInfo(EnumPage.home);
    const { pageUrlAbsolute: newsPageUrl, pageTitle: newsPageTitle } = useSitePageInfo(EnumPage.newsList);
    const {
        pageUrlAbsolute: newsArticlePageUrlAbsolute,
        pageUrlRelative: newsArticlePageUrlRelative
    } = useSitePageInfo(EnumPage.newsArticle,
        { newsId: String(newsArticle._id) });

    const title = SanityApi.getLocale(newsArticle.title, languageCode);
    const highlightedTitle = SanityApi.getLocale(newsArticle.highlightedTitle, languageCode);

    const newsArticleTitle = (highlightedTitle + " " || "") + (title || "");
    const newsText = SanityApi.getLocale(newsArticle.text as ILocaleString, languageCode);

    const { siteTitle } = useSiteTitle(newsArticleTitle);

    const newsDate = moment(newsArticle.newsDate);

    const newsArticleImageUrl = Project.replaceSanityCdn(newsArticle.newsPreviewImageUrl);

    return (
        <>
            <AppH1 title={siteTitle} />
            <NextSeo
                robotsProps={{
                    maxImagePreview: "large"
                }}
                title={siteTitle}
                titleTemplate={newsArticleTitle}
                defaultTitle={newsArticleTitle}
                description={newsText}
                canonical={"https://" + Seo.getCanonicalDomain(project) + newsArticlePageUrlRelative}
                openGraph={{
                    type: "website",
                    locale: appConfig.systemSettings.defaultLanguageCode,
                    url: newsArticlePageUrlAbsolute,
                    site_name: msg.skywindCompanyName,
                    title: siteTitle,
                    description: newsText,
                    images: [
                        ...(newsArticleImageUrl ? [
                            {
                                url: newsArticleImageUrl,
                                width: 428,
                                height: 268,
                                alt: newsArticleTitle
                            }
                        ] : [])
                    ]
                }} />
            <NewsArticleJsonLd url={newsArticlePageUrlAbsolute} title={newsArticleTitle}
                               images={[...(newsArticleImageUrl ? [newsArticleImageUrl] : [])]}
                               section={newsArticle.newsCategories?.map(c =>
                                   SanityApi.getLocale(c?.newsCategoryTitle, languageCode)
                               ).join(", ") || ""}
                               keywords={""}
                               dateCreated={newsDate.format("YYYY-MM-DD")}
                               datePublished={newsDate.format("YYYY-MM-DD")}
                               authorName={String(msg.skywindCompanyName)}
                               description={newsText} body={newsText}
                               publisherName={String(msg.skywindCompanyName)} publisherLogo={""} />
            <BreadcrumbJsonLd
                itemListElements={[
                    {
                        position: 1,
                        name: msg.skywindCompanyName,
                        item: homePageUrl
                    },
                    {
                        position: 2,
                        name: newsPageTitle,
                        item: newsPageUrl
                    },
                    {
                        position: 3,
                        name: newsArticleTitle,
                        item: newsArticleImageUrl
                    }
                ]}
            />
        </>
    );
});

export default SeoNewsArticle;