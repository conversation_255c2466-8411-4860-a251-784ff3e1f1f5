import React, { useEffect, useRef, useState } from "react";
import moment from "moment";
import { observer } from "mobx-react-lite";

export interface ICmpFlipCountdownProps {
    children?: React.ReactNode;
    theme?: string;
    size?: any;
    endAt?: any;
    monthTitle?: string;
    yearTitle?: string;
    dayTitle?: string;
    hourTitle?: string;
    minuteTitle?: string;
    secondTitle?: string;
    hideYear?: any;
    hideMonth?: any;
    hideDay?: any;
    hideHour?: any;
    hideMinute?: any;
    hideSecond?: any;
    titlePosition?: any;
    endAtZero?: any;
}

const FlipCountDown = observer(({
    children,
    theme = "dark",
    size = "medium",
    endAt = moment(),
    monthTitle = "Months",
    yearTitle = "Years",
    dayTitle = "Days",
    hourTitle = "Hours",
    minuteTitle = "Minutes",
    secondTitle = "Seconds",
    hideYear,
    hideMonth,
    hideDay,
    hideHour,
    hideMinute,
    hideSecond,
    titlePosition = "top",
    endAtZero,
}: ICmpFlipCountdownProps) => {
    const [completed, setCompleted] = useState(false);
    const title: { [x: string]: string } = { monthTitle, yearTitle, dayTitle, hourTitle, minuteTitle, secondTitle };
    const clock: { [key: string]: { title: string; value: any; prevValue: any; ref: any } } = {
        year: {
            title: "Year",
            value: useState(0),
            prevValue: useState(0),
            ref: useRef(null),
        },
        month: {
            title: "Month",
            value: useState(0),
            prevValue: useState(0),
            ref: useRef(null),
        },
        day: {
            title: "Day",
            value: useState(0),
            prevValue: useState(0),
            ref: useRef(null),
        },
        hour: {
            title: "Hour",
            value: useState(0),
            prevValue: useState(0),
            ref: useRef(null),
        },
        minute: {
            title: "Minute",
            value: useState(0),
            prevValue: useState(0),
            ref: useRef(null),
        },
        second: {
            title: "Second",
            value: useState(0),
            prevValue: useState(0),
            ref: useRef(null),
        },
    };

    let interval: unknown | null | number | undefined = null;
    let prev = moment.duration(moment().diff(moment()));

    useEffect(() => {
        processClock();
        // eslint-disable-next-line react-hooks/exhaustive-deps
        interval = setInterval(() => {
            processClock();
        }, 1000);

        return () => {
            if (interval) {
                clearInterval(interval as NodeJS.Timeout);
            }
        };
    }, [endAt]);

    const processClock = () => {
        const then = moment(endAt);
        const value = moment.duration(then.diff(moment()));

        if (value.milliseconds() < 0) {
            setCompleted(true);
            clearInterval(Number(interval));
            return;
        }

        // Year
        if (!hideYear) {
            clock.year.value[1](value.years());
            clock.year.prevValue[1](prev.years());
            if (
                parseInt(String(value.years() / 10)) !== parseInt(String(prev.years() / 10)) &&
                clock.year.ref.current
            ) {
                const section = (clock.year.ref.current as HTMLElement).querySelector(".flip-countdown-card-sec.one");
                section?.classList.remove("flip");
                // @ts-ignore
                void section?.offsetWidth;
                section?.classList.add("flip");
            }

            if (
                parseInt(String(value.years() % 10)) !== parseInt(String(prev.years() % 10)) &&
                clock.year.ref.current
            ) {
                const section = (clock.year.ref.current as HTMLElement).querySelector(".flip-countdown-card-sec.two");
                section?.classList.remove("flip");
                // @ts-ignore
                void section?.offsetWidth;
                section?.classList.add("flip");
            }
        }

        // Months
        if (!hideMonth) {
            clock.month.value[1](value.months());
            clock.month.prevValue[1](prev.months());
            if (
                parseInt(String(value.months() / 10)) !== parseInt(String(prev.months() / 10)) &&
                clock.month.ref.current
            ) {
                const section = (clock.month.ref.current as HTMLElement).querySelector(".flip-countdown-card-sec.one");
                section?.classList.remove("flip");
                // @ts-ignore
                void section?.offsetWidth;
                section?.classList.add("flip");
            }

            if (
                parseInt(String(value.months() % 10)) !== parseInt(String(prev.months() % 10)) &&
                clock.month.ref.current
            ) {
                const section = (clock.month.ref.current as HTMLElement).querySelector(".flip-countdown-card-sec.two");
                section?.classList.remove("flip");
                // @ts-ignore
                void section?.offsetWidth;
                section?.classList.add("flip");
            }
        }

        // Days
        if (!hideDay) {
            clock.day.value[1](value.days());
            clock.day.prevValue[1](prev.days());
            if (parseInt(String(value.days() / 10)) !== parseInt(String(prev.days() / 10)) && clock.day.ref.current) {
                const section = (clock.day.ref.current as HTMLElement).querySelector(".flip-countdown-card-sec.one");
                section?.classList.remove("flip");
                // @ts-ignore
                void section?.offsetWidth;
                section?.classList.add("flip");
            }

            if (parseInt(String(value.days() % 10)) !== parseInt(String(prev.days() % 10)) && clock.day.ref.current) {
                const section = (clock.day.ref.current as HTMLElement).querySelector(".flip-countdown-card-sec.two");
                section?.classList.remove("flip");
                // @ts-ignore
                void section?.offsetWidth;
                section?.classList.add("flip");
            }
        }

        // Hours
        if (!hideHour) {
            clock.hour.value[1](value.hours());
            clock.hour.prevValue[1](prev.hours());
            if (
                parseInt(String(value.hours() / 10)) !== parseInt(String(prev.hours() / 10)) &&
                clock.hour.ref.current
            ) {
                const section = (clock.hour.ref.current as HTMLElement).querySelector(".flip-countdown-card-sec.one");
                section?.classList.remove("flip");
                // @ts-ignore
                void section?.offsetWidth;
                section?.classList.add("flip");
            }

            if (
                parseInt(String(value.hours() % 10)) !== parseInt(String(prev.hours() % 10)) &&
                clock.hour.ref.current
            ) {
                const section = (clock.hour.ref.current as HTMLElement).querySelector(".flip-countdown-card-sec.two");
                section?.classList.remove("flip");
                // @ts-ignore
                void section?.offsetWidth;
                section?.classList.add("flip");
            }
        }

        // Minutes
        if (!hideMinute) {
            clock.minute.value[1](value.minutes());
            clock.minute.prevValue[1](prev.minutes());
            if (
                parseInt(String(value.minutes() / 10)) !== parseInt(String(prev.minutes() / 10)) &&
                clock.minute.ref.current
            ) {
                const section = (clock.minute.ref.current as HTMLElement).querySelector(".flip-countdown-card-sec.one");
                section?.classList.remove("flip");
                // @ts-ignore
                void section?.offsetWidth;
                section?.classList.add("flip");
            }

            if (
                parseInt(String(value.minutes() % 10)) !== parseInt(String(prev.minutes() % 10)) &&
                clock.minute.ref.current
            ) {
                const section = (clock.minute.ref.current as HTMLElement).querySelector(".flip-countdown-card-sec.two");
                section?.classList.remove("flip");
                // @ts-ignore
                void section?.offsetWidth;
                section?.classList.add("flip");
            }
        }

        // Seconds
        if (!hideSecond) {
            clock.second.value[1](value.seconds());
            clock.second.prevValue[1](prev.seconds());
            if (
                parseInt(String(value.seconds() / 10)) !== parseInt(String(prev.seconds() / 10)) &&
                clock.second.ref.current
            ) {
                const section = (clock.second.ref.current as HTMLElement).querySelector(".flip-countdown-card-sec.one");
                section?.classList.remove("flip");
                // @ts-ignore
                void section?.offsetWidth;
                section?.classList.add("flip");
            }

            if (
                parseInt(String(value.seconds() % 10)) !== parseInt(String(prev.seconds() % 10)) &&
                clock.second.ref.current
            ) {
                const section = (clock.second.ref.current as HTMLElement).querySelector(".flip-countdown-card-sec.two");
                section?.classList.remove("flip");
                // @ts-ignore
                void section?.offsetWidth;
                section?.classList.add("flip");
            }
        }

        prev = value;
    };

    const getPiece = (key: string) => {
        const data = clock[key];
        const [value] = data.value;
        const [prevValue] = data.prevValue;
        const part1 = parseInt(String(value / 10));
        const part2 = parseInt(String(value % 10));
        const prev1 = parseInt(String(prevValue / 10));
        const prev2 = parseInt(String(prevValue % 10));

        return (
            <span className="flip-countdown-piece" ref={data.ref}>
                {"top" === titlePosition && (
                    <span className="flip-countdown-title">{title[key + "Title"] || data.title}</span>
                )}
                <span className="flip-countdown-card">
                    <span className="flip-countdown-card-sec one">
                        <span className="card__top">{part1}</span>
                        <span className="card__bottom" data-value={prev1} />
                        <span className="card__back" data-value={prev1}>
                            <span className="card__bottom" data-value={part1} />
                        </span>
                    </span>
                    <span className="flip-countdown-card-sec two">
                        <span className="card__top">{part2}</span>
                        <span className="card__bottom" data-value={prev2} />
                        <span className="card__back" data-value={prev2}>
                            <span className="card__bottom" data-value={part2} />
                        </span>
                    </span>
                </span>
                {"bottom" === titlePosition && (
                    <span className="flip-countdown-title">{title[key + "Title"] || data.title}</span>
                )}
            </span>
        );
    };

    if (completed && endAtZero) {
        return null;
    }

    if (completed && !endAtZero) {
        return <div className="flip-countdown">{children || endAt}</div>;
    }

    return (
        <div className={`flip-countdown theme-${theme} size-${size}`}>
            {!hideYear && getPiece("year")}
            {!hideMonth && getPiece("month")}
            {!hideDay && getPiece("day")}
            {!hideHour && getPiece("hour")}
            {!hideMinute && getPiece("minute")}
            {!hideSecond && getPiece("second")}
        </div>
    );
});

export default FlipCountDown;
