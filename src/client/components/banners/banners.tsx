import React, { useMemo } from "react";

import "swiper/css";
import "swiper/css/autoplay";
import "swiper/css/mousewheel";

import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Mousewheel } from "swiper";
import Banner from "./banner";
import { IBannerExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { observer } from "mobx-react-lite";

export interface ICmpBannersProps {
    className: string;
    banners: IBannerExtended[];
    autoPlay: number;
    loop: boolean;
}

const Banners = observer(({ className, banners, autoPlay = 10, loop = true }: ICmpBannersProps) => {

    const autoPlayProps = useMemo(() => autoPlay > 0 && {
        autoplay: {
            delay: autoPlay * 1000,
            disableOnInteraction: true
        }
    }, [autoPlay]);

    if (!Array.isArray(banners) || banners.length < 1) {
        return null;
    }

    return (
        <Swiper
            modules={[Autoplay, Mousewheel]}
            slidesPerView={"auto"}
            spaceBetween={0}
            grabCursor={true}
            centeredSlides={true}
            watchSlidesProgress={true}
            {...autoPlayProps}
            mousewheel={true}
            loop={loop}
            className={"banner-swiper " + className}
        >
            {banners.map((banner) =>
                <SwiperSlide className={"banner-main-slide"} key={banner._id}>
                    <Banner className={"banner-main"} banner={banner} />
                </SwiperSlide>
            )}
        </Swiper>
    );
});

export default Banners;
