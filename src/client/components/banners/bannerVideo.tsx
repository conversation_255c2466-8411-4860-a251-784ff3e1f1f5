import React, { ReactNode, useRef } from "react";
import { observer } from "mobx-react-lite";
import { Project } from "utils/project";
import AppVideo from "@components/main/appVideo";

interface IVideo {
    src: string;
    title?: string;
    poster?: string;
    controls?: boolean;
    autoPlay?: boolean;
    loop?: boolean;
    muted?: boolean;
}

interface ITemplateFunction {
    children?: ReactNode;
    id?: string;
    className?: string;
    desktop?: IVideo;
    mobileLandscape?: IVideo;
    mobilePortrait?: IVideo;
}

const BannerVideo = observer(({ id, className, desktop, mobileLandscape, mobilePortrait }: ITemplateFunction) => {
    const target = React.useRef(null);

    const playerRef = useRef<HTMLVideoElement>(null);

    return (
        <div className={"banner-video " + (className || "")} id={id} ref={target}>
            {desktop && (
                <AppVideo
                    className={"desktop-video"}
                    videoUrl={Project.replaceSanityCdn(desktop.src as string)}
                    videoRef={playerRef}
                    title={desktop.title}
                    posterUrl={desktop.poster}
                    {...(desktop.controls === true && { controls: true })}
                    {...(desktop.autoPlay === true && { autoPlay: true })}
                    {...(desktop.loop === true && { loop: true })}
                    {...(desktop.muted === true && { muted: true })}
                />
            )}
            {mobileLandscape && (
                <AppVideo
                    className={"mobile-landscape-video"}
                    videoUrl={Project.replaceSanityCdn(mobileLandscape.src as string)}
                    videoRef={playerRef}
                    title={mobileLandscape.title}
                    posterUrl={mobileLandscape.poster}
                    {...(mobileLandscape.controls === true && { controls: true })}
                    {...(mobileLandscape.autoPlay === true && { autoPlay: true })}
                    {...(mobileLandscape.loop === true && { loop: true })}
                    {...(mobileLandscape.muted === true && { muted: true })}
                />
            )}
            {mobilePortrait && (
                <AppVideo
                    className={"mobile-portrait-video"}
                    videoUrl={Project.replaceSanityCdn(mobilePortrait.src as string)}
                    videoRef={playerRef}
                    title={mobilePortrait.title}
                    posterUrl={mobilePortrait.poster}
                    {...(mobilePortrait.controls === true && { controls: true })}
                    {...(mobilePortrait.autoPlay === true && { autoPlay: true })}
                    {...(mobilePortrait.loop === true && { loop: true })}
                    {...(mobilePortrait.muted === true && { muted: true })}
                />
            )}
        </div>
    );
});

export default BannerVideo;
