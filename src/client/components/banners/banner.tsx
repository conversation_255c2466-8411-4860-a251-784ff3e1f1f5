import React, { useContext } from "react";
import { Game } from "utils/game";
import { EnumAssetType, EnumBannerOnDevices, EnumUrlType } from "models/enum/sanity";
import { isDesktop, isMobile } from "react-device-detect";
import { IBannerExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import ClientUsersService from "client/services/clientUsersService";
import { EnumUserClientAction } from "models/enum/user";
import AppLink from "@components/main/appLink";
import AppImg from "@components/main/appImg";
import BannerVideo from "@components/banners/bannerVideo";
import { Project } from "utils/project";
import { EnumPage } from "models/enum/page";
import { MainAppContext } from "@pages/_app";

const Banner = ({ className, banner }: { className: string; banner: IBannerExtended }) => {
    const { appStore } = useContext(MainAppContext);

    if (!banner || banner.bannerType !== "pa" || !banner.isActive) {
        return null;
    }

    let gameUrl = "";
    if (banner.urlType === EnumUrlType.game && banner.game?.isActive === true) {
        const gameCode = Game.getFirstGameCode(banner.game);
        if (gameCode) {
            gameUrl = Project.getPageUrl(appStore, EnumPage.gameDetails,
                { gameId: String(banner.game.gameId?.current) });
        }
    } else if (banner.urlType === EnumUrlType.external && banner.url?.url?.length) {
        gameUrl = banner.url?.url.trim();
    }

    const smallImageUrl =
        (banner.banner600x140?.assetType === EnumAssetType.image && banner.banner600x140.assetImageUrl) || null;
    const smallVideoUrl =
        (banner.banner600x140?.assetType === EnumAssetType.video && banner.banner600x140.assetVideoUrl) || null;

    const largeImageUrl =
        (banner.banner1920x120?.assetType === EnumAssetType.image && banner.banner1920x120.assetImageUrl) || null;
    const largeVideoUrl =
        (banner.banner1920x120?.assetType === EnumAssetType.video && banner.banner1920x120.assetVideoUrl) || null;

    const desktopType =
        (banner.bannerOnDesktop === EnumBannerOnDevices.banner600x140 && banner.banner600x140?.assetType) ||
        (banner.bannerOnDesktop === EnumBannerOnDevices.banner1920x120 && banner.banner1920x120?.assetType) ||
        EnumBannerOnDevices.nothing;

    const mobLandType =
        (banner.bannerOnMobileLandscape === EnumBannerOnDevices.banner600x140 && banner.banner600x140?.assetType) ||
        (banner.bannerOnMobileLandscape === EnumBannerOnDevices.banner1920x120 && banner.banner1920x120?.assetType) ||
        EnumBannerOnDevices.nothing;

    const mobPortType =
        (banner.bannerOnMobilePortrait === EnumBannerOnDevices.banner600x140 && banner.banner600x140?.assetType) ||
        (banner.bannerOnMobilePortrait === EnumBannerOnDevices.banner1920x120 && banner.banner1920x120?.assetType) ||
        EnumBannerOnDevices.nothing;

    const isVideo = [desktopType, mobLandType, mobPortType].includes(EnumAssetType.video);
    const isImage = [desktopType, mobLandType, mobPortType].includes(EnumAssetType.image);

    const bannerVideo = {
        controls: false,
        autoPlay: true,
        loop: true,
        muted: true
    };

    const getVideoUrl = (bannerType: EnumBannerOnDevices): string => {
        return String((bannerType === EnumBannerOnDevices.banner600x140 && smallVideoUrl) || largeVideoUrl);
    };

    const getImageUrl = (bannerType: EnumBannerOnDevices): string => {
        return String((bannerType === EnumBannerOnDevices.banner600x140 && smallImageUrl) || largeImageUrl);
    };

    return (
        <div className={"banner-slide " + className}>
            <AppLink
                href={gameUrl}
                target={banner.url?.urlTarget || "_self"}
                onClick={async () => {
                    await ClientUsersService.clientAction(
                        EnumUserClientAction.bannerTop,
                        location.pathname,
                        {
                            gameUrl,
                            isVideo,
                            isImage,
                            isDesktop,
                            isMobile
                        },
                        "banner"
                    );
                }}
            >
                {isVideo && (smallVideoUrl || largeVideoUrl) && (
                    <BannerVideo
                        {...(desktopType === EnumAssetType.video && {
                            desktop: {
                                ...bannerVideo,
                                src: getVideoUrl(banner.bannerOnDesktop as EnumBannerOnDevices)
                            }
                        })}
                        {...(mobLandType === EnumAssetType.video && {
                            mobileLandscape: {
                                ...bannerVideo,
                                src: getVideoUrl(banner.bannerOnMobileLandscape as EnumBannerOnDevices)
                            }
                        })}
                        {...(mobPortType === EnumAssetType.video && {
                            mobilePortrait: {
                                ...bannerVideo,
                                src: getVideoUrl(banner.bannerOnMobilePortrait as EnumBannerOnDevices)
                            }
                        })}
                    />
                )}
                {isImage &&
                    (smallImageUrl || largeImageUrl) &&
                    ((isDesktop && (
                            <AppImg
                                src={getImageUrl(banner.bannerOnDesktop as EnumBannerOnDevices)}
                                className={"banner-image"}
                            />
                        )) ||
                        (isMobile && (
                            <div className={"banner-mobile"}>
                                <AppImg
                                    src={getImageUrl(banner.bannerOnMobileLandscape as EnumBannerOnDevices)}
                                    className={"banner-landscape-image"}
                                />
                                <AppImg
                                    src={getImageUrl(banner.bannerOnMobilePortrait as EnumBannerOnDevices)}
                                    className={"banner-portrait-image"}
                                />
                            </div>
                        )))}
            </AppLink>
        </div>
    );
};

export default Banner;
