import React from "react";
import { IBannersCarouselExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import Banners from "@components/banners/banners";

const BannersCarousel = ({
    className,
    bannersCarousel,
}: {
    className: string;
    bannersCarousel: IBannersCarouselExtended;
}) => {
    if (!bannersCarousel || bannersCarousel.isActive !== true) {
        return null;
    }

    const autoPlay = Number(bannersCarousel.autoPlay);

    return (
        <Banners
            className={className}
            banners={bannersCarousel.banners}
            autoPlay={autoPlay > 0 ? autoPlay : 0}
            loop={bannersCarousel.isLoop === true}
        />
    );
};

export default BannersCarousel;
