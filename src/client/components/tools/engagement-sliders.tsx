import React from "react";
import { observer } from "mobx-react-lite";
import { EnumLanguageCode } from "models/enum/system";

import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/scrollbar";

import { Swiper, SwiperSlide } from "swiper/react";
import { Keyboard, Mousewheel, Navigation, Pagination, Scrollbar } from "swiper";
import { SanityApi } from "utils/sanityApi";
import { ILocaleString } from "api/data-providers/sanity/schemas";
import { IEngagementSlideExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import AppImg from "@components/main/appImg";

const EngagementSliders = observer((props: { sliders: IEngagementSlideExtended[]; languageCode: EnumLanguageCode }) => {
    const { sliders, languageCode } = props;
    // const { authStore, appStore } = useContext(AppContext);

    // const swiperRef = React.useRef(null);

    // const router = useRouter();
    // const locale = router.locale;
    // const msg = getLocaleMessages(locale as EnumLocales).messages;

    const isFeatures = sliders && Array.isArray(sliders);
    const isFeaturesLength = isFeatures ? sliders.length : 0;

    if (isFeaturesLength > 0) {
        return (
            <Swiper
                modules={[Navigation, Pagination, Mousewheel, Keyboard, Scrollbar]}
                slidesPerView={"auto"}
                spaceBetween={0}
                grabCursor={true}
                // https://github.com/nolimits4web/swiper/issues/3855
                // ref={swiperRef}
                // mousewheel={true}
                keyboard={true}
                // watchSlidesProgress={true}
                // watchSlidesVisibility={true}
                // scrollbar={true}
                loop={false}
                navigation={true}
                className={"engagement-slider" + (isFeaturesLength === 1 ? "-1" : "")}
            >
                {sliders.map((slider) => {
                    const header1 = SanityApi.getLocale(slider.header1 as ILocaleString, languageCode);
                    const text1 = SanityApi.getLocale(slider.text1 as ILocaleString, languageCode);
                    const header2 = SanityApi.getLocale(slider.header2 as ILocaleString, languageCode);
                    const text2 = SanityApi.getLocale(slider.text2 as ILocaleString, languageCode);
                    return (
                        <SwiperSlide className={"engagement-slide"} key={slider._key}>
                            <div className="engagement-slider-figure">
                                <AppImg
                                    src={slider.engagementSliderImageUrl}
                                    title={header1}
                                    alt={header1}
                                    className="engagement-slider-figure-img"
                                />
                            </div>
                            <div className="col-lg-12">
                                <div className="tools-slider-item">
                                    <div className="tools-slider-item__name">{header1}</div>
                                    <div className="tools-slider-item__text">{text1}</div>
                                    <div className="tools-slider-item__subname">{header2}</div>
                                    <div className="tools-slider-item__subtext">{text2}</div>
                                </div>
                            </div>
                        </SwiperSlide>
                    );
                })}
            </Swiper>
        );
    } else {
        return null;
    }
});

export default EngagementSliders;
