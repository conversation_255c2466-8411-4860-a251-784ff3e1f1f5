import React from "react";
import { observer } from "mobx-react-lite";
import { ILocaleString } from "api/data-providers/sanity/schemas";
import { EnumLanguageCode } from "models/enum/system";
import { SanityApi } from "utils/sanityApi";
import { IJackpotInfoExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";

const JackpotFeatures = observer((props: { features: IJackpotInfoExtended[]; languageCode: EnumLanguageCode }) => {
    const { features, languageCode } = props;
    // const { authStore, appStore } = useContext(AppContext);

    // const swiperRef = React.useRef(null);

    // const router = useRouter();
    // const locale = router.locale;
    // const msg = getLocaleMessages(locale as EnumLocales).messages;

    const isFeatures = features && Array.isArray(features);
    const isFeaturesLength = isFeatures ? features.length : 0;

    if (isFeaturesLength > 0) {
        return (
            <section id="jackpot-features" className="jackpot-features">
                <div className="container-fluid">
                    <div className="row">
                        <div className="col text-center">
                            <h2 className="title title--h2 h2a" data-text="features">
                                jackpot <span className="accent">features</span>
                            </h2>
                        </div>
                    </div>
                    <div className="wrap">
                        <div className="jackpot-features__inner">
                            <div className="row d-flex justify-content-center">
                                {features.map((feature) => {
                                    const header = SanityApi.getLocale(feature.header as ILocaleString, languageCode);
                                    const description = SanityApi.getLocale(
                                        feature.description as ILocaleString,
                                        languageCode
                                    );

                                    return (
                                        <div className="col-lg-6 col-md-6" key={feature._key}>
                                            <div className="jp-feature">
                                                <div
                                                    className="jp-feature__img"
                                                    style={{
                                                        backgroundImage: "url('" + feature.jackpotsInfoIconUrl + "')",
                                                    }}
                                                />
                                                <div className="jp-feature__name">
                                                    <span className="accent">
                                                        {header.substr(0, header.indexOf(" "))}
                                                    </span>
                                                    {header.substr(header.indexOf(" "))}
                                                </div>
                                                <div className="jp-feature__text">{description}</div>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        );
    } else {
        return null;
    }
});

export default JackpotFeatures;
