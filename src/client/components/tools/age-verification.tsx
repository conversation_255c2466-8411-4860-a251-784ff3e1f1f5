import React, { useEffect, useRef, useState } from "react";
import { DropdownButton } from "react-bootstrap";
import DropdownItem from "react-bootstrap/DropdownItem";
import { getCookie, removeCookies, setCookies } from "cookies-next";
import { observer } from "mobx-react-lite";

const AgeVerification = observer(() => {
    const [showArea, setShowArea] = useState<boolean>(false);

    const cookieName = "sw-age-verification";
    const cookieAgeExp = 31;

    const strictModeRef = useRef<boolean>(false);

    useEffect(() => {
        if (!strictModeRef.current) {
            strictModeRef.current = true;
            if (parseInt(String(getCookie(cookieName)), 10) !== cookieAgeExp) {
                removeCookies(cookieName);
                setShowArea(true);
            }
        }
    }, []);

    useEffect(() => {
        if(showArea) {
            document.body.classList.add("no-scroll");
            document.documentElement.classList.add("no-scroll");
        } else {
            document.body.classList.remove("no-scroll");
            document.documentElement.classList.remove("no-scroll");
        }
    }, [showArea]);

    const onLeaveClick = () : void => {
        window.location.href = "https://www.google.com";
    }

    const onEnterClick = (): void => {
        setShowArea(false);
        setCookies(cookieName, cookieAgeExp, { maxAge: cookieAgeExp * 24 * 3600, path: "/" });
    };

    if (!showArea) {
        return null;
    }

    return (
        <div className="responsible-overlay">
            <div className="responsible-overlay__content">
                <h2 className="responsible-overlay__title">You must be over 18 years old to visit this site</h2>

                <div className="responsible-list__container">
                    <button
                        className="responsible-list__button"
                        onClick={onLeaveClick}
                    >No, I am under 18 - LEAVE
                    </button>
                    <button
                        className="responsible-list__button enter"
                        onClick={onEnterClick}
                    >Yes, I am over 18 - ENTER
                    </button>
                </div>
            </div>
        </div>
    );
});

export default AgeVerification;