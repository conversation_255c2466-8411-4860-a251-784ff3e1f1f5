import React, { useContext, useEffect, useRef, useState } from "react";
import { Button } from "react-bootstrap";
import { getCookie, removeCookies, setCookies } from "cookies-next";
import { observer } from "mobx-react-lite";
import { EnumPage } from "models/enum/page";
import { MainAppContext } from "@pages/_app";
import { Project } from "utils/project";

const CookieDisclaimer = observer(() => {
    const { appStore } = useContext(MainAppContext);
    const [showArea, setShowArea] = useState<boolean>(false);

    const cookieDisName = "sw-cookie-disclaimer";
    const cookieExp = 720;

    const strictModeRef = useRef<boolean>(false);

    const cookiesPageUrl = Project.getPageUrl(appStore, EnumPage.cookies);

    useEffect(() => {
        if (!strictModeRef.current) {
            strictModeRef.current = true;

            if (parseInt(String(getCookie(cookieDisName)), 10) === cookieExp) {
                setShowArea(false);
            } else {
                removeCookies(cookieDisName);
                setShowArea(true);
            }
        }
    }, []);

    const onAcceptCookies = (e: React.MouseEvent<HTMLButtonElement>) => {
        e.preventDefault();
        setCookies(cookieDisName, cookieExp, { maxAge: cookieExp * 24 * 3600, path: "/" });
        setShowArea(false);
    };

    if (!showArea) {
        return null;
    }

    return (
        <div className="sw-cookie">
            <div className="wrap">
                <div className="container-fluid">
                    <div className="row">
                        <div className="col">
                            <div className="sw-cookie-disclaimer">
                                <div className="sw-cookie-disclaimer__text">We use cookies to improve your experience on
                                    our website. By browsing this website, you agree to our use of cookies.
                                </div>
                                <div className="sw-cookie-disclaimer__btns">
                                    <Button
                                        className={"button button--primary accept-cookie"}
                                        onClick={onAcceptCookies}>Accept</Button>
                                    <Button
                                        className={"button button--secondary"}
                                        href={cookiesPageUrl} target={"_blank"}>EXPLORE MORE</Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
});

export default CookieDisclaimer;