import React, { useContext } from "react";
import AppLink from "@components/main/appLink";
import { observer } from "mobx-react-lite";
import { EnumPage } from "models/enum/page";
import { MainAppContext } from "@pages/_app";
import { Project } from "utils/project";
import { EnumProjectType } from "models/enum/system";
import appConfig from "appConfig";

const FooterPolicy = observer(() => {
    const { appStore } = useContext(MainAppContext);

    const privacyPageUrl = (appStore.getAppProject().type === EnumProjectType.pa)
                             ? appConfig.client.endpoints.external.skywind.policy
                             :  Project.getPageUrl(appStore, EnumPage.privacy);

    const cookiesPageUrl = (appStore.getAppProject().type === EnumProjectType.pa)
                           ? appConfig.client.endpoints.external.skywind.cookies
                           :  Project.getPageUrl(appStore, EnumPage.cookies);

    return (
        <div className="footer__contact">
            <div className="footer__title">Our Policy</div>
            <ul>
                <li key={privacyPageUrl+"1"}>
                    <AppLink
                        href={privacyPageUrl}
                        target={"_blank"}
                        title={"Privacy Policy"}
                        rel="noreferrer"
                    >
                        Privacy Policy
                    </AppLink>
                </li>
                <li key={cookiesPageUrl+"2"}>
                    <AppLink
                        href={cookiesPageUrl}
                        target={"_blank"}
                        title={"Cookie Policy"}
                        rel="noreferrer"
                    >
                        Cookie Policy
                    </AppLink>
                </li>
            </ul>
        </div>
    );
});

export default FooterPolicy;
