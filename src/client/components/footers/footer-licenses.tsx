import React from "react";
import { SanityApi } from "utils/sanityApi";
import { ILocaleString } from "api/data-providers/sanity/schemas";
import AppImg from "@components/main/appImg";
import AppLink from "@components/main/appLink";
import { IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { EnumLanguageCode } from "models/enum/system";
import { observer } from "mobx-react-lite";

export interface ICmpFooterLicensesProps {
    footerData: IWebsiteFooter;
    languageCode: EnumLanguageCode;
}

const FooterLicenses = observer(({ footerData, languageCode }: ICmpFooterLicensesProps) => {
    return (
        <div className="footer__licenses">
            <div className="footer__title" />
            <div className="flicenses">
                {(Array.isArray(footerData?.footerLicenses) && footerData?.footerLicenses.length > 0
                  ? footerData.footerLicenses
                  : []
                ).map((license) => {
                    const regulatorName = SanityApi.getLocale(license.regulator as ILocaleString, languageCode);

                    const logo = (
                        <div className={"flicenses__item"} key={"l-img-" + license._id}>
                            <AppImg
                                key={"img-" + license._id}
                                src={license.licensePhotoUrl}
                                title={regulatorName}
                                alt={regulatorName}
                            />
                        </div>
                    );

                    return license?.licenseUrl ? (
                        <AppLink
                            href={license?.licenseUrl || "#"}
                            title={regulatorName}
                            key={"a-" + license._id}
                            target={"_blank"}
                            rel="noreferrer"
                        >
                            {logo}
                        </AppLink>
                    ) : (
                               logo
                           );
                })}
            </div>
        </div>
    );
});

export default FooterLicenses;
