import React from "react";
import { FaChevronDown } from "@react-icons/all-files/fa/FaChevronDown";
import { FaChevronUp } from "@react-icons/all-files/fa/FaChevronUp";
import { observer } from "mobx-react-lite";

const FixedToolbar = observer(({ handlerOpenClose, state }: { handlerOpenClose: (state: boolean) => void; state: boolean }) => {
    return (
        <div className={"show_all"}>
            <div className={"f_title"}>{!state && <span>Company and contact details</span>}</div>
            <div
                className={"btn btn-light btn-lg f_show_btn"}
                role="button"
                onClick={() => {
                    handlerOpenClose(!state);
                }}
            >
                {state && <FaChevronDown />}
                {!state && <FaChevronUp />}
            </div>
        </div>
    );
});

export default FixedToolbar;
