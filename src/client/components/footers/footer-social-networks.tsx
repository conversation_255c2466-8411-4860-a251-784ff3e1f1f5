import React from "react";
import { Sanity<PERSON><PERSON> } from "utils/sanityApi";
import { ILocaleString } from "api/data-providers/sanity/schemas";
import AppLink from "@components/main/appLink";
import AppImg from "@components/main/appImg";
import { IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { EnumLanguageCode } from "models/enum/system";
import { observer } from "mobx-react-lite";

export interface ICmpFooterSocialNetworksProps {
    footerData: IWebsiteFooter;
    languageCode: EnumLanguageCode;
}

const FooterSocialNetworks = observer(({ footerData, languageCode}: ICmpFooterSocialNetworksProps) => {
    return (
        <div className="footer__connected">
            <div className="footer__title">
                Stay Connected
                <span className="accent"> </span>
            </div>
            <div>
                <ul>
                    {Array.isArray(footerData?.socialNetworks) &&
                        footerData?.socialNetworks.map((sn) => {
                            const name = SanityApi.getLocale(sn.snName as ILocaleString, languageCode);
                            return (
                                <li key={sn._id}>
                                    <AppLink
                                        href={String(sn?.snUrl)}
                                        key={"a-" + sn.code?.current}
                                        target={"_blank"}
                                        rel="noreferrer external"
                                    >
                                        {sn.iconUrl && (
                                            <AppImg src={sn.iconUrl} width={"32"} height={"32"} alt={name} />
                                        )}
                                        {name}
                                    </AppLink>
                                </li>
                            );
                        })}
                </ul>
            </div>
        </div>
    );
});

export default FooterSocialNetworks;
