import React, { useContext, useMemo } from "react";
import AppLink from "@components/main/appLink";
import appConfig from "appConfig";
import { IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { observer } from "mobx-react-lite";
import { EnumPage } from "models/enum/page";
import { MainAppContext } from "@pages/_app";
import { Project } from "utils/project";
import { EnumProjectType } from "models/enum/system";

const configConstant = appConfig.constant;
// force build 1
const FooterContact = observer(({ footerData }: { footerData: IWebsiteFooter }) => {
    const { appStore } = useContext(MainAppContext);

    const salesEmail = useMemo(() => Array.isArray(footerData?.emails) &&
            footerData?.emails.find((e) => e.code?.current === configConstant.salesEmail),
        [footerData?.emails]
    );
    const contactPhone = useMemo(() =>
            Array.isArray(footerData?.phones) &&
            footerData?.phones.find((p) => p.code?.current === configConstant.contactPhone),
        [footerData?.phones]
    );

        const contactUsPageUrl = (appStore.getAppProject().type === EnumProjectType.pa)
                                 ? appConfig.client.endpoints.external.skywind.contactUs
                                 :  Project.getPageUrl(appStore, EnumPage.contact);

    return (
        <div className="footer__contact">
            <div className="footer__title">Contact</div>
            <div>
                <ul>
                    <li key={"s1"}>Madison Buildings</li>
                    <li key={"s2"}>Midtown, Queensway</li>
                    <li key={"s3"}>GX11 1AA</li>
                    <li key={"s4"}>Gibraltar</li>
                    {/*<li key={"s5"}>*/}
                    {/*    <AppLink key={"skype"} href="skype:skywindsales">*/}
                    {/*        skype: skywindsales*/}
                    {/*    </AppLink>*/}
                    {/*</li>*/}
                    {salesEmail && (
                        <li key={"s6"}>
                            {/*<AppLink key={"email"} href={"mailto:" + (salesEmail?.emailAddress || "")}>*/}
                            {/*    {salesEmail.emailAddress}*/}
                            {/*</AppLink>*/}
                            <AppLink
                                href={contactUsPageUrl}
                                title={"Contact us"}
                                rel="noreferrer"
                            >
                                Contact us
                            </AppLink>
                        </li>
                    )}
                    {contactPhone && (
                        <li key={"s7"}>
                            <AppLink key={"tel"} href={"tel:" + (contactPhone?.phoneNumber || "")}>
                                {contactPhone.phoneNumber}
                            </AppLink>
                        </li>
                    )}
                </ul>
            </div>
        </div>
    );
});

export default FooterContact;
