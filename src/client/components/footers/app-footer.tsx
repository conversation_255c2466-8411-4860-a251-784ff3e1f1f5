import React, { useState } from "react";
import { IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { EnumLanguageCode } from "models/enum/system";
import AppLink from "@components/main/appLink";
import appConfig from "appConfig";
import FixedToolbar from "client/components/footers/fixed-toolbar";
import FooterPolicy from "client/components/footers/footer-policy";
import FooterContact from "client/components/footers/footer-contact";
import FooterLicenses from "client/components/footers/footer-licenses";
import FooterSocialNetworks from "client/components/footers/footer-social-networks";
import { observer } from "mobx-react-lite";

export interface IAppFooterProps {
    footerData: IWebsiteFooter;
    languageCode: EnumLanguageCode;
    fixed: boolean;
}

const AppFooter = observer(({ fixed, footerData, languageCode }: IAppFooterProps) => {
    const [open, setOpen] = useState(false);

    const url = appConfig.client.endpoints.external;

    if (footerData) {
        return (
            <footer id="app-footer" className={"footer" + (fixed ? " window-fixed" : "") + (open ? " open" : "")}>
                {fixed && <FixedToolbar handlerOpenClose={setOpen} state={open} />}
                <div className={"wrap " + (open ? " open" : "")}>
                    <div className="container">
                        <div className="row">
                            <div className="col-lg-2 col-md-4 col-sm-8">
                                <FooterPolicy />
                            </div>
                            <div className="col-lg-2 col-md-6 col-sm-8">
                                <FooterContact footerData={footerData} />
                            </div>
                            <div className="col-lg-5 col-md-12">
                                <FooterLicenses footerData={footerData} languageCode={languageCode} />
                            </div>
                            <div className="col-lg-3 col-md-12">
                                <FooterSocialNetworks footerData={footerData} languageCode={languageCode} />
                            </div>
                        </div>

                        <div className="row sw-footer-licensed-limited" style={{ fontSize: "smaller" }}>
                            <div className="col-lg-12 col-md-4 col-sm-8" style={{ textAlign: "center" }}>
                                <div className="footer__copyright">© ALL RIGHTS RESERVED SKYWIND 2024</div>
                                <br />
                                <AppLink
                                    key={"a-gamblingcommission"}
                                    href={url.skywind.registrationUrl}
                                    target="_blank"
                                    rel="noreferrer"
                                >
                                    Skywind Holdings Limited is licensed and regulated in Great Britain by the Gambling Commission under account number 52261.<br/>Skywind Malta Limited (C 87023) having its registered address at Horeca Building, 3rd Floor, Triq L-Imgarr, Xewkija, Ghawdex, XWK 9012, Malta,<br/>is
                                    licensed and regulated by the Malta Gaming Authority having a B2B-Critical Supply License, Type 1 Gaming Services, with license number MGA/B2B/519/2018, issued on the 9th of January 2019.
                                </AppLink>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
        );
    }
    return null;
});

export default AppFooter;
