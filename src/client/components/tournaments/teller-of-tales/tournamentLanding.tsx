import { Tournaments } from "utils/tournamets";
import React, { useRef } from "react";
import AppImg from "@components/main/appImg";
import AppLink from "@components/main/appLink";
import FlipCountDown from "@components/flip-countdown/flip-count-down";
import { isMobile } from "react-device-detect";
import moment from "moment";
import AppVideo from "@components/main/appVideo";
import ClientUsersService from "client/services/clientUsersService";
import { EnumUserClientAction } from "models/enum/user";

const TournamentLanding = () => {
    const tournament = Tournaments.getTournamentsStorage().find((t) => t.code === "tellerOfTales");
    const playerRef = useRef<HTMLVideoElement>(null);
    if (!tournament) {
        return <></>;
    }

    return (
        <div className={"tournament-landing"}>
            <div className="sw-bg">
                <div className="container">
                    <div className="banner-figure-container">
                        <AppVideo
                            videoRef={playerRef}
                            videoUrl={"https://cdn.skywindgroup.com/video/games/teller-of-tales/hls/video.m3u8"}
                            posterUrl={"/landing/teller-of-tales/assets/images/poster.jpg"}
                            className={"banner-figure"}
                            autoPlay={true}
                            muted={true}
                            loop={true}
                        />
                    </div>
                    <div className="header">
                        <AppImg
                            className="header-img"
                            src="/landing/teller-of-tales/assets/images/bg-1.png"
                            alt=""
                            title=""
                        />
                        <div className="sw-logo-effect">
                            <AppImg
                                src="/landing/teller-of-tales/assets/images/header/logo-effect.png"
                                alt=""
                                title=""
                            />
                        </div>
                        <div className="sw-symbol s1">
                            <AppImg src="/landing/teller-of-tales/assets/images/header/s1.png" alt="" title="" />
                        </div>
                        <div className="sw-symbol s2">
                            <AppImg src="/landing/teller-of-tales/assets/images/header/s2.png" alt="" title="" />
                        </div>
                        <div className="sw-logo three-d-image">
                            <AppImg
                                src="/landing/teller-of-tales/assets/images/header/logo.png"
                                alt=""
                                title=""
                                className="main-img"
                            />
                            <AppImg
                                src="/landing/teller-of-tales/assets/images/header/logo.png"
                                alt=""
                                title=""
                                className="left-img"
                            />
                            <AppImg
                                src="/landing/teller-of-tales/assets/images/header/logo.png"
                                alt=""
                                title=""
                                className="right-img"
                            />
                        </div>
                    </div>
                    <div className="section">
                        <p className="sw-pa-with-le">
                            Partners Tournament
                            <br /> with Lucky Envelopes
                        </p>
                        <div className="sw-date">
                            <AppImg src="/landing/teller-of-tales/assets/images/body/date.png" alt="" title="" />
                        </div>
                        <div className="sw-ticker">
                            <div className="sw-symbol s3">
                                <AppImg src="/landing/teller-of-tales/assets/images/header/s3.png" alt="" title="" />
                            </div>
                            <div className="sw-ticker-dots">
                                <AppImg
                                    src="/landing/teller-of-tales/assets/images/body/ticker-dots.png"
                                    alt=""
                                    title=""
                                />
                            </div>
                            {(Tournaments.isTournamentTimerStartEnabled(tournament) ||
                                Tournaments.isTournamentTimerEndEnabled(tournament)) && (
                                <div className="sw-ticker-title">
                                    {Tournaments.isTournamentTimerStartEnabled(tournament) ? "Starts in" : "Ends in"}:
                                </div>
                            )}
                            {Tournaments.isTournamentTimerStartEnabled(tournament) && (
                                <FlipCountDown
                                    hideYear
                                    hideMonth
                                    endAtZero
                                    titlePosition="bottom"
                                    theme="dark"
                                    dayTitle="Days"
                                    hourTitle="Hrs"
                                    minuteTitle="Mins"
                                    secondTitle="Secs"
                                    {...((isMobile && { size: "small" }) || { size: "medium" })}
                                    endAt={moment(tournament.timerStartDate).local(true).format("YYYY-MM-DD HH:mm:ss")} // Date/Time
                                />
                            )}
                            {Tournaments.isTournamentTimerEndEnabled(tournament) && (
                                <FlipCountDown
                                    hideYear
                                    hideMonth
                                    endAtZero
                                    titlePosition="bottom"
                                    theme="dark"
                                    dayTitle="Days"
                                    hourTitle="Hrs"
                                    minuteTitle="Mins"
                                    secondTitle="Secs"
                                    {...((isMobile && { size: "small" }) || { size: "medium" })}
                                    endAt={moment(tournament.playEndDate).local(true).format("YYYY-MM-DD HH:mm:ss")} // Date/Time
                                />
                            )}
                        </div>
                        {/*{Tournaments.isTournamentPlayEnabled(tournament) && (*/}
                        {/*    <AppLink*/}
                        {/*        className="sw-black-btn"*/}
                        {/*        href={"/play/player/" + tournament.gameCode}*/}
                        {/*        target={"_blank"}*/}
                        {/*        rel="noreferrer"*/}
                        {/*        onClick={async () => {*/}
                        {/*            await ClientUsersService.clientAction(*/}
                        {/*                EnumUserClientAction.playTournament,*/}
                        {/*                location.pathname,*/}
                        {/*                {*/}
                        {/*                    code: tournament.code,*/}
                        {/*                    gameCode: tournament.gameCode,*/}
                        {/*                    isActive: tournament.isActive,*/}
                        {/*                    button: "button-take-part",*/}
                        {/*                },*/}
                        {/*                tournament.code + "Landing"*/}
                        {/*            );*/}
                        {/*        }}*/}
                        {/*    >*/}
                        {/*        Take Part to Win Prizes*/}
                        {/*    </AppLink>*/}
                        {/*)}*/}
                        <div className="section-yellow">
                            <AppImg
                                src="/landing/teller-of-tales/assets/images/body/yellow-border-top.png"
                                alt=""
                                title=""
                            />
                            <div className="sw-symbol s4">
                                <AppImg src="/landing/teller-of-tales/assets/images/header/s4.png" alt="" title="" />
                            </div>
                            <div className="sw-symbol s5">
                                <AppImg src="/landing/teller-of-tales/assets/images/header/s5.png" alt="" title="" />
                            </div>
                            <div className="sw-symbol s6">
                                <AppImg src="/landing/teller-of-tales/assets/images/header/s6.png" alt="" title="" />
                            </div>
                            <div className="sw-symbol s7">
                                <AppImg src="/landing/teller-of-tales/assets/images/header/s6.png" alt="" title="" />
                            </div>
                            <div className="section-yellow-body">
                                {Tournaments.isTournamentPlayEnabled(tournament) && (
                                    <AppLink
                                        className="sw-green-btn"
                                        href={"/play/player/" + tournament.gameCode}
                                        target={"_blank"}
                                        rel="noreferrer"
                                        onClick={async () => {
                                            await ClientUsersService.clientAction(
                                                EnumUserClientAction.playTournament,
                                                location.pathname,
                                                {
                                                    code: tournament.code,
                                                    gameCode: tournament.gameCode,
                                                    isActive: tournament.isActive,
                                                    button: "button-play",
                                                },
                                                tournament.code + "Landing"
                                            );
                                        }}
                                    >
                                        PLAY TELLER OF TALES
                                    </AppLink>
                                )}
                                <p>
                                    You’re invited to try out Skywind’s newest title, <strong>Teller of Tales</strong>.
                                </p>
                                <p>
                                    A magical 4x6 slot with dynamic random modifiers,
                                    <br />
                                    Teller of Tales is set to be released <strong>April 20, 2022</strong>. With a unique
                                    chapter-based
                                    <br />
                                    Free Game feature, will the story you hear be about hidden treasure?
                                </p>
                                <div className="sw-green-line">EXCLUSIVE TOURNAMENT WITH LUCKY ENVELOPES</div>
                                <p>
                                    Compete to prove yourself the sort they tell tales about in an exclusive Teller of
                                    Tales
                                    <br />
                                    tournament for Partners. <strong>Between April 4 and 8</strong>, climb the
                                    leaderboard to win some
                                    <br />
                                    great prizes. With a limited budget, you’ll need to make every bet count! Who knows
                                    <br />
                                    what your story holds?
                                </p>
                                <AppImg
                                    className="sw-prizes"
                                    src="/landing/teller-of-tales/assets/images/body/prizes.png"
                                    alt=""
                                    title=""
                                />
                                <p>
                                    If luck is on your side, you might even find an envelope waiting for you
                                    <br />– a <strong>Lucky Envelope</strong>, one of Skywind’s latest engagement tools.
                                    <br />
                                    Each day of the tournament, two lucky participants
                                    <br />
                                    will find an envelope worth €50.
                                </p>
                            </div>
                            <AppImg
                                src="/landing/teller-of-tales/assets/images/body/yellow-border-btm.png"
                                alt=""
                                title=""
                            />
                        </div>
                        {Tournaments.isTournamentPlayEnabled(tournament) && (
                            <AppLink
                                className="sw-black-btn"
                                href={"/play/player/" + tournament.gameCode}
                                target={"_blank"}
                                rel="noreferrer"
                                onClick={async () => {
                                    await ClientUsersService.clientAction(
                                        EnumUserClientAction.playTournament,
                                        location.pathname,
                                        {
                                            code: tournament.code,
                                            gameCode: tournament.gameCode,
                                            isActive: tournament.isActive,
                                            button: "button-enter-tournament",
                                        },
                                        tournament.code + "Landing"
                                    );
                                }}
                            >
                                Enter partner tournament
                            </AppLink>
                        )}
                        <div className="sw-good-luck">Good luck</div>
                        <div className="sw-list">
                            <AppLink
                                className="sw-list-item"
                                href="/landing/teller-of-tales/assets/Teller-of-Tales-Tourney-Ts-Cs.docx"
                                target="_blank"
                                onClick={async () => {
                                    await ClientUsersService.clientAction(
                                        EnumUserClientAction.rulesTournament,
                                        location.pathname,
                                        {
                                            code: tournament.code,
                                            gameCode: tournament.gameCode,
                                            isActive: tournament.isActive,
                                            link: "tournament-rules",
                                        },
                                        tournament.gameId
                                    );
                                }}
                            >
                                Read Partner Tournament Rules
                            </AppLink>
                            <AppLink
                                className="sw-list-item"
                                href="https://www.skywindgroup.com/en/tools/tournament.html"
                                target="_blank"
                            >
                                Learn more about Skywind Tournaments
                            </AppLink>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TournamentLanding;
