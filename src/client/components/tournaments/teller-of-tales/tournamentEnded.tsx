import { Tournaments } from "utils/tournamets";
import React, { useContext, useRef } from "react";
import AppImg from "@components/main/appImg";
import AppLink from "@components/main/appLink";
import AppVideo from "@components/main/appVideo";
import { Project } from "utils/project";
import ClientUsersService from "client/services/clientUsersService";
import { EnumUserClientAction } from "models/enum/user";
import { observer } from "mobx-react-lite";
import { MainAppContext } from "@pages/_app";
import { EnumPage } from "models/enum/page";

const TournamentLanding = observer(() => {
    const tournament = Tournaments.getTournamentsStorage().find((t) => t.code === "tellerOfTales");
    const playerRef = useRef<HTMLVideoElement>(null);
    const { appStore } = useContext(MainAppContext);

    if (!tournament) {
        return null;
    }

    return (
        <div className={"tournament-landing"}>
            <div className="sw-bg">
                <div className="container">
                    <div className="banner-figure-container">
                        <AppVideo
                            videoRef={playerRef}
                            videoUrl={"https://cdn.skywindgroup.com/video/games/teller-of-tales/hls/video.m3u8"}
                            posterUrl={"/landing/teller-of-tales/assets/images/poster.jpg"}
                            className={"banner-figure"}
                            autoPlay={true}
                            muted={true}
                            loop={true}
                        />
                    </div>
                    <div className="header">
                        <AppImg
                            className="header-img"
                            src="/landing/teller-of-tales/assets/images/bg-1.png"
                            alt=""
                            title=""
                        />
                        <div className="sw-logo-effect">
                            <AppImg
                                src="/landing/teller-of-tales/assets/images/header/logo-effect.png"
                                alt=""
                                title=""
                            />
                        </div>
                        <div className="sw-symbol s1">
                            <AppImg src="/landing/teller-of-tales/assets/images/header/s1.png" alt="" title="" />
                        </div>
                        <div className="sw-symbol s2">
                            <AppImg src="/landing/teller-of-tales/assets/images/header/s2.png" alt="" title="" />
                        </div>
                        <div className="sw-logo three-d-image">
                            <AppImg
                                src="/landing/teller-of-tales/assets/images/header/logo.png"
                                alt=""
                                title=""
                                className="main-img"
                            />
                            <AppImg
                                src="/landing/teller-of-tales/assets/images/header/logo.png"
                                alt=""
                                title=""
                                className="left-img"
                            />
                            <AppImg
                                src="/landing/teller-of-tales/assets/images/header/logo.png"
                                alt=""
                                title=""
                                className="right-img"
                            />
                        </div>
                    </div>
                    <div className="section">
                        <p className="sw-pa-with-le">
                            Partners Tournament
                            <br /> with Lucky Envelopes
                        </p>
                        <div className="sw-date mb-0">
                            <AppImg src="/landing/teller-of-tales/assets/images/body/date.png" alt="" title="" />
                        </div>
                        <div className="section-yellow">
                            <AppImg
                                src="/landing/teller-of-tales/assets/images/body/yellow-border-top.png"
                                alt=""
                                title=""
                            />
                            <div className="sw-symbol s12">
                                <AppImg src="/landing/teller-of-tales/assets/images/header/s5.png" alt="" title="" />
                            </div>
                            <div className="sw-symbol s10">
                                <AppImg src="/landing/teller-of-tales/assets/images/header/s10.png" alt="" title="" />
                            </div>
                            <div className="sw-symbol s11">
                                <AppImg src="/landing/teller-of-tales/assets/images/header/s10.png" alt="" title="" />
                            </div>
                            <div className="sw-symbol s8">
                                <AppImg src="/landing/teller-of-tales/assets/images/header/s3.png" alt="" title="" />
                            </div>
                            <div className="sw-symbol s9">
                                <AppImg src="/landing/teller-of-tales/assets/images/header/s9.png" alt="" title="" />
                            </div>
                            <div className="section-yellow-body">
                                <div className="sw-purple-btn">
                                    <a
                                        href={"/play/player/" + tournament.gameCode}
                                        target={"_blank"}
                                        rel="noreferrer"
                                        style={{ color: "#fff" }}
                                        onClick={async () => {
                                            await ClientUsersService.clientAction(
                                                EnumUserClientAction.playTournament,
                                                location.pathname,
                                                {
                                                    code: tournament.code,
                                                    gameCode: tournament.gameCode,
                                                    isActive: tournament.isActive,
                                                    button: "button-tournament-ended"
                                                },
                                                tournament.code + "Ended"
                                            );
                                        }}
                                    >
                                        TOURNAMENT ENDED
                                    </a>
                                </div>
                                <p className="text-sm">Thank you all for taking part in the tournament.</p>
                                <p className="text-lg">
                                    Congratulations to all our winners, and to the fortunate
                                    <br />
                                    who received <strong>Lucky Envelopes</strong>.
                                </p>
                                <AppImg
                                    className="sw-prizes lg"
                                    src="/landing/teller-of-tales/assets/images/body/prizes.png"
                                    alt=""
                                    title=""
                                />
                                <p className="text-sm">
                                    Look forward to revisiting Teller of Tales when it releases on April 20, 2022.
                                </p>
                                <div className="section-yellow-divider" />
                                <AppLink
                                    className="sw-green-btn lg"
                                    target="_blank"
                                    href="https://forms.gle/4LkdJxVjSfTucubdA"
                                    onClick={async () => {
                                        await ClientUsersService.clientAction(
                                            EnumUserClientAction.tournamentSurvey,
                                            location.pathname,
                                            {
                                                code: tournament.code,
                                                gameCode: tournament.gameCode,
                                                isActive: tournament.isActive,
                                                button: "button-tell-us"
                                            },
                                            tournament.code + "Ended"
                                        );
                                    }}
                                >
                                    Tell us what you thought
                                </AppLink>
                                <p className="text-sm">
                                    Skywind values your opinion, so we would appreciate it if you
                                    <br />
                                    could complete a short post-tournament survey
                                    <br />
                                    about your experience during the event.
                                </p>
                            </div>
                            <AppImg
                                src="/landing/teller-of-tales/assets/images/body/yellow-border-btm.png"
                                alt=""
                                title=""
                            />
                        </div>
                        <AppLink className="sw-purple-btn sm"
                                 href={Project.getPageUrl(appStore, EnumPage.roadmapGames)}>
                            SEE YOU ALL AT THE NEXT TOURNAMENT
                        </AppLink>
                        <div className="sw-list simple">
                            {/*<AppLink
                                className="sw-list-item"
                                href="/landing/teller-of-tales/assets/Teller-of-Tales-Tourney-Ts-Cs.docx"
                                target="_blank"
                                onClick={async () => {
                                    await ClientUsersService.clientAction(
                                        EnumUserClientAction.rulesTournament,
                                        location.pathname,
                                        {
                                            code: tournament.code,
                                            gameCode: tournament.gameCode,
                                            isActive: tournament.isActive,
                                            link: "tournament-rules",
                                        },
                                        tournament.gameId
                                    );
                                }}
                            >
                                Read Partner Tournament Rules
                            </AppLink>*/}
                            <AppLink
                                className="sw-list-item"
                                href="https://www.skywindgroup.com/en/tools/tournament.html"
                                target="_blank"
                            >
                                Learn more about Skywind Tournaments
                            </AppLink>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
});

export default TournamentLanding;
