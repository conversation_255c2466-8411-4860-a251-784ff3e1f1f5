import React, { useContext } from "react";
import AppImg from "@components/main/appImg";
import { Tournaments } from "utils/tournamets";
import ClientUsersService from "client/services/clientUsersService";
import { EnumUserClientAction } from "models/enum/user";
import appConfig from "appConfig";
import AppLink from "@components/main/appLink";
import { observer } from "mobx-react-lite";
import { Project } from "utils/project";
import { MainAppContext } from "@pages/_app";
import { EnumPage } from "models/enum/page";

const TournamentEnding = observer(() => {
    const { appStore } = useContext(MainAppContext);
    const tournament = Tournaments.getTournamentsStorage().find((t) => t.code === "nunchucksChicken");
    if (!tournament) {
        return <></>;
    }

    return (
        <div className={"tournament-landing"}>
            <header>
                <div className="container">
                    <div id="stars" />
                    <div id="stars2" />
                    <div id="stars3" />
                    <div className="background-img">
                        <AppImg src="/landing/nunchuckschicken/assets/images/header/bg.jpg" alt="" title="" />
                    </div>

                    <AppLink
                        href={tournament && Project.getPageUrl(appStore,
                            EnumPage.gameDetails,
                            { gameId: tournament.gameId })}
                        onClick={async () => {
                            await ClientUsersService.clientAction(
                                EnumUserClientAction.pageTournament,
                                location.pathname,
                                {
                                    code: tournament?.code,
                                    gameCode: tournament?.gameCode,
                                    isActive: appConfig.tournaments.isActive
                                },
                                "TournamentEnding"
                            );
                        }}
                    >
                        <div className="logo-img three-d-image">
                            <AppImg
                                src="/landing/nunchuckschicken/assets/images/header/header_logo.png"
                                alt=""
                                title=""
                                className="main-img"
                            />
                            <AppImg
                                src="/landing/nunchuckschicken/assets/images/header/header_logo.png"
                                alt=""
                                title=""
                                className="left-img"
                            />
                            <AppImg
                                src="/landing/nunchuckschicken/assets/images/header/header_logo.png"
                                alt=""
                                title=""
                                className="right-img"
                            />
                        </div>
                    </AppLink>

                    <div className="elements-1">
                        <AppImg src="/landing/nunchuckschicken/assets/images/header/elements-1.png" alt="" title="" />
                    </div>
                    <div className="elements-2">
                        <AppImg src="/landing/nunchuckschicken/assets/images/header/trophies.png" alt="" title="" />
                    </div>
                    <div className="tournament-date">
                        <AppImg
                            src="/landing/nunchuckschicken/assets/images/header/date.png"
                            alt="February 7-11, 2022"
                            title="February 7-11, 2022"
                        />
                    </div>

                    <div className="header-title-1">Partner Tournament</div>
                    <div className="header-title-2">with Lucky Envelopes</div>

                    <div className="thank-you-all-text">
                        <span className={"txt-line-one"}>Thank you all for participating in the tournament</span>
                        <span className={"txt-line-two"}>Congratulations to all the winners!</span>
                    </div>
                </div>
            </header>
            <section className={"ended"}>
                <div className="container">
                    <div className="head-1">
                        <AppImg src="/landing/nunchuckschicken/assets/images/section/head_1_Fox.png" alt="" title="" />
                    </div>
                    <div className="head-2">
                        <AppImg
                            src="/landing/nunchuckschicken/assets/images/section/head_2_Horse.png"
                            alt=""
                            title=""
                        />
                    </div>
                    <div className="lucky-envelopes-logo three-d-image">
                        <AppImg
                            src="/landing/nunchuckschicken/assets/images/footer/logo-luck-envelopes.png"
                            alt=""
                            title=""
                            className="main-img"
                        />
                        <AppImg
                            src="/landing/nunchuckschicken/assets/images/footer/logo-luck-envelopes.png"
                            alt=""
                            title=""
                            className="left-img"
                        />
                        <AppImg
                            src="/landing/nunchuckschicken/assets/images/footer/logo-luck-envelopes.png"
                            alt=""
                            title=""
                            className="right-img"
                        />
                    </div>

                    <div className="button-ended">
                        <AppImg
                            src="/landing/nunchuckschicken/assets/images/section/tournament-ended-button.png"
                            alt="Enter Partner Tournament"
                            title="Enter Partner Tournament"
                        />
                    </div>
                    <div className="bottom-ellipse">
                        <AppImg
                            src="/landing/nunchuckschicken/assets/images/header/ellipse-yellow.png"
                            alt=""
                            title=""
                        />
                    </div>
                </div>
            </section>
            <section className={"landing"}>
                <div className="container">
                    <div style={{ height: 20 }} />
                    <div className="prizes">
                        <div className="prize prz-1">
                            <div className="prize-title">1st Prize</div>
                            <AppImg
                                src="/landing/nunchuckschicken/assets/images/section/prize-200.png"
                                alt=""
                                title=""
                            />
                        </div>
                        <div className="prize prz-2">
                            <div className="prize-title">2nd Prize</div>
                            <AppImg
                                src="/landing/nunchuckschicken/assets/images/section/prize-150.png"
                                alt=""
                                title=""
                            />
                        </div>
                        <div className="prize prz-3">
                            <div className="prize-title">3rd Prize</div>
                            <AppImg
                                src="/landing/nunchuckschicken/assets/images/section/prize-100.png"
                                alt=""
                                title=""
                            />
                        </div>
                    </div>
                    <div style={{ height: 80 }} />
                </div>
            </section>
            <section className={"footer"}>
                <div className="container">
                    <div className="button-see-next-time">
                        <AppImg
                            src="/landing/nunchuckschicken/assets/images/section/see-you-next-button.png"
                            alt="Enter Partner Tournament"
                            title="Enter Partner Tournament"
                        />
                    </div>
                </div>
            </section>
        </div>
    );
});

export default TournamentEnding;
