import React from "react";
import AppImg from "@components/main/appImg";
import { isMobile } from "react-device-detect";
import moment from "moment";
import ClientUsersService from "client/services/clientUsersService";
import { EnumUserClientAction } from "models/enum/user";
import { Tournaments } from "utils/tournamets";
import FlipCountDown from "@components/flip-countdown/flip-count-down";

const TournamentLanding = () => {
    const tournament = Tournaments.getTournamentsStorage().find((t) => t.code === "nunchucksChicken");
    if (!tournament) {
        return <></>;
    }

    return (
        <div className={"tournament-landing"}>
            <header>
                <div className="container">
                    <div id="stars" />
                    <div id="stars2" />
                    <div id="stars3" />
                    <div className="background-img">
                        <AppImg src="/landing/nunchuckschicken/assets/images/header/bg.jpg" alt="" title="" />
                    </div>
                    <div className="logo-img three-d-image">
                        <AppImg
                            src="/landing/nunchuckschicken/assets/images/header/header_logo.png"
                            alt=""
                            title=""
                            className="main-img"
                        />
                        <AppImg
                            src="/landing/nunchuckschicken/assets/images/header/header_logo.png"
                            alt=""
                            title=""
                            className="left-img"
                        />
                        <AppImg
                            src="/landing/nunchuckschicken/assets/images/header/header_logo.png"
                            alt=""
                            title=""
                            className="right-img"
                        />
                    </div>
                    <div className="elements-1">
                        <AppImg src="/landing/nunchuckschicken/assets/images/header/elements-1.png" alt="" title="" />
                    </div>
                    <div className="elements-2">
                        <AppImg src="/landing/nunchuckschicken/assets/images/header/trophies.png" alt="" title="" />
                    </div>
                    <div className="header-title-1">Partner Tournament</div>
                    <div className="header-title-2">with Lucky Envelopes</div>
                    <div className="tournament-date">
                        <AppImg
                            src="/landing/nunchuckschicken/assets/images/header/date.png"
                            alt="February 7-11, 2022"
                            title="February 7-11, 2022"
                        />
                    </div>
                    {Tournaments.isTournamentTimerStartEnabled(tournament) && (
                        <div className={"tournament-timer"}>
                            <FlipCountDown
                                hideYear
                                hideMonth
                                endAtZero
                                titlePosition="bottom"
                                theme="dark"
                                dayTitle="Days"
                                hourTitle="Hours"
                                minuteTitle="Minutes"
                                secondTitle="Seconds"
                                {...((isMobile && { size: "small" }) || { size: "medium" })}
                                endAt={moment(tournament.timerStartDate).local(true).format("YYYY-MM-DD HH:mm:ss")} // Date/Time
                            />
                        </div>
                    )}

                    {Tournaments.isTournamentPlayEnabled(tournament) && (
                        <div className="button-take-part">
                            <a
                                href={"/play/player/" + tournament.gameCode}
                                className="animated-button2"
                                target={"_blank"}
                                rel="noreferrer"
                                onClick={async () => {
                                    await ClientUsersService.clientAction(
                                        EnumUserClientAction.playTournament,
                                        location.pathname,
                                        {
                                            code: tournament.code,
                                            gameCode: tournament.gameCode,
                                            isActive: tournament.isActive,
                                            button: "button-take-part",
                                        },
                                        "NunchucksChickenLanding"
                                    );
                                }}
                            >
                                <span />
                                <span />
                                <span />
                                <span />
                                <AppImg
                                    src="/landing/nunchuckschicken/assets/images/header/button-take-part.png"
                                    alt="take part to win prizes"
                                    title="take part to win prizes"
                                />
                            </a>
                        </div>
                    )}
                    <div className="bottom-ellipse">
                        <AppImg
                            src="/landing/nunchuckschicken/assets/images/header/ellipse-yellow.png"
                            alt=""
                            title=""
                        />
                    </div>
                </div>
            </header>
            <section className={"landing"}>
                <div className="container">
                    <div className="head-1">
                        <AppImg src="/landing/nunchuckschicken/assets/images/section/head_1_Fox.png" alt="" title="" />
                    </div>
                    <div className="head-2">
                        <AppImg
                            src="/landing/nunchuckschicken/assets/images/section/head_2_Horse.png"
                            alt=""
                            title=""
                        />
                    </div>
                    <div className="section-title-h1">PLAY NUNCHUCKS CHICKEN</div>
                    <div className="prizes">
                        <div className="prize prz-1">
                            <div className="prize-title">1st Prize</div>
                            <AppImg
                                src="/landing/nunchuckschicken/assets/images/section/prize-200.png"
                                alt=""
                                title=""
                            />
                        </div>
                        <div className="prize prz-2">
                            <div className="prize-title">2nd Prize</div>
                            <AppImg
                                src="/landing/nunchuckschicken/assets/images/section/prize-150.png"
                                alt=""
                                title=""
                            />
                        </div>
                        <div className="prize prz-3">
                            <div className="prize-title">3rd Prize</div>
                            <AppImg
                                src="/landing/nunchuckschicken/assets/images/section/prize-100.png"
                                alt=""
                                title=""
                            />
                        </div>
                    </div>
                    <div className="section-text">
                        We invite you to try out Skywind’s latest title, <span className="bold">Nunchucks Chicken</span>{" "}
                        which is scheduled to be released <span className="bold">February 23, 2022</span>. Get ready to
                        punch and smash your way to prizes with the power of Cluck-Fu.
                    </div>
                    <div className="section-header-title">
                        <span className="text">EXCLUSIVE TOURNAMENT</span>{" "}
                        <span className="text">WITH LUCKY ENVELOPES</span>
                    </div>
                    <div className="section-text">
                        Compete against others in an exclusive <span className="bold">Nunchucks Chicken</span>{" "}
                        Tournament for Partners between <span className="bold">February 7 -11, 2022</span>. You have a
                        limited wallet, so every bet counts. Finish at the top of the leaderboard and you could win some
                        high kicking prizes.
                    </div>
                    <div className="bottom-ellipse-large">
                        <AppImg
                            src="/landing/nunchuckschicken/assets/images/section/ellipse-white-large.png"
                            alt=""
                            title=""
                        />
                    </div>
                    <div className="bottom-ellipse-small">
                        <AppImg
                            src="/landing/nunchuckschicken/assets/images/section/ellipse-white-small.png"
                            alt=""
                            title=""
                        />
                    </div>
                    <div className="bottom-block" />
                </div>
            </section>
            <footer>
                <div className="container">
                    <div className="lucky-envelopes-logo three-d-image">
                        <AppImg
                            src="/landing/nunchuckschicken/assets/images/footer/logo-luck-envelopes.png"
                            alt=""
                            title=""
                            className="main-img"
                        />
                        <AppImg
                            src="/landing/nunchuckschicken/assets/images/footer/logo-luck-envelopes.png"
                            alt=""
                            title=""
                            className="left-img"
                        />
                        <AppImg
                            src="/landing/nunchuckschicken/assets/images/footer/logo-luck-envelopes.png"
                            alt=""
                            title=""
                            className="right-img"
                        />
                    </div>
                    <div className="top-block" />
                    <div className="section-text">
                        The tournament will also feature Skywind’s latest player engagement tool{" "}
                        <span className="bold">Lucky Envelopes</span>. Every day an envelope will drop randomly awarding
                        one lucky player a <span className="bold">50 Euro voucher</span>.
                    </div>
                    <div className="footer-title">Good luck!</div>
                    {Tournaments.isTournamentPlayEnabled(tournament) && (
                        <div className="button-enter-tournament">
                            <a
                                href={"/play/player/" + tournament.gameCode}
                                className="animated-button2"
                                target={"_blank"}
                                rel="noreferrer"
                                onClick={async () => {
                                    await ClientUsersService.clientAction(
                                        EnumUserClientAction.playTournament,
                                        location.pathname,
                                        {
                                            code: tournament.code,
                                            gameCode: tournament.gameCode,
                                            isActive: tournament.isActive,
                                            button: "button-enter",
                                        },
                                        "NunchucksChickenLanding"
                                    );
                                }}
                            >
                                <span />
                                <span />
                                <span />
                                <span />
                                <AppImg
                                    src="/landing/nunchuckschicken/assets/images/footer/button-enter.png"
                                    alt="Enter Partner Tournament"
                                    title="Enter Partner Tournament"
                                />
                            </a>
                        </div>
                    )}
                    {Tournaments.isTournamentTimerEndEnabled(tournament) && (
                        <div className={"tournament-timer"}>
                            <div className={"footer-title"}>ENDS IN</div>
                            <FlipCountDown
                                hideYear
                                hideMonth
                                endAtZero
                                titlePosition="bottom"
                                theme="dark"
                                dayTitle="Days"
                                hourTitle="Hours"
                                minuteTitle="Minutes"
                                secondTitle="Seconds"
                                {...((isMobile && { size: "small" }) || { size: "medium" })}
                                endAt={moment(tournament.playEndDate).local(true).format("YYYY-MM-DD HH:mm:ss")} // Date/Time
                            />
                        </div>
                    )}
                    {!Tournaments.isTournamentTimerStartEnabled(tournament) &&
                        !Tournaments.isTournamentTimerEndEnabled(tournament) && (
                            <div className={"tournament-timer"}>
                                <div className={"footer-title"}>TOURNAMENT ENDED</div>
                            </div>
                        )}
                    <div className="text-gray">
                        <a
                            href={"https://www.skywindgroup.com/en/tools/must-win-jackpot.html"}
                            target={"_blank"}
                            rel="noreferrer"
                            onClick={async () => {
                                await ClientUsersService.clientAction(
                                    EnumUserClientAction.rulesTournament,
                                    location.pathname,
                                    {
                                        code: tournament.code,
                                        gameCode: tournament.gameCode,
                                        isActive: tournament.isActive,
                                        link: "must-win-jackpot",
                                    },
                                    "NunchucksChickenLanding"
                                );
                            }}
                        >
                            Learn more about Skywind Tournaments
                        </a>
                    </div>
                    <div className="text-gray">
                        <a
                            href={"/landing/nunchuckschicken/Nunchucks-Chicken-Tourney-Ts-Cs-31.docx"}
                            target={"_blank"}
                            rel="noreferrer"
                            onClick={async () => {
                                await ClientUsersService.clientAction(
                                    EnumUserClientAction.rulesTournament,
                                    location.pathname,
                                    {
                                        code: tournament.code,
                                        gameCode: tournament.gameCode,
                                        isActive: tournament.isActive,
                                        link: "tournament-rules",
                                    },
                                    "NunchucksChickenLanding"
                                );
                            }}
                        >
                            Read Partner Tournament Rules
                        </a>
                    </div>
                </div>
            </footer>
        </div>
    );
};

export default TournamentLanding;
