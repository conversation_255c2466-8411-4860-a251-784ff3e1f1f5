import React from "react";
import { isDesktop, isMobile } from "react-device-detect";
import AppImg from "@components/main/appImg";
import moment from "moment";
import AppLink from "@components/main/appLink";
import appConfig from "appConfig";
import ClientUsersService from "client/services/clientUsersService";
import { EnumUserClientAction } from "models/enum/user";
import { Tournaments } from "utils/tournamets";
import FlipCountDown from "@components/flip-countdown/flip-count-down";

const TournamentBanner = () => {
    const tournament = Tournaments.getTournamentsStorage().find((t) => t.code === "nunchucksChicken");
    if (!tournament) {
        return <></>;
    }
    return (
        <div className={"tournament-wrapper"}>
            <AppLink
                href={
                    tournament && Tournaments.isTournamentPlayEnabled(tournament)
                        ? "/play/player/" + tournament.gameCode
                        : ""
                }
                onClick={async () => {
                    await ClientUsersService.clientAction(
                        EnumUserClientAction.playTournament,
                        location.pathname,
                        { gameCode: tournament?.gameCode, isActive: appConfig.tournaments.isActive },
                        "NunchucksChickenBanner"
                    );
                }}
            >
                {isDesktop && (
                    <AppImg
                        src={"/landing/nunchuckschicken/nunchuckschicken-desktop.jpg"}
                        className={"tournament-image desktop"}
                        alt={"Play in tournament"}
                    />
                )}
                {isMobile && (
                    <AppImg
                        src={"/landing/nunchuckschicken/nunchuckschicken-mobile-h.jpg"}
                        className={"tournament-image mobile-h"}
                        alt={"Play in tournament"}
                    />
                )}
                {isMobile && (
                    <AppImg
                        src={"/landing/nunchuckschicken/nunchuckschicken-mobile-v.jpg"}
                        className={"tournament-image mobile-v"}
                        alt={"Play in tournament"}
                    />
                )}
                <FlipCountDown
                    hideYear
                    hideMonth
                    endAtZero
                    titlePosition="bottom"
                    theme="dark"
                    dayTitle="Days"
                    hourTitle="Hours"
                    minuteTitle="Minutes"
                    secondTitle="Seconds"
                    {...((isMobile && { size: "small" }) || { size: "medium" })}
                    endAt={moment(tournament?.timerStartDate).local(true).format("YYYY-MM-DD HH:mm:ss")} // Date/Time
                />
            </AppLink>
        </div>
    );
};

export default TournamentBanner;
