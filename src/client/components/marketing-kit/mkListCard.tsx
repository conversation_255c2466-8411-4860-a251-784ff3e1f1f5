import React from "react";
import AppImg from "@components/main/appImg";
import { Button, Card } from "react-bootstrap";
import { observer } from "mobx-react-lite";
import { IMarketingKitTree } from "utils/marketing-kit-utils";

function isImage(url: string): boolean {
    return url.match(/\.(jpeg|jpg|gif|png|svg)$/) != null;
}

const MkListCard = observer(
    ({ folder, file, url }: { folder: IMarketingKitTree; file: IMarketingKitTree; url: string }) => {
        const fullFileUrl = url + file.path + "/" + file.name;

        const imgSrc = (file: IMarketingKitTree, url: string) => {
            return isImage(file.name) ? url + file.path + "/" + file.name : "/assets/pa/images/poster-428x268.png";
        };

        // const fileExt = file.name.split(".").pop();

        return (
            <Card style={{ maxWidth: "320px" }}>
                <AppImg
                    style={{
                        objectFit: "cover",
                        width: "100%",
                    }}
                    alt={file.name}
                    height="200"
                    src={imgSrc(file, url)}
                    onClick={() => {
                        window.open(fullFileUrl, "_blank");
                    }}
                />
                <Card.Body>
                    <Card.Title>{folder.name}</Card.Title>
                    <Card.Text>{file.name}</Card.Text>
                    <Button variant="primary" href={fullFileUrl} target={"_blank"}>
                        Download
                    </Button>
                </Card.Body>
            </Card>
        );
    }
);

export default MkListCard;
