import React, { useEffect, useRef, useState } from "react";
import { observer } from "mobx-react-lite";
import ClientGamesService from "../../services/clientGamesService";
import MkListItem from "@components/marketing-kit/mkListItem";
import { IMarketingKitTree, MarketingKitUtils } from "utils/marketing-kit-utils";
//
// export interface IAccordion {
//     [folder: string]: string[];
// }

const MarketingKit = observer((props: { gameId: string }) => {
    const { gameId } = props;

    const [status, setStatus] = useState<number>(0);

    const [marketingKit, setMarketingKit] = useState<IMarketingKitTree[]>([]);

    const hideItems = (mk: IMarketingKitTree[]): IMarketingKitTree[] => {
        mk.forEach((i) => {
            i.open = false;
            if (i.items.length > 0) {
                i.items = hideItems(i.items);
            }
        });
        return mk;
    };

    const setOpenItems = (path: string, mk: IMarketingKitTree[], open: boolean): IMarketingKitTree[] => {
        const pArr = path.split("/").slice(1);
        for (let pi = 0; pi < pArr.length; pi++) {
            const p = pArr[pi];
            const i = mk.findIndex((i) => i.name === p && i.type === "folder");
            if (i !== -1) {
                mk[i].open = pArr.length === 1 ? open : true;
                const newPArr = pArr.slice(pi + 1);
                if (newPArr.length > 0 && mk[i].items.length > 0) {
                    mk[i].items = setOpenItems("/" + newPArr.join("/"), mk[i].items, open);
                    break;
                }
            }
        }
        return mk;
    };

    const clickMarketingKitItem = (path: string, open: boolean) => {
        setMarketingKit([...setOpenItems(path, hideItems(marketingKit), open)]);
    };

    const strictModeRef = useRef<boolean>(false);

    useEffect(() => {
        if (!strictModeRef.current) {
            strictModeRef.current = true;
            (async () => {
                setStatus(1);

                const data = await ClientGamesService.getMarketingKit(gameId);
                if (data) {
                    const tree = MarketingKitUtils.parseFromSanityToTree(data, ["thumbs.db"]);
                    if (Array.isArray(tree)) {
                        setMarketingKit(tree);
                        setStatus(2);
                    } else {
                        setStatus(3);
                    }
                }
            })();
        }
        // eslint-disable-next-line
    }, []);

    let mkList = marketingKit;
    if (Array.isArray(marketingKit) && marketingKit.length === 1 && marketingKit[0].name === "Marketing Kit") {
        mkList = marketingKit[0].items;
    }

    if (status === 2 && Array.isArray(mkList) && mkList.length > 0) {
        return (
            <div className={"mk-list-items"}>
                {mkList.map((item) => (
                    <MkListItem
                        key={item.path + item.name}
                        item={item}
                        url={"https://storage.googleapis.com/lobby.stg1.m27613.com/gamestudios/" + gameId}
                        clickHandler={clickMarketingKitItem}
                    />
                ))}
            </div>
        );
    } else if (status === 1) {
        return <div>Please wait, loading...</div>;
    } else {
        return <div>No marketing kit to download</div>;
    }
});

export default MarketingKit;
