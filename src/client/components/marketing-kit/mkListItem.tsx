import React from "react";
import { Card } from "react-bootstrap";
import { HiOutlineDocument } from "@react-icons/all-files/hi/HiOutlineDocument";
import { IMarketingKitTree } from "utils/marketing-kit-utils";
import { observer } from "mobx-react-lite";
import MkListCard from "@components/marketing-kit/mkListCard";

const MkListItem = observer(
    ({
        item,
        url,
        clickHandler,
    }: {
        item: IMarketingKitTree;
        url: string;
        clickHandler: (path: string, open: boolean) => void;
    }) => {
        const itemFullPath = item.path + "/" + item.name;
        const fLength = itemFullPath.split("/").length;
        return (
            <Card key={itemFullPath}>
                <Card.Header style={{ paddingLeft: fLength - 3 > 0 ? (fLength - 3) * 30 : 0 }}>
                    <button
                        className="btn btn-primary"
                        type="button"
                        onClick={() => {
                            clickHandler(item.path + "/" + item.name, !item.open);
                        }}
                    >
                        <HiOutlineDocument color={"white"} style={{ color: "#8dd363", fontSize: "1.5em" }} />{" "}
                        {item.name}
                    </button>
                </Card.Header>
                <div className={item.open ? "" : "collapse"}>
                    <div className={"sub-folders"}>
                        {item.items.map(
                            (f) =>
                                f.type === "folder" &&
                                f.items.length > 0 && (
                                    <MkListItem key={f.path + f.name} item={f} url={url} clickHandler={clickHandler} />
                                )
                        )}
                    </div>
                    <Card.Body
                        style={{
                            display: "flex",
                            flexFlow: "row wrap",
                            justifyContent: "space-around",
                        }}
                    >
                        {item.items.map(
                            (f) =>
                                f.type === "file" && (
                                    <MkListCard key={f.path + "/" + f.name} folder={item} file={f} url={url} />
                                )
                        )}
                    </Card.Body>
                </div>
            </Card>
        );
    }
);

export default MkListItem;
