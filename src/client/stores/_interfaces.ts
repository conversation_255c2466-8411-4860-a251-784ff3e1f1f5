import { ToastOptions } from "react-toastify";

export interface IAppNotification {
    message: string;
    title?: string;
    notification: ToastOptions;
    date: string;
}

export interface IGamesSearchFullScreen {
    isVisible: boolean;
}

export interface IAppCdnImageInfo { w: number; h: number, s: number[] }
export interface IAppCdnImageSizes {
    [fileHash: string]: IAppCdnImageInfo
}

export interface IAppCdnFileInfo { p: string }
export interface IAppCdnFiles {
    [fileHash: string]: IAppCdnFileInfo
}