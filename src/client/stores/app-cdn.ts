import { IAppCdnFileInfo, IAppCdnFiles, IAppCdnImageInfo, IAppCdnImageSizes } from "@stores/_interfaces";
import { JsUtils } from "utils/js-utils";
import axios, { AxiosResponse } from "axios";

export class AppCdn {

    public cdnCache = { images: { time: 0, ttl: 3600 * 1000 }, files: { time: 0, ttl: 3600 * 1000 } };

    public appImages: IAppCdnImageSizes = {};

    public appFiles: IAppCdnFiles = {};

    private static _instance: AppCdn;

    public static getInstance(): AppCdn {
        if (!AppCdn._instance) {
            AppCdn._instance = new AppCdn();
        }

        return AppCdn._instance;
    }

    async getAppImages(): Promise<IAppCdnImageSizes> {
        const now = new Date().getTime();
        if (!JsUtils.isObjectAndNotEmpty(this.appImages) || (this.cdnCache.images.time + this.cdnCache.images.ttl) < now) {
            let cdnImagesResponse: AxiosResponse<IAppCdnImageSizes> | undefined = await axios.get<IAppCdnImageSizes>(
                "https://cdn.skywindgroup.com/cdn/sizes/images.json"
            );
            if (JsUtils.isObjectAndNotEmpty(cdnImagesResponse.data)) {
                this.appImages = cdnImagesResponse.data;
                this.cdnCache.images.time = now;
            }
        }
        return this.appImages;
    };

    async getAppImageSize(fileHash?: string): Promise<IAppCdnImageInfo | null> {
        if (!JsUtils.isObjectAndNotEmpty(this.appImages)) {
            await this.getAppImages();
        }
        if (typeof fileHash === "string" && fileHash in this.appImages) {
            return this.appImages[fileHash];
        }
        return null;
    }

    async getAppFiles(): Promise<IAppCdnFiles> {
        const now = new Date().getTime();
        if (!JsUtils.isObjectAndNotEmpty(this.appFiles) || (this.cdnCache.files.time + this.cdnCache.files.ttl) < now) {
            let cdnFilesResponse: AxiosResponse<IAppCdnFiles> | undefined = await axios.get<IAppCdnFiles>(
                "https://cdn.skywindgroup.com/cdn/file/files.json"
            );
            if (JsUtils.isObjectAndNotEmpty(cdnFilesResponse.data)) {
                this.appFiles = cdnFilesResponse.data;
                this.cdnCache.files.time = now;
            }
        }
        return this.appFiles;
    };

    async getAppFile(fileHash?: string): Promise<IAppCdnFileInfo | null> {
        if (!JsUtils.isObjectAndNotEmpty(this.appFiles)) {
            await this.getAppFiles();
        }
        if (typeof fileHash === "string" && fileHash in this.appFiles) {
            return this.appFiles[fileHash];
        }
        return null;
    }
}

export const appCdn = AppCdn.getInstance();