import { makeAutoObservable } from "mobx";
import moment from "moment";
import { AxiosError } from "axios";
import { ResponseError } from "utils/serverResponse";
import { IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import ClientProjectService from "client/services/clientProjectService";
import { toast, ToastOptions } from "react-toastify";
import { IAppNotification, IGamesSearchFullScreen } from "@stores/_interfaces";
import { IAppDialog } from "@components/dialogs/iDialogs";
import { EnumDialogType } from "@components/dialogs/enums";
import { EnumLocale } from "locales/locales";
import { IConfigProject } from "iAppConfig";
import { IAppServerConfig } from "appServerConfig";
import { EnumServerEnv } from "models/enum/system";
import { IProjectPages } from "models/site-pages";

const AppStore = makeAutoObservable({
    fixedClassName: "window-fixed",

    notifications: [] as IAppNotification[],

    websiteFooter: null as IWebsiteFooter | null,

    isAppLoading: false,

    gameSearchFullScreen: { isVisible: false } as IGamesSearchFullScreen,

    appUserFirstEntranceAgreement: false,

    appUserPasswordChangeStatus: false,

    appDialog: { title: "", type: EnumDialogType.images, isOpen: false, attrs: {} } as IAppDialog,

    appHostnameWithPort: "",

    appProjectLocale: EnumLocale.enUS,

    appProject: {} as IConfigProject,

    appServerConfig: {} as IAppServerConfig,

    appProjectPages: {} as IProjectPages,

    setAppProjectPages(appProjectPages: IProjectPages): void {
        this.appProjectPages = appProjectPages;
    },

    getAppProjectPages(): IProjectPages {
        return this.appProjectPages;
    },

    setHostWithPort(hostWithPort: string): void {
        this.appHostnameWithPort = hostWithPort;
    },

    getHostWithPort(): string {
        return this.appHostnameWithPort;
    },

    getHttpSchema(): string {
        return this.appServerConfig.serverEnv === EnumServerEnv.local ? "http://" : "https://";
    },

    getHostName(): string {
        return this.appServerConfig.serverEnv === EnumServerEnv.local
               ? this.appHostnameWithPort
               : this.appHostnameWithPort.split(":")[0];
    },

    setAppLocale(locale: EnumLocale): void {
        this.appProjectLocale = locale;
    },

    getAppLocale(): EnumLocale {
        return this.appProjectLocale;
    },

    setAppProject(appProject: IConfigProject): void {
        this.appProject = appProject;
    },

    getAppProject(): IConfigProject {
        return this.appProject;
    },

    setAppServerConfig(appServerConfig: IAppServerConfig): void {
        this.appServerConfig = appServerConfig;
    },

    getAppServerConfig(): IAppServerConfig {
        return this.appServerConfig;
    },

    addNotification(message: string, title: string, notification: ToastOptions): void {
        if (this.notifications.length > 25) {
            this.notifications.pop();
        }
        this.notifications.unshift({ message, title, notification, date: moment().toISOString() });
    },

    addNotificationError(message: string, title: string = "Error") {
        this.addNotification(message, title, { type: toast.TYPE.ERROR });
    },

    addNotificationSuccess(message: string, title: string = "Success") {
        this.addNotification(message, title, { type: toast.TYPE.SUCCESS });
    },

    removeNotification(id: number) {
        this.notifications.splice(id, 1);
    },

    getNotifications(): IAppNotification[] {
        return this.notifications;
    },

    clearNotifications(): void {
        this.notifications = [];
    },

    catchClientError(e: Error) {
        // @ts-ignore
        const message = (e as AxiosError).response?.data?.message || e.message;
        this.addNotificationError(message + " Date: " + moment().toISOString(), e.name);
        this.showNotifications();
    },

    showNotifications(from: number = 0, number: number = 1): void {
        const nl = this.notifications.length;
        if (from > nl) {
            return;
        }
        for (let step = from; step < (from + number > nl ? nl : from + number); step++) {
            const msg = this.notifications[step];
            toast(msg.message, {
                ...msg.notification,
                position: "top-right",
                autoClose: 5000,
                hideProgressBar: false,
                closeOnClick: true,
                pauseOnHover: true,
                draggable: true,
                progress: undefined
            });
        }
    },

    async onClientResponseError(
        err: ResponseError,
        skipExecuteParent = false,
        onError?: (error: ResponseError) => void
    ) {
        if (!skipExecuteParent) {
            const messages = err.getMessages();
            this.addNotificationError(messages.join(". "));
            this.showNotifications();
        }
        if (onError) {
            onError.call(this, err);
        }
    },

    getGamesSearchFullScreen(): IGamesSearchFullScreen {
        return this.gameSearchFullScreen;
    },

    setGamesSearchFullScreen(o: IGamesSearchFullScreen): void {
        this.gameSearchFullScreen = o;
    },

    setUserFirstEntranceAgreement(status: boolean) {
        this.appUserFirstEntranceAgreement = status;
    },

    getUserFirstEntranceAgreement(): boolean {
        return this.appUserFirstEntranceAgreement;
    },

    setUserPasswordChangeStatus(status: boolean) {
        this.appUserPasswordChangeStatus = status;
    },

    getUserPasswordChangeStatus(): boolean {
        return this.appUserPasswordChangeStatus;
    },

    setAppLoading(status: boolean) {
        this.isAppLoading = status;
    },

    getAppLoading(): boolean {
        return this.isAppLoading;
    },

    openDialog(dialog: IAppDialog) {
        if (this.appDialog?.isOpen === true) {
            this.closeDialog();
        }
        this.appDialog = dialog;
        this.appDialog.isOpen = true;
    },

    getDialog(): IAppDialog {
        return this.appDialog;
    },

    closeDialog() {
        if (this.appDialog !== null) {
            this.appDialog = { title: "", type: EnumDialogType.images, isOpen: false, attrs: {} };
        }
    },

    async getWebsiteFooter(): Promise<IWebsiteFooter | null> {
        if (!this.websiteFooter) {
            this.websiteFooter = await ClientProjectService.getWebsiteFooter();
        }
        return this.websiteFooter;
    }
});

export type IAppStore = typeof AppStore;

export default AppStore;
