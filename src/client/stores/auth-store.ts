import { makeAutoObservable } from "mobx";
import { AxiosError } from "axios";

import appConfig from "appConfig";
import {
    IUserChangePasswordRequest,
    IUserLoginRequest,
    IUserLoginResponse,
    IUserRefreshTokensResponse
} from "api/services/interfaces/iUsersService";
import appStore from "@stores/app-store";
import { EnumLocalStorage } from "models/enum/local-storage";
import { ClientApiResponse, ResponseError, ResponseSuccess, ServerResponse } from "utils/serverResponse";
import { IResponseSuccess } from "api/interfaces/response";
import ClientUsersService from "client/services/clientUsersService";
import { EnumDialogType } from "@components/dialogs/enums";
import moment from "moment";
import { EnumDBUserPermissions } from "models/enum/db";
import { Project } from "utils/project";
import { EnumPage } from "models/enum/page";

const AuthStore = makeAutoObservable({
    user: {} as IUserLoginResponse,
    isAuthorized: false,

    setAuth(bool: boolean): void {
        this.isAuthorized = bool;
    },

    setUser(user: IUserLoginResponse): void {
        this.user = { ...(this.user || {}), ...user };
    },

    getUser(): IUserLoginResponse | null {
        return this.user;
    },

    setLoading(status: boolean): void {
        appStore.setAppLoading(status);
    },

    async registration(email: string, password: string) {
        try {
            return await ClientUsersService.registrationRequest(email, password);
        } catch (e) {
            this.reset();
            return (e as AxiosError).response?.data;
        }
    },

    reset(): void {
        localStorage.removeItem(EnumLocalStorage.accessToken);
        this.setAuth(false);
        this.setUser({} as IUserLoginResponse);
    },

    authorise(user: IUserLoginResponse): void {
        if (!user || !user?.accessToken) {
            this.reset();
        } else {
            localStorage.setItem(EnumLocalStorage.accessToken, String(user.accessToken));
            this.setAuth(true);
            this.setUser(user);
        }
    },

    async changePassword(
        params: IUserChangePasswordRequest,
        onSuccess?: (success: ResponseSuccess) => void,
        onError?: (error: ResponseError) => void
    ) {
        try {
            const self = this;
            const response = new ClientApiResponse(await ClientUsersService.changePasswordRequest(params)).response;

            response
                .onError((err, skipExecuteParent = false) => {
                    appStore.onClientResponseError(err, skipExecuteParent, onError);
                })
                .onSuccess((success, skipExecuteParent = false) => {
                    if (!skipExecuteParent) {
                        const user = success.data as IUserLoginResponse;
                        if (
                            typeof user?.passwordUpdatedAt !== "string" ||
                            !moment(String(user?.passwordUpdatedAt)).isValid()
                        ) {
                            appStore.openDialog({
                                type: EnumDialogType.userPasswordChange,
                                title: "Change Password",
                                attrs: {}
                            });
                        }
                        self.setUser(user);
                    }
                    if (onSuccess) {
                        onSuccess.call(this, success);
                    }
                });
            return response;
        } catch (e) {
            appStore.catchClientError(e as Error);
        }
    },

    async logout(): Promise<void> {
        try {
            await ClientUsersService.logoutRequest();
            this.reset();
        } catch (e) {
            this.reset();
            appStore.catchClientError(e as Error);
        }
    },

    async login(
        params: IUserLoginRequest,
        onSuccess?: (success: IResponseSuccess) => void,
        onError?: (error: ResponseError) => void
    ) {
        const self = this;
        try {
            const response = new ClientApiResponse(await ClientUsersService.loginRequest(params)).response;

            response
                .onError((err, skipExecuteParent = false) => {
                    appStore.onClientResponseError(err, skipExecuteParent, onError);
                })
                .onSuccess((success: IResponseSuccess<IUserLoginResponse>, skipExecuteParent = false) => {
                    if (!skipExecuteParent) {
                        const user = success.data;
                        self.authorise(user);
                    }
                    if (onSuccess) {
                        onSuccess.call(this, success);
                    }
                });
        } catch (e) {
            appStore.catchClientError(e as Error);
        }
    },

    getBasicEndpoint(): string {
        const user = this.getUser();
        const userPermissions = user?.userPermissions?.permissions;
        let response: string = "/";
        if (typeof window !== "undefined" && typeof localStorage !== "undefined" && (localStorage.getItem(EnumLocalStorage.redirectAfterLogin) !== "/login")) {
            const redirectPath = localStorage.getItem(EnumLocalStorage.redirectAfterLogin) || false;
            if (redirectPath) {
                localStorage.removeItem(EnumLocalStorage.redirectAfterLogin);
                response = redirectPath;
            }
        } else if (user && Array.isArray(userPermissions)) {
            if (userPermissions.includes(EnumDBUserPermissions.paCanViewPageRoadmap)) {
                response = appConfig.client.endpoints.pa.default;
            } else if (userPermissions.includes(EnumDBUserPermissions.paCanViewSwDocs)) {
                const p = Project.getPaPages();
                response = p[EnumPage.supportDocs].path;
            }
        }
        return response;
    },

    async checkAuth(failedCallback?: () => void, successCallback?: () => void) {
        this.setLoading(true);
        try {
            const accessToken = localStorage.getItem(EnumLocalStorage.accessToken);
            if (accessToken) {
                const response = (await ClientUsersService.refreshToken()) as ServerResponse;

                response
                    .onError((err: ResponseError) => {
                        this.reset();
                        if (failedCallback) {
                            failedCallback();
                        }
                        appStore.onClientResponseError(err, false);
                    })
                    .onSuccess((success: IResponseSuccess<IUserRefreshTokensResponse>) => {
                        const refreshResponse = success.data;
                        localStorage.setItem(EnumLocalStorage.accessToken, String(refreshResponse.accessToken));
                        this.setAuth(true);
                        this.setUser(refreshResponse as IUserLoginResponse);
                        if (successCallback) {
                            successCallback();
                        }
                    });

                // if (response?.status === 401 || response?.status !== 200) {
                //     this.reset();
                //     if (failedCallback) {
                //         failedCallback();
                //     }
                // } else {
                //     localStorage.setItem(EnumLocalStorage.accessToken, String(response.data.accessToken));
                //     this.setAuth(true);
                //     this.setUser(response.data as IUserLoginResponse);
                //     if (successCallback) {
                //         successCallback();
                //     }
                // }
            } else {
                this.reset();
                if (failedCallback) {
                    failedCallback();
                }
            }
        } catch (e) {
            if ((e as AxiosError).response?.status === 401) {
                this.reset();
                if (failedCallback) {
                    failedCallback();
                }
            }
            return (e as AxiosError).response?.data;
        } finally {
            this.setLoading(false);
        }
    }
});

export default AuthStore;
