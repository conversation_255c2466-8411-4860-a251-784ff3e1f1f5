import { EnumDialogType } from "@components/dialogs/enums";
import {
    IAppDialogCertificatesAttrs,
    IAppDialogIframeAttrs,
    IAppDialogImage,
    IAppDialogImagesAttrs,
    IAppDialogMarketingKitAttrs,
    IAppDialogVideoAttrs,
} from "@components/dialogs/iDialogs";
import { IMarketCertificate } from "api/services/interfaces/iGamesService";
import AppStore from "@stores/app-store";

export class Dialogs {
    static openMarketingKit = (title: string, gameId: string): void => {
        AppStore.openDialog({
            title,
            type: EnumDialogType.marketingKit,
            attrs: { gameId } as IAppDialogMarketingKitAttrs,
        });
    };
    static openIframe = (title: string, url: string): void => {
        AppStore.openDialog({
            title,
            type: EnumDialogType.iframe,
            attrs: { url } as IAppDialogIframeAttrs,
        });
    };
    static openVideo = (title: string, videoUrl: string): void => {
        AppStore.openDialog({
            title,
            type: EnumDialogType.video,
            attrs: { videoUrl } as IAppDialogVideoAttrs,
        });
    };
    static openCertificates = (title: string, certificates: IMarketCertificate[]): void => {
        AppStore.openDialog({
            title,
            type: EnumDialogType.certificates,
            attrs: { certificates } as IAppDialogCertificatesAttrs,
        });
    };
    static openImages = (title: string, images: IAppDialogImage[]): void => {
        AppStore.openDialog({
            title,
            type: EnumDialogType.images,
            attrs: { images } as IAppDialogImagesAttrs,
        });
    };
    static openFirstEntrance = (title: string): void => {
        AppStore.openDialog({
            title,
            type: EnumDialogType.userFirstEntrance,
            attrs: {},
        });
    };
    static openPasswordChange = (title: string): void => {
        AppStore.openDialog({
            title,
            type: EnumDialogType.userPasswordChange,
            attrs: {},
        });
    };
}
