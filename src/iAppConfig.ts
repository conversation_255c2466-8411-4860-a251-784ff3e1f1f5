import {
    EnumCurrencyCode,
    EnumGameServerEntityType,
    EnumGameServerPlayMode,
    EnumProjectCode,
    EnumProjectType,
    EnumRegionCode,
    EnumServerEnv
} from "models/enum/system";

export interface IConfigGameServerEntity {
    name: string;
    entity: {
        key: string;
        username: string;
        password?: string;
        type: EnumGameServerEntityType;
        playMode: EnumGameServerPlayMode;
        token?: string;
    };
    api: {
        operator?: { url: string };
        site?: { url: string };
        player?: { url: string };
    };
}

export interface IConfigGameServerApi {
    regionCode: EnumRegionCode;
    googleBucketKey: string;
    active: boolean;
    currencyCode: EnumCurrencyCode;
    currencySymbol: string;
    entities: IConfigGameServerEntity[];
}

export interface IConfigGameServer {
    available: IConfigGameServerApi[];
    default: { regionCode: string };
}

export interface IConfigProjectDomain {
    hostname: string;
    environment: EnumServerEnv;
}

export interface IConfigProject {
    code: EnumProjectCode;
    googleAnalyticsTracingCode: string;
    name: string;
    type: EnumProjectType;
    domains: IConfigProjectDomain[];
    canonicalDomain: string; // only one domain can be used as canonical for search engines
}

export interface IConfigProjects {
    [projectCode: string]: IConfigProject;
}

export interface ITournamentsUsed {
    code: string;
}

export interface ITournamentsConfig {
    isActive: boolean;
    currentlyUsed: ITournamentsUsed[];
    storage: ITournamentModel[];
}

export interface ITournamentModel {
    isActive: boolean;
    gameId: string;
    code: string;
    gameCode: string;
    startDate: string; // UTC
    endDate: string; // UTC
    timerStartDate: string; // UTC
    timerEndDate: string; // UTC
    playStartDate: string; // UTC
    playEndDate: string; // UTC
    gameServerSecretKey: string;
}
