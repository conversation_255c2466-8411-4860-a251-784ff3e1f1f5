import logger from "utils/logger";
import { IProjectsController } from "api/controllers/interfaces/iProjectsController";
import { NextApiRequest, NextApiResponse } from "next";
import appConfig from "appConfig";
import { ServerRequest } from "utils/serverRequest";
import { ServerController } from "api/controllers/serverController";
import { EnumServerResponseType } from "utils/serverResponse";
import { errorCodes } from "errorCodes";
import { validateRequestParams, validator } from "models/validatorSchemas";
import { ProjectServiceInstance } from "api/services/projectService";

const log = logger("controller--project");

export class ProjectController extends ServerController implements IProjectsController {

    constructor(serverRequest: ServerRequest) {
        super(serverRequest);
    }

    static async handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
        try {
            const endpoints = appConfig.client.endpoints.api;
            const c = new ServerRequest(ProjectController, req, res, log, appConfig.client.endpoints.api.path);
            await c.httpGet(endpoints.project.health, ProjectController.prototype.getHealth, false);
            await c.httpGet(endpoints.project.newsList, ProjectController.prototype.getNewsList, false);
            await c.httpGet(endpoints.project.newsArticle, ProjectController.prototype.getNewsInfo, false);
            await c.httpGet(endpoints.project.websiteFooter, ProjectController.prototype.getWebsiteFooter, false);
            await c.httpEnd();
        } catch (e) {
            log.error(e);
        }
    }

    async getHealth(): Promise<void> {
        try {
            this.successResponse(
                await ProjectServiceInstance.checkHealth(),
                EnumServerResponseType.successObject,
                ProjectController.prototype
            ).serverResponse();
        } catch (e) {
            this.errorResponseForGet(e as Error, ProjectController.prototype, errorCodes.e13001001);
        }
    }

    async getNewsList(params: {}): Promise<void> {
        try {
            this.successResponse(
                await ProjectServiceInstance.getNewsList(this.request.req, params),
                EnumServerResponseType.successArray,
                ProjectController.prototype
            ).serverResponse();
        } catch (e) {
            this.errorResponseForGet(e as Error, ProjectController.prototype, errorCodes.e13002001);
        }
    }

    async getNewsInfo(params: { newsId: string }): Promise<void> {
        try {
            const data = await validateRequestParams<{ newsId: string }>(
                {
                    newsId: validator.news.newsId.required()
                },
                params
            );
            const response = await ProjectServiceInstance.getNewsArticle(this.request.req, data.newsId);
            this.successResponse(
                response,
                EnumServerResponseType.successObject,
                ProjectController.prototype
            ).serverResponse();
        } catch (e) {
            this.errorResponseForGet(e as Error, ProjectController.prototype, errorCodes.e13004001);
        }
    }

    async getWebsiteFooter(params: {}): Promise<void> {
        try {
            const response = await ProjectServiceInstance.getWebsiteFooter(this.request.req, params);
            this.successResponse(
                response,
                EnumServerResponseType.successObject,
                ProjectController.prototype
            ).serverResponse();
        } catch (e) {
            this.errorResponseForGet(e as Error, ProjectController.prototype, errorCodes.e13003001);
        }
    }
}
