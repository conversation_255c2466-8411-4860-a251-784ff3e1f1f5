import { NextApiRequest, NextApiResponse } from "next";
import logger from "../../utils/logger";
import { IUserClientActionParams, IUsersController } from "./interfaces/iUsersController";
import {
    IUserAttrsRequest,
    IUserChangePasswordRequest,
    IUserInfo,
    IUserLoginRequest,
    IUserLoginResponse,
    IUserRefreshTokensResponse,
    IUserRegisterRequest,
    IUserRegisterResponse
} from "../services/interfaces/iUsersService";
import { validateRequestParams, validator } from "models/validatorSchemas";
import { errorCodes } from "errorCodes";
import appConfig from "appConfig";
import { ServerCookies } from "utils/serverCookies";
import { ServerRequest } from "utils/serverRequest";
import { ref } from "yup";
import { EnumServerResponseType, ServerResponse } from "utils/serverResponse";
import { ServerController } from "./serverController";
import { Unauthorized } from "utils/errors";
import { UsersServiceInstance } from "api/services/usersService";
import { TokenServiceInstance } from "api/services/tokenService";

const log = logger("controller--users");

export class UsersController extends ServerController implements IUsersController {

    constructor(serverRequest: ServerRequest) {
        super(serverRequest);
    }

    static async handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
        try {
            const endpoints = appConfig.client.endpoints.api;
            const c = new ServerRequest(UsersController, req, res, log, appConfig.client.endpoints.api.path);
            await c.httpGet(endpoints.users.activate, UsersController.prototype.activate, false);
            await c.httpPost(endpoints.users.register, UsersController.prototype.register, true);
            await c.httpPost(endpoints.users.playerCode, UsersController.prototype.setPlayerCode, true);
            await c.httpGet(endpoints.users.playerCode, UsersController.prototype.getPlayerCode, true);
            await c.httpPost(endpoints.users.login, UsersController.prototype.login, false);
            await c.httpPut(endpoints.users.changePassword, UsersController.prototype.changePassword, true);
            await c.httpPut(endpoints.users.saveAttribute, UsersController.prototype.saveAttribute, true);
            await c.httpGet(endpoints.users.refresh, UsersController.prototype.refresh, false);
            await c.httpDelete(endpoints.users.logout, UsersController.prototype.logout, true);
            await c.httpPost(endpoints.users.clientAction, UsersController.prototype.saveUserClientAction, true);
            await c.httpEnd();
        } catch (e) {
            log.error(e);
        }
    }

    async activate(params: { activationHash: string }): Promise<void> {
        try {
            const data = await validateRequestParams<{ activationHash: string }>(
                { activationHash: validator.user.activationHash.required() },
                params
            );
            const response = (await UsersServiceInstance.activateUser(
                data.activationHash,
                this.request.req
            ));
            new ServerResponse(response).serverRedirect("/api");
        } catch (e) {
            this.errorResponseForGet(e as Error, UsersController.prototype, errorCodes.e11005001);
        }
    }

    async register(): Promise<void> {
        try {
            const data = await validateRequestParams<IUserRegisterRequest>(
                {
                    email: validator.user.email.required(),
                    password: validator.user.newPassword.required(),
                    verifyPassword: validator.user.newPassword
                        .required()
                        .oneOf([ref("password"), null], "Passwords must match"),
                    firstName: validator.user.firstName,
                    lastName: validator.user.lastName,
                    company: validator.user.company,
                    companyPosition: validator.user.companyPosition
                },
                this.request.req.body
            );

            const response = await UsersServiceInstance.register(data, this.request.req);
            delete (response as IUserRegisterResponse)?.accessToken;
            delete (response as IUserRegisterResponse)?.refreshToken;

            this.successResponse(
                response,
                EnumServerResponseType.successObject,
                UsersController.prototype,
                201
            ).serverResponse();
        } catch (e) {
            this.errorResponseForPost(e as Error, UsersController.prototype, errorCodes.e11001001);
        }
    }

    async setPlayerCode(): Promise<void> {
        try {
            const data = await validateRequestParams<{ playerCode: string }>(
                {
                    playerCode: validator.user.playerCode.required()
                },
                this.request.req.body
            );
            const response = await UsersServiceInstance.setPlayerCode(data.playerCode, this.request.req);
            this.successResponse(
                response,
                EnumServerResponseType.successObject,
                UsersController.prototype,
                201
            ).serverResponse();
        } catch (e) {
            this.errorResponseForPost(e as Error, UsersController.prototype, errorCodes.e11007001);
        }
    }

    async getPlayerCode(): Promise<void> {
        try {
            const response = await UsersServiceInstance.getPlayerCode(this.request.req);
            this.successResponse(
                response,
                EnumServerResponseType.successText,
                UsersController.prototype
            ).serverResponse();
        } catch (e) {
            this.errorResponseForGet(e as Error, UsersController.prototype, errorCodes.e11008001);
        }
    }

    async refresh(): Promise<void> {
        try {
            const { refreshToken } = this.request.req.cookies;

            const response = await UsersServiceInstance.refreshUserToken(refreshToken, this.request.req);

            if (response === false) {
                const user = (await UsersServiceInstance.getAuthorisedUser(this.request.req)) as IUserInfo;
                if (user && refreshToken) {
                    await TokenServiceInstance.deleteToken(user.id, refreshToken);
                }
                ServerCookies.deleteRefreshToken(this.request.req, this.request.res);
                ServerCookies.deleteSessionId(this.request.req, this.request.res);

                this.errorResponse(
                    new Unauthorized(errorCodes.e11006002),
                    EnumServerResponseType.successObject,
                    errorCodes.e11005001
                );
            } else {
                ServerCookies.setRefreshToken(
                    this.request.req,
                    this.request.res,
                    String((response as IUserRefreshTokensResponse).refreshToken)
                );
                this.successResponse(
                    response,
                    EnumServerResponseType.successObject,
                    UsersController.prototype
                ).serverResponse();
            }
        } catch (e) {
            this.errorResponseForPut(e as Error, UsersController.prototype, errorCodes.e11006001);
        }
    }

    async login(): Promise<void> {
        try {
            const data = await validateRequestParams<IUserLoginRequest>(
                {
                    login: validator.user.login.required(),
                    password: validator.user.currentPassword.required()
                },
                this.request.req.body
            );

            this.successResponse(
                await UsersServiceInstance.login(data, this.request.req),
                EnumServerResponseType.successObject,
                UsersController.prototype
            )
                .onSuccess((res) => {
                    ServerCookies.setRefreshToken(
                        this.request.req,
                        this.request.res,
                        String((res.data as IUserLoginResponse).refreshToken)
                    );
                })
                .serverResponse();
        } catch (e) {
            this.errorResponseForPost(e as Error, UsersController.prototype, errorCodes.e11002001);
        }
    }

    async changePassword(): Promise<void> {
        try {
            const data = await validateRequestParams<IUserChangePasswordRequest>(
                {
                    currentPassword: validator.user.currentPassword.required(),
                    newPassword: validator.user.newPassword.required(),
                    repeatPassword: validator.user.newPassword.required()
                },
                this.request.req.body
            );
            this.successResponse(
                await UsersServiceInstance.changePassword(data, this.request),
                EnumServerResponseType.successObject,
                UsersController.prototype
            ).serverResponse();
        } catch (e) {
            this.errorResponseForPut(e as Error, UsersController.prototype, errorCodes.e11009001);
        }
    }

    async saveAttribute(): Promise<void> {
        try {
            const data = await validateRequestParams<IUserAttrsRequest>(
                {
                    firstEntranceAgreement: validator.user.firstEntranceAgreement.required()
                },
                this.request.req.body
            );
            this.successResponse(
                await UsersServiceInstance.saveAttribute(data, this.request),
                EnumServerResponseType.successObject,
                UsersController.prototype
            ).serverResponse();
        } catch (e) {
            this.errorResponseForPut(e as Error, UsersController.prototype, errorCodes.e11010001);
        }
    }

    async logout(): Promise<void> {
        try {
            const { refreshToken } = this.request.req.cookies;
            ServerCookies.deleteRefreshToken(this.request.req, this.request.res);
            ServerCookies.deleteSessionId(this.request.req, this.request.res);
            this.successResponse(
                await UsersServiceInstance.logout(refreshToken, this.request.req),
                EnumServerResponseType.successObject,
                UsersController.prototype
            ).serverResponse();
        } catch (e) {
            this.errorResponseForDelete(e as Error, UsersController.prototype, errorCodes.e11003001);
        }
    }

    async saveUserClientAction(): Promise<void> {
        try {
            const reqBody = this.request.req.body;
            const data = await validateRequestParams<IUserClientActionParams>(
                {
                    action: validator.clientAction.action.required(),
                    fromPageUrl: validator.clientAction.fromPageUrl.required(),
                    fromArea: validator.clientAction.fromArea,
                    params: validator.clientAction.params.required()
                },
                reqBody
            );
            data.params = reqBody.params || {};
            this.successResponse(
                await UsersServiceInstance.registerUserClientAction(data, this.request.req),
                EnumServerResponseType.successObject,
                UsersController.prototype
            ).serverResponse();
        } catch (e) {
            this.errorResponseForGet(e as Error, UsersController.prototype, errorCodes.e11011001);
        }
    }

    async getUsers(): Promise<void> {
        try {
        } catch (e) {
            this.errorResponseForGet(e as Error, UsersController.prototype, errorCodes.e11004001);
        }
    }
}
