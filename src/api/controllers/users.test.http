### Send GET request to refresh
GET http://localhost:3044/api/users/refresh
Content-Type: application/json
Authorization: Bearer {{accessToken}}

### Send DELETE request to login
DELETE http://localhost:3044/api/users/logout
Content-Type: application/x-www-form-urlencoded

### Send POST request to login
POST http://localhost:3044/api/users/login
Content-Type: application/x-www-form-urlencoded

login=<EMAIL>&password=SkywindPartners2021

> {% client.global.set("accessToken", response.body.data.accessToken); %}

### Send GET request to activate user
GET http://localhost:3044/api/users/activate/2149ffcf-003f-434f-8451-c420a3b2a539
Accept: application/json

### Send POST request to register user
POST http://localhost:3044/api/users/register
Content-Type: application/x-www-form-urlencoded

email=<EMAIL>&password=SkywindPartners2021&verifyPassword=SkywindPartners2021

### Send PUT request to save user attributes
PUT http://localhost:3044/api/users/save-attribute
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer {{accessToken}}

firstEntranceAgreement=true

### Send POST request to save user attributes
POST http://localhost:3044/api/users/client-action
Content-Type: application/json
Authorization: Bearer {{accessToken}}

{
  "action": "page-roadmap",
  "fromPageUrl":"/en-US/news",
  "params": {"ss": 1},
  "fromArea": "mainToolbar"
}

###
