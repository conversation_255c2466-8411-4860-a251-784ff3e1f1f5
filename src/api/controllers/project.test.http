### Send POST request to login
POST http://localhost:3044/api/users/login
Content-Type: application/x-www-form-urlencoded

login=<EMAIL>&password=SkywindPartners2021

> {% client.global.set("accessToken", response.body.data.accessToken); %}

### Send GET request
GET http://localhost:3044/api/project/website-footer
Accept: application/json
Authorization: Bearer {{accessToken}}

### Send GET request
GET http://localhost:3044/api/project/news-list
Accept: application/json
Authorization: Bearer {{accessToken}}

### Send GET request
GET http://localhost:3044/api/games/available
Accept: application/json
Authorization: Bearer {{accessToken}}

### Send GET request
GET http://localhost:3044/api/project/gitbook/space/url?space=skywind-seamless-api-documentation
Accept: application/json
Authorization: Bearer {{accessToken}}