import { EnumUserClientAction, EnumUserClientArea } from "models/enum/user";

export interface IUserClientActionParams {
    action: EnumUserClientAction;
    fromPageUrl: string;
    fromArea?: EnumUserClientArea | string;
    params: object | { [key: string]: any };
}

export interface IUsersController {
    setPlayerCode(): Promise<void>;

    register(): Promise<void>;

    activate(params: { activationHash: string }): Promise<void>;

    login(): Promise<void>;

    logout(): Promise<void>;

    getUsers(): Promise<void>;

    getPlayerCode(): Promise<void>;

    refresh(): Promise<void>;

    saveUserClientAction(params: IUserClientActionParams): Promise<void>;
}
