import { EnumServerResponseType } from "utils/serverResponse";
import { IResponseSuccessArray } from "api/interfaces/response";

export interface IHttpResponseError {
    code: number;
    level: number;
    message: string;
    responseCode: number;
    status: string;
    type: EnumServerResponseType;
    params?: object;
}

export interface IHttpResponseSuccess {
    code: number;
    data: unknown;
    type: EnumServerResponseType;
    message: string;
    responseCode: number;
    status: string;
    params: IResponseSuccessArray;
}
