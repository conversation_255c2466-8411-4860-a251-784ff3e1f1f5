import { ValidationError } from "yup";
import * as Errors from "utils/errors";
import { error400 } from "utils/errors";
import { EnumServerResponseType, ResponseError, ResponseSuccess, ServerResponse } from "utils/serverResponse";
import { ServerRequest } from "utils/serverRequest";
import { IErrorInputValidation } from "api/interfaces/response";
import appServerConfig from "appServerConfig";

export class ServerController {
    public request: ServerRequest;

    constructor(serverRequest: ServerRequest) {
        this.request = serverRequest;
    }

    errorResponse(err: Error, methodName: unknown, methodErrorCode: number): void {
        const methodNameCode = Object.getOwnPropertyNames(methodName)[1];
        const sr = this.request;

        if (err instanceof ValidationError) {
            sr.log.warn(err, "Validation error at: " + methodNameCode + " " + sr.req.method + " " + sr.req.url);
            new Errors.BadRequest(
                error400(
                    methodErrorCode,
                    JSON.stringify({
                        method: methodNameCode,
                        path: sr.req.url,
                        errors: err.errors,
                        message: err.message,
                        param: err.path,
                    } as IErrorInputValidation) as never,
                    EnumServerResponseType.inputValidationError
                )
            ).response(sr?.res);
        } else if (err instanceof ResponseError) {
            sr.log.warn(err, "Response error at: " + methodNameCode + " " + sr.req.method + " " + sr.req.url);
            err.response(sr?.res);
        } else {
            sr.log.error(err, "Runtime error at: " + methodNameCode + " " + sr.req.method + " " + sr.req.url);
            new Errors.BadRequest(error400(methodErrorCode, err.message as never)).response(sr.res);
        }
    }

    successResponse<T>(response: T, type: EnumServerResponseType, methodName: unknown, status = 200): ServerResponse {
        const methodNameCode = Object.getOwnPropertyNames(methodName)[1];
        const sr = this.request;
        const serverResponse = new ServerResponse(new ResponseSuccess(type, response, status), this.request);

        if (serverResponse.isSuccess() && appServerConfig.logging.logSuccessResponse) {
            sr.log.info("Successfully executed " + methodNameCode + " " + sr.req.method + " " + sr.req.url);
        }
        return serverResponse;
    }

    errorResponseForGet(err: Error, methodName: unknown, methodErrorCode: number): void {
        this.errorResponse(err, methodName, methodErrorCode);
    }

    errorResponseForPost(err: Error, methodName: unknown, methodErrorCode: number): void {
        this.errorResponse(err, methodName, methodErrorCode);
    }

    errorResponseForPut(err: Error, methodName: unknown, methodErrorCode: number): void {
        this.errorResponse(err, methodName, methodErrorCode);
    }

    errorResponseForDelete(err: Error, methodName: unknown, methodErrorCode: number): void {
        this.errorResponse(err, methodName, methodErrorCode);
    }
}
