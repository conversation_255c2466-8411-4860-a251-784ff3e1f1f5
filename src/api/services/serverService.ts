import { NextApiRequest } from "next";
import { logging } from "utils/logging/logging";
import { ValidationError } from "yup";
import { IUserActivityParams } from "./interfaces/iUsersService";
import { ResponseError } from "utils/serverResponse";
import { InternalServerError } from "utils/errors";
import { IServerService } from "./interfaces/iServerService";
import { UserActivityCreationAttributes } from "../data-providers/db/mysql/sequelize/userActivity";

export default class ServerService implements IServerService {

    public async saveFailedActivity(err: unknown, activityAttrs: UserActivityCreationAttributes): Promise<UserActivityCreationAttributes> {
        activityAttrs.params = {
            ...activityAttrs.params,
            ...((err as Error)?.message && { errorMessage: (err as Error).message }),
            ...((err as Error)?.stack && { error: (err as Error).stack })
        } as IUserActivityParams;
        return activityAttrs;
    }

    public async handleServiceError(
        req: NextApiRequest,
        log: logging.appLogger,
        err: unknown,
        methodName: string,
        activityAttrs: UserActivityCreationAttributes | null
    ): Promise<ResponseError> {

        if(activityAttrs) {
            this.saveFailedActivity(err, activityAttrs);
        }

        if (err instanceof ValidationError) {
            log.warn(err, "Validation error at: " + methodName);
            return new ResponseError(400, 0, String((err as Error)?.message));
        } else if (err instanceof ResponseError) {
            return err;
        } else {
            log.error(err, "Error at service: " + methodName);
            return new InternalServerError();
        }
    }
}