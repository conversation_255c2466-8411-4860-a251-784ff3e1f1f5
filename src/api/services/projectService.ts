import { IProjectHealthResponse, IProjectService } from "./interfaces/iProjectService";
import logger from "utils/logger";
import { IUserActivityParams } from "./interfaces/iUsersService";
import ServerService from "api/services/serverService";
import { EnumActivityStatus, EnumUserAction } from "models/enum/user";
import { ResponseError } from "utils/serverResponse";
import * as Errors from "utils/errors";
import { errorCodes } from "errorCodes";
import { NextApiRequest } from "next";
import appConfig from "appConfig";
import moment from "moment";
import { EnumLanguageCode, EnumRegionCode } from "models/enum/system";
import { INewsExtended, IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { Project } from "utils/project";
import { SanityProviderInstance } from "api/data-providers/sanity/sanityProvider";
import { DBProviderInstance } from "api/data-providers/db/dbProvider";
import { UserActivityCreationAttributes } from "api/data-providers/db/mysql/sequelize/userActivity";
import appServerConfig from "appServerConfig";

const log = logger("service--project");

let getNewsListCache: INewsExtended[];
let getWebsiteFooterCache: { [key: string]: IWebsiteFooter } = {};
const cacheTTL: { [key: string]: number } = { newsList: 0 };

export class ProjectService extends ServerService implements IProjectService {

    constructor() {
        super();
    }

    async saveFailedActivity(err: unknown,
                             activityAttrs: UserActivityCreationAttributes): Promise<UserActivityCreationAttributes> {
        const attrs = await super.saveFailedActivity(err, activityAttrs);
        if (DBProviderInstance) {
            await DBProviderInstance.activityFailed(attrs, undefined, EnumActivityStatus.serverError);
        }

        return attrs;
    }

    public async checkHealth(): Promise<IProjectHealthResponse | void> {
        try {
            const sanity = SanityProviderInstance;
            log.info("api health");
            const sanityClientConfig = sanity.getSanityClient()?.clientConfig;
            return {
                version: Project.getVersion(),
                db: {
                    version: await (await DBProviderInstance.getConnection()).databaseVersion(),
                    host: appServerConfig.db.mysql.write.host.substring(0, 10)
                },
                server: {
                    environment: appServerConfig.environment,
                    server: appServerConfig.serverEnv,
                    dateTime: moment().toISOString(true),
                    sanity: { apiHost: sanityClientConfig?.apiHost, apiVersion: sanityClientConfig?.apiVersion }
                },
                smtp: {
                    host: appServerConfig.smtp.host.substring(0, 8)
                }
            };
        } catch (e) {
            log.error(e, "Error at ProjectService:checkHealth.");
            return Promise.reject(e);
        }
    }

    public async getNewsList(req: NextApiRequest, params: {}): Promise<ResponseError | INewsExtended[]> {

        let activityAttrs = await DBProviderInstance.activityPrepare(
            EnumUserAction.getNewsList,
            { params: {} } as IUserActivityParams,
            req
        );
        try {
            // const user = (await this.usersService.getAuthorisedUser(req)) as IUserInfo;
            // if (!user) {
            //     const error = new Errors.BadRequest(errorCodes.e30001001);
            //     await DBProviderInstance.activityFailed(activityAttrs, error);
            //     return Promise.reject(error);
            // }
            // activityAttrs = DBProviderInstance.activitySetUser(activityAttrs, user.id);

            const languageCode = appConfig.systemSettings.defaultLanguageCode;
            const regionCode = appConfig.systemSettings.defaultRegionCode;

            const nowTimestamp = +new Date();
            if (/*appConfig.environment !== EnumEnvironment.production || */nowTimestamp - cacheTTL.newsList > 120 * 1000) {
                cacheTTL.newsList = nowTimestamp;
                getNewsListCache = await SanityProviderInstance.getNews(regionCode, languageCode);
            }

            await DBProviderInstance.activitySuccess(activityAttrs);

            return getNewsListCache;
        } catch (e) {
            return Promise.reject(
                await this.handleServiceError(req, log, e, "ProjectService.getNewsList", activityAttrs)
            );
        }
    }

    public async getNewsArticle(req: NextApiRequest, newsId: string): Promise<ResponseError | INewsExtended> {
        let activityAttrs = await DBProviderInstance.activityPrepare(
            EnumUserAction.getNewsInfo,
            { params: { newsId } } as IUserActivityParams,
            req
        );
        try {
            const languageCode = appConfig.systemSettings.defaultLanguageCode;
            // const user = (await this.usersService.getAuthorisedUser(req)) as IUserInfo;
            // if (!user) {
            //     const error = new Errors.BadRequest(errorCodes.e30003001);
            //     await DBProviderInstance.activityFailed(activityAttrs, error);
            //     return Promise.reject(error);
            // }
            // activityAttrs = DBProviderInstance.activitySetUser(activityAttrs, user.id);

            const newsInfo = await SanityProviderInstance.getNews(EnumRegionCode.eu, languageCode, newsId);
            if (!Array.isArray(newsInfo) || newsInfo.length !== 1) {
                const error = new Errors.NotFound(errorCodes.e30003002);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }

            await DBProviderInstance.activitySuccess(activityAttrs);

            return newsInfo[0];
        } catch (e) {
            return Promise.reject(
                await this.handleServiceError(req, log, e, "ProjectService.getNewsArticle", activityAttrs)
            );
        }
    }

    public async getWebsiteFooterData(
        regionCode: EnumRegionCode,
        languageCode: EnumLanguageCode
    ): Promise<IWebsiteFooter> {
        const nowTimestamp = +new Date();
        const cacheKey = "websiteFooter-" + regionCode + "-" + languageCode;
        if (!cacheTTL[cacheKey]) {
            cacheTTL[cacheKey] = 0;
        }
        if (!getWebsiteFooterCache[cacheKey]) {
            getWebsiteFooterCache[cacheKey] = {} as IWebsiteFooter;
        }
        if (/*appConfig.environment !== EnumEnvironment.production || */ nowTimestamp - cacheTTL[cacheKey] > 120 * 1000) {
            cacheTTL[cacheKey] = nowTimestamp;

            getWebsiteFooterCache[cacheKey] = await SanityProviderInstance.getWebsiteConfig(regionCode, languageCode);
        }

        return getWebsiteFooterCache[cacheKey];
    }

    public async getWebsiteFooter(req: NextApiRequest, params: {}): Promise<ResponseError | IWebsiteFooter> {
        let activityAttrs = await DBProviderInstance.activityPrepare(
            EnumUserAction.getWebsiteFooter,
            { params: {} } as IUserActivityParams,
            req
        );
        try {
            // const user = (await this.usersService.getAuthorisedUser(req)) as IUserInfo;
            // if (!user) {
            //     const error = new Errors.BadRequest(errorCodes.e30002001);
            //     return Promise.reject(error);
            // }
            const languageCode = appConfig.systemSettings.defaultLanguageCode;
            const regionCode = appConfig.systemSettings.defaultRegionCode;
            return this.getWebsiteFooterData(regionCode, languageCode);
        } catch (e) {
            return Promise.reject(
                await this.handleServiceError(req, log, e, "ProjectService.getWebsiteFooter", activityAttrs)
            );
        }
    }
}

export const ProjectServiceInstance = new ProjectService();