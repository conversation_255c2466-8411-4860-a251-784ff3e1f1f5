import {
    IUserActivityParams,
    IUserAttrsRequest,
    IUserChangePasswordRequest,
    IUserInfo,
    IUserLoginRequest,
    IUserLoginResponse,
    IUserPlayerCodeResponse,
    IUserRefreshTokensInfo,
    IUserRefreshTokensResponse,
    IUserRegisterRequest,
    IUserRegisterResponse,
    IUsersService
} from "./interfaces/iUsersService";
import logger from "utils/logger";
import * as Errors from "utils/errors";
import { errorCodes } from "errorCodes";
import { hashids } from "utils/hash";
import moment from "moment";
import { EnumDBUserGroupCodes } from "models/enum/db";
import { UserDto } from "models/userDto";
import { NextApiRequest } from "next";
import { EnumActivityStatus, EnumUserAction } from "models/enum/user";
import { TokenService, TokenServiceInstance } from "api/services/tokenService";
import { ServerRequest } from "utils/serverRequest";
import { IGSLoginResponse } from "api/services/interfaces/iGameServerService";
import { ResponseError } from "utils/serverResponse";
import { IResponseError } from "api/interfaces/response";
import ServerService from "api/services/serverService";
import { validateRequestParams, validator } from "models/validatorSchemas";
import { IUserClientActionParams } from "api/controllers/interfaces/iUsersController";
import { ServerCookies } from "utils/serverCookies";
import { Users } from "../data-providers/db/mysql/sequelize";
import { UserActivityCreationAttributes } from "../data-providers/db/mysql/sequelize/userActivity";
import axios, { AxiosError } from "axios";
import { Tournaments } from "utils/tournamets";
import { GameServerServiceInstance } from "api/services/gameServerService";
import { DBProviderInstance } from "api/data-providers/db/dbProvider";

const bcrypt = require("bcrypt");
const log = logger("service--users");
const uuid = require("uuid");

export class UsersService extends ServerService implements IUsersService {

    constructor() {
        super();
    }

    public static async passwordHash(password: string): Promise<string> {
        try {
            return (await bcrypt.hash(password, 12)).replace("$2b$", "$2y$");
        } catch (e) {
            log.error(e, "Error at UsersService:passwordHash.");
            return Promise.reject(e);
        }
    }

    public static async passwordCompare(dbPassword: string, userPassword: string): Promise<boolean> {
        try {
            return await bcrypt.compare(userPassword, dbPassword.replace("$2y$", "$2b$"));
        } catch (e) {
            log.error(e, "Error at UsersService:passwordCompare.");
            return Promise.reject(e);
        }
    }

    public static activationHash(): string {
        return uuid.v4();
    }

    async saveFailedActivity(err: unknown,
                             activityAttrs: UserActivityCreationAttributes): Promise<UserActivityCreationAttributes> {
        const attrs = await super.saveFailedActivity(err, activityAttrs);
        if (DBProviderInstance) {
            await DBProviderInstance.activityFailed(attrs, undefined, EnumActivityStatus.serverError);
        }

        return attrs;
    }

    public async register(
        params: IUserRegisterRequest,
        req: NextApiRequest
    ): Promise<IResponseError | IUserRegisterResponse> {

        let activityAttrs = await DBProviderInstance.activityPrepare(
            EnumUserAction.userRegistration,
            { params: { email: params.email } } as IUserActivityParams,
            req
        );
        try {
            if (await DBProviderInstance.findUserByEmail(params.email)) {
                const error = new Errors.BadRequest(errorCodes.e10001001);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            if (await DBProviderInstance.findUserByLogin(params.email)) {
                const error = new Errors.BadRequest(errorCodes.e10001002);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }

            params.password = await UsersService.passwordHash(params.password);

            const user = await DBProviderInstance.createUserAndAssignToGroups(params, [
                EnumDBUserGroupCodes.partnerUser,
                EnumDBUserGroupCodes.siteReader
            ]);

            const activationHash = UsersService.activationHash();
            await DBProviderInstance.createUserActivationHash(user.id, activationHash);

            // await MailServiceInstance.sendActivationEmail(
            //     user,
            //     appConfig.client.endpoints.api.path + appConfig.client.endpoints.api.users.activate + "/" + activationHash
            // );

            const userDto = new UserDto(user);
            const tokens = await TokenService.generateTokens({ ...userDto });
            await TokenServiceInstance.saveToken(user.id, tokens.refreshToken, req);

            await DBProviderInstance.activitySetUser(activityAttrs, user.id);
            await DBProviderInstance.activitySuccess(activityAttrs);

            return {
                ...(await DBProviderInstance.mapUserInfo(user)),
                ...tokens,
                id: hashids.encode(user.id),
                activationHash
            };
        } catch (e) {
            return Promise.reject(await this.handleServiceError(req, log, e, "UsersService.register", activityAttrs));
        }
    }

    public async getAuthorisedUser(
        req: NextApiRequest,
        options: { includePassword: boolean } = { includePassword: false }
    ): Promise<IUserInfo | null | IResponseError> {

        let activityAttrs = await DBProviderInstance.activityPrepare(
            EnumUserAction.userGetAuthorised,
            { params: {} } as IUserActivityParams,
            req
        );
        try {
            const accessToken = ServerRequest.getAccessToken(req);
            if (accessToken) {
                const userDto = UserDto.decode(<string>accessToken);
                if (userDto) {
                    const user = await DBProviderInstance.findUserByEmail(userDto.email);
                    if (user && user.is_active === 1) {
                        return DBProviderInstance.mapUserInfo(user, options.includePassword);
                    } else {
                        const error = new Errors.BadRequest(errorCodes.e10007002);
                        activityAttrs.params = {
                            ...activityAttrs.params,
                            userId: user?.id,
                            userIsActive: user?.is_active
                        };
                        await DBProviderInstance.activityFailed(activityAttrs, error);
                        return Promise.reject(error);
                    }
                } else {
                    const error = new Errors.BadRequest(errorCodes.e10007001);
                    activityAttrs.params = {
                        ...activityAttrs.params,
                        accessToken,
                        userDto
                    };
                    await DBProviderInstance.activityFailed(activityAttrs, error);
                    return Promise.reject(error);
                }
            }
            return null;
        } catch (e) {
            return Promise.reject(
                await this.handleServiceError(req, log, e, "UsersService.getAuthorisedUser", activityAttrs)
            );
        }
    }

    public async refreshUserToken(
        refreshToken: string | undefined,
        req: NextApiRequest
    ): Promise<IUserRefreshTokensResponse | boolean> {

        let activityAttrs = await DBProviderInstance.activityPrepare(
            EnumUserAction.userRefreshToken,
            { params: { refreshToken, headersAuth: req.headers?.authorization } } as IUserActivityParams,
            req
        );
        try {
            const user = (await this.getAuthorisedUser(req)) as IUserInfo;
            let token: boolean | IUserRefreshTokensInfo = false;
            if (!user) {
                const error = new Errors.BadRequest(errorCodes.e10010001);
                activityAttrs.params = {
                    ...activityAttrs.params,
                    accessToken: ServerRequest.getAccessToken(req),

                    userId: null
                };
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return false;
            }
            if (refreshToken) {
                const isValidToken = await TokenService.validateRefreshToken(refreshToken);
                if (!isValidToken) {
                    activityAttrs.params = {
                        ...activityAttrs.params,
                        isValidToken
                    };
                    await DBProviderInstance.activityFailed(activityAttrs);
                    return false;
                }
                const curUserDto = UserDto.decode(refreshToken);
                if (!curUserDto) {
                    activityAttrs.params = {
                        ...activityAttrs.params,
                        refreshToken,
                        curUserDto
                    };
                    await DBProviderInstance.activityFailed(activityAttrs);
                    return false;
                }
                token = (await DBProviderInstance.refreshUserToken(user.id,
                    refreshToken,
                    req)) as IUserRefreshTokensInfo;

                if (token) {
                    await DBProviderInstance.activitySuccess(activityAttrs);
                } else {
                    activityAttrs.params = {
                        ...activityAttrs.params,
                        refreshToken,
                        token
                    };
                    await DBProviderInstance.activityFailed(activityAttrs);
                }
            }

            return token !== false
                   ? {
                    ...(token as IUserInfo),
                    ...{ id: hashids.encode((token as IUserInfo).id) }
                }
                   : false;
        } catch (e) {
            return Promise.reject(
                await this.handleServiceError(req, log, e, "UsersService.refreshUserToken", activityAttrs)
            );
        }
    }

    public async activateUser(activationHash: string, req: NextApiRequest): Promise<ResponseError | boolean> {

        let activityAttrs = await DBProviderInstance.activityPrepare(
            EnumUserAction.userActivation,
            { params: {} } as IUserActivityParams,
            req
        );
        try {
            const userActivation = await DBProviderInstance.findUserActivationHash(activationHash);
            if (!userActivation) {
                const error = new Errors.NotFound(errorCodes.e10003001);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            await DBProviderInstance.activitySetUser(activityAttrs, userActivation.user_id);
            const userId = await DBProviderInstance.activateUser(userActivation);

            activityAttrs.params = {
                ...activityAttrs.params,
                activationHash,
                userId
            };
            await DBProviderInstance.activitySuccess(activityAttrs);

            return typeof userId === "number";
        } catch (e) {
            return Promise.reject(
                await this.handleServiceError(req, log, e, "UsersService.activateUser", activityAttrs)
            );
        }
    }

    public async login(params: IUserLoginRequest, req: NextApiRequest): Promise<void | IUserLoginResponse> {

        let activityAttrs = await DBProviderInstance.activityPrepare(
            EnumUserAction.userLogin,
            { params: { login: params.login } } as IUserActivityParams,
            req
        );
        let error: ResponseError | null = null;
        try {
            const user = (await DBProviderInstance.findUserByLogin(params.login)) as Users;
            if (!user) {
                error = new Errors.NotFound(errorCodes.e10002001);
            } else if (user?.is_active === 0) {
                error = new Errors.BadRequest(errorCodes.e10002002);
            }
            if (!(await UsersService.passwordCompare(String(user?.password), params.password))) {
                error = new Errors.BadRequest(errorCodes.e10002003);
            }
            if (user) {
                await DBProviderInstance.activitySetUser(activityAttrs, user.id);
            }
            if (error) {
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            const userDto = new UserDto(user);
            const tokens = await TokenService.generateTokens({ ...userDto });
            await TokenServiceInstance.saveToken(user.id, tokens.refreshToken, req);

            await DBProviderInstance.login(user.id);

            await DBProviderInstance.activitySuccess(activityAttrs);

            return {
                ...(await DBProviderInstance.mapUserInfo(user)),
                ...tokens,
                id: hashids.encode(user.id)
            } as IUserLoginResponse;
        } catch (e) {
            return Promise.reject(await this.handleServiceError(req, log, e, "UsersService.login", activityAttrs));
        }
    }

    public async setPlayerCode(
        playerCode: string,
        req: NextApiRequest
    ): Promise<IResponseError | IUserPlayerCodeResponse> {

        let activityAttrs = await DBProviderInstance.activityPrepare(
            EnumUserAction.userSetPlayerCode,
            { params: { playerCode } } as IUserActivityParams,
            req
        );
        try {
            const user = (await this.getAuthorisedUser(req)) as IUserInfo;
            if (!user) {
                const error = new Errors.BadRequest(errorCodes.e10008001);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            await DBProviderInstance.activitySetUser(activityAttrs, user.id);
            const tournament = Tournaments.getCurrentlyUsed({ filter: { isActive: true } });
            if (tournament.length !== 1) {
                const error = new Errors.BadRequest(errorCodes.e10008007);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            const gameServer = await DBProviderInstance.findGameServer(tournament[0].gameServerSecretKey);
            if (!gameServer) {
                const error = new Errors.BadRequest(errorCodes.e10008008);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            const userPlayerCode = await DBProviderInstance.findUserPlayerCode(user?.id as number, gameServer.id);
            if (userPlayerCode) {
                const error = new Errors.BadRequest(errorCodes.e10008002);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            const loginResponse = await GameServerServiceInstance.login();
            if (axios.isAxiosError(loginResponse) || !(loginResponse as IGSLoginResponse).accessToken) {
                const error = new Errors.BadRequest(errorCodes.e10008003);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            const playerCodeResponse = await GameServerServiceInstance.registerPlayer(playerCode);
            if (axios.isAxiosError(playerCodeResponse)) {
                const error = playerCodeResponse as AxiosError;
                if (error.response?.status === 400) {
                    const error = new Errors.BadRequest(errorCodes.e10008004);
                    await DBProviderInstance.activityFailed(activityAttrs, error);
                    return Promise.reject(error);
                } else if (error.response?.status === 409) {
                    const error = new Errors.BadRequest(errorCodes.e10008005);
                    await DBProviderInstance.activityFailed(activityAttrs, error);
                    return Promise.reject(error);
                }
            }
            const playerTransfer = await GameServerServiceInstance.setPlayerTransferIn(playerCode, "USD", 1000);
            if (axios.isAxiosError(playerTransfer)) {
                const error = new Errors.BadRequest(errorCodes.e10008006);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            await DBProviderInstance.createUserPlayerCode(user?.id as number, playerCode, gameServer.id);

            await DBProviderInstance.activitySuccess(activityAttrs);

            const isValid = await validator.user.playerCode.required().isValid(playerCode);

            return {
                playerCode,
                isValid
            };
        } catch (e) {
            return Promise.reject(
                await this.handleServiceError(req, log, e, "UsersService.setPlayerCode", activityAttrs)
            );
        }
    }

    public async getPlayerCode(req: NextApiRequest): Promise<IResponseError | IUserPlayerCodeResponse> {

        let activityAttrs = await DBProviderInstance.activityPrepare(
            EnumUserAction.userGetPlayerCode,
            { params: {} } as IUserActivityParams,
            req
        );
        try {
            const user = (await this.getAuthorisedUser(req)) as IUserInfo;
            if (!user) {
                const error = new Errors.BadRequest(errorCodes.e10009001);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            const tournament = Tournaments.getCurrentlyUsed({ filter: { isActive: true } });
            if (tournament.length !== 1) {
                const error = new Errors.BadRequest(errorCodes.e10009002);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            await DBProviderInstance.activitySetUser(activityAttrs, user.id);
            const gameServer = await DBProviderInstance.findGameServer(tournament[0].gameServerSecretKey);
            if (!gameServer) {
                const error = new Errors.BadRequest(errorCodes.e10009003);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            const userPlayerCode = await DBProviderInstance.findUserPlayerCode(
                user?.id as number,
                gameServer?.id as number
            );

            activityAttrs.params = {
                ...activityAttrs.params,
                playerCode: userPlayerCode
            };
            await DBProviderInstance.activitySuccess(activityAttrs);

            const isValid = await validator.user.playerCode.required().isValid(userPlayerCode?.player_code);

            return { playerCode: userPlayerCode?.player_code || null, isValid };
        } catch (e) {
            return Promise.reject(
                await this.handleServiceError(req, log, e, "UsersService.getPlayerCode", activityAttrs)
            );
        }
    }

    public async changePassword(
        params: IUserChangePasswordRequest,
        request: ServerRequest
    ): Promise<null | IUserInfo | IResponseError> {
        const req = request.req;

        let activityAttrs = await DBProviderInstance.activityPrepare(
            EnumUserAction.userChangePassword,
            { params: {} } as IUserActivityParams,
            req
        );
        try {
            const user = (await this.getAuthorisedUser(req, { includePassword: true })) as IUserInfo;
            if (!user) {
                const error = new Errors.BadRequest(errorCodes.e10005001);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            await DBProviderInstance.activitySetUser(activityAttrs, user.id);

            if (!(await UsersService.passwordCompare(user?.password as string, params.currentPassword))) {
                const error = new Errors.BadRequest(errorCodes.e10005002);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }

            const data = await validateRequestParams<IUserChangePasswordRequest>(
                {
                    currentPassword: validator.user.currentPassword.required(),
                    newPassword: validator.user.newPassword.required(),
                    repeatPassword: validator.user.newPassword.required()
                },
                params
            );

            const password = await UsersService.passwordHash(data.newPassword);

            await DBProviderInstance.changeUserPassword(user?.id as number, password);

            await DBProviderInstance.activitySuccess(activityAttrs);

            return await this.getAuthorisedUser(req);
        } catch (e) {
            return Promise.reject(
                await this.handleServiceError(req, log, e, "UsersService.changePassword", activityAttrs)
            );
        }
    }

    public async saveAttribute(
        params: IUserAttrsRequest,
        request: ServerRequest
    ): Promise<null | IUserInfo | IResponseError> {
        const req = request.req;

        let activityAttrs = await DBProviderInstance.activityPrepare(
            EnumUserAction.userSetPlayerCode,
            { params } as IUserActivityParams,
            req
        );
        try {
            const user = (await this.getAuthorisedUser(req)) as IUserInfo;
            if (!user) {
                const error = new Errors.BadRequest(errorCodes.e10006001);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            await DBProviderInstance.activitySetUser(activityAttrs, user.id);

            await DBProviderInstance.updateUserAttr(user.id, {
                firstEntranceAgreementAt: moment().toISOString()
            });

            await DBProviderInstance.activitySuccess(activityAttrs);

            return await this.getAuthorisedUser(req);
        } catch (e) {
            return Promise.reject(
                await this.handleServiceError(req, log, e, "UsersService.saveAttribute", activityAttrs)
            );
        }
    }

    public async logout(refreshToken: string | undefined, req: NextApiRequest): Promise<IResponseError | boolean> {

        let activityAttrs = await DBProviderInstance.activityPrepare(
            EnumUserAction.userLogout,
            { params: { refreshToken } } as IUserActivityParams,
            req
        );
        try {
            const user = (await this.getAuthorisedUser(req)) as IUserInfo;
            if (!user) {
                const error = new Errors.NotFound(errorCodes.e10004001);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            // const userDtoDecoded = UserDto.decode(refreshToken) as IUserDtoDecoded;
            // const token = await DBProviderInstance.findUserRefreshToken(refreshToken as string,
            // userDtoDecoded?.id); if (!userDtoDecoded || !token) { const error = new
            // Errors.NotFound(errorCodes.e10004001); activityAttrs.params = { ...activityAttrs.params, userDtoDecoded,
            // token, }; await DBProviderInstance.activityFailed(activityAttrs, error); // return
            // Promise.reject(error); }

            await TokenServiceInstance.deleteToken(user.id);

            await DBProviderInstance.activitySuccess(activityAttrs);

            return true;
        } catch (e) {
            return Promise.reject(await this.handleServiceError(req, log, e, "UsersService.logout", activityAttrs));
        }
    }

    public async registerUserClientAction(
        params: IUserClientActionParams,
        req: NextApiRequest
    ): Promise<IResponseError | boolean> {

        const userAgentInfo = await DBProviderInstance.findOrCreateUserAgent(ServerRequest.getUserAgent(req));
        const userAgentId = userAgentInfo?.id || null;

        let activityAttrs = {
            user_action: "CA-" + params.action,
            request_path: params.fromPageUrl,
            user_ip: ServerRequest.getUserIp(req),
            user_agent_id: userAgentId as number,
            params: { ...params.params, fromArea: params.fromArea },
            session_id: ServerCookies.getSessionId(req) ?? undefined,
            game_code: (typeof params?.params === "object" && "gameCode" in params?.params && params?.params["gameCode"]) || undefined
        } as UserActivityCreationAttributes;
        try {
            const user = (await this.getAuthorisedUser(req)) as IUserInfo;
            if (!user) {
                const error = new Errors.BadRequest(errorCodes.e10011001);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            activityAttrs.user_id = user.id;
            await DBProviderInstance.activitySuccess(activityAttrs);
            return true;
        } catch (e) {
            return Promise.reject(
                await this.handleServiceError(req, log, e, "UsersService.registerUserClientAction", activityAttrs)
            );
        }
    }
}

export const UsersServiceInstance = new UsersService();