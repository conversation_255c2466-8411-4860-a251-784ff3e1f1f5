import { ITokenService, ITokensResponse } from "./interfaces/iTokenService";
import { NextApiRequest } from "next";
import ServerService from "api/services/serverService";
import { UserTokens } from "../data-providers/db/mysql/sequelize";
import { UserDto } from "models/userDto";
import logger from "utils/logger";
import { DBProviderInstance } from "api/data-providers/db/dbProvider";
import { UserActivityCreationAttributes } from "api/data-providers/db/mysql/sequelize/userActivity";
import { EnumActivityStatus } from "models/enum/user";
import appServerConfig from "appServerConfig";

const jwt = require("jsonwebtoken");
const log = logger("service--token");

export class TokenService extends ServerService implements ITokenService {

    constructor() {
        super();
    }

    public static async generateTokens(payload: string | Buffer | object): Promise<ITokensResponse> {
        try {
            const accessToken = jwt.sign(payload, appServerConfig.jwt.access.secretKey, {
                expiresIn: appServerConfig.jwt.access.expiresIn
            });
            const refreshToken = jwt.sign(payload, appServerConfig.jwt.refresh.secretKey, {
                expiresIn: appServerConfig.jwt.refresh.expiresIn
            });

            return {
                accessToken,
                refreshToken
            };
        } catch (e) {
            log.error(e, "Error at TokenService:generateTokens.");
            return Promise.reject(e);
        }
    }

    public static validateAccessToken(accessToken: string): UserDto | null {
        try {
            return jwt.verify(accessToken, appServerConfig.jwt.access.secretKey);
        } catch (e) {
            return null;
        }
    }

    public static validateRefreshToken(refreshToken: string): UserDto | null {
        try {
            return jwt.verify(refreshToken, appServerConfig.jwt.refresh.secretKey);
        } catch (e) {
            return null;
        }
    }

    async saveFailedActivity(err: unknown,
                             activityAttrs: UserActivityCreationAttributes): Promise<UserActivityCreationAttributes> {
        const attrs = await super.saveFailedActivity(err, activityAttrs);
        if (DBProviderInstance) {
            await DBProviderInstance.activityFailed(attrs, undefined, EnumActivityStatus.serverError);
        }

        return attrs;
    }

    public async saveToken(
        userId: number,
        refreshToken: string,
        req: NextApiRequest,
        login = false
    ): Promise<UserTokens> {
        try {
            return await DBProviderInstance.saveUserToken(userId, refreshToken, req, login);
        } catch (e) {
            log.error(e, "Error at TokenService:saveToken.");
            return Promise.reject(e);
        }
    }

    public async deleteToken(userId: number, refreshToken?: string): Promise<boolean> {
        try {
            // const udtUser = UserDto.decode(refreshToken) as IUserDtoDecoded;
            return await DBProviderInstance.deleteUserToken(userId, refreshToken);
        } catch (e) {
            log.error(e, "Error at TokenService:deleteToken.");
            return Promise.reject(e);
        }
    }
}

export const TokenServiceInstance = new TokenService();