import logger from "../../utils/logger";
import {
    IGameServerService,
    IGSGamePlayResponse,
    IGSLoginResponse,
    IGSPlayerTransferIn,
    IGSRegisterResponse
} from "./interfaces/iGameServerService";
import axios, { AxiosError, AxiosResponse } from "axios";

const log = logger("service--game-server");

export class GameServerService implements IGameServerService {
    public accessToken: string | null = null;

    // production
    // public static operatorDomain: string = "https://operator-eu.sw420101.com";
    // public operatorPath: string =
    // "swftest_eu_entity:PartnerArea_to_Production_entity:PartnerArea_to_Production_brand:"; public credentials = {
    // secretKey: "fb09c9af-a92a-4d6f-a8ff-ebd42d56433c", username: "PartnerArea_to_Production_brand_api_prod",
    // password: "jHbg8fs4dh5RE8c9", }; public defaultPassword = false; public useOperatorPath = false; public
    // playerPasswordPrefix = "!A"; public playerPasswordPostfix = "B0"; public playerCurrency = "USD";

    // staging
    public static operatorDomain: string = "https://operator-eu.ss211208.com";
    public operatorPath: string = "PartnerArea_to_PreProduction_entity:PartnerArea_to_PreProduction_brand";
    public credentials = {
        secretKey: "b0685b57-cedc-4385-99ef-013691e4d174",
        username: "sws_admin1",
        password: "CjnUvRnfC9pEQK8z"
    };
    public defaultPassword = "963258741Aa!";
    public useOperatorPath = true;
    public playerPasswordPrefix = "";
    public playerPasswordPostfix = "";
    public playerCurrency = "USD";

    constructor() {
    }

    public async login(username?: string, password?: string): Promise<IGSLoginResponse | AxiosError | Error> {
        try {
            const response = await axios.post<unknown, AxiosResponse<IGSLoginResponse>>(
                GameServerService.operatorDomain + "/v1/login",
                this.credentials,
                {
                    headers: {
                        "Content-Type": "application/json",
                        accept: "application/json"
                    }
                }
            );

            this.accessToken = response.data.accessToken;
            return response.data;
        } catch (e) {
            log.error(e, "Error at GameServerService:login.");
            return e as AxiosError;
        }
    }

    public async registerPlayer(playerCode: string): Promise<IGSRegisterResponse | AxiosError | Error> {
        try {
            let resp = await axios.post<unknown, AxiosResponse<IGSRegisterResponse>>(
                GameServerService.operatorDomain +
                "/v1" +
                (this.useOperatorPath ? "/entities/" + this.operatorPath : "") +
                "/players/register",
                {
                    code: playerCode,
                    password:
                        this.defaultPassword || this.playerPasswordPrefix + playerCode + this.playerPasswordPostfix
                },
                {
                    headers: {
                        "Content-Type": "application/json",
                        accept: "application/json",
                        "X-ACCESS-TOKEN": String(this.accessToken)
                    }
                }
            );
            return resp.data;
        } catch (e) {
            log.error(e, "Error at GameServerService:registerPlayer.");
            return e as Error;
        }
    }

    public async setPlayerTransferIn(
        playerCode: string,
        currency: string,
        amount: number
    ): Promise<IGSPlayerTransferIn | AxiosError | Error> {
        try {
            const response = await axios.post<unknown, AxiosResponse<IGSPlayerTransferIn>>(
                GameServerService.operatorDomain +
                "/v1" +
                (this.useOperatorPath ? "/entities/" + this.operatorPath : "") +
                "/payments/transfers/in",
                { playerCode: playerCode, currency: this.playerCurrency, amount: amount },
                {
                    headers: {
                        "Content-Type": "application/json",
                        accept: "application/json",
                        "X-ACCESS-TOKEN": String(this.accessToken)
                    }
                }
            );
            return response.data;
        } catch (e) {
            log.error(e, "Error at GameServerService:getPlayerTransferIn.");
            return e as Error;
        }
    }

    public async getGameUrl(playerCode: string, gameCode: string): Promise<IGSGamePlayResponse | AxiosError | Error> {
        try {
            const response = await axios.get<unknown, AxiosResponse<IGSGamePlayResponse>>(
                GameServerService.operatorDomain +
                "/v1" +
                (this.useOperatorPath ? "/entities/" + this.operatorPath : "") +
                "/players/" +
                playerCode +
                "/games/" +
                gameCode,
                {
                    headers: {
                        "Content-Type": "application/json",
                        accept: "application/json",
                        "X-ACCESS-TOKEN": String(this.accessToken)
                    }
                }
            );

            return response.data;
        } catch (e) {
            log.error(e, "Error at GameServerService:registerPlayer.");
            return e as Error;
        }
    }
}

export const GameServerServiceInstance = new GameServerService();