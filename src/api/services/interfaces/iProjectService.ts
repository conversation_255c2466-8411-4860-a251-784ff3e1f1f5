import { NextApiRequest } from "next";
import { ResponseError } from "utils/serverResponse";
import { EnumLanguageCode, EnumRegionCode } from "models/enum/system";
import { INewsExtended, IWebsiteFooter } from "api/data-providers/sanity/interfaces/iSanityProvider";

export interface IProjectHealthResponse {
    db: {
        version: string;
        host: string;
    };
    gameServerStatus?: {
        [name: string]: string;
    };
    version: string;
    server: {
        environment: string;
        server: string;
        dateTime: string;
        sanity: { apiHost?: string; apiVersion?: string };
    };
    smtp: {
        host: string;
    };
}

export interface ITokenConfig {
    readonly secret: string;
    readonly algorithm: string;
    readonly issuer: string;
    readonly expiresIn?: number;
}

export interface IGitBookConfigData {
    config: ITokenConfig;
    url: string;
}

export interface IGitBookAuthData {
    url: string;
}

export interface IProjectService {
    checkHealth(): Promise<IProjectHealthResponse | void>;

    getNewsList(req: NextApiRequest, params: {}): Promise<ResponseError | INewsExtended[]>;

    getNewsArticle(req: NextApiRequest, newsId: string): Promise<ResponseError | INewsExtended>;

    getWebsiteFooterData(regionCode: EnumRegionCode, languageCode: EnumLanguageCode): Promise<IWebsiteFooter>;

    getWebsiteFooter(req: NextApiRequest, params: {}): Promise<ResponseError | IWebsiteFooter>;
}
