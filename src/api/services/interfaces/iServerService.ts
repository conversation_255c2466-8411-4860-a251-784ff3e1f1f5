import { NextApiRequest } from "next";
import { UserActivityCreationAttributes } from "api/data-providers/db/mysql/sequelize/userActivity";
import { ResponseError } from "utils/serverResponse";
import { logging } from "utils/logging/logging";

export interface IServerService {
    saveFailedActivity(err: unknown,
                       activityAttrs: UserActivityCreationAttributes): Promise<UserActivityCreationAttributes>;

    handleServiceError(
        req: NextApiRequest,
        log: logging.appLogger,
        err: unknown,
        methodName: unknown,
        activityAttrs: UserActivityCreationAttributes
    ): Promise<ResponseError>;
}
