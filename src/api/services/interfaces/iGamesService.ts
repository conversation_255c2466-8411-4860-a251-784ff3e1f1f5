import { NextApiRequest } from "next";
import { ISearchData } from "@components/games-search/_interfaces";
import { IGameInfoFromBI, IGameVideosExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";
import { EnumGameRTP, EnumShowGameCode, EnumShowGameRTP } from "models/enum/game";

export interface IRTPType {
    type: EnumGameRTP;
    rtp: number | null;
}

export interface IMarketWithReleaseDate {
    [releaseDate: string]: string;
}

export interface IGamesGridRow {
    id: string;
    gameCode: string[];
    gamePoster: string;
    gameName: string;
    releaseDate: string;
    year: string;
    markets: string;
    marketsWithReleaseDate?: IMarketWithReleaseDate;
    jackpot: string;
    features: string;
    type: string;
    types: string[];
    rtp: IRTPType[];
    vol: string;
    tag: string[];
    cert: string[];
    demoUrl: string;
    videos: IGameVideosExtended[];
    productSheetUrl: string;
    infoSheetUrl: string;
    certificates: IMarketCertificate[] | null;
    languageCode: string;
    gameInfo?: IGameInfoFromBI;
    rank?: number;
    options: { showGameRTP: EnumShowGameRTP; showGameCode: EnumShowGameCode; formatReleaseDate: string; };
}

export interface IRoadmapAvailableMarkets {
    [key: string]: string;
}

export interface IRoadmapBiggestMarkets {
    [key: string]: string;
}

export interface IRoadmapMonths {
    [month: number]: number;
}

export interface IRoadmapMarkets {
    [key: string]: {
        key: string;
        text: string;
        iconUrl?: string;
    };
}

export interface IRoadmapGames {
    [marketCode: string]: { [gameCode: string]: IGamesGridRow };
}

export interface IRoadmapResponse {
    showPerMonthToolbar: boolean;
    defaultMarketCode: string;
    // months: Array<number>;
    availableMarkets: IRoadmapAvailableMarkets;
    biggestMarkets: IRoadmapBiggestMarkets;
    roadmapMonths: IRoadmapMonths;
    roadmapMarkets: IRoadmapMarkets;
    games: IRoadmapGames;
    limit: {
        latestReleasesMonth: number;
        upcomingMonthThirdParty: number;
    };
}

export interface IAvailableGamesResponse {
    availableMarkets: IRoadmapAvailableMarkets;
    games: IGamesGridRow[];
}

export interface IMarketCertificate {
    id: string;
    marketCode: string;
    marketName: string;
    fileUrl: string | null;
    translated: boolean;
    comingSoon?: boolean | null;
    iconUrl?: string;
}

export interface IMarketCertificates {
    [marketCode: string]: IMarketCertificate;
}

// export interface IGameMarketCertificates {
//     [gameCode: string]: IMarketCertificates;
// }

export interface IMarketingKitResponse {
    gameCode: string;
    rtp: string;
    shortDescriptionEN: string;
    "shortDescriptionZH-CN": string;
    titleEN: string;
    "titleZH-CN": string;
    marketingKit: { [folderName: string]: string[] };
}

export interface IGamesService {
    // getCertificates(languageCode: string): Promise<IGameMarketCertificates>;

    getAvailableGames(
        req: NextApiRequest,
        options: { skipAuthorisation?: boolean; gameInfo?: boolean }
    ): Promise<void | IAvailableGamesResponse>;

    getSearchGames(req: NextApiRequest, options: {}): Promise<void | ISearchData>;

    getRoadmapGames(req: NextApiRequest): Promise<void | IRoadmapResponse>;

    getGamePromoUrl(req: NextApiRequest): Promise<void | string>;

    getMarketingKit(req: NextApiRequest, gameId: string): Promise<void | IMarketingKitResponse>;
}
