import { NextApiRequest } from "next";
import { UserTokens } from "../../data-providers/db/mysql/sequelize";

export interface ITokensResponse {
    accessToken: string;
    refreshToken: string;
}

export interface ITokenService {
    saveToken(userId: number, refreshToken: string, req: NextApiRequest, login?: boolean): Promise<UserTokens>;

    deleteToken(userId: number, refreshToken?: string): Promise<boolean>;
}
