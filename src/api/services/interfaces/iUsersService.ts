import { NextApiRequest } from "next";
import { ServerRequest } from "utils/serverRequest";
import { IResponseError } from "api/interfaces/response";
import { IUserClientActionParams } from "api/controllers/interfaces/iUsersController";

export interface IUserRegisterRequest {
    email: string;
    password: string;
    verifyPassword: string;
    firstName?: string;
    lastName?: string;
    company?: string;
    companyPosition?: string;
}

export interface IUserActivityParams {
    params: object;
    errorMessage?: string;
    error?: string;
}

export interface IUserLoginRequest {
    login: string;
    password: string;
}

export interface IUserChangePasswordRequest {
    currentPassword: string;
    newPassword: string;
    repeatPassword: string;
}

export interface IUserAttrsRequest {
    firstEntranceAgreement?: boolean;
}

export interface IUserAttrs {
    firstEntranceAgreementAt?: string;
}

export interface IUserPlayerCodeResponse {
    playerCode: string | null;
    isValid: boolean;
}

export interface IUserLoginResponse {
    id: string | number;
    login: string;
    email: string;
    firstName?: string;
    lastName?: string;
    company?: string;
    companyPosition?: string;
    createdAt: string;
    updatedAt?: string;
    isActive: boolean;
    accessToken?: string;
    refreshToken?: string;
    playerCode?: string | null;
    passwordUpdatedAt: string;
    firstEntranceAgreementAt: string;
    userPermissions: { groups: string[]; permissions: string[] };
}

export interface IUserInfo extends IUserLoginResponse {
    id: number;
    password?: string;
}

export interface IUserRefreshTokensResponse extends IUserLoginResponse {
    id: string;
    accessToken?: string;
    refreshToken?: string;
}

export interface IUserRefreshTokensInfo {
    id: number;
}

export interface IUserRegisterResponse extends IUserLoginResponse {
    id: string;
    activationHash: string;
}

export interface IUserCreateUpdate {
    id?: string;
    email: string;
    password: string;
    firstName?: string;
    lastName?: string;
    company?: string;
    companyPosition?: string;
}

export interface IUsersService {
    register(params: IUserRegisterRequest, req: NextApiRequest): Promise<IResponseError | IUserRegisterResponse>;

    activateUser(activationHash: string, req: NextApiRequest): Promise<unknown>;

    getAuthorisedUser(
        req: NextApiRequest,
        options?: { includePassword: boolean }
    ): Promise<IUserInfo | null | IResponseError>;

    refreshUserToken(refreshToken: string, req: NextApiRequest): Promise<IUserRefreshTokensResponse | boolean>;

    login(params: IUserLoginRequest, req: NextApiRequest): Promise<void | IUserLoginResponse>;

    changePassword(
        params: IUserChangePasswordRequest,
        request: ServerRequest
    ): Promise<null | IUserInfo | IResponseError>;

    saveAttribute(params: IUserAttrsRequest, req: ServerRequest): Promise<null | IUserInfo | IResponseError>;

    logout(refreshToken: string, req: NextApiRequest): Promise<IResponseError | boolean>;

    setPlayerCode(playerCode: string, req: NextApiRequest): Promise<IResponseError | IUserPlayerCodeResponse>;

    getPlayerCode(req: NextApiRequest): Promise<IResponseError | IUserPlayerCodeResponse>;

    registerUserClientAction(params: IUserClientActionParams, req: NextApiRequest): Promise<IResponseError | boolean>;
}
