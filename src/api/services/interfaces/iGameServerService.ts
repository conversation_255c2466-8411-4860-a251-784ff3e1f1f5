import { AxiosError, AxiosResponse } from "axios";

export interface IGSLoginResponse {
    accessToken: string;
    key: string;
    username: string;
    lastPasswordUpdate: string;
}

export interface IGSGamePlayResponse {
    url: string;
    token: string;
    currency: string;
}

export interface IGSPlayerTransferIn {
    trxId: string;
    extTrxId: string;
    brandId: string;
    playerCode: string;
    orderId: string;
    orderType: string;
    orderDate: string;
    orderInfo: string;
    orderStatus: string;
    currencyCode: string;
    amount: number;
    playerBalanceAfter: number;
    startDate: string;
    endDate: string;
    paymentMethodCode: string;
    isTest: boolean;
}

export interface IGSRegisterResponse {
    code: string;
    id: string;
    status: string;
    currency: string;
    country: string;
    language: string;
    gameGroup: string;
    agentId: string;
    isTest: boolean;
    lastLogin: string;
    createdAt: string;
    updatedAt: string;
    deactivatedAt: string;
    brandId: string;
    brandTitle: string;
    nickname: string;
    isVip: boolean;
    defaultGameGroup: string;
    isBlocked: boolean;
    isPrivateChatBlock: boolean;
    isPublicChatBlock: boolean;
    isTracked: boolean;
    hasWarn: boolean;
}

export interface IGameServerService {
    login(): Promise<AxiosResponse | IGSLoginResponse | AxiosError | Error>;

    registerPlayer(playerCode: string): Promise<AxiosResponse | IGSRegisterResponse | AxiosError | Error>;

    setPlayerTransferIn(
        playerCode: string,
        currency: string,
        amount: number
    ): Promise<AxiosResponse | IGSPlayerTransferIn | AxiosError | Error>;

    getGameUrl(playerCode: string, gameCode: string): Promise<AxiosResponse | IGSGamePlayResponse | AxiosError | Error>;
}
