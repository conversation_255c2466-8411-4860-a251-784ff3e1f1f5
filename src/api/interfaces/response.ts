import { NextApiResponse } from "next";
import { EnumServerResponseType } from "utils/serverResponse";

export interface IResponseError extends Error {
    response(res: NextApiResponse): void;
}

export interface IErrorInputValidation {
    method: string;
    path: string;
    errors: string[];
    message: string;
    param: string;
}

export interface IResponseSuccessArray {
    offset: number;
    limit: number;
    total: number;
}

export interface IResponseSuccess<T = any> {
    code?: number;
    data: T;
    message: string;
    params: IResponseSuccessArray;
    responseCode: number;
    type: EnumServerResponseType;
}
