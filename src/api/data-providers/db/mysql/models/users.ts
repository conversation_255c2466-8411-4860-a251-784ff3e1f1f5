import moment from "moment";
import { IUserAttrs } from "../../../../services/interfaces/iUsersService";

export function mapUserAttrs(userAttrs?: IUserAttrs, newAttrs?: IUserAttrs): IUserAttrs {
    userAttrs = userAttrs || ({} as IUserAttrs);

    return {
        firstEntranceAgreementAt:
            newAttrs?.firstEntranceAgreementAt && moment(newAttrs?.firstEntranceAgreementAt).isValid()
                ? newAttrs?.firstEntranceAgreementAt
                : userAttrs?.firstEntranceAgreementAt && moment(userAttrs?.firstEntranceAgreementAt).isValid()
                ? userAttrs?.firstEntranceAgreementAt
                : null,
    } as IUserAttrs;
}
