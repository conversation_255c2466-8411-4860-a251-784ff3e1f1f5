import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';

export interface UsersRevisionsAttributes {
  revision_operation_type?: 'insert' | 'update' | 'delete';
  revision_number: number;
  revision_created_at: Date;
  id: number;
  login: string;
  password: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  company?: string;
  company_position?: string;
  created_at: Date;
  updated_by?: number;
  updated_at?: Date;
  is_active?: number;
  attrs?: object;
  password_updated_at?: Date;
  last_login_at?: Date;
}

export type UsersRevisionsPk = "revision_number" | "id";
export type UsersRevisionsId = UsersRevisions[UsersRevisionsPk];
export type UsersRevisionsOptionalAttributes = "revision_operation_type" | "revision_number" | "revision_created_at" | "id" | "first_name" | "last_name" | "email" | "company" | "company_position" | "created_at" | "updated_by" | "updated_at" | "is_active" | "attrs" | "password_updated_at" | "last_login_at";
export type UsersRevisionsCreationAttributes = Optional<UsersRevisionsAttributes, UsersRevisionsOptionalAttributes>;

export class UsersRevisions extends Model<UsersRevisionsAttributes, UsersRevisionsCreationAttributes> implements UsersRevisionsAttributes {
  revision_operation_type?: 'insert' | 'update' | 'delete';
  revision_number!: number;
  revision_created_at!: Date;
  id!: number;
  login!: string;
  password!: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  company?: string;
  company_position?: string;
  created_at!: Date;
  updated_by?: number;
  updated_at?: Date;
  is_active?: number;
  attrs?: object;
  password_updated_at?: Date;
  last_login_at?: Date;


  static initModel(sequelize: Sequelize.Sequelize): typeof UsersRevisions {
    return UsersRevisions.init({
    revision_operation_type: {
      type: DataTypes.ENUM('insert','update','delete'),
      allowNull: true,
      defaultValue: "insert"
    },
    revision_number: {
      autoIncrement: true,
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    revision_created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    login: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    password: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    first_name: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    last_name: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    company: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    company_position: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    updated_by: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: 0
    },
    attrs: {
      type: DataTypes.JSON,
      allowNull: true
    },
    password_updated_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    last_login_at: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'users_revisions',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
          { name: "revision_number" },
        ]
      },
      {
        name: "IDX_users_revisions_password",
        using: "BTREE",
        fields: [
          { name: "revision_number" },
        ]
      },
    ]
  });
  }
}
