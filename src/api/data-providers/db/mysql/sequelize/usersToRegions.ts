import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { Regions, RegionsId } from './regions';
import type { Users, UsersId } from './users';

export interface UsersToRegionsAttributes {
  user_id: number;
  region_id: number;
}

export type UsersToRegionsPk = "user_id" | "region_id";
export type UsersToRegionsId = UsersToRegions[UsersToRegionsPk];
export type UsersToRegionsOptionalAttributes = "user_id" | "region_id";
export type UsersToRegionsCreationAttributes = Optional<UsersToRegionsAttributes, UsersToRegionsOptionalAttributes>;

export class UsersToRegions extends Model<UsersToRegionsAttributes, UsersToRegionsCreationAttributes> implements UsersToRegionsAttributes {
  user_id!: number;
  region_id!: number;

  // UsersToRegions belongsTo Regions via region_id
  region!: Regions;
  getRegion!: Sequelize.BelongsToGetAssociationMixin<Regions>;
  setRegion!: Sequelize.BelongsToSetAssociationMixin<Regions, RegionsId>;
  createRegion!: Sequelize.BelongsToCreateAssociationMixin<Regions>;
  // UsersToRegions belongsTo Users via user_id
  user!: Users;
  getUser!: Sequelize.BelongsToGetAssociationMixin<Users>;
  setUser!: Sequelize.BelongsToSetAssociationMixin<Users, UsersId>;
  createUser!: Sequelize.BelongsToCreateAssociationMixin<Users>;

  static initModel(sequelize: Sequelize.Sequelize): typeof UsersToRegions {
    return UsersToRegions.init({
    user_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    region_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true,
      references: {
        model: 'regions',
        key: 'id'
      }
    }
  }, {
    sequelize,
    tableName: 'users_to_regions',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "user_id" },
          { name: "region_id" },
        ]
      },
      {
        name: "FK_users_to_regions_region_id",
        using: "BTREE",
        fields: [
          { name: "region_id" },
        ]
      },
    ]
  });
  }
}
