import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { UserBrowserAgents, UserBrowserAgentsId } from './userBrowserAgents';
import type { Users, UsersId } from './users';

export interface UserTokensAttributes {
  user_id: number;
  refresh_token: string;
  user_ip_address?: string;
  user_agent_id?: number;
  last_login?: Date;
  updated_at: Date;
  created_at: Date;
  number_of_attempts: number;
}

export type UserTokensPk = "user_id";
export type UserTokensId = UserTokens[UserTokensPk];
export type UserTokensOptionalAttributes = "user_id" | "user_ip_address" | "user_agent_id" | "last_login" | "updated_at" | "created_at" | "number_of_attempts";
export type UserTokensCreationAttributes = Optional<UserTokensAttributes, UserTokensOptionalAttributes>;

export class UserTokens extends Model<UserTokensAttributes, UserTokensCreationAttributes> implements UserTokensAttributes {
  user_id!: number;
  refresh_token!: string;
  user_ip_address?: string;
  user_agent_id?: number;
  last_login?: Date;
  updated_at!: Date;
  created_at!: Date;
  number_of_attempts!: number;

  // UserTokens belongsTo UserBrowserAgents via user_agent_id
  user_agent!: UserBrowserAgents;
  getUser_agent!: Sequelize.BelongsToGetAssociationMixin<UserBrowserAgents>;
  setUser_agent!: Sequelize.BelongsToSetAssociationMixin<UserBrowserAgents, UserBrowserAgentsId>;
  createUser_agent!: Sequelize.BelongsToCreateAssociationMixin<UserBrowserAgents>;
  // UserTokens belongsTo Users via user_id
  user!: Users;
  getUser!: Sequelize.BelongsToGetAssociationMixin<Users>;
  setUser!: Sequelize.BelongsToSetAssociationMixin<Users, UsersId>;
  createUser!: Sequelize.BelongsToCreateAssociationMixin<Users>;

  static initModel(sequelize: Sequelize.Sequelize): typeof UserTokens {
    return UserTokens.init({
    user_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    refresh_token: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    user_ip_address: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    user_agent_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true,
      references: {
        model: 'user_browser_agents',
        key: 'id'
      }
    },
    last_login: {
      type: DataTypes.DATE,
      allowNull: true
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    number_of_attempts: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'user_tokens',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "user_id" },
        ]
      },
      {
        name: "FK_user_tokens_user_agent_id",
        using: "BTREE",
        fields: [
          { name: "user_agent_id" },
        ]
      },
    ]
  });
  }
}
