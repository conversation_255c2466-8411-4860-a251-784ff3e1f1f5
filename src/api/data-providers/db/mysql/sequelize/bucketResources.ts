import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { Users, UsersId } from './users';

export interface BucketResourcesAttributes {
  id: number;
  bucket: 'asia' | 'eu' | 'asiaskywind';
  path_hash: string;
  path: string;
  resource_type: 'html' | 'css' | 'image' | 'video' | 'file';
  optimized_image_id?: number;
  uploaded_at: Date;
  uploaded_by: number;
}

export type BucketResourcesPk = "id";
export type BucketResourcesId = BucketResources[BucketResourcesPk];
export type BucketResourcesOptionalAttributes = "id" | "optimized_image_id" | "uploaded_at";
export type BucketResourcesCreationAttributes = Optional<BucketResourcesAttributes, BucketResourcesOptionalAttributes>;

export class BucketResources extends Model<BucketResourcesAttributes, BucketResourcesCreationAttributes> implements BucketResourcesAttributes {
  id!: number;
  bucket!: 'asia' | 'eu' | 'asiaskywind';
  path_hash!: string;
  path!: string;
  resource_type!: 'html' | 'css' | 'image' | 'video' | 'file';
  optimized_image_id?: number;
  uploaded_at!: Date;
  uploaded_by!: number;

  // BucketResources belongsTo Users via uploaded_by
  uploaded_by_user!: Users;
  getUploaded_by_user!: Sequelize.BelongsToGetAssociationMixin<Users>;
  setUploaded_by_user!: Sequelize.BelongsToSetAssociationMixin<Users, UsersId>;
  createUploaded_by_user!: Sequelize.BelongsToCreateAssociationMixin<Users>;

  static initModel(sequelize: Sequelize.Sequelize): typeof BucketResources {
    return BucketResources.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    bucket: {
      type: DataTypes.ENUM('asia','eu','asiaskywind'),
      allowNull: false
    },
    path_hash: {
      type: DataTypes.STRING(32),
      allowNull: false
    },
    path: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    resource_type: {
      type: DataTypes.BLOB,
      allowNull: false
    },
    optimized_image_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true
    },
    uploaded_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    uploaded_by: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    sequelize,
    tableName: 'bucket_resources',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
      {
        name: "UK_bucket_resources",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "bucket" },
          { name: "path_hash" },
        ]
      },
      {
        name: "UK_bucket_resources_bucket",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "bucket" },
          { name: "path_hash" },
        ]
      },
      {
        name: "FK_bucket_resources_uploaded_by",
        using: "BTREE",
        fields: [
          { name: "uploaded_by" },
        ]
      },
    ]
  });
  }
}
