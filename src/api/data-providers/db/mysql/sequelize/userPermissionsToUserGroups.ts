import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { UserGroups, UserGroupsId } from './userGroups';
import type { UserPermissions, UserPermissionsId } from './userPermissions';

export interface UserPermissionsToUserGroupsAttributes {
  user_group_id: number;
  permission_code: string;
}

export type UserPermissionsToUserGroupsPk = "user_group_id" | "permission_code";
export type UserPermissionsToUserGroupsId = UserPermissionsToUserGroups[UserPermissionsToUserGroupsPk];
export type UserPermissionsToUserGroupsOptionalAttributes = "user_group_id" | "permission_code";
export type UserPermissionsToUserGroupsCreationAttributes = Optional<UserPermissionsToUserGroupsAttributes, UserPermissionsToUserGroupsOptionalAttributes>;

export class UserPermissionsToUserGroups extends Model<UserPermissionsToUserGroupsAttributes, UserPermissionsToUserGroupsCreationAttributes> implements UserPermissionsToUserGroupsAttributes {
  user_group_id!: number;
  permission_code!: string;

  // UserPermissionsToUserGroups belongsTo UserGroups via user_group_id
  user_group!: UserGroups;
  getUser_group!: Sequelize.BelongsToGetAssociationMixin<UserGroups>;
  setUser_group!: Sequelize.BelongsToSetAssociationMixin<UserGroups, UserGroupsId>;
  createUser_group!: Sequelize.BelongsToCreateAssociationMixin<UserGroups>;
  // UserPermissionsToUserGroups belongsTo UserPermissions via permission_code
  permission_code_user_permission!: UserPermissions;
  getPermission_code_user_permission!: Sequelize.BelongsToGetAssociationMixin<UserPermissions>;
  setPermission_code_user_permission!: Sequelize.BelongsToSetAssociationMixin<UserPermissions, UserPermissionsId>;
  createPermission_code_user_permission!: Sequelize.BelongsToCreateAssociationMixin<UserPermissions>;

  static initModel(sequelize: Sequelize.Sequelize): typeof UserPermissionsToUserGroups {
    return UserPermissionsToUserGroups.init({
    user_group_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true,
      references: {
        model: 'user_groups',
        key: 'id'
      }
    },
    permission_code: {
      type: DataTypes.STRING(64),
      allowNull: false,
      primaryKey: true,
      references: {
        model: 'user_permissions',
        key: 'code'
      }
    }
  }, {
    sequelize,
    tableName: 'user_permissions_to_user_groups',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "user_group_id" },
          { name: "permission_code" },
        ]
      },
      {
        name: "FK_user_permissions_to_user_groups_permission_code",
        using: "BTREE",
        fields: [
          { name: "permission_code" },
        ]
      },
    ]
  });
  }
}
