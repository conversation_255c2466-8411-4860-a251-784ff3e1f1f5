import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { UserActivity, UserActivityId } from './userActivity';
import type { UserTokens, UserTokensId } from './userTokens';

export interface UserBrowserAgentsAttributes {
  id: number;
  attrs?: object;
  agent: string;
}

export type UserBrowserAgentsPk = "id";
export type UserBrowserAgentsId = UserBrowserAgents[UserBrowserAgentsPk];
export type UserBrowserAgentsOptionalAttributes = "id" | "attrs";
export type UserBrowserAgentsCreationAttributes = Optional<UserBrowserAgentsAttributes, UserBrowserAgentsOptionalAttributes>;

export class UserBrowserAgents extends Model<UserBrowserAgentsAttributes, UserBrowserAgentsCreationAttributes> implements UserBrowserAgentsAttributes {
  id!: number;
  attrs?: object;
  agent!: string;

  // UserBrowserAgents hasMany UserActivity via user_agent_id
  user_activities!: UserActivity[];
  getUser_activities!: Sequelize.HasManyGetAssociationsMixin<UserActivity>;
  setUser_activities!: Sequelize.HasManySetAssociationsMixin<UserActivity, UserActivityId>;
  addUser_activity!: Sequelize.HasManyAddAssociationMixin<UserActivity, UserActivityId>;
  addUser_activities!: Sequelize.HasManyAddAssociationsMixin<UserActivity, UserActivityId>;
  createUser_activity!: Sequelize.HasManyCreateAssociationMixin<UserActivity>;
  removeUser_activity!: Sequelize.HasManyRemoveAssociationMixin<UserActivity, UserActivityId>;
  removeUser_activities!: Sequelize.HasManyRemoveAssociationsMixin<UserActivity, UserActivityId>;
  hasUser_activity!: Sequelize.HasManyHasAssociationMixin<UserActivity, UserActivityId>;
  hasUser_activities!: Sequelize.HasManyHasAssociationsMixin<UserActivity, UserActivityId>;
  countUser_activities!: Sequelize.HasManyCountAssociationsMixin;
  // UserBrowserAgents hasMany UserTokens via user_agent_id
  user_tokens!: UserTokens[];
  getUser_tokens!: Sequelize.HasManyGetAssociationsMixin<UserTokens>;
  setUser_tokens!: Sequelize.HasManySetAssociationsMixin<UserTokens, UserTokensId>;
  addUser_token!: Sequelize.HasManyAddAssociationMixin<UserTokens, UserTokensId>;
  addUser_tokens!: Sequelize.HasManyAddAssociationsMixin<UserTokens, UserTokensId>;
  createUser_token!: Sequelize.HasManyCreateAssociationMixin<UserTokens>;
  removeUser_token!: Sequelize.HasManyRemoveAssociationMixin<UserTokens, UserTokensId>;
  removeUser_tokens!: Sequelize.HasManyRemoveAssociationsMixin<UserTokens, UserTokensId>;
  hasUser_token!: Sequelize.HasManyHasAssociationMixin<UserTokens, UserTokensId>;
  hasUser_tokens!: Sequelize.HasManyHasAssociationsMixin<UserTokens, UserTokensId>;
  countUser_tokens!: Sequelize.HasManyCountAssociationsMixin;

  static initModel(sequelize: Sequelize.Sequelize): typeof UserBrowserAgents {
    return UserBrowserAgents.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    attrs: {
      type: DataTypes.JSON,
      allowNull: true
    },
    agent: {
      type: DataTypes.TEXT,
      allowNull: false,
      unique: "UK_user_browser_agents_agent"
    }
  }, {
    sequelize,
    tableName: 'user_browser_agents',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
      {
        name: "UK_user_browser_agents_agent",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "agent", length: 700 },
        ]
      },
    ]
  });
  }
}
