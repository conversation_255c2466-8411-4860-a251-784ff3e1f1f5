import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { UserBrowserAgents, UserBrowserAgentsId } from './userBrowserAgents';
import type { Users, UsersId } from './users';

export interface UserActivityAttributes {
  id: number;
  created_at: Date;
  user_id?: number;
  user_login?: string;
  user_action: string;
  request_path?: string;
  user_ip?: string;
  user_agent_id?: number;
  params?: object;
  game_code?: string;
  status: string;
  session_id?: string;
}

export type UserActivityPk = "id";
export type UserActivityId = UserActivity[UserActivityPk];
export type UserActivityOptionalAttributes = "id" | "created_at" | "user_id" | "user_login" | "request_path" | "user_ip" | "user_agent_id" | "params" | "game_code" | "status" | "session_id";
export type UserActivityCreationAttributes = Optional<UserActivityAttributes, UserActivityOptionalAttributes>;

export class UserActivity extends Model<UserActivityAttributes, UserActivityCreationAttributes> implements UserActivityAttributes {
  id!: number;
  created_at!: Date;
  user_id?: number;
  user_login?: string;
  user_action!: string;
  request_path?: string;
  user_ip?: string;
  user_agent_id?: number;
  params?: object;
  game_code?: string;
  status!: string;
  session_id?: string;

  // UserActivity belongsTo UserBrowserAgents via user_agent_id
  user_agent!: UserBrowserAgents;
  getUser_agent!: Sequelize.BelongsToGetAssociationMixin<UserBrowserAgents>;
  setUser_agent!: Sequelize.BelongsToSetAssociationMixin<UserBrowserAgents, UserBrowserAgentsId>;
  createUser_agent!: Sequelize.BelongsToCreateAssociationMixin<UserBrowserAgents>;
  // UserActivity belongsTo Users via user_id
  user!: Users;
  getUser!: Sequelize.BelongsToGetAssociationMixin<Users>;
  setUser!: Sequelize.BelongsToSetAssociationMixin<Users, UsersId>;
  createUser!: Sequelize.BelongsToCreateAssociationMixin<Users>;

  static initModel(sequelize: Sequelize.Sequelize): typeof UserActivity {
    return UserActivity.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    user_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    user_login: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    user_action: {
      type: DataTypes.STRING(40),
      allowNull: false
    },
    request_path: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    user_ip: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    user_agent_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true,
      references: {
        model: 'user_browser_agents',
        key: 'id'
      }
    },
    params: {
      type: DataTypes.JSON,
      allowNull: true
    },
    game_code: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    status: {
      type: DataTypes.STRING(10),
      allowNull: false,
      defaultValue: "failed"
    },
    session_id: {
      type: DataTypes.STRING(255),
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'user_activity',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
      {
        name: "IDX_user_activity_user_id",
        using: "BTREE",
        fields: [
          { name: "user_id" },
        ]
      },
      {
        name: "IDX_user_activity_user_login",
        using: "BTREE",
        fields: [
          { name: "user_login" },
        ]
      },
      {
        name: "IDX_user_activity_user_action",
        using: "BTREE",
        fields: [
          { name: "user_action" },
        ]
      },
      {
        name: "IDX_user_activity_request_path",
        using: "BTREE",
        fields: [
          { name: "request_path" },
        ]
      },
      {
        name: "FK_user_activity_user_agent_id",
        using: "BTREE",
        fields: [
          { name: "user_agent_id" },
        ]
      },
      {
        name: "IDX_user_activity_session_id",
        using: "BTREE",
        fields: [
          { name: "session_id" },
        ]
      },
    ]
  });
  }
}
