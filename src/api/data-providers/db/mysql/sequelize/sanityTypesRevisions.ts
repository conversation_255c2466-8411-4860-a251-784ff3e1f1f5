import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';

export interface SanityTypesRevisionsAttributes {
  revision_operation_type?: 'insert' | 'update' | 'delete';
  revision_number: number;
  revision_created_at: Date;
  id: number;
  name: string;
  version_id: number;
}

export type SanityTypesRevisionsPk = "revision_number" | "id";
export type SanityTypesRevisionsId = SanityTypesRevisions[SanityTypesRevisionsPk];
export type SanityTypesRevisionsOptionalAttributes = "revision_operation_type" | "revision_number" | "revision_created_at" | "id";
export type SanityTypesRevisionsCreationAttributes = Optional<SanityTypesRevisionsAttributes, SanityTypesRevisionsOptionalAttributes>;

export class SanityTypesRevisions extends Model<SanityTypesRevisionsAttributes, SanityTypesRevisionsCreationAttributes> implements SanityTypesRevisionsAttributes {
  revision_operation_type?: 'insert' | 'update' | 'delete';
  revision_number!: number;
  revision_created_at!: Date;
  id!: number;
  name!: string;
  version_id!: number;


  static initModel(sequelize: Sequelize.Sequelize): typeof SanityTypesRevisions {
    return SanityTypesRevisions.init({
    revision_operation_type: {
      type: DataTypes.ENUM('insert','update','delete'),
      allowNull: true,
      defaultValue: "insert"
    },
    revision_number: {
      autoIncrement: true,
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    revision_created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    version_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'sanity_types_revisions',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
          { name: "revision_number" },
        ]
      },
      {
        name: "IDX_sanity_types_revisions_password",
        using: "BTREE",
        fields: [
          { name: "revision_number" },
        ]
      },
    ]
  });
  }
}
