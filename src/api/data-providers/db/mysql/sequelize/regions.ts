import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { Users, UsersId } from './users';
import type { UsersToRegions, UsersToRegionsId } from './usersToRegions';

export interface RegionsAttributes {
  id: number;
  code: string;
  name: string;
  is_active: number;
  attrs?: object;
  nicename: string;
  iso3?: string;
  numcode?: number;
  phonecode: number;
  type: string;
}

export type RegionsPk = "id";
export type RegionsId = Regions[RegionsPk];
export type RegionsOptionalAttributes = "id" | "attrs" | "iso3" | "numcode" | "type";
export type RegionsCreationAttributes = Optional<RegionsAttributes, RegionsOptionalAttributes>;

export class Regions extends Model<RegionsAttributes, RegionsCreationAttributes> implements RegionsAttributes {
  id!: number;
  code!: string;
  name!: string;
  is_active!: number;
  attrs?: object;
  nicename!: string;
  iso3?: string;
  numcode?: number;
  phonecode!: number;
  type!: string;

  // Regions belongsToMany Users via region_id and user_id
  user_id_users!: Users[];
  getUser_id_users!: Sequelize.BelongsToManyGetAssociationsMixin<Users>;
  setUser_id_users!: Sequelize.BelongsToManySetAssociationsMixin<Users, UsersId>;
  addUser_id_user!: Sequelize.BelongsToManyAddAssociationMixin<Users, UsersId>;
  addUser_id_users!: Sequelize.BelongsToManyAddAssociationsMixin<Users, UsersId>;
  createUser_id_user!: Sequelize.BelongsToManyCreateAssociationMixin<Users>;
  removeUser_id_user!: Sequelize.BelongsToManyRemoveAssociationMixin<Users, UsersId>;
  removeUser_id_users!: Sequelize.BelongsToManyRemoveAssociationsMixin<Users, UsersId>;
  hasUser_id_user!: Sequelize.BelongsToManyHasAssociationMixin<Users, UsersId>;
  hasUser_id_users!: Sequelize.BelongsToManyHasAssociationsMixin<Users, UsersId>;
  countUser_id_users!: Sequelize.BelongsToManyCountAssociationsMixin;
  // Regions hasMany UsersToRegions via region_id
  users_to_regions!: UsersToRegions[];
  getUsers_to_regions!: Sequelize.HasManyGetAssociationsMixin<UsersToRegions>;
  setUsers_to_regions!: Sequelize.HasManySetAssociationsMixin<UsersToRegions, UsersToRegionsId>;
  addUsers_to_region!: Sequelize.HasManyAddAssociationMixin<UsersToRegions, UsersToRegionsId>;
  addUsers_to_regions!: Sequelize.HasManyAddAssociationsMixin<UsersToRegions, UsersToRegionsId>;
  createUsers_to_region!: Sequelize.HasManyCreateAssociationMixin<UsersToRegions>;
  removeUsers_to_region!: Sequelize.HasManyRemoveAssociationMixin<UsersToRegions, UsersToRegionsId>;
  removeUsers_to_regions!: Sequelize.HasManyRemoveAssociationsMixin<UsersToRegions, UsersToRegionsId>;
  hasUsers_to_region!: Sequelize.HasManyHasAssociationMixin<UsersToRegions, UsersToRegionsId>;
  hasUsers_to_regions!: Sequelize.HasManyHasAssociationsMixin<UsersToRegions, UsersToRegionsId>;
  countUsers_to_regions!: Sequelize.HasManyCountAssociationsMixin;

  static initModel(sequelize: Sequelize.Sequelize): typeof Regions {
    return Regions.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    code: {
      type: DataTypes.STRING(32),
      allowNull: false,
      unique: "UK_regions_code"
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: "UK_regions_name"
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false
    },
    attrs: {
      type: DataTypes.JSON,
      allowNull: true
    },
    nicename: {
      type: DataTypes.STRING(80),
      allowNull: false
    },
    iso3: {
      type: DataTypes.CHAR(3),
      allowNull: true
    },
    numcode: {
      type: DataTypes.SMALLINT,
      allowNull: true
    },
    phonecode: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    type: {
      type: DataTypes.CHAR(1),
      allowNull: false,
      defaultValue: "r"
    }
  }, {
    sequelize,
    tableName: 'regions',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
      {
        name: "UK_regions_code",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "code" },
        ]
      },
      {
        name: "UK_regions_name",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "name" },
        ]
      },
    ]
  });
  }
}
