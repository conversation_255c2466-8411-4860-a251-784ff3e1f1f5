import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { SanityItems, SanityItemsId } from './sanityItems';

export interface OptimizedImagesAttributes {
  id: number;
  sanity_item_id?: number;
  parent_id?: number;
  path_hash: string;
  path: string;
  width: number;
  height: number;
  file_size: number;
  created_at: Date;
  updated_at?: Date;
}

export type OptimizedImagesPk = "id";
export type OptimizedImagesId = OptimizedImages[OptimizedImagesPk];
export type OptimizedImagesOptionalAttributes = "id" | "sanity_item_id" | "parent_id" | "created_at" | "updated_at";
export type OptimizedImagesCreationAttributes = Optional<OptimizedImagesAttributes, OptimizedImagesOptionalAttributes>;

export class OptimizedImages extends Model<OptimizedImagesAttributes, OptimizedImagesCreationAttributes> implements OptimizedImagesAttributes {
  id!: number;
  sanity_item_id?: number;
  parent_id?: number;
  path_hash!: string;
  path!: string;
  width!: number;
  height!: number;
  file_size!: number;
  created_at!: Date;
  updated_at?: Date;

  // OptimizedImages belongsTo SanityItems via sanity_item_id
  sanity_item!: SanityItems;
  getSanity_item!: Sequelize.BelongsToGetAssociationMixin<SanityItems>;
  setSanity_item!: Sequelize.BelongsToSetAssociationMixin<SanityItems, SanityItemsId>;
  createSanity_item!: Sequelize.BelongsToCreateAssociationMixin<SanityItems>;

  static initModel(sequelize: Sequelize.Sequelize): typeof OptimizedImages {
    return OptimizedImages.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    sanity_item_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true,
      references: {
        model: 'sanity_items',
        key: 'id'
      }
    },
    parent_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true
    },
    path_hash: {
      type: DataTypes.STRING(32),
      allowNull: false,
      unique: "UK_optimized_images_path_hash"
    },
    path: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    width: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false
    },
    height: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false
    },
    file_size: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'optimized_images',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
      {
        name: "UK_optimized_images_path_hash",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "path_hash" },
        ]
      },
      {
        name: "FK_optimized_images_sanity_item_id",
        using: "BTREE",
        fields: [
          { name: "sanity_item_id" },
        ]
      },
    ]
  });
  }
}
