import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { Versions, VersionsId } from './versions';

export interface DeployLogAttributes {
  id: number;
  type: 'request' | 'start' | 'info' | 'end';
  version_id: number;
  is_active: number;
  message: object;
  time: Date;
  ts?: Date;
}

export type DeployLogPk = "id";
export type DeployLogId = DeployLog[DeployLogPk];
export type DeployLogOptionalAttributes = "id" | "is_active" | "time" | "ts";
export type DeployLogCreationAttributes = Optional<DeployLogAttributes, DeployLogOptionalAttributes>;

export class DeployLog extends Model<DeployLogAttributes, DeployLogCreationAttributes> implements DeployLogAttributes {
  id!: number;
  type!: 'request' | 'start' | 'info' | 'end';
  version_id!: number;
  is_active!: number;
  message!: object;
  time!: Date;
  ts?: Date;

  // DeployLog belongsTo Versions via version_id
  version!: Versions;
  getVersion!: Sequelize.BelongsToGetAssociationMixin<Versions>;
  setVersion!: Sequelize.BelongsToSetAssociationMixin<Versions, VersionsId>;
  createVersion!: Sequelize.BelongsToCreateAssociationMixin<Versions>;

  static initModel(sequelize: Sequelize.Sequelize): typeof DeployLog {
    return DeployLog.init({
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT.UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    type: {
      type: DataTypes.ENUM('request','start','info','end'),
      allowNull: false
    },
    version_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      references: {
        model: 'versions',
        key: 'id'
      }
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: 0
    },
    message: {
      type: DataTypes.JSON,
      allowNull: false
    },
    time: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    ts: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    }
  }, {
    sequelize,
    tableName: 'deploy_log',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
      {
        name: "IDX_deploy_log_is_active",
        using: "BTREE",
        fields: [
          { name: "is_active" },
        ]
      },
      {
        name: "IDX_deploy_log_time",
        using: "BTREE",
        fields: [
          { name: "time" },
        ]
      },
      {
        name: "IDX_deploy_log_type",
        using: "BTREE",
        fields: [
          { name: "type" },
        ]
      },
      {
        name: "FK_deploy_log_version",
        using: "BTREE",
        fields: [
          { name: "version_id" },
        ]
      },
    ]
  });
  }
}
