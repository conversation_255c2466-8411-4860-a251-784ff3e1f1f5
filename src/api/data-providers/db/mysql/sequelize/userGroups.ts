import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { UserPermissions, UserPermissionsId } from './userPermissions';
import type { UserPermissionsToUserGroups, UserPermissionsToUserGroupsId } from './userPermissionsToUserGroups';
import type { Users, UsersId } from './users';
import type { UsersToUserGroups, UsersToUserGroupsId } from './usersToUserGroups';

export interface UserGroupsAttributes {
  id: number;
  group_attr: object;
  group_code: string;
  is_active: number;
}

export type UserGroupsPk = "id";
export type UserGroupsId = UserGroups[UserGroupsPk];
export type UserGroupsOptionalAttributes = "id" | "is_active";
export type UserGroupsCreationAttributes = Optional<UserGroupsAttributes, UserGroupsOptionalAttributes>;

export class UserGroups extends Model<UserGroupsAttributes, UserGroupsCreationAttributes> implements UserGroupsAttributes {
  id!: number;
  group_attr!: object;
  group_code!: string;
  is_active!: number;

  // UserGroups belongsToMany UserPermissions via user_group_id and permission_code
  permission_code_user_permissions!: UserPermissions[];
  getPermission_code_user_permissions!: Sequelize.BelongsToManyGetAssociationsMixin<UserPermissions>;
  setPermission_code_user_permissions!: Sequelize.BelongsToManySetAssociationsMixin<UserPermissions, UserPermissionsId>;
  addPermission_code_user_permission!: Sequelize.BelongsToManyAddAssociationMixin<UserPermissions, UserPermissionsId>;
  addPermission_code_user_permissions!: Sequelize.BelongsToManyAddAssociationsMixin<UserPermissions, UserPermissionsId>;
  createPermission_code_user_permission!: Sequelize.BelongsToManyCreateAssociationMixin<UserPermissions>;
  removePermission_code_user_permission!: Sequelize.BelongsToManyRemoveAssociationMixin<UserPermissions, UserPermissionsId>;
  removePermission_code_user_permissions!: Sequelize.BelongsToManyRemoveAssociationsMixin<UserPermissions, UserPermissionsId>;
  hasPermission_code_user_permission!: Sequelize.BelongsToManyHasAssociationMixin<UserPermissions, UserPermissionsId>;
  hasPermission_code_user_permissions!: Sequelize.BelongsToManyHasAssociationsMixin<UserPermissions, UserPermissionsId>;
  countPermission_code_user_permissions!: Sequelize.BelongsToManyCountAssociationsMixin;
  // UserGroups hasMany UserPermissionsToUserGroups via user_group_id
  user_permissions_to_user_groups!: UserPermissionsToUserGroups[];
  getUser_permissions_to_user_groups!: Sequelize.HasManyGetAssociationsMixin<UserPermissionsToUserGroups>;
  setUser_permissions_to_user_groups!: Sequelize.HasManySetAssociationsMixin<UserPermissionsToUserGroups, UserPermissionsToUserGroupsId>;
  addUser_permissions_to_user_group!: Sequelize.HasManyAddAssociationMixin<UserPermissionsToUserGroups, UserPermissionsToUserGroupsId>;
  addUser_permissions_to_user_groups!: Sequelize.HasManyAddAssociationsMixin<UserPermissionsToUserGroups, UserPermissionsToUserGroupsId>;
  createUser_permissions_to_user_group!: Sequelize.HasManyCreateAssociationMixin<UserPermissionsToUserGroups>;
  removeUser_permissions_to_user_group!: Sequelize.HasManyRemoveAssociationMixin<UserPermissionsToUserGroups, UserPermissionsToUserGroupsId>;
  removeUser_permissions_to_user_groups!: Sequelize.HasManyRemoveAssociationsMixin<UserPermissionsToUserGroups, UserPermissionsToUserGroupsId>;
  hasUser_permissions_to_user_group!: Sequelize.HasManyHasAssociationMixin<UserPermissionsToUserGroups, UserPermissionsToUserGroupsId>;
  hasUser_permissions_to_user_groups!: Sequelize.HasManyHasAssociationsMixin<UserPermissionsToUserGroups, UserPermissionsToUserGroupsId>;
  countUser_permissions_to_user_groups!: Sequelize.HasManyCountAssociationsMixin;
  // UserGroups belongsToMany Users via user_group_id and user_id
  user_id_users_users_to_user_groups!: Users[];
  getUser_id_users_users_to_user_groups!: Sequelize.BelongsToManyGetAssociationsMixin<Users>;
  setUser_id_users_users_to_user_groups!: Sequelize.BelongsToManySetAssociationsMixin<Users, UsersId>;
  addUser_id_users_users_to_user_group!: Sequelize.BelongsToManyAddAssociationMixin<Users, UsersId>;
  addUser_id_users_users_to_user_groups!: Sequelize.BelongsToManyAddAssociationsMixin<Users, UsersId>;
  createUser_id_users_users_to_user_group!: Sequelize.BelongsToManyCreateAssociationMixin<Users>;
  removeUser_id_users_users_to_user_group!: Sequelize.BelongsToManyRemoveAssociationMixin<Users, UsersId>;
  removeUser_id_users_users_to_user_groups!: Sequelize.BelongsToManyRemoveAssociationsMixin<Users, UsersId>;
  hasUser_id_users_users_to_user_group!: Sequelize.BelongsToManyHasAssociationMixin<Users, UsersId>;
  hasUser_id_users_users_to_user_groups!: Sequelize.BelongsToManyHasAssociationsMixin<Users, UsersId>;
  countUser_id_users_users_to_user_groups!: Sequelize.BelongsToManyCountAssociationsMixin;
  // UserGroups hasMany UsersToUserGroups via user_group_id
  users_to_user_groups!: UsersToUserGroups[];
  getUsers_to_user_groups!: Sequelize.HasManyGetAssociationsMixin<UsersToUserGroups>;
  setUsers_to_user_groups!: Sequelize.HasManySetAssociationsMixin<UsersToUserGroups, UsersToUserGroupsId>;
  addUsers_to_user_group!: Sequelize.HasManyAddAssociationMixin<UsersToUserGroups, UsersToUserGroupsId>;
  addUsers_to_user_groups!: Sequelize.HasManyAddAssociationsMixin<UsersToUserGroups, UsersToUserGroupsId>;
  createUsers_to_user_group!: Sequelize.HasManyCreateAssociationMixin<UsersToUserGroups>;
  removeUsers_to_user_group!: Sequelize.HasManyRemoveAssociationMixin<UsersToUserGroups, UsersToUserGroupsId>;
  removeUsers_to_user_groups!: Sequelize.HasManyRemoveAssociationsMixin<UsersToUserGroups, UsersToUserGroupsId>;
  hasUsers_to_user_group!: Sequelize.HasManyHasAssociationMixin<UsersToUserGroups, UsersToUserGroupsId>;
  hasUsers_to_user_groups!: Sequelize.HasManyHasAssociationsMixin<UsersToUserGroups, UsersToUserGroupsId>;
  countUsers_to_user_groups!: Sequelize.HasManyCountAssociationsMixin;

  static initModel(sequelize: Sequelize.Sequelize): typeof UserGroups {
    return UserGroups.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    group_attr: {
      type: DataTypes.JSON,
      allowNull: false
    },
    group_code: {
      type: DataTypes.STRING(64),
      allowNull: false,
      unique: "user_groups_group_code_key"
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: 1
    }
  }, {
    sequelize,
    tableName: 'user_groups',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
      {
        name: "id",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
      {
        name: "user_groups_group_code_key",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "group_code" },
        ]
      },
    ]
  });
  }
}
