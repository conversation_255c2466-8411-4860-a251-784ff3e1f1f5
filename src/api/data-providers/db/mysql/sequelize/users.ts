import * as Sequelize from "sequelize";
import { DataTypes, Model, Optional } from "sequelize";
import type { BucketResources, BucketResourcesId } from "./bucketResources";
import type { Regions, RegionsId } from "./regions";
import type { SanityItems, SanityItemsId } from "./sanityItems";
import type { UserActivation, UserActivationCreationAttributes, UserActivationId } from "./userActivation";
import type { UserActivity, UserActivityId } from "./userActivity";
import type { UserGroups, UserGroupsId } from "./userGroups";
import type { UserPlayers, UserPlayersId } from "./userPlayers";
import type { UserTokens, UserTokensCreationAttributes, UserTokensId } from "./userTokens";
import type { UsersToRegions, UsersToRegionsId } from "./usersToRegions";
import type { UsersToUserGroups, UsersToUserGroupsId } from "./usersToUserGroups";
import type { UsersToUsers, UsersToUsersId } from "./usersToUsers";
import type { Versions, VersionsId } from "./versions";

export interface UsersAttributes {
    id: number;
    login: string;
    password: string;
    first_name?: string;
    last_name?: string;
    email: string;
    company?: string;
    company_position?: string;
    created_at: Date;
    updated_by?: number;
    updated_at?: Date;
    is_active?: number;
    attrs?: object;
    password_updated_at?: Date;
    last_login_at?: Date;
}

export type UsersPk = "id";
export type UsersId = Users[UsersPk];
export type UsersOptionalAttributes =
    | "id"
    | "first_name"
    | "last_name"
    | "company"
    | "company_position"
    | "created_at"
    | "updated_by"
    | "updated_at"
    | "is_active"
    | "attrs"
    | "password_updated_at"
    | "last_login_at";
export type UsersCreationAttributes = Optional<UsersAttributes, UsersOptionalAttributes>;

export class Users extends Model<UsersAttributes, UsersCreationAttributes> implements UsersAttributes {
    id!: number;
    login!: string;
    password!: string;
    first_name?: string;
    last_name?: string;
    email!: string;
    company?: string;
    company_position?: string;
    created_at!: Date;
    updated_by?: number;
    updated_at?: Date;
    is_active?: number;
    attrs?: object;
    password_updated_at?: Date;
    last_login_at?: Date;

    // Users hasMany BucketResources via uploaded_by
    bucket_resources!: BucketResources[];
    getBucket_resources!: Sequelize.HasManyGetAssociationsMixin<BucketResources>;
    setBucket_resources!: Sequelize.HasManySetAssociationsMixin<BucketResources, BucketResourcesId>;
    addBucket_resource!: Sequelize.HasManyAddAssociationMixin<BucketResources, BucketResourcesId>;
    addBucket_resources!: Sequelize.HasManyAddAssociationsMixin<BucketResources, BucketResourcesId>;
    createBucket_resource!: Sequelize.HasManyCreateAssociationMixin<BucketResources>;
    removeBucket_resource!: Sequelize.HasManyRemoveAssociationMixin<BucketResources, BucketResourcesId>;
    removeBucket_resources!: Sequelize.HasManyRemoveAssociationsMixin<BucketResources, BucketResourcesId>;
    hasBucket_resource!: Sequelize.HasManyHasAssociationMixin<BucketResources, BucketResourcesId>;
    hasBucket_resources!: Sequelize.HasManyHasAssociationsMixin<BucketResources, BucketResourcesId>;
    countBucket_resources!: Sequelize.HasManyCountAssociationsMixin;
    // Users belongsToMany Regions via user_id and region_id
    region_id_regions!: Regions[];
    getRegion_id_regions!: Sequelize.BelongsToManyGetAssociationsMixin<Regions>;
    setRegion_id_regions!: Sequelize.BelongsToManySetAssociationsMixin<Regions, RegionsId>;
    addRegion_id_region!: Sequelize.BelongsToManyAddAssociationMixin<Regions, RegionsId>;
    addRegion_id_regions!: Sequelize.BelongsToManyAddAssociationsMixin<Regions, RegionsId>;
    createRegion_id_region!: Sequelize.BelongsToManyCreateAssociationMixin<Regions>;
    removeRegion_id_region!: Sequelize.BelongsToManyRemoveAssociationMixin<Regions, RegionsId>;
    removeRegion_id_regions!: Sequelize.BelongsToManyRemoveAssociationsMixin<Regions, RegionsId>;
    hasRegion_id_region!: Sequelize.BelongsToManyHasAssociationMixin<Regions, RegionsId>;
    hasRegion_id_regions!: Sequelize.BelongsToManyHasAssociationsMixin<Regions, RegionsId>;
    countRegion_id_regions!: Sequelize.BelongsToManyCountAssociationsMixin;
    // Users hasMany SanityItems via created_by
    sanity_items!: SanityItems[];
    getSanity_items!: Sequelize.HasManyGetAssociationsMixin<SanityItems>;
    setSanity_items!: Sequelize.HasManySetAssociationsMixin<SanityItems, SanityItemsId>;
    addSanity_item!: Sequelize.HasManyAddAssociationMixin<SanityItems, SanityItemsId>;
    addSanity_items!: Sequelize.HasManyAddAssociationsMixin<SanityItems, SanityItemsId>;
    createSanity_item!: Sequelize.HasManyCreateAssociationMixin<SanityItems>;
    removeSanity_item!: Sequelize.HasManyRemoveAssociationMixin<SanityItems, SanityItemsId>;
    removeSanity_items!: Sequelize.HasManyRemoveAssociationsMixin<SanityItems, SanityItemsId>;
    hasSanity_item!: Sequelize.HasManyHasAssociationMixin<SanityItems, SanityItemsId>;
    hasSanity_items!: Sequelize.HasManyHasAssociationsMixin<SanityItems, SanityItemsId>;
    countSanity_items!: Sequelize.HasManyCountAssociationsMixin;
    // Users hasMany SanityItems via updated_by
    updated_by_sanity_items!: SanityItems[];
    getUpdated_by_sanity_items!: Sequelize.HasManyGetAssociationsMixin<SanityItems>;
    setUpdated_by_sanity_items!: Sequelize.HasManySetAssociationsMixin<SanityItems, SanityItemsId>;
    addUpdated_by_sanity_item!: Sequelize.HasManyAddAssociationMixin<SanityItems, SanityItemsId>;
    addUpdated_by_sanity_items!: Sequelize.HasManyAddAssociationsMixin<SanityItems, SanityItemsId>;
    createUpdated_by_sanity_item!: Sequelize.HasManyCreateAssociationMixin<SanityItems>;
    removeUpdated_by_sanity_item!: Sequelize.HasManyRemoveAssociationMixin<SanityItems, SanityItemsId>;
    removeUpdated_by_sanity_items!: Sequelize.HasManyRemoveAssociationsMixin<SanityItems, SanityItemsId>;
    hasUpdated_by_sanity_item!: Sequelize.HasManyHasAssociationMixin<SanityItems, SanityItemsId>;
    hasUpdated_by_sanity_items!: Sequelize.HasManyHasAssociationsMixin<SanityItems, SanityItemsId>;
    countUpdated_by_sanity_items!: Sequelize.HasManyCountAssociationsMixin;
    // Users hasOne UserActivation via user_id
    user_activation!: UserActivation;
    getUser_activation!: Sequelize.HasOneGetAssociationMixin<UserActivation>;
    setUser_activation!: Sequelize.HasOneSetAssociationMixin<UserActivation, UserActivationId>;
    // @ts-ignore
    createUser_activation!: Sequelize.HasOneCreateAssociationMixin<UserActivationCreationAttributes>;
    // Users hasMany UserActivity via user_id
    user_activities!: UserActivity[];
    getUser_activities!: Sequelize.HasManyGetAssociationsMixin<UserActivity>;
    setUser_activities!: Sequelize.HasManySetAssociationsMixin<UserActivity, UserActivityId>;
    addUser_activity!: Sequelize.HasManyAddAssociationMixin<UserActivity, UserActivityId>;
    addUser_activities!: Sequelize.HasManyAddAssociationsMixin<UserActivity, UserActivityId>;
    createUser_activity!: Sequelize.HasManyCreateAssociationMixin<UserActivity>;
    removeUser_activity!: Sequelize.HasManyRemoveAssociationMixin<UserActivity, UserActivityId>;
    removeUser_activities!: Sequelize.HasManyRemoveAssociationsMixin<UserActivity, UserActivityId>;
    hasUser_activity!: Sequelize.HasManyHasAssociationMixin<UserActivity, UserActivityId>;
    hasUser_activities!: Sequelize.HasManyHasAssociationsMixin<UserActivity, UserActivityId>;
    countUser_activities!: Sequelize.HasManyCountAssociationsMixin;
    // Users belongsToMany UserGroups via user_id and user_group_id
    user_group_id_user_groups_users_to_user_groups!: UserGroups[];
    getUser_group_id_user_groups_users_to_user_groups!: Sequelize.BelongsToManyGetAssociationsMixin<UserGroups>;
    setUser_group_id_user_groups_users_to_user_groups!: Sequelize.BelongsToManySetAssociationsMixin<
        UserGroups,
        UserGroupsId
    >;
    addUser_group_id_user_groups_users_to_user_group!: Sequelize.BelongsToManyAddAssociationMixin<
        UserGroups,
        UserGroupsId
    >;
    addUser_group_id_user_groups_users_to_user_groups!: Sequelize.BelongsToManyAddAssociationsMixin<
        UserGroups,
        UserGroupsId
    >;
    createUser_group_id_user_groups_users_to_user_group!: Sequelize.BelongsToManyCreateAssociationMixin<UserGroups>;
    removeUser_group_id_user_groups_users_to_user_group!: Sequelize.BelongsToManyRemoveAssociationMixin<
        UserGroups,
        UserGroupsId
    >;
    removeUser_group_id_user_groups_users_to_user_groups!: Sequelize.BelongsToManyRemoveAssociationsMixin<
        UserGroups,
        UserGroupsId
    >;
    hasUser_group_id_user_groups_users_to_user_group!: Sequelize.BelongsToManyHasAssociationMixin<
        UserGroups,
        UserGroupsId
    >;
    hasUser_group_id_user_groups_users_to_user_groups!: Sequelize.BelongsToManyHasAssociationsMixin<
        UserGroups,
        UserGroupsId
    >;
    countUser_group_id_user_groups_users_to_user_groups!: Sequelize.BelongsToManyCountAssociationsMixin;
    // Users hasMany UserPlayers via user_id
    user_players!: UserPlayers[];
    getUser_players!: Sequelize.HasManyGetAssociationsMixin<UserPlayers>;
    setUser_players!: Sequelize.HasManySetAssociationsMixin<UserPlayers, UserPlayersId>;
    addUser_player!: Sequelize.HasManyAddAssociationMixin<UserPlayers, UserPlayersId>;
    addUser_players!: Sequelize.HasManyAddAssociationsMixin<UserPlayers, UserPlayersId>;
    createUser_player!: Sequelize.HasManyCreateAssociationMixin<UserPlayers>;
    removeUser_player!: Sequelize.HasManyRemoveAssociationMixin<UserPlayers, UserPlayersId>;
    removeUser_players!: Sequelize.HasManyRemoveAssociationsMixin<UserPlayers, UserPlayersId>;
    hasUser_player!: Sequelize.HasManyHasAssociationMixin<UserPlayers, UserPlayersId>;
    hasUser_players!: Sequelize.HasManyHasAssociationsMixin<UserPlayers, UserPlayersId>;
    countUser_players!: Sequelize.HasManyCountAssociationsMixin;
    // Users hasOne UserTokens via user_id
    user_token!: UserTokens;
    getUser_token!: Sequelize.HasOneGetAssociationMixin<UserTokens>;
    setUser_token!: Sequelize.HasOneSetAssociationMixin<UserTokens, UserTokensId>;
    // @ts-ignore
    createUser_token!: Sequelize.HasOneCreateAssociationMixin<UserTokensCreationAttributes>;
    // Users belongsToMany Users via main_user_id and related_user_id
    related_user_id_users!: Users[];
    getRelated_user_id_users!: Sequelize.BelongsToManyGetAssociationsMixin<Users>;
    setRelated_user_id_users!: Sequelize.BelongsToManySetAssociationsMixin<Users, UsersId>;
    addRelated_user_id_user!: Sequelize.BelongsToManyAddAssociationMixin<Users, UsersId>;
    addRelated_user_id_users!: Sequelize.BelongsToManyAddAssociationsMixin<Users, UsersId>;
    createRelated_user_id_user!: Sequelize.BelongsToManyCreateAssociationMixin<Users>;
    removeRelated_user_id_user!: Sequelize.BelongsToManyRemoveAssociationMixin<Users, UsersId>;
    removeRelated_user_id_users!: Sequelize.BelongsToManyRemoveAssociationsMixin<Users, UsersId>;
    hasRelated_user_id_user!: Sequelize.BelongsToManyHasAssociationMixin<Users, UsersId>;
    hasRelated_user_id_users!: Sequelize.BelongsToManyHasAssociationsMixin<Users, UsersId>;
    countRelated_user_id_users!: Sequelize.BelongsToManyCountAssociationsMixin;
    // Users belongsToMany Users via related_user_id and main_user_id
    main_user_id_users!: Users[];
    getMain_user_id_users!: Sequelize.BelongsToManyGetAssociationsMixin<Users>;
    setMain_user_id_users!: Sequelize.BelongsToManySetAssociationsMixin<Users, UsersId>;
    addMain_user_id_user!: Sequelize.BelongsToManyAddAssociationMixin<Users, UsersId>;
    addMain_user_id_users!: Sequelize.BelongsToManyAddAssociationsMixin<Users, UsersId>;
    createMain_user_id_user!: Sequelize.BelongsToManyCreateAssociationMixin<Users>;
    removeMain_user_id_user!: Sequelize.BelongsToManyRemoveAssociationMixin<Users, UsersId>;
    removeMain_user_id_users!: Sequelize.BelongsToManyRemoveAssociationsMixin<Users, UsersId>;
    hasMain_user_id_user!: Sequelize.BelongsToManyHasAssociationMixin<Users, UsersId>;
    hasMain_user_id_users!: Sequelize.BelongsToManyHasAssociationsMixin<Users, UsersId>;
    countMain_user_id_users!: Sequelize.BelongsToManyCountAssociationsMixin;
    // Users hasMany UsersToRegions via user_id
    users_to_regions!: UsersToRegions[];
    getUsers_to_regions!: Sequelize.HasManyGetAssociationsMixin<UsersToRegions>;
    setUsers_to_regions!: Sequelize.HasManySetAssociationsMixin<UsersToRegions, UsersToRegionsId>;
    addUsers_to_region!: Sequelize.HasManyAddAssociationMixin<UsersToRegions, UsersToRegionsId>;
    addUsers_to_regions!: Sequelize.HasManyAddAssociationsMixin<UsersToRegions, UsersToRegionsId>;
    createUsers_to_region!: Sequelize.HasManyCreateAssociationMixin<UsersToRegions>;
    removeUsers_to_region!: Sequelize.HasManyRemoveAssociationMixin<UsersToRegions, UsersToRegionsId>;
    removeUsers_to_regions!: Sequelize.HasManyRemoveAssociationsMixin<UsersToRegions, UsersToRegionsId>;
    hasUsers_to_region!: Sequelize.HasManyHasAssociationMixin<UsersToRegions, UsersToRegionsId>;
    hasUsers_to_regions!: Sequelize.HasManyHasAssociationsMixin<UsersToRegions, UsersToRegionsId>;
    countUsers_to_regions!: Sequelize.HasManyCountAssociationsMixin;
    // Users hasMany UsersToUserGroups via user_id
    users_to_user_groups!: UsersToUserGroups[];
    getUsers_to_user_groups!: Sequelize.HasManyGetAssociationsMixin<UsersToUserGroups>;
    setUsers_to_user_groups!: Sequelize.HasManySetAssociationsMixin<UsersToUserGroups, UsersToUserGroupsId>;
    addUsers_to_user_group!: Sequelize.HasManyAddAssociationMixin<UsersToUserGroups, UsersToUserGroupsId>;
    addUsers_to_user_groups!: Sequelize.HasManyAddAssociationsMixin<UsersToUserGroups, UsersToUserGroupsId>;
    createUsers_to_user_group!: Sequelize.HasManyCreateAssociationMixin<UsersToUserGroups>;
    removeUsers_to_user_group!: Sequelize.HasManyRemoveAssociationMixin<UsersToUserGroups, UsersToUserGroupsId>;
    removeUsers_to_user_groups!: Sequelize.HasManyRemoveAssociationsMixin<UsersToUserGroups, UsersToUserGroupsId>;
    hasUsers_to_user_group!: Sequelize.HasManyHasAssociationMixin<UsersToUserGroups, UsersToUserGroupsId>;
    hasUsers_to_user_groups!: Sequelize.HasManyHasAssociationsMixin<UsersToUserGroups, UsersToUserGroupsId>;
    countUsers_to_user_groups!: Sequelize.HasManyCountAssociationsMixin;
    // Users hasMany UsersToUsers via main_user_id
    users_to_users!: UsersToUsers[];
    getUsers_to_users!: Sequelize.HasManyGetAssociationsMixin<UsersToUsers>;
    setUsers_to_users!: Sequelize.HasManySetAssociationsMixin<UsersToUsers, UsersToUsersId>;
    addUsers_to_user!: Sequelize.HasManyAddAssociationMixin<UsersToUsers, UsersToUsersId>;
    addUsers_to_users!: Sequelize.HasManyAddAssociationsMixin<UsersToUsers, UsersToUsersId>;
    createUsers_to_user!: Sequelize.HasManyCreateAssociationMixin<UsersToUsers>;
    removeUsers_to_user!: Sequelize.HasManyRemoveAssociationMixin<UsersToUsers, UsersToUsersId>;
    removeUsers_to_users!: Sequelize.HasManyRemoveAssociationsMixin<UsersToUsers, UsersToUsersId>;
    hasUsers_to_user!: Sequelize.HasManyHasAssociationMixin<UsersToUsers, UsersToUsersId>;
    hasUsers_to_users!: Sequelize.HasManyHasAssociationsMixin<UsersToUsers, UsersToUsersId>;
    countUsers_to_users!: Sequelize.HasManyCountAssociationsMixin;
    // Users hasMany UsersToUsers via related_user_id
    related_user_users_to_users!: UsersToUsers[];
    getRelated_user_users_to_users!: Sequelize.HasManyGetAssociationsMixin<UsersToUsers>;
    setRelated_user_users_to_users!: Sequelize.HasManySetAssociationsMixin<UsersToUsers, UsersToUsersId>;
    addRelated_user_users_to_user!: Sequelize.HasManyAddAssociationMixin<UsersToUsers, UsersToUsersId>;
    addRelated_user_users_to_users!: Sequelize.HasManyAddAssociationsMixin<UsersToUsers, UsersToUsersId>;
    createRelated_user_users_to_user!: Sequelize.HasManyCreateAssociationMixin<UsersToUsers>;
    removeRelated_user_users_to_user!: Sequelize.HasManyRemoveAssociationMixin<UsersToUsers, UsersToUsersId>;
    removeRelated_user_users_to_users!: Sequelize.HasManyRemoveAssociationsMixin<UsersToUsers, UsersToUsersId>;
    hasRelated_user_users_to_user!: Sequelize.HasManyHasAssociationMixin<UsersToUsers, UsersToUsersId>;
    hasRelated_user_users_to_users!: Sequelize.HasManyHasAssociationsMixin<UsersToUsers, UsersToUsersId>;
    countRelated_user_users_to_users!: Sequelize.HasManyCountAssociationsMixin;
    // Users hasMany Versions via created_by
    versions!: Versions[];
    getVersions!: Sequelize.HasManyGetAssociationsMixin<Versions>;
    setVersions!: Sequelize.HasManySetAssociationsMixin<Versions, VersionsId>;
    addVersion!: Sequelize.HasManyAddAssociationMixin<Versions, VersionsId>;
    addVersions!: Sequelize.HasManyAddAssociationsMixin<Versions, VersionsId>;
    createVersion!: Sequelize.HasManyCreateAssociationMixin<Versions>;
    removeVersion!: Sequelize.HasManyRemoveAssociationMixin<Versions, VersionsId>;
    removeVersions!: Sequelize.HasManyRemoveAssociationsMixin<Versions, VersionsId>;
    hasVersion!: Sequelize.HasManyHasAssociationMixin<Versions, VersionsId>;
    hasVersions!: Sequelize.HasManyHasAssociationsMixin<Versions, VersionsId>;
    countVersions!: Sequelize.HasManyCountAssociationsMixin;

    static initModel(sequelize: Sequelize.Sequelize): typeof Users {
        return Users.init(
            {
                id: {
                    autoIncrement: true,
                    type: DataTypes.INTEGER.UNSIGNED,
                    allowNull: false,
                    primaryKey: true,
                },
                login: {
                    type: DataTypes.STRING(255),
                    allowNull: false,
                    unique: "UK_users_login",
                },
                password: {
                    type: DataTypes.STRING(255),
                    allowNull: false,
                },
                first_name: {
                    type: DataTypes.STRING(255),
                    allowNull: true,
                },
                last_name: {
                    type: DataTypes.STRING(255),
                    allowNull: true,
                },
                email: {
                    type: DataTypes.STRING(255),
                    allowNull: false,
                },
                company: {
                    type: DataTypes.STRING(255),
                    allowNull: true,
                },
                company_position: {
                    type: DataTypes.STRING(255),
                    allowNull: true,
                },
                created_at: {
                    type: DataTypes.DATE,
                    allowNull: false,
                    defaultValue: Sequelize.Sequelize.literal("CURRENT_TIMESTAMP"),
                },
                updated_by: {
                    type: DataTypes.INTEGER.UNSIGNED,
                    allowNull: true,
                },
                updated_at: {
                    type: DataTypes.DATE,
                    allowNull: true,
                },
                is_active: {
                    type: DataTypes.BOOLEAN,
                    allowNull: true,
                    defaultValue: 0,
                },
                attrs: {
                    type: DataTypes.JSON,
                    allowNull: true,
                },
                password_updated_at: {
                    type: DataTypes.DATE,
                    allowNull: true,
                },
                last_login_at: {
                    type: DataTypes.DATE,
                    allowNull: true,
                },
            },
            {
                sequelize,
                tableName: "users",
                hasTrigger: true,
                timestamps: false,
                indexes: [
                    {
                        name: "PRIMARY",
                        unique: true,
                        using: "BTREE",
                        fields: [{ name: "id" }],
                    },
                    {
                        name: "UK_users_login",
                        unique: true,
                        using: "BTREE",
                        fields: [{ name: "login" }],
                    },
                ],
            }
        );
    }
}
