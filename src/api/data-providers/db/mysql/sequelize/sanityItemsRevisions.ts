import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';

export interface SanityItemsRevisionsAttributes {
  revision_operation_type?: 'insert' | 'update' | 'delete';
  revision_number: number;
  revision_created_at: Date;
  id: number;
  _createdAt: Date;
  _id: string;
  _rev: string;
  _type: number;
  _updatedAt: Date;
  data: object;
  code?: string;
  version_id: number;
  created_at: Date;
  created_by: number;
  updated_at: Date;
  updated_by: number;
}

export type SanityItemsRevisionsPk = "revision_number" | "id";
export type SanityItemsRevisionsId = SanityItemsRevisions[SanityItemsRevisionsPk];
export type SanityItemsRevisionsOptionalAttributes = "revision_operation_type" | "revision_number" | "revision_created_at" | "id" | "_createdAt" | "_updatedAt" | "code" | "created_at" | "updated_at";
export type SanityItemsRevisionsCreationAttributes = Optional<SanityItemsRevisionsAttributes, SanityItemsRevisionsOptionalAttributes>;

export class SanityItemsRevisions extends Model<SanityItemsRevisionsAttributes, SanityItemsRevisionsCreationAttributes> implements SanityItemsRevisionsAttributes {
  revision_operation_type?: 'insert' | 'update' | 'delete';
  revision_number!: number;
  revision_created_at!: Date;
  id!: number;
  _createdAt!: Date;
  _id!: string;
  _rev!: string;
  _type!: number;
  _updatedAt!: Date;
  data!: object;
  code?: string;
  version_id!: number;
  created_at!: Date;
  created_by!: number;
  updated_at!: Date;
  updated_by!: number;


  static initModel(sequelize: Sequelize.Sequelize): typeof SanityItemsRevisions {
    return SanityItemsRevisions.init({
    revision_operation_type: {
      type: DataTypes.ENUM('insert','update','delete'),
      allowNull: true,
      defaultValue: "insert"
    },
    revision_number: {
      autoIncrement: true,
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    revision_created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    _createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    _id: {
      type: DataTypes.STRING(128),
      allowNull: false
    },
    _rev: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    _type: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false
    },
    _updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    data: {
      type: DataTypes.JSON,
      allowNull: false
    },
    code: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    version_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    created_by: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    updated_by: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'sanity_items_revisions',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
          { name: "revision_number" },
        ]
      },
      {
        name: "IDX_sanity_items_revisions_password",
        using: "BTREE",
        fields: [
          { name: "revision_number" },
        ]
      },
    ]
  });
  }
}
