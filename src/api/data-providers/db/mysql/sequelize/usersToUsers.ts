import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { Users, UsersId } from './users';

export interface UsersToUsersAttributes {
  main_user_id: number;
  related_user_id: number;
}

export type UsersToUsersPk = "main_user_id" | "related_user_id";
export type UsersToUsersId = UsersToUsers[UsersToUsersPk];
export type UsersToUsersOptionalAttributes = "main_user_id" | "related_user_id";
export type UsersToUsersCreationAttributes = Optional<UsersToUsersAttributes, UsersToUsersOptionalAttributes>;

export class UsersToUsers extends Model<UsersToUsersAttributes, UsersToUsersCreationAttributes> implements UsersToUsersAttributes {
  main_user_id!: number;
  related_user_id!: number;

  // UsersToUsers belongsTo Users via main_user_id
  main_user!: Users;
  getMain_user!: Sequelize.BelongsToGetAssociationMixin<Users>;
  setMain_user!: Sequelize.BelongsToSetAssociationMixin<Users, UsersId>;
  createMain_user!: Sequelize.BelongsToCreateAssociationMixin<Users>;
  // UsersToUsers belongsTo Users via related_user_id
  related_user!: Users;
  getRelated_user!: Sequelize.BelongsToGetAssociationMixin<Users>;
  setRelated_user!: Sequelize.BelongsToSetAssociationMixin<Users, UsersId>;
  createRelated_user!: Sequelize.BelongsToCreateAssociationMixin<Users>;

  static initModel(sequelize: Sequelize.Sequelize): typeof UsersToUsers {
    return UsersToUsers.init({
    main_user_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    related_user_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    sequelize,
    tableName: 'users_to_users',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "main_user_id" },
          { name: "related_user_id" },
        ]
      },
      {
        name: "FK_users_to_users_related_user_id",
        using: "BTREE",
        fields: [
          { name: "related_user_id" },
        ]
      },
    ]
  });
  }
}
