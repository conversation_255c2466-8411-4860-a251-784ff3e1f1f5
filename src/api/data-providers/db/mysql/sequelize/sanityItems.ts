import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { OptimizedImages, OptimizedImagesId } from './optimizedImages';
import type { SanityTypes, SanityTypesId } from './sanityTypes';
import type { Users, UsersId } from './users';
import type { Versions, VersionsId } from './versions';

export interface SanityItemsAttributes {
  id: number;
  _createdAt: Date;
  _id: string;
  _rev: string;
  _type: number;
  _updatedAt: Date;
  data: object;
  code?: string;
  version_id: number;
  created_at: Date;
  created_by: number;
  updated_at: Date;
  updated_by: number;
}

export type SanityItemsPk = "id";
export type SanityItemsId = SanityItems[SanityItemsPk];
export type SanityItemsOptionalAttributes = "id" | "_createdAt" | "_updatedAt" | "code" | "created_at" | "updated_at";
export type SanityItemsCreationAttributes = Optional<SanityItemsAttributes, SanityItemsOptionalAttributes>;

export class SanityItems extends Model<SanityItemsAttributes, SanityItemsCreationAttributes> implements SanityItemsAttributes {
  id!: number;
  _createdAt!: Date;
  _id!: string;
  _rev!: string;
  _type!: number;
  _updatedAt!: Date;
  data!: object;
  code?: string;
  version_id!: number;
  created_at!: Date;
  created_by!: number;
  updated_at!: Date;
  updated_by!: number;

  // SanityItems hasMany OptimizedImages via sanity_item_id
  optimized_images!: OptimizedImages[];
  getOptimized_images!: Sequelize.HasManyGetAssociationsMixin<OptimizedImages>;
  setOptimized_images!: Sequelize.HasManySetAssociationsMixin<OptimizedImages, OptimizedImagesId>;
  addOptimized_image!: Sequelize.HasManyAddAssociationMixin<OptimizedImages, OptimizedImagesId>;
  addOptimized_images!: Sequelize.HasManyAddAssociationsMixin<OptimizedImages, OptimizedImagesId>;
  createOptimized_image!: Sequelize.HasManyCreateAssociationMixin<OptimizedImages>;
  removeOptimized_image!: Sequelize.HasManyRemoveAssociationMixin<OptimizedImages, OptimizedImagesId>;
  removeOptimized_images!: Sequelize.HasManyRemoveAssociationsMixin<OptimizedImages, OptimizedImagesId>;
  hasOptimized_image!: Sequelize.HasManyHasAssociationMixin<OptimizedImages, OptimizedImagesId>;
  hasOptimized_images!: Sequelize.HasManyHasAssociationsMixin<OptimizedImages, OptimizedImagesId>;
  countOptimized_images!: Sequelize.HasManyCountAssociationsMixin;
  // SanityItems belongsTo SanityTypes via _type
  _type_sanity_type!: SanityTypes;
  get_type_sanity_type!: Sequelize.BelongsToGetAssociationMixin<SanityTypes>;
  set_type_sanity_type!: Sequelize.BelongsToSetAssociationMixin<SanityTypes, SanityTypesId>;
  create_type_sanity_type!: Sequelize.BelongsToCreateAssociationMixin<SanityTypes>;
  // SanityItems belongsTo Users via created_by
  created_by_user!: Users;
  getCreated_by_user!: Sequelize.BelongsToGetAssociationMixin<Users>;
  setCreated_by_user!: Sequelize.BelongsToSetAssociationMixin<Users, UsersId>;
  createCreated_by_user!: Sequelize.BelongsToCreateAssociationMixin<Users>;
  // SanityItems belongsTo Users via updated_by
  updated_by_user!: Users;
  getUpdated_by_user!: Sequelize.BelongsToGetAssociationMixin<Users>;
  setUpdated_by_user!: Sequelize.BelongsToSetAssociationMixin<Users, UsersId>;
  createUpdated_by_user!: Sequelize.BelongsToCreateAssociationMixin<Users>;
  // SanityItems belongsTo Versions via version_id
  version!: Versions;
  getVersion!: Sequelize.BelongsToGetAssociationMixin<Versions>;
  setVersion!: Sequelize.BelongsToSetAssociationMixin<Versions, VersionsId>;
  createVersion!: Sequelize.BelongsToCreateAssociationMixin<Versions>;

  static initModel(sequelize: Sequelize.Sequelize): typeof SanityItems {
    return SanityItems.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    _createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    _id: {
      type: DataTypes.STRING(128),
      allowNull: false
    },
    _rev: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    _type: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      references: {
        model: 'sanity_types',
        key: 'id'
      }
    },
    _updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    data: {
      type: DataTypes.JSON,
      allowNull: false
    },
    code: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    version_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      references: {
        model: 'versions',
        key: 'id'
      }
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    created_by: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    updated_by: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    sequelize,
    tableName: 'sanity_items',
    hasTrigger: true,
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
      {
        name: "UK_sanityitems",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "_id" },
          { name: "_type" },
        ]
      },
      {
        name: "FK_sanity_items__type",
        using: "BTREE",
        fields: [
          { name: "_type" },
        ]
      },
      {
        name: "FK_sanity_items_created_by",
        using: "BTREE",
        fields: [
          { name: "created_by" },
        ]
      },
      {
        name: "FK_sanity_items_updated_by",
        using: "BTREE",
        fields: [
          { name: "updated_by" },
        ]
      },
      {
        name: "FK_sanity_items_version_id",
        using: "BTREE",
        fields: [
          { name: "version_id" },
        ]
      },
    ]
  });
  }
}
