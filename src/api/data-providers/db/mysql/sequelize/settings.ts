import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';

export interface SettingsAttributes {
  key: string;
  value: object;
  created_at: Date;
  updated_at: Date;
}

export type SettingsPk = "key";
export type SettingsId = Settings[SettingsPk];
export type SettingsOptionalAttributes = "key" | "created_at" | "updated_at";
export type SettingsCreationAttributes = Optional<SettingsAttributes, SettingsOptionalAttributes>;

export class Settings extends Model<SettingsAttributes, SettingsCreationAttributes> implements SettingsAttributes {
  key!: string;
  value!: object;
  created_at!: Date;
  updated_at!: Date;


  static initModel(sequelize: Sequelize.Sequelize): typeof Settings {
    return Settings.init({
    key: {
      type: DataTypes.STRING(255),
      allowNull: false,
      primaryKey: true
    },
    value: {
      type: DataTypes.JSON,
      allowNull: false
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    }
  }, {
    sequelize,
    tableName: 'settings',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "key" },
        ]
      },
    ]
  });
  }
}
