import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { UserGroups, UserGroupsId } from './userGroups';
import type { Users, UsersId } from './users';

export interface UsersToUserGroupsAttributes {
  user_id: number;
  user_group_id: number;
}

export type UsersToUserGroupsPk = "user_id" | "user_group_id";
export type UsersToUserGroupsId = UsersToUserGroups[UsersToUserGroupsPk];
export type UsersToUserGroupsOptionalAttributes = "user_id" | "user_group_id";
export type UsersToUserGroupsCreationAttributes = Optional<UsersToUserGroupsAttributes, UsersToUserGroupsOptionalAttributes>;

export class UsersToUserGroups extends Model<UsersToUserGroupsAttributes, UsersToUserGroupsCreationAttributes> implements UsersToUserGroupsAttributes {
  user_id!: number;
  user_group_id!: number;

  // UsersToUserGroups belongsTo UserGroups via user_group_id
  user_group!: UserGroups;
  getUser_group!: Sequelize.BelongsToGetAssociationMixin<UserGroups>;
  setUser_group!: Sequelize.BelongsToSetAssociationMixin<UserGroups, UserGroupsId>;
  createUser_group!: Sequelize.BelongsToCreateAssociationMixin<UserGroups>;
  // UsersToUserGroups belongsTo Users via user_id
  user!: Users;
  getUser!: Sequelize.BelongsToGetAssociationMixin<Users>;
  setUser!: Sequelize.BelongsToSetAssociationMixin<Users, UsersId>;
  createUser!: Sequelize.BelongsToCreateAssociationMixin<Users>;

  static initModel(sequelize: Sequelize.Sequelize): typeof UsersToUserGroups {
    return UsersToUserGroups.init({
    user_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    user_group_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true,
      references: {
        model: 'user_groups',
        key: 'id'
      }
    }
  }, {
    sequelize,
    tableName: 'users_to_user_groups',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "user_id" },
          { name: "user_group_id" },
        ]
      },
      {
        name: "users_to_user_groups_FK_1",
        using: "BTREE",
        fields: [
          { name: "user_group_id" },
        ]
      },
    ]
  });
  }
}
