import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { UserPlayers, UserPlayersId } from './userPlayers';

export interface GameServersAttributes {
  id: number;
  domain_url: string;
  entity_path: string;
  server_type: 'develop' | 'staging' | 'production';
  secret_key: string;
  user_name: string;
  attrs?: object;
  is_active: number;
  created_at: Date;
  updated_at?: Date;
}

export type GameServersPk = "id";
export type GameServersId = GameServers[GameServersPk];
export type GameServersOptionalAttributes = "id" | "attrs" | "created_at" | "updated_at";
export type GameServersCreationAttributes = Optional<GameServersAttributes, GameServersOptionalAttributes>;

export class GameServers extends Model<GameServersAttributes, GameServersCreationAttributes> implements GameServersAttributes {
  id!: number;
  domain_url!: string;
  entity_path!: string;
  server_type!: 'develop' | 'staging' | 'production';
  secret_key!: string;
  user_name!: string;
  attrs?: object;
  is_active!: number;
  created_at!: Date;
  updated_at?: Date;

  // GameServers hasMany UserPlayers via game_server_id
  user_players!: UserPlayers[];
  getUser_players!: Sequelize.HasManyGetAssociationsMixin<UserPlayers>;
  setUser_players!: Sequelize.HasManySetAssociationsMixin<UserPlayers, UserPlayersId>;
  addUser_player!: Sequelize.HasManyAddAssociationMixin<UserPlayers, UserPlayersId>;
  addUser_players!: Sequelize.HasManyAddAssociationsMixin<UserPlayers, UserPlayersId>;
  createUser_player!: Sequelize.HasManyCreateAssociationMixin<UserPlayers>;
  removeUser_player!: Sequelize.HasManyRemoveAssociationMixin<UserPlayers, UserPlayersId>;
  removeUser_players!: Sequelize.HasManyRemoveAssociationsMixin<UserPlayers, UserPlayersId>;
  hasUser_player!: Sequelize.HasManyHasAssociationMixin<UserPlayers, UserPlayersId>;
  hasUser_players!: Sequelize.HasManyHasAssociationsMixin<UserPlayers, UserPlayersId>;
  countUser_players!: Sequelize.HasManyCountAssociationsMixin;

  static initModel(sequelize: Sequelize.Sequelize): typeof GameServers {
    return GameServers.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    domain_url: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    entity_path: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    server_type: {
      type: DataTypes.ENUM('develop','staging','production'),
      allowNull: false
    },
    secret_key: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    user_name: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    attrs: {
      type: DataTypes.JSON,
      allowNull: true
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'game_servers',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
  }
}
