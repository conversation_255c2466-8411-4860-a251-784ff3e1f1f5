import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { Users, UsersId } from './users';

export interface UserActivationAttributes {
  user_id: number;
  activation_hash: string;
  created_at: Date;
  updated_at?: Date;
  number_of_attempts: number;
  is_activated?: number;
}

export type UserActivationPk = "user_id";
export type UserActivationId = UserActivation[UserActivationPk];
export type UserActivationOptionalAttributes = "user_id" | "created_at" | "updated_at" | "number_of_attempts" | "is_activated";
export type UserActivationCreationAttributes = Optional<UserActivationAttributes, UserActivationOptionalAttributes>;

export class UserActivation extends Model<UserActivationAttributes, UserActivationCreationAttributes> implements UserActivationAttributes {
  user_id!: number;
  activation_hash!: string;
  created_at!: Date;
  updated_at?: Date;
  number_of_attempts!: number;
  is_activated?: number;

  // UserActivation belongsTo Users via user_id
  user!: Users;
  getUser!: Sequelize.BelongsToGetAssociationMixin<Users>;
  setUser!: Sequelize.BelongsToSetAssociationMixin<Users, UsersId>;
  createUser!: Sequelize.BelongsToCreateAssociationMixin<Users>;

  static initModel(sequelize: Sequelize.Sequelize): typeof UserActivation {
    return UserActivation.init({
    user_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    activation_hash: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: "UK_user_activation_activation_hash"
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    number_of_attempts: {
      type: DataTypes.TINYINT,
      allowNull: false,
      defaultValue: 0
    },
    is_activated: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: 0
    }
  }, {
    sequelize,
    tableName: 'user_activation',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "user_id" },
        ]
      },
      {
        name: "UK_user_activation_activation_hash",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "activation_hash" },
        ]
      },
    ]
  });
  }
}
