import type { Sequelize } from "sequelize";
import { BucketResources as _BucketResources } from "./bucketResources";
import type { BucketResourcesAttributes, BucketResourcesCreationAttributes } from "./bucketResources";
import { DeployLog as _DeployLog } from "./deployLog";
import type { DeployLogAttributes, DeployLogCreationAttributes } from "./deployLog";
import { GameInfo as _GameInfo } from "./gameInfo";
import type { GameInfoAttributes, GameInfoCreationAttributes } from "./gameInfo";
import { GameServers as _GameServers } from "./gameServers";
import type { GameServersAttributes, GameServersCreationAttributes } from "./gameServers";
import { Logs as _Logs } from "./logs";
import type { LogsAttributes, LogsCreationAttributes } from "./logs";
import { MarketingMaterials as _MarketingMaterials } from "./marketingMaterials";
import type { MarketingMaterialsAttributes, MarketingMaterialsCreationAttributes } from "./marketingMaterials";
import { OptimizedImages as _OptimizedImages } from "./optimizedImages";
import type { OptimizedImagesAttributes, OptimizedImagesCreationAttributes } from "./optimizedImages";
import { ProviderGames as _ProviderGames } from "./providerGames";
import type { ProviderGamesAttributes, ProviderGamesCreationAttributes } from "./providerGames";
import { Regions as _Regions } from "./regions";
import type { RegionsAttributes, RegionsCreationAttributes } from "./regions";
import { SanityItems as _SanityItems } from "./sanityItems";
import type { SanityItemsAttributes, SanityItemsCreationAttributes } from "./sanityItems";
import { SanityItemsRevisions as _SanityItemsRevisions } from "./sanityItemsRevisions";
import type { SanityItemsRevisionsAttributes, SanityItemsRevisionsCreationAttributes } from "./sanityItemsRevisions";
import { SanityTypes as _SanityTypes } from "./sanityTypes";
import type { SanityTypesAttributes, SanityTypesCreationAttributes } from "./sanityTypes";
import { SanityTypesRevisions as _SanityTypesRevisions } from "./sanityTypesRevisions";
import type { SanityTypesRevisionsAttributes, SanityTypesRevisionsCreationAttributes } from "./sanityTypesRevisions";
import { Sessions as _Sessions } from "./sessions";
import type { SessionsAttributes, SessionsCreationAttributes } from "./sessions";
import { Settings as _Settings } from "./settings";
import type { SettingsAttributes, SettingsCreationAttributes } from "./settings";
import { UserActivation as _UserActivation } from "./userActivation";
import type { UserActivationAttributes, UserActivationCreationAttributes } from "./userActivation";
import { UserActivity as _UserActivity } from "./userActivity";
import type { UserActivityAttributes, UserActivityCreationAttributes } from "./userActivity";
import { UserBrowserAgents as _UserBrowserAgents } from "./userBrowserAgents";
import type { UserBrowserAgentsAttributes, UserBrowserAgentsCreationAttributes } from "./userBrowserAgents";
import { UserGroups as _UserGroups } from "./userGroups";
import type { UserGroupsAttributes, UserGroupsCreationAttributes } from "./userGroups";
import { UserPermissions as _UserPermissions } from "./userPermissions";
import type { UserPermissionsAttributes, UserPermissionsCreationAttributes } from "./userPermissions";
import { UserPermissionsToUserGroups as _UserPermissionsToUserGroups } from "./userPermissionsToUserGroups";
import type { UserPermissionsToUserGroupsAttributes, UserPermissionsToUserGroupsCreationAttributes } from "./userPermissionsToUserGroups";
import { UserPlayers as _UserPlayers } from "./userPlayers";
import type { UserPlayersAttributes, UserPlayersCreationAttributes } from "./userPlayers";
import { UserTokens as _UserTokens } from "./userTokens";
import type { UserTokensAttributes, UserTokensCreationAttributes } from "./userTokens";
import { Users as _Users } from "./users";
import type { UsersAttributes, UsersCreationAttributes } from "./users";
import { UsersRevisions as _UsersRevisions } from "./usersRevisions";
import type { UsersRevisionsAttributes, UsersRevisionsCreationAttributes } from "./usersRevisions";
import { UsersToRegions as _UsersToRegions } from "./usersToRegions";
import type { UsersToRegionsAttributes, UsersToRegionsCreationAttributes } from "./usersToRegions";
import { UsersToUserGroups as _UsersToUserGroups } from "./usersToUserGroups";
import type { UsersToUserGroupsAttributes, UsersToUserGroupsCreationAttributes } from "./usersToUserGroups";
import { UsersToUsers as _UsersToUsers } from "./usersToUsers";
import type { UsersToUsersAttributes, UsersToUsersCreationAttributes } from "./usersToUsers";
import { Versions as _Versions } from "./versions";
import type { VersionsAttributes, VersionsCreationAttributes } from "./versions";

export {
 _BucketResources as BucketResources,
 _DeployLog as DeployLog,
 _GameInfo as GameInfo,
 _GameServers as GameServers,
 _Logs as Logs,
 _MarketingMaterials as MarketingMaterials,
 _OptimizedImages as OptimizedImages,
 _ProviderGames as ProviderGames,
 _Regions as Regions,
 _SanityItems as SanityItems,
 _SanityItemsRevisions as SanityItemsRevisions,
 _SanityTypes as SanityTypes,
 _SanityTypesRevisions as SanityTypesRevisions,
 _Sessions as Sessions,
 _Settings as Settings,
 _UserActivation as UserActivation,
 _UserActivity as UserActivity,
 _UserBrowserAgents as UserBrowserAgents,
 _UserGroups as UserGroups,
 _UserPermissions as UserPermissions,
 _UserPermissionsToUserGroups as UserPermissionsToUserGroups,
 _UserPlayers as UserPlayers,
 _UserTokens as UserTokens,
 _Users as Users,
 _UsersRevisions as UsersRevisions,
 _UsersToRegions as UsersToRegions,
 _UsersToUserGroups as UsersToUserGroups,
 _UsersToUsers as UsersToUsers,
 _Versions as Versions,
};

export type {
  BucketResourcesAttributes,
  BucketResourcesCreationAttributes,
  DeployLogAttributes,
  DeployLogCreationAttributes,
  GameInfoAttributes,
  GameInfoCreationAttributes,
  GameServersAttributes,
  GameServersCreationAttributes,
  LogsAttributes,
  LogsCreationAttributes,
  MarketingMaterialsAttributes,
  MarketingMaterialsCreationAttributes,
  OptimizedImagesAttributes,
  OptimizedImagesCreationAttributes,
  ProviderGamesAttributes,
  ProviderGamesCreationAttributes,
  RegionsAttributes,
  RegionsCreationAttributes,
  SanityItemsAttributes,
  SanityItemsCreationAttributes,
  SanityItemsRevisionsAttributes,
  SanityItemsRevisionsCreationAttributes,
  SanityTypesAttributes,
  SanityTypesCreationAttributes,
  SanityTypesRevisionsAttributes,
  SanityTypesRevisionsCreationAttributes,
  SessionsAttributes,
  SessionsCreationAttributes,
  SettingsAttributes,
  SettingsCreationAttributes,
  UserActivationAttributes,
  UserActivationCreationAttributes,
  UserActivityAttributes,
  UserActivityCreationAttributes,
  UserBrowserAgentsAttributes,
  UserBrowserAgentsCreationAttributes,
  UserGroupsAttributes,
  UserGroupsCreationAttributes,
  UserPermissionsAttributes,
  UserPermissionsCreationAttributes,
  UserPermissionsToUserGroupsAttributes,
  UserPermissionsToUserGroupsCreationAttributes,
  UserPlayersAttributes,
  UserPlayersCreationAttributes,
  UserTokensAttributes,
  UserTokensCreationAttributes,
  UsersAttributes,
  UsersCreationAttributes,
  UsersRevisionsAttributes,
  UsersRevisionsCreationAttributes,
  UsersToRegionsAttributes,
  UsersToRegionsCreationAttributes,
  UsersToUserGroupsAttributes,
  UsersToUserGroupsCreationAttributes,
  UsersToUsersAttributes,
  UsersToUsersCreationAttributes,
  VersionsAttributes,
  VersionsCreationAttributes,
};

export function initModels(sequelize: Sequelize) {
  const BucketResources = _BucketResources.initModel(sequelize);
  const DeployLog = _DeployLog.initModel(sequelize);
  const GameInfo = _GameInfo.initModel(sequelize);
  const GameServers = _GameServers.initModel(sequelize);
  const Logs = _Logs.initModel(sequelize);
  const MarketingMaterials = _MarketingMaterials.initModel(sequelize);
  const OptimizedImages = _OptimizedImages.initModel(sequelize);
  const ProviderGames = _ProviderGames.initModel(sequelize);
  const Regions = _Regions.initModel(sequelize);
  const SanityItems = _SanityItems.initModel(sequelize);
  const SanityItemsRevisions = _SanityItemsRevisions.initModel(sequelize);
  const SanityTypes = _SanityTypes.initModel(sequelize);
  const SanityTypesRevisions = _SanityTypesRevisions.initModel(sequelize);
  const Sessions = _Sessions.initModel(sequelize);
  const Settings = _Settings.initModel(sequelize);
  const UserActivation = _UserActivation.initModel(sequelize);
  const UserActivity = _UserActivity.initModel(sequelize);
  const UserBrowserAgents = _UserBrowserAgents.initModel(sequelize);
  const UserGroups = _UserGroups.initModel(sequelize);
  const UserPermissions = _UserPermissions.initModel(sequelize);
  const UserPermissionsToUserGroups = _UserPermissionsToUserGroups.initModel(sequelize);
  const UserPlayers = _UserPlayers.initModel(sequelize);
  const UserTokens = _UserTokens.initModel(sequelize);
  const Users = _Users.initModel(sequelize);
  const UsersRevisions = _UsersRevisions.initModel(sequelize);
  const UsersToRegions = _UsersToRegions.initModel(sequelize);
  const UsersToUserGroups = _UsersToUserGroups.initModel(sequelize);
  const UsersToUsers = _UsersToUsers.initModel(sequelize);
  const Versions = _Versions.initModel(sequelize);

  Regions.belongsToMany(Users, { as: 'user_id_users', through: UsersToRegions, foreignKey: "region_id", otherKey: "user_id" });
  UserGroups.belongsToMany(UserPermissions, { as: 'permission_code_user_permissions', through: UserPermissionsToUserGroups, foreignKey: "user_group_id", otherKey: "permission_code" });
  UserGroups.belongsToMany(Users, { as: 'user_id_users_users_to_user_groups', through: UsersToUserGroups, foreignKey: "user_group_id", otherKey: "user_id" });
  UserPermissions.belongsToMany(UserGroups, { as: 'user_group_id_user_groups', through: UserPermissionsToUserGroups, foreignKey: "permission_code", otherKey: "user_group_id" });
  Users.belongsToMany(Regions, { as: 'region_id_regions', through: UsersToRegions, foreignKey: "user_id", otherKey: "region_id" });
  Users.belongsToMany(UserGroups, { as: 'user_group_id_user_groups_users_to_user_groups', through: UsersToUserGroups, foreignKey: "user_id", otherKey: "user_group_id" });
  Users.belongsToMany(Users, { as: 'related_user_id_users', through: UsersToUsers, foreignKey: "main_user_id", otherKey: "related_user_id" });
  Users.belongsToMany(Users, { as: 'main_user_id_users', through: UsersToUsers, foreignKey: "related_user_id", otherKey: "main_user_id" });
  UserPlayers.belongsTo(GameServers, { as: "game_server", foreignKey: "game_server_id"});
  GameServers.hasMany(UserPlayers, { as: "user_players", foreignKey: "game_server_id"});
  UsersToRegions.belongsTo(Regions, { as: "region", foreignKey: "region_id"});
  Regions.hasMany(UsersToRegions, { as: "users_to_regions", foreignKey: "region_id"});
  OptimizedImages.belongsTo(SanityItems, { as: "sanity_item", foreignKey: "sanity_item_id"});
  SanityItems.hasMany(OptimizedImages, { as: "optimized_images", foreignKey: "sanity_item_id"});
  SanityItems.belongsTo(SanityTypes, { as: "_type_sanity_type", foreignKey: "_type"});
  SanityTypes.hasMany(SanityItems, { as: "sanity_items", foreignKey: "_type"});
  UserActivity.belongsTo(UserBrowserAgents, { as: "user_agent", foreignKey: "user_agent_id"});
  UserBrowserAgents.hasMany(UserActivity, { as: "user_activities", foreignKey: "user_agent_id"});
  UserTokens.belongsTo(UserBrowserAgents, { as: "user_agent", foreignKey: "user_agent_id"});
  UserBrowserAgents.hasMany(UserTokens, { as: "user_tokens", foreignKey: "user_agent_id"});
  UserPermissionsToUserGroups.belongsTo(UserGroups, { as: "user_group", foreignKey: "user_group_id"});
  UserGroups.hasMany(UserPermissionsToUserGroups, { as: "user_permissions_to_user_groups", foreignKey: "user_group_id"});
  UsersToUserGroups.belongsTo(UserGroups, { as: "user_group", foreignKey: "user_group_id"});
  UserGroups.hasMany(UsersToUserGroups, { as: "users_to_user_groups", foreignKey: "user_group_id"});
  UserPermissionsToUserGroups.belongsTo(UserPermissions, { as: "permission_code_user_permission", foreignKey: "permission_code"});
  UserPermissions.hasMany(UserPermissionsToUserGroups, { as: "user_permissions_to_user_groups", foreignKey: "permission_code"});
  BucketResources.belongsTo(Users, { as: "uploaded_by_user", foreignKey: "uploaded_by"});
  Users.hasMany(BucketResources, { as: "bucket_resources", foreignKey: "uploaded_by"});
  SanityItems.belongsTo(Users, { as: "created_by_user", foreignKey: "created_by"});
  Users.hasMany(SanityItems, { as: "sanity_items", foreignKey: "created_by"});
  SanityItems.belongsTo(Users, { as: "updated_by_user", foreignKey: "updated_by"});
  Users.hasMany(SanityItems, { as: "updated_by_sanity_items", foreignKey: "updated_by"});
  UserActivation.belongsTo(Users, { as: "user", foreignKey: "user_id"});
  Users.hasOne(UserActivation, { as: "user_activation", foreignKey: "user_id"});
  UserActivity.belongsTo(Users, { as: "user", foreignKey: "user_id"});
  Users.hasMany(UserActivity, { as: "user_activities", foreignKey: "user_id"});
  UserPlayers.belongsTo(Users, { as: "user", foreignKey: "user_id"});
  Users.hasMany(UserPlayers, { as: "user_players", foreignKey: "user_id"});
  UserTokens.belongsTo(Users, { as: "user", foreignKey: "user_id"});
  Users.hasOne(UserTokens, { as: "user_token", foreignKey: "user_id"});
  UsersToRegions.belongsTo(Users, { as: "user", foreignKey: "user_id"});
  Users.hasMany(UsersToRegions, { as: "users_to_regions", foreignKey: "user_id"});
  UsersToUserGroups.belongsTo(Users, { as: "user", foreignKey: "user_id"});
  Users.hasMany(UsersToUserGroups, { as: "users_to_user_groups", foreignKey: "user_id"});
  UsersToUsers.belongsTo(Users, { as: "main_user", foreignKey: "main_user_id"});
  Users.hasMany(UsersToUsers, { as: "users_to_users", foreignKey: "main_user_id"});
  UsersToUsers.belongsTo(Users, { as: "related_user", foreignKey: "related_user_id"});
  Users.hasMany(UsersToUsers, { as: "related_user_users_to_users", foreignKey: "related_user_id"});
  Versions.belongsTo(Users, { as: "created_by_user", foreignKey: "created_by"});
  Users.hasMany(Versions, { as: "versions", foreignKey: "created_by"});
  DeployLog.belongsTo(Versions, { as: "version", foreignKey: "version_id"});
  Versions.hasMany(DeployLog, { as: "deploy_logs", foreignKey: "version_id"});
  SanityItems.belongsTo(Versions, { as: "version", foreignKey: "version_id"});
  Versions.hasMany(SanityItems, { as: "sanity_items", foreignKey: "version_id"});
  SanityTypes.belongsTo(Versions, { as: "version", foreignKey: "version_id"});
  Versions.hasMany(SanityTypes, { as: "sanity_types", foreignKey: "version_id"});

  return {
    BucketResources: BucketResources,
    DeployLog: DeployLog,
    GameInfo: GameInfo,
    GameServers: GameServers,
    Logs: Logs,
    MarketingMaterials: MarketingMaterials,
    OptimizedImages: OptimizedImages,
    ProviderGames: ProviderGames,
    Regions: Regions,
    SanityItems: SanityItems,
    SanityItemsRevisions: SanityItemsRevisions,
    SanityTypes: SanityTypes,
    SanityTypesRevisions: SanityTypesRevisions,
    Sessions: Sessions,
    Settings: Settings,
    UserActivation: UserActivation,
    UserActivity: UserActivity,
    UserBrowserAgents: UserBrowserAgents,
    UserGroups: UserGroups,
    UserPermissions: UserPermissions,
    UserPermissionsToUserGroups: UserPermissionsToUserGroups,
    UserPlayers: UserPlayers,
    UserTokens: UserTokens,
    Users: Users,
    UsersRevisions: UsersRevisions,
    UsersToRegions: UsersToRegions,
    UsersToUserGroups: UsersToUserGroups,
    UsersToUsers: UsersToUsers,
    Versions: Versions,
  };
}
