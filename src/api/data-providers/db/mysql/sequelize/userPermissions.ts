import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { UserGroups, UserGroupsId } from './userGroups';
import type { UserPermissionsToUserGroups, UserPermissionsToUserGroupsId } from './userPermissionsToUserGroups';

export interface UserPermissionsAttributes {
  code: string;
  full_name?: string;
  description?: string;
}

export type UserPermissionsPk = "code";
export type UserPermissionsId = UserPermissions[UserPermissionsPk];
export type UserPermissionsOptionalAttributes = "code" | "full_name" | "description";
export type UserPermissionsCreationAttributes = Optional<UserPermissionsAttributes, UserPermissionsOptionalAttributes>;

export class UserPermissions extends Model<UserPermissionsAttributes, UserPermissionsCreationAttributes> implements UserPermissionsAttributes {
  code!: string;
  full_name?: string;
  description?: string;

  // UserPermissions belongsToMany UserGroups via permission_code and user_group_id
  user_group_id_user_groups!: UserGroups[];
  getUser_group_id_user_groups!: Sequelize.BelongsToManyGetAssociationsMixin<UserGroups>;
  setUser_group_id_user_groups!: Sequelize.BelongsToManySetAssociationsMixin<UserGroups, UserGroupsId>;
  addUser_group_id_user_group!: Sequelize.BelongsToManyAddAssociationMixin<UserGroups, UserGroupsId>;
  addUser_group_id_user_groups!: Sequelize.BelongsToManyAddAssociationsMixin<UserGroups, UserGroupsId>;
  createUser_group_id_user_group!: Sequelize.BelongsToManyCreateAssociationMixin<UserGroups>;
  removeUser_group_id_user_group!: Sequelize.BelongsToManyRemoveAssociationMixin<UserGroups, UserGroupsId>;
  removeUser_group_id_user_groups!: Sequelize.BelongsToManyRemoveAssociationsMixin<UserGroups, UserGroupsId>;
  hasUser_group_id_user_group!: Sequelize.BelongsToManyHasAssociationMixin<UserGroups, UserGroupsId>;
  hasUser_group_id_user_groups!: Sequelize.BelongsToManyHasAssociationsMixin<UserGroups, UserGroupsId>;
  countUser_group_id_user_groups!: Sequelize.BelongsToManyCountAssociationsMixin;
  // UserPermissions hasMany UserPermissionsToUserGroups via permission_code
  user_permissions_to_user_groups!: UserPermissionsToUserGroups[];
  getUser_permissions_to_user_groups!: Sequelize.HasManyGetAssociationsMixin<UserPermissionsToUserGroups>;
  setUser_permissions_to_user_groups!: Sequelize.HasManySetAssociationsMixin<UserPermissionsToUserGroups, UserPermissionsToUserGroupsId>;
  addUser_permissions_to_user_group!: Sequelize.HasManyAddAssociationMixin<UserPermissionsToUserGroups, UserPermissionsToUserGroupsId>;
  addUser_permissions_to_user_groups!: Sequelize.HasManyAddAssociationsMixin<UserPermissionsToUserGroups, UserPermissionsToUserGroupsId>;
  createUser_permissions_to_user_group!: Sequelize.HasManyCreateAssociationMixin<UserPermissionsToUserGroups>;
  removeUser_permissions_to_user_group!: Sequelize.HasManyRemoveAssociationMixin<UserPermissionsToUserGroups, UserPermissionsToUserGroupsId>;
  removeUser_permissions_to_user_groups!: Sequelize.HasManyRemoveAssociationsMixin<UserPermissionsToUserGroups, UserPermissionsToUserGroupsId>;
  hasUser_permissions_to_user_group!: Sequelize.HasManyHasAssociationMixin<UserPermissionsToUserGroups, UserPermissionsToUserGroupsId>;
  hasUser_permissions_to_user_groups!: Sequelize.HasManyHasAssociationsMixin<UserPermissionsToUserGroups, UserPermissionsToUserGroupsId>;
  countUser_permissions_to_user_groups!: Sequelize.HasManyCountAssociationsMixin;

  static initModel(sequelize: Sequelize.Sequelize): typeof UserPermissions {
    return UserPermissions.init({
    code: {
      type: DataTypes.STRING(64),
      allowNull: false,
      primaryKey: true
    },
    full_name: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    description: {
      type: DataTypes.STRING(255),
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'user_permissions',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "code" },
        ]
      },
    ]
  });
  }
}
