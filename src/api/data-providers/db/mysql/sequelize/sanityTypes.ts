import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { SanityItems, SanityItemsId } from './sanityItems';
import type { Versions, VersionsId } from './versions';

export interface SanityTypesAttributes {
  id: number;
  name: string;
  version_id: number;
}

export type SanityTypesPk = "id";
export type SanityTypesId = SanityTypes[SanityTypesPk];
export type SanityTypesOptionalAttributes = "id";
export type SanityTypesCreationAttributes = Optional<SanityTypesAttributes, SanityTypesOptionalAttributes>;

export class SanityTypes extends Model<SanityTypesAttributes, SanityTypesCreationAttributes> implements SanityTypesAttributes {
  id!: number;
  name!: string;
  version_id!: number;

  // SanityTypes hasMany SanityItems via _type
  sanity_items!: SanityItems[];
  getSanity_items!: Sequelize.HasManyGetAssociationsMixin<SanityItems>;
  setSanity_items!: Sequelize.HasManySetAssociationsMixin<SanityItems, SanityItemsId>;
  addSanity_item!: Sequelize.HasManyAddAssociationMixin<SanityItems, SanityItemsId>;
  addSanity_items!: Sequelize.HasManyAddAssociationsMixin<SanityItems, SanityItemsId>;
  createSanity_item!: Sequelize.HasManyCreateAssociationMixin<SanityItems>;
  removeSanity_item!: Sequelize.HasManyRemoveAssociationMixin<SanityItems, SanityItemsId>;
  removeSanity_items!: Sequelize.HasManyRemoveAssociationsMixin<SanityItems, SanityItemsId>;
  hasSanity_item!: Sequelize.HasManyHasAssociationMixin<SanityItems, SanityItemsId>;
  hasSanity_items!: Sequelize.HasManyHasAssociationsMixin<SanityItems, SanityItemsId>;
  countSanity_items!: Sequelize.HasManyCountAssociationsMixin;
  // SanityTypes belongsTo Versions via version_id
  version!: Versions;
  getVersion!: Sequelize.BelongsToGetAssociationMixin<Versions>;
  setVersion!: Sequelize.BelongsToSetAssociationMixin<Versions, VersionsId>;
  createVersion!: Sequelize.BelongsToCreateAssociationMixin<Versions>;

  static initModel(sequelize: Sequelize.Sequelize): typeof SanityTypes {
    return SanityTypes.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    version_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      references: {
        model: 'versions',
        key: 'id'
      }
    }
  }, {
    sequelize,
    tableName: 'sanity_types',
    hasTrigger: true,
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
      {
        name: "FK_sanity_types_version_id",
        using: "BTREE",
        fields: [
          { name: "version_id" },
        ]
      },
    ]
  });
  }
}
