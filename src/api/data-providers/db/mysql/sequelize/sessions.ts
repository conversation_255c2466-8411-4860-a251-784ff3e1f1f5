import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';

export interface SessionsAttributes {
  sess_id: string;
  sess_data: any;
  sess_time: number;
  sess_lifetime: number;
}

export type SessionsPk = "sess_id";
export type SessionsId = Sessions[SessionsPk];
export type SessionsOptionalAttributes = "sess_id";
export type SessionsCreationAttributes = Optional<SessionsAttributes, SessionsOptionalAttributes>;

export class Sessions extends Model<SessionsAttributes, SessionsCreationAttributes> implements SessionsAttributes {
  sess_id!: string;
  sess_data!: any;
  sess_time!: number;
  sess_lifetime!: number;


  static initModel(sequelize: Sequelize.Sequelize): typeof Sessions {
    return Sessions.init({
    sess_id: {
      type: DataTypes.STRING(128),
      allowNull: false,
      primaryKey: true
    },
    sess_data: {
      type: DataTypes.BLOB,
      allowNull: false
    },
    sess_time: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false
    },
    sess_lifetime: {
      type: DataTypes.MEDIUMINT,
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'sessions',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "sess_id" },
        ]
      },
    ]
  });
  }
}
