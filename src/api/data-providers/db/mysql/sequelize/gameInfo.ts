import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';

export interface GameInfoAttributes {
  id: number;
  game_code: string;
  game_name: string;
  type: string;
  theoretical_rtp?: number;
  house_volatility?: number;
  player_volatility?: number;
  release_date?: string;
  certification?: string;
  project_type?: string;
  grid?: string;
  gametags?: string;
  marketingfeatures?: string;
  max_number_of_lines?: number;
  target_region?: string;
  wrapper_version?: string;
  hit_rate?: number;
  progressive_jackpot?: string;
  branded?: string;
  supports_portrait?: string;
  max_win_multiplier?: number;
  combined_feature_frequency?: number;
  max_exposure?: number;
  default_bet?: number;
  min_bet?: number;
  max_bet?: number;
  jp_contribution?: number;
  supported_languages?: string;
  game_features?: object;
  supported_currencies?: string;
  country_gra_rank?: object;
  target_market?: string;
  release_date_per_market?: object;
}

export type GameInfoPk = "id";
export type GameInfoId = GameInfo[GameInfoPk];
export type GameInfoOptionalAttributes = "id" | "theoretical_rtp" | "house_volatility" | "player_volatility" | "release_date" | "certification" | "project_type" | "grid" | "gametags" | "marketingfeatures" | "max_number_of_lines" | "target_region" | "wrapper_version" | "hit_rate" | "progressive_jackpot" | "branded" | "supports_portrait" | "max_win_multiplier" | "combined_feature_frequency" | "max_exposure" | "default_bet" | "min_bet" | "max_bet" | "jp_contribution" | "supported_languages" | "game_features" | "supported_currencies" | "country_gra_rank" | "target_market" | "release_date_per_market";
export type GameInfoCreationAttributes = Optional<GameInfoAttributes, GameInfoOptionalAttributes>;

export class GameInfo extends Model<GameInfoAttributes, GameInfoCreationAttributes> implements GameInfoAttributes {
  id!: number;
  game_code!: string;
  game_name!: string;
  type!: string;
  theoretical_rtp?: number;
  house_volatility?: number;
  player_volatility?: number;
  release_date?: string;
  certification?: string;
  project_type?: string;
  grid?: string;
  gametags?: string;
  marketingfeatures?: string;
  max_number_of_lines?: number;
  target_region?: string;
  wrapper_version?: string;
  hit_rate?: number;
  progressive_jackpot?: string;
  branded?: string;
  supports_portrait?: string;
  max_win_multiplier?: number;
  combined_feature_frequency?: number;
  max_exposure?: number;
  default_bet?: number;
  min_bet?: number;
  max_bet?: number;
  jp_contribution?: number;
  supported_languages?: string;
  game_features?: object;
  supported_currencies?: string;
  country_gra_rank?: object;
  target_market?: string;
  release_date_per_market?: object;


  static initModel(sequelize: Sequelize.Sequelize): typeof GameInfo {
    return GameInfo.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    game_code: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: "UK_game_info_game_code"
    },
    game_name: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    type: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    theoretical_rtp: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    house_volatility: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    player_volatility: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    release_date: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    certification: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    project_type: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    grid: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    gametags: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    marketingfeatures: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    max_number_of_lines: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: true
    },
    target_region: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    wrapper_version: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    hit_rate: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    progressive_jackpot: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    branded: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    supports_portrait: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    max_win_multiplier: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    combined_feature_frequency: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    max_exposure: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    default_bet: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    min_bet: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    max_bet: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    jp_contribution: {
      type: DataTypes.DOUBLE,
      allowNull: true
    },
    supported_languages: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    game_features: {
      type: DataTypes.JSON,
      allowNull: true
    },
    supported_currencies: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    country_gra_rank: {
      type: DataTypes.JSON,
      allowNull: true
    },
    target_market: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    release_date_per_market: {
      type: DataTypes.JSON,
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'game_info',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
      {
        name: "UK_game_info_game_code",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "game_code" },
        ]
      },
    ]
  });
  }
}
