import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { DeployLog, DeployLogId } from './deployLog';
import type { SanityItems, SanityItemsId } from './sanityItems';
import type { SanityTypes, SanityTypesId } from './sanityTypes';
import type { Users, UsersId } from './users';

export interface VersionsAttributes {
  id: number;
  created_at: Date;
  created_by: number;
}

export type VersionsPk = "id";
export type VersionsId = Versions[VersionsPk];
export type VersionsOptionalAttributes = "id" | "created_at";
export type VersionsCreationAttributes = Optional<VersionsAttributes, VersionsOptionalAttributes>;

export class Versions extends Model<VersionsAttributes, VersionsCreationAttributes> implements VersionsAttributes {
  id!: number;
  created_at!: Date;
  created_by!: number;

  // Versions belongsTo Users via created_by
  created_by_user!: Users;
  getCreated_by_user!: Sequelize.BelongsToGetAssociationMixin<Users>;
  setCreated_by_user!: Sequelize.BelongsToSetAssociationMixin<Users, UsersId>;
  createCreated_by_user!: Sequelize.BelongsToCreateAssociationMixin<Users>;
  // Versions hasMany DeployLog via version_id
  deploy_logs!: DeployLog[];
  getDeploy_logs!: Sequelize.HasManyGetAssociationsMixin<DeployLog>;
  setDeploy_logs!: Sequelize.HasManySetAssociationsMixin<DeployLog, DeployLogId>;
  addDeploy_log!: Sequelize.HasManyAddAssociationMixin<DeployLog, DeployLogId>;
  addDeploy_logs!: Sequelize.HasManyAddAssociationsMixin<DeployLog, DeployLogId>;
  createDeploy_log!: Sequelize.HasManyCreateAssociationMixin<DeployLog>;
  removeDeploy_log!: Sequelize.HasManyRemoveAssociationMixin<DeployLog, DeployLogId>;
  removeDeploy_logs!: Sequelize.HasManyRemoveAssociationsMixin<DeployLog, DeployLogId>;
  hasDeploy_log!: Sequelize.HasManyHasAssociationMixin<DeployLog, DeployLogId>;
  hasDeploy_logs!: Sequelize.HasManyHasAssociationsMixin<DeployLog, DeployLogId>;
  countDeploy_logs!: Sequelize.HasManyCountAssociationsMixin;
  // Versions hasMany SanityItems via version_id
  sanity_items!: SanityItems[];
  getSanity_items!: Sequelize.HasManyGetAssociationsMixin<SanityItems>;
  setSanity_items!: Sequelize.HasManySetAssociationsMixin<SanityItems, SanityItemsId>;
  addSanity_item!: Sequelize.HasManyAddAssociationMixin<SanityItems, SanityItemsId>;
  addSanity_items!: Sequelize.HasManyAddAssociationsMixin<SanityItems, SanityItemsId>;
  createSanity_item!: Sequelize.HasManyCreateAssociationMixin<SanityItems>;
  removeSanity_item!: Sequelize.HasManyRemoveAssociationMixin<SanityItems, SanityItemsId>;
  removeSanity_items!: Sequelize.HasManyRemoveAssociationsMixin<SanityItems, SanityItemsId>;
  hasSanity_item!: Sequelize.HasManyHasAssociationMixin<SanityItems, SanityItemsId>;
  hasSanity_items!: Sequelize.HasManyHasAssociationsMixin<SanityItems, SanityItemsId>;
  countSanity_items!: Sequelize.HasManyCountAssociationsMixin;
  // Versions hasMany SanityTypes via version_id
  sanity_types!: SanityTypes[];
  getSanity_types!: Sequelize.HasManyGetAssociationsMixin<SanityTypes>;
  setSanity_types!: Sequelize.HasManySetAssociationsMixin<SanityTypes, SanityTypesId>;
  addSanity_type!: Sequelize.HasManyAddAssociationMixin<SanityTypes, SanityTypesId>;
  addSanity_types!: Sequelize.HasManyAddAssociationsMixin<SanityTypes, SanityTypesId>;
  createSanity_type!: Sequelize.HasManyCreateAssociationMixin<SanityTypes>;
  removeSanity_type!: Sequelize.HasManyRemoveAssociationMixin<SanityTypes, SanityTypesId>;
  removeSanity_types!: Sequelize.HasManyRemoveAssociationsMixin<SanityTypes, SanityTypesId>;
  hasSanity_type!: Sequelize.HasManyHasAssociationMixin<SanityTypes, SanityTypesId>;
  hasSanity_types!: Sequelize.HasManyHasAssociationsMixin<SanityTypes, SanityTypesId>;
  countSanity_types!: Sequelize.HasManyCountAssociationsMixin;

  static initModel(sequelize: Sequelize.Sequelize): typeof Versions {
    return Versions.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    created_by: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    sequelize,
    tableName: 'versions',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
      {
        name: "FK_versions_created_by",
        using: "BTREE",
        fields: [
          { name: "created_by" },
        ]
      },
    ]
  });
  }
}
