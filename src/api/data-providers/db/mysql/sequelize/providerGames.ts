import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';

export interface ProviderGamesAttributes {
  id: number;
  provider: 'asia' | 'eu' | 'asiaskywind';
  game_code: string;
  is_active: number;
  is_jackpot: number;
  is_grc_game: number;
  title?: string;
  releaseDate?: Date;
  game_data: object;
  created_at: Date;
  updated_at: Date;
}

export type ProviderGamesPk = "id";
export type ProviderGamesId = ProviderGames[ProviderGamesPk];
export type ProviderGamesOptionalAttributes = "id" | "title" | "releaseDate" | "created_at" | "updated_at";
export type ProviderGamesCreationAttributes = Optional<ProviderGamesAttributes, ProviderGamesOptionalAttributes>;

export class ProviderGames extends Model<ProviderGamesAttributes, ProviderGamesCreationAttributes> implements ProviderGamesAttributes {
  id!: number;
  provider!: 'asia' | 'eu' | 'asiaskywind';
  game_code!: string;
  is_active!: number;
  is_jackpot!: number;
  is_grc_game!: number;
  title?: string;
  releaseDate?: Date;
  game_data!: object;
  created_at!: Date;
  updated_at!: Date;


  static initModel(sequelize: Sequelize.Sequelize): typeof ProviderGames {
    return ProviderGames.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    provider: {
      type: DataTypes.ENUM('asia','eu','asiaskywind'),
      allowNull: false
    },
    game_code: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false
    },
    is_jackpot: {
      type: DataTypes.BOOLEAN,
      allowNull: false
    },
    is_grc_game: {
      type: DataTypes.BOOLEAN,
      allowNull: false
    },
    title: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    releaseDate: {
      type: DataTypes.DATE(3),
      allowNull: true
    },
    game_data: {
      type: DataTypes.JSON,
      allowNull: false
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    }
  }, {
    sequelize,
    tableName: 'provider_games',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
      {
        name: "UK_provider_games_game_code",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "provider" },
          { name: "game_code" },
        ]
      },
      {
        name: "IDX_provider_games_is_active",
        using: "BTREE",
        fields: [
          { name: "provider" },
          { name: "is_active" },
        ]
      },
      {
        name: "IDX_provider_games_is_jackpot",
        using: "BTREE",
        fields: [
          { name: "provider" },
          { name: "is_jackpot" },
        ]
      },
    ]
  });
  }
}
