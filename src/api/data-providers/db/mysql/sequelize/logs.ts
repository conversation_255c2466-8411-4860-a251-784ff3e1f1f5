import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';

export interface LogsAttributes {
  id: number;
  channel: string;
  level: number;
  message: string;
  time: Date;
  ts?: Date;
}

export type LogsPk = "id";
export type LogsId = Logs[LogsPk];
export type LogsOptionalAttributes = "id" | "time" | "ts";
export type LogsCreationAttributes = Optional<LogsAttributes, LogsOptionalAttributes>;

export class Logs extends Model<LogsAttributes, LogsCreationAttributes> implements LogsAttributes {
  id!: number;
  channel!: string;
  level!: number;
  message!: string;
  time!: Date;
  ts?: Date;


  static initModel(sequelize: Sequelize.Sequelize): typeof Logs {
    return Logs.init({
    id: {
      autoIncrement: true,
      type: DataTypes.BIGINT.UNSIGNED,
      allowNull: false,
      primaryKey: true
    },
    channel: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    level: {
      type: DataTypes.INTEGER,
      allowNull: false
    },
    message: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    time: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    ts: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    }
  }, {
    sequelize,
    tableName: 'logs',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
      {
        name: "channel",
        using: "BTREE",
        fields: [
          { name: "channel" },
        ]
      },
      {
        name: "level",
        using: "BTREE",
        fields: [
          { name: "level" },
        ]
      },
      {
        name: "time",
        using: "BTREE",
        fields: [
          { name: "time" },
        ]
      },
    ]
  });
  }
}
