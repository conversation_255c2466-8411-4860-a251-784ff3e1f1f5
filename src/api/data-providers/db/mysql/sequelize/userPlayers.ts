import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { GameServers, GameServersId } from './gameServers';
import type { Users, UsersId } from './users';

export interface UserPlayersAttributes {
  user_id: number;
  is_active: number;
  player_code: string;
  created_at: Date;
  game_server_id: number;
}

export type UserPlayersPk = "user_id" | "is_active";
export type UserPlayersId = UserPlayers[UserPlayersPk];
export type UserPlayersOptionalAttributes = "user_id" | "is_active" | "created_at";
export type UserPlayersCreationAttributes = Optional<UserPlayersAttributes, UserPlayersOptionalAttributes>;

export class UserPlayers extends Model<UserPlayersAttributes, UserPlayersCreationAttributes> implements UserPlayersAttributes {
  user_id!: number;
  is_active!: number;
  player_code!: string;
  created_at!: Date;
  game_server_id!: number;

  // UserPlayers belongsTo GameServers via game_server_id
  game_server!: GameServers;
  getGame_server!: Sequelize.BelongsToGetAssociationMixin<GameServers>;
  setGame_server!: Sequelize.BelongsToSetAssociationMixin<GameServers, GameServersId>;
  createGame_server!: Sequelize.BelongsToCreateAssociationMixin<GameServers>;
  // UserPlayers belongsTo Users via user_id
  user!: Users;
  getUser!: Sequelize.BelongsToGetAssociationMixin<Users>;
  setUser!: Sequelize.BelongsToSetAssociationMixin<Users, UsersId>;
  createUser!: Sequelize.BelongsToCreateAssociationMixin<Users>;

  static initModel(sequelize: Sequelize.Sequelize): typeof UserPlayers {
    return UserPlayers.init({
    user_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      primaryKey: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      primaryKey: true
    },
    player_code: {
      type: DataTypes.STRING(32),
      allowNull: false
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: Sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
    },
    game_server_id: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false,
      references: {
        model: 'game_servers',
        key: 'id'
      }
    }
  }, {
    sequelize,
    tableName: 'user_players',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "user_id" },
          { name: "is_active" },
        ]
      },
      {
        name: "user_players_FK",
        using: "BTREE",
        fields: [
          { name: "game_server_id" },
        ]
      },
    ]
  });
  }
}
