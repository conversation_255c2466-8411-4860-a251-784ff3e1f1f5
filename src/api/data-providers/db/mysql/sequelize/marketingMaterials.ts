import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';

export interface MarketingMaterialsAttributes {
  id: number;
  game_code: string;
  data?: object;
}

export type MarketingMaterialsPk = "id";
export type MarketingMaterialsId = MarketingMaterials[MarketingMaterialsPk];
export type MarketingMaterialsOptionalAttributes = "id" | "data";
export type MarketingMaterialsCreationAttributes = Optional<MarketingMaterialsAttributes, MarketingMaterialsOptionalAttributes>;

export class MarketingMaterials extends Model<MarketingMaterialsAttributes, MarketingMaterialsCreationAttributes> implements MarketingMaterialsAttributes {
  id!: number;
  game_code!: string;
  data?: object;


  static initModel(sequelize: Sequelize.Sequelize): typeof MarketingMaterials {
    return MarketingMaterials.init({
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    game_code: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: "marketing_materials_game_code_IDX"
    },
    data: {
      type: DataTypes.JSON,
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'marketing_materials',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
      {
        name: "marketing_materials_game_code_IDX",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "game_code" },
        ]
      },
    ]
  });
  }
}
