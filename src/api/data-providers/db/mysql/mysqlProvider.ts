import { Op, Sequelize, Transaction } from "sequelize";

import { NextApiRequest } from "next";
import * as Errors from "utils/errors";
import { Error500, InternalServerError } from "utils/errors";
import { mapUserAttrs } from "./models/users";
import {
    BucketResources,
    DeployLog,
    GameInfo,
    GameServers,
    initModels,
    MarketingMaterials,
    OptimizedImages,
    ProviderGames,
    Regions,
    SanityItems,
    SanityItemsRevisions,
    SanityTypes,
    SanityTypesRevisions,
    Sessions,
    Settings,
    UserActivation,
    UserActivity,
    UserActivityCreationAttributes,
    UserBrowserAgents,
    UserGroups,
    UserPermissions,
    UserPermissionsToUserGroups,
    UserPlayers,
    Users,
    UsersRevisions,
    UsersToRegions,
    UsersToUserGroups,
    UsersToUsers,
    UserTokens,
    Versions
} from "./sequelize/init-models";
import { TokenService } from "api/services/tokenService";
import { IDBProvider, IProviderGame } from "api/data-providers/db/mysql/interfaces/iDBProvider";
import { EnumActivityStatus, EnumUserAction } from "models/enum/user";
import { ResponseError } from "utils/serverResponse";
import { errorCodes } from "errorCodes";
import { UserDto } from "models/userDto";
import { EnumRegionCode } from "models/enum/system";
import {
    IUserActivityParams,
    IUserAttrs,
    IUserCreateUpdate,
    IUserInfo,
    IUserRefreshTokensInfo
} from "api/services/interfaces/iUsersService";
import { EnumAliasDBModel, EnumDBProviders, EnumDBUserGroupCodes, EnumDBUserPermissions } from "models/enum/db";
import { ITokensResponse } from "api/services/interfaces/iTokenService";
import { UsersService } from "api/services/usersService";
import { ServerCookies } from "utils/serverCookies";
import { Project } from "utils/project";
import { ServerRequest } from "utils/serverRequest";
import logger from "utils/logger";
import { Tournaments } from "utils/tournamets";
import appServerConfig from "appServerConfig";

const log = logger("data-provider--mysql");

interface IModels {
    BucketResources: typeof BucketResources;
    DeployLog: typeof DeployLog;
    GameInfo: typeof GameInfo;
    GameServers: typeof GameServers;
    MarketingMaterials: typeof MarketingMaterials;
    OptimizedImages: typeof OptimizedImages;
    ProviderGames: typeof ProviderGames;
    Regions: typeof Regions;
    SanityItems: typeof SanityItems;
    SanityItemsRevisions: typeof SanityItemsRevisions;
    SanityTypes: typeof SanityTypes;
    SanityTypesRevisions: typeof SanityTypesRevisions;
    Sessions: typeof Sessions;
    Settings: typeof Settings;
    UserActivity: typeof UserActivity;
    UserActivation: typeof UserActivation;
    UserBrowserAgents: typeof UserBrowserAgents;
    UserGroups: typeof UserGroups;
    UserPermissions: typeof UserPermissions;
    UserPermissionsToUserGroups: typeof UserPermissionsToUserGroups;
    UserTokens: typeof UserTokens;
    UserPlayers: typeof UserPlayers;
    Users: typeof Users;
    UsersRevisions: typeof UsersRevisions;
    UsersToRegions: typeof UsersToRegions;
    UsersToUserGroups: typeof UsersToUserGroups;
    UsersToUsers: typeof UsersToUsers;
    Versions: typeof Versions;
}

export class MysqlProvider implements IDBProvider {
    private sequelize: Sequelize = {} as Sequelize;
    private models: IModels = {} as IModels;

    public isConnected: boolean = false;

    public async getNow(): Promise<never> {
        const sequelize = await this.getConnection();
        return sequelize.Sequelize.literal("now()") as never;
    }

    public async getConnection(): Promise<Sequelize> {
        try {
            await this.connectUsingConfig();
            return this.sequelize;
        } catch (e) {
            return Promise.reject(e);
        }
    }

    public async getModels(): Promise<IModels> {
        try {
            await this.connectUsingConfig();
            return this.models;
        } catch (e) {
            return Promise.reject(e);
        }
    }

    public async connectUsingConfig() {
        try {
            if (!this.isConnected || !this.models) {
                await this.connect({
                    dbHost: appServerConfig.db.mysql.write.host,
                    dbPort: appServerConfig.db.mysql.write.port,
                    dbName: appServerConfig.db.mysql.write.database,
                    userName: appServerConfig.db.mysql.write.user,
                    password: appServerConfig.db.mysql.write.password
                });
            }
        } catch (e) {
            return Promise.reject(e);
        }
    }

    public async connect({
                             dbHost,
                             dbPort,
                             dbName,
                             userName,
                             password
                         }: {
        dbHost: string;
        dbPort: number;
        dbName: string;
        userName: string;
        password: string;
    }): Promise<boolean> {
        try {
            const self = this;
            this.sequelize = new Sequelize({
                host: dbHost,
                port: dbPort,
                database: dbName,
                dialect: EnumDBProviders.mysql,
                username: userName,
                password: password
            });

            await this.sequelize.authenticate().then(() => {
                self.isConnected = true;
            });

            this.models = initModels(this.sequelize);
            return true;
        } catch (e) {
            log.error(e, "Can't connect to MySQL.");
            return Promise.reject(new InternalServerError(e as Error500));
        }
    }

    public async findUserByEmail(email: string): Promise<Users | null> {
        try {
            const models = await this.getModels();
            return await models.Users.findOne({ where: { email }, raw: true });
        } catch (e) {
            log.error(e, "Error at findUserByEmail.");
            return Promise.reject(e);
        }
    }

    public async findUserPlayerCode(userId: number, gameServerId: number): Promise<UserPlayers | null> {
        try {
            const models = await this.getModels();
            return await models.UserPlayers.findOne({
                where: { user_id: userId, is_active: 1, game_server_id: gameServerId },
                raw: true
            });
        } catch (e) {
            log.error(e, "Error at findUserPlayerCode.");
            return Promise.reject(e);
        }
    }

    public async findGameServer(secretKey: string): Promise<GameServers | null> {
        try {
            const models = await this.getModels();
            return await models.GameServers.findOne({ where: { secret_key: secretKey, is_active: 1 }, raw: true });
        } catch (e) {
            log.error(e, "Error at findGameServer.");
            return Promise.reject(e);
        }
    }

    public async createUserPlayerCode(
        userId: number,
        playerCode: string,
        gameServerId: number,
        trans?: Transaction
    ): Promise<UserPlayers | null> {
        try {
            const models = await this.getModels();
            return await models.UserPlayers.create(
                { user_id: userId, is_active: 1, player_code: playerCode, game_server_id: gameServerId },
                { transaction: trans }
            );
        } catch (e) {
            log.error(e, "Error at createUserPlayerCode.");
            return Promise.reject(e);
        }
    }

    public async findUserByLogin(login: string): Promise<Users | null> {
        try {
            const models = await this.getModels();
            return await models.Users.findOne({ where: { login }, raw: true });
        } catch (e) {
            log.error(e, "Error at findUserByLogin.");
            return Promise.reject(e);
        }
    }

    public async findUserGroups(userId: number): Promise<UsersToUserGroups[]> {
        try {
            const models = await this.getModels();
            return await models.UsersToUserGroups.findAll({
                attributes: [],
                where: { user_id: userId },
                include: [
                    {
                        model: UserGroups,
                        as: EnumAliasDBModel.user_group,
                        where: { is_active: 1 },
                        required: true
                    }
                ],
                raw: true,
                nest: true
            });
        } catch (e) {
            log.error(e, "Error at findUserGroups.");
            return Promise.reject(e);
        }
    }

    public async findUserRefreshToken(
        refreshToken: string,
        userId?: number,
        trans?: Transaction
    ): Promise<UserTokens | null> {
        try {
            const models = await this.getModels();
            return await models.UserTokens.findOne({
                where: { ...(userId && { user_id: userId }), refresh_token: refreshToken },
                include: [{ model: Users, as: EnumAliasDBModel.user }],
                raw: true,
                nest: true
            });
        } catch (e) {
            log.error(e, "Error at findUserRefreshToken.");
            return Promise.reject(e);
        }
    }

    public async createUser(params: IUserCreateUpdate, trans?: Transaction): Promise<Users> {
        try {
            const models = await this.getModels();
            return await models.Users.create(
                {
                    login: params.email.trim().toLowerCase(),
                    email: params.email.trim().toLowerCase(),
                    password: params.password.trim(),
                    first_name: params.firstName?.trim(),
                    last_name: params.lastName?.trim(),
                    company: params.company?.trim(),
                    company_position: params.companyPosition?.trim(),
                    is_active: 0
                },
                {
                    transaction: trans,
                    raw: true
                }
            );
        } catch (e) {
            log.error(e, "Error at createUser.");
            return Promise.reject(e);
        }
    }

    public async changeUserPassword(userId: number, password: string, trans?: Transaction): Promise<boolean | null> {
        try {
            const models = await this.getModels();
            const now = await this.getNow();
            const status = await models.Users.update(
                { password, updated_at: now, password_updated_at: now },
                { where: { id: userId }, transaction: trans }
            );
            return Array.isArray(status) && status.length > 0 && status[0] === 1;
        } catch (e) {
            log.error(e, "Error at changeUserPassword.");
            return Promise.reject(e);
        }
    }

    public async login(userId: number, trans?: Transaction): Promise<boolean | null> {
        try {
            const models = await this.getModels();
            const now = await this.getNow();
            const status = await models.Users.update(
                { last_login_at: now },
                { where: { id: userId }, transaction: trans }
            );
            return Array.isArray(status) && status.length > 0 && status[0] === 1;
        } catch (e) {
            log.error(e, "Error at login.");
            return Promise.reject(e);
        }
    }

    public async updateUserAttr(userId: number, attrs: IUserAttrs, trans?: Transaction): Promise<boolean | null> {
        try {
            const models = await this.getModels();
            const user = await models.Users.findOne({ attributes: ["id", "attrs"], where: { id: userId }, raw: true });
            const userAttrs = mapUserAttrs(user?.attrs, attrs);
            const status = await models.Users.update(
                { attrs: userAttrs, updated_at: await this.getNow() },
                { where: { id: userId }, transaction: trans }
            );
            return Array.isArray(status) && status.length > 0 && status[0] === 1;
        } catch (e) {
            log.error(e, "Error at updateUserAttr.");
            return Promise.reject(e);
        }
    }

    public async assignUserToUserGroups(
        userId: number,
        userGroupCodes: string[],
        trans?: Transaction
    ): Promise<UserGroups[]> {
        try {
            const models = await this.getModels();
            let groupCodes: UserGroups[] = [];
            if (Array.isArray(userGroupCodes) && userGroupCodes.length > 0) {
                groupCodes = await models.UserGroups.findAll({
                    where: { group_code: { [Op.in]: userGroupCodes } },
                    transaction: trans,
                    raw: true
                });
            }
            await models.UsersToUserGroups.destroy({
                where: { user_id: userId },
                transaction: trans
            });
            if (groupCodes.length > 0) {
                await models.UsersToUserGroups.bulkCreate(
                    groupCodes.map((groupCode) => {
                        return {
                            user_id: userId,
                            user_group_id: groupCode.id
                        };
                    }),
                    {
                        fields: ["user_id", "user_group_id"],
                        transaction: trans
                    }
                );
            }

            return groupCodes;
        } catch (e) {
            log.error(e, "Error at assignUserToUserGroups.");
            return Promise.reject(e);
        }
    }

    public async createUserAndAssignToGroups(
        params: IUserCreateUpdate,
        userGroupCodes: string[],
        trans?: Transaction
    ): Promise<Users> {
        try {
            const sequelize = await this.getConnection();
            const createAndAssign = async (
                params: IUserCreateUpdate,
                userGroupCodes: string[],
                trans?: Transaction
            ): Promise<Users> => {
                const user = await this.createUser(params, trans);
                await this.assignUserToUserGroups(user.id, userGroupCodes, trans);
                return user;
            };
            let user = {} as Users;
            if (!trans) {
                await sequelize.transaction(async (trans: Transaction) => {
                    user = await createAndAssign(params, userGroupCodes, trans);
                });
            } else {
                user = await createAndAssign(params, userGroupCodes, trans);
            }
            return user;
        } catch (e) {
            log.error(e, "Error at createUserAndAssignToGroups.");
            return Promise.reject(e);
        }
    }

    public async createUserActivationHash(userId: number, hash: string, trans?: Transaction): Promise<UserActivation> {
        try {
            const models = await this.getModels();
            const sequelize = await this.getConnection();
            let activation = {} as UserActivation;
            const activationFn = async (userId: number, hash: string, trans?: Transaction): Promise<UserActivation> => {
                await models.UserActivation.destroy({ where: { user_id: userId }, transaction: trans });
                return await models.UserActivation.create(
                    {
                        user_id: userId,
                        activation_hash: hash
                    },
                    { transaction: trans, raw: true }
                );
            };
            if (!trans) {
                await sequelize.transaction(async (trans: Transaction) => {
                    activation = await activationFn(userId, hash, trans);
                });
            } else {
                activation = await activationFn(userId, hash, trans);
            }
            return activation;
        } catch (e) {
            log.error(e, "Error at createUserActivationHash.");
            return Promise.reject(e);
        }
    }

    public async findUserAgent(userAgent: string, trans?: Transaction): Promise<UserBrowserAgents | null> {
        try {
            const models = await this.getModels();
            return await models.UserBrowserAgents.findOne({
                where: {
                    agent: userAgent
                },
                transaction: trans,
                raw: true
            });
        } catch (e) {
            log.error(e, "Error at findUserAgent.");
            return Promise.reject(e);
        }
    }

    public async findOrCreateUserAgent(userAgent: string, trans?: Transaction): Promise<UserBrowserAgents | null> {
        try {
            const models = await this.getModels();
            let userAgentData = await this.findUserAgent(userAgent);
            if (!userAgentData) {
                userAgentData = await models.UserBrowserAgents.create({ agent: userAgent }, { transaction: trans });
            }
            return userAgentData;
        } catch (e) {
            log.error(e, "Error at findOrCreateUserAgent.");
            return Promise.reject(e);
        }
    }

    public async findUserActivationHash(activationHash: string, trans?: Transaction): Promise<UserActivation | null> {
        try {
            const models = await this.getModels();
            return await models.UserActivation.findOne({
                where: {
                    activation_hash: activationHash
                },
                transaction: trans,
                raw: true
            });
        } catch (e) {
            log.error(e, "Error at findUserActivationHash.");
            return Promise.reject(e);
        }
    }

    public async getProviderGames(regionCode: EnumRegionCode, trans?: Transaction): Promise<IProviderGame[] | null> {
        try {
            const models = await this.getModels();
            return (await models.ProviderGames.findAll({
                attributes: [
                    ["game_code", "gameCode"],
                    ["is_active", "isActive"],
                    ["is_jackpot", "isJackpot"],
                    ["is_grc_game", "isGRCGame"]
                ],
                where: { provider: regionCode },
                transaction: trans,
                raw: true
            })) as never;
        } catch (e) {
            log.error(e, "Error at getProviderGames.");
            return Promise.reject(e);
        }
    }

    public async activateUser(userActivation: UserActivation): Promise<number | boolean> {
        try {
            const models = await this.getModels();
            const sequelize = await this.getConnection();
            let flag: number | boolean = false;
            const maxNumOfAttempts = 10;
            await sequelize.transaction(async (trans: Transaction) => {
                const reachedLimit = Number(userActivation?.number_of_attempts) >= maxNumOfAttempts;
                await models.UserActivation.update(
                    {
                        number_of_attempts: Number(userActivation?.number_of_attempts) + 1,
                        updated_at: await this.getNow(),
                        activation_hash:
                            reachedLimit || userActivation.is_activated === 0
                            ? UsersService.activationHash()
                            : userActivation.activation_hash,
                        is_activated: !reachedLimit && userActivation.is_activated === 0 ? 1 : 0
                    },
                    {
                        where: { user_id: userActivation.user_id },
                        transaction: trans
                    }
                );
                if (userActivation.is_activated === 0 && !reachedLimit) {
                    await models.Users.update(
                        {
                            is_active: 1
                        },
                        { where: { id: userActivation.user_id }, transaction: trans }
                    );

                    flag = userActivation.user_id;
                }
            });

            return flag;
        } catch (e) {
            log.error(e, "Error at activateUser.");
            return Promise.reject(e);
        }
    }

    public async saveUserToken(
        userId: number,
        refreshToken: string,
        req: NextApiRequest,
        login = false
    ): Promise<UserTokens> {
        try {
            const models = await this.getModels();
            const sequelize = await this.getConnection();
            await sequelize.transaction(async (trans: Transaction) => {
                const userToken = await models.UserTokens.findOne({
                    where: { user_id: userId },
                    transaction: trans,
                    raw: true
                });
                const now = await this.getNow();
                const userIp = ServerRequest.getUserIp(req);
                const userAgent = ServerRequest.getUserAgent(req);
                const userAgentInfo = await this.findOrCreateUserAgent(userAgent);
                const userAgentId = userAgentInfo?.id || null;
                if (userToken) {
                    await models.UserTokens.update(
                        {
                            refresh_token: refreshToken,
                            user_ip_address: userIp !== userToken.user_ip_address ? userIp : userToken.user_ip_address,
                            user_agent_id: userAgentId as number,
                            ...(login && { last_login: now }),
                            updated_at: now
                        },
                        {
                            where: { user_id: userId },
                            transaction: trans
                        }
                    );
                } else {
                    await models.UserTokens.create(
                        {
                            user_id: userId,
                            refresh_token: refreshToken,
                            user_ip_address: userIp,
                            user_agent_id: userAgentId as number,
                            ...(login && { last_login: now })
                        },
                        {
                            where: { user_id: userId },
                            transaction: trans
                        }
                    );
                }
            });
            return (await this.findUserRefreshToken(refreshToken, userId)) as UserTokens;
        } catch (e) {
            log.error(e, "Error at saveUserToken.");
            return Promise.reject(e);
        }
    }

    public async userHasPermissions(permissions: string[], userId?: number): Promise<boolean> {
        try {
            const models = await this.getModels();
            if (userId) {
                return (
                    (await models.UserPermissionsToUserGroups.count({
                        attributes: [],
                        where: {
                            // user_id: userId,
                            permission_code: { [Op.in]: permissions },
                            user_group_id: {
                                [Op.in]: Sequelize.literal(`(
    SELECT utug.user_group_id 
    FROM users_to_user_groups utug 
        LEFT JOIN users u ON u.id = utug.user_id AND u.is_active = 1 
    WHERE utug.user_id = ${Number(userId)}
)`)
                            }
                        }
                    })) > 0
                );
            } else {
                return (
                    (await models.UserPermissionsToUserGroups.count({
                        attributes: [],
                        include: [
                            {
                                as: EnumAliasDBModel.user_group,
                                required: true,
                                attributes: [],
                                model: UserGroups,
                                where: { groupCode: EnumDBUserGroupCodes.guest, is_active: 1 }
                            }
                        ],
                        where: { permission_code: { $in: permissions } }
                    })) > 0
                );
            }
        } catch (e) {
            log.error(e, "Error at isUserPermission.");
            return Promise.reject(e);
        }
    }

    public async getGuestPermissions(): Promise<{ groups: string[]; permissions: string[] }> {
        try {
            const models = await this.getModels();
            const uPermissions = await models.UserPermissionsToUserGroups.findAll({
                attributes: ["permission_code"],
                include: [
                    {
                        attributes: ["group_code"],
                        model: UserGroups,
                        as: EnumAliasDBModel.user_group,
                        where: { group_code: EnumDBUserGroupCodes.guest, is_active: 1 }
                    }
                ],
                raw: true,
                nest: true
            });

            const groups = new Set();
            const permissions = new Set();

            for (const up of uPermissions) {
                groups.add(up.permission_code);
                permissions.add(up.user_group.group_code);
            }
            return { groups: Array.from(groups) as string[], permissions: Array.from(permissions) as string[] };
        } catch (err) {
            log.error(err, "End getGuestPermissions Error");
            return Promise.reject(err);
        }
    }

    public async getUserPermissions(
        currentUserId: number,
        userId?: number,
        needPermission = true
    ): Promise<{ groups: string[]; permissions: string[] }> {
        try {
            const models = await this.getModels();
            if (userId === undefined) {
                return await this.getGuestPermissions();
            }
            const userPermissions = await models.UserPermissionsToUserGroups.findAll({
                attributes: ["permission_code"],
                where: {
                    user_group_id: {
                        [Op.in]: Sequelize.literal(`(
    SELECT utug.user_group_id 
    FROM users_to_user_groups utug 
        LEFT JOIN users u ON u.id = utug.user_id AND u.is_active = 1 
    WHERE utug.user_id = ${Number(userId)}
)`)
                    }
                },
                include: [
                    {
                        attributes: ["group_code"],
                        model: UserGroups,
                        as: EnumAliasDBModel.user_group,
                        where: { is_active: 1 },
                        required: true
                    }
                ],
                raw: true,
                nest: true
            });

            const pc: { [code: string]: string } = {};
            const gc: { [code: string]: string } = {};

            for (const up of userPermissions) {
                pc[up.permission_code] = up.permission_code;
                gc[up.user_group.group_code] = up.user_group.group_code;
            }
            if (
                needPermission &&
                currentUserId !== userId &&
                userId &&
                !pc[EnumDBUserPermissions.canGetUsersPermissions]
            ) {
                return Promise.reject(new Errors.Forbidden(errorCodes.e100403));
            }

            return { groups: Object.values(gc), permissions: Object.values(pc) };
        } catch (e) {
            log.error(e, "Error at getUserPermissions.");
            return Promise.reject(e);
        }
    }

    public async mapUserInfo(user: Users, includePassword?: boolean): Promise<IUserInfo> {
        let userPlayers: UserPlayers | null = null;
        const tournaments = Tournaments.getCurrentlyUsed({ filter: { isActive: true } });
        if (tournaments.length === 1) {
            const gameServers = await this.findGameServer(tournaments[0].gameServerSecretKey);
            if (gameServers) {
                userPlayers = await this.findUserPlayerCode(user.id, gameServers?.id);
            }
        }
        // const usersToUserGroups = await this.findUserGroups(user.id);
        const userPermissions = await this.getUserPermissions(user.id, user.id);

        const userAttrs = mapUserAttrs(user.attrs);

        return {
            id: user.id,
            login: user.login,
            email: user.email,
            firstName: user.first_name,
            lastName: user.last_name,
            company: user.company,
            companyPosition: user.company_position,
            createdAt: Project.toISODate(user.created_at) as never,
            updatedAt: Project.toISODate(user.updated_at) as never,
            firstEntranceAgreementAt: Project.toISODate(userAttrs?.firstEntranceAgreementAt) as never,
            passwordUpdatedAt: Project.toISODate(user.password_updated_at) as never,
            playerCode: userPlayers?.player_code,
            isActive: user.is_active === 1,
            // userGroups: usersToUserGroups.map((group) => group.user_group.group_code),
            userPermissions,
            ...(includePassword && { password: user?.password })
        };
    }

    public async refreshUserToken(
        userId: number,
        refreshToken: string,
        req: NextApiRequest
    ): Promise<IUserRefreshTokensInfo | boolean> {
        try {
            const sequelize = await this.getConnection();
            let dbToken = {} as UserTokens;
            let tokens = {} as ITokensResponse;
            await sequelize.transaction(async (trans: Transaction) => {
                dbToken = (await this.findUserRefreshToken(refreshToken, userId, trans)) as UserTokens;
                if (dbToken && dbToken?.user && dbToken.user?.id) {
                    const userDto = new UserDto(dbToken.user);
                    tokens = await TokenService.generateTokens({ ...userDto });
                    dbToken = await this.saveUserToken(dbToken.user.id, tokens.refreshToken, req);
                }
            });
            return dbToken && dbToken?.user && dbToken.user?.id
                   ? {
                    ...(await this.mapUserInfo(dbToken.user)),
                    ...{
                        accessToken: tokens.accessToken,
                        refreshToken: tokens.refreshToken
                    }
                }
                   : false;
        } catch (e) {
            log.error(e, "Error at refreshUserToken.");
            return Promise.reject(e);
        }
    }

    public async deleteUserToken(userId: number, refreshToken?: string, trans?: Transaction): Promise<boolean> {
        try {
            const models = await this.getModels();
            return (
                (await models.UserTokens.destroy({
                    where: { user_id: userId, ...(refreshToken && { refresh_token: String(refreshToken).trim() }) },
                    transaction: trans
                })) === 1
            );
        } catch (e) {
            log.error(e, "Error at deleteUserToken.");
            return Promise.reject(e);
        }
    }

    public async activityPrepare(
        action: EnumUserAction,
        params: IUserActivityParams,
        req: NextApiRequest
    ): Promise<UserActivityCreationAttributes> {
        try {
            const url = ServerRequest.getUrl(req);
            const userAgentInfo = await this.findOrCreateUserAgent(ServerRequest.getUserAgent(req));
            const userAgentId = userAgentInfo?.id || null;

            return {
                user_action: action,
                request_path: url.pathname,
                user_ip: ServerRequest.getUserIp(req),
                user_agent_id: userAgentId as number,
                params: { ...params.params, urlParams: { searchParams: url.searchParams } },
                session_id: ServerCookies.getSessionId(req) ?? undefined
            };
        } catch (e) {
            log.error(e, "Error at activitySave.");
            return Promise.reject(e);
        }
    }

    public activitySetUser(
        attrs: UserActivityCreationAttributes,
        userId: number | null | undefined,
        userLogin: string | null | undefined = null
    ): UserActivityCreationAttributes {
        attrs.user_id = userId || undefined;
        attrs.user_login = userLogin || undefined;
        return attrs;
    }

    public async activitySuccess(attrs: UserActivityCreationAttributes): Promise<UserActivity> {
        try {
            const models = await this.getModels();
            attrs.status = EnumActivityStatus.success;
            return await models.UserActivity.create(attrs, {
                raw: true
            });
        } catch (e) {
            log.error(e, "Error at activitySuccess.");
            return Promise.reject(e);
        }
    }

    public async activityFailed(
        attrs: UserActivityCreationAttributes,
        err?: ResponseError,
        status?: EnumActivityStatus
    ): Promise<UserActivity> {
        try {
            const models = await this.getModels();
            attrs.status = status || EnumActivityStatus.failed;
            attrs.params = {
                ...attrs.params,
                ...(err?.message && { errorMessage: err.message }),
                ...(err?.errorCode && { error: err?.errorCode + " " + err?.name + " " + err.responseCode })
            } as IUserActivityParams;
            log.error(err, attrs);
            return await models.UserActivity.create(attrs, {
                raw: true
            });
        } catch (e) {
            log.error(e, "Error at activityFailed.");
            return Promise.reject(e);
        }
    }
}
