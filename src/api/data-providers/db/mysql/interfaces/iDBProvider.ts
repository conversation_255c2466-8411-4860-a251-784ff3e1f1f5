import { NextApiRequest } from "next";

import { Sequelize, Transaction } from "sequelize";
import { UserGroups, Users, UsersToUserGroups, UserTokens } from "../sequelize";
import { UserPlayers } from "api/data-providers/db/mysql/sequelize/userPlayers";
import { UserActivation } from "api/data-providers/db/mysql/sequelize/userActivation";
import { UserActivity, UserActivityCreationAttributes } from "api/data-providers/db/mysql/sequelize/userActivity";
import { UserBrowserAgents } from "api/data-providers/db/mysql/sequelize/userBrowserAgents";
import {
    IUserActivityParams,
    IUserAttrs,
    IUserCreateUpdate,
    IUserInfo,
    IUserRefreshTokensInfo,
} from "api/services/interfaces/iUsersService";
import { EnumActivityStatus, EnumUserAction } from "models/enum/user";
import { ResponseError } from "utils/serverResponse";
import { EnumRegionCode } from "models/enum/system";
import { GameServers } from "api/data-providers/db/mysql/sequelize/gameServers";

export interface IProviderGame {
    gameCode: string;
    isActive: number;
    isJackpot: number;
    isGRCGame: number;
}

export interface IDBProvider {
    getConnection(): Promise<Sequelize>;

    connect({
        dbHost,
        dbPort,
        dbName,
        userName,
        password,
    }: {
        dbHost: string;
        dbPort: number;
        dbName: string;
        userName: string;
        password: string;
    }): Promise<boolean>;

    findUserByEmail(email: string): Promise<Users | null>;

    findUserPlayerCode(userId: number, gameServerId: number): Promise<UserPlayers | null>;

    findGameServer(secretKey: string): Promise<GameServers | null>;

    createUserPlayerCode(
        userId: number,
        playerCode: string,
        gameServerId: number,
        trans?: Transaction
    ): Promise<UserPlayers | null>;

    findUserByLogin(login: string): Promise<Users | null>;

    findUserGroups(userId: number): Promise<UsersToUserGroups[]>;

    findUserRefreshToken(refreshToken: string, userId?: number, trans?: Transaction): Promise<UserTokens | null>;

    createUser(params: IUserCreateUpdate, trans?: Transaction): Promise<Users>;

    changeUserPassword(userId: number, password: string, trans?: Transaction): Promise<boolean | null>;

    login(userId: number, trans?: Transaction): Promise<boolean | null>;

    updateUserAttr(userId: number, attrs: IUserAttrs): Promise<boolean | null>;

    assignUserToUserGroups(userId: number, userGroupCodes: string[], trans?: Transaction): Promise<UserGroups[]>;

    createUserAndAssignToGroups(
        params: IUserCreateUpdate,
        userGroupCodes: string[],
        trans?: Transaction
    ): Promise<Users>;

    createUserActivationHash(userId: number, hash: string, trans?: Transaction): Promise<UserActivation>;

    findUserAgent(userAgent: string, trans?: Transaction): Promise<UserBrowserAgents | null>;

    findOrCreateUserAgent(userAgent: string, trans?: Transaction): Promise<UserBrowserAgents | null>;

    findUserActivationHash(activationHash: string, trans?: Transaction): Promise<UserActivation | null>;

    getProviderGames(regionCode: EnumRegionCode, trans?: Transaction): Promise<IProviderGame[] | null>;

    activateUser(userActivation: UserActivation): Promise<number | boolean>;

    saveUserToken(userId: number, refreshToken: string, req: NextApiRequest, login?: boolean): Promise<UserTokens>;

    mapUserInfo(user: Users, includePassword?: boolean): Promise<IUserInfo>;

    refreshUserToken(
        userId: number,
        refreshToken: string,
        req: NextApiRequest
    ): Promise<IUserRefreshTokensInfo | boolean>;

    deleteUserToken(userId: number, refreshToken?: string, trans?: Transaction): Promise<boolean>;

    activityPrepare(
        action: EnumUserAction,
        params: IUserActivityParams,
        req: NextApiRequest
    ): Promise<UserActivityCreationAttributes>;

    activitySetUser(
        attrs: UserActivityCreationAttributes,
        userId: number | null | undefined,
        userLogin?: string | null | undefined
    ): UserActivityCreationAttributes;

    activitySuccess(attrs: UserActivityCreationAttributes): Promise<UserActivity>;

    activityFailed(
        attrs: UserActivityCreationAttributes,
        err?: ResponseError,
        status?: EnumActivityStatus
    ): Promise<UserActivity>;
}
