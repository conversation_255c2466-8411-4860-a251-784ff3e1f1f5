import { SanityClient } from "@sanity/client";
import {
    IBanner,
    IBannerAsset,
    IBannersCarousel,
    IDateTime,
    IEmailAddress,
    IEmployee,
    IEngagementSlide,
    IGame,
    IGameCode,
    IGameFeatures,
    IGameMarketCertificates,
    IGameRTP,
    IGameType,
    IJackpotInfo,
    ILicense,
    IMarket,
    INews,
    IOffice,
    IPageGames,
    IPageRoadmap,
    IPageToolsJackpot,
    IPageToolsTournament,
    IPartner,
    IPhone,
    IRoadmapGame,
    IRoadmapGameMarket,
    ISectionGroupSliderGames,
    ISectionSliderGames,
    ISlug,
    ISocialNetwork,
    IVideo,
    IWebsiteConfig,
} from "../schemas";
import { EnumLanguageCode, EnumRegionCode } from "models/enum/system";
import { IMarketCertificate } from "api/services/interfaces/iGamesService";
import { IProviderGame } from "api/data-providers/db/mysql/interfaces/iDBProvider";

export interface IGameShortInfo {
    _id: string;
    gameId: ISlug;
    gameCodes?: Array<IGameCode | null>;
    gameFeatures?: Array<IGameFeatures | null>;
    paUrl?: string;
}

export interface IGameInfoFromBI {
    game_code: string; //  "sw_lacadepa";
    game_name: string; // "La Casa de Papel";
    type: string; //  "Video Slot";
    theoretical_rtp: string; // "0.9639";
    house_volatility: string; // "7";
    player_volatility: string; // "5";
    release_date: string; // "None";
    certification: string; // "Spain";
    project_type: string; // "Regular";
    grid: string; // "3x5";
    gametags: string; // "Branded, Robbery, TV Series, Mafia, Money";
    marketingfeatures: string; // "";
    max_number_of_lines: string; // "10";
    target_region: string; // "Non Asia";
    wrapper_version: string; // "";
    hit_rate: string; // "0.231633";
    progressive_jackpot: string; // "No";
    branded: string; // "Yes";
    supports_portrait: string; // "Yes";
    max_win_multiplier: string; // "10000.0";
    combined_feature_frequency: string; // "255.0";
    max_exposure: string; // "6198900.0";
    default_bet: string; // "1.0";
    min_bet: string; // "0.1";
    max_bet: string; // "300.0";
    jp_contribution: string; // "";
    supported_languages: string; // "";
    game_features: string; // '{ "BNS" : "None", "Free bets" : "None", "MWJP support" : "None", "RTP Reducer support" :
    // "None"}';
    supported_currencies: string; // "AMD, ARS, AUD, AZN, BGN, BMD, BND, BNS, BRL, CAD, CHF, CLP, CNY, COP, CRC, CZK,
    // DKK, DOP, EUR, GBP, GEL, GHS, HKD, HNL, HRK, HUF, IDR, IDS, ILS, INR, ISK, JPY,
    // KES, KGS, KRW, KZT, MAD, MDL, MMK, MNT, MOP, MXN, MYR, NIO, NOK, NZD, PEN, PHP,
    // PLN, PYG, RON, RSD, RUB, RUP, SEK, SGD, THB, TRY, TWD, TZS, UAH, USD, UYU, VDO,
    // VEF, VES, VND, VNS, XOF, XXX, ZAR, ZMW";
    country_gra_rank: string; // '[ { "Country Code" : "XX", "GRA Rank" : "15.439999580383300", "GRA Position" : "12"
    // },{ "Country Code" : "ES", "GRA Rank" : "35.200000762939452", "GRA Position" : "1" }
    // ]';
    release_date_per_market: string;
    target_market: string; // "Spain";
    release_date_denmark: string; // "None";
    release_date_gib: string; // "None";
    release_date_greece: string; // "None";
    release_date_italy: string; // "None";
    release_date_latvia: string; // "None";
    release_date_mga: string; // "None";
    release_date_romania: string; // "None";
    release_date_spain: string; // "None";
    release_date_sweden: string; // "None";
    release_date_uk: string; // "None";
    release_date_asia: string; // "None";
}

export interface IGameFeatureExtended extends IGameFeatures {
    gameFeaturePosterUrl: string;
}

export interface IGameVideosExtended {
    videoUrl: string;
    languageCode: string;
}

export interface IVideoExtended extends IVideo {
    videoUrl?: string;
    posterUrl?: string; // 1920x1080
    posterVerticalUrl?: string; // 435x662
}

export interface INewsExtended extends INews {
    headerPromoVideo: IVideoExtended;
    newsPreviewImageUrl: string;
}

export interface IMarketExtended extends IMarket {
    iconUrl?: string;
}

export interface IGameMarketCertificatesExtended extends IGameMarketCertificates {
    certificateUrl?: string;
    certificate1Url?: string;
    certificate2Url?: string;
    gameMarket: IMarketExtended;
}

export interface IPartnerExtended extends IPartner {
    partnerImageUrl: string;
}

export interface IEmployeeExtended extends IEmployee {
    employeePhotoUrl: string;
}

export interface ILicenseExtended extends ILicense {
    licensePhotoUrl: string;
}

export interface IOfficeExtended extends IOffice {
    largePhotosExt: { imageUrl: string }[];
    officePhotoUrl: string;
}

export interface IPageAboutUsExtended {
    partners?: IPartnerExtended[];
    employees?: IEmployeeExtended[];
    licenses?: ILicenseExtended[];
    offices?: IOfficeExtended[];
}

export interface ISectionSliderGamesExtended extends ISectionSliderGames {
    games: IGameExtended[];
    sectionLogoUrl: string;
}

export interface ISectionGroupSliderGamesExtended extends ISectionGroupSliderGames {
    sliderGames: ISectionSliderGamesExtended[];
}

export interface ISectionGroupSliderGamesResponse {
    data: ISectionGroupSliderGamesExtended;
    providerGames: IProviderGame[] | null;
}

export interface IGameExtended extends IGame {
    allGameMarkets?: IMarketExtended[];
    biggestMarkets?: IMarketExtended[];
    gameVideo?: IVideoExtended;
    gameVideosExt?: IGameVideosExtended[];
    gameCodes: IGameCode[];
    gameRTPExt: IGameRTP[];
    gameTypes: IGameType[];
    gameFeatures: IGameFeatureExtended[];
    gameIconUrl: string; // 180x180
    gameImageUrl: string; // 320x240
    gamePosterUrl: string; // 428x268
    gameLogoUrl: string;
    gameHDPosterUrl: string; // 1920x990
    gameTowerPosterUrl: string; // 428x550
    gameTower4PosterUrl: string; // 435x662
    gameBillboardUrl: string; // 534x930
    gameBrandedPosterUrl: string; // 405x765
    productSheetUrl: string;
    infoSheetUrl: string;
    certificatesPerMarket?: IGameMarketCertificatesExtended[];
    certificates?: IMarketCertificate[] | null | undefined;
    roadmapGame?: IRoadmapGame;
    marketReleaseDate?: string;
}

export interface IFooterLicenceExtended extends ILicense {
    licensePhotoUrl: string;
}

export interface IFooterSocialNetworksExtended extends ISocialNetwork {
    iconUrl: string;
}

export interface IWebsiteConfigExtended extends IWebsiteConfig {
    footerLicenses: IFooterLicenceExtended[];
    socialNetworks: IFooterSocialNetworksExtended[];
    phones: IPhone[];
    emails: IEmailAddress[];
}

export interface IWebsiteFooter {
    _createdAt?: IDateTime;
    _id?: string;
    _key?: string;
    _rev?: string;
    _updatedAt?: IDateTime;
    footerLicenses: IFooterLicenceExtended[];
    socialNetworks: IFooterSocialNetworksExtended[];
    phones: IPhone[];
    emails: IEmailAddress[];
}

export interface IEngagementSlideExtended extends IEngagementSlide {
    engagementSliderImageUrl: string;
}

export interface IPageToolsTournamentExtended extends IPageToolsTournament {
    engagementSliders: IEngagementSlideExtended[];
}

export interface IJackpotInfoExtended extends IJackpotInfo {
    jackpotsInfoIconUrl: string;
}

export interface IPageToolsJackpotExtended extends IPageToolsJackpot {
    engagementSliders: IEngagementSlideExtended[];
    jackpotsInfo: IJackpotInfoExtended[];
}

export interface IPageGamesExtended extends IPageGames {
    allGameMarkets: IMarketExtended[];
    biggestMarkets: IMarketExtended[];
    brandedGames: { game: IGameExtended }[];
    playlistsGames: { playlistGames: { game: IGameExtended }[] }[];
    categoriesGames: { categoryGames: { game: IGameExtended }[] }[];
}

//
// export interface IMarketGameCertificateExtended extends IMarketGameCertificate {
//     certificateUrl: string;
//     certificate1Url: string;
//     games: IGameExtended[];
// }

export interface IRoadmapGameMarketExtended extends IRoadmapGameMarket {
    gameMarket: IMarketExtended;
}

export interface IRoadmapGameExtended extends IRoadmapGame {
    game: IGameExtended;
    roadmapGameMarket?: Array<IRoadmapGameMarketExtended | null>;
}

export interface IPageRoadmapExtended extends IPageRoadmap {
    allGameMarkets: IMarketExtended[];
    biggestMarkets: IMarketExtended[];
    roadmapGame: IRoadmapGameExtended[];
}

export interface IBannerAssetExtended extends IBannerAsset {
    assetImageUrl?: string;
    assetVideoUrl?: string;
}

export interface IBannerExtended extends IBanner {
    game?: IGameExtended;
    banner1920x120?: IBannerAssetExtended;
    banner600x140?: IBannerAssetExtended;
}

export interface IBannersCarouselExtended extends IBannersCarousel {
    banners: IBannerExtended[];
}

export interface ISanityProvider {
    getSanityClient(): SanityClient | null;

    // marketGameCertificates(languageCode: EnumLanguageCode): Promise<IMarketGameCertificateExtended[]>;

    getAvailableGames(languageCode: EnumLanguageCode): Promise<IGameExtended[]>;

    gameMarketsExtended(region: string, languageCode: EnumLanguageCode): Promise<IPageGamesExtended>;

    gamePageGames(region: string, languageCode: EnumLanguageCode): Promise<IPageGamesExtended>;

    game(ids: string[]): Promise<IGame[]>;

    sectionSliderGames(): Promise<ISectionSliderGames[]>;

    sectionGroupSliderGames(codes: string[], languageCode: EnumLanguageCode): Promise<ISectionGroupSliderGames[]>;

    pageBanner(languageCode: string, pageId: string): Promise<IBannerExtended>;

    pageBannersCarousel(languageCode: string, pageId: string): Promise<IBannersCarouselExtended>;

    pageRoadmap(languageCode: string): Promise<IPageRoadmapExtended>;

    getAboutUsPage(regionCode: EnumRegionCode, languageCode: EnumLanguageCode): Promise<IPageAboutUsExtended | null>;

    getSectionGroupSliderGames(
        groupCode: string,
        regionCode: EnumRegionCode,
        languageCode: EnumLanguageCode
    ): Promise<ISectionGroupSliderGamesResponse | null>;

    getGameProfile(gameId: string, languageCode: EnumLanguageCode): Promise<IGameExtended | null>;

    getWebsiteConfig(regionCode: EnumRegionCode, languageCode: EnumLanguageCode): Promise<IWebsiteConfigExtended>;

    getPageToolsTournament(
        regionCode: EnumRegionCode,
        languageCode: EnumLanguageCode
    ): Promise<IPageToolsTournamentExtended>;

    getPageToolsJackpot(regionCode: EnumRegionCode, languageCode: EnumLanguageCode): Promise<IPageToolsJackpotExtended>;

    getGamesForRoadMap(languageCode: string): Promise<IGameShortInfo[]>;

    getNews(regionCode: EnumRegionCode, languageCode: EnumLanguageCode, id?: string | null): Promise<INewsExtended[]>;
}
