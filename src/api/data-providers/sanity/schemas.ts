/* tslint:disable */
/* eslint-disable */
import { GraphQLResolveInfo, GraphQLScalarType } from 'graphql';
/**
 * This file is auto-generated by graphql-schema-typescript
 * Please note that any changes in this file may be overwritten
 */
 

/*******************************
 *                             *
 *          TYPE DEFS          *
 *                             *
 *******************************/
/**
 * A Sanity document
 */
export interface IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
}

/** Use this to resolve interface type Document */
export type IPossibleDocumentTypeNames =
'Banner' |
'Country' |
'Currency' |
'EmailAddress' |
'Employee' |
'EngagementSlide' |
'Game' |
'GameCategory' |
'GameCertificate' |
'GameCode' |
'GamePlaylist' |
'GameRTP' |
'GameRibbon' |
'GameType' |
'JackpotInfo' |
'Language' |
'License' |
'Link' |
'Localization' |
'Market' |
'MediaTag' |
'News' |
'NewsCategory' |
'Office' |
'PageAboutUs' |
'PageGameSearch' |
'PageGames' |
'PageGamingSolutions' |
'PageHome' |
'PageLiveCasino' |
'PageMediaServices' |
'PageNews' |
'PageRoadmap' |
'PageToolsBonusCoin' |
'PageToolsGRC' |
'PageToolsJackpot' |
'PageToolsTournament' |
'PageTournaments' |
'Partner' |
'Phone' |
'Provider' |
'Region' |
'RoadmapGame' |
'RoadmapGameMarket' |
'SanityFileAsset' |
'SanityImageAsset' |
'SectionGroupSliderGames' |
'SectionSliderGames' |
'SiteConfig' |
'SitePage' |
'SocialNetwork' |
'Studio' |
'Tournament' |
'WebsiteConfig' |
'WidgetPartners' |
'WidgetSwiper';

export interface IDocumentNameMap {
  Document: IDocument;
  Banner: IBanner;
  Country: ICountry;
  Currency: ICurrency;
  EmailAddress: IEmailAddress;
  Employee: IEmployee;
  EngagementSlide: IEngagementSlide;
  Game: IGame;
  GameCategory: IGameCategory;
  GameCertificate: IGameCertificate;
  GameCode: IGameCode;
  GamePlaylist: IGamePlaylist;
  GameRTP: IGameRTP;
  GameRibbon: IGameRibbon;
  GameType: IGameType;
  JackpotInfo: IJackpotInfo;
  Language: ILanguage;
  License: ILicense;
  Link: ILink;
  Localization: ILocalization;
  Market: IMarket;
  MediaTag: IMediaTag;
  News: INews;
  NewsCategory: INewsCategory;
  Office: IOffice;
  PageAboutUs: IPageAboutUs;
  PageGameSearch: IPageGameSearch;
  PageGames: IPageGames;
  PageGamingSolutions: IPageGamingSolutions;
  PageHome: IPageHome;
  PageLiveCasino: IPageLiveCasino;
  PageMediaServices: IPageMediaServices;
  PageNews: IPageNews;
  PageRoadmap: IPageRoadmap;
  PageToolsBonusCoin: IPageToolsBonusCoin;
  PageToolsGRC: IPageToolsGRC;
  PageToolsJackpot: IPageToolsJackpot;
  PageToolsTournament: IPageToolsTournament;
  PageTournaments: IPageTournaments;
  Partner: IPartner;
  Phone: IPhone;
  Provider: IProvider;
  Region: IRegion;
  RoadmapGame: IRoadmapGame;
  RoadmapGameMarket: IRoadmapGameMarket;
  SanityFileAsset: ISanityFileAsset;
  SanityImageAsset: ISanityImageAsset;
  SectionGroupSliderGames: ISectionGroupSliderGames;
  SectionSliderGames: ISectionSliderGames;
  SiteConfig: ISiteConfig;
  SitePage: ISitePage;
  SocialNetwork: ISocialNetwork;
  Studio: IStudio;
  Tournament: ITournament;
  WebsiteConfig: IWebsiteConfig;
  WidgetPartners: IWidgetPartners;
  WidgetSwiper: IWidgetSwiper;
}

export type IBlockOrImage = IBlock | IImage;

/** Use this to resolve union type BlockOrImage */
export type IPossibleBlockOrImageTypeNames = 'Block' | 'Image';

export interface IBlockOrImageNameMap {
  BlockOrImage: IBlockOrImage;
  Block: IBlock;
  Image: IImage;
}

export type ILinkOrSitePage = ILink | ISitePage;

/** Use this to resolve union type LinkOrSitePage */
export type IPossibleLinkOrSitePageTypeNames = 'Link' | 'SitePage';

export interface ILinkOrSitePageNameMap {
  LinkOrSitePage: ILinkOrSitePage;
  Link: ILink;
  SitePage: ISitePage;
}

export type ISingleLine1OrSingleLine2OrSingleLine3OrTextarea =
ISingleLine1 |
ISingleLine2 |
ISingleLine3 |
ITextarea;

/** Use this to resolve union type SingleLine1OrSingleLine2OrSingleLine3OrTextarea */
export type IPossibleSingleLine1OrSingleLine2OrSingleLine3OrTextareaTypeNames =
'SingleLine1' |
'SingleLine2' |
'SingleLine3' |
'Textarea';

export interface ISingleLine1OrSingleLine2OrSingleLine3OrTextareaNameMap {
  SingleLine1OrSingleLine2OrSingleLine3OrTextarea: ISingleLine1OrSingleLine2OrSingleLine3OrTextarea;
  SingleLine1: ISingleLine1;
  SingleLine2: ISingleLine2;
  SingleLine3: ISingleLine3;
  Textarea: ITextarea;
}

export interface IBanner extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  banner1920x120?: IBannerAsset;
  banner600x140?: IBannerAsset;
  bannerName?: ILocaleString;
  bannerOnDesktop?: string;
  bannerOnMobileLandscape?: string;
  bannerOnMobilePortrait?: string;
  bannerType?: string;
  code?: ISlug;
  game?: IGame;
  isActive?: boolean;
  url?: IUrlTarget;
  urlType?: string;
}

export interface IBannerAsset {
  _key?: string;
  _type?: string;
  assetImage?: ILocaleImage;
  assetType?: string;
  assetVideo?: IFile;
}

export interface IBannersCarousel {
  _key?: string;
  _type?: string;
  
  /**
   * Delay between banners in seconds. To disable autoplay set value to 0
   */
  autoPlay?: number;
  banners?: Array<IBanner | null>;
  isActive?: boolean;
  
  /**
   * Enable continuous loop mode
   */
  isLoop?: boolean;
}

export interface IBlock {
  _key?: string;
  _type?: string;
  children?: Array<ISpan | null>;
  list?: string;
  style?: string;
}

export interface IColor {
  _key?: string;
  _type?: string;
  alpha?: number;
  hex?: string;
  hsl?: IHslaColor;
  hsv?: IHsvaColor;
  rgb?: IRgbaColor;
}

export interface ICountry extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  countryCode?: string;
  countryIcon?: ILocaleImage;
  countryName?: string;
  countryNativeName?: string;
  currencies?: Array<ICurrency | null>;
  games?: Array<IGameLine | null>;
  languages?: Array<ILanguage | null>;
}

export interface ICountryGames {
  _key?: string;
  _type?: string;
  country?: ICountry;
  games?: Array<IGameLine | null>;
}

export interface ICurrency extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  currencyCode?: string;
  currencyIcon?: ILocaleImage;
  currencyName?: string;
  currencyNativeName?: string;
}

export interface IEmailAddress extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  code?: ISlug;
  emailAddress?: string;
  emailTitle?: ILocaleString;
  isActive?: boolean;
}

export interface IEmployee extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  employeeDescription?: ILocaleText;
  employeeName?: ILocaleString;
  employeePhoto?: ILocaleImage;
  employeePosition?: ILocaleString;
  isActive?: boolean;
}

export interface IEngagementSlide extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  header1?: ILocaleString;
  header2?: ILocaleString;
  image?: ILocaleImage;
  imageType?: string;
  isActive?: boolean;
  text1?: ILocaleText;
  text2?: ILocaleText;
}

export interface IFile {
  _key?: string;
  _type?: string;
  asset?: ISanityFileAsset;
}

export interface IGame extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  availableIn?: Array<string | null>;
  certificatesPerMarket?: Array<IGameMarketCertificates | null>;
  gameBillboard?: ILocaleImage;
  gameBrandedPoster?: ILocaleImage;
  gameCode?: Array<IGameCode | null>;
  gameDescription?: ILocaleText;
  gameFeatures?: Array<IGameFeatures | null>;
  gameHDPoster?: ILocaleImage;
  gameIcon?: ILocaleImage;
  gameId?: ISlug;
  gameImage?: ILocaleImage;
  gameInfo?: IGameInfo;
  gameLogo?: ILocaleImage;
  gameManage?: IGameManage;
  gameName?: ILocaleString;
  gameOrientation?: string;
  gamePoster?: ILocaleImage;
  gameRTP?: Array<IGameRTP | null>;
  gameRibbon?: IGameRibbon;
  gameScreenshotHD?: Array<ILocaleImage | null>;
  gameScreenshotPoster?: Array<ILocaleImage | null>;
  gameTop4Poster?: ILocaleImage;
  gameTowerPoster?: ILocaleImage;
  gameType?: Array<IGameType | null>;
  gameVideo?: IVideo;
  gameVideos?: Array<IVideoUrl | null>;
  
  /**
   * Auto detect will show video if video area contains all required video and posters instead Game HD Poster and Tower Poster for mobile. By default is auto.
   */
  header?: string;
  isActive?: boolean;
  paUrl?: string;
  releaseNote?: IFile;
  saleSheet?: IFile;
  
  /**
   * Allows you to choose options for displaying the game code
   */
  showGameCode?: string;
  
  /**
   * Allows you to choose options for displaying the game RTP
   */
  showGameRTP?: string;
  siteDescription?: ILocaleText;
}

export interface IGameAPI {
  _key?: string;
  _type?: string;
  playerApiUrl?: string;
  siteApiUrl?: string;
  siteToken?: string;
}

export interface IGameCategory extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  categoryCode?: ISlug;
  categoryGames?: Array<IGameLine | null>;
  categoryName?: ILocaleString;
  categoryRegion?: string;
  categoryType?: string;
  countryGames?: Array<ICountryGames | null>;
  isActive?: boolean;
  showGamesFrom?: string;
}

export interface IGameCertificate extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  gameCertificateCode?: ISlug;
  gameCertificateName?: ILocaleString;
}

export interface IGameCode extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  gameCode?: string;
  
  /**
   * Game info from BI report. Updated using Cron.
   */
  gameInfo?: string;
  gameProvider?: IProvider;
  
  /**
   * Game marketing materials from Box.com. Updated using Cron and Jenkins job from box.com
   */
  marketingMaterials?: string;
}

export interface IGameFeatures {
  _key?: string;
  _type?: string;
  gameFeatureDescription?: ILocaleText;
  gameFeaturePoster?: ILocaleImage;
  gameFeatureTitle?: ILocaleString;
}

export interface IGameInfo {
  _key?: string;
  _type?: string;
  
  /**
   * Field allows to format how game release date will be shown or show "Coming soon" message.
   */
  formatOfReleaseDate?: string;
  isFreeBets?: boolean;
  isLuckyEnvelopes?: boolean;
  isMustWinJackpot?: boolean;
  isTournaments?: boolean;
  
  /**
   * 6 reels , 2-7 rows
   */
  layout?: string;
  markets?: Array<IMarket | null>;
  releaseDates?: Array<IGameReleaseDate | null>;
  
  /**
   * 96.5
   */
  rtp?: number;
  
  /**
   * 4/5
   */
  volatility?: string;
  
  /**
   * 117649
   */
  ways?: string;
}

export interface IGameLine {
  _key?: string;
  _type?: string;
  game?: IGame;
  gameRibbon?: IGameRibbon;
  layout?: string;
}

export interface IGameManage {
  _key?: string;
  _type?: string;
  releaseDate?: IDate;
  releaseText?: ILocaleString;
  status?: string;
}

export interface IGameMarketCertificates {
  _key?: string;
  _type?: string;
  certificate?: IFile;
  certificate_1?: IFile;
  certificate_2?: IFile;
  comingSoon?: boolean;
  gameMarket?: IMarket;
}

export interface IGamePlaylist extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  isActive?: boolean;
  playlistBanner?: ILocaleImage;
  playlistCode?: ISlug;
  playlistDescription?: ILocaleText;
  playlistGames?: Array<IGame | null>;
  playlistName?: ILocaleString;
  playlistPoster?: ILocaleImage;
  playlistRegion?: string;
}

export interface IGameRTP extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  gameRTP?: string;
}

export interface IGameReleaseDate {
  _key?: string;
  _type?: string;
  gameMarket?: IMarket;
  releaseDate?: IDate;
}

export interface IGameRibbon extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  gameRibbonCode?: ISlug;
  gameRibbonImage?: ILocaleImage;
  gameRibbonName?: ILocaleString;
}

export interface IGameType extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  gameTypeCode?: ISlug;
  gameTypeTitle?: ILocaleString;
}

export interface IGeopoint {
  _key?: string;
  _type?: string;
  alt?: number;
  lat?: number;
  lng?: number;
}

export interface IHeaderText {
  _key?: string;
  _type?: string;
  headerTextArea?: ILocaleText;
  headerTitle?: IHeaderTitle;
}

export interface IHeaderTicker {
  _key?: string;
  _type?: string;
  isActive?: boolean;
  tickerDate?: IDate;
}

export interface IHeaderTitle {
  _key?: string;
  _type?: string;
  title1?: ILocaleString;
  title2?: ILocaleString;
  title3?: ILocaleString;
}

export interface IHslaColor {
  _key?: string;
  _type?: string;
  a?: number;
  h?: number;
  l?: number;
  s?: number;
}

export interface IHsvaColor {
  _key?: string;
  _type?: string;
  a?: number;
  h?: number;
  s?: number;
  v?: number;
}

export interface IImage {
  _key?: string;
  _type?: string;
  asset?: ISanityImageAsset;
  crop?: ISanityImageCrop;
  hotspot?: ISanityImageHotspot;
}

export interface IJackpotInfo extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  code?: ISlug;
  description?: ILocaleText;
  header?: ILocaleString;
  icon?: ILocaleImage;
  iconHover?: ILocaleImage;
  isActive?: boolean;
}

export interface ILanguage extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  languageCode?: string;
  languageIcon?: ILocaleImage;
  languageName?: string;
  languageNativeName?: string;
}

export interface ILicense extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  isActive?: boolean;
  jurisdiction?: ILocaleString;
  licenseDescription?: ILocaleText;
  licenseNo?: string;
  licensePhoto?: ILocaleImage;
  licenseUrl?: string;
  regulator?: ILocaleString;
}

export interface ILink extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  code?: ISlug;
  isActive?: boolean;
  isExternal?: boolean;
  title?: ILocaleString;
}

export interface ILocaleBlock {
  _key?: string;
  _type?: string;
  enRaw?: IJSON;
  zhCnRaw?: IJSON;
}

export interface ILocaleImage {
  _key?: string;
  _type?: string;
  en?: IImage;
  zhCn?: IImage;
}

export interface ILocaleString {
  _key?: string;
  _type?: string;
  en?: string;
  zhCn?: string;
}

export interface ILocaleText {
  _key?: string;
  _type?: string;
  en?: string;
  zhCn?: string;
}

export interface ILocaleUrl {
  _key?: string;
  _type?: string;
  en?: string;
  zhCn?: string;
}

export interface ILocalization extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  dataArray?: Array<ISingleLine1OrSingleLine2OrSingleLine3OrTextarea | null>;
  textCode?: ISlug;
}

export interface IMaintenance {
  _key?: string;
  _type?: string;
  
  /**
   * Show or hide dialog with message or image
   */
  isActive?: boolean;
  landscapeImage?: ILocaleImage;
  portraitImage?: ILocaleImage;
  textMessage?: ILocaleString;
}

export interface IMarket extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  code?: ISlug;
  isActive?: boolean;
  marketIcon?: ILocaleImage;
  marketName?: ILocaleString;
}

export interface IMediaServicesBlock {
  _key?: string;
  _type?: string;
  blockCode?: ISlug;
  header?: ILocaleString;
  icon?: ILocaleImage;
  iconHover?: ILocaleImage;
  text?: ILocaleText;
}

export interface IMediaServicesTech {
  _key?: string;
  _type?: string;
  blockCode?: ISlug;
  cardType?: string;
  header?: ILocaleString;
  icon?: ILocaleImage;
  iconHover?: ILocaleImage;
  text?: ILocaleText;
}

export interface IMediaTag extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  name?: ISlug;
}

export interface INews extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  block?: ILocaleBlock;
  game?: IGame;
  headerPromoVideo?: IVideo;
  highlightedTitle?: ILocaleString;
  isActive?: boolean;
  newsCategories?: Array<INewsCategory | null>;
  newsDate?: IDate;
  newsPreviewImage?: ILocaleImage;
  newsType?: string;
  region?: string;
  text?: ILocaleText;
  textType?: string;
  title?: ILocaleString;
}

export interface INewsCategory extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  newsCategoryCode?: ISlug;
  newsCategoryTitle?: ILocaleString;
}

export interface IOffice extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  address?: ILocaleText;
  code?: ISlug;
  companyName?: ILocaleString;
  country?: ILocaleString;
  email?: string;
  isActive?: boolean;
  largePhotos?: Array<IImage | null>;
  leftOnMap?: number;
  officeName?: ILocaleString;
  officePhoto?: ILocaleImage;
  phone1?: string;
  phone2?: string;
  topOnMap?: number;
}

export interface IPageAboutUs extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  canDeploy?: boolean;
  employees?: Array<IEmployee | null>;
  headerPromoVideo?: IVideo;
  headerText?: IHeaderText;
  isActive?: boolean;
  licenses?: Array<ILicense | null>;
  offices?: Array<IOffice | null>;
  pageTitle?: ILocaleString;
}

export interface IPageGameSearch extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  banners?: IBannersCarousel;
  bestGames?: Array<IGame | null>;
  pageTitle?: ILocaleString;
}

export interface IPageGames extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  canDeploy?: boolean;
  gamePlaylist?: Array<IGamePlaylist | null>;
  gamesBranded?: Array<IGameLine | null>;
  gamesCategories?: Array<IGameCategory | null>;
  headerPromoVideo?: IVideo;
  headerText?: IHeaderText;
  isActive?: boolean;
  pageTitle?: ILocaleString;
}

export interface IPageGamingSolutions extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  canDeploy?: boolean;
  cloudArray?: Array<IMediaServicesBlock | null>;
  dataArray?: Array<IMediaServicesBlock | null>;
  headerText?: IHeaderText;
  infraArray?: Array<IMediaServicesBlock | null>;
  isActive?: boolean;
  mobileArray?: Array<IMediaServicesBlock | null>;
  pageTitle?: ILocaleString;
}

export interface IPageHome extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  canDeploy?: boolean;
  headerPromoVideo?: IVideo;
  headerText?: IHeaderText;
  isActive?: boolean;
  pageTitle?: ILocaleString;
  partners?: Array<IPartner | null>;
  topGame1?: IGameLine;
  topGame2?: IGameLine;
  topGame3?: IGameLine;
  topGame4?: IGameLine;
}

export interface IPageLiveCasino extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  canDeploy?: boolean;
  dataArray?: Array<IMediaServicesBlock | null>;
  headerPromoVideo?: IVideo;
  headerText?: IHeaderText;
  infoArray?: Array<IMediaServicesBlock | null>;
  isActive?: boolean;
  liveGames?: Array<IGameLine | null>;
  pageTitle?: ILocaleString;
  studios?: Array<IStudio | null>;
}

export interface IPageMediaServices extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  canDeploy?: boolean;
  dataArray?: Array<IMediaServicesBlock | null>;
  engagementSliders?: Array<IEngagementSlide | null>;
  headerText?: IHeaderText;
  isActive?: boolean;
  pageTitle?: ILocaleString;
  techArray?: Array<IMediaServicesTech | null>;
}

export interface IPageNews extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  banners?: IBannersCarousel;
  canDeploy?: boolean;
  headerPromoVideo?: IVideo;
  headerText?: IHeaderText;
  isActive?: boolean;
  newsCategories?: Array<INewsCategory | null>;
  pageTitle?: ILocaleString;
}

export interface IPageRoadmap extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  availableMarkets?: Array<IMarket | null>;
  banners?: IBannersCarousel;
  biggestMarkets?: Array<IMarket | null>;
  defaultMarket?: IMarket;
  latestReleasesMonth?: number;
  pageTitle?: ILocaleString;
  roadmapGame?: Array<IRoadmapGame | null>;
  
  /**
   * Hide or show the toolbar with the choice of the month in the "Full roadmap" state
   */
  showPerMonthToolbar?: boolean;
  upcomingMonthThirdParty?: number;
}

export interface IPageToolsBonusCoin extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  canDeploy?: boolean;
  engagementSliders?: Array<IEngagementSlide | null>;
  headerPromoVideo?: IVideo;
  headerText?: IHeaderText;
  isActive?: boolean;
  pageTitle?: ILocaleString;
}

export interface IPageToolsGRC extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  canDeploy?: boolean;
  challenges?: Array<IEngagementSlide | null>;
  grcGames?: Array<IGameLine | null>;
  headerText?: IHeaderText;
  isActive?: boolean;
  jackpotsInfo?: Array<IJackpotInfo | null>;
  pageTitle?: ILocaleString;
  piggyRewardsSliders?: Array<IEngagementSlide | null>;
  tournamentsJackpot?: Array<IEngagementSlide | null>;
}

export interface IPageToolsJackpot extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  canDeploy?: boolean;
  engagementSliders?: Array<IEngagementSlide | null>;
  headerText?: IHeaderText;
  isActive?: boolean;
  jackpotsInfo?: Array<IJackpotInfo | null>;
  pageTitle?: ILocaleString;
}

export interface IPageToolsTournament extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  canDeploy?: boolean;
  engagementSliders?: Array<IEngagementSlide | null>;
  headerText?: IHeaderText;
  isActive?: boolean;
  pageTitle?: ILocaleString;
}

export interface IPageTournaments extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  maintenance?: IMaintenance;
  pageTitle?: ILocaleString;
}

export interface IPartner extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  isActive?: boolean;
  partnerCode?: ISlug;
  partnerImage?: ILocaleImage;
  partnerName?: ILocaleString;
  partnerUrl?: string;
}

export interface IPhone extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  code?: ISlug;
  isActive?: boolean;
  phoneNumber?: string;
  phoneTitle?: ILocaleString;
}

export interface IProvider extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  providerCode?: string;
  providerName?: string;
}

export interface IRegion extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  regionCode?: string;
}

export interface IRgbaColor {
  _key?: string;
  _type?: string;
  a?: number;
  b?: number;
  g?: number;
  r?: number;
}

export interface IRoadmapGame extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  comingSoon?: boolean;
  game?: IGame;
  hideInAllMarkets?: boolean;
  roadmapGameMarket?: Array<IRoadmapGameMarket | null>;
}

export interface IRoadmapGameMarket extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  endDate?: IDate;
  gameMarket?: IMarket;
  startDate?: IDate;
}

export interface IRootQuery {
  Banner?: IBanner;
  Country?: ICountry;
  Currency?: ICurrency;
  Document?: IDocument;
  EmailAddress?: IEmailAddress;
  Employee?: IEmployee;
  EngagementSlide?: IEngagementSlide;
  Game?: IGame;
  GameCategory?: IGameCategory;
  GameCertificate?: IGameCertificate;
  GameCode?: IGameCode;
  GamePlaylist?: IGamePlaylist;
  GameRTP?: IGameRTP;
  GameRibbon?: IGameRibbon;
  GameType?: IGameType;
  JackpotInfo?: IJackpotInfo;
  Language?: ILanguage;
  License?: ILicense;
  Link?: ILink;
  Localization?: ILocalization;
  Market?: IMarket;
  MediaTag?: IMediaTag;
  News?: INews;
  NewsCategory?: INewsCategory;
  Office?: IOffice;
  PageAboutUs?: IPageAboutUs;
  PageGameSearch?: IPageGameSearch;
  PageGames?: IPageGames;
  PageGamingSolutions?: IPageGamingSolutions;
  PageHome?: IPageHome;
  PageLiveCasino?: IPageLiveCasino;
  PageMediaServices?: IPageMediaServices;
  PageNews?: IPageNews;
  PageRoadmap?: IPageRoadmap;
  PageToolsBonusCoin?: IPageToolsBonusCoin;
  PageToolsGRC?: IPageToolsGRC;
  PageToolsJackpot?: IPageToolsJackpot;
  PageToolsTournament?: IPageToolsTournament;
  PageTournaments?: IPageTournaments;
  Partner?: IPartner;
  Phone?: IPhone;
  Provider?: IProvider;
  Region?: IRegion;
  RoadmapGame?: IRoadmapGame;
  RoadmapGameMarket?: IRoadmapGameMarket;
  SanityFileAsset?: ISanityFileAsset;
  SanityImageAsset?: ISanityImageAsset;
  SectionGroupSliderGames?: ISectionGroupSliderGames;
  SectionSliderGames?: ISectionSliderGames;
  SiteConfig?: ISiteConfig;
  SitePage?: ISitePage;
  SocialNetwork?: ISocialNetwork;
  Studio?: IStudio;
  Tournament?: ITournament;
  WebsiteConfig?: IWebsiteConfig;
  WidgetPartners?: IWidgetPartners;
  WidgetSwiper?: IWidgetSwiper;
  allBanner: Array<IBanner>;
  allCountry: Array<ICountry>;
  allCurrency: Array<ICurrency>;
  allDocument: Array<IDocument>;
  allEmailAddress: Array<IEmailAddress>;
  allEmployee: Array<IEmployee>;
  allEngagementSlide: Array<IEngagementSlide>;
  allGame: Array<IGame>;
  allGameCategory: Array<IGameCategory>;
  allGameCertificate: Array<IGameCertificate>;
  allGameCode: Array<IGameCode>;
  allGamePlaylist: Array<IGamePlaylist>;
  allGameRTP: Array<IGameRTP>;
  allGameRibbon: Array<IGameRibbon>;
  allGameType: Array<IGameType>;
  allJackpotInfo: Array<IJackpotInfo>;
  allLanguage: Array<ILanguage>;
  allLicense: Array<ILicense>;
  allLink: Array<ILink>;
  allLocalization: Array<ILocalization>;
  allMarket: Array<IMarket>;
  allMediaTag: Array<IMediaTag>;
  allNews: Array<INews>;
  allNewsCategory: Array<INewsCategory>;
  allOffice: Array<IOffice>;
  allPageAboutUs: Array<IPageAboutUs>;
  allPageGameSearch: Array<IPageGameSearch>;
  allPageGames: Array<IPageGames>;
  allPageGamingSolutions: Array<IPageGamingSolutions>;
  allPageHome: Array<IPageHome>;
  allPageLiveCasino: Array<IPageLiveCasino>;
  allPageMediaServices: Array<IPageMediaServices>;
  allPageNews: Array<IPageNews>;
  allPageRoadmap: Array<IPageRoadmap>;
  allPageToolsBonusCoin: Array<IPageToolsBonusCoin>;
  allPageToolsGRC: Array<IPageToolsGRC>;
  allPageToolsJackpot: Array<IPageToolsJackpot>;
  allPageToolsTournament: Array<IPageToolsTournament>;
  allPageTournaments: Array<IPageTournaments>;
  allPartner: Array<IPartner>;
  allPhone: Array<IPhone>;
  allProvider: Array<IProvider>;
  allRegion: Array<IRegion>;
  allRoadmapGame: Array<IRoadmapGame>;
  allRoadmapGameMarket: Array<IRoadmapGameMarket>;
  allSanityFileAsset: Array<ISanityFileAsset>;
  allSanityImageAsset: Array<ISanityImageAsset>;
  allSectionGroupSliderGames: Array<ISectionGroupSliderGames>;
  allSectionSliderGames: Array<ISectionSliderGames>;
  allSiteConfig: Array<ISiteConfig>;
  allSitePage: Array<ISitePage>;
  allSocialNetwork: Array<ISocialNetwork>;
  allStudio: Array<IStudio>;
  allTournament: Array<ITournament>;
  allWebsiteConfig: Array<IWebsiteConfig>;
  allWidgetPartners: Array<IWidgetPartners>;
  allWidgetSwiper: Array<IWidgetSwiper>;
}

export interface ISanityAssetSourceData {
  _key?: string;
  _type?: string;
  
  /**
   * The unique ID for the asset within the originating source so you can programatically find back to it
   */
  id?: string;
  
  /**
   * A canonical name for the source this asset is originating from
   */
  name?: string;
  
  /**
   * A URL to find more information about this asset in the originating source
   */
  url?: string;
}

export interface ISanityFileAsset extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  altText?: string;
  assetId?: string;
  description?: string;
  extension?: string;
  label?: string;
  mimeType?: string;
  originalFilename?: string;
  path?: string;
  sha1hash?: string;
  size?: number;
  source?: ISanityAssetSourceData;
  title?: string;
  url?: string;
}

export interface ISanityImageAsset extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  altText?: string;
  assetId?: string;
  description?: string;
  extension?: string;
  label?: string;
  metadata?: ISanityImageMetadata;
  mimeType?: string;
  originalFilename?: string;
  path?: string;
  sha1hash?: string;
  size?: number;
  source?: ISanityAssetSourceData;
  title?: string;
  uploadId?: string;
  url?: string;
}

export interface ISanityImageCrop {
  _key?: string;
  _type?: string;
  bottom?: number;
  left?: number;
  right?: number;
  top?: number;
}

export interface ISanityImageDimensions {
  _key?: string;
  _type?: string;
  aspectRatio?: number;
  height?: number;
  width?: number;
}

export interface ISanityImageHotspot {
  _key?: string;
  _type?: string;
  height?: number;
  width?: number;
  x?: number;
  y?: number;
}

export interface ISanityImageMetadata {
  _key?: string;
  _type?: string;
  blurHash?: string;
  dimensions?: ISanityImageDimensions;
  hasAlpha?: boolean;
  isOpaque?: boolean;
  location?: IGeopoint;
  lqip?: string;
  palette?: ISanityImagePalette;
}

export interface ISanityImagePalette {
  _key?: string;
  _type?: string;
  darkMuted?: ISanityImagePaletteSwatch;
  darkVibrant?: ISanityImagePaletteSwatch;
  dominant?: ISanityImagePaletteSwatch;
  lightMuted?: ISanityImagePaletteSwatch;
  lightVibrant?: ISanityImagePaletteSwatch;
  muted?: ISanityImagePaletteSwatch;
  vibrant?: ISanityImagePaletteSwatch;
}

export interface ISanityImagePaletteSwatch {
  _key?: string;
  _type?: string;
  background?: string;
  foreground?: string;
  population?: number;
  title?: string;
}

export interface ISectionGroupSliderGames extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  groupCode?: ISlug;
  groupName?: ILocaleString;
  isActive?: boolean;
  region?: string;
  sliderGames?: Array<ISectionSliderGames | null>;
}

export interface ISectionSliderGames extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  games?: Array<IGame | null>;
  isActive?: boolean;
  isShowGamesNumber?: boolean;
  isVisibleSectionLogo?: boolean;
  layout?: string;
  region?: string;
  sectionCode?: ISlug;
  sectionLogo?: ILocaleImage;
  sectionName?: ILocaleString;
  sortGames?: string;
}

export interface ISingleLine1 {
  _key?: string;
  _type?: string;
  text?: ILocaleString;
  textCode?: ISlug;
}

export interface ISingleLine2 {
  _key?: string;
  _type?: string;
  text1?: ILocaleString;
  text2?: ILocaleString;
  textCode?: ISlug;
}

export interface ISingleLine3 {
  _key?: string;
  _type?: string;
  text1?: ILocaleString;
  text2?: ILocaleString;
  text3?: ILocaleString;
  textCode?: ISlug;
}

export interface ISiteConfig extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  emails?: Array<IEmailAddress | null>;
  employees?: Array<ILinkOrSitePage | null>;
  homePage?: ISitePage;
  languages?: Array<string | null>;
  
  /**
   * A meta description is an element in the HTML code of a web page that provides a brief description of the content of that page.
   */
  metaDescription?: string;
  
  /**
   * Meta Keywords are a specific type of meta tag that appear in the HTML code of a Web page and help tell search engines what the topic of the page is.
   */
  metaKeywords?: string;
  metaRobots?: string;
  phones?: Array<IPhone | null>;
  region?: string;
  title?: ILocaleString;
}

export interface ISitePage extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  code?: ISlug;
  isActive?: boolean;
  name?: string;
  region?: string;
  title?: ILocaleString;
  version?: string;
  widgets?: Array<IWidgetPartners | null>;
}

export interface ISlug {
  _key?: string;
  _type?: string;
  current?: string;
}

export interface ISocialNetwork extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  code?: ISlug;
  isActive?: boolean;
  snIcon?: ILocaleImage;
  snName?: ILocaleString;
  snUrl?: string;
}

export interface ISpan {
  _key?: string;
  _type?: string;
  marks?: Array<string | null>;
  text?: string;
}

export interface IStudio extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  code?: ISlug;
  studioName?: ILocaleString;
  studioPhoto?: ILocaleImage;
}

export interface ITextarea {
  _key?: string;
  _type?: string;
  text?: ILocaleText;
  textCode?: ISlug;
}

export interface ITournament extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  code?: ISlug;
  isActive?: boolean;
  name?: ILocaleString;
}

export interface IUrlTarget {
  _key?: string;
  _type?: string;
  url?: string;
  urlTarget?: string;
}

export interface IVideo {
  _key?: string;
  _type?: string;
  file?: IFile;
  headerTicker?: IHeaderTicker;
  height?: number;
  mobileVideoPoster?: ILocaleImage;
  type?: string;
  
  /**
   * Paste in the full URL for your video
   */
  url?: string;
  videoPoster?: ILocaleImage;
  width?: number;
}

export interface IVideoUrl {
  _key?: string;
  _type?: string;
  file?: IFile;
  languageCode?: string;
  type?: string;
  url?: string;
}

export interface IWebsiteConfig extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  emails?: Array<IEmailAddress | null>;
  facebookUrl?: string;
  footerLicenses?: Array<ILicense | null>;
  gameAPI?: IGameAPI;
  instagramUrl?: string;
  linkedInUrl?: string;
  
  /**
   * A meta description is an element in the HTML code of a web page that provides a brief description of the content of that page.
   */
  metaDescription?: string;
  
  /**
   * Meta Keywords are a specific type of meta tag that appear in the HTML code of a Web page and help tell search engines what the topic of the page is.
   */
  metaKeywords?: string;
  metaRobots?: string;
  phones?: Array<IPhone | null>;
  socialNetworks?: Array<ISocialNetwork | null>;
  
  /**
   * SkyWind Group Corporate Portal
   */
  title?: string;
  youtubeUrl?: string;
}

export interface IWidgetPartners extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  code?: ISlug;
  isActive?: boolean;
  name?: string;
  partners?: Array<IPartner | null>;
  title?: ILocaleString;
}

export interface IWidgetSwiper extends IDocument {
  
  /**
   * Date the document was created
   */
  _createdAt?: IDateTime;
  
  /**
   * Document ID
   */
  _id?: string;
  _key?: string;
  
  /**
   * Current document revision
   */
  _rev?: string;
  
  /**
   * Document type
   */
  _type?: string;
  
  /**
   * Date the document was last modified
   */
  _updatedAt?: IDateTime;
  code?: ISlug;
  isActive?: boolean;
  name?: string;
  swiperParameters?: IWidgetSwiperParameters;
}

export interface IWidgetSwiperParameters {
  _key?: string;
  _type?: string;
  
  /**
   * Set to false to disable swiping to next slide direction (to right or bottom). Default: true
   */
  allowSlideNext?: boolean;
  
  /**
   * Set to false to disable swiping to previous slide direction (to left or top). Default: true
   */
  allowSlidePrev?: boolean;
  
  /**
   * If false, then the only way to switch the slide is use of external API functions like slidePrev or slideNext. Default: true
   */
  allowTouchMove?: boolean;
  
  /**
   * Set to true and slider wrapper will adapt its height to the height of the currently active slide. Default: false
   */
  autoHeight?: boolean;
  
  /**
   * If true, then active slide will be centered, not always on the left side. Default: false
   */
  centeredSlides?: boolean;
  
  /**
   * Can be 'horizontal' or 'vertical' (for vertical slider). Default: horizontal
   */
  direction?: string;
}

export enum ISortOrder {
  
  /**
   * Sorts on the value in ascending order.
   */
  ASC = 'ASC',
  
  /**
   * Sorts on the value in descending order.
   */
  DESC = 'DESC'
}

/**
 * A date string, such as 2007-12-03, compliant with the `full-date` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.
 */
export type IDate = any;

/**
 * A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.
 */
export type IDateTime = any;

/**
 * The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
 */
export type IJSON = any;

export interface IBannerAssetFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  assetImage?: ILocaleImageFilter;
  assetType?: IStringFilter;
  assetVideo?: IFileFilter;
}

export interface IBannerAssetSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  assetImage?: ILocaleImageSorting;
  assetType?: ISortOrder;
  assetVideo?: IFileSorting;
}

export interface IBannerFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  banner1920x120?: IBannerAssetFilter;
  banner600x140?: IBannerAssetFilter;
  bannerName?: ILocaleStringFilter;
  bannerOnDesktop?: IStringFilter;
  bannerOnMobileLandscape?: IStringFilter;
  bannerOnMobilePortrait?: IStringFilter;
  bannerType?: IStringFilter;
  code?: ISlugFilter;
  game?: IGameFilter;
  isActive?: IBooleanFilter;
  url?: IUrlTargetFilter;
  urlType?: IStringFilter;
}

export interface IBannerSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  banner1920x120?: IBannerAssetSorting;
  banner600x140?: IBannerAssetSorting;
  bannerName?: ILocaleStringSorting;
  bannerOnDesktop?: ISortOrder;
  bannerOnMobileLandscape?: ISortOrder;
  bannerOnMobilePortrait?: ISortOrder;
  bannerType?: ISortOrder;
  code?: ISlugSorting;
  isActive?: ISortOrder;
  url?: IUrlTargetSorting;
  urlType?: ISortOrder;
}

export interface IBannersCarouselFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  autoPlay?: IFloatFilter;
  isActive?: IBooleanFilter;
  isLoop?: IBooleanFilter;
}

export interface IBannersCarouselSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  autoPlay?: ISortOrder;
  isActive?: ISortOrder;
  isLoop?: ISortOrder;
}

export interface IBooleanFilter {
  
  /**
   * Checks if the value is equal to the given input.
   */
  eq?: boolean;
  
  /**
   * Checks if the value is not equal to the given input.
   */
  neq?: boolean;
}

export interface IColorFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  alpha?: IFloatFilter;
  hex?: IStringFilter;
  hsl?: IHslaColorFilter;
  hsv?: IHsvaColorFilter;
  rgb?: IRgbaColorFilter;
}

export interface IColorSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  alpha?: ISortOrder;
  hex?: ISortOrder;
  hsl?: IHslaColorSorting;
  hsv?: IHsvaColorSorting;
  rgb?: IRgbaColorSorting;
}

export interface ICountryFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  countryCode?: IStringFilter;
  countryIcon?: ILocaleImageFilter;
  countryName?: IStringFilter;
  countryNativeName?: IStringFilter;
}

export interface ICountryGamesFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  country?: ICountryFilter;
}

export interface ICountryGamesSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
}

export interface ICountrySorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  countryCode?: ISortOrder;
  countryIcon?: ILocaleImageSorting;
  countryName?: ISortOrder;
  countryNativeName?: ISortOrder;
}

export interface ICurrencyFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  currencyCode?: IStringFilter;
  currencyIcon?: ILocaleImageFilter;
  currencyName?: IStringFilter;
  currencyNativeName?: IStringFilter;
}

export interface ICurrencySorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  currencyCode?: ISortOrder;
  currencyIcon?: ILocaleImageSorting;
  currencyName?: ISortOrder;
  currencyNativeName?: ISortOrder;
}

export interface IDateFilter {
  
  /**
   * Checks if the value is equal to the given input.
   */
  eq?: IDate;
  
  /**
   * Checks if the value is greater than the given input.
   */
  gt?: IDate;
  
  /**
   * Checks if the value is greater than or equal to the given input.
   */
  gte?: IDate;
  
  /**
   * Checks if the value is lesser than the given input.
   */
  lt?: IDate;
  
  /**
   * Checks if the value is lesser than or equal to the given input.
   */
  lte?: IDate;
  
  /**
   * Checks if the value is not equal to the given input.
   */
  neq?: IDate;
}

export interface IDatetimeFilter {
  
  /**
   * Checks if the value is equal to the given input.
   */
  eq?: IDateTime;
  
  /**
   * Checks if the value is greater than the given input.
   */
  gt?: IDateTime;
  
  /**
   * Checks if the value is greater than or equal to the given input.
   */
  gte?: IDateTime;
  
  /**
   * Checks if the value is lesser than the given input.
   */
  lt?: IDateTime;
  
  /**
   * Checks if the value is lesser than or equal to the given input.
   */
  lte?: IDateTime;
  
  /**
   * Checks if the value is not equal to the given input.
   */
  neq?: IDateTime;
}

export interface IDocumentFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
}

export interface IDocumentSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
}

export interface IEmailAddressFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  code?: ISlugFilter;
  emailAddress?: IStringFilter;
  emailTitle?: ILocaleStringFilter;
  isActive?: IBooleanFilter;
}

export interface IEmailAddressSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  code?: ISlugSorting;
  emailAddress?: ISortOrder;
  emailTitle?: ILocaleStringSorting;
  isActive?: ISortOrder;
}

export interface IEmployeeFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  employeeDescription?: ILocaleTextFilter;
  employeeName?: ILocaleStringFilter;
  employeePhoto?: ILocaleImageFilter;
  employeePosition?: ILocaleStringFilter;
  isActive?: IBooleanFilter;
}

export interface IEmployeeSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  employeeDescription?: ILocaleTextSorting;
  employeeName?: ILocaleStringSorting;
  employeePhoto?: ILocaleImageSorting;
  employeePosition?: ILocaleStringSorting;
  isActive?: ISortOrder;
}

export interface IEngagementSlideFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  header1?: ILocaleStringFilter;
  header2?: ILocaleStringFilter;
  image?: ILocaleImageFilter;
  imageType?: IStringFilter;
  isActive?: IBooleanFilter;
  text1?: ILocaleTextFilter;
  text2?: ILocaleTextFilter;
}

export interface IEngagementSlideSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  header1?: ILocaleStringSorting;
  header2?: ILocaleStringSorting;
  image?: ILocaleImageSorting;
  imageType?: ISortOrder;
  isActive?: ISortOrder;
  text1?: ILocaleTextSorting;
  text2?: ILocaleTextSorting;
}

export interface IFileFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  asset?: ISanityFileAssetFilter;
}

export interface IFileSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
}

export interface IFloatFilter {
  
  /**
   * Checks if the value is equal to the given input.
   */
  eq?: number;
  
  /**
   * Checks if the value is greater than the given input.
   */
  gt?: number;
  
  /**
   * Checks if the value is greater than or equal to the given input.
   */
  gte?: number;
  
  /**
   * Checks if the value is lesser than the given input.
   */
  lt?: number;
  
  /**
   * Checks if the value is lesser than or equal to the given input.
   */
  lte?: number;
  
  /**
   * Checks if the value is not equal to the given input.
   */
  neq?: number;
}

export interface IGameAPIFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  playerApiUrl?: IStringFilter;
  siteApiUrl?: IStringFilter;
  siteToken?: IStringFilter;
}

export interface IGameAPISorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  playerApiUrl?: ISortOrder;
  siteApiUrl?: ISortOrder;
  siteToken?: ISortOrder;
}

export interface IGameCategoryFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  categoryCode?: ISlugFilter;
  categoryName?: ILocaleStringFilter;
  categoryRegion?: IStringFilter;
  categoryType?: IStringFilter;
  isActive?: IBooleanFilter;
  showGamesFrom?: IStringFilter;
}

export interface IGameCategorySorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  categoryCode?: ISlugSorting;
  categoryName?: ILocaleStringSorting;
  categoryRegion?: ISortOrder;
  categoryType?: ISortOrder;
  isActive?: ISortOrder;
  showGamesFrom?: ISortOrder;
}

export interface IGameCertificateFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  gameCertificateCode?: ISlugFilter;
  gameCertificateName?: ILocaleStringFilter;
}

export interface IGameCertificateSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  gameCertificateCode?: ISlugSorting;
  gameCertificateName?: ILocaleStringSorting;
}

export interface IGameCodeFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  gameCode?: IStringFilter;
  gameInfo?: IStringFilter;
  gameProvider?: IProviderFilter;
  marketingMaterials?: IStringFilter;
}

export interface IGameCodeSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  gameCode?: ISortOrder;
  gameInfo?: ISortOrder;
  marketingMaterials?: ISortOrder;
}

export interface IGameFeaturesFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  gameFeatureDescription?: ILocaleTextFilter;
  gameFeaturePoster?: ILocaleImageFilter;
  gameFeatureTitle?: ILocaleStringFilter;
}

export interface IGameFeaturesSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  gameFeatureDescription?: ILocaleTextSorting;
  gameFeaturePoster?: ILocaleImageSorting;
  gameFeatureTitle?: ILocaleStringSorting;
}

export interface IGameFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  gameBillboard?: ILocaleImageFilter;
  gameBrandedPoster?: ILocaleImageFilter;
  gameDescription?: ILocaleTextFilter;
  gameHDPoster?: ILocaleImageFilter;
  gameIcon?: ILocaleImageFilter;
  gameId?: ISlugFilter;
  gameImage?: ILocaleImageFilter;
  gameInfo?: IGameInfoFilter;
  gameLogo?: ILocaleImageFilter;
  gameManage?: IGameManageFilter;
  gameName?: ILocaleStringFilter;
  gameOrientation?: IStringFilter;
  gamePoster?: ILocaleImageFilter;
  gameRibbon?: IGameRibbonFilter;
  gameTop4Poster?: ILocaleImageFilter;
  gameTowerPoster?: ILocaleImageFilter;
  gameVideo?: IVideoFilter;
  header?: IStringFilter;
  isActive?: IBooleanFilter;
  paUrl?: IStringFilter;
  releaseNote?: IFileFilter;
  saleSheet?: IFileFilter;
  showGameCode?: IStringFilter;
  showGameRTP?: IStringFilter;
  siteDescription?: ILocaleTextFilter;
}

export interface IGameInfoFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  formatOfReleaseDate?: IStringFilter;
  isFreeBets?: IBooleanFilter;
  isLuckyEnvelopes?: IBooleanFilter;
  isMustWinJackpot?: IBooleanFilter;
  isTournaments?: IBooleanFilter;
  layout?: IStringFilter;
  rtp?: IFloatFilter;
  volatility?: IStringFilter;
  ways?: IStringFilter;
}

export interface IGameInfoSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  formatOfReleaseDate?: ISortOrder;
  isFreeBets?: ISortOrder;
  isLuckyEnvelopes?: ISortOrder;
  isMustWinJackpot?: ISortOrder;
  isTournaments?: ISortOrder;
  layout?: ISortOrder;
  rtp?: ISortOrder;
  volatility?: ISortOrder;
  ways?: ISortOrder;
}

export interface IGameLineFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  game?: IGameFilter;
  gameRibbon?: IGameRibbonFilter;
  layout?: IStringFilter;
}

export interface IGameLineSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  layout?: ISortOrder;
}

export interface IGameManageFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  releaseDate?: IDateFilter;
  releaseText?: ILocaleStringFilter;
  status?: IStringFilter;
}

export interface IGameManageSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  releaseDate?: ISortOrder;
  releaseText?: ILocaleStringSorting;
  status?: ISortOrder;
}

export interface IGameMarketCertificatesFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  certificate?: IFileFilter;
  certificate_1?: IFileFilter;
  certificate_2?: IFileFilter;
  comingSoon?: IBooleanFilter;
  gameMarket?: IMarketFilter;
}

export interface IGameMarketCertificatesSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  certificate?: IFileSorting;
  certificate_1?: IFileSorting;
  certificate_2?: IFileSorting;
  comingSoon?: ISortOrder;
}

export interface IGamePlaylistFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  isActive?: IBooleanFilter;
  playlistBanner?: ILocaleImageFilter;
  playlistCode?: ISlugFilter;
  playlistDescription?: ILocaleTextFilter;
  playlistName?: ILocaleStringFilter;
  playlistPoster?: ILocaleImageFilter;
  playlistRegion?: IStringFilter;
}

export interface IGamePlaylistSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  isActive?: ISortOrder;
  playlistBanner?: ILocaleImageSorting;
  playlistCode?: ISlugSorting;
  playlistDescription?: ILocaleTextSorting;
  playlistName?: ILocaleStringSorting;
  playlistPoster?: ILocaleImageSorting;
  playlistRegion?: ISortOrder;
}

export interface IGameRTPFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  gameRTP?: IStringFilter;
}

export interface IGameRTPSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  gameRTP?: ISortOrder;
}

export interface IGameReleaseDateFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  gameMarket?: IMarketFilter;
  releaseDate?: IDateFilter;
}

export interface IGameReleaseDateSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  releaseDate?: ISortOrder;
}

export interface IGameRibbonFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  gameRibbonCode?: ISlugFilter;
  gameRibbonImage?: ILocaleImageFilter;
  gameRibbonName?: ILocaleStringFilter;
}

export interface IGameRibbonSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  gameRibbonCode?: ISlugSorting;
  gameRibbonImage?: ILocaleImageSorting;
  gameRibbonName?: ILocaleStringSorting;
}

export interface IGameSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  gameBillboard?: ILocaleImageSorting;
  gameBrandedPoster?: ILocaleImageSorting;
  gameDescription?: ILocaleTextSorting;
  gameHDPoster?: ILocaleImageSorting;
  gameIcon?: ILocaleImageSorting;
  gameId?: ISlugSorting;
  gameImage?: ILocaleImageSorting;
  gameInfo?: IGameInfoSorting;
  gameLogo?: ILocaleImageSorting;
  gameManage?: IGameManageSorting;
  gameName?: ILocaleStringSorting;
  gameOrientation?: ISortOrder;
  gamePoster?: ILocaleImageSorting;
  gameTop4Poster?: ILocaleImageSorting;
  gameTowerPoster?: ILocaleImageSorting;
  gameVideo?: IVideoSorting;
  header?: ISortOrder;
  isActive?: ISortOrder;
  paUrl?: ISortOrder;
  releaseNote?: IFileSorting;
  saleSheet?: IFileSorting;
  showGameCode?: ISortOrder;
  showGameRTP?: ISortOrder;
  siteDescription?: ILocaleTextSorting;
}

export interface IGameTypeFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  gameTypeCode?: ISlugFilter;
  gameTypeTitle?: ILocaleStringFilter;
}

export interface IGameTypeSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  gameTypeCode?: ISlugSorting;
  gameTypeTitle?: ILocaleStringSorting;
}

export interface IGeopointFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  alt?: IFloatFilter;
  lat?: IFloatFilter;
  lng?: IFloatFilter;
}

export interface IGeopointSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  alt?: ISortOrder;
  lat?: ISortOrder;
  lng?: ISortOrder;
}

export interface IHeaderTextFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  headerTextArea?: ILocaleTextFilter;
  headerTitle?: IHeaderTitleFilter;
}

export interface IHeaderTextSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  headerTextArea?: ILocaleTextSorting;
  headerTitle?: IHeaderTitleSorting;
}

export interface IHeaderTickerFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  isActive?: IBooleanFilter;
  tickerDate?: IDateFilter;
}

export interface IHeaderTickerSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  isActive?: ISortOrder;
  tickerDate?: ISortOrder;
}

export interface IHeaderTitleFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  title1?: ILocaleStringFilter;
  title2?: ILocaleStringFilter;
  title3?: ILocaleStringFilter;
}

export interface IHeaderTitleSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  title1?: ILocaleStringSorting;
  title2?: ILocaleStringSorting;
  title3?: ILocaleStringSorting;
}

export interface IHslaColorFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  a?: IFloatFilter;
  h?: IFloatFilter;
  l?: IFloatFilter;
  s?: IFloatFilter;
}

export interface IHslaColorSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  a?: ISortOrder;
  h?: ISortOrder;
  l?: ISortOrder;
  s?: ISortOrder;
}

export interface IHsvaColorFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  a?: IFloatFilter;
  h?: IFloatFilter;
  s?: IFloatFilter;
  v?: IFloatFilter;
}

export interface IHsvaColorSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  a?: ISortOrder;
  h?: ISortOrder;
  s?: ISortOrder;
  v?: ISortOrder;
}

export interface IIDFilter {
  
  /**
   * Checks if the value is equal to the given input.
   */
  eq?: string;
  in?: Array<string>;
  
  /**
   * Checks if the value matches the given word/words.
   */
  matches?: string;
  
  /**
   * Checks if the value is not equal to the given input.
   */
  neq?: string;
  nin?: Array<string>;
}

export interface IImageFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  asset?: ISanityImageAssetFilter;
  crop?: ISanityImageCropFilter;
  hotspot?: ISanityImageHotspotFilter;
}

export interface IImageSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  crop?: ISanityImageCropSorting;
  hotspot?: ISanityImageHotspotSorting;
}

export interface IIntFilter {
  
  /**
   * Checks if the value is equal to the given input.
   */
  eq?: number;
  
  /**
   * Checks if the value is greater than the given input.
   */
  gt?: number;
  
  /**
   * Checks if the value is greater than or equal to the given input.
   */
  gte?: number;
  
  /**
   * Checks if the value is lesser than the given input.
   */
  lt?: number;
  
  /**
   * Checks if the value is lesser than or equal to the given input.
   */
  lte?: number;
  
  /**
   * Checks if the value is not equal to the given input.
   */
  neq?: number;
}

export interface IJackpotInfoFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  code?: ISlugFilter;
  description?: ILocaleTextFilter;
  header?: ILocaleStringFilter;
  icon?: ILocaleImageFilter;
  iconHover?: ILocaleImageFilter;
  isActive?: IBooleanFilter;
}

export interface IJackpotInfoSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  code?: ISlugSorting;
  description?: ILocaleTextSorting;
  header?: ILocaleStringSorting;
  icon?: ILocaleImageSorting;
  iconHover?: ILocaleImageSorting;
  isActive?: ISortOrder;
}

export interface ILanguageFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  languageCode?: IStringFilter;
  languageIcon?: ILocaleImageFilter;
  languageName?: IStringFilter;
  languageNativeName?: IStringFilter;
}

export interface ILanguageSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  languageCode?: ISortOrder;
  languageIcon?: ILocaleImageSorting;
  languageName?: ISortOrder;
  languageNativeName?: ISortOrder;
}

export interface ILicenseFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  isActive?: IBooleanFilter;
  jurisdiction?: ILocaleStringFilter;
  licenseDescription?: ILocaleTextFilter;
  licenseNo?: IStringFilter;
  licensePhoto?: ILocaleImageFilter;
  licenseUrl?: IStringFilter;
  regulator?: ILocaleStringFilter;
}

export interface ILicenseSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  isActive?: ISortOrder;
  jurisdiction?: ILocaleStringSorting;
  licenseDescription?: ILocaleTextSorting;
  licenseNo?: ISortOrder;
  licensePhoto?: ILocaleImageSorting;
  licenseUrl?: ISortOrder;
  regulator?: ILocaleStringSorting;
}

export interface ILinkFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  code?: ISlugFilter;
  isActive?: IBooleanFilter;
  isExternal?: IBooleanFilter;
  title?: ILocaleStringFilter;
}

export interface ILinkSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  code?: ISlugSorting;
  isActive?: ISortOrder;
  isExternal?: ISortOrder;
  title?: ILocaleStringSorting;
}

export interface ILocaleBlockFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
}

export interface ILocaleBlockSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
}

export interface ILocaleImageFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  en?: IImageFilter;
  zhCn?: IImageFilter;
}

export interface ILocaleImageSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  en?: IImageSorting;
  zhCn?: IImageSorting;
}

export interface ILocaleStringFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  en?: IStringFilter;
  zhCn?: IStringFilter;
}

export interface ILocaleStringSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  en?: ISortOrder;
  zhCn?: ISortOrder;
}

export interface ILocaleTextFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  en?: IStringFilter;
  zhCn?: IStringFilter;
}

export interface ILocaleTextSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  en?: ISortOrder;
  zhCn?: ISortOrder;
}

export interface ILocaleUrlFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  en?: IStringFilter;
  zhCn?: IStringFilter;
}

export interface ILocaleUrlSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  en?: ISortOrder;
  zhCn?: ISortOrder;
}

export interface ILocalizationFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  textCode?: ISlugFilter;
}

export interface ILocalizationSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  textCode?: ISlugSorting;
}

export interface IMaintenanceFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  isActive?: IBooleanFilter;
  landscapeImage?: ILocaleImageFilter;
  portraitImage?: ILocaleImageFilter;
  textMessage?: ILocaleStringFilter;
}

export interface IMaintenanceSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  isActive?: ISortOrder;
  landscapeImage?: ILocaleImageSorting;
  portraitImage?: ILocaleImageSorting;
  textMessage?: ILocaleStringSorting;
}

export interface IMarketFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  code?: ISlugFilter;
  isActive?: IBooleanFilter;
  marketIcon?: ILocaleImageFilter;
  marketName?: ILocaleStringFilter;
}

export interface IMarketSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  code?: ISlugSorting;
  isActive?: ISortOrder;
  marketIcon?: ILocaleImageSorting;
  marketName?: ILocaleStringSorting;
}

export interface IMediaServicesBlockFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  blockCode?: ISlugFilter;
  header?: ILocaleStringFilter;
  icon?: ILocaleImageFilter;
  iconHover?: ILocaleImageFilter;
  text?: ILocaleTextFilter;
}

export interface IMediaServicesBlockSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  blockCode?: ISlugSorting;
  header?: ILocaleStringSorting;
  icon?: ILocaleImageSorting;
  iconHover?: ILocaleImageSorting;
  text?: ILocaleTextSorting;
}

export interface IMediaServicesTechFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  blockCode?: ISlugFilter;
  cardType?: IStringFilter;
  header?: ILocaleStringFilter;
  icon?: ILocaleImageFilter;
  iconHover?: ILocaleImageFilter;
  text?: ILocaleTextFilter;
}

export interface IMediaServicesTechSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  blockCode?: ISlugSorting;
  cardType?: ISortOrder;
  header?: ILocaleStringSorting;
  icon?: ILocaleImageSorting;
  iconHover?: ILocaleImageSorting;
  text?: ILocaleTextSorting;
}

export interface IMediaTagFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  name?: ISlugFilter;
}

export interface IMediaTagSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  name?: ISlugSorting;
}

export interface INewsCategoryFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  newsCategoryCode?: ISlugFilter;
  newsCategoryTitle?: ILocaleStringFilter;
}

export interface INewsCategorySorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  newsCategoryCode?: ISlugSorting;
  newsCategoryTitle?: ILocaleStringSorting;
}

export interface INewsFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  block?: ILocaleBlockFilter;
  game?: IGameFilter;
  headerPromoVideo?: IVideoFilter;
  highlightedTitle?: ILocaleStringFilter;
  isActive?: IBooleanFilter;
  newsDate?: IDateFilter;
  newsPreviewImage?: ILocaleImageFilter;
  newsType?: IStringFilter;
  region?: IStringFilter;
  text?: ILocaleTextFilter;
  textType?: IStringFilter;
  title?: ILocaleStringFilter;
}

export interface INewsSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  block?: ILocaleBlockSorting;
  headerPromoVideo?: IVideoSorting;
  highlightedTitle?: ILocaleStringSorting;
  isActive?: ISortOrder;
  newsDate?: ISortOrder;
  newsPreviewImage?: ILocaleImageSorting;
  newsType?: ISortOrder;
  region?: ISortOrder;
  text?: ILocaleTextSorting;
  textType?: ISortOrder;
  title?: ILocaleStringSorting;
}

export interface IOfficeFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  address?: ILocaleTextFilter;
  code?: ISlugFilter;
  companyName?: ILocaleStringFilter;
  country?: ILocaleStringFilter;
  email?: IStringFilter;
  isActive?: IBooleanFilter;
  leftOnMap?: IFloatFilter;
  officeName?: ILocaleStringFilter;
  officePhoto?: ILocaleImageFilter;
  phone1?: IStringFilter;
  phone2?: IStringFilter;
  topOnMap?: IFloatFilter;
}

export interface IOfficeSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  address?: ILocaleTextSorting;
  code?: ISlugSorting;
  companyName?: ILocaleStringSorting;
  country?: ILocaleStringSorting;
  email?: ISortOrder;
  isActive?: ISortOrder;
  leftOnMap?: ISortOrder;
  officeName?: ILocaleStringSorting;
  officePhoto?: ILocaleImageSorting;
  phone1?: ISortOrder;
  phone2?: ISortOrder;
  topOnMap?: ISortOrder;
}

export interface IPageAboutUsFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  canDeploy?: IBooleanFilter;
  headerPromoVideo?: IVideoFilter;
  headerText?: IHeaderTextFilter;
  isActive?: IBooleanFilter;
  pageTitle?: ILocaleStringFilter;
}

export interface IPageAboutUsSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  canDeploy?: ISortOrder;
  headerPromoVideo?: IVideoSorting;
  headerText?: IHeaderTextSorting;
  isActive?: ISortOrder;
  pageTitle?: ILocaleStringSorting;
}

export interface IPageGameSearchFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  banners?: IBannersCarouselFilter;
  pageTitle?: ILocaleStringFilter;
}

export interface IPageGameSearchSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  banners?: IBannersCarouselSorting;
  pageTitle?: ILocaleStringSorting;
}

export interface IPageGamesFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  canDeploy?: IBooleanFilter;
  headerPromoVideo?: IVideoFilter;
  headerText?: IHeaderTextFilter;
  isActive?: IBooleanFilter;
  pageTitle?: ILocaleStringFilter;
}

export interface IPageGamesSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  canDeploy?: ISortOrder;
  headerPromoVideo?: IVideoSorting;
  headerText?: IHeaderTextSorting;
  isActive?: ISortOrder;
  pageTitle?: ILocaleStringSorting;
}

export interface IPageGamingSolutionsFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  canDeploy?: IBooleanFilter;
  headerText?: IHeaderTextFilter;
  isActive?: IBooleanFilter;
  pageTitle?: ILocaleStringFilter;
}

export interface IPageGamingSolutionsSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  canDeploy?: ISortOrder;
  headerText?: IHeaderTextSorting;
  isActive?: ISortOrder;
  pageTitle?: ILocaleStringSorting;
}

export interface IPageHomeFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  canDeploy?: IBooleanFilter;
  headerPromoVideo?: IVideoFilter;
  headerText?: IHeaderTextFilter;
  isActive?: IBooleanFilter;
  pageTitle?: ILocaleStringFilter;
  topGame1?: IGameLineFilter;
  topGame2?: IGameLineFilter;
  topGame3?: IGameLineFilter;
  topGame4?: IGameLineFilter;
}

export interface IPageHomeSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  canDeploy?: ISortOrder;
  headerPromoVideo?: IVideoSorting;
  headerText?: IHeaderTextSorting;
  isActive?: ISortOrder;
  pageTitle?: ILocaleStringSorting;
  topGame1?: IGameLineSorting;
  topGame2?: IGameLineSorting;
  topGame3?: IGameLineSorting;
  topGame4?: IGameLineSorting;
}

export interface IPageLiveCasinoFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  canDeploy?: IBooleanFilter;
  headerPromoVideo?: IVideoFilter;
  headerText?: IHeaderTextFilter;
  isActive?: IBooleanFilter;
  pageTitle?: ILocaleStringFilter;
}

export interface IPageLiveCasinoSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  canDeploy?: ISortOrder;
  headerPromoVideo?: IVideoSorting;
  headerText?: IHeaderTextSorting;
  isActive?: ISortOrder;
  pageTitle?: ILocaleStringSorting;
}

export interface IPageMediaServicesFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  canDeploy?: IBooleanFilter;
  headerText?: IHeaderTextFilter;
  isActive?: IBooleanFilter;
  pageTitle?: ILocaleStringFilter;
}

export interface IPageMediaServicesSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  canDeploy?: ISortOrder;
  headerText?: IHeaderTextSorting;
  isActive?: ISortOrder;
  pageTitle?: ILocaleStringSorting;
}

export interface IPageNewsFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  banners?: IBannersCarouselFilter;
  canDeploy?: IBooleanFilter;
  headerPromoVideo?: IVideoFilter;
  headerText?: IHeaderTextFilter;
  isActive?: IBooleanFilter;
  pageTitle?: ILocaleStringFilter;
}

export interface IPageNewsSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  banners?: IBannersCarouselSorting;
  canDeploy?: ISortOrder;
  headerPromoVideo?: IVideoSorting;
  headerText?: IHeaderTextSorting;
  isActive?: ISortOrder;
  pageTitle?: ILocaleStringSorting;
}

export interface IPageRoadmapFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  banners?: IBannersCarouselFilter;
  defaultMarket?: IMarketFilter;
  latestReleasesMonth?: IFloatFilter;
  pageTitle?: ILocaleStringFilter;
  showPerMonthToolbar?: IBooleanFilter;
  upcomingMonthThirdParty?: IFloatFilter;
}

export interface IPageRoadmapSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  banners?: IBannersCarouselSorting;
  latestReleasesMonth?: ISortOrder;
  pageTitle?: ILocaleStringSorting;
  showPerMonthToolbar?: ISortOrder;
  upcomingMonthThirdParty?: ISortOrder;
}

export interface IPageToolsBonusCoinFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  canDeploy?: IBooleanFilter;
  headerPromoVideo?: IVideoFilter;
  headerText?: IHeaderTextFilter;
  isActive?: IBooleanFilter;
  pageTitle?: ILocaleStringFilter;
}

export interface IPageToolsBonusCoinSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  canDeploy?: ISortOrder;
  headerPromoVideo?: IVideoSorting;
  headerText?: IHeaderTextSorting;
  isActive?: ISortOrder;
  pageTitle?: ILocaleStringSorting;
}

export interface IPageToolsGRCFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  canDeploy?: IBooleanFilter;
  headerText?: IHeaderTextFilter;
  isActive?: IBooleanFilter;
  pageTitle?: ILocaleStringFilter;
}

export interface IPageToolsGRCSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  canDeploy?: ISortOrder;
  headerText?: IHeaderTextSorting;
  isActive?: ISortOrder;
  pageTitle?: ILocaleStringSorting;
}

export interface IPageToolsJackpotFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  canDeploy?: IBooleanFilter;
  headerText?: IHeaderTextFilter;
  isActive?: IBooleanFilter;
  pageTitle?: ILocaleStringFilter;
}

export interface IPageToolsJackpotSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  canDeploy?: ISortOrder;
  headerText?: IHeaderTextSorting;
  isActive?: ISortOrder;
  pageTitle?: ILocaleStringSorting;
}

export interface IPageToolsTournamentFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  canDeploy?: IBooleanFilter;
  headerText?: IHeaderTextFilter;
  isActive?: IBooleanFilter;
  pageTitle?: ILocaleStringFilter;
}

export interface IPageToolsTournamentSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  canDeploy?: ISortOrder;
  headerText?: IHeaderTextSorting;
  isActive?: ISortOrder;
  pageTitle?: ILocaleStringSorting;
}

export interface IPageTournamentsFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  maintenance?: IMaintenanceFilter;
  pageTitle?: ILocaleStringFilter;
}

export interface IPageTournamentsSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  maintenance?: IMaintenanceSorting;
  pageTitle?: ILocaleStringSorting;
}

export interface IPartnerFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  isActive?: IBooleanFilter;
  partnerCode?: ISlugFilter;
  partnerImage?: ILocaleImageFilter;
  partnerName?: ILocaleStringFilter;
  partnerUrl?: IStringFilter;
}

export interface IPartnerSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  isActive?: ISortOrder;
  partnerCode?: ISlugSorting;
  partnerImage?: ILocaleImageSorting;
  partnerName?: ILocaleStringSorting;
  partnerUrl?: ISortOrder;
}

export interface IPhoneFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  code?: ISlugFilter;
  isActive?: IBooleanFilter;
  phoneNumber?: IStringFilter;
  phoneTitle?: ILocaleStringFilter;
}

export interface IPhoneSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  code?: ISlugSorting;
  isActive?: ISortOrder;
  phoneNumber?: ISortOrder;
  phoneTitle?: ILocaleStringSorting;
}

export interface IProviderFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  providerCode?: IStringFilter;
  providerName?: IStringFilter;
}

export interface IProviderSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  providerCode?: ISortOrder;
  providerName?: ISortOrder;
}

export interface IRegionFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  regionCode?: IStringFilter;
}

export interface IRegionSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  regionCode?: ISortOrder;
}

export interface IRgbaColorFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  a?: IFloatFilter;
  b?: IFloatFilter;
  g?: IFloatFilter;
  r?: IFloatFilter;
}

export interface IRgbaColorSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  a?: ISortOrder;
  b?: ISortOrder;
  g?: ISortOrder;
  r?: ISortOrder;
}

export interface IRoadmapGameFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  comingSoon?: IBooleanFilter;
  game?: IGameFilter;
  hideInAllMarkets?: IBooleanFilter;
}

export interface IRoadmapGameMarketFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  endDate?: IDateFilter;
  gameMarket?: IMarketFilter;
  startDate?: IDateFilter;
}

export interface IRoadmapGameMarketSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  endDate?: ISortOrder;
  startDate?: ISortOrder;
}

export interface IRoadmapGameSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  comingSoon?: ISortOrder;
  hideInAllMarkets?: ISortOrder;
}

export interface ISanityAssetSourceDataFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  id?: IStringFilter;
  name?: IStringFilter;
  url?: IStringFilter;
}

export interface ISanityAssetSourceDataSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  id?: ISortOrder;
  name?: ISortOrder;
  url?: ISortOrder;
}

export interface ISanityFileAssetFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  altText?: IStringFilter;
  assetId?: IStringFilter;
  description?: IStringFilter;
  extension?: IStringFilter;
  label?: IStringFilter;
  mimeType?: IStringFilter;
  originalFilename?: IStringFilter;
  path?: IStringFilter;
  sha1hash?: IStringFilter;
  size?: IFloatFilter;
  source?: ISanityAssetSourceDataFilter;
  title?: IStringFilter;
  url?: IStringFilter;
}

export interface ISanityFileAssetSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  altText?: ISortOrder;
  assetId?: ISortOrder;
  description?: ISortOrder;
  extension?: ISortOrder;
  label?: ISortOrder;
  mimeType?: ISortOrder;
  originalFilename?: ISortOrder;
  path?: ISortOrder;
  sha1hash?: ISortOrder;
  size?: ISortOrder;
  source?: ISanityAssetSourceDataSorting;
  title?: ISortOrder;
  url?: ISortOrder;
}

export interface ISanityImageAssetFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  altText?: IStringFilter;
  assetId?: IStringFilter;
  description?: IStringFilter;
  extension?: IStringFilter;
  label?: IStringFilter;
  metadata?: ISanityImageMetadataFilter;
  mimeType?: IStringFilter;
  originalFilename?: IStringFilter;
  path?: IStringFilter;
  sha1hash?: IStringFilter;
  size?: IFloatFilter;
  source?: ISanityAssetSourceDataFilter;
  title?: IStringFilter;
  uploadId?: IStringFilter;
  url?: IStringFilter;
}

export interface ISanityImageAssetSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  altText?: ISortOrder;
  assetId?: ISortOrder;
  description?: ISortOrder;
  extension?: ISortOrder;
  label?: ISortOrder;
  metadata?: ISanityImageMetadataSorting;
  mimeType?: ISortOrder;
  originalFilename?: ISortOrder;
  path?: ISortOrder;
  sha1hash?: ISortOrder;
  size?: ISortOrder;
  source?: ISanityAssetSourceDataSorting;
  title?: ISortOrder;
  uploadId?: ISortOrder;
  url?: ISortOrder;
}

export interface ISanityImageCropFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  bottom?: IFloatFilter;
  left?: IFloatFilter;
  right?: IFloatFilter;
  top?: IFloatFilter;
}

export interface ISanityImageCropSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  bottom?: ISortOrder;
  left?: ISortOrder;
  right?: ISortOrder;
  top?: ISortOrder;
}

export interface ISanityImageDimensionsFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  aspectRatio?: IFloatFilter;
  height?: IFloatFilter;
  width?: IFloatFilter;
}

export interface ISanityImageDimensionsSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  aspectRatio?: ISortOrder;
  height?: ISortOrder;
  width?: ISortOrder;
}

export interface ISanityImageHotspotFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  height?: IFloatFilter;
  width?: IFloatFilter;
  x?: IFloatFilter;
  y?: IFloatFilter;
}

export interface ISanityImageHotspotSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  height?: ISortOrder;
  width?: ISortOrder;
  x?: ISortOrder;
  y?: ISortOrder;
}

export interface ISanityImageMetadataFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  blurHash?: IStringFilter;
  dimensions?: ISanityImageDimensionsFilter;
  hasAlpha?: IBooleanFilter;
  isOpaque?: IBooleanFilter;
  location?: IGeopointFilter;
  lqip?: IStringFilter;
  palette?: ISanityImagePaletteFilter;
}

export interface ISanityImageMetadataSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  blurHash?: ISortOrder;
  dimensions?: ISanityImageDimensionsSorting;
  hasAlpha?: ISortOrder;
  isOpaque?: ISortOrder;
  location?: IGeopointSorting;
  lqip?: ISortOrder;
  palette?: ISanityImagePaletteSorting;
}

export interface ISanityImagePaletteFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  darkMuted?: ISanityImagePaletteSwatchFilter;
  darkVibrant?: ISanityImagePaletteSwatchFilter;
  dominant?: ISanityImagePaletteSwatchFilter;
  lightMuted?: ISanityImagePaletteSwatchFilter;
  lightVibrant?: ISanityImagePaletteSwatchFilter;
  muted?: ISanityImagePaletteSwatchFilter;
  vibrant?: ISanityImagePaletteSwatchFilter;
}

export interface ISanityImagePaletteSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  darkMuted?: ISanityImagePaletteSwatchSorting;
  darkVibrant?: ISanityImagePaletteSwatchSorting;
  dominant?: ISanityImagePaletteSwatchSorting;
  lightMuted?: ISanityImagePaletteSwatchSorting;
  lightVibrant?: ISanityImagePaletteSwatchSorting;
  muted?: ISanityImagePaletteSwatchSorting;
  vibrant?: ISanityImagePaletteSwatchSorting;
}

export interface ISanityImagePaletteSwatchFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  background?: IStringFilter;
  foreground?: IStringFilter;
  population?: IFloatFilter;
  title?: IStringFilter;
}

export interface ISanityImagePaletteSwatchSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  background?: ISortOrder;
  foreground?: ISortOrder;
  population?: ISortOrder;
  title?: ISortOrder;
}

export interface ISanity_DocumentFilter {
  
  /**
   * All documents that are drafts.
   */
  is_draft?: boolean;
  
  /**
   * All documents referencing the given document ID.
   */
  references?: string;
}

export interface ISectionGroupSliderGamesFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  groupCode?: ISlugFilter;
  groupName?: ILocaleStringFilter;
  isActive?: IBooleanFilter;
  region?: IStringFilter;
}

export interface ISectionGroupSliderGamesSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  groupCode?: ISlugSorting;
  groupName?: ILocaleStringSorting;
  isActive?: ISortOrder;
  region?: ISortOrder;
}

export interface ISectionSliderGamesFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  isActive?: IBooleanFilter;
  isShowGamesNumber?: IBooleanFilter;
  isVisibleSectionLogo?: IBooleanFilter;
  layout?: IStringFilter;
  region?: IStringFilter;
  sectionCode?: ISlugFilter;
  sectionLogo?: ILocaleImageFilter;
  sectionName?: ILocaleStringFilter;
  sortGames?: IStringFilter;
}

export interface ISectionSliderGamesSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  isActive?: ISortOrder;
  isShowGamesNumber?: ISortOrder;
  isVisibleSectionLogo?: ISortOrder;
  layout?: ISortOrder;
  region?: ISortOrder;
  sectionCode?: ISlugSorting;
  sectionLogo?: ILocaleImageSorting;
  sectionName?: ILocaleStringSorting;
  sortGames?: ISortOrder;
}

export interface ISingleLine1Filter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  text?: ILocaleStringFilter;
  textCode?: ISlugFilter;
}

export interface ISingleLine1Sorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  text?: ILocaleStringSorting;
  textCode?: ISlugSorting;
}

export interface ISingleLine2Filter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  text1?: ILocaleStringFilter;
  text2?: ILocaleStringFilter;
  textCode?: ISlugFilter;
}

export interface ISingleLine2Sorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  text1?: ILocaleStringSorting;
  text2?: ILocaleStringSorting;
  textCode?: ISlugSorting;
}

export interface ISingleLine3Filter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  text1?: ILocaleStringFilter;
  text2?: ILocaleStringFilter;
  text3?: ILocaleStringFilter;
  textCode?: ISlugFilter;
}

export interface ISingleLine3Sorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  text1?: ILocaleStringSorting;
  text2?: ILocaleStringSorting;
  text3?: ILocaleStringSorting;
  textCode?: ISlugSorting;
}

export interface ISiteConfigFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  homePage?: ISitePageFilter;
  metaDescription?: IStringFilter;
  metaKeywords?: IStringFilter;
  metaRobots?: IStringFilter;
  region?: IStringFilter;
  title?: ILocaleStringFilter;
}

export interface ISiteConfigSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  metaDescription?: ISortOrder;
  metaKeywords?: ISortOrder;
  metaRobots?: ISortOrder;
  region?: ISortOrder;
  title?: ILocaleStringSorting;
}

export interface ISitePageFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  code?: ISlugFilter;
  isActive?: IBooleanFilter;
  name?: IStringFilter;
  region?: IStringFilter;
  title?: ILocaleStringFilter;
  version?: IStringFilter;
}

export interface ISitePageSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  code?: ISlugSorting;
  isActive?: ISortOrder;
  name?: ISortOrder;
  region?: ISortOrder;
  title?: ILocaleStringSorting;
  version?: ISortOrder;
}

export interface ISlugFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  current?: IStringFilter;
}

export interface ISlugSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  current?: ISortOrder;
}

export interface ISocialNetworkFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  code?: ISlugFilter;
  isActive?: IBooleanFilter;
  snIcon?: ILocaleImageFilter;
  snName?: ILocaleStringFilter;
  snUrl?: IStringFilter;
}

export interface ISocialNetworkSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  code?: ISlugSorting;
  isActive?: ISortOrder;
  snIcon?: ILocaleImageSorting;
  snName?: ILocaleStringSorting;
  snUrl?: ISortOrder;
}

export interface IStringFilter {
  
  /**
   * Checks if the value is equal to the given input.
   */
  eq?: string;
  in?: Array<string>;
  
  /**
   * Checks if the value matches the given word/words.
   */
  matches?: string;
  
  /**
   * Checks if the value is not equal to the given input.
   */
  neq?: string;
  nin?: Array<string>;
}

export interface IStudioFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  code?: ISlugFilter;
  studioName?: ILocaleStringFilter;
  studioPhoto?: ILocaleImageFilter;
}

export interface IStudioSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  code?: ISlugSorting;
  studioName?: ILocaleStringSorting;
  studioPhoto?: ILocaleImageSorting;
}

export interface ITextareaFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  text?: ILocaleTextFilter;
  textCode?: ISlugFilter;
}

export interface ITextareaSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  text?: ILocaleTextSorting;
  textCode?: ISlugSorting;
}

export interface ITournamentFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  code?: ISlugFilter;
  isActive?: IBooleanFilter;
  name?: ILocaleStringFilter;
}

export interface ITournamentSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  code?: ISlugSorting;
  isActive?: ISortOrder;
  name?: ILocaleStringSorting;
}

export interface IUrlTargetFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  url?: IStringFilter;
  urlTarget?: IStringFilter;
}

export interface IUrlTargetSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  url?: ISortOrder;
  urlTarget?: ISortOrder;
}

export interface IVideoFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  file?: IFileFilter;
  headerTicker?: IHeaderTickerFilter;
  height?: IFloatFilter;
  mobileVideoPoster?: ILocaleImageFilter;
  type?: IStringFilter;
  url?: IStringFilter;
  videoPoster?: ILocaleImageFilter;
  width?: IFloatFilter;
}

export interface IVideoSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  file?: IFileSorting;
  headerTicker?: IHeaderTickerSorting;
  height?: ISortOrder;
  mobileVideoPoster?: ILocaleImageSorting;
  type?: ISortOrder;
  url?: ISortOrder;
  videoPoster?: ILocaleImageSorting;
  width?: ISortOrder;
}

export interface IVideoUrlFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  file?: IFileFilter;
  languageCode?: IStringFilter;
  type?: IStringFilter;
  url?: IStringFilter;
}

export interface IVideoUrlSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  file?: IFileSorting;
  languageCode?: ISortOrder;
  type?: ISortOrder;
  url?: ISortOrder;
}

export interface IWebsiteConfigFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  facebookUrl?: IStringFilter;
  gameAPI?: IGameAPIFilter;
  instagramUrl?: IStringFilter;
  linkedInUrl?: IStringFilter;
  metaDescription?: IStringFilter;
  metaKeywords?: IStringFilter;
  metaRobots?: IStringFilter;
  title?: IStringFilter;
  youtubeUrl?: IStringFilter;
}

export interface IWebsiteConfigSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  facebookUrl?: ISortOrder;
  gameAPI?: IGameAPISorting;
  instagramUrl?: ISortOrder;
  linkedInUrl?: ISortOrder;
  metaDescription?: ISortOrder;
  metaKeywords?: ISortOrder;
  metaRobots?: ISortOrder;
  title?: ISortOrder;
  youtubeUrl?: ISortOrder;
}

export interface IWidgetPartnersFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  code?: ISlugFilter;
  isActive?: IBooleanFilter;
  name?: IStringFilter;
  title?: ILocaleStringFilter;
}

export interface IWidgetPartnersSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  code?: ISlugSorting;
  isActive?: ISortOrder;
  name?: ISortOrder;
  title?: ILocaleStringSorting;
}

export interface IWidgetSwiperFilter {
  
  /**
   * Apply filters on document level
   */
  _?: ISanity_DocumentFilter;
  _createdAt?: IDatetimeFilter;
  _id?: IIDFilter;
  _key?: IStringFilter;
  _rev?: IStringFilter;
  _type?: IStringFilter;
  _updatedAt?: IDatetimeFilter;
  code?: ISlugFilter;
  isActive?: IBooleanFilter;
  name?: IStringFilter;
  swiperParameters?: IWidgetSwiperParametersFilter;
}

export interface IWidgetSwiperParametersFilter {
  _key?: IStringFilter;
  _type?: IStringFilter;
  allowSlideNext?: IBooleanFilter;
  allowSlidePrev?: IBooleanFilter;
  allowTouchMove?: IBooleanFilter;
  autoHeight?: IBooleanFilter;
  centeredSlides?: IBooleanFilter;
  direction?: IStringFilter;
}

export interface IWidgetSwiperParametersSorting {
  _key?: ISortOrder;
  _type?: ISortOrder;
  allowSlideNext?: ISortOrder;
  allowSlidePrev?: ISortOrder;
  allowTouchMove?: ISortOrder;
  autoHeight?: ISortOrder;
  centeredSlides?: ISortOrder;
  direction?: ISortOrder;
}

export interface IWidgetSwiperSorting {
  _createdAt?: ISortOrder;
  _id?: ISortOrder;
  _key?: ISortOrder;
  _rev?: ISortOrder;
  _type?: ISortOrder;
  _updatedAt?: ISortOrder;
  code?: ISlugSorting;
  isActive?: ISortOrder;
  name?: ISortOrder;
  swiperParameters?: IWidgetSwiperParametersSorting;
}

/*********************************
 *                               *
 *         TYPE RESOLVERS        *
 *                               *
 *********************************/
/**
 * This interface define the shape of your resolver
 * Note that this type is designed to be compatible with graphql-tools resolvers
 * However, you can still use other generated interfaces to make your resolver type-safed
 */
export interface IResolver {
  Document?: {
    __resolveType: IDocumentTypeResolver
  };
  
  BlockOrImage?: {
    __resolveType: IBlockOrImageTypeResolver
  };
  
  LinkOrSitePage?: {
    __resolveType: ILinkOrSitePageTypeResolver
  };
  
  SingleLine1OrSingleLine2OrSingleLine3OrTextarea?: {
    __resolveType: ISingleLine1OrSingleLine2OrSingleLine3OrTextareaTypeResolver
  };
  
  Banner?: IBannerTypeResolver;
  BannerAsset?: IBannerAssetTypeResolver;
  BannersCarousel?: IBannersCarouselTypeResolver;
  Block?: IBlockTypeResolver;
  Color?: IColorTypeResolver;
  Country?: ICountryTypeResolver;
  CountryGames?: ICountryGamesTypeResolver;
  Currency?: ICurrencyTypeResolver;
  EmailAddress?: IEmailAddressTypeResolver;
  Employee?: IEmployeeTypeResolver;
  EngagementSlide?: IEngagementSlideTypeResolver;
  File?: IFileTypeResolver;
  Game?: IGameTypeResolver;
  GameAPI?: IGameAPITypeResolver;
  GameCategory?: IGameCategoryTypeResolver;
  GameCertificate?: IGameCertificateTypeResolver;
  GameCode?: IGameCodeTypeResolver;
  GameFeatures?: IGameFeaturesTypeResolver;
  GameInfo?: IGameInfoTypeResolver;
  GameLine?: IGameLineTypeResolver;
  GameManage?: IGameManageTypeResolver;
  GameMarketCertificates?: IGameMarketCertificatesTypeResolver;
  GamePlaylist?: IGamePlaylistTypeResolver;
  GameRTP?: IGameRTPTypeResolver;
  GameReleaseDate?: IGameReleaseDateTypeResolver;
  GameRibbon?: IGameRibbonTypeResolver;
  GameType?: IGameTypeTypeResolver;
  Geopoint?: IGeopointTypeResolver;
  HeaderText?: IHeaderTextTypeResolver;
  HeaderTicker?: IHeaderTickerTypeResolver;
  HeaderTitle?: IHeaderTitleTypeResolver;
  HslaColor?: IHslaColorTypeResolver;
  HsvaColor?: IHsvaColorTypeResolver;
  Image?: IImageTypeResolver;
  JackpotInfo?: IJackpotInfoTypeResolver;
  Language?: ILanguageTypeResolver;
  License?: ILicenseTypeResolver;
  Link?: ILinkTypeResolver;
  LocaleBlock?: ILocaleBlockTypeResolver;
  LocaleImage?: ILocaleImageTypeResolver;
  LocaleString?: ILocaleStringTypeResolver;
  LocaleText?: ILocaleTextTypeResolver;
  LocaleUrl?: ILocaleUrlTypeResolver;
  Localization?: ILocalizationTypeResolver;
  Maintenance?: IMaintenanceTypeResolver;
  Market?: IMarketTypeResolver;
  MediaServicesBlock?: IMediaServicesBlockTypeResolver;
  MediaServicesTech?: IMediaServicesTechTypeResolver;
  MediaTag?: IMediaTagTypeResolver;
  News?: INewsTypeResolver;
  NewsCategory?: INewsCategoryTypeResolver;
  Office?: IOfficeTypeResolver;
  PageAboutUs?: IPageAboutUsTypeResolver;
  PageGameSearch?: IPageGameSearchTypeResolver;
  PageGames?: IPageGamesTypeResolver;
  PageGamingSolutions?: IPageGamingSolutionsTypeResolver;
  PageHome?: IPageHomeTypeResolver;
  PageLiveCasino?: IPageLiveCasinoTypeResolver;
  PageMediaServices?: IPageMediaServicesTypeResolver;
  PageNews?: IPageNewsTypeResolver;
  PageRoadmap?: IPageRoadmapTypeResolver;
  PageToolsBonusCoin?: IPageToolsBonusCoinTypeResolver;
  PageToolsGRC?: IPageToolsGRCTypeResolver;
  PageToolsJackpot?: IPageToolsJackpotTypeResolver;
  PageToolsTournament?: IPageToolsTournamentTypeResolver;
  PageTournaments?: IPageTournamentsTypeResolver;
  Partner?: IPartnerTypeResolver;
  Phone?: IPhoneTypeResolver;
  Provider?: IProviderTypeResolver;
  Region?: IRegionTypeResolver;
  RgbaColor?: IRgbaColorTypeResolver;
  RoadmapGame?: IRoadmapGameTypeResolver;
  RoadmapGameMarket?: IRoadmapGameMarketTypeResolver;
  RootQuery?: IRootQueryTypeResolver;
  SanityAssetSourceData?: ISanityAssetSourceDataTypeResolver;
  SanityFileAsset?: ISanityFileAssetTypeResolver;
  SanityImageAsset?: ISanityImageAssetTypeResolver;
  SanityImageCrop?: ISanityImageCropTypeResolver;
  SanityImageDimensions?: ISanityImageDimensionsTypeResolver;
  SanityImageHotspot?: ISanityImageHotspotTypeResolver;
  SanityImageMetadata?: ISanityImageMetadataTypeResolver;
  SanityImagePalette?: ISanityImagePaletteTypeResolver;
  SanityImagePaletteSwatch?: ISanityImagePaletteSwatchTypeResolver;
  SectionGroupSliderGames?: ISectionGroupSliderGamesTypeResolver;
  SectionSliderGames?: ISectionSliderGamesTypeResolver;
  SingleLine1?: ISingleLine1TypeResolver;
  SingleLine2?: ISingleLine2TypeResolver;
  SingleLine3?: ISingleLine3TypeResolver;
  SiteConfig?: ISiteConfigTypeResolver;
  SitePage?: ISitePageTypeResolver;
  Slug?: ISlugTypeResolver;
  SocialNetwork?: ISocialNetworkTypeResolver;
  Span?: ISpanTypeResolver;
  Studio?: IStudioTypeResolver;
  Textarea?: ITextareaTypeResolver;
  Tournament?: ITournamentTypeResolver;
  UrlTarget?: IUrlTargetTypeResolver;
  Video?: IVideoTypeResolver;
  VideoUrl?: IVideoUrlTypeResolver;
  WebsiteConfig?: IWebsiteConfigTypeResolver;
  WidgetPartners?: IWidgetPartnersTypeResolver;
  WidgetSwiper?: IWidgetSwiperTypeResolver;
  WidgetSwiperParameters?: IWidgetSwiperParametersTypeResolver;
  Date?: GraphQLScalarType;
  DateTime?: GraphQLScalarType;
  JSON?: GraphQLScalarType;
}
export interface IDocumentTypeResolver<TParent = any> {
  (parent: TParent, context: any, info: GraphQLResolveInfo): 'Banner' | 'Country' | 'Currency' | 'EmailAddress' | 'Employee' | 'EngagementSlide' | 'Game' | 'GameCategory' | 'GameCertificate' | 'GameCode' | 'GamePlaylist' | 'GameRTP' | 'GameRibbon' | 'GameType' | 'JackpotInfo' | 'Language' | 'License' | 'Link' | 'Localization' | 'Market' | 'MediaTag' | 'News' | 'NewsCategory' | 'Office' | 'PageAboutUs' | 'PageGameSearch' | 'PageGames' | 'PageGamingSolutions' | 'PageHome' | 'PageLiveCasino' | 'PageMediaServices' | 'PageNews' | 'PageRoadmap' | 'PageToolsBonusCoin' | 'PageToolsGRC' | 'PageToolsJackpot' | 'PageToolsTournament' | 'PageTournaments' | 'Partner' | 'Phone' | 'Provider' | 'Region' | 'RoadmapGame' | 'RoadmapGameMarket' | 'SanityFileAsset' | 'SanityImageAsset' | 'SectionGroupSliderGames' | 'SectionSliderGames' | 'SiteConfig' | 'SitePage' | 'SocialNetwork' | 'Studio' | 'Tournament' | 'WebsiteConfig' | 'WidgetPartners' | 'WidgetSwiper' | Promise<'Banner' | 'Country' | 'Currency' | 'EmailAddress' | 'Employee' | 'EngagementSlide' | 'Game' | 'GameCategory' | 'GameCertificate' | 'GameCode' | 'GamePlaylist' | 'GameRTP' | 'GameRibbon' | 'GameType' | 'JackpotInfo' | 'Language' | 'License' | 'Link' | 'Localization' | 'Market' | 'MediaTag' | 'News' | 'NewsCategory' | 'Office' | 'PageAboutUs' | 'PageGameSearch' | 'PageGames' | 'PageGamingSolutions' | 'PageHome' | 'PageLiveCasino' | 'PageMediaServices' | 'PageNews' | 'PageRoadmap' | 'PageToolsBonusCoin' | 'PageToolsGRC' | 'PageToolsJackpot' | 'PageToolsTournament' | 'PageTournaments' | 'Partner' | 'Phone' | 'Provider' | 'Region' | 'RoadmapGame' | 'RoadmapGameMarket' | 'SanityFileAsset' | 'SanityImageAsset' | 'SectionGroupSliderGames' | 'SectionSliderGames' | 'SiteConfig' | 'SitePage' | 'SocialNetwork' | 'Studio' | 'Tournament' | 'WebsiteConfig' | 'WidgetPartners' | 'WidgetSwiper'>;
}
export interface IBlockOrImageTypeResolver<TParent = any> {
  (parent: TParent, context: any, info: GraphQLResolveInfo): 'Block' | 'Image' | Promise<'Block' | 'Image'>;
}
export interface ILinkOrSitePageTypeResolver<TParent = any> {
  (parent: TParent, context: any, info: GraphQLResolveInfo): 'Link' | 'SitePage' | Promise<'Link' | 'SitePage'>;
}
export interface ISingleLine1OrSingleLine2OrSingleLine3OrTextareaTypeResolver<TParent = any> {
  (parent: TParent, context: any, info: GraphQLResolveInfo): 'SingleLine1' | 'SingleLine2' | 'SingleLine3' | 'Textarea' | Promise<'SingleLine1' | 'SingleLine2' | 'SingleLine3' | 'Textarea'>;
}
export interface IBannerTypeResolver<TParent = any> {
  _createdAt?: BannerTo_createdAtResolver<TParent>;
  _id?: BannerTo_idResolver<TParent>;
  _key?: BannerTo_keyResolver<TParent>;
  _rev?: BannerTo_revResolver<TParent>;
  _type?: BannerTo_typeResolver<TParent>;
  _updatedAt?: BannerTo_updatedAtResolver<TParent>;
  banner1920x120?: BannerToBanner1920x120Resolver<TParent>;
  banner600x140?: BannerToBanner600x140Resolver<TParent>;
  bannerName?: BannerToBannerNameResolver<TParent>;
  bannerOnDesktop?: BannerToBannerOnDesktopResolver<TParent>;
  bannerOnMobileLandscape?: BannerToBannerOnMobileLandscapeResolver<TParent>;
  bannerOnMobilePortrait?: BannerToBannerOnMobilePortraitResolver<TParent>;
  bannerType?: BannerToBannerTypeResolver<TParent>;
  code?: BannerToCodeResolver<TParent>;
  game?: BannerToGameResolver<TParent>;
  isActive?: BannerToIsActiveResolver<TParent>;
  url?: BannerToUrlResolver<TParent>;
  urlType?: BannerToUrlTypeResolver<TParent>;
}

export interface BannerTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannerTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannerTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannerTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannerTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannerTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannerToBanner1920x120Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannerToBanner600x140Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannerToBannerNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannerToBannerOnDesktopResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannerToBannerOnMobileLandscapeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannerToBannerOnMobilePortraitResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannerToBannerTypeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannerToCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannerToGameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannerToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannerToUrlResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannerToUrlTypeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IBannerAssetTypeResolver<TParent = any> {
  _key?: BannerAssetTo_keyResolver<TParent>;
  _type?: BannerAssetTo_typeResolver<TParent>;
  assetImage?: BannerAssetToAssetImageResolver<TParent>;
  assetType?: BannerAssetToAssetTypeResolver<TParent>;
  assetVideo?: BannerAssetToAssetVideoResolver<TParent>;
}

export interface BannerAssetTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannerAssetTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannerAssetToAssetImageResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannerAssetToAssetTypeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannerAssetToAssetVideoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IBannersCarouselTypeResolver<TParent = any> {
  _key?: BannersCarouselTo_keyResolver<TParent>;
  _type?: BannersCarouselTo_typeResolver<TParent>;
  autoPlay?: BannersCarouselToAutoPlayResolver<TParent>;
  banners?: BannersCarouselToBannersResolver<TParent>;
  isActive?: BannersCarouselToIsActiveResolver<TParent>;
  isLoop?: BannersCarouselToIsLoopResolver<TParent>;
}

export interface BannersCarouselTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannersCarouselTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannersCarouselToAutoPlayResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannersCarouselToBannersResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannersCarouselToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BannersCarouselToIsLoopResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IBlockTypeResolver<TParent = any> {
  _key?: BlockTo_keyResolver<TParent>;
  _type?: BlockTo_typeResolver<TParent>;
  children?: BlockToChildrenResolver<TParent>;
  list?: BlockToListResolver<TParent>;
  style?: BlockToStyleResolver<TParent>;
}

export interface BlockTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BlockTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BlockToChildrenResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BlockToListResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface BlockToStyleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IColorTypeResolver<TParent = any> {
  _key?: ColorTo_keyResolver<TParent>;
  _type?: ColorTo_typeResolver<TParent>;
  alpha?: ColorToAlphaResolver<TParent>;
  hex?: ColorToHexResolver<TParent>;
  hsl?: ColorToHslResolver<TParent>;
  hsv?: ColorToHsvResolver<TParent>;
  rgb?: ColorToRgbResolver<TParent>;
}

export interface ColorTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ColorTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ColorToAlphaResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ColorToHexResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ColorToHslResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ColorToHsvResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ColorToRgbResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ICountryTypeResolver<TParent = any> {
  _createdAt?: CountryTo_createdAtResolver<TParent>;
  _id?: CountryTo_idResolver<TParent>;
  _key?: CountryTo_keyResolver<TParent>;
  _rev?: CountryTo_revResolver<TParent>;
  _type?: CountryTo_typeResolver<TParent>;
  _updatedAt?: CountryTo_updatedAtResolver<TParent>;
  countryCode?: CountryToCountryCodeResolver<TParent>;
  countryIcon?: CountryToCountryIconResolver<TParent>;
  countryName?: CountryToCountryNameResolver<TParent>;
  countryNativeName?: CountryToCountryNativeNameResolver<TParent>;
  currencies?: CountryToCurrenciesResolver<TParent>;
  games?: CountryToGamesResolver<TParent>;
  languages?: CountryToLanguagesResolver<TParent>;
}

export interface CountryTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CountryTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CountryTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CountryTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CountryTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CountryTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CountryToCountryCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CountryToCountryIconResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CountryToCountryNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CountryToCountryNativeNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CountryToCurrenciesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CountryToGamesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CountryToLanguagesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ICountryGamesTypeResolver<TParent = any> {
  _key?: CountryGamesTo_keyResolver<TParent>;
  _type?: CountryGamesTo_typeResolver<TParent>;
  country?: CountryGamesToCountryResolver<TParent>;
  games?: CountryGamesToGamesResolver<TParent>;
}

export interface CountryGamesTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CountryGamesTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CountryGamesToCountryResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CountryGamesToGamesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ICurrencyTypeResolver<TParent = any> {
  _createdAt?: CurrencyTo_createdAtResolver<TParent>;
  _id?: CurrencyTo_idResolver<TParent>;
  _key?: CurrencyTo_keyResolver<TParent>;
  _rev?: CurrencyTo_revResolver<TParent>;
  _type?: CurrencyTo_typeResolver<TParent>;
  _updatedAt?: CurrencyTo_updatedAtResolver<TParent>;
  currencyCode?: CurrencyToCurrencyCodeResolver<TParent>;
  currencyIcon?: CurrencyToCurrencyIconResolver<TParent>;
  currencyName?: CurrencyToCurrencyNameResolver<TParent>;
  currencyNativeName?: CurrencyToCurrencyNativeNameResolver<TParent>;
}

export interface CurrencyTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CurrencyTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CurrencyTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CurrencyTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CurrencyTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CurrencyTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CurrencyToCurrencyCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CurrencyToCurrencyIconResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CurrencyToCurrencyNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface CurrencyToCurrencyNativeNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IEmailAddressTypeResolver<TParent = any> {
  _createdAt?: EmailAddressTo_createdAtResolver<TParent>;
  _id?: EmailAddressTo_idResolver<TParent>;
  _key?: EmailAddressTo_keyResolver<TParent>;
  _rev?: EmailAddressTo_revResolver<TParent>;
  _type?: EmailAddressTo_typeResolver<TParent>;
  _updatedAt?: EmailAddressTo_updatedAtResolver<TParent>;
  code?: EmailAddressToCodeResolver<TParent>;
  emailAddress?: EmailAddressToEmailAddressResolver<TParent>;
  emailTitle?: EmailAddressToEmailTitleResolver<TParent>;
  isActive?: EmailAddressToIsActiveResolver<TParent>;
}

export interface EmailAddressTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EmailAddressTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EmailAddressTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EmailAddressTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EmailAddressTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EmailAddressTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EmailAddressToCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EmailAddressToEmailAddressResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EmailAddressToEmailTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EmailAddressToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IEmployeeTypeResolver<TParent = any> {
  _createdAt?: EmployeeTo_createdAtResolver<TParent>;
  _id?: EmployeeTo_idResolver<TParent>;
  _key?: EmployeeTo_keyResolver<TParent>;
  _rev?: EmployeeTo_revResolver<TParent>;
  _type?: EmployeeTo_typeResolver<TParent>;
  _updatedAt?: EmployeeTo_updatedAtResolver<TParent>;
  employeeDescription?: EmployeeToEmployeeDescriptionResolver<TParent>;
  employeeName?: EmployeeToEmployeeNameResolver<TParent>;
  employeePhoto?: EmployeeToEmployeePhotoResolver<TParent>;
  employeePosition?: EmployeeToEmployeePositionResolver<TParent>;
  isActive?: EmployeeToIsActiveResolver<TParent>;
}

export interface EmployeeTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EmployeeTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EmployeeTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EmployeeTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EmployeeTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EmployeeTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EmployeeToEmployeeDescriptionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EmployeeToEmployeeNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EmployeeToEmployeePhotoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EmployeeToEmployeePositionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EmployeeToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IEngagementSlideTypeResolver<TParent = any> {
  _createdAt?: EngagementSlideTo_createdAtResolver<TParent>;
  _id?: EngagementSlideTo_idResolver<TParent>;
  _key?: EngagementSlideTo_keyResolver<TParent>;
  _rev?: EngagementSlideTo_revResolver<TParent>;
  _type?: EngagementSlideTo_typeResolver<TParent>;
  _updatedAt?: EngagementSlideTo_updatedAtResolver<TParent>;
  header1?: EngagementSlideToHeader1Resolver<TParent>;
  header2?: EngagementSlideToHeader2Resolver<TParent>;
  image?: EngagementSlideToImageResolver<TParent>;
  imageType?: EngagementSlideToImageTypeResolver<TParent>;
  isActive?: EngagementSlideToIsActiveResolver<TParent>;
  text1?: EngagementSlideToText1Resolver<TParent>;
  text2?: EngagementSlideToText2Resolver<TParent>;
}

export interface EngagementSlideTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EngagementSlideTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EngagementSlideTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EngagementSlideTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EngagementSlideTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EngagementSlideTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EngagementSlideToHeader1Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EngagementSlideToHeader2Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EngagementSlideToImageResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EngagementSlideToImageTypeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EngagementSlideToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EngagementSlideToText1Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface EngagementSlideToText2Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IFileTypeResolver<TParent = any> {
  _key?: FileTo_keyResolver<TParent>;
  _type?: FileTo_typeResolver<TParent>;
  asset?: FileToAssetResolver<TParent>;
}

export interface FileTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface FileTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface FileToAssetResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IGameTypeResolver<TParent = any> {
  _createdAt?: GameTo_createdAtResolver<TParent>;
  _id?: GameTo_idResolver<TParent>;
  _key?: GameTo_keyResolver<TParent>;
  _rev?: GameTo_revResolver<TParent>;
  _type?: GameTo_typeResolver<TParent>;
  _updatedAt?: GameTo_updatedAtResolver<TParent>;
  availableIn?: GameToAvailableInResolver<TParent>;
  certificatesPerMarket?: GameToCertificatesPerMarketResolver<TParent>;
  gameBillboard?: GameToGameBillboardResolver<TParent>;
  gameBrandedPoster?: GameToGameBrandedPosterResolver<TParent>;
  gameCode?: GameToGameCodeResolver<TParent>;
  gameDescription?: GameToGameDescriptionResolver<TParent>;
  gameFeatures?: GameToGameFeaturesResolver<TParent>;
  gameHDPoster?: GameToGameHDPosterResolver<TParent>;
  gameIcon?: GameToGameIconResolver<TParent>;
  gameId?: GameToGameIdResolver<TParent>;
  gameImage?: GameToGameImageResolver<TParent>;
  gameInfo?: GameToGameInfoResolver<TParent>;
  gameLogo?: GameToGameLogoResolver<TParent>;
  gameManage?: GameToGameManageResolver<TParent>;
  gameName?: GameToGameNameResolver<TParent>;
  gameOrientation?: GameToGameOrientationResolver<TParent>;
  gamePoster?: GameToGamePosterResolver<TParent>;
  gameRTP?: GameToGameRTPResolver<TParent>;
  gameRibbon?: GameToGameRibbonResolver<TParent>;
  gameScreenshotHD?: GameToGameScreenshotHDResolver<TParent>;
  gameScreenshotPoster?: GameToGameScreenshotPosterResolver<TParent>;
  gameTop4Poster?: GameToGameTop4PosterResolver<TParent>;
  gameTowerPoster?: GameToGameTowerPosterResolver<TParent>;
  gameType?: GameToGameTypeResolver<TParent>;
  gameVideo?: GameToGameVideoResolver<TParent>;
  gameVideos?: GameToGameVideosResolver<TParent>;
  header?: GameToHeaderResolver<TParent>;
  isActive?: GameToIsActiveResolver<TParent>;
  paUrl?: GameToPaUrlResolver<TParent>;
  releaseNote?: GameToReleaseNoteResolver<TParent>;
  saleSheet?: GameToSaleSheetResolver<TParent>;
  showGameCode?: GameToShowGameCodeResolver<TParent>;
  showGameRTP?: GameToShowGameRTPResolver<TParent>;
  siteDescription?: GameToSiteDescriptionResolver<TParent>;
}

export interface GameTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToAvailableInResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToCertificatesPerMarketResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameBillboardResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameBrandedPosterResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameDescriptionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameFeaturesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameHDPosterResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameIconResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameIdResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameImageResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameInfoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameLogoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameManageResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameOrientationResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGamePosterResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameRTPResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameRibbonResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameScreenshotHDResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameScreenshotPosterResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameTop4PosterResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameTowerPosterResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameTypeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameVideoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToGameVideosResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToHeaderResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToPaUrlResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToReleaseNoteResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToSaleSheetResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToShowGameCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToShowGameRTPResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameToSiteDescriptionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IGameAPITypeResolver<TParent = any> {
  _key?: GameAPITo_keyResolver<TParent>;
  _type?: GameAPITo_typeResolver<TParent>;
  playerApiUrl?: GameAPIToPlayerApiUrlResolver<TParent>;
  siteApiUrl?: GameAPIToSiteApiUrlResolver<TParent>;
  siteToken?: GameAPIToSiteTokenResolver<TParent>;
}

export interface GameAPITo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameAPITo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameAPIToPlayerApiUrlResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameAPIToSiteApiUrlResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameAPIToSiteTokenResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IGameCategoryTypeResolver<TParent = any> {
  _createdAt?: GameCategoryTo_createdAtResolver<TParent>;
  _id?: GameCategoryTo_idResolver<TParent>;
  _key?: GameCategoryTo_keyResolver<TParent>;
  _rev?: GameCategoryTo_revResolver<TParent>;
  _type?: GameCategoryTo_typeResolver<TParent>;
  _updatedAt?: GameCategoryTo_updatedAtResolver<TParent>;
  categoryCode?: GameCategoryToCategoryCodeResolver<TParent>;
  categoryGames?: GameCategoryToCategoryGamesResolver<TParent>;
  categoryName?: GameCategoryToCategoryNameResolver<TParent>;
  categoryRegion?: GameCategoryToCategoryRegionResolver<TParent>;
  categoryType?: GameCategoryToCategoryTypeResolver<TParent>;
  countryGames?: GameCategoryToCountryGamesResolver<TParent>;
  isActive?: GameCategoryToIsActiveResolver<TParent>;
  showGamesFrom?: GameCategoryToShowGamesFromResolver<TParent>;
}

export interface GameCategoryTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCategoryTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCategoryTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCategoryTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCategoryTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCategoryTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCategoryToCategoryCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCategoryToCategoryGamesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCategoryToCategoryNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCategoryToCategoryRegionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCategoryToCategoryTypeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCategoryToCountryGamesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCategoryToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCategoryToShowGamesFromResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IGameCertificateTypeResolver<TParent = any> {
  _createdAt?: GameCertificateTo_createdAtResolver<TParent>;
  _id?: GameCertificateTo_idResolver<TParent>;
  _key?: GameCertificateTo_keyResolver<TParent>;
  _rev?: GameCertificateTo_revResolver<TParent>;
  _type?: GameCertificateTo_typeResolver<TParent>;
  _updatedAt?: GameCertificateTo_updatedAtResolver<TParent>;
  gameCertificateCode?: GameCertificateToGameCertificateCodeResolver<TParent>;
  gameCertificateName?: GameCertificateToGameCertificateNameResolver<TParent>;
}

export interface GameCertificateTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCertificateTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCertificateTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCertificateTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCertificateTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCertificateTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCertificateToGameCertificateCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCertificateToGameCertificateNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IGameCodeTypeResolver<TParent = any> {
  _createdAt?: GameCodeTo_createdAtResolver<TParent>;
  _id?: GameCodeTo_idResolver<TParent>;
  _key?: GameCodeTo_keyResolver<TParent>;
  _rev?: GameCodeTo_revResolver<TParent>;
  _type?: GameCodeTo_typeResolver<TParent>;
  _updatedAt?: GameCodeTo_updatedAtResolver<TParent>;
  gameCode?: GameCodeToGameCodeResolver<TParent>;
  gameInfo?: GameCodeToGameInfoResolver<TParent>;
  gameProvider?: GameCodeToGameProviderResolver<TParent>;
  marketingMaterials?: GameCodeToMarketingMaterialsResolver<TParent>;
}

export interface GameCodeTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCodeTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCodeTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCodeTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCodeTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCodeTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCodeToGameCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCodeToGameInfoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCodeToGameProviderResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameCodeToMarketingMaterialsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IGameFeaturesTypeResolver<TParent = any> {
  _key?: GameFeaturesTo_keyResolver<TParent>;
  _type?: GameFeaturesTo_typeResolver<TParent>;
  gameFeatureDescription?: GameFeaturesToGameFeatureDescriptionResolver<TParent>;
  gameFeaturePoster?: GameFeaturesToGameFeaturePosterResolver<TParent>;
  gameFeatureTitle?: GameFeaturesToGameFeatureTitleResolver<TParent>;
}

export interface GameFeaturesTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameFeaturesTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameFeaturesToGameFeatureDescriptionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameFeaturesToGameFeaturePosterResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameFeaturesToGameFeatureTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IGameInfoTypeResolver<TParent = any> {
  _key?: GameInfoTo_keyResolver<TParent>;
  _type?: GameInfoTo_typeResolver<TParent>;
  formatOfReleaseDate?: GameInfoToFormatOfReleaseDateResolver<TParent>;
  isFreeBets?: GameInfoToIsFreeBetsResolver<TParent>;
  isLuckyEnvelopes?: GameInfoToIsLuckyEnvelopesResolver<TParent>;
  isMustWinJackpot?: GameInfoToIsMustWinJackpotResolver<TParent>;
  isTournaments?: GameInfoToIsTournamentsResolver<TParent>;
  layout?: GameInfoToLayoutResolver<TParent>;
  markets?: GameInfoToMarketsResolver<TParent>;
  releaseDates?: GameInfoToReleaseDatesResolver<TParent>;
  rtp?: GameInfoToRtpResolver<TParent>;
  volatility?: GameInfoToVolatilityResolver<TParent>;
  ways?: GameInfoToWaysResolver<TParent>;
}

export interface GameInfoTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameInfoTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameInfoToFormatOfReleaseDateResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameInfoToIsFreeBetsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameInfoToIsLuckyEnvelopesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameInfoToIsMustWinJackpotResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameInfoToIsTournamentsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameInfoToLayoutResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameInfoToMarketsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameInfoToReleaseDatesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameInfoToRtpResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameInfoToVolatilityResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameInfoToWaysResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IGameLineTypeResolver<TParent = any> {
  _key?: GameLineTo_keyResolver<TParent>;
  _type?: GameLineTo_typeResolver<TParent>;
  game?: GameLineToGameResolver<TParent>;
  gameRibbon?: GameLineToGameRibbonResolver<TParent>;
  layout?: GameLineToLayoutResolver<TParent>;
}

export interface GameLineTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameLineTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameLineToGameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameLineToGameRibbonResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameLineToLayoutResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IGameManageTypeResolver<TParent = any> {
  _key?: GameManageTo_keyResolver<TParent>;
  _type?: GameManageTo_typeResolver<TParent>;
  releaseDate?: GameManageToReleaseDateResolver<TParent>;
  releaseText?: GameManageToReleaseTextResolver<TParent>;
  status?: GameManageToStatusResolver<TParent>;
}

export interface GameManageTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameManageTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameManageToReleaseDateResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameManageToReleaseTextResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameManageToStatusResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IGameMarketCertificatesTypeResolver<TParent = any> {
  _key?: GameMarketCertificatesTo_keyResolver<TParent>;
  _type?: GameMarketCertificatesTo_typeResolver<TParent>;
  certificate?: GameMarketCertificatesToCertificateResolver<TParent>;
  certificate_1?: GameMarketCertificatesToCertificate_1Resolver<TParent>;
  certificate_2?: GameMarketCertificatesToCertificate_2Resolver<TParent>;
  comingSoon?: GameMarketCertificatesToComingSoonResolver<TParent>;
  gameMarket?: GameMarketCertificatesToGameMarketResolver<TParent>;
}

export interface GameMarketCertificatesTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameMarketCertificatesTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameMarketCertificatesToCertificateResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameMarketCertificatesToCertificate_1Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameMarketCertificatesToCertificate_2Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameMarketCertificatesToComingSoonResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameMarketCertificatesToGameMarketResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IGamePlaylistTypeResolver<TParent = any> {
  _createdAt?: GamePlaylistTo_createdAtResolver<TParent>;
  _id?: GamePlaylistTo_idResolver<TParent>;
  _key?: GamePlaylistTo_keyResolver<TParent>;
  _rev?: GamePlaylistTo_revResolver<TParent>;
  _type?: GamePlaylistTo_typeResolver<TParent>;
  _updatedAt?: GamePlaylistTo_updatedAtResolver<TParent>;
  isActive?: GamePlaylistToIsActiveResolver<TParent>;
  playlistBanner?: GamePlaylistToPlaylistBannerResolver<TParent>;
  playlistCode?: GamePlaylistToPlaylistCodeResolver<TParent>;
  playlistDescription?: GamePlaylistToPlaylistDescriptionResolver<TParent>;
  playlistGames?: GamePlaylistToPlaylistGamesResolver<TParent>;
  playlistName?: GamePlaylistToPlaylistNameResolver<TParent>;
  playlistPoster?: GamePlaylistToPlaylistPosterResolver<TParent>;
  playlistRegion?: GamePlaylistToPlaylistRegionResolver<TParent>;
}

export interface GamePlaylistTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GamePlaylistTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GamePlaylistTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GamePlaylistTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GamePlaylistTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GamePlaylistTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GamePlaylistToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GamePlaylistToPlaylistBannerResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GamePlaylistToPlaylistCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GamePlaylistToPlaylistDescriptionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GamePlaylistToPlaylistGamesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GamePlaylistToPlaylistNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GamePlaylistToPlaylistPosterResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GamePlaylistToPlaylistRegionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IGameRTPTypeResolver<TParent = any> {
  _createdAt?: GameRTPTo_createdAtResolver<TParent>;
  _id?: GameRTPTo_idResolver<TParent>;
  _key?: GameRTPTo_keyResolver<TParent>;
  _rev?: GameRTPTo_revResolver<TParent>;
  _type?: GameRTPTo_typeResolver<TParent>;
  _updatedAt?: GameRTPTo_updatedAtResolver<TParent>;
  gameRTP?: GameRTPToGameRTPResolver<TParent>;
}

export interface GameRTPTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameRTPTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameRTPTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameRTPTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameRTPTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameRTPTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameRTPToGameRTPResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IGameReleaseDateTypeResolver<TParent = any> {
  _key?: GameReleaseDateTo_keyResolver<TParent>;
  _type?: GameReleaseDateTo_typeResolver<TParent>;
  gameMarket?: GameReleaseDateToGameMarketResolver<TParent>;
  releaseDate?: GameReleaseDateToReleaseDateResolver<TParent>;
}

export interface GameReleaseDateTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameReleaseDateTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameReleaseDateToGameMarketResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameReleaseDateToReleaseDateResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IGameRibbonTypeResolver<TParent = any> {
  _createdAt?: GameRibbonTo_createdAtResolver<TParent>;
  _id?: GameRibbonTo_idResolver<TParent>;
  _key?: GameRibbonTo_keyResolver<TParent>;
  _rev?: GameRibbonTo_revResolver<TParent>;
  _type?: GameRibbonTo_typeResolver<TParent>;
  _updatedAt?: GameRibbonTo_updatedAtResolver<TParent>;
  gameRibbonCode?: GameRibbonToGameRibbonCodeResolver<TParent>;
  gameRibbonImage?: GameRibbonToGameRibbonImageResolver<TParent>;
  gameRibbonName?: GameRibbonToGameRibbonNameResolver<TParent>;
}

export interface GameRibbonTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameRibbonTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameRibbonTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameRibbonTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameRibbonTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameRibbonTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameRibbonToGameRibbonCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameRibbonToGameRibbonImageResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameRibbonToGameRibbonNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IGameTypeTypeResolver<TParent = any> {
  _createdAt?: GameTypeTo_createdAtResolver<TParent>;
  _id?: GameTypeTo_idResolver<TParent>;
  _key?: GameTypeTo_keyResolver<TParent>;
  _rev?: GameTypeTo_revResolver<TParent>;
  _type?: GameTypeTo_typeResolver<TParent>;
  _updatedAt?: GameTypeTo_updatedAtResolver<TParent>;
  gameTypeCode?: GameTypeToGameTypeCodeResolver<TParent>;
  gameTypeTitle?: GameTypeToGameTypeTitleResolver<TParent>;
}

export interface GameTypeTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameTypeTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameTypeTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameTypeTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameTypeTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameTypeTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameTypeToGameTypeCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GameTypeToGameTypeTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IGeopointTypeResolver<TParent = any> {
  _key?: GeopointTo_keyResolver<TParent>;
  _type?: GeopointTo_typeResolver<TParent>;
  alt?: GeopointToAltResolver<TParent>;
  lat?: GeopointToLatResolver<TParent>;
  lng?: GeopointToLngResolver<TParent>;
}

export interface GeopointTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GeopointTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GeopointToAltResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GeopointToLatResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface GeopointToLngResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IHeaderTextTypeResolver<TParent = any> {
  _key?: HeaderTextTo_keyResolver<TParent>;
  _type?: HeaderTextTo_typeResolver<TParent>;
  headerTextArea?: HeaderTextToHeaderTextAreaResolver<TParent>;
  headerTitle?: HeaderTextToHeaderTitleResolver<TParent>;
}

export interface HeaderTextTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface HeaderTextTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface HeaderTextToHeaderTextAreaResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface HeaderTextToHeaderTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IHeaderTickerTypeResolver<TParent = any> {
  _key?: HeaderTickerTo_keyResolver<TParent>;
  _type?: HeaderTickerTo_typeResolver<TParent>;
  isActive?: HeaderTickerToIsActiveResolver<TParent>;
  tickerDate?: HeaderTickerToTickerDateResolver<TParent>;
}

export interface HeaderTickerTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface HeaderTickerTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface HeaderTickerToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface HeaderTickerToTickerDateResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IHeaderTitleTypeResolver<TParent = any> {
  _key?: HeaderTitleTo_keyResolver<TParent>;
  _type?: HeaderTitleTo_typeResolver<TParent>;
  title1?: HeaderTitleToTitle1Resolver<TParent>;
  title2?: HeaderTitleToTitle2Resolver<TParent>;
  title3?: HeaderTitleToTitle3Resolver<TParent>;
}

export interface HeaderTitleTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface HeaderTitleTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface HeaderTitleToTitle1Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface HeaderTitleToTitle2Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface HeaderTitleToTitle3Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IHslaColorTypeResolver<TParent = any> {
  _key?: HslaColorTo_keyResolver<TParent>;
  _type?: HslaColorTo_typeResolver<TParent>;
  a?: HslaColorToAResolver<TParent>;
  h?: HslaColorToHResolver<TParent>;
  l?: HslaColorToLResolver<TParent>;
  s?: HslaColorToSResolver<TParent>;
}

export interface HslaColorTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface HslaColorTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface HslaColorToAResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface HslaColorToHResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface HslaColorToLResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface HslaColorToSResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IHsvaColorTypeResolver<TParent = any> {
  _key?: HsvaColorTo_keyResolver<TParent>;
  _type?: HsvaColorTo_typeResolver<TParent>;
  a?: HsvaColorToAResolver<TParent>;
  h?: HsvaColorToHResolver<TParent>;
  s?: HsvaColorToSResolver<TParent>;
  v?: HsvaColorToVResolver<TParent>;
}

export interface HsvaColorTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface HsvaColorTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface HsvaColorToAResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface HsvaColorToHResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface HsvaColorToSResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface HsvaColorToVResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IImageTypeResolver<TParent = any> {
  _key?: ImageTo_keyResolver<TParent>;
  _type?: ImageTo_typeResolver<TParent>;
  asset?: ImageToAssetResolver<TParent>;
  crop?: ImageToCropResolver<TParent>;
  hotspot?: ImageToHotspotResolver<TParent>;
}

export interface ImageTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ImageTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ImageToAssetResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ImageToCropResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ImageToHotspotResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IJackpotInfoTypeResolver<TParent = any> {
  _createdAt?: JackpotInfoTo_createdAtResolver<TParent>;
  _id?: JackpotInfoTo_idResolver<TParent>;
  _key?: JackpotInfoTo_keyResolver<TParent>;
  _rev?: JackpotInfoTo_revResolver<TParent>;
  _type?: JackpotInfoTo_typeResolver<TParent>;
  _updatedAt?: JackpotInfoTo_updatedAtResolver<TParent>;
  code?: JackpotInfoToCodeResolver<TParent>;
  description?: JackpotInfoToDescriptionResolver<TParent>;
  header?: JackpotInfoToHeaderResolver<TParent>;
  icon?: JackpotInfoToIconResolver<TParent>;
  iconHover?: JackpotInfoToIconHoverResolver<TParent>;
  isActive?: JackpotInfoToIsActiveResolver<TParent>;
}

export interface JackpotInfoTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface JackpotInfoTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface JackpotInfoTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface JackpotInfoTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface JackpotInfoTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface JackpotInfoTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface JackpotInfoToCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface JackpotInfoToDescriptionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface JackpotInfoToHeaderResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface JackpotInfoToIconResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface JackpotInfoToIconHoverResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface JackpotInfoToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ILanguageTypeResolver<TParent = any> {
  _createdAt?: LanguageTo_createdAtResolver<TParent>;
  _id?: LanguageTo_idResolver<TParent>;
  _key?: LanguageTo_keyResolver<TParent>;
  _rev?: LanguageTo_revResolver<TParent>;
  _type?: LanguageTo_typeResolver<TParent>;
  _updatedAt?: LanguageTo_updatedAtResolver<TParent>;
  languageCode?: LanguageToLanguageCodeResolver<TParent>;
  languageIcon?: LanguageToLanguageIconResolver<TParent>;
  languageName?: LanguageToLanguageNameResolver<TParent>;
  languageNativeName?: LanguageToLanguageNativeNameResolver<TParent>;
}

export interface LanguageTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LanguageTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LanguageTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LanguageTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LanguageTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LanguageTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LanguageToLanguageCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LanguageToLanguageIconResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LanguageToLanguageNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LanguageToLanguageNativeNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ILicenseTypeResolver<TParent = any> {
  _createdAt?: LicenseTo_createdAtResolver<TParent>;
  _id?: LicenseTo_idResolver<TParent>;
  _key?: LicenseTo_keyResolver<TParent>;
  _rev?: LicenseTo_revResolver<TParent>;
  _type?: LicenseTo_typeResolver<TParent>;
  _updatedAt?: LicenseTo_updatedAtResolver<TParent>;
  isActive?: LicenseToIsActiveResolver<TParent>;
  jurisdiction?: LicenseToJurisdictionResolver<TParent>;
  licenseDescription?: LicenseToLicenseDescriptionResolver<TParent>;
  licenseNo?: LicenseToLicenseNoResolver<TParent>;
  licensePhoto?: LicenseToLicensePhotoResolver<TParent>;
  licenseUrl?: LicenseToLicenseUrlResolver<TParent>;
  regulator?: LicenseToRegulatorResolver<TParent>;
}

export interface LicenseTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LicenseTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LicenseTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LicenseTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LicenseTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LicenseTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LicenseToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LicenseToJurisdictionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LicenseToLicenseDescriptionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LicenseToLicenseNoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LicenseToLicensePhotoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LicenseToLicenseUrlResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LicenseToRegulatorResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ILinkTypeResolver<TParent = any> {
  _createdAt?: LinkTo_createdAtResolver<TParent>;
  _id?: LinkTo_idResolver<TParent>;
  _key?: LinkTo_keyResolver<TParent>;
  _rev?: LinkTo_revResolver<TParent>;
  _type?: LinkTo_typeResolver<TParent>;
  _updatedAt?: LinkTo_updatedAtResolver<TParent>;
  code?: LinkToCodeResolver<TParent>;
  isActive?: LinkToIsActiveResolver<TParent>;
  isExternal?: LinkToIsExternalResolver<TParent>;
  title?: LinkToTitleResolver<TParent>;
}

export interface LinkTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LinkTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LinkTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LinkTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LinkTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LinkTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LinkToCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LinkToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LinkToIsExternalResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LinkToTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ILocaleBlockTypeResolver<TParent = any> {
  _key?: LocaleBlockTo_keyResolver<TParent>;
  _type?: LocaleBlockTo_typeResolver<TParent>;
  enRaw?: LocaleBlockToEnRawResolver<TParent>;
  zhCnRaw?: LocaleBlockToZhCnRawResolver<TParent>;
}

export interface LocaleBlockTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocaleBlockTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocaleBlockToEnRawResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocaleBlockToZhCnRawResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ILocaleImageTypeResolver<TParent = any> {
  _key?: LocaleImageTo_keyResolver<TParent>;
  _type?: LocaleImageTo_typeResolver<TParent>;
  en?: LocaleImageToEnResolver<TParent>;
  zhCn?: LocaleImageToZhCnResolver<TParent>;
}

export interface LocaleImageTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocaleImageTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocaleImageToEnResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocaleImageToZhCnResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ILocaleStringTypeResolver<TParent = any> {
  _key?: LocaleStringTo_keyResolver<TParent>;
  _type?: LocaleStringTo_typeResolver<TParent>;
  en?: LocaleStringToEnResolver<TParent>;
  zhCn?: LocaleStringToZhCnResolver<TParent>;
}

export interface LocaleStringTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocaleStringTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocaleStringToEnResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocaleStringToZhCnResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ILocaleTextTypeResolver<TParent = any> {
  _key?: LocaleTextTo_keyResolver<TParent>;
  _type?: LocaleTextTo_typeResolver<TParent>;
  en?: LocaleTextToEnResolver<TParent>;
  zhCn?: LocaleTextToZhCnResolver<TParent>;
}

export interface LocaleTextTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocaleTextTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocaleTextToEnResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocaleTextToZhCnResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ILocaleUrlTypeResolver<TParent = any> {
  _key?: LocaleUrlTo_keyResolver<TParent>;
  _type?: LocaleUrlTo_typeResolver<TParent>;
  en?: LocaleUrlToEnResolver<TParent>;
  zhCn?: LocaleUrlToZhCnResolver<TParent>;
}

export interface LocaleUrlTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocaleUrlTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocaleUrlToEnResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocaleUrlToZhCnResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ILocalizationTypeResolver<TParent = any> {
  _createdAt?: LocalizationTo_createdAtResolver<TParent>;
  _id?: LocalizationTo_idResolver<TParent>;
  _key?: LocalizationTo_keyResolver<TParent>;
  _rev?: LocalizationTo_revResolver<TParent>;
  _type?: LocalizationTo_typeResolver<TParent>;
  _updatedAt?: LocalizationTo_updatedAtResolver<TParent>;
  dataArray?: LocalizationToDataArrayResolver<TParent>;
  textCode?: LocalizationToTextCodeResolver<TParent>;
}

export interface LocalizationTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocalizationTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocalizationTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocalizationTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocalizationTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocalizationTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocalizationToDataArrayResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface LocalizationToTextCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IMaintenanceTypeResolver<TParent = any> {
  _key?: MaintenanceTo_keyResolver<TParent>;
  _type?: MaintenanceTo_typeResolver<TParent>;
  isActive?: MaintenanceToIsActiveResolver<TParent>;
  landscapeImage?: MaintenanceToLandscapeImageResolver<TParent>;
  portraitImage?: MaintenanceToPortraitImageResolver<TParent>;
  textMessage?: MaintenanceToTextMessageResolver<TParent>;
}

export interface MaintenanceTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MaintenanceTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MaintenanceToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MaintenanceToLandscapeImageResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MaintenanceToPortraitImageResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MaintenanceToTextMessageResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IMarketTypeResolver<TParent = any> {
  _createdAt?: MarketTo_createdAtResolver<TParent>;
  _id?: MarketTo_idResolver<TParent>;
  _key?: MarketTo_keyResolver<TParent>;
  _rev?: MarketTo_revResolver<TParent>;
  _type?: MarketTo_typeResolver<TParent>;
  _updatedAt?: MarketTo_updatedAtResolver<TParent>;
  code?: MarketToCodeResolver<TParent>;
  isActive?: MarketToIsActiveResolver<TParent>;
  marketIcon?: MarketToMarketIconResolver<TParent>;
  marketName?: MarketToMarketNameResolver<TParent>;
}

export interface MarketTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MarketTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MarketTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MarketTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MarketTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MarketTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MarketToCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MarketToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MarketToMarketIconResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MarketToMarketNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IMediaServicesBlockTypeResolver<TParent = any> {
  _key?: MediaServicesBlockTo_keyResolver<TParent>;
  _type?: MediaServicesBlockTo_typeResolver<TParent>;
  blockCode?: MediaServicesBlockToBlockCodeResolver<TParent>;
  header?: MediaServicesBlockToHeaderResolver<TParent>;
  icon?: MediaServicesBlockToIconResolver<TParent>;
  iconHover?: MediaServicesBlockToIconHoverResolver<TParent>;
  text?: MediaServicesBlockToTextResolver<TParent>;
}

export interface MediaServicesBlockTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MediaServicesBlockTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MediaServicesBlockToBlockCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MediaServicesBlockToHeaderResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MediaServicesBlockToIconResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MediaServicesBlockToIconHoverResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MediaServicesBlockToTextResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IMediaServicesTechTypeResolver<TParent = any> {
  _key?: MediaServicesTechTo_keyResolver<TParent>;
  _type?: MediaServicesTechTo_typeResolver<TParent>;
  blockCode?: MediaServicesTechToBlockCodeResolver<TParent>;
  cardType?: MediaServicesTechToCardTypeResolver<TParent>;
  header?: MediaServicesTechToHeaderResolver<TParent>;
  icon?: MediaServicesTechToIconResolver<TParent>;
  iconHover?: MediaServicesTechToIconHoverResolver<TParent>;
  text?: MediaServicesTechToTextResolver<TParent>;
}

export interface MediaServicesTechTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MediaServicesTechTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MediaServicesTechToBlockCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MediaServicesTechToCardTypeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MediaServicesTechToHeaderResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MediaServicesTechToIconResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MediaServicesTechToIconHoverResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MediaServicesTechToTextResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IMediaTagTypeResolver<TParent = any> {
  _createdAt?: MediaTagTo_createdAtResolver<TParent>;
  _id?: MediaTagTo_idResolver<TParent>;
  _key?: MediaTagTo_keyResolver<TParent>;
  _rev?: MediaTagTo_revResolver<TParent>;
  _type?: MediaTagTo_typeResolver<TParent>;
  _updatedAt?: MediaTagTo_updatedAtResolver<TParent>;
  name?: MediaTagToNameResolver<TParent>;
}

export interface MediaTagTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MediaTagTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MediaTagTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MediaTagTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MediaTagTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MediaTagTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface MediaTagToNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface INewsTypeResolver<TParent = any> {
  _createdAt?: NewsTo_createdAtResolver<TParent>;
  _id?: NewsTo_idResolver<TParent>;
  _key?: NewsTo_keyResolver<TParent>;
  _rev?: NewsTo_revResolver<TParent>;
  _type?: NewsTo_typeResolver<TParent>;
  _updatedAt?: NewsTo_updatedAtResolver<TParent>;
  block?: NewsToBlockResolver<TParent>;
  game?: NewsToGameResolver<TParent>;
  headerPromoVideo?: NewsToHeaderPromoVideoResolver<TParent>;
  highlightedTitle?: NewsToHighlightedTitleResolver<TParent>;
  isActive?: NewsToIsActiveResolver<TParent>;
  newsCategories?: NewsToNewsCategoriesResolver<TParent>;
  newsDate?: NewsToNewsDateResolver<TParent>;
  newsPreviewImage?: NewsToNewsPreviewImageResolver<TParent>;
  newsType?: NewsToNewsTypeResolver<TParent>;
  region?: NewsToRegionResolver<TParent>;
  text?: NewsToTextResolver<TParent>;
  textType?: NewsToTextTypeResolver<TParent>;
  title?: NewsToTitleResolver<TParent>;
}

export interface NewsTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsToBlockResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsToGameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsToHeaderPromoVideoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsToHighlightedTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsToNewsCategoriesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsToNewsDateResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsToNewsPreviewImageResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsToNewsTypeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsToRegionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsToTextResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsToTextTypeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsToTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface INewsCategoryTypeResolver<TParent = any> {
  _createdAt?: NewsCategoryTo_createdAtResolver<TParent>;
  _id?: NewsCategoryTo_idResolver<TParent>;
  _key?: NewsCategoryTo_keyResolver<TParent>;
  _rev?: NewsCategoryTo_revResolver<TParent>;
  _type?: NewsCategoryTo_typeResolver<TParent>;
  _updatedAt?: NewsCategoryTo_updatedAtResolver<TParent>;
  newsCategoryCode?: NewsCategoryToNewsCategoryCodeResolver<TParent>;
  newsCategoryTitle?: NewsCategoryToNewsCategoryTitleResolver<TParent>;
}

export interface NewsCategoryTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsCategoryTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsCategoryTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsCategoryTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsCategoryTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsCategoryTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsCategoryToNewsCategoryCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface NewsCategoryToNewsCategoryTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IOfficeTypeResolver<TParent = any> {
  _createdAt?: OfficeTo_createdAtResolver<TParent>;
  _id?: OfficeTo_idResolver<TParent>;
  _key?: OfficeTo_keyResolver<TParent>;
  _rev?: OfficeTo_revResolver<TParent>;
  _type?: OfficeTo_typeResolver<TParent>;
  _updatedAt?: OfficeTo_updatedAtResolver<TParent>;
  address?: OfficeToAddressResolver<TParent>;
  code?: OfficeToCodeResolver<TParent>;
  companyName?: OfficeToCompanyNameResolver<TParent>;
  country?: OfficeToCountryResolver<TParent>;
  email?: OfficeToEmailResolver<TParent>;
  isActive?: OfficeToIsActiveResolver<TParent>;
  largePhotos?: OfficeToLargePhotosResolver<TParent>;
  leftOnMap?: OfficeToLeftOnMapResolver<TParent>;
  officeName?: OfficeToOfficeNameResolver<TParent>;
  officePhoto?: OfficeToOfficePhotoResolver<TParent>;
  phone1?: OfficeToPhone1Resolver<TParent>;
  phone2?: OfficeToPhone2Resolver<TParent>;
  topOnMap?: OfficeToTopOnMapResolver<TParent>;
}

export interface OfficeTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface OfficeTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface OfficeTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface OfficeTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface OfficeTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface OfficeTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface OfficeToAddressResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface OfficeToCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface OfficeToCompanyNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface OfficeToCountryResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface OfficeToEmailResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface OfficeToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface OfficeToLargePhotosResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface OfficeToLeftOnMapResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface OfficeToOfficeNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface OfficeToOfficePhotoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface OfficeToPhone1Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface OfficeToPhone2Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface OfficeToTopOnMapResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IPageAboutUsTypeResolver<TParent = any> {
  _createdAt?: PageAboutUsTo_createdAtResolver<TParent>;
  _id?: PageAboutUsTo_idResolver<TParent>;
  _key?: PageAboutUsTo_keyResolver<TParent>;
  _rev?: PageAboutUsTo_revResolver<TParent>;
  _type?: PageAboutUsTo_typeResolver<TParent>;
  _updatedAt?: PageAboutUsTo_updatedAtResolver<TParent>;
  canDeploy?: PageAboutUsToCanDeployResolver<TParent>;
  employees?: PageAboutUsToEmployeesResolver<TParent>;
  headerPromoVideo?: PageAboutUsToHeaderPromoVideoResolver<TParent>;
  headerText?: PageAboutUsToHeaderTextResolver<TParent>;
  isActive?: PageAboutUsToIsActiveResolver<TParent>;
  licenses?: PageAboutUsToLicensesResolver<TParent>;
  offices?: PageAboutUsToOfficesResolver<TParent>;
  pageTitle?: PageAboutUsToPageTitleResolver<TParent>;
}

export interface PageAboutUsTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageAboutUsTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageAboutUsTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageAboutUsTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageAboutUsTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageAboutUsTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageAboutUsToCanDeployResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageAboutUsToEmployeesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageAboutUsToHeaderPromoVideoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageAboutUsToHeaderTextResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageAboutUsToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageAboutUsToLicensesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageAboutUsToOfficesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageAboutUsToPageTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IPageGameSearchTypeResolver<TParent = any> {
  _createdAt?: PageGameSearchTo_createdAtResolver<TParent>;
  _id?: PageGameSearchTo_idResolver<TParent>;
  _key?: PageGameSearchTo_keyResolver<TParent>;
  _rev?: PageGameSearchTo_revResolver<TParent>;
  _type?: PageGameSearchTo_typeResolver<TParent>;
  _updatedAt?: PageGameSearchTo_updatedAtResolver<TParent>;
  banners?: PageGameSearchToBannersResolver<TParent>;
  bestGames?: PageGameSearchToBestGamesResolver<TParent>;
  pageTitle?: PageGameSearchToPageTitleResolver<TParent>;
}

export interface PageGameSearchTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGameSearchTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGameSearchTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGameSearchTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGameSearchTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGameSearchTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGameSearchToBannersResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGameSearchToBestGamesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGameSearchToPageTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IPageGamesTypeResolver<TParent = any> {
  _createdAt?: PageGamesTo_createdAtResolver<TParent>;
  _id?: PageGamesTo_idResolver<TParent>;
  _key?: PageGamesTo_keyResolver<TParent>;
  _rev?: PageGamesTo_revResolver<TParent>;
  _type?: PageGamesTo_typeResolver<TParent>;
  _updatedAt?: PageGamesTo_updatedAtResolver<TParent>;
  canDeploy?: PageGamesToCanDeployResolver<TParent>;
  gamePlaylist?: PageGamesToGamePlaylistResolver<TParent>;
  gamesBranded?: PageGamesToGamesBrandedResolver<TParent>;
  gamesCategories?: PageGamesToGamesCategoriesResolver<TParent>;
  headerPromoVideo?: PageGamesToHeaderPromoVideoResolver<TParent>;
  headerText?: PageGamesToHeaderTextResolver<TParent>;
  isActive?: PageGamesToIsActiveResolver<TParent>;
  pageTitle?: PageGamesToPageTitleResolver<TParent>;
}

export interface PageGamesTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamesTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamesTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamesTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamesTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamesTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamesToCanDeployResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamesToGamePlaylistResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamesToGamesBrandedResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamesToGamesCategoriesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamesToHeaderPromoVideoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamesToHeaderTextResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamesToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamesToPageTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IPageGamingSolutionsTypeResolver<TParent = any> {
  _createdAt?: PageGamingSolutionsTo_createdAtResolver<TParent>;
  _id?: PageGamingSolutionsTo_idResolver<TParent>;
  _key?: PageGamingSolutionsTo_keyResolver<TParent>;
  _rev?: PageGamingSolutionsTo_revResolver<TParent>;
  _type?: PageGamingSolutionsTo_typeResolver<TParent>;
  _updatedAt?: PageGamingSolutionsTo_updatedAtResolver<TParent>;
  canDeploy?: PageGamingSolutionsToCanDeployResolver<TParent>;
  cloudArray?: PageGamingSolutionsToCloudArrayResolver<TParent>;
  dataArray?: PageGamingSolutionsToDataArrayResolver<TParent>;
  headerText?: PageGamingSolutionsToHeaderTextResolver<TParent>;
  infraArray?: PageGamingSolutionsToInfraArrayResolver<TParent>;
  isActive?: PageGamingSolutionsToIsActiveResolver<TParent>;
  mobileArray?: PageGamingSolutionsToMobileArrayResolver<TParent>;
  pageTitle?: PageGamingSolutionsToPageTitleResolver<TParent>;
}

export interface PageGamingSolutionsTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamingSolutionsTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamingSolutionsTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamingSolutionsTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamingSolutionsTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamingSolutionsTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamingSolutionsToCanDeployResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamingSolutionsToCloudArrayResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamingSolutionsToDataArrayResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamingSolutionsToHeaderTextResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamingSolutionsToInfraArrayResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamingSolutionsToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamingSolutionsToMobileArrayResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageGamingSolutionsToPageTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IPageHomeTypeResolver<TParent = any> {
  _createdAt?: PageHomeTo_createdAtResolver<TParent>;
  _id?: PageHomeTo_idResolver<TParent>;
  _key?: PageHomeTo_keyResolver<TParent>;
  _rev?: PageHomeTo_revResolver<TParent>;
  _type?: PageHomeTo_typeResolver<TParent>;
  _updatedAt?: PageHomeTo_updatedAtResolver<TParent>;
  canDeploy?: PageHomeToCanDeployResolver<TParent>;
  headerPromoVideo?: PageHomeToHeaderPromoVideoResolver<TParent>;
  headerText?: PageHomeToHeaderTextResolver<TParent>;
  isActive?: PageHomeToIsActiveResolver<TParent>;
  pageTitle?: PageHomeToPageTitleResolver<TParent>;
  partners?: PageHomeToPartnersResolver<TParent>;
  topGame1?: PageHomeToTopGame1Resolver<TParent>;
  topGame2?: PageHomeToTopGame2Resolver<TParent>;
  topGame3?: PageHomeToTopGame3Resolver<TParent>;
  topGame4?: PageHomeToTopGame4Resolver<TParent>;
}

export interface PageHomeTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageHomeTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageHomeTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageHomeTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageHomeTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageHomeTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageHomeToCanDeployResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageHomeToHeaderPromoVideoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageHomeToHeaderTextResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageHomeToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageHomeToPageTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageHomeToPartnersResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageHomeToTopGame1Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageHomeToTopGame2Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageHomeToTopGame3Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageHomeToTopGame4Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IPageLiveCasinoTypeResolver<TParent = any> {
  _createdAt?: PageLiveCasinoTo_createdAtResolver<TParent>;
  _id?: PageLiveCasinoTo_idResolver<TParent>;
  _key?: PageLiveCasinoTo_keyResolver<TParent>;
  _rev?: PageLiveCasinoTo_revResolver<TParent>;
  _type?: PageLiveCasinoTo_typeResolver<TParent>;
  _updatedAt?: PageLiveCasinoTo_updatedAtResolver<TParent>;
  canDeploy?: PageLiveCasinoToCanDeployResolver<TParent>;
  dataArray?: PageLiveCasinoToDataArrayResolver<TParent>;
  headerPromoVideo?: PageLiveCasinoToHeaderPromoVideoResolver<TParent>;
  headerText?: PageLiveCasinoToHeaderTextResolver<TParent>;
  infoArray?: PageLiveCasinoToInfoArrayResolver<TParent>;
  isActive?: PageLiveCasinoToIsActiveResolver<TParent>;
  liveGames?: PageLiveCasinoToLiveGamesResolver<TParent>;
  pageTitle?: PageLiveCasinoToPageTitleResolver<TParent>;
  studios?: PageLiveCasinoToStudiosResolver<TParent>;
}

export interface PageLiveCasinoTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageLiveCasinoTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageLiveCasinoTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageLiveCasinoTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageLiveCasinoTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageLiveCasinoTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageLiveCasinoToCanDeployResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageLiveCasinoToDataArrayResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageLiveCasinoToHeaderPromoVideoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageLiveCasinoToHeaderTextResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageLiveCasinoToInfoArrayResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageLiveCasinoToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageLiveCasinoToLiveGamesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageLiveCasinoToPageTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageLiveCasinoToStudiosResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IPageMediaServicesTypeResolver<TParent = any> {
  _createdAt?: PageMediaServicesTo_createdAtResolver<TParent>;
  _id?: PageMediaServicesTo_idResolver<TParent>;
  _key?: PageMediaServicesTo_keyResolver<TParent>;
  _rev?: PageMediaServicesTo_revResolver<TParent>;
  _type?: PageMediaServicesTo_typeResolver<TParent>;
  _updatedAt?: PageMediaServicesTo_updatedAtResolver<TParent>;
  canDeploy?: PageMediaServicesToCanDeployResolver<TParent>;
  dataArray?: PageMediaServicesToDataArrayResolver<TParent>;
  engagementSliders?: PageMediaServicesToEngagementSlidersResolver<TParent>;
  headerText?: PageMediaServicesToHeaderTextResolver<TParent>;
  isActive?: PageMediaServicesToIsActiveResolver<TParent>;
  pageTitle?: PageMediaServicesToPageTitleResolver<TParent>;
  techArray?: PageMediaServicesToTechArrayResolver<TParent>;
}

export interface PageMediaServicesTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageMediaServicesTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageMediaServicesTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageMediaServicesTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageMediaServicesTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageMediaServicesTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageMediaServicesToCanDeployResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageMediaServicesToDataArrayResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageMediaServicesToEngagementSlidersResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageMediaServicesToHeaderTextResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageMediaServicesToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageMediaServicesToPageTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageMediaServicesToTechArrayResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IPageNewsTypeResolver<TParent = any> {
  _createdAt?: PageNewsTo_createdAtResolver<TParent>;
  _id?: PageNewsTo_idResolver<TParent>;
  _key?: PageNewsTo_keyResolver<TParent>;
  _rev?: PageNewsTo_revResolver<TParent>;
  _type?: PageNewsTo_typeResolver<TParent>;
  _updatedAt?: PageNewsTo_updatedAtResolver<TParent>;
  banners?: PageNewsToBannersResolver<TParent>;
  canDeploy?: PageNewsToCanDeployResolver<TParent>;
  headerPromoVideo?: PageNewsToHeaderPromoVideoResolver<TParent>;
  headerText?: PageNewsToHeaderTextResolver<TParent>;
  isActive?: PageNewsToIsActiveResolver<TParent>;
  newsCategories?: PageNewsToNewsCategoriesResolver<TParent>;
  pageTitle?: PageNewsToPageTitleResolver<TParent>;
}

export interface PageNewsTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageNewsTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageNewsTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageNewsTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageNewsTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageNewsTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageNewsToBannersResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageNewsToCanDeployResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageNewsToHeaderPromoVideoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageNewsToHeaderTextResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageNewsToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageNewsToNewsCategoriesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageNewsToPageTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IPageRoadmapTypeResolver<TParent = any> {
  _createdAt?: PageRoadmapTo_createdAtResolver<TParent>;
  _id?: PageRoadmapTo_idResolver<TParent>;
  _key?: PageRoadmapTo_keyResolver<TParent>;
  _rev?: PageRoadmapTo_revResolver<TParent>;
  _type?: PageRoadmapTo_typeResolver<TParent>;
  _updatedAt?: PageRoadmapTo_updatedAtResolver<TParent>;
  availableMarkets?: PageRoadmapToAvailableMarketsResolver<TParent>;
  banners?: PageRoadmapToBannersResolver<TParent>;
  biggestMarkets?: PageRoadmapToBiggestMarketsResolver<TParent>;
  defaultMarket?: PageRoadmapToDefaultMarketResolver<TParent>;
  latestReleasesMonth?: PageRoadmapToLatestReleasesMonthResolver<TParent>;
  pageTitle?: PageRoadmapToPageTitleResolver<TParent>;
  roadmapGame?: PageRoadmapToRoadmapGameResolver<TParent>;
  showPerMonthToolbar?: PageRoadmapToShowPerMonthToolbarResolver<TParent>;
  upcomingMonthThirdParty?: PageRoadmapToUpcomingMonthThirdPartyResolver<TParent>;
}

export interface PageRoadmapTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageRoadmapTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageRoadmapTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageRoadmapTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageRoadmapTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageRoadmapTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageRoadmapToAvailableMarketsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageRoadmapToBannersResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageRoadmapToBiggestMarketsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageRoadmapToDefaultMarketResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageRoadmapToLatestReleasesMonthResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageRoadmapToPageTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageRoadmapToRoadmapGameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageRoadmapToShowPerMonthToolbarResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageRoadmapToUpcomingMonthThirdPartyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IPageToolsBonusCoinTypeResolver<TParent = any> {
  _createdAt?: PageToolsBonusCoinTo_createdAtResolver<TParent>;
  _id?: PageToolsBonusCoinTo_idResolver<TParent>;
  _key?: PageToolsBonusCoinTo_keyResolver<TParent>;
  _rev?: PageToolsBonusCoinTo_revResolver<TParent>;
  _type?: PageToolsBonusCoinTo_typeResolver<TParent>;
  _updatedAt?: PageToolsBonusCoinTo_updatedAtResolver<TParent>;
  canDeploy?: PageToolsBonusCoinToCanDeployResolver<TParent>;
  engagementSliders?: PageToolsBonusCoinToEngagementSlidersResolver<TParent>;
  headerPromoVideo?: PageToolsBonusCoinToHeaderPromoVideoResolver<TParent>;
  headerText?: PageToolsBonusCoinToHeaderTextResolver<TParent>;
  isActive?: PageToolsBonusCoinToIsActiveResolver<TParent>;
  pageTitle?: PageToolsBonusCoinToPageTitleResolver<TParent>;
}

export interface PageToolsBonusCoinTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsBonusCoinTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsBonusCoinTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsBonusCoinTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsBonusCoinTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsBonusCoinTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsBonusCoinToCanDeployResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsBonusCoinToEngagementSlidersResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsBonusCoinToHeaderPromoVideoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsBonusCoinToHeaderTextResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsBonusCoinToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsBonusCoinToPageTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IPageToolsGRCTypeResolver<TParent = any> {
  _createdAt?: PageToolsGRCTo_createdAtResolver<TParent>;
  _id?: PageToolsGRCTo_idResolver<TParent>;
  _key?: PageToolsGRCTo_keyResolver<TParent>;
  _rev?: PageToolsGRCTo_revResolver<TParent>;
  _type?: PageToolsGRCTo_typeResolver<TParent>;
  _updatedAt?: PageToolsGRCTo_updatedAtResolver<TParent>;
  canDeploy?: PageToolsGRCToCanDeployResolver<TParent>;
  challenges?: PageToolsGRCToChallengesResolver<TParent>;
  grcGames?: PageToolsGRCToGrcGamesResolver<TParent>;
  headerText?: PageToolsGRCToHeaderTextResolver<TParent>;
  isActive?: PageToolsGRCToIsActiveResolver<TParent>;
  jackpotsInfo?: PageToolsGRCToJackpotsInfoResolver<TParent>;
  pageTitle?: PageToolsGRCToPageTitleResolver<TParent>;
  piggyRewardsSliders?: PageToolsGRCToPiggyRewardsSlidersResolver<TParent>;
  tournamentsJackpot?: PageToolsGRCToTournamentsJackpotResolver<TParent>;
}

export interface PageToolsGRCTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsGRCTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsGRCTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsGRCTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsGRCTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsGRCTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsGRCToCanDeployResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsGRCToChallengesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsGRCToGrcGamesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsGRCToHeaderTextResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsGRCToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsGRCToJackpotsInfoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsGRCToPageTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsGRCToPiggyRewardsSlidersResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsGRCToTournamentsJackpotResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IPageToolsJackpotTypeResolver<TParent = any> {
  _createdAt?: PageToolsJackpotTo_createdAtResolver<TParent>;
  _id?: PageToolsJackpotTo_idResolver<TParent>;
  _key?: PageToolsJackpotTo_keyResolver<TParent>;
  _rev?: PageToolsJackpotTo_revResolver<TParent>;
  _type?: PageToolsJackpotTo_typeResolver<TParent>;
  _updatedAt?: PageToolsJackpotTo_updatedAtResolver<TParent>;
  canDeploy?: PageToolsJackpotToCanDeployResolver<TParent>;
  engagementSliders?: PageToolsJackpotToEngagementSlidersResolver<TParent>;
  headerText?: PageToolsJackpotToHeaderTextResolver<TParent>;
  isActive?: PageToolsJackpotToIsActiveResolver<TParent>;
  jackpotsInfo?: PageToolsJackpotToJackpotsInfoResolver<TParent>;
  pageTitle?: PageToolsJackpotToPageTitleResolver<TParent>;
}

export interface PageToolsJackpotTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsJackpotTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsJackpotTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsJackpotTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsJackpotTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsJackpotTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsJackpotToCanDeployResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsJackpotToEngagementSlidersResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsJackpotToHeaderTextResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsJackpotToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsJackpotToJackpotsInfoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsJackpotToPageTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IPageToolsTournamentTypeResolver<TParent = any> {
  _createdAt?: PageToolsTournamentTo_createdAtResolver<TParent>;
  _id?: PageToolsTournamentTo_idResolver<TParent>;
  _key?: PageToolsTournamentTo_keyResolver<TParent>;
  _rev?: PageToolsTournamentTo_revResolver<TParent>;
  _type?: PageToolsTournamentTo_typeResolver<TParent>;
  _updatedAt?: PageToolsTournamentTo_updatedAtResolver<TParent>;
  canDeploy?: PageToolsTournamentToCanDeployResolver<TParent>;
  engagementSliders?: PageToolsTournamentToEngagementSlidersResolver<TParent>;
  headerText?: PageToolsTournamentToHeaderTextResolver<TParent>;
  isActive?: PageToolsTournamentToIsActiveResolver<TParent>;
  pageTitle?: PageToolsTournamentToPageTitleResolver<TParent>;
}

export interface PageToolsTournamentTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsTournamentTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsTournamentTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsTournamentTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsTournamentTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsTournamentTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsTournamentToCanDeployResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsTournamentToEngagementSlidersResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsTournamentToHeaderTextResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsTournamentToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageToolsTournamentToPageTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IPageTournamentsTypeResolver<TParent = any> {
  _createdAt?: PageTournamentsTo_createdAtResolver<TParent>;
  _id?: PageTournamentsTo_idResolver<TParent>;
  _key?: PageTournamentsTo_keyResolver<TParent>;
  _rev?: PageTournamentsTo_revResolver<TParent>;
  _type?: PageTournamentsTo_typeResolver<TParent>;
  _updatedAt?: PageTournamentsTo_updatedAtResolver<TParent>;
  maintenance?: PageTournamentsToMaintenanceResolver<TParent>;
  pageTitle?: PageTournamentsToPageTitleResolver<TParent>;
}

export interface PageTournamentsTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageTournamentsTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageTournamentsTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageTournamentsTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageTournamentsTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageTournamentsTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageTournamentsToMaintenanceResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PageTournamentsToPageTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IPartnerTypeResolver<TParent = any> {
  _createdAt?: PartnerTo_createdAtResolver<TParent>;
  _id?: PartnerTo_idResolver<TParent>;
  _key?: PartnerTo_keyResolver<TParent>;
  _rev?: PartnerTo_revResolver<TParent>;
  _type?: PartnerTo_typeResolver<TParent>;
  _updatedAt?: PartnerTo_updatedAtResolver<TParent>;
  isActive?: PartnerToIsActiveResolver<TParent>;
  partnerCode?: PartnerToPartnerCodeResolver<TParent>;
  partnerImage?: PartnerToPartnerImageResolver<TParent>;
  partnerName?: PartnerToPartnerNameResolver<TParent>;
  partnerUrl?: PartnerToPartnerUrlResolver<TParent>;
}

export interface PartnerTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PartnerTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PartnerTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PartnerTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PartnerTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PartnerTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PartnerToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PartnerToPartnerCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PartnerToPartnerImageResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PartnerToPartnerNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PartnerToPartnerUrlResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IPhoneTypeResolver<TParent = any> {
  _createdAt?: PhoneTo_createdAtResolver<TParent>;
  _id?: PhoneTo_idResolver<TParent>;
  _key?: PhoneTo_keyResolver<TParent>;
  _rev?: PhoneTo_revResolver<TParent>;
  _type?: PhoneTo_typeResolver<TParent>;
  _updatedAt?: PhoneTo_updatedAtResolver<TParent>;
  code?: PhoneToCodeResolver<TParent>;
  isActive?: PhoneToIsActiveResolver<TParent>;
  phoneNumber?: PhoneToPhoneNumberResolver<TParent>;
  phoneTitle?: PhoneToPhoneTitleResolver<TParent>;
}

export interface PhoneTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PhoneTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PhoneTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PhoneTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PhoneTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PhoneTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PhoneToCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PhoneToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PhoneToPhoneNumberResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface PhoneToPhoneTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IProviderTypeResolver<TParent = any> {
  _createdAt?: ProviderTo_createdAtResolver<TParent>;
  _id?: ProviderTo_idResolver<TParent>;
  _key?: ProviderTo_keyResolver<TParent>;
  _rev?: ProviderTo_revResolver<TParent>;
  _type?: ProviderTo_typeResolver<TParent>;
  _updatedAt?: ProviderTo_updatedAtResolver<TParent>;
  providerCode?: ProviderToProviderCodeResolver<TParent>;
  providerName?: ProviderToProviderNameResolver<TParent>;
}

export interface ProviderTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ProviderTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ProviderTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ProviderTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ProviderTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ProviderTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ProviderToProviderCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ProviderToProviderNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IRegionTypeResolver<TParent = any> {
  _createdAt?: RegionTo_createdAtResolver<TParent>;
  _id?: RegionTo_idResolver<TParent>;
  _key?: RegionTo_keyResolver<TParent>;
  _rev?: RegionTo_revResolver<TParent>;
  _type?: RegionTo_typeResolver<TParent>;
  _updatedAt?: RegionTo_updatedAtResolver<TParent>;
  regionCode?: RegionToRegionCodeResolver<TParent>;
}

export interface RegionTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RegionTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RegionTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RegionTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RegionTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RegionTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RegionToRegionCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IRgbaColorTypeResolver<TParent = any> {
  _key?: RgbaColorTo_keyResolver<TParent>;
  _type?: RgbaColorTo_typeResolver<TParent>;
  a?: RgbaColorToAResolver<TParent>;
  b?: RgbaColorToBResolver<TParent>;
  g?: RgbaColorToGResolver<TParent>;
  r?: RgbaColorToRResolver<TParent>;
}

export interface RgbaColorTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RgbaColorTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RgbaColorToAResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RgbaColorToBResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RgbaColorToGResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RgbaColorToRResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IRoadmapGameTypeResolver<TParent = any> {
  _createdAt?: RoadmapGameTo_createdAtResolver<TParent>;
  _id?: RoadmapGameTo_idResolver<TParent>;
  _key?: RoadmapGameTo_keyResolver<TParent>;
  _rev?: RoadmapGameTo_revResolver<TParent>;
  _type?: RoadmapGameTo_typeResolver<TParent>;
  _updatedAt?: RoadmapGameTo_updatedAtResolver<TParent>;
  comingSoon?: RoadmapGameToComingSoonResolver<TParent>;
  game?: RoadmapGameToGameResolver<TParent>;
  hideInAllMarkets?: RoadmapGameToHideInAllMarketsResolver<TParent>;
  roadmapGameMarket?: RoadmapGameToRoadmapGameMarketResolver<TParent>;
}

export interface RoadmapGameTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RoadmapGameTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RoadmapGameTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RoadmapGameTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RoadmapGameTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RoadmapGameTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RoadmapGameToComingSoonResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RoadmapGameToGameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RoadmapGameToHideInAllMarketsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RoadmapGameToRoadmapGameMarketResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IRoadmapGameMarketTypeResolver<TParent = any> {
  _createdAt?: RoadmapGameMarketTo_createdAtResolver<TParent>;
  _id?: RoadmapGameMarketTo_idResolver<TParent>;
  _key?: RoadmapGameMarketTo_keyResolver<TParent>;
  _rev?: RoadmapGameMarketTo_revResolver<TParent>;
  _type?: RoadmapGameMarketTo_typeResolver<TParent>;
  _updatedAt?: RoadmapGameMarketTo_updatedAtResolver<TParent>;
  endDate?: RoadmapGameMarketToEndDateResolver<TParent>;
  gameMarket?: RoadmapGameMarketToGameMarketResolver<TParent>;
  startDate?: RoadmapGameMarketToStartDateResolver<TParent>;
}

export interface RoadmapGameMarketTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RoadmapGameMarketTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RoadmapGameMarketTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RoadmapGameMarketTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RoadmapGameMarketTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RoadmapGameMarketTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RoadmapGameMarketToEndDateResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RoadmapGameMarketToGameMarketResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RoadmapGameMarketToStartDateResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IRootQueryTypeResolver<TParent = any> {
  Banner?: RootQueryToBannerResolver<TParent>;
  Country?: RootQueryToCountryResolver<TParent>;
  Currency?: RootQueryToCurrencyResolver<TParent>;
  Document?: RootQueryToDocumentResolver<TParent>;
  EmailAddress?: RootQueryToEmailAddressResolver<TParent>;
  Employee?: RootQueryToEmployeeResolver<TParent>;
  EngagementSlide?: RootQueryToEngagementSlideResolver<TParent>;
  Game?: RootQueryToGameResolver<TParent>;
  GameCategory?: RootQueryToGameCategoryResolver<TParent>;
  GameCertificate?: RootQueryToGameCertificateResolver<TParent>;
  GameCode?: RootQueryToGameCodeResolver<TParent>;
  GamePlaylist?: RootQueryToGamePlaylistResolver<TParent>;
  GameRTP?: RootQueryToGameRTPResolver<TParent>;
  GameRibbon?: RootQueryToGameRibbonResolver<TParent>;
  GameType?: RootQueryToGameTypeResolver<TParent>;
  JackpotInfo?: RootQueryToJackpotInfoResolver<TParent>;
  Language?: RootQueryToLanguageResolver<TParent>;
  License?: RootQueryToLicenseResolver<TParent>;
  Link?: RootQueryToLinkResolver<TParent>;
  Localization?: RootQueryToLocalizationResolver<TParent>;
  Market?: RootQueryToMarketResolver<TParent>;
  MediaTag?: RootQueryToMediaTagResolver<TParent>;
  News?: RootQueryToNewsResolver<TParent>;
  NewsCategory?: RootQueryToNewsCategoryResolver<TParent>;
  Office?: RootQueryToOfficeResolver<TParent>;
  PageAboutUs?: RootQueryToPageAboutUsResolver<TParent>;
  PageGameSearch?: RootQueryToPageGameSearchResolver<TParent>;
  PageGames?: RootQueryToPageGamesResolver<TParent>;
  PageGamingSolutions?: RootQueryToPageGamingSolutionsResolver<TParent>;
  PageHome?: RootQueryToPageHomeResolver<TParent>;
  PageLiveCasino?: RootQueryToPageLiveCasinoResolver<TParent>;
  PageMediaServices?: RootQueryToPageMediaServicesResolver<TParent>;
  PageNews?: RootQueryToPageNewsResolver<TParent>;
  PageRoadmap?: RootQueryToPageRoadmapResolver<TParent>;
  PageToolsBonusCoin?: RootQueryToPageToolsBonusCoinResolver<TParent>;
  PageToolsGRC?: RootQueryToPageToolsGRCResolver<TParent>;
  PageToolsJackpot?: RootQueryToPageToolsJackpotResolver<TParent>;
  PageToolsTournament?: RootQueryToPageToolsTournamentResolver<TParent>;
  PageTournaments?: RootQueryToPageTournamentsResolver<TParent>;
  Partner?: RootQueryToPartnerResolver<TParent>;
  Phone?: RootQueryToPhoneResolver<TParent>;
  Provider?: RootQueryToProviderResolver<TParent>;
  Region?: RootQueryToRegionResolver<TParent>;
  RoadmapGame?: RootQueryToRoadmapGameResolver<TParent>;
  RoadmapGameMarket?: RootQueryToRoadmapGameMarketResolver<TParent>;
  SanityFileAsset?: RootQueryToSanityFileAssetResolver<TParent>;
  SanityImageAsset?: RootQueryToSanityImageAssetResolver<TParent>;
  SectionGroupSliderGames?: RootQueryToSectionGroupSliderGamesResolver<TParent>;
  SectionSliderGames?: RootQueryToSectionSliderGamesResolver<TParent>;
  SiteConfig?: RootQueryToSiteConfigResolver<TParent>;
  SitePage?: RootQueryToSitePageResolver<TParent>;
  SocialNetwork?: RootQueryToSocialNetworkResolver<TParent>;
  Studio?: RootQueryToStudioResolver<TParent>;
  Tournament?: RootQueryToTournamentResolver<TParent>;
  WebsiteConfig?: RootQueryToWebsiteConfigResolver<TParent>;
  WidgetPartners?: RootQueryToWidgetPartnersResolver<TParent>;
  WidgetSwiper?: RootQueryToWidgetSwiperResolver<TParent>;
  allBanner?: RootQueryToAllBannerResolver<TParent>;
  allCountry?: RootQueryToAllCountryResolver<TParent>;
  allCurrency?: RootQueryToAllCurrencyResolver<TParent>;
  allDocument?: RootQueryToAllDocumentResolver<TParent>;
  allEmailAddress?: RootQueryToAllEmailAddressResolver<TParent>;
  allEmployee?: RootQueryToAllEmployeeResolver<TParent>;
  allEngagementSlide?: RootQueryToAllEngagementSlideResolver<TParent>;
  allGame?: RootQueryToAllGameResolver<TParent>;
  allGameCategory?: RootQueryToAllGameCategoryResolver<TParent>;
  allGameCertificate?: RootQueryToAllGameCertificateResolver<TParent>;
  allGameCode?: RootQueryToAllGameCodeResolver<TParent>;
  allGamePlaylist?: RootQueryToAllGamePlaylistResolver<TParent>;
  allGameRTP?: RootQueryToAllGameRTPResolver<TParent>;
  allGameRibbon?: RootQueryToAllGameRibbonResolver<TParent>;
  allGameType?: RootQueryToAllGameTypeResolver<TParent>;
  allJackpotInfo?: RootQueryToAllJackpotInfoResolver<TParent>;
  allLanguage?: RootQueryToAllLanguageResolver<TParent>;
  allLicense?: RootQueryToAllLicenseResolver<TParent>;
  allLink?: RootQueryToAllLinkResolver<TParent>;
  allLocalization?: RootQueryToAllLocalizationResolver<TParent>;
  allMarket?: RootQueryToAllMarketResolver<TParent>;
  allMediaTag?: RootQueryToAllMediaTagResolver<TParent>;
  allNews?: RootQueryToAllNewsResolver<TParent>;
  allNewsCategory?: RootQueryToAllNewsCategoryResolver<TParent>;
  allOffice?: RootQueryToAllOfficeResolver<TParent>;
  allPageAboutUs?: RootQueryToAllPageAboutUsResolver<TParent>;
  allPageGameSearch?: RootQueryToAllPageGameSearchResolver<TParent>;
  allPageGames?: RootQueryToAllPageGamesResolver<TParent>;
  allPageGamingSolutions?: RootQueryToAllPageGamingSolutionsResolver<TParent>;
  allPageHome?: RootQueryToAllPageHomeResolver<TParent>;
  allPageLiveCasino?: RootQueryToAllPageLiveCasinoResolver<TParent>;
  allPageMediaServices?: RootQueryToAllPageMediaServicesResolver<TParent>;
  allPageNews?: RootQueryToAllPageNewsResolver<TParent>;
  allPageRoadmap?: RootQueryToAllPageRoadmapResolver<TParent>;
  allPageToolsBonusCoin?: RootQueryToAllPageToolsBonusCoinResolver<TParent>;
  allPageToolsGRC?: RootQueryToAllPageToolsGRCResolver<TParent>;
  allPageToolsJackpot?: RootQueryToAllPageToolsJackpotResolver<TParent>;
  allPageToolsTournament?: RootQueryToAllPageToolsTournamentResolver<TParent>;
  allPageTournaments?: RootQueryToAllPageTournamentsResolver<TParent>;
  allPartner?: RootQueryToAllPartnerResolver<TParent>;
  allPhone?: RootQueryToAllPhoneResolver<TParent>;
  allProvider?: RootQueryToAllProviderResolver<TParent>;
  allRegion?: RootQueryToAllRegionResolver<TParent>;
  allRoadmapGame?: RootQueryToAllRoadmapGameResolver<TParent>;
  allRoadmapGameMarket?: RootQueryToAllRoadmapGameMarketResolver<TParent>;
  allSanityFileAsset?: RootQueryToAllSanityFileAssetResolver<TParent>;
  allSanityImageAsset?: RootQueryToAllSanityImageAssetResolver<TParent>;
  allSectionGroupSliderGames?: RootQueryToAllSectionGroupSliderGamesResolver<TParent>;
  allSectionSliderGames?: RootQueryToAllSectionSliderGamesResolver<TParent>;
  allSiteConfig?: RootQueryToAllSiteConfigResolver<TParent>;
  allSitePage?: RootQueryToAllSitePageResolver<TParent>;
  allSocialNetwork?: RootQueryToAllSocialNetworkResolver<TParent>;
  allStudio?: RootQueryToAllStudioResolver<TParent>;
  allTournament?: RootQueryToAllTournamentResolver<TParent>;
  allWebsiteConfig?: RootQueryToAllWebsiteConfigResolver<TParent>;
  allWidgetPartners?: RootQueryToAllWidgetPartnersResolver<TParent>;
  allWidgetSwiper?: RootQueryToAllWidgetSwiperResolver<TParent>;
}

export interface RootQueryToBannerArgs {
  id: string;
}
export interface RootQueryToBannerResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToBannerArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToCountryArgs {
  id: string;
}
export interface RootQueryToCountryResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToCountryArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToCurrencyArgs {
  id: string;
}
export interface RootQueryToCurrencyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToCurrencyArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToDocumentArgs {
  id: string;
}
export interface RootQueryToDocumentResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToDocumentArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToEmailAddressArgs {
  id: string;
}
export interface RootQueryToEmailAddressResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToEmailAddressArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToEmployeeArgs {
  id: string;
}
export interface RootQueryToEmployeeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToEmployeeArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToEngagementSlideArgs {
  id: string;
}
export interface RootQueryToEngagementSlideResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToEngagementSlideArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToGameArgs {
  id: string;
}
export interface RootQueryToGameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToGameArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToGameCategoryArgs {
  id: string;
}
export interface RootQueryToGameCategoryResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToGameCategoryArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToGameCertificateArgs {
  id: string;
}
export interface RootQueryToGameCertificateResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToGameCertificateArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToGameCodeArgs {
  id: string;
}
export interface RootQueryToGameCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToGameCodeArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToGamePlaylistArgs {
  id: string;
}
export interface RootQueryToGamePlaylistResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToGamePlaylistArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToGameRTPArgs {
  id: string;
}
export interface RootQueryToGameRTPResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToGameRTPArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToGameRibbonArgs {
  id: string;
}
export interface RootQueryToGameRibbonResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToGameRibbonArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToGameTypeArgs {
  id: string;
}
export interface RootQueryToGameTypeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToGameTypeArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToJackpotInfoArgs {
  id: string;
}
export interface RootQueryToJackpotInfoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToJackpotInfoArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToLanguageArgs {
  id: string;
}
export interface RootQueryToLanguageResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToLanguageArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToLicenseArgs {
  id: string;
}
export interface RootQueryToLicenseResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToLicenseArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToLinkArgs {
  id: string;
}
export interface RootQueryToLinkResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToLinkArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToLocalizationArgs {
  id: string;
}
export interface RootQueryToLocalizationResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToLocalizationArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToMarketArgs {
  id: string;
}
export interface RootQueryToMarketResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToMarketArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToMediaTagArgs {
  id: string;
}
export interface RootQueryToMediaTagResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToMediaTagArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToNewsArgs {
  id: string;
}
export interface RootQueryToNewsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToNewsArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToNewsCategoryArgs {
  id: string;
}
export interface RootQueryToNewsCategoryResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToNewsCategoryArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToOfficeArgs {
  id: string;
}
export interface RootQueryToOfficeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToOfficeArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToPageAboutUsArgs {
  id: string;
}
export interface RootQueryToPageAboutUsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToPageAboutUsArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToPageGameSearchArgs {
  id: string;
}
export interface RootQueryToPageGameSearchResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToPageGameSearchArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToPageGamesArgs {
  id: string;
}
export interface RootQueryToPageGamesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToPageGamesArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToPageGamingSolutionsArgs {
  id: string;
}
export interface RootQueryToPageGamingSolutionsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToPageGamingSolutionsArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToPageHomeArgs {
  id: string;
}
export interface RootQueryToPageHomeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToPageHomeArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToPageLiveCasinoArgs {
  id: string;
}
export interface RootQueryToPageLiveCasinoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToPageLiveCasinoArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToPageMediaServicesArgs {
  id: string;
}
export interface RootQueryToPageMediaServicesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToPageMediaServicesArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToPageNewsArgs {
  id: string;
}
export interface RootQueryToPageNewsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToPageNewsArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToPageRoadmapArgs {
  id: string;
}
export interface RootQueryToPageRoadmapResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToPageRoadmapArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToPageToolsBonusCoinArgs {
  id: string;
}
export interface RootQueryToPageToolsBonusCoinResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToPageToolsBonusCoinArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToPageToolsGRCArgs {
  id: string;
}
export interface RootQueryToPageToolsGRCResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToPageToolsGRCArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToPageToolsJackpotArgs {
  id: string;
}
export interface RootQueryToPageToolsJackpotResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToPageToolsJackpotArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToPageToolsTournamentArgs {
  id: string;
}
export interface RootQueryToPageToolsTournamentResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToPageToolsTournamentArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToPageTournamentsArgs {
  id: string;
}
export interface RootQueryToPageTournamentsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToPageTournamentsArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToPartnerArgs {
  id: string;
}
export interface RootQueryToPartnerResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToPartnerArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToPhoneArgs {
  id: string;
}
export interface RootQueryToPhoneResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToPhoneArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToProviderArgs {
  id: string;
}
export interface RootQueryToProviderResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToProviderArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToRegionArgs {
  id: string;
}
export interface RootQueryToRegionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToRegionArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToRoadmapGameArgs {
  id: string;
}
export interface RootQueryToRoadmapGameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToRoadmapGameArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToRoadmapGameMarketArgs {
  id: string;
}
export interface RootQueryToRoadmapGameMarketResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToRoadmapGameMarketArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToSanityFileAssetArgs {
  id: string;
}
export interface RootQueryToSanityFileAssetResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToSanityFileAssetArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToSanityImageAssetArgs {
  id: string;
}
export interface RootQueryToSanityImageAssetResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToSanityImageAssetArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToSectionGroupSliderGamesArgs {
  id: string;
}
export interface RootQueryToSectionGroupSliderGamesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToSectionGroupSliderGamesArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToSectionSliderGamesArgs {
  id: string;
}
export interface RootQueryToSectionSliderGamesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToSectionSliderGamesArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToSiteConfigArgs {
  id: string;
}
export interface RootQueryToSiteConfigResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToSiteConfigArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToSitePageArgs {
  id: string;
}
export interface RootQueryToSitePageResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToSitePageArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToSocialNetworkArgs {
  id: string;
}
export interface RootQueryToSocialNetworkResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToSocialNetworkArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToStudioArgs {
  id: string;
}
export interface RootQueryToStudioResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToStudioArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToTournamentArgs {
  id: string;
}
export interface RootQueryToTournamentResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToTournamentArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToWebsiteConfigArgs {
  id: string;
}
export interface RootQueryToWebsiteConfigResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToWebsiteConfigArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToWidgetPartnersArgs {
  id: string;
}
export interface RootQueryToWidgetPartnersResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToWidgetPartnersArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToWidgetSwiperArgs {
  id: string;
}
export interface RootQueryToWidgetSwiperResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToWidgetSwiperArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllBannerArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IBannerSorting>;
  where?: IBannerFilter;
}
export interface RootQueryToAllBannerResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllBannerArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllCountryArgs {
  limit?: number;
  offset?: number;
  sort?: Array<ICountrySorting>;
  where?: ICountryFilter;
}
export interface RootQueryToAllCountryResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllCountryArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllCurrencyArgs {
  limit?: number;
  offset?: number;
  sort?: Array<ICurrencySorting>;
  where?: ICurrencyFilter;
}
export interface RootQueryToAllCurrencyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllCurrencyArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllDocumentArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IDocumentSorting>;
  where?: IDocumentFilter;
}
export interface RootQueryToAllDocumentResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllDocumentArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllEmailAddressArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IEmailAddressSorting>;
  where?: IEmailAddressFilter;
}
export interface RootQueryToAllEmailAddressResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllEmailAddressArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllEmployeeArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IEmployeeSorting>;
  where?: IEmployeeFilter;
}
export interface RootQueryToAllEmployeeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllEmployeeArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllEngagementSlideArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IEngagementSlideSorting>;
  where?: IEngagementSlideFilter;
}
export interface RootQueryToAllEngagementSlideResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllEngagementSlideArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllGameArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IGameSorting>;
  where?: IGameFilter;
}
export interface RootQueryToAllGameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllGameArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllGameCategoryArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IGameCategorySorting>;
  where?: IGameCategoryFilter;
}
export interface RootQueryToAllGameCategoryResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllGameCategoryArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllGameCertificateArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IGameCertificateSorting>;
  where?: IGameCertificateFilter;
}
export interface RootQueryToAllGameCertificateResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllGameCertificateArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllGameCodeArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IGameCodeSorting>;
  where?: IGameCodeFilter;
}
export interface RootQueryToAllGameCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllGameCodeArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllGamePlaylistArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IGamePlaylistSorting>;
  where?: IGamePlaylistFilter;
}
export interface RootQueryToAllGamePlaylistResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllGamePlaylistArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllGameRTPArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IGameRTPSorting>;
  where?: IGameRTPFilter;
}
export interface RootQueryToAllGameRTPResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllGameRTPArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllGameRibbonArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IGameRibbonSorting>;
  where?: IGameRibbonFilter;
}
export interface RootQueryToAllGameRibbonResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllGameRibbonArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllGameTypeArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IGameTypeSorting>;
  where?: IGameTypeFilter;
}
export interface RootQueryToAllGameTypeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllGameTypeArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllJackpotInfoArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IJackpotInfoSorting>;
  where?: IJackpotInfoFilter;
}
export interface RootQueryToAllJackpotInfoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllJackpotInfoArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllLanguageArgs {
  limit?: number;
  offset?: number;
  sort?: Array<ILanguageSorting>;
  where?: ILanguageFilter;
}
export interface RootQueryToAllLanguageResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllLanguageArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllLicenseArgs {
  limit?: number;
  offset?: number;
  sort?: Array<ILicenseSorting>;
  where?: ILicenseFilter;
}
export interface RootQueryToAllLicenseResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllLicenseArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllLinkArgs {
  limit?: number;
  offset?: number;
  sort?: Array<ILinkSorting>;
  where?: ILinkFilter;
}
export interface RootQueryToAllLinkResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllLinkArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllLocalizationArgs {
  limit?: number;
  offset?: number;
  sort?: Array<ILocalizationSorting>;
  where?: ILocalizationFilter;
}
export interface RootQueryToAllLocalizationResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllLocalizationArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllMarketArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IMarketSorting>;
  where?: IMarketFilter;
}
export interface RootQueryToAllMarketResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllMarketArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllMediaTagArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IMediaTagSorting>;
  where?: IMediaTagFilter;
}
export interface RootQueryToAllMediaTagResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllMediaTagArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllNewsArgs {
  limit?: number;
  offset?: number;
  sort?: Array<INewsSorting>;
  where?: INewsFilter;
}
export interface RootQueryToAllNewsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllNewsArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllNewsCategoryArgs {
  limit?: number;
  offset?: number;
  sort?: Array<INewsCategorySorting>;
  where?: INewsCategoryFilter;
}
export interface RootQueryToAllNewsCategoryResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllNewsCategoryArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllOfficeArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IOfficeSorting>;
  where?: IOfficeFilter;
}
export interface RootQueryToAllOfficeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllOfficeArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllPageAboutUsArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IPageAboutUsSorting>;
  where?: IPageAboutUsFilter;
}
export interface RootQueryToAllPageAboutUsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllPageAboutUsArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllPageGameSearchArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IPageGameSearchSorting>;
  where?: IPageGameSearchFilter;
}
export interface RootQueryToAllPageGameSearchResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllPageGameSearchArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllPageGamesArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IPageGamesSorting>;
  where?: IPageGamesFilter;
}
export interface RootQueryToAllPageGamesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllPageGamesArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllPageGamingSolutionsArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IPageGamingSolutionsSorting>;
  where?: IPageGamingSolutionsFilter;
}
export interface RootQueryToAllPageGamingSolutionsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllPageGamingSolutionsArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllPageHomeArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IPageHomeSorting>;
  where?: IPageHomeFilter;
}
export interface RootQueryToAllPageHomeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllPageHomeArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllPageLiveCasinoArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IPageLiveCasinoSorting>;
  where?: IPageLiveCasinoFilter;
}
export interface RootQueryToAllPageLiveCasinoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllPageLiveCasinoArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllPageMediaServicesArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IPageMediaServicesSorting>;
  where?: IPageMediaServicesFilter;
}
export interface RootQueryToAllPageMediaServicesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllPageMediaServicesArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllPageNewsArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IPageNewsSorting>;
  where?: IPageNewsFilter;
}
export interface RootQueryToAllPageNewsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllPageNewsArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllPageRoadmapArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IPageRoadmapSorting>;
  where?: IPageRoadmapFilter;
}
export interface RootQueryToAllPageRoadmapResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllPageRoadmapArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllPageToolsBonusCoinArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IPageToolsBonusCoinSorting>;
  where?: IPageToolsBonusCoinFilter;
}
export interface RootQueryToAllPageToolsBonusCoinResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllPageToolsBonusCoinArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllPageToolsGRCArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IPageToolsGRCSorting>;
  where?: IPageToolsGRCFilter;
}
export interface RootQueryToAllPageToolsGRCResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllPageToolsGRCArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllPageToolsJackpotArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IPageToolsJackpotSorting>;
  where?: IPageToolsJackpotFilter;
}
export interface RootQueryToAllPageToolsJackpotResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllPageToolsJackpotArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllPageToolsTournamentArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IPageToolsTournamentSorting>;
  where?: IPageToolsTournamentFilter;
}
export interface RootQueryToAllPageToolsTournamentResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllPageToolsTournamentArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllPageTournamentsArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IPageTournamentsSorting>;
  where?: IPageTournamentsFilter;
}
export interface RootQueryToAllPageTournamentsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllPageTournamentsArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllPartnerArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IPartnerSorting>;
  where?: IPartnerFilter;
}
export interface RootQueryToAllPartnerResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllPartnerArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllPhoneArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IPhoneSorting>;
  where?: IPhoneFilter;
}
export interface RootQueryToAllPhoneResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllPhoneArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllProviderArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IProviderSorting>;
  where?: IProviderFilter;
}
export interface RootQueryToAllProviderResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllProviderArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllRegionArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IRegionSorting>;
  where?: IRegionFilter;
}
export interface RootQueryToAllRegionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllRegionArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllRoadmapGameArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IRoadmapGameSorting>;
  where?: IRoadmapGameFilter;
}
export interface RootQueryToAllRoadmapGameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllRoadmapGameArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllRoadmapGameMarketArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IRoadmapGameMarketSorting>;
  where?: IRoadmapGameMarketFilter;
}
export interface RootQueryToAllRoadmapGameMarketResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllRoadmapGameMarketArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllSanityFileAssetArgs {
  limit?: number;
  offset?: number;
  sort?: Array<ISanityFileAssetSorting>;
  where?: ISanityFileAssetFilter;
}
export interface RootQueryToAllSanityFileAssetResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllSanityFileAssetArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllSanityImageAssetArgs {
  limit?: number;
  offset?: number;
  sort?: Array<ISanityImageAssetSorting>;
  where?: ISanityImageAssetFilter;
}
export interface RootQueryToAllSanityImageAssetResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllSanityImageAssetArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllSectionGroupSliderGamesArgs {
  limit?: number;
  offset?: number;
  sort?: Array<ISectionGroupSliderGamesSorting>;
  where?: ISectionGroupSliderGamesFilter;
}
export interface RootQueryToAllSectionGroupSliderGamesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllSectionGroupSliderGamesArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllSectionSliderGamesArgs {
  limit?: number;
  offset?: number;
  sort?: Array<ISectionSliderGamesSorting>;
  where?: ISectionSliderGamesFilter;
}
export interface RootQueryToAllSectionSliderGamesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllSectionSliderGamesArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllSiteConfigArgs {
  limit?: number;
  offset?: number;
  sort?: Array<ISiteConfigSorting>;
  where?: ISiteConfigFilter;
}
export interface RootQueryToAllSiteConfigResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllSiteConfigArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllSitePageArgs {
  limit?: number;
  offset?: number;
  sort?: Array<ISitePageSorting>;
  where?: ISitePageFilter;
}
export interface RootQueryToAllSitePageResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllSitePageArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllSocialNetworkArgs {
  limit?: number;
  offset?: number;
  sort?: Array<ISocialNetworkSorting>;
  where?: ISocialNetworkFilter;
}
export interface RootQueryToAllSocialNetworkResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllSocialNetworkArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllStudioArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IStudioSorting>;
  where?: IStudioFilter;
}
export interface RootQueryToAllStudioResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllStudioArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllTournamentArgs {
  limit?: number;
  offset?: number;
  sort?: Array<ITournamentSorting>;
  where?: ITournamentFilter;
}
export interface RootQueryToAllTournamentResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllTournamentArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllWebsiteConfigArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IWebsiteConfigSorting>;
  where?: IWebsiteConfigFilter;
}
export interface RootQueryToAllWebsiteConfigResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllWebsiteConfigArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllWidgetPartnersArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IWidgetPartnersSorting>;
  where?: IWidgetPartnersFilter;
}
export interface RootQueryToAllWidgetPartnersResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllWidgetPartnersArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface RootQueryToAllWidgetSwiperArgs {
  limit?: number;
  offset?: number;
  sort?: Array<IWidgetSwiperSorting>;
  where?: IWidgetSwiperFilter;
}
export interface RootQueryToAllWidgetSwiperResolver<TParent = any, TResult = any> {
  (parent: TParent, args: RootQueryToAllWidgetSwiperArgs, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ISanityAssetSourceDataTypeResolver<TParent = any> {
  _key?: SanityAssetSourceDataTo_keyResolver<TParent>;
  _type?: SanityAssetSourceDataTo_typeResolver<TParent>;
  id?: SanityAssetSourceDataToIdResolver<TParent>;
  name?: SanityAssetSourceDataToNameResolver<TParent>;
  url?: SanityAssetSourceDataToUrlResolver<TParent>;
}

export interface SanityAssetSourceDataTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityAssetSourceDataTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityAssetSourceDataToIdResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityAssetSourceDataToNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityAssetSourceDataToUrlResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ISanityFileAssetTypeResolver<TParent = any> {
  _createdAt?: SanityFileAssetTo_createdAtResolver<TParent>;
  _id?: SanityFileAssetTo_idResolver<TParent>;
  _key?: SanityFileAssetTo_keyResolver<TParent>;
  _rev?: SanityFileAssetTo_revResolver<TParent>;
  _type?: SanityFileAssetTo_typeResolver<TParent>;
  _updatedAt?: SanityFileAssetTo_updatedAtResolver<TParent>;
  altText?: SanityFileAssetToAltTextResolver<TParent>;
  assetId?: SanityFileAssetToAssetIdResolver<TParent>;
  description?: SanityFileAssetToDescriptionResolver<TParent>;
  extension?: SanityFileAssetToExtensionResolver<TParent>;
  label?: SanityFileAssetToLabelResolver<TParent>;
  mimeType?: SanityFileAssetToMimeTypeResolver<TParent>;
  originalFilename?: SanityFileAssetToOriginalFilenameResolver<TParent>;
  path?: SanityFileAssetToPathResolver<TParent>;
  sha1hash?: SanityFileAssetToSha1hashResolver<TParent>;
  size?: SanityFileAssetToSizeResolver<TParent>;
  source?: SanityFileAssetToSourceResolver<TParent>;
  title?: SanityFileAssetToTitleResolver<TParent>;
  url?: SanityFileAssetToUrlResolver<TParent>;
}

export interface SanityFileAssetTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityFileAssetTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityFileAssetTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityFileAssetTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityFileAssetTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityFileAssetTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityFileAssetToAltTextResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityFileAssetToAssetIdResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityFileAssetToDescriptionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityFileAssetToExtensionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityFileAssetToLabelResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityFileAssetToMimeTypeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityFileAssetToOriginalFilenameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityFileAssetToPathResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityFileAssetToSha1hashResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityFileAssetToSizeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityFileAssetToSourceResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityFileAssetToTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityFileAssetToUrlResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ISanityImageAssetTypeResolver<TParent = any> {
  _createdAt?: SanityImageAssetTo_createdAtResolver<TParent>;
  _id?: SanityImageAssetTo_idResolver<TParent>;
  _key?: SanityImageAssetTo_keyResolver<TParent>;
  _rev?: SanityImageAssetTo_revResolver<TParent>;
  _type?: SanityImageAssetTo_typeResolver<TParent>;
  _updatedAt?: SanityImageAssetTo_updatedAtResolver<TParent>;
  altText?: SanityImageAssetToAltTextResolver<TParent>;
  assetId?: SanityImageAssetToAssetIdResolver<TParent>;
  description?: SanityImageAssetToDescriptionResolver<TParent>;
  extension?: SanityImageAssetToExtensionResolver<TParent>;
  label?: SanityImageAssetToLabelResolver<TParent>;
  metadata?: SanityImageAssetToMetadataResolver<TParent>;
  mimeType?: SanityImageAssetToMimeTypeResolver<TParent>;
  originalFilename?: SanityImageAssetToOriginalFilenameResolver<TParent>;
  path?: SanityImageAssetToPathResolver<TParent>;
  sha1hash?: SanityImageAssetToSha1hashResolver<TParent>;
  size?: SanityImageAssetToSizeResolver<TParent>;
  source?: SanityImageAssetToSourceResolver<TParent>;
  title?: SanityImageAssetToTitleResolver<TParent>;
  uploadId?: SanityImageAssetToUploadIdResolver<TParent>;
  url?: SanityImageAssetToUrlResolver<TParent>;
}

export interface SanityImageAssetTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageAssetTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageAssetTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageAssetTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageAssetTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageAssetTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageAssetToAltTextResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageAssetToAssetIdResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageAssetToDescriptionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageAssetToExtensionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageAssetToLabelResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageAssetToMetadataResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageAssetToMimeTypeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageAssetToOriginalFilenameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageAssetToPathResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageAssetToSha1hashResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageAssetToSizeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageAssetToSourceResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageAssetToTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageAssetToUploadIdResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageAssetToUrlResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ISanityImageCropTypeResolver<TParent = any> {
  _key?: SanityImageCropTo_keyResolver<TParent>;
  _type?: SanityImageCropTo_typeResolver<TParent>;
  bottom?: SanityImageCropToBottomResolver<TParent>;
  left?: SanityImageCropToLeftResolver<TParent>;
  right?: SanityImageCropToRightResolver<TParent>;
  top?: SanityImageCropToTopResolver<TParent>;
}

export interface SanityImageCropTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageCropTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageCropToBottomResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageCropToLeftResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageCropToRightResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageCropToTopResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ISanityImageDimensionsTypeResolver<TParent = any> {
  _key?: SanityImageDimensionsTo_keyResolver<TParent>;
  _type?: SanityImageDimensionsTo_typeResolver<TParent>;
  aspectRatio?: SanityImageDimensionsToAspectRatioResolver<TParent>;
  height?: SanityImageDimensionsToHeightResolver<TParent>;
  width?: SanityImageDimensionsToWidthResolver<TParent>;
}

export interface SanityImageDimensionsTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageDimensionsTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageDimensionsToAspectRatioResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageDimensionsToHeightResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageDimensionsToWidthResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ISanityImageHotspotTypeResolver<TParent = any> {
  _key?: SanityImageHotspotTo_keyResolver<TParent>;
  _type?: SanityImageHotspotTo_typeResolver<TParent>;
  height?: SanityImageHotspotToHeightResolver<TParent>;
  width?: SanityImageHotspotToWidthResolver<TParent>;
  x?: SanityImageHotspotToXResolver<TParent>;
  y?: SanityImageHotspotToYResolver<TParent>;
}

export interface SanityImageHotspotTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageHotspotTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageHotspotToHeightResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageHotspotToWidthResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageHotspotToXResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageHotspotToYResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ISanityImageMetadataTypeResolver<TParent = any> {
  _key?: SanityImageMetadataTo_keyResolver<TParent>;
  _type?: SanityImageMetadataTo_typeResolver<TParent>;
  blurHash?: SanityImageMetadataToBlurHashResolver<TParent>;
  dimensions?: SanityImageMetadataToDimensionsResolver<TParent>;
  hasAlpha?: SanityImageMetadataToHasAlphaResolver<TParent>;
  isOpaque?: SanityImageMetadataToIsOpaqueResolver<TParent>;
  location?: SanityImageMetadataToLocationResolver<TParent>;
  lqip?: SanityImageMetadataToLqipResolver<TParent>;
  palette?: SanityImageMetadataToPaletteResolver<TParent>;
}

export interface SanityImageMetadataTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageMetadataTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageMetadataToBlurHashResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageMetadataToDimensionsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageMetadataToHasAlphaResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageMetadataToIsOpaqueResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageMetadataToLocationResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageMetadataToLqipResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImageMetadataToPaletteResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ISanityImagePaletteTypeResolver<TParent = any> {
  _key?: SanityImagePaletteTo_keyResolver<TParent>;
  _type?: SanityImagePaletteTo_typeResolver<TParent>;
  darkMuted?: SanityImagePaletteToDarkMutedResolver<TParent>;
  darkVibrant?: SanityImagePaletteToDarkVibrantResolver<TParent>;
  dominant?: SanityImagePaletteToDominantResolver<TParent>;
  lightMuted?: SanityImagePaletteToLightMutedResolver<TParent>;
  lightVibrant?: SanityImagePaletteToLightVibrantResolver<TParent>;
  muted?: SanityImagePaletteToMutedResolver<TParent>;
  vibrant?: SanityImagePaletteToVibrantResolver<TParent>;
}

export interface SanityImagePaletteTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImagePaletteTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImagePaletteToDarkMutedResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImagePaletteToDarkVibrantResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImagePaletteToDominantResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImagePaletteToLightMutedResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImagePaletteToLightVibrantResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImagePaletteToMutedResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImagePaletteToVibrantResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ISanityImagePaletteSwatchTypeResolver<TParent = any> {
  _key?: SanityImagePaletteSwatchTo_keyResolver<TParent>;
  _type?: SanityImagePaletteSwatchTo_typeResolver<TParent>;
  background?: SanityImagePaletteSwatchToBackgroundResolver<TParent>;
  foreground?: SanityImagePaletteSwatchToForegroundResolver<TParent>;
  population?: SanityImagePaletteSwatchToPopulationResolver<TParent>;
  title?: SanityImagePaletteSwatchToTitleResolver<TParent>;
}

export interface SanityImagePaletteSwatchTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImagePaletteSwatchTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImagePaletteSwatchToBackgroundResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImagePaletteSwatchToForegroundResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImagePaletteSwatchToPopulationResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SanityImagePaletteSwatchToTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ISectionGroupSliderGamesTypeResolver<TParent = any> {
  _createdAt?: SectionGroupSliderGamesTo_createdAtResolver<TParent>;
  _id?: SectionGroupSliderGamesTo_idResolver<TParent>;
  _key?: SectionGroupSliderGamesTo_keyResolver<TParent>;
  _rev?: SectionGroupSliderGamesTo_revResolver<TParent>;
  _type?: SectionGroupSliderGamesTo_typeResolver<TParent>;
  _updatedAt?: SectionGroupSliderGamesTo_updatedAtResolver<TParent>;
  groupCode?: SectionGroupSliderGamesToGroupCodeResolver<TParent>;
  groupName?: SectionGroupSliderGamesToGroupNameResolver<TParent>;
  isActive?: SectionGroupSliderGamesToIsActiveResolver<TParent>;
  region?: SectionGroupSliderGamesToRegionResolver<TParent>;
  sliderGames?: SectionGroupSliderGamesToSliderGamesResolver<TParent>;
}

export interface SectionGroupSliderGamesTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionGroupSliderGamesTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionGroupSliderGamesTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionGroupSliderGamesTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionGroupSliderGamesTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionGroupSliderGamesTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionGroupSliderGamesToGroupCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionGroupSliderGamesToGroupNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionGroupSliderGamesToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionGroupSliderGamesToRegionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionGroupSliderGamesToSliderGamesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ISectionSliderGamesTypeResolver<TParent = any> {
  _createdAt?: SectionSliderGamesTo_createdAtResolver<TParent>;
  _id?: SectionSliderGamesTo_idResolver<TParent>;
  _key?: SectionSliderGamesTo_keyResolver<TParent>;
  _rev?: SectionSliderGamesTo_revResolver<TParent>;
  _type?: SectionSliderGamesTo_typeResolver<TParent>;
  _updatedAt?: SectionSliderGamesTo_updatedAtResolver<TParent>;
  games?: SectionSliderGamesToGamesResolver<TParent>;
  isActive?: SectionSliderGamesToIsActiveResolver<TParent>;
  isShowGamesNumber?: SectionSliderGamesToIsShowGamesNumberResolver<TParent>;
  isVisibleSectionLogo?: SectionSliderGamesToIsVisibleSectionLogoResolver<TParent>;
  layout?: SectionSliderGamesToLayoutResolver<TParent>;
  region?: SectionSliderGamesToRegionResolver<TParent>;
  sectionCode?: SectionSliderGamesToSectionCodeResolver<TParent>;
  sectionLogo?: SectionSliderGamesToSectionLogoResolver<TParent>;
  sectionName?: SectionSliderGamesToSectionNameResolver<TParent>;
  sortGames?: SectionSliderGamesToSortGamesResolver<TParent>;
}

export interface SectionSliderGamesTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionSliderGamesTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionSliderGamesTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionSliderGamesTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionSliderGamesTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionSliderGamesTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionSliderGamesToGamesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionSliderGamesToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionSliderGamesToIsShowGamesNumberResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionSliderGamesToIsVisibleSectionLogoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionSliderGamesToLayoutResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionSliderGamesToRegionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionSliderGamesToSectionCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionSliderGamesToSectionLogoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionSliderGamesToSectionNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SectionSliderGamesToSortGamesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ISingleLine1TypeResolver<TParent = any> {
  _key?: SingleLine1To_keyResolver<TParent>;
  _type?: SingleLine1To_typeResolver<TParent>;
  text?: SingleLine1ToTextResolver<TParent>;
  textCode?: SingleLine1ToTextCodeResolver<TParent>;
}

export interface SingleLine1To_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SingleLine1To_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SingleLine1ToTextResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SingleLine1ToTextCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ISingleLine2TypeResolver<TParent = any> {
  _key?: SingleLine2To_keyResolver<TParent>;
  _type?: SingleLine2To_typeResolver<TParent>;
  text1?: SingleLine2ToText1Resolver<TParent>;
  text2?: SingleLine2ToText2Resolver<TParent>;
  textCode?: SingleLine2ToTextCodeResolver<TParent>;
}

export interface SingleLine2To_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SingleLine2To_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SingleLine2ToText1Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SingleLine2ToText2Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SingleLine2ToTextCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ISingleLine3TypeResolver<TParent = any> {
  _key?: SingleLine3To_keyResolver<TParent>;
  _type?: SingleLine3To_typeResolver<TParent>;
  text1?: SingleLine3ToText1Resolver<TParent>;
  text2?: SingleLine3ToText2Resolver<TParent>;
  text3?: SingleLine3ToText3Resolver<TParent>;
  textCode?: SingleLine3ToTextCodeResolver<TParent>;
}

export interface SingleLine3To_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SingleLine3To_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SingleLine3ToText1Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SingleLine3ToText2Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SingleLine3ToText3Resolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SingleLine3ToTextCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ISiteConfigTypeResolver<TParent = any> {
  _createdAt?: SiteConfigTo_createdAtResolver<TParent>;
  _id?: SiteConfigTo_idResolver<TParent>;
  _key?: SiteConfigTo_keyResolver<TParent>;
  _rev?: SiteConfigTo_revResolver<TParent>;
  _type?: SiteConfigTo_typeResolver<TParent>;
  _updatedAt?: SiteConfigTo_updatedAtResolver<TParent>;
  emails?: SiteConfigToEmailsResolver<TParent>;
  employees?: SiteConfigToEmployeesResolver<TParent>;
  homePage?: SiteConfigToHomePageResolver<TParent>;
  languages?: SiteConfigToLanguagesResolver<TParent>;
  metaDescription?: SiteConfigToMetaDescriptionResolver<TParent>;
  metaKeywords?: SiteConfigToMetaKeywordsResolver<TParent>;
  metaRobots?: SiteConfigToMetaRobotsResolver<TParent>;
  phones?: SiteConfigToPhonesResolver<TParent>;
  region?: SiteConfigToRegionResolver<TParent>;
  title?: SiteConfigToTitleResolver<TParent>;
}

export interface SiteConfigTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SiteConfigTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SiteConfigTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SiteConfigTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SiteConfigTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SiteConfigTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SiteConfigToEmailsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SiteConfigToEmployeesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SiteConfigToHomePageResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SiteConfigToLanguagesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SiteConfigToMetaDescriptionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SiteConfigToMetaKeywordsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SiteConfigToMetaRobotsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SiteConfigToPhonesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SiteConfigToRegionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SiteConfigToTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ISitePageTypeResolver<TParent = any> {
  _createdAt?: SitePageTo_createdAtResolver<TParent>;
  _id?: SitePageTo_idResolver<TParent>;
  _key?: SitePageTo_keyResolver<TParent>;
  _rev?: SitePageTo_revResolver<TParent>;
  _type?: SitePageTo_typeResolver<TParent>;
  _updatedAt?: SitePageTo_updatedAtResolver<TParent>;
  code?: SitePageToCodeResolver<TParent>;
  isActive?: SitePageToIsActiveResolver<TParent>;
  name?: SitePageToNameResolver<TParent>;
  region?: SitePageToRegionResolver<TParent>;
  title?: SitePageToTitleResolver<TParent>;
  version?: SitePageToVersionResolver<TParent>;
  widgets?: SitePageToWidgetsResolver<TParent>;
}

export interface SitePageTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SitePageTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SitePageTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SitePageTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SitePageTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SitePageTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SitePageToCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SitePageToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SitePageToNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SitePageToRegionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SitePageToTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SitePageToVersionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SitePageToWidgetsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ISlugTypeResolver<TParent = any> {
  _key?: SlugTo_keyResolver<TParent>;
  _type?: SlugTo_typeResolver<TParent>;
  current?: SlugToCurrentResolver<TParent>;
}

export interface SlugTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SlugTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SlugToCurrentResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ISocialNetworkTypeResolver<TParent = any> {
  _createdAt?: SocialNetworkTo_createdAtResolver<TParent>;
  _id?: SocialNetworkTo_idResolver<TParent>;
  _key?: SocialNetworkTo_keyResolver<TParent>;
  _rev?: SocialNetworkTo_revResolver<TParent>;
  _type?: SocialNetworkTo_typeResolver<TParent>;
  _updatedAt?: SocialNetworkTo_updatedAtResolver<TParent>;
  code?: SocialNetworkToCodeResolver<TParent>;
  isActive?: SocialNetworkToIsActiveResolver<TParent>;
  snIcon?: SocialNetworkToSnIconResolver<TParent>;
  snName?: SocialNetworkToSnNameResolver<TParent>;
  snUrl?: SocialNetworkToSnUrlResolver<TParent>;
}

export interface SocialNetworkTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SocialNetworkTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SocialNetworkTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SocialNetworkTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SocialNetworkTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SocialNetworkTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SocialNetworkToCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SocialNetworkToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SocialNetworkToSnIconResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SocialNetworkToSnNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SocialNetworkToSnUrlResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ISpanTypeResolver<TParent = any> {
  _key?: SpanTo_keyResolver<TParent>;
  _type?: SpanTo_typeResolver<TParent>;
  marks?: SpanToMarksResolver<TParent>;
  text?: SpanToTextResolver<TParent>;
}

export interface SpanTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SpanTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SpanToMarksResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface SpanToTextResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IStudioTypeResolver<TParent = any> {
  _createdAt?: StudioTo_createdAtResolver<TParent>;
  _id?: StudioTo_idResolver<TParent>;
  _key?: StudioTo_keyResolver<TParent>;
  _rev?: StudioTo_revResolver<TParent>;
  _type?: StudioTo_typeResolver<TParent>;
  _updatedAt?: StudioTo_updatedAtResolver<TParent>;
  code?: StudioToCodeResolver<TParent>;
  studioName?: StudioToStudioNameResolver<TParent>;
  studioPhoto?: StudioToStudioPhotoResolver<TParent>;
}

export interface StudioTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface StudioTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface StudioTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface StudioTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface StudioTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface StudioTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface StudioToCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface StudioToStudioNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface StudioToStudioPhotoResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ITextareaTypeResolver<TParent = any> {
  _key?: TextareaTo_keyResolver<TParent>;
  _type?: TextareaTo_typeResolver<TParent>;
  text?: TextareaToTextResolver<TParent>;
  textCode?: TextareaToTextCodeResolver<TParent>;
}

export interface TextareaTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface TextareaTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface TextareaToTextResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface TextareaToTextCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface ITournamentTypeResolver<TParent = any> {
  _createdAt?: TournamentTo_createdAtResolver<TParent>;
  _id?: TournamentTo_idResolver<TParent>;
  _key?: TournamentTo_keyResolver<TParent>;
  _rev?: TournamentTo_revResolver<TParent>;
  _type?: TournamentTo_typeResolver<TParent>;
  _updatedAt?: TournamentTo_updatedAtResolver<TParent>;
  code?: TournamentToCodeResolver<TParent>;
  isActive?: TournamentToIsActiveResolver<TParent>;
  name?: TournamentToNameResolver<TParent>;
}

export interface TournamentTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface TournamentTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface TournamentTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface TournamentTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface TournamentTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface TournamentTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface TournamentToCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface TournamentToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface TournamentToNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IUrlTargetTypeResolver<TParent = any> {
  _key?: UrlTargetTo_keyResolver<TParent>;
  _type?: UrlTargetTo_typeResolver<TParent>;
  url?: UrlTargetToUrlResolver<TParent>;
  urlTarget?: UrlTargetToUrlTargetResolver<TParent>;
}

export interface UrlTargetTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface UrlTargetTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface UrlTargetToUrlResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface UrlTargetToUrlTargetResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IVideoTypeResolver<TParent = any> {
  _key?: VideoTo_keyResolver<TParent>;
  _type?: VideoTo_typeResolver<TParent>;
  file?: VideoToFileResolver<TParent>;
  headerTicker?: VideoToHeaderTickerResolver<TParent>;
  height?: VideoToHeightResolver<TParent>;
  mobileVideoPoster?: VideoToMobileVideoPosterResolver<TParent>;
  type?: VideoToTypeResolver<TParent>;
  url?: VideoToUrlResolver<TParent>;
  videoPoster?: VideoToVideoPosterResolver<TParent>;
  width?: VideoToWidthResolver<TParent>;
}

export interface VideoTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface VideoTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface VideoToFileResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface VideoToHeaderTickerResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface VideoToHeightResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface VideoToMobileVideoPosterResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface VideoToTypeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface VideoToUrlResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface VideoToVideoPosterResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface VideoToWidthResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IVideoUrlTypeResolver<TParent = any> {
  _key?: VideoUrlTo_keyResolver<TParent>;
  _type?: VideoUrlTo_typeResolver<TParent>;
  file?: VideoUrlToFileResolver<TParent>;
  languageCode?: VideoUrlToLanguageCodeResolver<TParent>;
  type?: VideoUrlToTypeResolver<TParent>;
  url?: VideoUrlToUrlResolver<TParent>;
}

export interface VideoUrlTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface VideoUrlTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface VideoUrlToFileResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface VideoUrlToLanguageCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface VideoUrlToTypeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface VideoUrlToUrlResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IWebsiteConfigTypeResolver<TParent = any> {
  _createdAt?: WebsiteConfigTo_createdAtResolver<TParent>;
  _id?: WebsiteConfigTo_idResolver<TParent>;
  _key?: WebsiteConfigTo_keyResolver<TParent>;
  _rev?: WebsiteConfigTo_revResolver<TParent>;
  _type?: WebsiteConfigTo_typeResolver<TParent>;
  _updatedAt?: WebsiteConfigTo_updatedAtResolver<TParent>;
  emails?: WebsiteConfigToEmailsResolver<TParent>;
  facebookUrl?: WebsiteConfigToFacebookUrlResolver<TParent>;
  footerLicenses?: WebsiteConfigToFooterLicensesResolver<TParent>;
  gameAPI?: WebsiteConfigToGameAPIResolver<TParent>;
  instagramUrl?: WebsiteConfigToInstagramUrlResolver<TParent>;
  linkedInUrl?: WebsiteConfigToLinkedInUrlResolver<TParent>;
  metaDescription?: WebsiteConfigToMetaDescriptionResolver<TParent>;
  metaKeywords?: WebsiteConfigToMetaKeywordsResolver<TParent>;
  metaRobots?: WebsiteConfigToMetaRobotsResolver<TParent>;
  phones?: WebsiteConfigToPhonesResolver<TParent>;
  socialNetworks?: WebsiteConfigToSocialNetworksResolver<TParent>;
  title?: WebsiteConfigToTitleResolver<TParent>;
  youtubeUrl?: WebsiteConfigToYoutubeUrlResolver<TParent>;
}

export interface WebsiteConfigTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WebsiteConfigTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WebsiteConfigTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WebsiteConfigTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WebsiteConfigTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WebsiteConfigTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WebsiteConfigToEmailsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WebsiteConfigToFacebookUrlResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WebsiteConfigToFooterLicensesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WebsiteConfigToGameAPIResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WebsiteConfigToInstagramUrlResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WebsiteConfigToLinkedInUrlResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WebsiteConfigToMetaDescriptionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WebsiteConfigToMetaKeywordsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WebsiteConfigToMetaRobotsResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WebsiteConfigToPhonesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WebsiteConfigToSocialNetworksResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WebsiteConfigToTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WebsiteConfigToYoutubeUrlResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IWidgetPartnersTypeResolver<TParent = any> {
  _createdAt?: WidgetPartnersTo_createdAtResolver<TParent>;
  _id?: WidgetPartnersTo_idResolver<TParent>;
  _key?: WidgetPartnersTo_keyResolver<TParent>;
  _rev?: WidgetPartnersTo_revResolver<TParent>;
  _type?: WidgetPartnersTo_typeResolver<TParent>;
  _updatedAt?: WidgetPartnersTo_updatedAtResolver<TParent>;
  code?: WidgetPartnersToCodeResolver<TParent>;
  isActive?: WidgetPartnersToIsActiveResolver<TParent>;
  name?: WidgetPartnersToNameResolver<TParent>;
  partners?: WidgetPartnersToPartnersResolver<TParent>;
  title?: WidgetPartnersToTitleResolver<TParent>;
}

export interface WidgetPartnersTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetPartnersTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetPartnersTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetPartnersTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetPartnersTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetPartnersTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetPartnersToCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetPartnersToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetPartnersToNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetPartnersToPartnersResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetPartnersToTitleResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IWidgetSwiperTypeResolver<TParent = any> {
  _createdAt?: WidgetSwiperTo_createdAtResolver<TParent>;
  _id?: WidgetSwiperTo_idResolver<TParent>;
  _key?: WidgetSwiperTo_keyResolver<TParent>;
  _rev?: WidgetSwiperTo_revResolver<TParent>;
  _type?: WidgetSwiperTo_typeResolver<TParent>;
  _updatedAt?: WidgetSwiperTo_updatedAtResolver<TParent>;
  code?: WidgetSwiperToCodeResolver<TParent>;
  isActive?: WidgetSwiperToIsActiveResolver<TParent>;
  name?: WidgetSwiperToNameResolver<TParent>;
  swiperParameters?: WidgetSwiperToSwiperParametersResolver<TParent>;
}

export interface WidgetSwiperTo_createdAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetSwiperTo_idResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetSwiperTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetSwiperTo_revResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetSwiperTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetSwiperTo_updatedAtResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetSwiperToCodeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetSwiperToIsActiveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetSwiperToNameResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetSwiperToSwiperParametersResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface IWidgetSwiperParametersTypeResolver<TParent = any> {
  _key?: WidgetSwiperParametersTo_keyResolver<TParent>;
  _type?: WidgetSwiperParametersTo_typeResolver<TParent>;
  allowSlideNext?: WidgetSwiperParametersToAllowSlideNextResolver<TParent>;
  allowSlidePrev?: WidgetSwiperParametersToAllowSlidePrevResolver<TParent>;
  allowTouchMove?: WidgetSwiperParametersToAllowTouchMoveResolver<TParent>;
  autoHeight?: WidgetSwiperParametersToAutoHeightResolver<TParent>;
  centeredSlides?: WidgetSwiperParametersToCenteredSlidesResolver<TParent>;
  direction?: WidgetSwiperParametersToDirectionResolver<TParent>;
}

export interface WidgetSwiperParametersTo_keyResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetSwiperParametersTo_typeResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetSwiperParametersToAllowSlideNextResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetSwiperParametersToAllowSlidePrevResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetSwiperParametersToAllowTouchMoveResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetSwiperParametersToAutoHeightResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetSwiperParametersToCenteredSlidesResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}

export interface WidgetSwiperParametersToDirectionResolver<TParent = any, TResult = any> {
  (parent: TParent, args: {}, context: any, info: GraphQLResolveInfo): TResult;
}
