import sanityClient, { SanityClient } from "@sanity/client";
import logger from "utils/logger";
import { EnumLanguageCode, EnumRegionCode } from "models/enum/system";
import { IGame, ISanityFileAsset, ISanityImageAsset, ISectionGroupSliderGames, ISectionSliderGames } from "./schemas";
import {
    IBannerExtended,
    IBannersCarouselExtended,
    IGameExtended,
    IGameShortInfo,
    INewsExtended,
    IPageAboutUsExtended,
    IPageGamesExtended,
    IPageRoadmapExtended,
    IPageToolsJackpotExtended,
    IPageToolsTournamentExtended,
    ISanityProvider,
    ISectionGroupSliderGamesExtended,
    ISectionGroupSliderGamesResponse,
    IWebsiteConfigExtended
} from "api/data-providers/sanity/interfaces/iSanityProvider";
import { DBProviderInstance } from "api/data-providers/db/dbProvider";
import appServerConfig from "appServerConfig";

const log = logger("data-provider--sanity");

export class SanityProvider implements ISanityProvider {
    public getSanityClient(): SanityClient | null {
        try {
            return sanityClient({
                projectId: appServerConfig.sanity.client.projectId,
                dataset: appServerConfig.sanity.client.dataset,
                apiVersion: appServerConfig.sanity.client.apiVersion,
                token: appServerConfig.sanity.client.token,
                useCdn: false
            });
        } catch (e) {
            log.error(e, "Can't connect to sanity client");
            return null;
        }
    }

    public static getLocale(name: string, languageCode: EnumLanguageCode): string {
        return `"${name}": ${name}{${languageCode}}`;
    }

    public static getSlug(name: string): string {
        return `"${name}": ${name}{current}`;
    }

    public static getImageUrl(name: string, languageCode: EnumLanguageCode, nameAlias: string | null = null): string {
        const na = nameAlias || name + "Url";
        return `"${na}": ${name}.${languageCode}.asset->url`
    }

    public static getAllMarketsExtended(languageCode: EnumLanguageCode, isActive: boolean = true): string {
        return `
    "allGameMarkets": *[_type == "market"${isActive ? " && isActive == true" : ""} && !(_id in path('drafts.**'))]{
        ...,
        "iconUrl": marketIcon.${languageCode}.asset->url
    }`;
    }

    public static getBiggestMarketsExtended(languageCode: EnumLanguageCode) {
        return `
    "biggestMarkets": *[_id == "pageRoadmap-partner-area" && !(_id in path('drafts.**'))]{
        biggestMarkets[]->{
            ..., 
            "iconUrl": marketIcon.${languageCode}.asset->url
        }
    }[0]["biggestMarkets"]`;
    }

    public static getPARoadmapGameExtended() {
        return `
    "roadmapGame": *[_id == "pageRoadmap-partner-area" && !(_id in path('drafts.**'))]{roadmapGame}[0]["roadmapGame"]`;
    }

    public static getGameRTPExtended() {
        return `
    "gameRTPExt": gameRTP[]->{gameRTP}`;
    }

    public static getGameInfoExtended() {
        return `
    "gameInfo": gameInfo {..., "markets": markets[]-> {marketName} }`;
    }

    public static certificatesPerMarket(languageCode: EnumLanguageCode) {
        return `
    "certificatesPerMarket": certificatesPerMarket[] {
        ...,
        "certificateUrl": certificate.asset->url,
        "certificate1Url": certificate_1.asset->url,
        "certificate2Url": certificate_2.asset->url,
        ${SanityProvider.getGameMarketExtended(languageCode)}
    }        
        `;
    }

    public static getGameMarketExtended(languageCode: EnumLanguageCode) {
        return `
    "gameMarket": gameMarket-> {
        ...,
        "iconUrl": marketIcon.${languageCode}.asset->url
    }`;
    }

    public static getGameVideoExtended(languageCode: EnumLanguageCode) {
        const gameVideoFields = [
            "url",
            "type",
            `"videoUrl": file.asset->url`,
            SanityProvider.getImageUrl("videoPoster", languageCode, "posterUrl"),
            SanityProvider.getImageUrl("mobileVideoPoster", languageCode, "posterVerticalUrl")
        ];
        return `"gameVideo": gameVideo {
    ${gameVideoFields}
    }`;
    }

    public static getGameVideosExtended() {
        return `"gameVideosExt": gameVideos[] {
        "videoUrl": file.asset->url,
        languageCode
    }`;
    }

    public static getPageBannerExtended(languageCode: EnumLanguageCode) {
        return `
    banner[@->isActive == true]->{
        ...,
        game -> {
            gameName,
            isActive,
            "gameCodes": gameCode[]->        
        },
        "banner600x140": banner600x140 {
            ...,
            "assetImageUrl": assetImage.${languageCode}.asset->url,
            "assetVideoUrl": assetVideo.asset->url
        },
        "banner1920x120": banner1920x120 {
            ...,
            "assetImageUrl": assetImage.${languageCode}.asset->url,
            "assetVideoUrl": assetVideo.asset->url
        }
    } `;
    }

    public static getPageBannersExtended(languageCode: EnumLanguageCode) {
        return `
    banners[@->isActive == true]->{
        ...,
        game -> {
            ${SanityProvider.getSlug("gameId")},
            gameName,
            isActive,
            "gameCodes": gameCode[]->        
        },
        "banner600x140": banner600x140 {
            ...,
            "assetImageUrl": assetImage.${languageCode}.asset->url,
            "assetVideoUrl": assetVideo.asset->url
        },
        "banner1920x120": banner1920x120 {
            ...,
            "assetImageUrl": assetImage.${languageCode}.asset->url,
            "assetVideoUrl": assetVideo.asset->url
        }
    } `;
    }

    public static getPageBannersCarouselExtended(languageCode: EnumLanguageCode) {
        return `
    banners {
        ...,
        ${SanityProvider.getPageBannersExtended(languageCode)}
    } `;
    }

    public static getPartnersExtended(region: EnumRegionCode, languageCode: EnumLanguageCode) {
        const partnersFields = [
            "_id",
            SanityProvider.getSlug("partnerCode"),
            SanityProvider.getLocale("partnerName", languageCode),
            "partnerUrl",
            SanityProvider.getImageUrl("partnerImage", languageCode)
        ];
        return `
    "partners": *[_id == "pageHome-${region}" && !(_id in path('drafts.**'))]{partners[@->isActive == true]->{
        ${partnersFields}
}}[0].partners`;
    }

    public static getEmployeesExtended(region: EnumRegionCode, languageCode: EnumLanguageCode) {
        const employeesFields= [
            "_id",
            SanityProvider.getLocale("employeeDescription", languageCode),
            SanityProvider.getLocale("employeeName", languageCode),
            SanityProvider.getLocale("employeePosition", languageCode),
            SanityProvider.getImageUrl("employeePhoto", languageCode)
        ];
        return `
    "employees": *[_id == "pageAboutUs-${region}" && !(_id in path('drafts.**'))]{employees[@->isActive == true]->{
        ${employeesFields}
}}[0].employees`;
    }

    public static getLicensesExtended(region: EnumRegionCode, languageCode: EnumLanguageCode) {
        const licensesFields = [
            "_id",
            SanityProvider.getLocale("jurisdiction", languageCode),
            SanityProvider.getLocale("licenseDescription", languageCode),
            "licenseNo",
            "licenseUrl",
            SanityProvider.getLocale("regulator", languageCode),
            SanityProvider.getImageUrl("licensePhoto", languageCode)
        ];
        return `
    "licenses": *[_id == "pageAboutUs-${region}" && !(_id in path('drafts.**'))]{licenses[@->isActive == true]->{
        ${licensesFields}
}}[0].licenses`;
    }

    public static getOfficesExtended(region: EnumRegionCode, languageCode: EnumLanguageCode) {
        const officesFields = [
            "_id",
            SanityProvider.getLocale("address", languageCode),
            SanityProvider.getSlug("code"),
            SanityProvider.getLocale("companyName", languageCode),
            SanityProvider.getLocale("country", languageCode),
            "email",
            "leftOnMap",
            SanityProvider.getLocale("officeName", languageCode),
            "phone1",
            "topOnMap",
            `"largePhotosExt": largePhotos[]{"imageUrl": asset->url}`,
            SanityProvider.getImageUrl("officePhoto", languageCode)
        ];
        return `
    "offices": *[_id == "pageAboutUs-${region}" && !(_id in path('drafts.**'))]{offices[@->isActive == true]->{
        ${officesFields}
}}[0].offices`;
    }

    public async fetch(query: string, params: { [key: string]: string }): Promise<never> {
        const sanityClient: SanityClient = this.getSanityClient() as SanityClient;
        if (appServerConfig.logging.queryLogging) {
            log.info("SANITY QUERY: ", query, "PARAMS:", params);
        }
        return sanityClient.fetch(query, params);
    }

    public async getAvailableGames(languageCode: EnumLanguageCode): Promise<IGameExtended[]> {
        try {
            const data = await this.fetch(
                `*[_type == "game" && isActive == true && !(_id in path("drafts.**"))]
{
    ...,
    ${SanityProvider.getGameVideoExtended(languageCode)},
    ${SanityProvider.getGameVideosExtended()},
    ${SanityProvider.getGameRTPExtended()},
    ${SanityProvider.getGameInfoExtended()},
    "gameCodes": gameCode[]->,
    "gameTypes": gameType[]->,
    "gamePosterUrl": gamePoster.${languageCode}.asset->url,
    "gameTowerPosterUrl": gameTowerPoster.${languageCode}.asset->url,
    "productSheetUrl": releaseNote.asset->url,
    "infoSheetUrl": saleSheet.asset->url,
    ${SanityProvider.certificatesPerMarket(languageCode)}
}`,
                {}
            );
            return (Array.isArray(data) && data) || [];
        } catch (e) {
            log.error(e, "Error executing gamePageGames.");
            return Promise.reject(e);
        }
    }

    public async gameMarketsExtended(region: string, languageCode: EnumLanguageCode): Promise<IPageGamesExtended> {
        try {
            const data = await this.fetch(
                `*[_type == "pageGames" && (_id == $pageGame) && !(_id in path("drafts.**"))]
{
    ...,
    ${SanityProvider.getAllMarketsExtended(languageCode)},
    ${SanityProvider.getBiggestMarketsExtended(languageCode)},
}`,
                { pageGame: "pageGame-" + region }
            );
            return data[0] ?? {};
        } catch (e) {
            log.error(e, "Error executing gameMarketsExtended.");
            return Promise.reject(e);
        }
    }

    public async gamePageGames(region: string, languageCode: EnumLanguageCode): Promise<IPageGamesExtended> {
        try {
            const data = await this.fetch(
                `*[_type == "pageGames" && (_id == $pageGame) && !(_id in path("drafts.**"))]
{
    ...,
    ${SanityProvider.getAllMarketsExtended(languageCode)},
    ${SanityProvider.getBiggestMarketsExtended(languageCode)},
    "brandedGames": gamesBranded[]{game->{
        ...,
        ${SanityProvider.getGameVideoExtended(languageCode)},
        ${SanityProvider.getGameRTPExtended()},
        "gameCodes": gameCode[]->,
        "gamePosterUrl": gamePoster.${languageCode}.asset->url,
        "gameTowerPosterUrl": gameTowerPoster.${languageCode}.asset->url,
        "productSheetUrl": releaseNote.asset->url,
        "infoSheetUrl": saleSheet.asset->url,
        ${SanityProvider.certificatesPerMarket(languageCode)}
    }},
    "playlistsGames": gamePlaylist[]->{playlistGames[]{game->{
        ...,
        ${SanityProvider.getGameVideoExtended(languageCode)},
        ${SanityProvider.getGameRTPExtended()},
        "gameCodes": gameCode[]->,
        "gamePosterUrl": gamePoster.${languageCode}.asset->url,
        "gameTowerPosterUrl": gameTowerPoster.${languageCode}.asset->url,
        "productSheetUrl": releaseNote.asset->url,
        "infoSheetUrl": saleSheet.asset->url,
        ${SanityProvider.certificatesPerMarket(languageCode)}
    }}},
    "categoriesGames": gamesCategories[]->{categoryGames[]{game->{
        ...,
        ${SanityProvider.getGameVideoExtended(languageCode)},
        ${SanityProvider.getGameRTPExtended()},
        "gameCodes": gameCode[]->,
        "gamePosterUrl": gamePoster.${languageCode}.asset->url,
        "gameTowerPosterUrl": gameTowerPoster.${languageCode}.asset->url,
        "productSheetUrl": releaseNote.asset->url,
        "infoSheetUrl": saleSheet.asset->url,
        ${SanityProvider.certificatesPerMarket(languageCode)}
    }}}, 
    gamesCategories[]->, 
    gamePlaylist[]->
}`,
                { pageGame: "pageGame-" + region }
            );
            return data[0] ?? {};
        } catch (e) {
            log.error(e, "Error executing gamePageGames.");
            return Promise.reject(e);
        }
    }

    public async pageBanner(languageCode: EnumLanguageCode, pageId: string): Promise<IBannerExtended> {
        try {
            const data = await this.fetch(
                `*[_id == $pageId && !(_id in path('drafts.**'))]
{
    ${SanityProvider.getPageBannerExtended(languageCode)}
}.banner`,
                { pageId }
            );
            return data[0] ?? {};
        } catch (e) {
            log.error(e, "Error executing pageBanner.");
            return Promise.reject(e);
        }
    }

    public async getAssetById(assetId: string): Promise<ISanityFileAsset | ISanityImageAsset | null> {
        try {
            const data = await this.fetch(`
             *[(_type == 'sanity.imageAsset' || _type == 'sanity.fileAsset') && assetId == $assetId && !(_id in path('drafts.**'))][0...20]
{...}
`, {assetId});
            return data[0] ?? null;
        } catch (e) {
            log.error(e, "Error executing getAssetById.");
            return Promise.reject(e);
        }
    }

    public async pageBannersCarousel(
        languageCode: EnumLanguageCode,
        pageId: string
    ): Promise<IBannersCarouselExtended> {
        try {
            const data = await this.fetch(
                `*[_id == $pageId && !(_id in path('drafts.**'))]
{
    ${SanityProvider.getPageBannersCarouselExtended(languageCode)}
}.banners`,
                { pageId }
            );
            return data[0] ?? [];
        } catch (e) {
            log.error(e, "Error executing pageBannersCarousel.");
            return Promise.reject(e);
        }
    }

    public async pageRoadmap(languageCode: EnumLanguageCode): Promise<IPageRoadmapExtended> {
        try {
            const data = await this.fetch(
                `*[_id == $pageId && !(_id in path('drafts.**'))]
{   
    ...,
    "defaultMarket": defaultMarket->,
    "availableMarkets": availableMarkets[]->,
    ${SanityProvider.getAllMarketsExtended(languageCode)},
    ${SanityProvider.getBiggestMarketsExtended(languageCode)},
    roadmapGame[game->isActive == true]{
        ...,
        game->{
            ...,
            ${SanityProvider.getGameVideoExtended(languageCode)},
            ${SanityProvider.getGameVideosExtended()},
            "availableMarkets": market[]->,
            ${SanityProvider.getGameRTPExtended()},
            "gameCodes": gameCode[]->,
            "gamePosterUrl": gamePoster.${languageCode}.asset->url,
            "gameTowerPosterUrl": gameTowerPoster.${languageCode}.asset->url,
            "productSheetUrl": releaseNote.asset->url,
            "infoSheetUrl": saleSheet.asset->url,
            ${SanityProvider.certificatesPerMarket(languageCode)}
        },
        roadmapGameMarket[]{
            startDate, 
            gameMarket->{
                code,
                isActive,
                marketName, 
                "iconUrl": marketIcon.${languageCode}.asset->url
            }
        }
    }
}`,
                { pageId: "pageRoadmap-partner-area" }
            );
            return data[0] ?? {};
        } catch (e) {
            log.error(e, "Error executing pageRoadmap.");
            return Promise.reject(e);
        }
    }

    public async getAboutUsPage(
        regionCode: EnumRegionCode,
        languageCode: EnumLanguageCode
    ): Promise<IPageAboutUsExtended | null> {
        try {
            const data: [IPageAboutUsExtended] = await this.fetch(
                `*[_id == $pageId && !(_id in path('drafts.**'))]
{
    ${SanityProvider.getPartnersExtended(regionCode, languageCode)},
    ${SanityProvider.getEmployeesExtended(regionCode, languageCode)},
    ${SanityProvider.getLicensesExtended(regionCode, languageCode)},
    ${SanityProvider.getOfficesExtended(regionCode, languageCode)}
}`,
                { pageId: "pageAboutUs-" + regionCode }
            );
            return (Array.isArray(data) && data.length === 1 && data[0]) || null;
        } catch (e) {
            log.error(e, "Error executing getAboutUsPage.");
            return Promise.reject(e);
        }
    }

    public async getSectionGroupSliderGames(
        groupCode: string,
        regionCode: EnumRegionCode,
        languageCode: EnumLanguageCode
    ): Promise<ISectionGroupSliderGamesResponse | null> {
        const codes = JSON.stringify([groupCode]);
        try {
            const sectionFields = [
                "_id",
                SanityProvider.getSlug("groupCode"),
                SanityProvider.getLocale("groupName", languageCode),
                "region"
            ];
            const sectionSlidersFields = [
                "_id",
                "isShowGamesNumber",
                "isVisibleSectionLogo",
                "layout",
                "region",
                SanityProvider.getSlug("sectionCode"),
                SanityProvider.getLocale("sectionName", languageCode),
                "sortGames",
                SanityProvider.getImageUrl("sectionLogo", languageCode)
            ];
            const gameFields = [
                "_id",
                SanityProvider.getSlug("gameId"),
                SanityProvider.getLocale("gameName", languageCode),
                SanityProvider.getLocale("siteDescription", languageCode),
                "paUrl",
                SanityProvider.getGameVideoExtended(languageCode),
                SanityProvider.getImageUrl("gamePoster", languageCode),
                SanityProvider.getImageUrl("gameTowerPoster", languageCode)
            ];
            const data: ISectionGroupSliderGamesExtended = await this.fetch(
                `*[_type == $type && groupCode.current in ${codes} && isActive == true && !(_id in path('drafts.**'))]
    {
        ${sectionFields},
        "sliderGames": sliderGames[@->isActive==true]->{
            ${sectionSlidersFields},
            "games": games[@->isActive==true]->{
                ${gameFields},
                "gameCodes": gameCode[]->{
                    gameCode
                },
            }
        }
    }`,
                { type: "sectionGroupSliderGames" }
            );
            return (
                (Array.isArray(data) &&
                    data.length === 1 && {
                        data: data[0],
                        providerGames: await DBProviderInstance.getProviderGames(regionCode)
                    }) ||
                null
            );
        } catch (e) {
            log.error(e, "Error executing getSectionGroupSliderGames.");
            return Promise.reject(e);
        }
    }

    public async getGameProfile(gameId: string,
                                languageCode: EnumLanguageCode,
                                isActive: boolean = true): Promise<IGameExtended | null> {
        try {
            const data = await this.fetch(
                `*[_type == "game" ${isActive !== undefined
                                     ? " && isActive == " + (isActive ? "true" : "false") : ""
                } && gameId.current == $gameId && !(_id in path('drafts.**'))]
{
    ...,
    ${SanityProvider.getAllMarketsExtended(languageCode)},
    ${SanityProvider.getGameVideoExtended(languageCode)},
    ${SanityProvider.getGameVideosExtended()},
    ${SanityProvider.getBiggestMarketsExtended(languageCode)},
    ${SanityProvider.getGameRTPExtended()},
    "gameCodes": gameCode[]->,
    "gameTypes": gameType[]->,
    "gameFeatures": gameFeatures[]{...,"gameFeaturePosterUrl": gameFeaturePoster.${languageCode}.asset->url},
    "gameIconUrl": gameIcon.${languageCode}.asset->url,
    "gameImageUrl": gameImage.${languageCode}.asset->url,
    "gamePosterUrl": gamePoster.${languageCode}.asset->url,
    "gameHDPosterUrl": gameHDPoster.${languageCode}.asset->url,
    "gameLogoUrl": gameLogo.${languageCode}.asset->url,
    "gameTowerPosterUrl": gameTowerPoster.${languageCode}.asset->url,
    "gameTower4PosterUrl": gameTowerPoster.${languageCode}.asset->url,
    "gameBillboardUrl": gameBillboard.${languageCode}.asset->url,
    "gameBrandedPosterUrl": gameBrandedPoster.${languageCode}.asset->url,
    "productSheetUrl": releaseNote.asset->url,
    "infoSheetUrl": saleSheet.asset->url,
    ${SanityProvider.certificatesPerMarket(languageCode)},
    ${SanityProvider.getPARoadmapGameExtended()}
}`,
                { gameId }
            );
            return data[0] ?? null;
        } catch (e) {
            log.error(e, "Error executing getGameProfile.");
            return Promise.reject(e);
        }
    }

    public async getWebsiteConfig(
        regionCode: EnumRegionCode,
        languageCode: EnumLanguageCode
    ): Promise<IWebsiteConfigExtended> {
        try {
            const footerLicensesFields = [
                "_id",
                SanityProvider.getLocale("jurisdiction", languageCode),
                SanityProvider.getLocale("licenseDescription", languageCode),
                "licenseNo",
                "licenseUrl",
                SanityProvider.getLocale("regulator", languageCode),
                SanityProvider.getImageUrl("licensePhoto", languageCode),
                SanityProvider.getImageUrl("licensePhoto", languageCode)
            ];
            const socialNetworksFields = [
                "_id",
                SanityProvider.getSlug("code"),
                SanityProvider.getLocale("snName", languageCode),
                "snUrl",
                SanityProvider.getImageUrl("snIcon", languageCode, "iconUrl")
            ];
            const emailsFields = [
                "_id",
                SanityProvider.getSlug("code"),
                "emailAddress",
                SanityProvider.getLocale("emailTitle", languageCode)
            ];
            const phonesFields = [
                "_id",
                SanityProvider.getSlug("code"),
                "phoneNumber",
                SanityProvider.getLocale("phoneTitle", languageCode)
            ];
            const data = await this.fetch(
                `*[_type == 'websiteConfig' && _id == $id && !(_id in path('drafts.**'))]
{
    _id,
    "footerLicenses": footerLicenses[@->isActive==true]->{
        ${footerLicensesFields}
    },
    "socialNetworks": socialNetworks[@->isActive==true]->{
        ${socialNetworksFields}
    },
    "emails": emails[@->isActive==true]->{
        ${emailsFields}
    },
    "phones": phones[@->isActive==true]->{
        ${phonesFields}
    }
}`,
                { id: "website-config-" + regionCode }
            );
            return data[0] ?? {};
        } catch (e) {
            log.error(e, "Error executing getWebsiteConfig.");
            return Promise.reject(e);
        }
    }

    public async getPageToolsTournament(
        regionCode: EnumRegionCode,
        languageCode: EnumLanguageCode
    ): Promise<IPageToolsTournamentExtended> {
        try {
            const data = await this.fetch(
                `*[_type == 'pageToolsTournament' && _id == $id && isActive == true && !(_id in path('drafts.**'))]
{
    ...,
    "engagementSliders": engagementSliders[]->{
      ..., 
      "engagementSliderImageUrl": image.${languageCode}.asset->url
    }
}`,
                { id: "pageToolsTournament-" + regionCode }
            );
            return data[0] ?? {};
        } catch (e) {
            log.error(e, "Error executing getPageToolsTournament.");
            return Promise.reject(e);
        }
    }

    public async getPageToolsJackpot(
        regionCode: EnumRegionCode,
        languageCode: EnumLanguageCode
    ): Promise<IPageToolsJackpotExtended> {
        try {
            const data = await this.fetch(
                `*[_type == 'pageToolsJackpot' && _id == $id && isActive == true && !(_id in path('drafts.**'))]
{
    ...,
    "engagementSliders": engagementSliders[]->{
      ..., 
      "engagementSliderImageUrl": image.${languageCode}.asset->url
    },
		"jackpotsInfo": jackpotsInfo[]->{
      ..., 
      "jackpotsInfoIconUrl": icon.${languageCode}.asset->url
    },
}`,
                { id: "pageToolsJackpot-" + regionCode }
            );
            return data[0] ?? {};
        } catch (e) {
            log.error(e, "Error executing getPageToolsJackpot.");
            return Promise.reject(e);
        }
    }

    public async getNews(
        regionCode: EnumRegionCode,
        languageCode: EnumLanguageCode,
        id?: string | null
    ): Promise<INewsExtended[]> {
        try {
            const newsFields= [
                "_id",
                "block",
                SanityProvider.getLocale("highlightedTitle", languageCode),
                SanityProvider.getLocale("title", languageCode),
                "newsDate",
                "newsDate",
                SanityProvider.getImageUrl("newsPreviewImage", languageCode),
                "newsType",
                "region",
                "textType"
            ];
            const newsCategoriesFields = [
                "_id",
                SanityProvider.getSlug("newsCategoryCode"),
                SanityProvider.getLocale("newsCategoryTitle", languageCode)
            ];
            const headerPromoVideoFields = [
                "type",
                "url",
                "_type",
                `"videoUrl": file.asset->url`,
                SanityProvider.getImageUrl("videoPoster", languageCode, "posterUrl"),
                SanityProvider.getImageUrl("mobileVideoPoster", languageCode, "posterVerticalUrl"),
            ];
            const data = await this.fetch(
                `*[_type == 'news' && isActive == true && region == $region ${
                    id ? ` && _id == $id ` : ""
                } && !(_id in path('drafts.**'))]  | order(newsDate desc)
{
    ${newsFields},
    game->{
        ${SanityProvider.getSlug("gameId")},
        "gameCodes": gameCode[]->{
            gameCode
        },
    },
    "headerPromoVideo": headerPromoVideo {
        ${headerPromoVideoFields}
    },
    "newsCategories": newsCategories[]->{
        ${newsCategoriesFields}
    }
}`,
                { region: regionCode, ...(id && {id: String(id)}) }
            );
            return data ?? [];
        } catch (e) {
            log.error(e, "Error executing getWebsiteConfig.");
            return Promise.reject(e);
        }
    }

    public async getGamesForRoadMap(languageCode: string): Promise<IGameShortInfo[]> {
        try {
            const data = await this.fetch(
                `*[_type == $type && isActive == true && !(_id in path('drafts.**'))]
                {_id, 
                 gameId, 
                 "gameCodes": gameCode[]->{gameCode, gameInfo}, 
                 gameFeatures,
                 paUrl}`,
                { type: "game" }
            );
            return data ?? [];
        } catch (e) {
            log.error(e, "Error executing getGamesForRoadMap.");
            return Promise.reject(e);
        }
    }

    public async game(ids: string[]): Promise<IGame[]> {
        try {
            const sanityClient: SanityClient = this.getSanityClient() as SanityClient;
            const data = (await sanityClient.getDocuments(ids)) as IGame[];
            return Array.isArray(data) ? data : [];
        } catch (e) {
            log.error(e, "Error executing gamePageGames.");
            return Promise.reject(e);
        }
    }

    public async sectionSliderGames(): Promise<ISectionSliderGames[]> {
        try {
            const data = await this.fetch(
                `*[_type == "sectionSliderGames" && !(_id in path('drafts.**'))] {..., games[@->isActive==true]->}`,
                {}
            );
            return Array.isArray(data) ? data : [];
        } catch (e) {
            log.error(e, "Error executing sectionSliderGames.");
            return Promise.reject(e);
        }
    }

    public async sectionGroupSliderGames(
        codes: string[],
        languageCode: EnumLanguageCode
    ): Promise<ISectionGroupSliderGames[]> {
        try {
            let queryCodes = "";
            if (Array.isArray(codes) && codes.length) {
                queryCodes += "&& groupCode.current in " + JSON.stringify(codes);
            }

            const data = await this.fetch(
                `*[_type == "sectionGroupSliderGames" ${queryCodes} && !(_id in path('drafts.**'))]
                {
                ..., 
                sliderGames[]->{
                    ..., 
                    "sectionLogoUrl": sectionLogo.${languageCode}.asset->url,
                    "games": games[@->isActive==true]->{
                        ..., 
                        ${SanityProvider.getGameVideoExtended(languageCode)},
                        "gameCodes": gameCode[]->,
                        "gamePosterUrl": gamePoster.${languageCode}.asset->url,
                        "gameTowerPosterUrl": gameTowerPoster.${languageCode}.asset->url
                    }
                } }`,
                {}
            );

            return Array.isArray(data) ? data : [];
        } catch (e) {
            log.error(e, "Error executing sectionGroupSliderGames.");
            return Promise.reject(e);
        }
    }
}

export const SanityProviderInstance = new SanityProvider();