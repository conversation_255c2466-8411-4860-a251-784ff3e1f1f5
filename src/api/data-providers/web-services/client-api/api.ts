/* tslint:disable */
/* eslint-disable */
/**
 * Skywind Web Services
 * Skywind Web Services
 *
 * The version of the OpenAPI document: 0.1.34
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import { Configuration } from './configuration';
import globalAxios, { AxiosPromise, AxiosInstance, AxiosRequestConfig } from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from './common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from './base';

/**
 * 
 * @export
 * @interface ICheckHealthResponseDto
 */
export interface ICheckHealthResponseDto {
    /**
     * 
     * @type {string}
     * @memberof ICheckHealthResponseDto
     */
    'version': string;
    /**
     * 
     * @type {IDBNested}
     * @memberof ICheckHealthResponseDto
     */
    'dbObj': IDBNested;
    /**
     * 
     * @type {IServerNested}
     * @memberof ICheckHealthResponseDto
     */
    'serverObj': IServerNested;
    /**
     * 
     * @type {ISmtpNested}
     * @memberof ICheckHealthResponseDto
     */
    'smtpObj': ISmtpNested;
}
/**
 * 
 * @export
 * @interface IDBNested
 */
export interface IDBNested {
    /**
     * 
     * @type {string}
     * @memberof IDBNested
     */
    'version': string;
    /**
     * 
     * @type {string}
     * @memberof IDBNested
     */
    'host': string;
}
/**
 * 
 * @export
 * @interface IHSRCheckHealthResponseDto
 */
export interface IHSRCheckHealthResponseDto {
    /**
     * 
     * @type {IHSRCheckHealthResponseDtoCodesObj}
     * @memberof IHSRCheckHealthResponseDto
     */
    'codesObj': IHSRCheckHealthResponseDtoCodesObj;
    /**
     * 
     * @type {string}
     * @memberof IHSRCheckHealthResponseDto
     */
    'status': IHSRCheckHealthResponseDtoStatusEnum;
    /**
     * 
     * @type {IHSRSuccessDataCheckHealth}
     * @memberof IHSRCheckHealthResponseDto
     */
    'successObj'?: IHSRSuccessDataCheckHealth;
    /**
     * 
     * @type {IHSRError}
     * @memberof IHSRCheckHealthResponseDto
     */
    'errorObj'?: IHSRError;
    /**
     * 
     * @type {IHSRMessages}
     * @memberof IHSRCheckHealthResponseDto
     */
    'messagesObj'?: IHSRMessages;
    /**
     * 
     * @type {IHSRRequest}
     * @memberof IHSRCheckHealthResponseDto
     */
    'requestObj': IHSRRequest;
}

export const IHSRCheckHealthResponseDtoStatusEnum = {
    Success: 'success',
    Error: 'error',
    Unknown: 'unknown'
} as const;

export type IHSRCheckHealthResponseDtoStatusEnum = typeof IHSRCheckHealthResponseDtoStatusEnum[keyof typeof IHSRCheckHealthResponseDtoStatusEnum];

/**
 * Response server codes
 * @export
 * @interface IHSRCheckHealthResponseDtoCodesObj
 */
export interface IHSRCheckHealthResponseDtoCodesObj {
    /**
     * HTTP response status codes indicate whether a specific HTTP request has been successfully completed. 100-599
     * @type {number}
     * @memberof IHSRCheckHealthResponseDtoCodesObj
     */
    'responseStatusCode': number;
    /**
     * Api method response code
     * @type {number}
     * @memberof IHSRCheckHealthResponseDtoCodesObj
     */
    'appStatusCode': number;
}
/**
 * 
 * @export
 * @interface IHSRCodes
 */
export interface IHSRCodes {
    /**
     * HTTP response status codes indicate whether a specific HTTP request has been successfully completed. 100-599
     * @type {number}
     * @memberof IHSRCodes
     */
    'responseStatusCode': number;
    /**
     * Api method response code
     * @type {number}
     * @memberof IHSRCodes
     */
    'appStatusCode': number;
}
/**
 * 
 * @export
 * @interface IHSRError
 */
export interface IHSRError {
    /**
     * 
     * @type {string}
     * @memberof IHSRError
     */
    'type': IHSRErrorTypeEnum;
}

export const IHSRErrorTypeEnum = {
    InputValidation: 'inputValidation',
    InternalServer: 'internalServer',
    Service: 'service'
} as const;

export type IHSRErrorTypeEnum = typeof IHSRErrorTypeEnum[keyof typeof IHSRErrorTypeEnum];

/**
 * 
 * @export
 * @interface IHSRJackpotGetTotalResponseDto
 */
export interface IHSRJackpotGetTotalResponseDto {
    /**
     * 
     * @type {IHSRCheckHealthResponseDtoCodesObj}
     * @memberof IHSRJackpotGetTotalResponseDto
     */
    'codesObj': IHSRCheckHealthResponseDtoCodesObj;
    /**
     * 
     * @type {string}
     * @memberof IHSRJackpotGetTotalResponseDto
     */
    'status': IHSRJackpotGetTotalResponseDtoStatusEnum;
    /**
     * 
     * @type {IHSRSuccessDataJackpotInfo}
     * @memberof IHSRJackpotGetTotalResponseDto
     */
    'successObj'?: IHSRSuccessDataJackpotInfo;
    /**
     * 
     * @type {IHSRError}
     * @memberof IHSRJackpotGetTotalResponseDto
     */
    'errorObj'?: IHSRError;
    /**
     * 
     * @type {IHSRMessages}
     * @memberof IHSRJackpotGetTotalResponseDto
     */
    'messagesObj'?: IHSRMessages;
    /**
     * 
     * @type {IHSRRequest}
     * @memberof IHSRJackpotGetTotalResponseDto
     */
    'requestObj': IHSRRequest;
}

export const IHSRJackpotGetTotalResponseDtoStatusEnum = {
    Success: 'success',
    Error: 'error',
    Unknown: 'unknown'
} as const;

export type IHSRJackpotGetTotalResponseDtoStatusEnum = typeof IHSRJackpotGetTotalResponseDtoStatusEnum[keyof typeof IHSRJackpotGetTotalResponseDtoStatusEnum];

/**
 * 
 * @export
 * @interface IHSRMessage
 */
export interface IHSRMessage {
    /**
     * 
     * @type {string}
     * @memberof IHSRMessage
     */
    'code': string;
    /**
     * 
     * @type {string}
     * @memberof IHSRMessage
     */
    'type': IHSRMessageTypeEnum;
    /**
     * 
     * @type {string}
     * @memberof IHSRMessage
     */
    'message': string;
    /**
     * 
     * @type {object}
     * @memberof IHSRMessage
     */
    'attrsObj'?: object;
    /**
     * 
     * @type {object}
     * @memberof IHSRMessage
     */
    'translateObj'?: object;
}

export const IHSRMessageTypeEnum = {
    ValidationError: 'validationError',
    Notice: 'notice',
    Warning: 'warning',
    Error: 'error',
    Info: 'info'
} as const;

export type IHSRMessageTypeEnum = typeof IHSRMessageTypeEnum[keyof typeof IHSRMessageTypeEnum];

/**
 * 
 * @export
 * @interface IHSRMessages
 */
export interface IHSRMessages {
    /**
     * Error messages
     * @type {Array<IHSRMessage>}
     * @memberof IHSRMessages
     */
    'itemsArr': Array<IHSRMessage>;
}
/**
 * 
 * @export
 * @interface IHSRPagination
 */
export interface IHSRPagination {
    /**
     * 
     * @type {number}
     * @memberof IHSRPagination
     */
    'limit': number;
    /**
     * 
     * @type {number}
     * @memberof IHSRPagination
     */
    'offset': number;
}
/**
 * 
 * @export
 * @interface IHSRRequest
 */
export interface IHSRRequest {
    /**
     * Unique request identifier which set for each request and can help find problem in logs
     * @type {string}
     * @memberof IHSRRequest
     */
    'traceId': string;
    /**
     * Api request path with parameters
     * @type {string}
     * @memberof IHSRRequest
     */
    'path': string;
    /**
     * Api request method
     * @type {string}
     * @memberof IHSRRequest
     */
    'method': string;
    /**
     * 
     * @type {string}
     * @memberof IHSRRequest
     */
    'serverTime': string;
    /**
     * 
     * @type {string}
     * @memberof IHSRRequest
     */
    'apiVersion': string;
}
/**
 * 
 * @export
 * @interface IHSRSitemapPagesResponseDto
 */
export interface IHSRSitemapPagesResponseDto {
    /**
     * 
     * @type {IHSRCheckHealthResponseDtoCodesObj}
     * @memberof IHSRSitemapPagesResponseDto
     */
    'codesObj': IHSRCheckHealthResponseDtoCodesObj;
    /**
     * 
     * @type {string}
     * @memberof IHSRSitemapPagesResponseDto
     */
    'status': IHSRSitemapPagesResponseDtoStatusEnum;
    /**
     * 
     * @type {IHSRSuccessDataSitemapPages}
     * @memberof IHSRSitemapPagesResponseDto
     */
    'successObj'?: IHSRSuccessDataSitemapPages;
    /**
     * 
     * @type {IHSRError}
     * @memberof IHSRSitemapPagesResponseDto
     */
    'errorObj'?: IHSRError;
    /**
     * 
     * @type {IHSRMessages}
     * @memberof IHSRSitemapPagesResponseDto
     */
    'messagesObj'?: IHSRMessages;
    /**
     * 
     * @type {IHSRRequest}
     * @memberof IHSRSitemapPagesResponseDto
     */
    'requestObj': IHSRRequest;
}

export const IHSRSitemapPagesResponseDtoStatusEnum = {
    Success: 'success',
    Error: 'error',
    Unknown: 'unknown'
} as const;

export type IHSRSitemapPagesResponseDtoStatusEnum = typeof IHSRSitemapPagesResponseDtoStatusEnum[keyof typeof IHSRSitemapPagesResponseDtoStatusEnum];

/**
 * 
 * @export
 * @interface IHSRSuccessDataCheckHealth
 */
export interface IHSRSuccessDataCheckHealth {
    /**
     * 
     * @type {string}
     * @memberof IHSRSuccessDataCheckHealth
     */
    'type': IHSRSuccessDataCheckHealthTypeEnum;
    /**
     * 
     * @type {ICheckHealthResponseDto}
     * @memberof IHSRSuccessDataCheckHealth
     */
    'data': ICheckHealthResponseDto;
    /**
     * 
     * @type {number}
     * @memberof IHSRSuccessDataCheckHealth
     */
    'total': number;
    /**
     * 
     * @type {boolean}
     * @memberof IHSRSuccessDataCheckHealth
     */
    'isAllData': boolean;
    /**
     * 
     * @type {IHSRPagination}
     * @memberof IHSRSuccessDataCheckHealth
     */
    'paginationObj'?: IHSRPagination;
}

export const IHSRSuccessDataCheckHealthTypeEnum = {
    Object: 'object',
    Text: 'text',
    Array: 'array'
} as const;

export type IHSRSuccessDataCheckHealthTypeEnum = typeof IHSRSuccessDataCheckHealthTypeEnum[keyof typeof IHSRSuccessDataCheckHealthTypeEnum];

/**
 * 
 * @export
 * @interface IHSRSuccessDataJackpotInfo
 */
export interface IHSRSuccessDataJackpotInfo {
    /**
     * 
     * @type {string}
     * @memberof IHSRSuccessDataJackpotInfo
     */
    'type': IHSRSuccessDataJackpotInfoTypeEnum;
    /**
     * 
     * @type {IJackpotInfoGetResponseDto}
     * @memberof IHSRSuccessDataJackpotInfo
     */
    'data': IJackpotInfoGetResponseDto;
    /**
     * 
     * @type {number}
     * @memberof IHSRSuccessDataJackpotInfo
     */
    'total': number;
    /**
     * 
     * @type {boolean}
     * @memberof IHSRSuccessDataJackpotInfo
     */
    'isAllData': boolean;
    /**
     * 
     * @type {IHSRPagination}
     * @memberof IHSRSuccessDataJackpotInfo
     */
    'paginationObj'?: IHSRPagination;
}

export const IHSRSuccessDataJackpotInfoTypeEnum = {
    Object: 'object',
    Text: 'text',
    Array: 'array'
} as const;

export type IHSRSuccessDataJackpotInfoTypeEnum = typeof IHSRSuccessDataJackpotInfoTypeEnum[keyof typeof IHSRSuccessDataJackpotInfoTypeEnum];

/**
 * 
 * @export
 * @interface IHSRSuccessDataSitemapPages
 */
export interface IHSRSuccessDataSitemapPages {
    /**
     * 
     * @type {string}
     * @memberof IHSRSuccessDataSitemapPages
     */
    'type': IHSRSuccessDataSitemapPagesTypeEnum;
    /**
     * 
     * @type {ISitemapPagesResponseDto}
     * @memberof IHSRSuccessDataSitemapPages
     */
    'data': ISitemapPagesResponseDto;
    /**
     * 
     * @type {number}
     * @memberof IHSRSuccessDataSitemapPages
     */
    'total': number;
    /**
     * 
     * @type {boolean}
     * @memberof IHSRSuccessDataSitemapPages
     */
    'isAllData': boolean;
    /**
     * 
     * @type {IHSRPagination}
     * @memberof IHSRSuccessDataSitemapPages
     */
    'paginationObj'?: IHSRPagination;
}

export const IHSRSuccessDataSitemapPagesTypeEnum = {
    Object: 'object',
    Text: 'text',
    Array: 'array'
} as const;

export type IHSRSuccessDataSitemapPagesTypeEnum = typeof IHSRSuccessDataSitemapPagesTypeEnum[keyof typeof IHSRSuccessDataSitemapPagesTypeEnum];

/**
 * 
 * @export
 * @interface IHSRSuccessDataWebsiteContactUs
 */
export interface IHSRSuccessDataWebsiteContactUs {
    /**
     * 
     * @type {string}
     * @memberof IHSRSuccessDataWebsiteContactUs
     */
    'type': IHSRSuccessDataWebsiteContactUsTypeEnum;
    /**
     * 
     * @type {IWebsiteContactUsDto}
     * @memberof IHSRSuccessDataWebsiteContactUs
     */
    'data': IWebsiteContactUsDto;
    /**
     * 
     * @type {number}
     * @memberof IHSRSuccessDataWebsiteContactUs
     */
    'total': number;
    /**
     * 
     * @type {boolean}
     * @memberof IHSRSuccessDataWebsiteContactUs
     */
    'isAllData': boolean;
    /**
     * 
     * @type {IHSRPagination}
     * @memberof IHSRSuccessDataWebsiteContactUs
     */
    'paginationObj'?: IHSRPagination;
}

export const IHSRSuccessDataWebsiteContactUsTypeEnum = {
    Object: 'object',
    Text: 'text',
    Array: 'array'
} as const;

export type IHSRSuccessDataWebsiteContactUsTypeEnum = typeof IHSRSuccessDataWebsiteContactUsTypeEnum[keyof typeof IHSRSuccessDataWebsiteContactUsTypeEnum];

/**
 * 
 * @export
 * @interface IHSRSuccessDataWebsiteNewDealerRequest
 */
export interface IHSRSuccessDataWebsiteNewDealerRequest {
    /**
     * 
     * @type {string}
     * @memberof IHSRSuccessDataWebsiteNewDealerRequest
     */
    'type': IHSRSuccessDataWebsiteNewDealerRequestTypeEnum;
    /**
     * 
     * @type {IWebsiteNewDealerRequestDto}
     * @memberof IHSRSuccessDataWebsiteNewDealerRequest
     */
    'data': IWebsiteNewDealerRequestDto;
    /**
     * 
     * @type {number}
     * @memberof IHSRSuccessDataWebsiteNewDealerRequest
     */
    'total': number;
    /**
     * 
     * @type {boolean}
     * @memberof IHSRSuccessDataWebsiteNewDealerRequest
     */
    'isAllData': boolean;
    /**
     * 
     * @type {IHSRPagination}
     * @memberof IHSRSuccessDataWebsiteNewDealerRequest
     */
    'paginationObj'?: IHSRPagination;
}

export const IHSRSuccessDataWebsiteNewDealerRequestTypeEnum = {
    Object: 'object',
    Text: 'text',
    Array: 'array'
} as const;

export type IHSRSuccessDataWebsiteNewDealerRequestTypeEnum = typeof IHSRSuccessDataWebsiteNewDealerRequestTypeEnum[keyof typeof IHSRSuccessDataWebsiteNewDealerRequestTypeEnum];

/**
 * 
 * @export
 * @interface IHSRWebsiteContactUsResponseDto
 */
export interface IHSRWebsiteContactUsResponseDto {
    /**
     * 
     * @type {IHSRCheckHealthResponseDtoCodesObj}
     * @memberof IHSRWebsiteContactUsResponseDto
     */
    'codesObj': IHSRCheckHealthResponseDtoCodesObj;
    /**
     * 
     * @type {string}
     * @memberof IHSRWebsiteContactUsResponseDto
     */
    'status': IHSRWebsiteContactUsResponseDtoStatusEnum;
    /**
     * 
     * @type {IHSRSuccessDataWebsiteContactUs}
     * @memberof IHSRWebsiteContactUsResponseDto
     */
    'successObj'?: IHSRSuccessDataWebsiteContactUs;
    /**
     * 
     * @type {IHSRError}
     * @memberof IHSRWebsiteContactUsResponseDto
     */
    'errorObj'?: IHSRError;
    /**
     * 
     * @type {IHSRMessages}
     * @memberof IHSRWebsiteContactUsResponseDto
     */
    'messagesObj'?: IHSRMessages;
    /**
     * 
     * @type {IHSRRequest}
     * @memberof IHSRWebsiteContactUsResponseDto
     */
    'requestObj': IHSRRequest;
}

export const IHSRWebsiteContactUsResponseDtoStatusEnum = {
    Success: 'success',
    Error: 'error',
    Unknown: 'unknown'
} as const;

export type IHSRWebsiteContactUsResponseDtoStatusEnum = typeof IHSRWebsiteContactUsResponseDtoStatusEnum[keyof typeof IHSRWebsiteContactUsResponseDtoStatusEnum];

/**
 * 
 * @export
 * @interface IHSRWebsiteNewDealerRequestResponseDto
 */
export interface IHSRWebsiteNewDealerRequestResponseDto {
    /**
     * 
     * @type {IHSRCheckHealthResponseDtoCodesObj}
     * @memberof IHSRWebsiteNewDealerRequestResponseDto
     */
    'codesObj': IHSRCheckHealthResponseDtoCodesObj;
    /**
     * 
     * @type {string}
     * @memberof IHSRWebsiteNewDealerRequestResponseDto
     */
    'status': IHSRWebsiteNewDealerRequestResponseDtoStatusEnum;
    /**
     * 
     * @type {IHSRSuccessDataWebsiteNewDealerRequest}
     * @memberof IHSRWebsiteNewDealerRequestResponseDto
     */
    'successObj'?: IHSRSuccessDataWebsiteNewDealerRequest;
    /**
     * 
     * @type {IHSRError}
     * @memberof IHSRWebsiteNewDealerRequestResponseDto
     */
    'errorObj'?: IHSRError;
    /**
     * 
     * @type {IHSRMessages}
     * @memberof IHSRWebsiteNewDealerRequestResponseDto
     */
    'messagesObj'?: IHSRMessages;
    /**
     * 
     * @type {IHSRRequest}
     * @memberof IHSRWebsiteNewDealerRequestResponseDto
     */
    'requestObj': IHSRRequest;
}

export const IHSRWebsiteNewDealerRequestResponseDtoStatusEnum = {
    Success: 'success',
    Error: 'error',
    Unknown: 'unknown'
} as const;

export type IHSRWebsiteNewDealerRequestResponseDtoStatusEnum = typeof IHSRWebsiteNewDealerRequestResponseDtoStatusEnum[keyof typeof IHSRWebsiteNewDealerRequestResponseDtoStatusEnum];

/**
 * 
 * @export
 * @interface IHttpServerErrorResponseDto
 */
export interface IHttpServerErrorResponseDto {
    /**
     * 
     * @type {IHSRCheckHealthResponseDtoCodesObj}
     * @memberof IHttpServerErrorResponseDto
     */
    'codesObj': IHSRCheckHealthResponseDtoCodesObj;
    /**
     * 
     * @type {string}
     * @memberof IHttpServerErrorResponseDto
     */
    'status': IHttpServerErrorResponseDtoStatusEnum;
    /**
     * 
     * @type {IHSRError}
     * @memberof IHttpServerErrorResponseDto
     */
    'errorObj': IHSRError;
    /**
     * 
     * @type {IHSRMessages}
     * @memberof IHttpServerErrorResponseDto
     */
    'messagesObj': IHSRMessages;
    /**
     * 
     * @type {IHSRRequest}
     * @memberof IHttpServerErrorResponseDto
     */
    'requestObj': IHSRRequest;
}

export const IHttpServerErrorResponseDtoStatusEnum = {
    Success: 'success',
    Error: 'error',
    Unknown: 'unknown'
} as const;

export type IHttpServerErrorResponseDtoStatusEnum = typeof IHttpServerErrorResponseDtoStatusEnum[keyof typeof IHttpServerErrorResponseDtoStatusEnum];

/**
 * 
 * @export
 * @interface IJackpotInfoGetResponseDto
 */
export interface IJackpotInfoGetResponseDto {
    /**
     * 
     * @type {IJackpotInfoNested}
     * @memberof IJackpotInfoGetResponseDto
     */
    'jackpotObj': IJackpotInfoNested;
}
/**
 * 
 * @export
 * @interface IJackpotInfoNested
 */
export interface IJackpotInfoNested {
    /**
     * 
     * @type {number}
     * @memberof IJackpotInfoNested
     */
    'amount': number;
    /**
     * 
     * @type {string}
     * @memberof IJackpotInfoNested
     */
    'type': IJackpotInfoNestedTypeEnum;
    /**
     * 
     * @type {string}
     * @memberof IJackpotInfoNested
     */
    'currencyCode': IJackpotInfoNestedCurrencyCodeEnum;
    /**
     * 
     * @type {string}
     * @memberof IJackpotInfoNested
     */
    'projectCode': IJackpotInfoNestedProjectCodeEnum;
    /**
     * 
     * @type {object}
     * @memberof IJackpotInfoNested
     */
    'dateObj': object;
}

export const IJackpotInfoNestedTypeEnum = {
    Total: 'total',
    Game: 'game'
} as const;

export type IJackpotInfoNestedTypeEnum = typeof IJackpotInfoNestedTypeEnum[keyof typeof IJackpotInfoNestedTypeEnum];
export const IJackpotInfoNestedCurrencyCodeEnum = {
    Usd: 'USD',
    Eur: 'EUR',
    Cny: 'CNY'
} as const;

export type IJackpotInfoNestedCurrencyCodeEnum = typeof IJackpotInfoNestedCurrencyCodeEnum[keyof typeof IJackpotInfoNestedCurrencyCodeEnum];
export const IJackpotInfoNestedProjectCodeEnum = {
    Pa: 'pa',
    WebsiteEu: 'website_eu',
    WebsiteChina: 'website_china',
    WebsiteAsia: 'website_asia'
} as const;

export type IJackpotInfoNestedProjectCodeEnum = typeof IJackpotInfoNestedProjectCodeEnum[keyof typeof IJackpotInfoNestedProjectCodeEnum];

/**
 * 
 * @export
 * @interface ISanityNested
 */
export interface ISanityNested {
    /**
     * 
     * @type {string}
     * @memberof ISanityNested
     */
    'apiHost': string;
    /**
     * 
     * @type {string}
     * @memberof ISanityNested
     */
    'apiVersion': string;
    /**
     * 
     * @type {number}
     * @memberof ISanityNested
     */
    'totalDocs': number;
}
/**
 * 
 * @export
 * @interface IServerNested
 */
export interface IServerNested {
    /**
     * 
     * @type {string}
     * @memberof IServerNested
     */
    'api': string;
    /**
     * 
     * @type {string}
     * @memberof IServerNested
     */
    'environment': string;
    /**
     * 
     * @type {string}
     * @memberof IServerNested
     */
    'dateTime': string;
    /**
     * 
     * @type {ISanityNested}
     * @memberof IServerNested
     */
    'sanityObj': ISanityNested;
}
/**
 * 
 * @export
 * @interface ISitemapPagesGames
 */
export interface ISitemapPagesGames {
    /**
     * 
     * @type {string}
     * @memberof ISitemapPagesGames
     */
    'gameId': string;
    /**
     * 
     * @type {string}
     * @memberof ISitemapPagesGames
     */
    'title': string;
    /**
     * 
     * @type {string}
     * @memberof ISitemapPagesGames
     */
    'updatedAt': string;
}
/**
 * 
 * @export
 * @interface ISitemapPagesGamesNested
 */
export interface ISitemapPagesGamesNested {
    /**
     * 
     * @type {Array<ISitemapPagesGames>}
     * @memberof ISitemapPagesGamesNested
     */
    'itemsArr': Array<ISitemapPagesGames>;
}
/**
 * 
 * @export
 * @interface ISitemapPagesNews
 */
export interface ISitemapPagesNews {
    /**
     * 
     * @type {string}
     * @memberof ISitemapPagesNews
     */
    'id': string;
    /**
     * 
     * @type {string}
     * @memberof ISitemapPagesNews
     */
    'title': string;
    /**
     * 
     * @type {string}
     * @memberof ISitemapPagesNews
     */
    'updatedAt': string;
}
/**
 * 
 * @export
 * @interface ISitemapPagesNewsNested
 */
export interface ISitemapPagesNewsNested {
    /**
     * 
     * @type {Array<ISitemapPagesNews>}
     * @memberof ISitemapPagesNewsNested
     */
    'itemsArr': Array<ISitemapPagesNews>;
}
/**
 * 
 * @export
 * @interface ISitemapPagesResponseDto
 */
export interface ISitemapPagesResponseDto {
    /**
     * 
     * @type {ISitemapPagesGamesNested}
     * @memberof ISitemapPagesResponseDto
     */
    'gamesObj': ISitemapPagesGamesNested;
    /**
     * 
     * @type {ISitemapPagesNewsNested}
     * @memberof ISitemapPagesResponseDto
     */
    'newsObj': ISitemapPagesNewsNested;
}
/**
 * 
 * @export
 * @interface ISmtpNested
 */
export interface ISmtpNested {
    /**
     * 
     * @type {string}
     * @memberof ISmtpNested
     */
    'host': string;
}
/**
 * 
 * @export
 * @interface IWebsiteContactUsBodyDto
 */
export interface IWebsiteContactUsBodyDto {
    /**
     * 
     * @type {string}
     * @memberof IWebsiteContactUsBodyDto
     */
    'firstName': string;
    /**
     * 
     * @type {string}
     * @memberof IWebsiteContactUsBodyDto
     */
    'lastName': string;
    /**
     * 
     * @type {string}
     * @memberof IWebsiteContactUsBodyDto
     */
    'company': string;
    /**
     * 
     * @type {string}
     * @memberof IWebsiteContactUsBodyDto
     */
    'email': string;
    /**
     * 
     * @type {string}
     * @memberof IWebsiteContactUsBodyDto
     */
    'message': string;
}
/**
 * 
 * @export
 * @interface IWebsiteContactUsDto
 */
export interface IWebsiteContactUsDto {
    /**
     * 
     * @type {boolean}
     * @memberof IWebsiteContactUsDto
     */
    'status': boolean;
}
/**
 * 
 * @export
 * @interface IWebsiteNewDealerRequestBodyDto
 */
export interface IWebsiteNewDealerRequestBodyDto {
    /**
     * 
     * @type {string}
     * @memberof IWebsiteNewDealerRequestBodyDto
     */
    'name': string;
    /**
     * 
     * @type {string}
     * @memberof IWebsiteNewDealerRequestBodyDto
     */
    'email': string;
    /**
     * 
     * @type {string}
     * @memberof IWebsiteNewDealerRequestBodyDto
     */
    'phone': string;
}
/**
 * 
 * @export
 * @interface IWebsiteNewDealerRequestDto
 */
export interface IWebsiteNewDealerRequestDto {
    /**
     * 
     * @type {boolean}
     * @memberof IWebsiteNewDealerRequestDto
     */
    'status': boolean;
}

/**
 * JackpotsApi - axios parameter creator
 * @export
 */
export const JackpotsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary Get game jackpot for requested criteria
         * @param {'pa' | 'website_eu' | 'website_china' | 'website_asia'} projectCode 
         * @param {'USD' | 'EUR' | 'CNY'} currencyCode 
         * @param {string} gameCode 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        jackpotControllerGetGameJackpot: async (projectCode: 'pa' | 'website_eu' | 'website_china' | 'website_asia', currencyCode: 'USD' | 'EUR' | 'CNY', gameCode: string, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectCode' is not null or undefined
            assertParamExists('jackpotControllerGetGameJackpot', 'projectCode', projectCode)
            // verify required parameter 'currencyCode' is not null or undefined
            assertParamExists('jackpotControllerGetGameJackpot', 'currencyCode', currencyCode)
            // verify required parameter 'gameCode' is not null or undefined
            assertParamExists('jackpotControllerGetGameJackpot', 'gameCode', gameCode)
            const localVarPath = `/v1/jackpots/game/{projectCode}/{currencyCode}/{gameCode}`
                .replace(`{${"projectCode"}}`, encodeURIComponent(String(projectCode)))
                .replace(`{${"currencyCode"}}`, encodeURIComponent(String(currencyCode)))
                .replace(`{${"gameCode"}}`, encodeURIComponent(String(gameCode)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Get total jackpot for requested criteria
         * @param {'pa' | 'website_eu' | 'website_china' | 'website_asia'} projectCode 
         * @param {'USD' | 'EUR' | 'CNY'} currencyCode 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        jackpotControllerGetTotalJackpot: async (projectCode: 'pa' | 'website_eu' | 'website_china' | 'website_asia', currencyCode: 'USD' | 'EUR' | 'CNY', options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'projectCode' is not null or undefined
            assertParamExists('jackpotControllerGetTotalJackpot', 'projectCode', projectCode)
            // verify required parameter 'currencyCode' is not null or undefined
            assertParamExists('jackpotControllerGetTotalJackpot', 'currencyCode', currencyCode)
            const localVarPath = `/v1/jackpots/total/{projectCode}/{currencyCode}`
                .replace(`{${"projectCode"}}`, encodeURIComponent(String(projectCode)))
                .replace(`{${"currencyCode"}}`, encodeURIComponent(String(currencyCode)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * JackpotsApi - functional programming interface
 * @export
 */
export const JackpotsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = JackpotsApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary Get game jackpot for requested criteria
         * @param {'pa' | 'website_eu' | 'website_china' | 'website_asia'} projectCode 
         * @param {'USD' | 'EUR' | 'CNY'} currencyCode 
         * @param {string} gameCode 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async jackpotControllerGetGameJackpot(projectCode: 'pa' | 'website_eu' | 'website_china' | 'website_asia', currencyCode: 'USD' | 'EUR' | 'CNY', gameCode: string, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<IHSRJackpotGetTotalResponseDto>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.jackpotControllerGetGameJackpot(projectCode, currencyCode, gameCode, options);
            return createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration);
        },
        /**
         * 
         * @summary Get total jackpot for requested criteria
         * @param {'pa' | 'website_eu' | 'website_china' | 'website_asia'} projectCode 
         * @param {'USD' | 'EUR' | 'CNY'} currencyCode 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async jackpotControllerGetTotalJackpot(projectCode: 'pa' | 'website_eu' | 'website_china' | 'website_asia', currencyCode: 'USD' | 'EUR' | 'CNY', options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<IHSRJackpotGetTotalResponseDto>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.jackpotControllerGetTotalJackpot(projectCode, currencyCode, options);
            return createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration);
        },
    }
};

/**
 * JackpotsApi - factory interface
 * @export
 */
export const JackpotsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = JackpotsApiFp(configuration)
    return {
        /**
         * 
         * @summary Get game jackpot for requested criteria
         * @param {'pa' | 'website_eu' | 'website_china' | 'website_asia'} projectCode 
         * @param {'USD' | 'EUR' | 'CNY'} currencyCode 
         * @param {string} gameCode 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        jackpotControllerGetGameJackpot(projectCode: 'pa' | 'website_eu' | 'website_china' | 'website_asia', currencyCode: 'USD' | 'EUR' | 'CNY', gameCode: string, options?: any): AxiosPromise<IHSRJackpotGetTotalResponseDto> {
            return localVarFp.jackpotControllerGetGameJackpot(projectCode, currencyCode, gameCode, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Get total jackpot for requested criteria
         * @param {'pa' | 'website_eu' | 'website_china' | 'website_asia'} projectCode 
         * @param {'USD' | 'EUR' | 'CNY'} currencyCode 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        jackpotControllerGetTotalJackpot(projectCode: 'pa' | 'website_eu' | 'website_china' | 'website_asia', currencyCode: 'USD' | 'EUR' | 'CNY', options?: any): AxiosPromise<IHSRJackpotGetTotalResponseDto> {
            return localVarFp.jackpotControllerGetTotalJackpot(projectCode, currencyCode, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * JackpotsApi - object-oriented interface
 * @export
 * @class JackpotsApi
 * @extends {BaseAPI}
 */
export class JackpotsApi extends BaseAPI {
    /**
     * 
     * @summary Get game jackpot for requested criteria
     * @param {'pa' | 'website_eu' | 'website_china' | 'website_asia'} projectCode 
     * @param {'USD' | 'EUR' | 'CNY'} currencyCode 
     * @param {string} gameCode 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof JackpotsApi
     */
    public jackpotControllerGetGameJackpot(projectCode: 'pa' | 'website_eu' | 'website_china' | 'website_asia', currencyCode: 'USD' | 'EUR' | 'CNY', gameCode: string, options?: AxiosRequestConfig) {
        return JackpotsApiFp(this.configuration).jackpotControllerGetGameJackpot(projectCode, currencyCode, gameCode, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Get total jackpot for requested criteria
     * @param {'pa' | 'website_eu' | 'website_china' | 'website_asia'} projectCode 
     * @param {'USD' | 'EUR' | 'CNY'} currencyCode 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof JackpotsApi
     */
    public jackpotControllerGetTotalJackpot(projectCode: 'pa' | 'website_eu' | 'website_china' | 'website_asia', currencyCode: 'USD' | 'EUR' | 'CNY', options?: AxiosRequestConfig) {
        return JackpotsApiFp(this.configuration).jackpotControllerGetTotalJackpot(projectCode, currencyCode, options).then((request) => request(this.axios, this.basePath));
    }
}


/**
 * ProjectsApi - axios parameter creator
 * @export
 */
export const ProjectsApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary Get project status
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        projectControllerGetProjectHealth: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/projects/health`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * ProjectsApi - functional programming interface
 * @export
 */
export const ProjectsApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = ProjectsApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary Get project status
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async projectControllerGetProjectHealth(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<IHSRCheckHealthResponseDto>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.projectControllerGetProjectHealth(options);
            return createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration);
        },
    }
};

/**
 * ProjectsApi - factory interface
 * @export
 */
export const ProjectsApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = ProjectsApiFp(configuration)
    return {
        /**
         * 
         * @summary Get project status
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        projectControllerGetProjectHealth(options?: any): AxiosPromise<IHSRCheckHealthResponseDto> {
            return localVarFp.projectControllerGetProjectHealth(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * ProjectsApi - object-oriented interface
 * @export
 * @class ProjectsApi
 * @extends {BaseAPI}
 */
export class ProjectsApi extends BaseAPI {
    /**
     * 
     * @summary Get project status
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof ProjectsApi
     */
    public projectControllerGetProjectHealth(options?: AxiosRequestConfig) {
        return ProjectsApiFp(this.configuration).projectControllerGetProjectHealth(options).then((request) => request(this.axios, this.basePath));
    }
}


/**
 * StorageApi - axios parameter creator
 * @export
 */
export const StorageApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary Save data from website contact us form to storage.
         * @param {IWebsiteContactUsBodyDto} iWebsiteContactUsBodyDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        storageControllerPostWebsiteContactUs: async (iWebsiteContactUsBodyDto: IWebsiteContactUsBodyDto, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'iWebsiteContactUsBodyDto' is not null or undefined
            assertParamExists('storageControllerPostWebsiteContactUs', 'iWebsiteContactUsBodyDto', iWebsiteContactUsBodyDto)
            const localVarPath = `/v1/storage/website/contact-us`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(iWebsiteContactUsBodyDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary Save data from website form \"Welcome dealer\" to storage.
         * @param {IWebsiteNewDealerRequestBodyDto} iWebsiteNewDealerRequestBodyDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        storageControllerPostWebsiteNewDealerRequest: async (iWebsiteNewDealerRequestBodyDto: IWebsiteNewDealerRequestBodyDto, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'iWebsiteNewDealerRequestBodyDto' is not null or undefined
            assertParamExists('storageControllerPostWebsiteNewDealerRequest', 'iWebsiteNewDealerRequestBodyDto', iWebsiteNewDealerRequestBodyDto)
            const localVarPath = `/v1/storage/website/new-dealer-request`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(iWebsiteNewDealerRequestBodyDto, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * StorageApi - functional programming interface
 * @export
 */
export const StorageApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = StorageApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary Save data from website contact us form to storage.
         * @param {IWebsiteContactUsBodyDto} iWebsiteContactUsBodyDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async storageControllerPostWebsiteContactUs(iWebsiteContactUsBodyDto: IWebsiteContactUsBodyDto, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<IHSRWebsiteContactUsResponseDto>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.storageControllerPostWebsiteContactUs(iWebsiteContactUsBodyDto, options);
            return createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration);
        },
        /**
         * 
         * @summary Save data from website form \"Welcome dealer\" to storage.
         * @param {IWebsiteNewDealerRequestBodyDto} iWebsiteNewDealerRequestBodyDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async storageControllerPostWebsiteNewDealerRequest(iWebsiteNewDealerRequestBodyDto: IWebsiteNewDealerRequestBodyDto, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<IHSRWebsiteNewDealerRequestResponseDto>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.storageControllerPostWebsiteNewDealerRequest(iWebsiteNewDealerRequestBodyDto, options);
            return createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration);
        },
    }
};

/**
 * StorageApi - factory interface
 * @export
 */
export const StorageApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = StorageApiFp(configuration)
    return {
        /**
         * 
         * @summary Save data from website contact us form to storage.
         * @param {IWebsiteContactUsBodyDto} iWebsiteContactUsBodyDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        storageControllerPostWebsiteContactUs(iWebsiteContactUsBodyDto: IWebsiteContactUsBodyDto, options?: any): AxiosPromise<IHSRWebsiteContactUsResponseDto> {
            return localVarFp.storageControllerPostWebsiteContactUs(iWebsiteContactUsBodyDto, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary Save data from website form \"Welcome dealer\" to storage.
         * @param {IWebsiteNewDealerRequestBodyDto} iWebsiteNewDealerRequestBodyDto 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        storageControllerPostWebsiteNewDealerRequest(iWebsiteNewDealerRequestBodyDto: IWebsiteNewDealerRequestBodyDto, options?: any): AxiosPromise<IHSRWebsiteNewDealerRequestResponseDto> {
            return localVarFp.storageControllerPostWebsiteNewDealerRequest(iWebsiteNewDealerRequestBodyDto, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * StorageApi - object-oriented interface
 * @export
 * @class StorageApi
 * @extends {BaseAPI}
 */
export class StorageApi extends BaseAPI {
    /**
     * 
     * @summary Save data from website contact us form to storage.
     * @param {IWebsiteContactUsBodyDto} iWebsiteContactUsBodyDto 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof StorageApi
     */
    public storageControllerPostWebsiteContactUs(iWebsiteContactUsBodyDto: IWebsiteContactUsBodyDto, options?: AxiosRequestConfig) {
        return StorageApiFp(this.configuration).storageControllerPostWebsiteContactUs(iWebsiteContactUsBodyDto, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary Save data from website form \"Welcome dealer\" to storage.
     * @param {IWebsiteNewDealerRequestBodyDto} iWebsiteNewDealerRequestBodyDto 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof StorageApi
     */
    public storageControllerPostWebsiteNewDealerRequest(iWebsiteNewDealerRequestBodyDto: IWebsiteNewDealerRequestBodyDto, options?: AxiosRequestConfig) {
        return StorageApiFp(this.configuration).storageControllerPostWebsiteNewDealerRequest(iWebsiteNewDealerRequestBodyDto, options).then((request) => request(this.axios, this.basePath));
    }
}


/**
 * WebSiteApi - axios parameter creator
 * @export
 */
export const WebSiteApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary Get sitemap pages
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        webSiteControllerSitemapPages: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/v1/web-site/sitemap/pages`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * WebSiteApi - functional programming interface
 * @export
 */
export const WebSiteApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = WebSiteApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary Get sitemap pages
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async webSiteControllerSitemapPages(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<IHSRSitemapPagesResponseDto>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.webSiteControllerSitemapPages(options);
            return createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration);
        },
    }
};

/**
 * WebSiteApi - factory interface
 * @export
 */
export const WebSiteApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = WebSiteApiFp(configuration)
    return {
        /**
         * 
         * @summary Get sitemap pages
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        webSiteControllerSitemapPages(options?: any): AxiosPromise<IHSRSitemapPagesResponseDto> {
            return localVarFp.webSiteControllerSitemapPages(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * WebSiteApi - object-oriented interface
 * @export
 * @class WebSiteApi
 * @extends {BaseAPI}
 */
export class WebSiteApi extends BaseAPI {
    /**
     * 
     * @summary Get sitemap pages
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof WebSiteApi
     */
    public webSiteControllerSitemapPages(options?: AxiosRequestConfig) {
        return WebSiteApiFp(this.configuration).webSiteControllerSitemapPages(options).then((request) => request(this.axios, this.basePath));
    }
}


