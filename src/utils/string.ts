export function replaceObject(findReplace: { [index: string]: string }, replaceString: string): string {
    let regex;
    if (typeof findReplace === "object" && findReplace !== null && Object.keys(findReplace).length > 0) {
        for (const property in findReplace) {
            if (findReplace.hasOwnProperty(property)) {
                regex = new RegExp(property, "g");
                replaceString = replaceString.replace(regex, findReplace[property]);
            }
        }
    }
    return replaceString;
}
