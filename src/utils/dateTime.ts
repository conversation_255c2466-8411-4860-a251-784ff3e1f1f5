/**
 * Await specified amount of time in milliseconds.
 * @param {number} timeMs
 * @returns {Promise<void>}
 */
import moment from "moment";

export async function sleep(timeMs: number): Promise<void> {
    return new Promise<void>((resolve) => {
        setTimeout(resolve, timeMs);
    });
}

export const isDateAvailable = (date: string, fromDate: string, toDate: string): boolean => {
    // const flag = false;

    const mDate = moment(date);
    // const mFromDate = moment(fromDate);
    // const mToDate = moment(toDate);
    //

    return mDate.isBetween(fromDate, toDate, undefined, "[]");
};
