import {
    isAndroid,
    isChrome,
    isChromium,
    isDesktop,
    isEdge,
    isFirefox,
    isIE,
    isIOS,
    isMobile,
    isMobileOnly,
    isOpera,
    isSafari,
    isTablet,
    isWinPhone,
    isYandex,
} from "react-device-detect";

export class DetectDevice {
    public static setDomClasses(domEl: HTMLElement, isLandscape: boolean, isPortrait: boolean) {
        const className: string[] = [];
        if (isMobileOnly || isMobile) {
            className.push("device-mobile");
        }
        if (isTablet) {
            className.push("device-tablet");
        }
        if (isDesktop) {
            className.push("device-desktop");
        }
        if (isAndroid) {
            className.push("device-os-android");
        }
        if (isWinPhone) {
            className.push("device-os-windows");
        }
        if (isIOS) {
            className.push("device-os-ios");
        }
        if (isChrome) {
            className.push("browser-chrome");
        }
        if (isFirefox) {
            className.push("browser-firefox");
        }
        if (isSafari) {
            className.push("browser-safari");
        }
        if (isOpera) {
            className.push("browser-opera");
        }
        if (isIE) {
            className.push("browser-ie");
        }
        if (isEdge) {
            className.push("browser-edge");
        }
        if (isYandex) {
            className.push("browser-yandex");
        }
        if (isChromium) {
            className.push("browser-chromium");
        }
        if (isLandscape || window.matchMedia("(orientation: landscape)").matches) {
            domEl.classList.remove("in-portrait");
            className.push("in-landscape");
        }
        if (isPortrait || window.matchMedia("(orientation: portrait)").matches) {
            domEl.classList.remove("in-landscape");
            className.push("in-portrait");
        }

        if (domEl.classList) {
            className.map((cls) => {
                domEl.classList.add(cls);
            });
        }
    }
}
