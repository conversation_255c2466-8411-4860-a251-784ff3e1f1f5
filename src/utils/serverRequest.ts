import { NextApiRequest, NextApiResponse } from "next";
import { EnumRequestMethods } from "models/enum/http";
import { match } from "path-to-regexp";
import { TokenService } from "api/services/tokenService";
import { logging } from "utils/logging/logging";

const requestIp = require("request-ip");

export class ServerRequest {
    public req: NextApiRequest;
    public res: NextApiResponse;
    public log: logging.appLogger;
    public controller: unknown;
    public executed: boolean = false;
    public apiPath: string;

    constructor(
        controller: { new (serverRequest: ServerRequest): void },
        req: NextApiRequest,
        res: NextApiResponse,
        log: logging.appLogger,
        apiPath?: string
    ) {
        this.req = req;
        this.res = res;
        this.log = log;
        this.controller = new controller(this);
        this.apiPath = apiPath as string;
    }

    public static getUserIp(req: NextApiRequest): string {
        // return "*************";
        return requestIp.getClientIp(req) || "::ffff:127.0.0.1";
    }

    public static getUserAgent(req: NextApiRequest): string {
        return req.headers["user-agent"] as string;
    }

    public static getUrl(req: NextApiRequest): URL {
        return new URL(req.url as string, `https://${req.headers.host}`);
    }

    public async httpMethod(
        reqMethod: EnumRequestMethods,
        path: string,
        fn: Function,
        withAuthorisation: boolean
    ): Promise<void> {
        if (this.executed) {
            return;
        }
        const { method } = this.req;
        if (method === reqMethod) {
            const url = ServerRequest.getUrl(this.req);
            const regex = match((this.apiPath || "") + path, { decode: decodeURIComponent });
            const result = regex(url.pathname);
            if (result && result?.params) {
                this.executed = true;
                if (withAuthorisation && !ServerRequest.isAuthorised(this.req)) {
                    this.res.status(401).end();
                    return;
                }
                return await fn.call(this.controller, result.params);
            }
        }
    }

    public async httpGet(path: string, fn: Function, withAuthorisation: boolean): Promise<void> {
        return await this.httpMethod(EnumRequestMethods.GET, path, fn, withAuthorisation);
    }

    public async httpPost(path: string, fn: Function, withAuthorisation: boolean): Promise<void> {
        return await this.httpMethod(EnumRequestMethods.POST, path, fn, withAuthorisation);
    }

    public async httpDelete(path: string, fn: Function, withAuthorisation: boolean): Promise<void> {
        return await this.httpMethod(EnumRequestMethods.DELETE, path, fn, withAuthorisation);
    }

    public async httpPut(path: string, fn: Function, withAuthorisation: boolean): Promise<void> {
        return await this.httpMethod(EnumRequestMethods.PUT, path, fn, withAuthorisation);
    }

    public async httpPatch(path: string, fn: Function, withAuthorisation: boolean): Promise<void> {
        return await this.httpMethod(EnumRequestMethods.PATCH, path, fn, withAuthorisation);
    }

    public async httpEnd() {
        if (!this.executed) {
            this.res.status(404).end("Unknown API Request Error: " + this.req.method + " " + this.req.url);
        }
    }

    public static getAccessToken(req: NextApiRequest): string | boolean {
        const authorizationHeader = String(req.headers?.authorization);
        if (!authorizationHeader) {
            return false;
        }

        const accessToken = authorizationHeader.split(" ")[1];
        if (!accessToken) {
            return false;
        }
        return accessToken;
    }

    public static isAuthorised(req: NextApiRequest): boolean {
        try {
            const accessToken = ServerRequest.getAccessToken(req);
            if (!accessToken) {
                return false;
            }

            const userData = TokenService.validateAccessToken(<string>accessToken);
            if (!userData) {
                return false;
            }
        } catch (e) {
            return false;
        }
        return true;
    }
}
