import { Transform, Writable } from "stream";

import { sleep } from "../dateTime";
import { logging } from "./logging";

/**
 * The structure that bole emits to the outputStream
 */
export interface LogRecord {
    name: string;
    hostName: string;
    time: number;
    message: any;
    level: string;
    err?: Error;
}

/**
 * Utility interface to map the output record to the actual representation that must be written to the outputstream
 */
export interface MessageMapper<T = any> {
    map(log: LogRecord): T;
}

export type LoggingStream = Writable & {
    // @ts-ignore
    close?(cb: (err?: Error) => void);
};

/**
 * Logging stream factory
 */
export type StreamFactory = (config: logging.OutputConfig) => Writable;

/**
 *  Logging output stream.
 *  It transforms data from by using mapper and ouputs to the underlying stream
 */
export class LoggingOutput extends Transform {
    // @ts-ignore
    private stream: LoggingStream;

    constructor(
        private readonly streamFactory: StreamFactory,
        private readonly outputConfig: logging.StreamOutputConfig,
        private readonly messageMapper: MessageMapper
    ) {
        super({ objectMode: true, highWaterMark: outputConfig.highWaterMark });
        this.initStream();
    }

    public initStream() {
        if (this.stream) {
            if (this.stream.close) {
                this.stream.close((err) => {
                    if (err) {
                        console.error(err);
                    }
                });
            }
            this.stream.destroy();
        }
        this.stream = this.streamFactory(this.outputConfig);
        this.pipe(this.stream);
        this.stream.on("error", async (error) => {
            console.error(error);
            this.unpipe(this.stream);
            await sleep(this.outputConfig.recreateStreamTimeout as number);
            this.initStream();
        });
    }

    public _transform(
        chunk: any,
        encoding: string,
        cb?: (error: Error | null | undefined, msg: { topic: string; messages: any }) => void
    ): boolean {
        if (cb) {
            cb(undefined, this.messageMapper.map(chunk));
        }
        return true;
    }
}
