import { ProducerStream } from "kafka-node";

import { GelfMessageMapper } from "./gelfMapper";
import { logging } from "./logging";
import { LoggingOutput, LogRecord, MessageMapper, StreamFactory } from "./outputStream";

/**
 *  Create kafka producer stream
 * @param outputConfig
 */
export function createStream(outputConfig: logging.KafkaOutputConfig) {
    return new ProducerStream({
        highWaterMark: outputConfig.highWaterMark,
        kafkaClient: {
            kafkaHost: outputConfig.kafkaHost,
            autoConnect: true,
            connectRetryOptions: { forever: true, minTimeout: 100, maxTimeout: 10000 } as any,
            requestTimeout: outputConfig.requestTimeout,
            connectTimeout: outputConfig.connectionTimeout,
        },
        producer: {
            requireAcks: outputConfig.requireAcks,
            ackTimeoutMs: outputConfig.ackTimeoutMs,
            partitionerType: outputConfig.partitionerType,
        },
    });
}

/**
 * The record that should finally go to the kafka producer stream
 */
export interface KafkaLogRecord {
    topic: string;
    messages: any;
}

/**
 * Kafka output record, to map message to specific topic to send
 */
export class KafkaMessageMapper implements MessageMapper<KafkaLogRecord> {
    constructor(private readonly config: logging.KafkaOutputConfig, private readonly origin: MessageMapper) {}

    public map(log: LogRecord): { topic: string; messages: any } {
        return { topic: this.config.topic as string, messages: JSON.stringify(this.origin.map(log)) };
    }
}

export function createKafkaOutput(config: logging.KafkaOutputConfig) {
    return new LoggingOutput(
        createStream as StreamFactory,
        config,
        new KafkaMessageMapper(config, new GelfMessageMapper())
    );
}
