const bole = require("bole");

type LogLevel = "info" | "error" | "warn" | "debug";
export namespace logging {
    let rootName = "";

    export function logger(name: string): appLogger {
        return new appLogger(bole(rootName ? `${rootName}:${name}` : name));
    }

    export class appLogger {
        private readonly boleLogger;

        constructor(boleLogger: any) {
            this.boleLogger = boleLogger;
        }

        public debug(...args: any[]): appLogger {
            return this.log("debug", args);
        }

        public error(...args: any[]): appLogger {
            return this.log("error", args);
        }

        public info(...args: any[]): appLogger {
            return this.log("info", args);
        }

        public warn(...args: any[]): appLogger {
            return this.log("warn", args);
        }

        private log(level: LogLevel, args: any[]) {
            // const traceID = measureProvider.getTraceID();
            // if (traceID) {
            //     if (args.length > 0 && typeof args[0] === "object") {
            //         const first = args[0] instanceof Error ? { err: errAsObject(args[0] as any) } : args[0];
            //         args[0] = this.setTransferableFields({ ...first, "sw-trace-id": traceID });
            //     } else {
            //         newArgs = [this.setTransferableFields({ "sw-trace-id": traceID })].concat(args);
            //     }
            // }
            this.boleLogger[level](...args);
            return this;
        }
    }

    export interface OutputConfig {
        type: "console" | "graylog" | "kafka";
        logLevel: string;
    }

    export interface GrayLogOutputConfig extends OutputConfig {
        type: "graylog";
        host: string;
        port: number;
    }

    export interface StreamOutputConfig extends OutputConfig {
        highWaterMark?: number;
        recreateStreamTimeout?: number;
    }

    // @ts-ignore
    export interface KafkaOutputConfig extends StreamOutputConfig {
        kafkaHost?: string;
        requestTimeout?: number;
        connectionTimeout?: number;
        requireAcks?: number;
        ackTimeoutMs?: number;
        partitionerType?: number;
        topic?: string;
        type: "kafka";
    }
}
