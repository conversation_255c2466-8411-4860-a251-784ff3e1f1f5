import { LogRecord, MessageMapper } from "./outputStream";

/**
 *  Transform log record to the gelf representation
 */
export class GelfMessageMapper implements MessageMapper<any> {
    public map(log: LogRecord): any {
        const msg: any = {
            host: log.hostName,
            timestamp: +new Date(log.time) / 1000,
            short_message: log.message,
            facility: log.name,
            level: log.level,
            full_message: JSON.stringify(log, null, 2),
        };

        if (log.err) {
            msg.short_message = log.err.message;
        }

        const errFile = log.err && log.err.stack && /\n\s+at .+ \(([^:]+):([0-9]+)/.exec(log.err.stack);
        if (errFile && errFile.length >= 3) {
            if (errFile[1]) {
                msg.file = errFile[1];
            }
            if (errFile[2]) {
                msg.line = errFile[2];
            }
        }

        return msg;
    }
}
