import { IMarketingKitResponse } from "api/services/interfaces/iGamesService";
import { JsUtils } from "utils/js-utils";

export interface IMarketingKitTree {
    name: string;
    path: string;
    open: boolean;
    level: number;
    type: "file" | "folder";
    items: IMarketingKitTree[];
}

export class MarketingKitUtils {
    public static parseFromSanityToTree(
        data: IMarketingKitResponse,
        excludeFiles: string[]
    ): IMarketingKitTree[] | null {
        const tree: IMarketingKitTree[] = [];
        if (JsUtils.isObjectAndNotEmpty(data?.marketingKit)) {
            const paths: string[] = Array.prototype.concat.apply([], Object.values(data.marketingKit)).sort();

            paths.forEach((filePath) => {
                const builder = (filePath: string, names: string[], index: number, treeLink: IMarketingKitTree[]) => {
                    const l = names.length;
                    const type = l - 1 === index ? "file" : "folder";
                    const name = names[index];
                    const path = "/" + names.slice(0, index).join("/");

                    let fi = treeLink.findIndex((i) => i.name === names[index]);
                    if (fi === -1) {
                        if (type === "folder" || !excludeFiles.includes(name.toLowerCase())) {
                            treeLink.push({ name, path, open: false, level: l - 1, type, items: [] });
                        }
                        fi = treeLink.length - 1;
                    }
                    if (index < l - 1) {
                        builder(filePath, names, index + 1, treeLink[fi].items);
                    }
                };
                builder(filePath, filePath.split("/"), 0, tree);
            });
            return tree;
        }
        return null;
    }
}
