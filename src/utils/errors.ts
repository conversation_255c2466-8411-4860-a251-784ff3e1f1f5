import { replaceObject } from "./string";
import { EnumError } from "locales/enumLocale";
import { EnumLocale, getLocaleMessages } from "locales/locales";
import { EnumServerResponseType, ResponseError } from "./serverResponse";
import { EnumErrorLevel } from "models/enum/http";

export interface ErrorObject {
    status?: number;
    type?: EnumServerResponseType;
    code: number;
    message: string;
    replace: { [index: string]: string };
}

export interface Error400 extends ErrorObject {
    status400: boolean;
}

export interface Error401 extends ErrorObject {
    status401: boolean;
}

export interface Error403 extends ErrorObject {
    status403: boolean;
}

export interface Error404 extends ErrorObject {
    status404: boolean;
}

export interface Error409 extends ErrorObject {
    status409: boolean;
}

export interface Error412 extends ErrorObject {
    status412: boolean;
}

export interface Error500 extends ErrorObject {
    status500: boolean;
}

export function error400(
    errCode: number,
    errMessage: EnumError,
    type?: string,
    replace?: { [index: string]: string }
): Error400 {
    return {
        status400: true,
        code: errCode,
        type,
        message: errMessage,
        replace,
    } as Error400;
}

export function error401(errCode: number, errMessage: EnumError, replace?: { [index: string]: string }): Error401 {
    return {
        status401: true,
        code: errCode,
        message: errMessage,
        replace,
    } as Error401;
}

export function error403(errCode: number, errMessage: EnumError, replace?: { [index: string]: string }): Error403 {
    return {
        status403: true,
        code: errCode,
        message: errMessage,
        replace,
    } as Error403;
}

export function error404(errCode: number, errMessage: EnumError, replace?: { [index: string]: string }): Error404 {
    return {
        status404: true,
        code: errCode,
        message: errMessage,
        replace,
    } as Error404;
}

export function error409(errCode: number, errMessage: EnumError, replace?: { [index: string]: string }): Error409 {
    return {
        status409: true,
        code: errCode,
        message: errMessage,
        replace,
    } as Error409;
}

export function error412(errCode: number, errMessage: EnumError, replace?: { [index: string]: string }): Error412 {
    return {
        status412: true,
        code: errCode,
        message: errMessage,
        replace,
    } as Error412;
}

export class Unauthorized extends ResponseError {
    constructor(error: Error401) {
        const messages = getLocaleMessages(EnumLocale.enUS).errors;
        error.message = replaceObject(
            error.replace,
            error.message in messages ? messages[error.message as never] : error.message
        );
        super(401, error.code, error.message, EnumErrorLevel.error, EnumServerResponseType.serviceError);
    }
}

export class Forbidden extends ResponseError {
    constructor(error: Error403) {
        const messages = getLocaleMessages(EnumLocale.enUS).errors;
        error.message = replaceObject(
            error.replace,
            error.message in messages ? messages[error.message as never] : error.message
        );
        super(403, error.code, error.message, EnumErrorLevel.error, EnumServerResponseType.serviceError);
    }
}

export class NotFound extends ResponseError {
    constructor(error: Error404) {
        const messages = getLocaleMessages(EnumLocale.enUS).errors;
        error.message = replaceObject(
            error.replace,
            error.message in messages ? messages[error.message as never] : error.message
        );
        super(404, error.code, error.message, EnumErrorLevel.error, EnumServerResponseType.serviceError);
    }
}

export class BadRequest extends ResponseError {
    constructor(error: Error400) {
        const messages = getLocaleMessages(EnumLocale.enUS).errors;
        error.message = replaceObject(
            error.replace,
            error.message in messages ? messages[error.message as never] : error.message
        );
        super(400, error.code, error.message, EnumErrorLevel.warn, error.type || EnumServerResponseType.serviceError);
    }
}
export class Conflict extends ResponseError {
    constructor(error: Error409) {
        const messages = getLocaleMessages(EnumLocale.enUS).errors;
        error.message = replaceObject(
            error.replace,
            error.message in messages ? messages[error.message as never] : error.message
        );
        super(409, error.code, error.message, EnumErrorLevel.warn, EnumServerResponseType.serviceError);
    }
}

export class PreconditionFailed extends ResponseError {
    constructor(error: Error412) {
        const messages = getLocaleMessages(EnumLocale.enUS).errors;
        error.message = replaceObject(
            error.replace,
            error.message in messages ? messages[error.message as never] : error.message
        );
        super(412, error.code, error.message, EnumErrorLevel.warn, EnumServerResponseType.serviceError);
    }
}

export class InternalServerError extends ResponseError {
    // noinspection JSUnusedLocalSymbols
    constructor(error?: Error500) {
        const messages = getLocaleMessages(EnumLocale.enUS).errors;
        super(
            500,
            500,
            messages[EnumError.internalServerError as never], // + " " + JSON.stringify((error as Error)?.message),
            EnumErrorLevel.error,
            EnumServerResponseType.serverError
        );
    }
}
