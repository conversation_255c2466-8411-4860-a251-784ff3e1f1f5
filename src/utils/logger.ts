import { logging } from "./logging/logging";
import { createKafkaOutput } from "./logging/kafkaOutputStream";
import appServerConfig from "appServerConfig";

const bole = require("bole");
const gelfStream = require("gelf-stream");

((): void => {
    let outputStream: { end: () => void };

    const graylogConfig = appServerConfig.logging.graylog;
    const kafkaConfig = appServerConfig.logging.kafka;
    const logType = appServerConfig.logging.loggingOutputType;
    // For temporary and test purposes we are able to send logs directly to graylog
    if (logType === "graylog" && graylogConfig.host && graylogConfig.port) {
        outputStream = gelfStream.forBunyan(graylogConfig.host, graylogConfig.port);
    } else if (logType === "kafka" && kafkaConfig.kafkaHost && kafkaConfig.topic) {
        const kafkaConfig = { ...appServerConfig.logging.kafka, type: logType, logLevel: appServerConfig.logging.logLevel };
        outputStream = createKafkaOutput(kafkaConfig);
    } else {
        const boleConsole = require("bole-console");

        outputStream = boleConsole({
            timestamp: true,
            // requestDetails: true,
        });
    }

    bole.reset(); // reset for next dev
    bole.output([
        {
            level: appServerConfig.logging.logLevel,
            stream: outputStream,
        } /* stream: process.stdout */ /* stream: outputStream */, // stream: fs.createWriteStream('app.log')
    ]);

    process.on("SIGTERM", () => {
        outputStream.end();
        process.exit(0);
    });
})();

export type LoggerFactory = (name?: string) => logging.appLogger;

const defaultRoot = "sw-partner-area";

function loggerFactory(name = ""): logging.appLogger {
    return logging.logger(name ? `${defaultRoot}:${name}` : defaultRoot);
}

export default loggerFactory as LoggerFactory;
