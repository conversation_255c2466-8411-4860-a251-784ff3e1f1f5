import { EnumLanguageCode, EnumProjectType, EnumRegionCode } from "models/enum/system";
import { IEnumLocale } from "locales/enumLocale";
import moment from "moment/moment";
import { IMarketCertificate, IMarketCertificates, IRTPType } from "api/services/interfaces/iGamesService";
import { SanityApi } from "./sanityApi";
import { IGameInfo, ILocaleString } from "api/data-providers/sanity/schemas";
import {
    IGameExtended,
    IGameInfoFromBI,
    IGameVideosExtended,
    IMarketExtended
} from "api/data-providers/sanity/interfaces/iSanityProvider";
import { EnumGameRTP, EnumShowGameCode, EnumShowGameRTP } from "models/enum/game";
import appConfig from "appConfig";

export class Game {
    public static getFirstGameCode(game: IGameExtended): string | null {
        if (Array.isArray(game?.gameCodes) && game?.gameCodes?.length > 0) {
            return String(game?.gameCodes[0].gameCode);
        }
        return null;
    }

    public static parseGame(
        game: IGameExtended,
        languageCode: EnumLanguageCode,
        projectType: EnumProjectType,
        gamePageUrlRelative: string
    ): { gameCode: string, gamePageUrlRelative: string, gameName: string, gameDescription: string, isLiveGameType: boolean } | null {
        const gameCode = Game.getFirstGameCode(game);
        const gameName = SanityApi.getLocale(game?.gameName as ILocaleString, languageCode);
        const gameDescription = SanityApi.getLocale(game?.gameDescription as ILocaleString, languageCode);
        const isLiveGameType = Array.isArray(game?.gameTypes) && game?.gameTypes.filter((g) => g.gameTypeCode?.current === "live").length === 1;

        return gameCode ? { gameCode, gamePageUrlRelative, gameName, gameDescription, isLiveGameType } : null;
    }

    public static getPlayGameCode(gameCodes: string[]): string {
        return Array.isArray(gameCodes) && gameCodes.length > 0 ? gameCodes[0] : "";
    }

    public static getGameCodes(game: IGameExtended): string[] | null {
        const gameCodes = [] as string[];
        if (Array.isArray(game?.gameCodes) && game?.gameCodes.length > 0) {
            for (const gc of game?.gameCodes) {
                if (typeof gc.gameCode === "string") {
                    gameCodes.push(gc.gameCode);
                }
            }
        }
        if (gameCodes.length > 0) {
            return gameCodes;
        }
        return null;
    }

    public static prepareRTPs(
        biRTP: string | null | undefined,
        giRTP: number | null | undefined,
        gmRTP: number[] | null | undefined
    ): IRTPType[] {
        const bi = parseFloat(String(biRTP));
        const gi = parseFloat(String(giRTP));
        const rtp: IRTPType[] = [];
        rtp.push({ type: EnumGameRTP.bi, rtp: isNaN(bi) ? null : bi * 100 });
        rtp.push({ type: EnumGameRTP.gi, rtp: isNaN(gi) ? null : gi });
        if (Array.isArray(gmRTP)) {
            gmRTP.map((v) => {
                rtp.push({ type: EnumGameRTP.gm, rtp: v });
            });
        }
        return rtp;
    }

    public static getGameRTPs(game: IGameExtended): number[] | null {
        const gameRTPs = [] as number[];
        if (Array.isArray(game?.gameRTPExt) && game?.gameRTPExt.length > 0) {
            for (const rtp of game?.gameRTPExt) {
                const v = parseFloat(String(rtp?.gameRTP));
                if (!isNaN(v)) {
                    gameRTPs.push(v);
                }
            }
        }
        if (gameRTPs.length > 0) {
            return gameRTPs;
        }
        return null;
    }

    public static getGamePlayUrl(
        gameCode: string | null,
        // gameUrl: string | null | undefined,
        // regionCode: EnumRegionCode = EnumRegionCode.eu,
        isGameExistsInProvider: boolean = true
    ): string | null {
        const regionCode = appConfig.systemSettings.defaultRegionCode;
        return (
            // gameUrl ||
            (gameCode && isGameExistsInProvider
             ? `https://play.skywindgroup.com/en/play-game/${regionCode}/${gameCode}.html`
             : null)
        );
    }

    public static getMarketingDownloadAllUrl(gameCode: string | null, languageCode: EnumLanguageCode): string {
        return gameCode
               ?
               `https://storage.googleapis.com/lobby.stg1.m27613.com/gamestudios/${gameCode}/${gameCode}_marketing_kit_${languageCode}.zip`
               :
               "#";
    }

    public static getGameCertificates(
        game: IGameExtended,
        languageCode: EnumLanguageCode,
        allMarkets: IMarketExtended[],
        biggestMarkets: IMarketExtended[]
    ): IGameExtended {
        const certificates = {} as IMarketCertificates;
        if (Array.isArray(allMarkets) && Array.isArray(game?.certificatesPerMarket)) {
            for (const crt of game?.certificatesPerMarket) {
                if (crt.certificateUrl || crt.certificate1Url || crt.certificate2Url) {
                    const curMarketCode = String(crt.gameMarket.code?.current);
                    if (crt.certificateUrl) {
                        certificates[curMarketCode + "_0"] = {
                            id: String(crt._key) + "_0",
                            marketCode: curMarketCode,
                            marketName: String(
                                SanityApi.getLocale(crt.gameMarket.marketName as ILocaleString, languageCode)
                            ),
                            fileUrl: crt.certificateUrl,
                            translated: false,
                            comingSoon: crt.comingSoon || null,
                            iconUrl: crt.gameMarket.iconUrl
                        };
                    }
                    if (crt.certificate1Url) {
                        certificates[curMarketCode + "_1"] = {
                            id: String(crt._key) + "_1",
                            marketCode: curMarketCode,
                            marketName: String(
                                SanityApi.getLocale(crt.gameMarket.marketName as ILocaleString, languageCode)
                            ),
                            fileUrl: crt.certificate1Url,
                            translated: true,
                            comingSoon: crt.comingSoon || null,
                            iconUrl: crt.gameMarket.iconUrl
                        };
                    }
                    if (crt.certificate2Url) {
                        certificates[curMarketCode + "_2"] = {
                            id: String(crt._key) + "_2",
                            marketCode: curMarketCode,
                            marketName: String(
                                SanityApi.getLocale(crt.gameMarket.marketName as ILocaleString, languageCode)
                            ),
                            fileUrl: crt.certificate2Url,
                            translated: false,
                            comingSoon: crt.comingSoon || null,
                            iconUrl: crt.gameMarket.iconUrl
                        };
                    }
                }
            }
            let id = 0;
            allMarkets.map((m) => {
                const marketCode = String(m.code?.current);
                if (
                    !certificates[marketCode + "_0"] &&
                    !certificates[marketCode + "_1"] &&
                    !certificates[marketCode + "_2"]
                ) {
                    certificates[marketCode + "_0"] = {
                        id: marketCode + id,
                        marketCode: marketCode,
                        marketName: String(SanityApi.getLocale(m.marketName as ILocaleString, languageCode)),
                        fileUrl: null,
                        translated: false,
                        comingSoon: true,
                        iconUrl: m.iconUrl
                    } as IMarketCertificate;
                    id++;
                }
            });
        }
        game.certificates = Object.keys(certificates).length > 0 ? Object.values(certificates) : null;

        if (Array.isArray(game?.certificates)) {
            game.certificates.sort((a, b) => a.marketName.localeCompare(b.marketName));
            const gc = game.certificates;
            const nc: IMarketCertificate[] = [];
            if (Array.isArray(biggestMarkets)) {
                game.certificates = [];
                biggestMarkets.map((m) => {
                    const marketCode = String(m.code?.current);
                    for (let fi = 0; fi < gc.length; fi++) {
                        if (gc[fi] && marketCode == gc[fi].marketCode) {
                            if (gc[fi].fileUrl && gc[fi].comingSoon !== true) {
                                (game.certificates as IMarketCertificate[]).push(gc[fi]);
                            } else {
                                nc.push(gc[fi]);
                            }
                            gc.splice(fi, 1);
                            fi--;
                        }
                    }
                });
                for (let fi = 0; fi < gc.length; fi++) {
                    if (gc[fi].fileUrl && gc[fi].comingSoon !== true) {
                        (game.certificates as IMarketCertificate[]).push(gc[fi]);
                    } else {
                        nc.push(gc[fi]);
                    }
                }
                if (Array.isArray(nc) && nc.length > 0) {
                    game.certificates = [...game.certificates, ...nc];
                }
            }
        }
        //
        // game.certificates = game?.certificatesPerMarket?.reduce(
        //     (a, v) => ({
        //         ...a,
        //         ...(v.certificateUrl && {
        //             [String(v.gameMarket.code?.current)]: {
        //                 marketCode: String(v.gameMarket.code?.current),
        //                 marketName: String(Game.getLocale(v.gameMarket.marketName as ILocaleString, languageCode)),
        //                 fileUrl: v.certificateUrl,
        //                 translated: false,
        //             },
        //         }),
        //         ...(v.certificate1Url && {
        //             [String(v.gameMarket.code?.current)]: {
        //                 marketCode: String(v.gameMarket.code?.current),
        //                 marketName: String(Game.getLocale(v.gameMarket.marketName as ILocaleString, languageCode)),
        //                 fileUrl: v.certificate1Url,
        //                 translated: true,
        //             },
        //         }),
        //     }),
        //     {}
        // ) as IMarketCertificates;

        return game;
    }

    public static getGameInfoFromBI(game: IGameExtended): IGameInfoFromBI {
        if (
            Array.isArray(game?.gameCodes) &&
            game?.gameCodes.length > 0 &&
            typeof game?.gameCodes[0].gameInfo === "string"
        ) {
            const info = JSON.parse(<string>game?.gameCodes[0].gameInfo);
            if (info.game_features) {
                info.game_features = JSON.parse(<string>info.game_features);
            }
            return info;
        }
        return {} as IGameInfoFromBI;
    }

    public static getGameVideos(game: IGameExtended): IGameVideosExtended[] {
        const gameVideos: IGameVideosExtended[] = [];
        if (game.gameVideo?.videoUrl) {
            gameVideos.push({
                videoUrl: game.gameVideo?.videoUrl,
                languageCode: "enUs"
            });
        }
        if (Array.isArray(game.gameVideosExt)) {
            for (let i = 0; i < game.gameVideosExt.length; i++) {
                gameVideos.push(game.gameVideosExt[i]);
            }
        }
        return gameVideos;
    }

    public static getGameInfo(gameInfo: IGameInfo | null | undefined, languageCode: EnumLanguageCode) {
        const rtp = parseFloat(String(gameInfo?.rtp));
        let releaseDate = null;
        if (gameInfo) {
            const { releaseDates } = gameInfo;
            const validReleaseDates = Array.isArray(releaseDates) && releaseDates.length > 0 ? releaseDates : null;
            if (validReleaseDates) {
                const com = validReleaseDates?.filter((v) => v?.gameMarket?.code?._key === "COM");
                if (Array.isArray(com) && com.length === 1) {
                    releaseDate = com[0]?.releaseDate;
                } else {
                    releaseDate = validReleaseDates[0]?.releaseDate;
                }
            }
        }
        const markets = Array.isArray(gameInfo?.markets)
                        ? gameInfo?.markets.map(
                m => SanityApi.getLocale(m?.marketName as ILocaleString, languageCode)
            ).join(", ")
                        : null;

        return {
            rtp: !isNaN(rtp) ? rtp : null,
            volatility: gameInfo?.volatility || null,
            ways: typeof gameInfo?.ways === "string" ? gameInfo?.ways.trim() : null,
            layout: typeof gameInfo?.layout === "string" ? gameInfo?.layout.trim() : null,
            releaseDate,
            formatReleaseDate: gameInfo?.formatOfReleaseDate || null,
            markets,
            engTools: {
                isFreeBets: gameInfo?.isFreeBets !== false,
                isTournaments: gameInfo?.isTournaments !== false,
                isLuckyEnvelopes: gameInfo?.isLuckyEnvelopes !== false,
                isMustWinJackpot: gameInfo?.isMustWinJackpot !== false
            }
        };
    }

    public static formatRTPSingle(value: string | number, msg: IEnumLocale, postfix = "%") {
        const rtp = parseFloat(String(value));
        if (value !== null && rtp && !isNaN(rtp) && rtp > 0) {
            return rtp.toFixed(2) + postfix || "";
        }
        return String(msg.notApplicable);
    }

    public static formatGameCodes(gameCodes: string[], showGameCode: EnumShowGameCode, separator = " "): string {
        if (Array.isArray(gameCodes) && gameCodes.length > 0) {
            if (showGameCode === EnumShowGameCode.first_one) {
                return gameCodes.slice(0, 1).join(separator);
            } else if (showGameCode === EnumShowGameCode.all_without_first) {
                return gameCodes.slice(1).join(separator);
            }
            return gameCodes.join(separator);
        }
        return "";
    }

    public static formatRTP(
        rtp: IRTPType[],
        showGameRTP: EnumShowGameRTP | undefined,
        msg: IEnumLocale,
        separator = " ",
        postfix = "%"
    ): string {
        if (Array.isArray(rtp) && rtp.length > 0) {
            let biRTP: string | null = null;
            let giRTP: string | null = null;
            const gmRTP: string[] = [];
            for (const RTPType of rtp) {
                if (RTPType.rtp) {
                    const rtp = Game.formatRTPSingle(String(RTPType.rtp), msg, postfix);
                    if (RTPType.type === EnumGameRTP.bi) {
                        biRTP = rtp;
                    }
                    if (RTPType.type === EnumGameRTP.gi) {
                        giRTP = rtp;
                    }
                    if (RTPType.type === EnumGameRTP.gm) {
                        gmRTP.push(rtp);
                    }
                }
            }
            if ((!showGameRTP && biRTP) || showGameRTP === EnumShowGameRTP.from_bi) {
                return String(biRTP);
            }
            if ((!showGameRTP && !biRTP && giRTP) || showGameRTP === EnumShowGameRTP.from_game_info) {
                return String(giRTP || biRTP);
            }
            if (showGameRTP === EnumShowGameRTP.from_game_rtp) {
                return gmRTP.join(separator);
            }
        }
        return "";
    }

    public static formatVolatility(value: string, msg: IEnumLocale, postfix = "/5"): string {
        const vol = parseInt(value);
        if (value !== null && vol && !isNaN(vol) && vol > 0) {
            return vol.toFixed(0) + postfix || "";
        }
        if (value !== null && value && value.trim() !== "") {
            return value;
        }
        return String(msg.notApplicable);
    }

    public static formatWays(value: string, msg: IEnumLocale): string {
        if (["string", "number"].includes(typeof value)) {
            return String(value).trim();
        }
        // const ways = parseInt(value);
        // if (value !== null && ways && !isNaN(ways) && ways > 0) {
        //     return ways.toFixed(0);
        // }
        return String(msg.notApplicable);
    }

    public static formatLayout(value: string, msg: IEnumLocale): string {
        if (value !== null && value && value.trim() !== "") {
            return value;
        }
        return String(msg.notApplicable);
    }

    public static formatReleaseDate(
        value: string,
        msg: IEnumLocale,
        inputMomentFormat: string,
        outputMomentFormat: string
    ): string {
        const date = moment(value, inputMomentFormat);
        if (value !== null && date.isValid()) {
            return date.format(outputMomentFormat);
        }
        return String(msg.notApplicable);
    }
}
