import packageJson from "./../../package.json";
import moment from "moment";
import appConfig from "appConfig";
import { IConfigProject, IConfigProjectDomain } from "iAppConfig";
import { EnumProjectCode } from "models/enum/system";
import { AppLocales, EnumLocale } from "locales/locales";
import { JsUtils } from "utils/js-utils";
import { IProjectPageInfo, IProjectPages } from "models/site-pages";
import { sitePages } from "data/site/site-pages";
import { IAppStore } from "@stores/app-store";
import { paPages } from "data/pa/pa-pages";
import { EnumPage } from "models/enum/page";

export class Project {
    public static getVersion() {
        return packageJson.version;
    }

    public static getPageUrl(
        appStore: IAppStore,
        page: EnumPage,
        params?: { [name: string]: string },
        hostParams?: { hostname: string, schema: string }
    ): string {
        const pages = appStore.getAppProjectPages();
        if (!appStore.getAppProject().type) {
            return "/";
        }
        const locale = appStore.getAppLocale();
        //        console.log(page, appStore.getAppProject().type)

        let nPath = pages[page].path;
        if (params) {
            for (const p in params) {
                nPath = nPath.replace(":" + p, params[p]);
            }
        }
        return (hostParams ? hostParams.schema + hostParams.hostname : "") + (locale ? "/" + locale : "") + nPath;
    }

    public static getPageLocaleUrl(locale: EnumLocale,
                                   path: string,
                                   params?: { [name: string]: string },
                                   hostParams?: { hostname: string, schema: string }): string {
        let nPath = path;
        if (params) {
            for (const p in params) {
                nPath = nPath.replace(":" + p, params[p]);
            }
        }
        return (hostParams ? hostParams.schema + hostParams.hostname : "") + (locale ? "/" + locale : "") + nPath;
    }

    public static getSitePageTitle(locale: EnumLocale, pageInfo: IProjectPageInfo): string {
        return pageInfo.title[locale] ? pageInfo.title[locale].text : "";
    }

    public static comparePath(locale: string | undefined, routerPath: string, sitePath: string): boolean {
        if (routerPath === sitePath) {
            return true;
        }
        const lcLocale = locale?.toLowerCase();
        const appLocale = Object.values(AppLocales)
            .find(l => l.code.toLowerCase() === lcLocale || l.languageCode.toLowerCase() === lcLocale);
        const sPath = routerPath.split("/");
        if (sPath.length === 1 && sPath[0] === "") {
            return false;
        }
        const pathLocale = sPath.length > 1 ? sPath[1].toLowerCase() : "";
        const pathWithoutLocale = sPath.length > 2 ? "/" + sPath.slice(2).join("/") : routerPath;
        return JsUtils.isObjectAndNotEmpty(appLocale) && typeof appLocale === "object" &&
            (appLocale.code.toLowerCase() === pathLocale || appLocale.languageCode.toLowerCase() === pathLocale) &&
            sitePath === pathWithoutLocale;
    }

    public static toISODate(date: string | Date | null | undefined): string | null {
        if (date) {
            try {
                const m = moment(date);
                return m.isValid() ? m.toISOString() : null;
            } catch (e) {
            }
        }
        return null;
    }

    public static getGameIdFromPath(urlPath: string, locale?: string): string | null {
        const pArr = urlPath.split("/");
        return pArr[1] === "game" && pArr.length === 3
               ? pArr[2]
               : ((pArr[1] === locale || pArr[1] === "en") && pArr[2] === "game" && pArr.length === 4
                  ? pArr[3] : null);
    }

    public static getNewsIdFromPath(urlPath: string, locale?: string): string | null {
        const pArr = urlPath.split("/");
        return pArr[1] === "news" && pArr.length === 3
               ? pArr[2]
               : ((pArr[1] === locale || pArr[1] === "en") && pArr[2] === "news" && pArr.length === 4
                  ? pArr[3] : null);
    }

    public static getPlayerGameIdFromPath(urlPath: string, locale?: string): string | null {
        const pArr = urlPath.split("/");
        return pArr[1] === "play" && pArr[2] === "player" && pArr.length === 4
               ? pArr[4]
               :
               ((pArr[1] === locale || pArr[1] === "en") && pArr[2] === "play" && pArr[3] === "player" && pArr.length === 5
                ? pArr[4] : null);
    }

    // public static isPartnerArea(router: NextRouter): boolean {
    //     return router.route.startsWith("/pa/");
    // }
    //
    // public static isSite(router: NextRouter): boolean {
    //     return router.route.startsWith("/site/") || router.route.startsWith("/en") ||
    // router.asPath.startsWith("/en"); }

    // public static getProjectType(router: NextRouter): EnumProjectType {
    //     return Project.isSite(router) ? EnumProjectType.site : EnumProjectType.pa;
    // }

    public static getProjectByHost(host: string): IConfigProject | null {
        const domain = String(host).split(":");

        for (const projectCode in appConfig.projects) {
            for (const d of appConfig.projects[projectCode].domains) {
                if (d.hostname === domain[0]) {
                    return appConfig.projects[projectCode];
                }
            }
        }
        return null;
    }

    public static getSitePages(project: EnumProjectCode): IProjectPages {
        return sitePages;
    }

    public static getPaPages(): IProjectPages {
        return paPages;
    }

    public static getProjectDomainByHost(host: string): IConfigProjectDomain | null {
        const domain = host.split(":");

        for (const projectCode in appConfig.projects) {
            for (const d of appConfig.projects[projectCode].domains) {
                if (d.hostname === domain[0]) {
                    return d;
                }
            }
        }
        return null;
    }

    public static replaceSanityCdn(src: string) {
        let pathSrc = src;
        if (src && src?.search(/sanity.io/i) !== -1) {
            try {
                const srcArr = src.split("/");
                const fileName = srcArr.pop() as string;
                const fileExt = fileName.split(".").pop() as string;
                const baseName = fileName.slice(0, fileName.length - fileExt.length - 1);
                const fileStart = baseName.split("-").shift();
                if (fileName && fileExt && fileStart) {
                    // pathSrc = "https://cdn.skywindgroup.com/cdn/file/" +
                    //     fileName.slice(0, 2) + "/" + fileName.slice(2, 4) + "/" + fileStart + "." + fileExt;
                    pathSrc = "/cdn/" + fileStart + "." + fileExt;
                }
            } catch (e) {
            }
            // https://cdn.skywindgroup.com/cdn/file
            // https://cdn.sanity.io/images/usfaerfs/production/837f44e08ffa4eaadd84cf65f7d736b4d8d21622-24x24.svg
        }
        return pathSrc;
    }
}
