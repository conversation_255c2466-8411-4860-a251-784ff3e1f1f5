import { isDateAvailable } from "utils/dateTime";
import appConfig from "appConfig";
import moment from "moment";
import { ITournamentModel } from "iAppConfig";

export class Tournaments {
    public static getTournamentsStorage = (): ITournamentModel[] => {
        return appConfig.tournaments.storage;
    };

    public static getCurrentlyUsed = ({ filter }: { filter?: { isActive: boolean } }): ITournamentModel[] => {
        const tournamentUsed = [];

        for (const tournamentInfo of appConfig.tournaments.currentlyUsed) {
            const tournament = appConfig.tournaments.storage.find((t) => t.code === tournamentInfo.code);
            if (tournament && (filter?.isActive === undefined || tournament.isActive === filter?.isActive)) {
                tournamentUsed.push(tournament);
            }
        }
        return tournamentUsed;
    };

    public static isEnabled = (): boolean => {
        return (
            appConfig.tournaments.isActive && Tournaments.getCurrentlyUsed({ filter: { isActive: true } }).length > 0
        );
    };

    public static isTournamentEnabled = (tournament: ITournamentModel): boolean => {
        return tournament.isActive && isDateAvailable(moment().toISOString(), tournament.startDate, tournament.endDate);
    };

    public static isTournamentPlayEnabled = (tournament: ITournamentModel): boolean => {
        return (
            tournament.isActive &&
            isDateAvailable(moment().toISOString(), tournament.playStartDate, tournament.playEndDate)
        );
    };

    public static isTournamentTimerStartEnabled = (tournament: ITournamentModel): boolean => {
        return (
            tournament.isActive &&
            isDateAvailable(moment().toISOString(), tournament.startDate, tournament.timerStartDate)
        );
    };

    public static isTournamentTimerEndEnabled = (tournament: ITournamentModel): boolean => {
        return (
            tournament.isActive &&
            isDateAvailable(moment().toISOString(), tournament.timerStartDate, tournament.timerEndDate)
        );
    };
}
