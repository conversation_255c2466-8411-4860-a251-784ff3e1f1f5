export const HTMLElementHeight = (elmID: string): number | null => {
    let elmHeight: number,
        elmMargin: number,
        elm = document.getElementById(elmID);
    if (elm) {
        const c = document?.defaultView?.getComputedStyle(elm, "");
        if (c?.getPropertyValue("display") === "none") {
            return 0;
        }
        elmHeight = parseInt(String(c?.getPropertyValue("height")));
        elmMargin =
            parseInt(String(c?.getPropertyValue("margin-top"))) +
            parseInt(String(c?.getPropertyValue("margin-bottom")));
        return elmHeight + elmMargin;
    }
    return null;
};

export const getWindowSize = (): { width: number; height: number } => {
    const win = window,
        doc = document,
        docElem = doc.documentElement,
        body = doc.getElementsByTagName("body")[0],
        width = win.innerWidth || docElem.clientWidth || body.clientWidth,
        height = win.innerHeight || docElem.clientHeight || body.clientHeight;
    return { width, height };
};

export const getElCoordinates = (el: HTMLElement) => {
    let box = el.getBoundingClientRect();

    return {
        top: box.top + window.scrollY,
        right: box.right + window.scrollX,
        bottom: box.bottom + window.scrollY,
        left: box.left + window.scrollX,
        width: box.width,
        height: box.height,
    };
};
