import { EnumVideoUrlType } from "models/enum/sanity";
import { IVideoExtended } from "api/data-providers/sanity/interfaces/iSanityProvider";

export class Video {
    static m3u8 = "m3u8";

    public static isUrlVideoHls(url: string | undefined): boolean {
        return typeof url === "string" && url.trim().slice(url.length - Video.m3u8.length) === Video.m3u8;
    }

    public static isGameVideoHls(gameVideo?: IVideoExtended): boolean {
        const urlType = gameVideo?.type;
        const url = (urlType === EnumVideoUrlType.url && gameVideo?.url) || gameVideo?.videoUrl;
        return Video.isUrlVideoHls(url);
    }
}
