export class JsUtils {
    public static isObject<T>(obj: T): boolean {
        return typeof obj === "object" && obj !== null;
    }

    public static isObjectEmpty<T>(obj: T): boolean {
        // noinspection LoopStatementThatDoesntLoopJS
        for (const key in obj) return false;
        return true;
    }

    public static isObjectAndNotEmpty<T>(obj: T): boolean {
        return JsUtils.isObject(obj) && !JsUtils.isObjectEmpty(obj);
    }

    // 1444x670
    public static gpsToCoordinates(lat: number, lon: number, alt: number, w: number, h: number) {
        // const lat = 53.9006; // N
        // const lon = 27.5590; // E
        // const alt = 281; // altitude in meters
        const a = 6378137.0; //              #radius a of earth in meters cfr WGS84
        const b = 6356752.3; //             #radius b of earth in meters cfr WGS84
        const pi = Math.PI;
        const e2 = 1 - b ** 2 / a ** 2;
        const latR = (lat / 90) * 0.5 * pi; //       #latitude in radians
        const lonR = (lon / 180) * pi; //      #longituede in radians
        const NphI = a / Math.sqrt(1 - e2 * Math.sin(latR) ** 2);
        const x = (NphI + alt) * Math.cos(latR) * Math.cos(lonR);
        const y = (NphI + alt) * Math.cos(latR) * Math.sin(lonR);
        const z = ((b ** 2 / a ** 2) * NphI + alt) * Math.sin(latR);
        return { x, y, z, wx: (x / a) * w, hy: (y / b) * h };
    }
}
