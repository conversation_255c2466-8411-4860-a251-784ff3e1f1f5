import { EnumLanguageCode } from "models/enum/system";
import { ILocaleString } from "api/data-providers/sanity/schemas";

export class SanityApi {
    public static getLocale(locale: ILocaleString | undefined, languageCode: EnumLanguageCode): string {
        if (locale) {
            switch (languageCode) {
                case EnumLanguageCode.en:
                    return locale?.en || "";
                case EnumLanguageCode.zh:
                    return locale?.zhCn || "";
            }
        }
        return "";
    }

    public static getImageDimensionsFromUrl(url: string): { width: string; height: string; ext: string } | null {
        const regex = /(cdn.sanity.io)[\w\W]+-([\d]+)x([\d]+).([\w]+)$/gm;
        let result;

        while ((result = regex.exec(url)) !== null) {
            // This is necessary to avoid infinite loops with zero-width matches
            if (result.index === regex.lastIndex) {
                regex.lastIndex++;
            }
            if (result) {
                return {
                    width: result[2],
                    height: result[3],
                    ext: result[4],
                };
            }
        }
        return null;
    }
}
