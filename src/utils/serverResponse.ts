import { NextApiResponse } from "next";
import { IHttpResponseError, IHttpResponseSuccess } from "api/controllers/interfaces/iHttp";
import {
    IErrorInputValidation,
    IResponseError,
    IResponseSuccess,
    IResponseSuccessArray,
} from "api/interfaces/response";
import { EnumErrorLevel } from "models/enum/http";
import { AxiosResponse } from "axios";
import { ServerRequest } from "./serverRequest";

export enum EnumServerResponseType {
    successObject = "successObject",
    successText = "successText",
    successArray = "successArray",
    serverError = "serverError",
    serviceError = "serviceError",
    inputValidationError = "inputValidationError",
}

export class ResponseSuccess implements IResponseSuccess {
    public code?: number;
    public data: any = null;
    public message: string;
    public params: IResponseSuccessArray;
    public responseCode: number;
    public type: EnumServerResponseType;

    constructor(
        type: EnumServerResponseType,
        data: unknown,
        status: number = 200,
        params: IResponseSuccessArray = {} as IResponseSuccessArray,
        code: number = 0,
        message: string = ""
    ) {
        this.code = code;
        this.data = data;
        this.message = message;
        this.params = params;
        this.responseCode = status;
        this.type = type;
    }

    response(res: NextApiResponse): void {
        res.status(this.responseCode).json({
            code: this.code,
            data: this.data,
            message: this.message,
            params: this.params,
            responseCode: this.responseCode,
            status: "success",
            type: this.type,
        } as IHttpResponseSuccess);
    }
}

export class ResponseError implements IResponseError {
    public name: string;
    public errorCode: number;
    public errorLevel: number;
    public message: string;
    public params: object;
    public responseCode: number;
    public translateMessage: boolean;
    public type: EnumServerResponseType;

    constructor(
        responseCode: number,
        errorCode: number,
        message: string,
        level: number = EnumErrorLevel.warn,
        type: EnumServerResponseType = EnumServerResponseType.serviceError,
        params = {}
    ) {
        this.name = "ResponseError";
        this.message = message;
        this.responseCode = responseCode;
        this.errorCode = errorCode;
        this.type = type;
        this.errorLevel = level;
        this.translateMessage = true;
        this.params = params;
    }

    response(res: NextApiResponse): void {
        res.status(this.responseCode).json({
            code: this.errorCode,
            level: this.errorLevel,
            message: this.message,
            responseCode: this.responseCode,
            status: "error",
            type: this.type,
        } as IHttpResponseError);
    }

    getMessages(): string[] {
        if (this.type === EnumServerResponseType.inputValidationError) {
            const message: IErrorInputValidation = JSON.parse(this.message);
            if (message?.message && Array.isArray(message?.errors)) {
                return message.errors;
            }
        }

        return [this.message];
    }
}

export class ClientApiResponse {
    private res: ResponseError | ResponseSuccess;

    constructor(response: AxiosResponse) {
        this.res = this.parseAxiosResponse(response);
    }

    get response() {
        return new ServerResponse(this.res);
    }

    parseAxiosResponse(response: AxiosResponse) {
        if (
            (response?.status >= 200 || response?.status < 300) &&
            (response?.data as IHttpResponseSuccess).status === "success"
        ) {
            const data = response.data as IHttpResponseSuccess;
            this.res = new ResponseSuccess(
                data.type,
                data.data,
                data.responseCode,
                data.params,
                data.code,
                data.message
            );
        } else if ((response?.data as IHttpResponseError)?.status === "error") {
            const data = response.data as IHttpResponseError;
            this.res = new ResponseError(
                data.responseCode,
                data.code,
                data.message,
                data.level,
                data.type,
                data.params
            );
        }
        return this.res;
    }
}

export class ServerResponse {
    private res: ResponseSuccess | ResponseError;
    private serverRequest: ServerRequest | undefined;

    get response() {
        return this.res;
    }

    set response(value: ResponseSuccess | ResponseError) {
        this.res = value;
    }

    constructor(response: ResponseSuccess | ResponseError | any, serverRequest?: ServerRequest) {
        this.res = response;
        this.serverRequest = serverRequest;
        return this;
    }

    isError(): boolean {
        return this.res instanceof ResponseError;
    }

    isSuccess(): boolean {
        return this.res instanceof ResponseSuccess;
    }

    serverResponse(): void {
        if (this.res?.response && typeof this.res?.response === "function" && this.serverRequest?.res) {
            this.res.response(this.serverRequest?.res);
        }
    }

    serverRedirect(url: string): void {
        if (this.serverRequest?.res) {
            if (this.res instanceof ResponseError) {
                this.res.response(this.serverRequest?.res);
            } else {
                this.serverRequest?.res.redirect(url);
            }
        }
    }

    onSuccess(callback: (res: ResponseSuccess) => void) {
        if (this.res instanceof ResponseSuccess) {
            callback.call(this, this.res as ResponseSuccess);
        }
        return this;
    }

    onError(callback: (res: ResponseError) => void) {
        if (this.res instanceof ResponseError) {
            callback.call(this, this.res as ResponseError);
        }
        return this;
    }
}
