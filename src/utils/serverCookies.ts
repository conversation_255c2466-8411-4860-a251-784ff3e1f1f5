import { NextApiRequest, NextApiResponse } from "next";
import cookie, { CookieSerializeOptions } from "cookie";
import { EnumCookies } from "models/enum/cookies";
import { ServerRequest } from "./serverRequest";
import appServerConfig from "appServerConfig";

const uuid = require("uuid");

let cookiesCache = {} as any;

export class ServerCookies {
    static cookiesOptions(req: NextApiRequest, options: CookieSerializeOptions): CookieSerializeOptions {

        const date = new Date(new Date().getTime() + Number(options.maxAge));
        return {
            httpOnly: true,
            sameSite: "lax",
            path: "/",
            expires: date,
            secure: ServerCookies.isSecure(req),
        };
    }

    static isSecure(req: NextApiRequest): boolean {
        const url = ServerRequest.getUrl(req);
        return url.protocol.toLowerCase().search(/https/) !== -1;
    }

    static setCookie(res: NextApiResponse, name: string, options: any) {
        cookiesCache[name] = options;
        res.setHeader("Set-Cookie", Object.values(cookiesCache));
    }

    static setRefreshToken(req: NextApiRequest, res: NextApiResponse, refreshToken: string): void {
        ServerCookies.setCookie(
            res,
            EnumCookies.refreshToken,
            cookie.serialize(
                EnumCookies.refreshToken,
                String(refreshToken),
                ServerCookies.cookiesOptions(req, appServerConfig.jwt.refresh.cookie.options)
            )
        );

        const sessionId = ServerCookies.getSessionId(req);
        if (!sessionId) {
            ServerCookies.setSessionId(req, res, ServerCookies.generateSessionId());
        }
    }

    static deleteRefreshToken(req: NextApiRequest, res: NextApiResponse): void {
        ServerCookies.setCookie(
            res,
            EnumCookies.refreshToken,
            cookie.serialize(
                EnumCookies.refreshToken,
                "",
                ServerCookies.cookiesOptions(req, {
                    ...appServerConfig.jwt.refresh.cookie.options,
                    maxAge: -1,
                })
            )
        );
    }

    public static generateSessionId(): string {
        return uuid.v4();
    }

    public static validateSessionId(value: string | undefined): boolean {
        return uuid.validate(value as string);
    }

    static setSessionId(req: NextApiRequest, res: NextApiResponse, sessionId: string): void {
        ServerCookies.setCookie(
            res,
            EnumCookies.sessionId,
            cookie.serialize(
                EnumCookies.sessionId,
                String(sessionId),
                ServerCookies.cookiesOptions(req, appServerConfig.jwt.refresh.cookie.options)
            )
        );
    }

    static getSessionId(req: NextApiRequest): string | null | undefined {
        const sessionId = req.cookies[EnumCookies.sessionId];
        return ServerCookies.validateSessionId(sessionId) ? sessionId : null;
    }

    static deleteSessionId(req: NextApiRequest, res: NextApiResponse): void {
        ServerCookies.setCookie(
            res,
            EnumCookies.sessionId,
            cookie.serialize(
                EnumCookies.sessionId,
                "",
                ServerCookies.cookiesOptions(req, {
                    ...appServerConfig.jwt.refresh.cookie.options,
                    maxAge: -1,
                })
            )
        );
    }
}
