import { EnumSitePathType } from "models/enum/system";
import { EnumLocale } from "locales/locales";
import { EnumPage } from "models/enum/page";

export type TypeChangeFrequency = "always" | "hourly" | "daily" | "weekly" | "monthly" | "yearly" | "never"

export interface IProjectPageInfo {
    type: EnumSitePathType;
    path: string;
    seoPriority: number;
    seoChangeFrequency: TypeChangeFrequency;
    inSitemap: boolean;
    isLocalized: boolean;
    isActive: boolean;
    title: Record<EnumLocale, { locale: EnumLocale; text: string }>;
}

export type IProjectPages = Record<EnumPage, IProjectPageInfo>
