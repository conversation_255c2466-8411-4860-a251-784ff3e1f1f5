// ISO 639-1 Language Codes
export enum EnumLanguageCode {
    en = "en", // English
    zh = "zh", // Chinese
}

export enum EnumEnvironment {
    develop = "development", // next dev || next debug
    production = "production", // next start
}

export enum EnumProjectType {
    pa = "pa",
    site = "site",
}

export enum EnumServerEnv {
    local = "local",
    develop = "develop",
    stage = "stage",
    production = "production",
}

export enum EnumProjectCode {
    pa = "pa",
    website_eu = "website_eu",
    website_china = "website_china",
    website_asia = "website_asia",
}

export enum EnumSeoCanonicalDomain {
    pa = "partnerarea.skywindgroup.com",
    website_eu = "skywindgroup.com",
    website_china = "asia.skywindgroup.com",
    website_asia = "asiaskywind.com",
}

export enum EnumSitePathType {
    static_page = "static_page",
    dynamic_page = "dynamic_page"
}

export enum EnumCurrencyCode {
    USD = "USD",
    EUR = "EUR",
    CNY = "CNY",
}

export enum EnumRegionCode {
    eu = "eu",
    china = "china",
    asia = "asia",
}

export enum EnumRegionName {
    eu = "Europe (skywindgroup.com)",
    china = "China (asia.skywindgroup.com)",
    asia = "Asia (asiaskywind.com)",
}

export enum EnumGameServerEntityType {
    production = "production",
    staging = "staging",
    development = "development",
    test = "test",
}

export enum EnumGameServerPlayMode {
    fun = "fun",
    real = "real",
}
