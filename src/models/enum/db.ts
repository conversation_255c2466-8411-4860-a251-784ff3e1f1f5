export enum EnumDBProviders {
    mysql = "mysql",
}

export enum EnumDBUserPermissions {
    canGetUsersPermissions = "can-get-users-permissions",
    //
    // Partner Area
    // The user can enter the partner area
    paCanLogin = "pa_can_login",
    // The user can access the news page in the partner area.
    paCanViewPageNews = "pa_can_view_page_news",
    // The user can access the news info page in the partner area.
    paCanViewPageNewsInfo = "pa_can_view_page_news-info",
    // The user can access the roadmap page in the partner area.
    paCanViewPageRoadmap = "pa_can_view_page_roadmap",
    // The user can access page with live games in the partner area.
    paCanViewPageLiveGames = "pa_can_view_page_live-games",
    // The user can access the roadmap available list in the partner area.
    paCanViewPageRoadmapAvailable = "pa_can_view_page_roadmap-available",
    // The user can access the roadmap latest list in the partner area.
    paCanViewPageRoadmapLatest = "pa_can_view_page_roadmap-latest",
    // The user can access the roadmap upcoming list in the partner area.
    paCanViewPageRoadmapUpcoming = "pa_can_view_page_roadmap-upcoming",
    // The user can access the search page in the partner area.
    paCanViewPageSearch = "pa_can_view_page_search",
    // The user can register new users in the partner area.
    paCanSignUp = "pa_can_sign-up",
    // The user can restore password in the partner area.
    paCanRestorePassword = "pa_can_restore_password",
    // The user can access user account page in the partner area.
    paCanViewPageAccount = "pa_can_view_page_account",
    // The user can access game page details in the partner area.
    paCanViewPageGameInfo = "pa_can_view_page_game-info",
    // The user can access free bets page in the partner area.
    paCanViewPageFreeBets = "pa_can_view_page_free-bets",
    // The user can access lucky envelopes page in the partner area.
    paCanViewPageLuckyEnvelopes = "pa_can_view_page_lucky-envelopes",
    // The user can access must win jackpot page in the partner area.
    paCanViewPageMustWinJackpot = "pa_can_view_page_must-win-jackpot",
    // The user can access tournaments page in the partner area.
    paCanViewPageTournaments = "pa_can_view_page_tournaments",
    // The user can play games in the partner area.
    paCanPlayGames = "pa_can_play_games",
    // The user can view game info sheet in the partner area.
    paCanViewInfoSheet = "pa_can_view_info-sheet",
    // The user can view promo video in the partner area.
    paCanViewPromoVideo = "pa_can_view_promo-video",
    // The user can view marketing kit in the partner area.
    paCanViewMarketingKit = "pa_can_view_marketing-kit",
    // The user can download marketing kit in the partner area.
    paCanDownloadMarketingKit = "pa_can_download_marketing-kit",
    // The user can view product sheet in the partner area.
    paCanViewProductSheet = "pa_can_view_product-sheet",
    // The user can view certificates in the partner area.
    paCanViewCertificates = "pa_can_view_certificates",
    // The user can play in tournaments.
    paCanUseTournamentPlayer = "pa_can_use_tournament-player",
    //
    // SW Documentation
    paCanManageUsersSwDocs = "pa_can_manage_users_sw-docs", // The user can manage users of skywind documentation in
    // the partner area.
    paCanViewSwDocs = "pa_can_view_sw-docs", // The user can access skywind documentation in the partner area.
    paCanDownloadSwDocs = "pa_can_download_sw-docs", // The user can download skywind documentation in the partner area.
    paCanUploadSwDocs = "pa_can_upload_sw-docs", // The user can upload skywind documentation in the partner area.
    //
    // User management
    // The user can view page with list of users.
    paCanViewPageUsersList = "pa_can_view_page_users-list",
}

export enum EnumDBUserGroupCodes {
    guest = "guest",
    siteAdmin = "site-admin",
    partnerAdmin = "partner-admin",
    partnerUser = "partner-user",
    partnerThirdParty = "partner-third-party",
    siteReader = "site-reader",
    partnerManager = "partner-manager",
    swDocsAdmin = "swDocsAdmin",
    swDocsReader = "swDocsReader",
}

export enum EnumAliasDBModel {
    bucket_resources = "bucket_resources",
    deploy_log = "deploy_log",
    game_info = "game_info",
    logs = "logs",
    marketing_materials = "marketing_materials",
    optimized_images = "optimized_images",
    provider_games = "provider_games",
    regions = "regions",
    sanity_items = "sanity_items",
    sanity_items_revisions = "sanity_items_revisions",
    sanity_types = "sanity_types",
    sanity_types_revisions = "sanity_types_revisions",
    sessions = "sessions",
    settings = "settings",
    user_activation = "user_activation",
    user_activity = "user_activity",
    user_browser_agents = "user_browser_agents",
    user_group = "user_group",
    user_permissions = "user_permissions",
    user_permissions_to_user_groups = "user_permissions_to_user_groups",
    user_players = "user_players",
    user_tokens = "user_tokens",
    user = "user",
    users_revisions = "users_revisions",
    users_to_regions = "users_to_regions",
    users_to_user_groups = "users_to_user_groups",
    users_to_users = "users_to_users",
    versions = "versions",
}
