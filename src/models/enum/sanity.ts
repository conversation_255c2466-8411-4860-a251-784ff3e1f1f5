export enum EnumUrlType {
    external = "external",
    game = "game",
}

export enum EnumVideoUrlType {
    videoMp4 = "video/mp4",
    videoWebm = "video/webm",
    videoOgg = "video/ogg",
    youtube = "youtube",
    url = "url",
}

export enum EnumUrlTarget {
    _self = "_self",
    _blank = "_blank",
}

export enum EnumAssetType {
    image = "image",
    video = "video",
}

export enum EnumBannerType {
    pa = "pa",
}

export enum EnumBannerOnDevices {
    nothing = "nothing",
    banner600x140 = "banner600x140",
    banner1920x120 = "banner1920x120",
}
