export enum EnumGameRTP {
    bi = "bi", // rtp from BI theoretical_rtp
    gi = "gi", // rtp from sanity Game Info
    gm = "gm", // rtp from sanity Game RTP
}

export enum EnumShowGameCode {
    first_one = "first_one", // Show only one first in the list
    all_without_first = "all_without_first", // Show all except first
    all = "all", // Show all
}

export enum EnumShowGameRTP {
    from_bi = "from_bi", // Use RTP from BI
    from_game_info = "from_game_info", // Use RTP from Game Info Below
    from_game_rtp = "from_game_rtp", // Use RTP from "Game RTP" list
}
