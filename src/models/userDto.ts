import { hashids } from "../utils/hash";
import { IUserDtoDecoded, IUserDtoEncoded } from "../api/interfaces/userDto";
import { Users } from "../api/data-providers/db/mysql/sequelize";

const jwt = require("jsonwebtoken");

export class UserDto implements IUserDtoEncoded {
    public email: string;
    public id: string;

    constructor(user: Users) {
        this.email = user.email;
        this.id = hashids.encode(user.id);
    }

    static decode(token: string): IUserDtoDecoded | false {
        try {
            const decodedToken: UserDto = jwt.decode(token);
            if (typeof decodedToken === "object" && decodedToken?.email && decodedToken?.id) {
                const idArray = hashids.decode(decodedToken.id);
                if (Array.isArray(idArray) && idArray.length === 1) {
                    return {
                        email: decodedToken.email,
                        id: idArray.pop() as number,
                    };
                }
            }
        } catch (e) {}
        return false;
    }
}
