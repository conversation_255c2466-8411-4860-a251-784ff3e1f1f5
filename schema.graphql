# This file was generated based on ".graphqlconfig". Do not edit manually.

schema {
    query: RootQuery
}

"Field is a \"raw\" JSON alias for a different field"
directive @jsonAlias(
    "Source field name"
    for: String!
) on FIELD_DEFINITION

"Field references one or more documents"
directive @reference on FIELD_DEFINITION

"A Sanity document"
interface Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
}

union BlockOrImage = Block | Image

union LinkOrSitePage = Link | SitePage

union SingleLine1OrSingleLine2OrSingleLine3OrTextarea = SingleLine1 | SingleLine2 | SingleLine3 | Textarea

type Banner implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    banner1920x120: BannerAsset
    banner600x140: BannerAsset
    bannerName: LocaleString
    bannerOnDesktop: String
    bannerOnMobileLandscape: String
    bannerOnMobilePortrait: String
    bannerType: String
    code: Slug
    game: Game
    isActive: Boolean
    url: UrlTarget
    urlType: String
}

type BannerAsset {
    _key: String
    _type: String
    assetImage: LocaleImage
    assetType: String
    assetVideo: File
}

type BannersCarousel {
    _key: String
    _type: String
    "Delay between banners in seconds. To disable autoplay set value to 0"
    autoPlay: Float
    banners: [Banner]
    isActive: Boolean
    "Enable continuous loop mode"
    isLoop: Boolean
}

type Block {
    _key: String
    _type: String
    children: [Span]
    list: String
    style: String
}

type Color {
    _key: String
    _type: String
    alpha: Float
    hex: String
    hsl: HslaColor
    hsv: HsvaColor
    rgb: RgbaColor
}

type Country implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    countryCode: String
    countryIcon: LocaleImage
    countryName: String
    countryNativeName: String
    currencies: [Currency]
    games: [GameLine]
    languages: [Language]
}

type CountryGames {
    _key: String
    _type: String
    country: Country
    games: [GameLine]
}

type Currency implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    currencyCode: String
    currencyIcon: LocaleImage
    currencyName: String
    currencyNativeName: String
}

type EmailAddress implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    code: Slug
    emailAddress: String
    emailTitle: LocaleString
    isActive: Boolean
}

type Employee implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    employeeDescription: LocaleText
    employeeName: LocaleString
    employeePhoto: LocaleImage
    employeePosition: LocaleString
    isActive: Boolean
}

type EngagementSlide implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    header1: LocaleString
    header2: LocaleString
    image: LocaleImage
    imageType: String
    isActive: Boolean
    text1: LocaleText
    text2: LocaleText
}

type File {
    _key: String
    _type: String
    asset: SanityFileAsset
}

type Game implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    availableIn: [String]
    certificatesPerMarket: [GameMarketCertificates]
    gameBillboard: LocaleImage
    gameBrandedPoster: LocaleImage
    gameCode: [GameCode]
    gameDescription: LocaleText
    gameFeatures: [GameFeatures]
    gameHDPoster: LocaleImage
    gameIcon: LocaleImage
    gameId: Slug
    gameImage: LocaleImage
    gameInfo: GameInfo
    gameLogo: LocaleImage
    gameManage: GameManage
    gameName: LocaleString
    gameOrientation: String
    gamePoster: LocaleImage
    gameRTP: [GameRTP]
    gameRibbon: GameRibbon
    gameScreenshotHD: [LocaleImage]
    gameScreenshotPoster: [LocaleImage]
    gameTop4Poster: LocaleImage
    gameTowerPoster: LocaleImage
    gameType: [GameType]
    gameVideo: Video
    gameVideos: [VideoUrl]
    "Auto detect will show video if video area contains all required video and posters instead Game HD Poster and Tower Poster for mobile. By default is auto."
    header: String
    isActive: Boolean
    paUrl: String
    releaseNote: File
    saleSheet: File
    "Allows you to choose options for displaying the game code"
    showGameCode: String
    "Allows you to choose options for displaying the game RTP"
    showGameRTP: String
    siteDescription: LocaleText
}

type GameAPI {
    _key: String
    _type: String
    playerApiUrl: String
    siteApiUrl: String
    siteToken: String
}

type GameCategory implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    categoryCode: Slug
    categoryGames: [GameLine]
    categoryName: LocaleString
    categoryRegion: String
    categoryType: String
    countryGames: [CountryGames]
    isActive: Boolean
    showGamesFrom: String
}

type GameCertificate implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    gameCertificateCode: Slug
    gameCertificateName: LocaleString
}

type GameCode implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    gameCode: String
    "Game info from BI report. Updated using Cron."
    gameInfo: String
    gameProvider: Provider
    "Game marketing materials from Box.com. Updated using Cron and Jenkins job from box.com"
    marketingMaterials: String
}

type GameFeatures {
    _key: String
    _type: String
    gameFeatureDescription: LocaleText
    gameFeaturePoster: LocaleImage
    gameFeatureTitle: LocaleString
}

type GameInfo {
    _key: String
    _type: String
    "Field allows to format how game release date will be shown or show \"Coming soon\" message."
    formatOfReleaseDate: String
    isFreeBets: Boolean
    isLuckyEnvelopes: Boolean
    isMustWinJackpot: Boolean
    isTournaments: Boolean
    "6 reels , 2-7 rows"
    layout: String
    markets: [Market]
    releaseDates: [GameReleaseDate]
    "96.5"
    rtp: Float
    "4/5"
    volatility: String
    "117649"
    ways: String
}

type GameLine {
    _key: String
    _type: String
    game: Game
    gameRibbon: GameRibbon
    layout: String
}

type GameManage {
    _key: String
    _type: String
    releaseDate: Date
    releaseText: LocaleString
    status: String
}

type GameMarketCertificates {
    _key: String
    _type: String
    certificate: File
    certificate_1: File
    certificate_2: File
    comingSoon: Boolean
    gameMarket: Market
}

type GamePlaylist implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    isActive: Boolean
    playlistBanner: LocaleImage
    playlistCode: Slug
    playlistDescription: LocaleText
    playlistGames: [Game]
    playlistName: LocaleString
    playlistPoster: LocaleImage
    playlistRegion: String
}

type GameRTP implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    gameRTP: String
}

type GameReleaseDate {
    _key: String
    _type: String
    gameMarket: Market
    releaseDate: Date
}

type GameRibbon implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    gameRibbonCode: Slug
    gameRibbonImage: LocaleImage
    gameRibbonName: LocaleString
}

type GameType implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    gameTypeCode: Slug
    gameTypeTitle: LocaleString
}

type Geopoint {
    _key: String
    _type: String
    alt: Float
    lat: Float
    lng: Float
}

type HeaderText {
    _key: String
    _type: String
    headerTextArea: LocaleText
    headerTitle: HeaderTitle
}

type HeaderTicker {
    _key: String
    _type: String
    isActive: Boolean
    tickerDate: Date
}

type HeaderTitle {
    _key: String
    _type: String
    title1: LocaleString
    title2: LocaleString
    title3: LocaleString
}

type HslaColor {
    _key: String
    _type: String
    a: Float
    h: Float
    l: Float
    s: Float
}

type HsvaColor {
    _key: String
    _type: String
    a: Float
    h: Float
    s: Float
    v: Float
}

type Image {
    _key: String
    _type: String
    asset: SanityImageAsset
    crop: SanityImageCrop
    hotspot: SanityImageHotspot
}

type JackpotInfo implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    code: Slug
    description: LocaleText
    header: LocaleString
    icon: LocaleImage
    iconHover: LocaleImage
    isActive: Boolean
}

type Language implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    languageCode: String
    languageIcon: LocaleImage
    languageName: String
    languageNativeName: String
}

type License implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    isActive: Boolean
    jurisdiction: LocaleString
    licenseDescription: LocaleText
    licenseNo: String
    licensePhoto: LocaleImage
    licenseUrl: String
    regulator: LocaleString
}

type Link implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    code: Slug
    isActive: Boolean
    isExternal: Boolean
    title: LocaleString
}

type LocaleBlock {
    _key: String
    _type: String
    enRaw: JSON
    zhCnRaw: JSON
}

type LocaleImage {
    _key: String
    _type: String
    en: Image
    zhCn: Image
}

type LocaleString {
    _key: String
    _type: String
    en: String
    zhCn: String
}

type LocaleText {
    _key: String
    _type: String
    en: String
    zhCn: String
}

type LocaleUrl {
    _key: String
    _type: String
    en: String
    zhCn: String
}

type Localization implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    dataArray: [SingleLine1OrSingleLine2OrSingleLine3OrTextarea]
    textCode: Slug
}

type Maintenance {
    _key: String
    _type: String
    "Show or hide dialog with message or image"
    isActive: Boolean
    landscapeImage: LocaleImage
    portraitImage: LocaleImage
    textMessage: LocaleString
}

type Market implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    code: Slug
    isActive: Boolean
    marketIcon: LocaleImage
    marketName: LocaleString
}

type MediaServicesBlock {
    _key: String
    _type: String
    blockCode: Slug
    header: LocaleString
    icon: LocaleImage
    iconHover: LocaleImage
    text: LocaleText
}

type MediaServicesTech {
    _key: String
    _type: String
    blockCode: Slug
    cardType: String
    header: LocaleString
    icon: LocaleImage
    iconHover: LocaleImage
    text: LocaleText
}

type MediaTag implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    name: Slug
}

type News implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    block: LocaleBlock
    game: Game
    headerPromoVideo: Video
    highlightedTitle: LocaleString
    isActive: Boolean
    newsCategories: [NewsCategory]
    newsDate: Date
    newsPreviewImage: LocaleImage
    newsType: String
    region: String
    text: LocaleText
    textType: String
    title: LocaleString
}

type NewsCategory implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    newsCategoryCode: Slug
    newsCategoryTitle: LocaleString
}

type Office implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    address: LocaleText
    code: Slug
    companyName: LocaleString
    country: LocaleString
    email: String
    isActive: Boolean
    largePhotos: [Image]
    leftOnMap: Float
    officeName: LocaleString
    officePhoto: LocaleImage
    phone1: String
    phone2: String
    topOnMap: Float
}

type PageAboutUs implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    canDeploy: Boolean
    employees: [Employee]
    headerPromoVideo: Video
    headerText: HeaderText
    isActive: Boolean
    licenses: [License]
    offices: [Office]
    pageTitle: LocaleString
}

type PageGameSearch implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    banners: BannersCarousel
    bestGames: [Game]
    pageTitle: LocaleString
}

type PageGames implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    canDeploy: Boolean
    gamePlaylist: [GamePlaylist]
    gamesBranded: [GameLine]
    gamesCategories: [GameCategory]
    headerPromoVideo: Video
    headerText: HeaderText
    isActive: Boolean
    pageTitle: LocaleString
}

type PageGamingSolutions implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    canDeploy: Boolean
    cloudArray: [MediaServicesBlock]
    dataArray: [MediaServicesBlock]
    headerText: HeaderText
    infraArray: [MediaServicesBlock]
    isActive: Boolean
    mobileArray: [MediaServicesBlock]
    pageTitle: LocaleString
}

type PageHome implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    canDeploy: Boolean
    headerPromoVideo: Video
    headerText: HeaderText
    isActive: Boolean
    pageTitle: LocaleString
    partners: [Partner]
    topGame1: GameLine
    topGame2: GameLine
    topGame3: GameLine
    topGame4: GameLine
}

type PageLiveCasino implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    canDeploy: Boolean
    dataArray: [MediaServicesBlock]
    headerPromoVideo: Video
    headerText: HeaderText
    infoArray: [MediaServicesBlock]
    isActive: Boolean
    liveGames: [GameLine]
    pageTitle: LocaleString
    studios: [Studio]
}

type PageMediaServices implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    canDeploy: Boolean
    dataArray: [MediaServicesBlock]
    engagementSliders: [EngagementSlide]
    headerText: HeaderText
    isActive: Boolean
    pageTitle: LocaleString
    techArray: [MediaServicesTech]
}

type PageNews implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    banners: BannersCarousel
    canDeploy: Boolean
    headerPromoVideo: Video
    headerText: HeaderText
    isActive: Boolean
    newsCategories: [NewsCategory]
    pageTitle: LocaleString
}

type PageRoadmap implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    availableMarkets: [Market]
    banners: BannersCarousel
    biggestMarkets: [Market]
    defaultMarket: Market
    latestReleasesMonth: Float
    pageTitle: LocaleString
    roadmapGame: [RoadmapGame]
    "Hide or show the toolbar with the choice of the month in the \"Full roadmap\" state"
    showPerMonthToolbar: Boolean
    upcomingMonthThirdParty: Float
}

type PageToolsBonusCoin implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    canDeploy: Boolean
    engagementSliders: [EngagementSlide]
    headerPromoVideo: Video
    headerText: HeaderText
    isActive: Boolean
    pageTitle: LocaleString
}

type PageToolsGRC implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    canDeploy: Boolean
    challenges: [EngagementSlide]
    grcGames: [GameLine]
    headerText: HeaderText
    isActive: Boolean
    jackpotsInfo: [JackpotInfo]
    pageTitle: LocaleString
    piggyRewardsSliders: [EngagementSlide]
    tournamentsJackpot: [EngagementSlide]
}

type PageToolsJackpot implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    canDeploy: Boolean
    engagementSliders: [EngagementSlide]
    headerText: HeaderText
    isActive: Boolean
    jackpotsInfo: [JackpotInfo]
    pageTitle: LocaleString
}

type PageToolsTournament implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    canDeploy: Boolean
    engagementSliders: [EngagementSlide]
    headerText: HeaderText
    isActive: Boolean
    pageTitle: LocaleString
}

type PageTournaments implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    maintenance: Maintenance
    pageTitle: LocaleString
}

type Partner implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    isActive: Boolean
    partnerCode: Slug
    partnerImage: LocaleImage
    partnerName: LocaleString
    partnerUrl: String
}

type Phone implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    code: Slug
    isActive: Boolean
    phoneNumber: String
    phoneTitle: LocaleString
}

type Provider implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    providerCode: String
    providerName: String
}

type Region implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    regionCode: String
}

type RgbaColor {
    _key: String
    _type: String
    a: Float
    b: Float
    g: Float
    r: Float
}

type RoadmapGame implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    comingSoon: Boolean
    game: Game
    hideInAllMarkets: Boolean
    roadmapGameMarket: [RoadmapGameMarket]
}

type RoadmapGameMarket implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    endDate: Date
    gameMarket: Market
    startDate: Date
}

type RootQuery {
    Banner(
        "Banner document ID"
        id: ID!
    ): Banner
    Country(
        "Country document ID"
        id: ID!
    ): Country
    Currency(
        "Currency document ID"
        id: ID!
    ): Currency
    Document(
        "Document document ID"
        id: ID!
    ): Document
    EmailAddress(
        "EmailAddress document ID"
        id: ID!
    ): EmailAddress
    Employee(
        "Employee document ID"
        id: ID!
    ): Employee
    EngagementSlide(
        "EngagementSlide document ID"
        id: ID!
    ): EngagementSlide
    Game(
        "Game document ID"
        id: ID!
    ): Game
    GameCategory(
        "GameCategory document ID"
        id: ID!
    ): GameCategory
    GameCertificate(
        "GameCertificate document ID"
        id: ID!
    ): GameCertificate
    GameCode(
        "GameCode document ID"
        id: ID!
    ): GameCode
    GamePlaylist(
        "GamePlaylist document ID"
        id: ID!
    ): GamePlaylist
    GameRTP(
        "GameRTP document ID"
        id: ID!
    ): GameRTP
    GameRibbon(
        "GameRibbon document ID"
        id: ID!
    ): GameRibbon
    GameType(
        "GameType document ID"
        id: ID!
    ): GameType
    JackpotInfo(
        "JackpotInfo document ID"
        id: ID!
    ): JackpotInfo
    Language(
        "Language document ID"
        id: ID!
    ): Language
    License(
        "License document ID"
        id: ID!
    ): License
    Link(
        "Link document ID"
        id: ID!
    ): Link
    Localization(
        "Localization document ID"
        id: ID!
    ): Localization
    Market(
        "Market document ID"
        id: ID!
    ): Market
    MediaTag(
        "MediaTag document ID"
        id: ID!
    ): MediaTag
    News(
        "News document ID"
        id: ID!
    ): News
    NewsCategory(
        "NewsCategory document ID"
        id: ID!
    ): NewsCategory
    Office(
        "Office document ID"
        id: ID!
    ): Office
    PageAboutUs(
        "PageAboutUs document ID"
        id: ID!
    ): PageAboutUs
    PageGameSearch(
        "PageGameSearch document ID"
        id: ID!
    ): PageGameSearch
    PageGames(
        "PageGames document ID"
        id: ID!
    ): PageGames
    PageGamingSolutions(
        "PageGamingSolutions document ID"
        id: ID!
    ): PageGamingSolutions
    PageHome(
        "PageHome document ID"
        id: ID!
    ): PageHome
    PageLiveCasino(
        "PageLiveCasino document ID"
        id: ID!
    ): PageLiveCasino
    PageMediaServices(
        "PageMediaServices document ID"
        id: ID!
    ): PageMediaServices
    PageNews(
        "PageNews document ID"
        id: ID!
    ): PageNews
    PageRoadmap(
        "PageRoadmap document ID"
        id: ID!
    ): PageRoadmap
    PageToolsBonusCoin(
        "PageToolsBonusCoin document ID"
        id: ID!
    ): PageToolsBonusCoin
    PageToolsGRC(
        "PageToolsGRC document ID"
        id: ID!
    ): PageToolsGRC
    PageToolsJackpot(
        "PageToolsJackpot document ID"
        id: ID!
    ): PageToolsJackpot
    PageToolsTournament(
        "PageToolsTournament document ID"
        id: ID!
    ): PageToolsTournament
    PageTournaments(
        "PageTournaments document ID"
        id: ID!
    ): PageTournaments
    Partner(
        "Partner document ID"
        id: ID!
    ): Partner
    Phone(
        "Phone document ID"
        id: ID!
    ): Phone
    Provider(
        "Provider document ID"
        id: ID!
    ): Provider
    Region(
        "Region document ID"
        id: ID!
    ): Region
    RoadmapGame(
        "RoadmapGame document ID"
        id: ID!
    ): RoadmapGame
    RoadmapGameMarket(
        "RoadmapGameMarket document ID"
        id: ID!
    ): RoadmapGameMarket
    SanityFileAsset(
        "SanityFileAsset document ID"
        id: ID!
    ): SanityFileAsset
    SanityImageAsset(
        "SanityImageAsset document ID"
        id: ID!
    ): SanityImageAsset
    SectionGroupSliderGames(
        "SectionGroupSliderGames document ID"
        id: ID!
    ): SectionGroupSliderGames
    SectionSliderGames(
        "SectionSliderGames document ID"
        id: ID!
    ): SectionSliderGames
    SiteConfig(
        "SiteConfig document ID"
        id: ID!
    ): SiteConfig
    SitePage(
        "SitePage document ID"
        id: ID!
    ): SitePage
    SocialNetwork(
        "SocialNetwork document ID"
        id: ID!
    ): SocialNetwork
    Studio(
        "Studio document ID"
        id: ID!
    ): Studio
    Tournament(
        "Tournament document ID"
        id: ID!
    ): Tournament
    WebsiteConfig(
        "WebsiteConfig document ID"
        id: ID!
    ): WebsiteConfig
    WidgetPartners(
        "WidgetPartners document ID"
        id: ID!
    ): WidgetPartners
    WidgetSwiper(
        "WidgetSwiper document ID"
        id: ID!
    ): WidgetSwiper
    allBanner(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [BannerSorting!],
        where: BannerFilter
    ): [Banner!]!
    allCountry(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [CountrySorting!],
        where: CountryFilter
    ): [Country!]!
    allCurrency(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [CurrencySorting!],
        where: CurrencyFilter
    ): [Currency!]!
    allDocument(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [DocumentSorting!],
        where: DocumentFilter
    ): [Document!]!
    allEmailAddress(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [EmailAddressSorting!],
        where: EmailAddressFilter
    ): [EmailAddress!]!
    allEmployee(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [EmployeeSorting!],
        where: EmployeeFilter
    ): [Employee!]!
    allEngagementSlide(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [EngagementSlideSorting!],
        where: EngagementSlideFilter
    ): [EngagementSlide!]!
    allGame(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [GameSorting!],
        where: GameFilter
    ): [Game!]!
    allGameCategory(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [GameCategorySorting!],
        where: GameCategoryFilter
    ): [GameCategory!]!
    allGameCertificate(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [GameCertificateSorting!],
        where: GameCertificateFilter
    ): [GameCertificate!]!
    allGameCode(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [GameCodeSorting!],
        where: GameCodeFilter
    ): [GameCode!]!
    allGamePlaylist(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [GamePlaylistSorting!],
        where: GamePlaylistFilter
    ): [GamePlaylist!]!
    allGameRTP(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [GameRTPSorting!],
        where: GameRTPFilter
    ): [GameRTP!]!
    allGameRibbon(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [GameRibbonSorting!],
        where: GameRibbonFilter
    ): [GameRibbon!]!
    allGameType(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [GameTypeSorting!],
        where: GameTypeFilter
    ): [GameType!]!
    allJackpotInfo(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [JackpotInfoSorting!],
        where: JackpotInfoFilter
    ): [JackpotInfo!]!
    allLanguage(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [LanguageSorting!],
        where: LanguageFilter
    ): [Language!]!
    allLicense(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [LicenseSorting!],
        where: LicenseFilter
    ): [License!]!
    allLink(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [LinkSorting!],
        where: LinkFilter
    ): [Link!]!
    allLocalization(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [LocalizationSorting!],
        where: LocalizationFilter
    ): [Localization!]!
    allMarket(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [MarketSorting!],
        where: MarketFilter
    ): [Market!]!
    allMediaTag(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [MediaTagSorting!],
        where: MediaTagFilter
    ): [MediaTag!]!
    allNews(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [NewsSorting!],
        where: NewsFilter
    ): [News!]!
    allNewsCategory(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [NewsCategorySorting!],
        where: NewsCategoryFilter
    ): [NewsCategory!]!
    allOffice(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [OfficeSorting!],
        where: OfficeFilter
    ): [Office!]!
    allPageAboutUs(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [PageAboutUsSorting!],
        where: PageAboutUsFilter
    ): [PageAboutUs!]!
    allPageGameSearch(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [PageGameSearchSorting!],
        where: PageGameSearchFilter
    ): [PageGameSearch!]!
    allPageGames(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [PageGamesSorting!],
        where: PageGamesFilter
    ): [PageGames!]!
    allPageGamingSolutions(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [PageGamingSolutionsSorting!],
        where: PageGamingSolutionsFilter
    ): [PageGamingSolutions!]!
    allPageHome(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [PageHomeSorting!],
        where: PageHomeFilter
    ): [PageHome!]!
    allPageLiveCasino(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [PageLiveCasinoSorting!],
        where: PageLiveCasinoFilter
    ): [PageLiveCasino!]!
    allPageMediaServices(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [PageMediaServicesSorting!],
        where: PageMediaServicesFilter
    ): [PageMediaServices!]!
    allPageNews(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [PageNewsSorting!],
        where: PageNewsFilter
    ): [PageNews!]!
    allPageRoadmap(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [PageRoadmapSorting!],
        where: PageRoadmapFilter
    ): [PageRoadmap!]!
    allPageToolsBonusCoin(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [PageToolsBonusCoinSorting!],
        where: PageToolsBonusCoinFilter
    ): [PageToolsBonusCoin!]!
    allPageToolsGRC(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [PageToolsGRCSorting!],
        where: PageToolsGRCFilter
    ): [PageToolsGRC!]!
    allPageToolsJackpot(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [PageToolsJackpotSorting!],
        where: PageToolsJackpotFilter
    ): [PageToolsJackpot!]!
    allPageToolsTournament(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [PageToolsTournamentSorting!],
        where: PageToolsTournamentFilter
    ): [PageToolsTournament!]!
    allPageTournaments(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [PageTournamentsSorting!],
        where: PageTournamentsFilter
    ): [PageTournaments!]!
    allPartner(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [PartnerSorting!],
        where: PartnerFilter
    ): [Partner!]!
    allPhone(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [PhoneSorting!],
        where: PhoneFilter
    ): [Phone!]!
    allProvider(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [ProviderSorting!],
        where: ProviderFilter
    ): [Provider!]!
    allRegion(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [RegionSorting!],
        where: RegionFilter
    ): [Region!]!
    allRoadmapGame(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [RoadmapGameSorting!],
        where: RoadmapGameFilter
    ): [RoadmapGame!]!
    allRoadmapGameMarket(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [RoadmapGameMarketSorting!],
        where: RoadmapGameMarketFilter
    ): [RoadmapGameMarket!]!
    allSanityFileAsset(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [SanityFileAssetSorting!],
        where: SanityFileAssetFilter
    ): [SanityFileAsset!]!
    allSanityImageAsset(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [SanityImageAssetSorting!],
        where: SanityImageAssetFilter
    ): [SanityImageAsset!]!
    allSectionGroupSliderGames(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [SectionGroupSliderGamesSorting!],
        where: SectionGroupSliderGamesFilter
    ): [SectionGroupSliderGames!]!
    allSectionSliderGames(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [SectionSliderGamesSorting!],
        where: SectionSliderGamesFilter
    ): [SectionSliderGames!]!
    allSiteConfig(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [SiteConfigSorting!],
        where: SiteConfigFilter
    ): [SiteConfig!]!
    allSitePage(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [SitePageSorting!],
        where: SitePageFilter
    ): [SitePage!]!
    allSocialNetwork(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [SocialNetworkSorting!],
        where: SocialNetworkFilter
    ): [SocialNetwork!]!
    allStudio(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [StudioSorting!],
        where: StudioFilter
    ): [Studio!]!
    allTournament(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [TournamentSorting!],
        where: TournamentFilter
    ): [Tournament!]!
    allWebsiteConfig(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [WebsiteConfigSorting!],
        where: WebsiteConfigFilter
    ): [WebsiteConfig!]!
    allWidgetPartners(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [WidgetPartnersSorting!],
        where: WidgetPartnersFilter
    ): [WidgetPartners!]!
    allWidgetSwiper(
        "Max documents to return"
        limit: Int,
        "Offset at which to start returning documents from"
        offset: Int,
        sort: [WidgetSwiperSorting!],
        where: WidgetSwiperFilter
    ): [WidgetSwiper!]!
}

type SanityAssetSourceData {
    _key: String
    _type: String
    "The unique ID for the asset within the originating source so you can programatically find back to it"
    id: String
    "A canonical name for the source this asset is originating from"
    name: String
    "A URL to find more information about this asset in the originating source"
    url: String
}

type SanityFileAsset implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    altText: String
    assetId: String
    description: String
    extension: String
    label: String
    mimeType: String
    originalFilename: String
    path: String
    sha1hash: String
    size: Float
    source: SanityAssetSourceData
    title: String
    url: String
}

type SanityImageAsset implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    altText: String
    assetId: String
    description: String
    extension: String
    label: String
    metadata: SanityImageMetadata
    mimeType: String
    originalFilename: String
    path: String
    sha1hash: String
    size: Float
    source: SanityAssetSourceData
    title: String
    uploadId: String
    url: String
}

type SanityImageCrop {
    _key: String
    _type: String
    bottom: Float
    left: Float
    right: Float
    top: Float
}

type SanityImageDimensions {
    _key: String
    _type: String
    aspectRatio: Float
    height: Float
    width: Float
}

type SanityImageHotspot {
    _key: String
    _type: String
    height: Float
    width: Float
    x: Float
    y: Float
}

type SanityImageMetadata {
    _key: String
    _type: String
    blurHash: String
    dimensions: SanityImageDimensions
    hasAlpha: Boolean
    isOpaque: Boolean
    location: Geopoint
    lqip: String
    palette: SanityImagePalette
}

type SanityImagePalette {
    _key: String
    _type: String
    darkMuted: SanityImagePaletteSwatch
    darkVibrant: SanityImagePaletteSwatch
    dominant: SanityImagePaletteSwatch
    lightMuted: SanityImagePaletteSwatch
    lightVibrant: SanityImagePaletteSwatch
    muted: SanityImagePaletteSwatch
    vibrant: SanityImagePaletteSwatch
}

type SanityImagePaletteSwatch {
    _key: String
    _type: String
    background: String
    foreground: String
    population: Float
    title: String
}

type SectionGroupSliderGames implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    groupCode: Slug
    groupName: LocaleString
    isActive: Boolean
    region: String
    sliderGames: [SectionSliderGames]
}

type SectionSliderGames implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    games: [Game]
    isActive: Boolean
    isShowGamesNumber: Boolean
    isVisibleSectionLogo: Boolean
    layout: String
    region: String
    sectionCode: Slug
    sectionLogo: LocaleImage
    sectionName: LocaleString
    sortGames: String
}

type SingleLine1 {
    _key: String
    _type: String
    text: LocaleString
    textCode: Slug
}

type SingleLine2 {
    _key: String
    _type: String
    text1: LocaleString
    text2: LocaleString
    textCode: Slug
}

type SingleLine3 {
    _key: String
    _type: String
    text1: LocaleString
    text2: LocaleString
    text3: LocaleString
    textCode: Slug
}

type SiteConfig implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    emails: [EmailAddress]
    employees: [LinkOrSitePage]
    homePage: SitePage
    languages: [String]
    "A meta description is an element in the HTML code of a web page that provides a brief description of the content of that page."
    metaDescription: String
    "Meta Keywords are a specific type of meta tag that appear in the HTML code of a Web page and help tell search engines what the topic of the page is."
    metaKeywords: String
    metaRobots: String
    phones: [Phone]
    region: String
    title: LocaleString
}

type SitePage implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    code: Slug
    isActive: Boolean
    name: String
    region: String
    title: LocaleString
    version: String
    widgets: [WidgetPartners]
}

type Slug {
    _key: String
    _type: String
    current: String
}

type SocialNetwork implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    code: Slug
    isActive: Boolean
    snIcon: LocaleImage
    snName: LocaleString
    snUrl: String
}

type Span {
    _key: String
    _type: String
    marks: [String]
    text: String
}

type Studio implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    code: Slug
    studioName: LocaleString
    studioPhoto: LocaleImage
}

type Textarea {
    _key: String
    _type: String
    text: LocaleText
    textCode: Slug
}

type Tournament implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    code: Slug
    isActive: Boolean
    name: LocaleString
}

type UrlTarget {
    _key: String
    _type: String
    url: String
    urlTarget: String
}

type Video {
    _key: String
    _type: String
    file: File
    headerTicker: HeaderTicker
    height: Float
    mobileVideoPoster: LocaleImage
    type: String
    "Paste in the full URL for your video"
    url: String
    videoPoster: LocaleImage
    width: Float
}

type VideoUrl {
    _key: String
    _type: String
    file: File
    languageCode: String
    type: String
    url: String
}

type WebsiteConfig implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    emails: [EmailAddress]
    facebookUrl: String
    footerLicenses: [License]
    gameAPI: GameAPI
    instagramUrl: String
    linkedInUrl: String
    "A meta description is an element in the HTML code of a web page that provides a brief description of the content of that page."
    metaDescription: String
    "Meta Keywords are a specific type of meta tag that appear in the HTML code of a Web page and help tell search engines what the topic of the page is."
    metaKeywords: String
    metaRobots: String
    phones: [Phone]
    socialNetworks: [SocialNetwork]
    "SkyWind Group Corporate Portal"
    title: String
    youtubeUrl: String
}

type WidgetPartners implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    code: Slug
    isActive: Boolean
    name: String
    partners: [Partner]
    title: LocaleString
}

type WidgetSwiper implements Document {
    "Date the document was created"
    _createdAt: DateTime
    "Document ID"
    _id: ID
    _key: String
    "Current document revision"
    _rev: String
    "Document type"
    _type: String
    "Date the document was last modified"
    _updatedAt: DateTime
    code: Slug
    isActive: Boolean
    name: String
    swiperParameters: WidgetSwiperParameters
}

type WidgetSwiperParameters {
    _key: String
    _type: String
    "Set to false to disable swiping to next slide direction (to right or bottom). Default: true"
    allowSlideNext: Boolean
    "Set to false to disable swiping to previous slide direction (to left or top). Default: true"
    allowSlidePrev: Boolean
    "If false, then the only way to switch the slide is use of external API functions like slidePrev or slideNext. Default: true"
    allowTouchMove: Boolean
    "Set to true and slider wrapper will adapt its height to the height of the currently active slide. Default: false"
    autoHeight: Boolean
    "If true, then active slide will be centered, not always on the left side. Default: false"
    centeredSlides: Boolean
    "Can be 'horizontal' or 'vertical' (for vertical slider). Default: horizontal"
    direction: String
}

enum SortOrder {
    "Sorts on the value in ascending order."
    ASC
    "Sorts on the value in descending order."
    DESC
}

"A date string, such as 2007-12-03, compliant with the `full-date` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar."
scalar Date

"A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar."
scalar DateTime

"The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf)."
scalar JSON

input BannerAssetFilter {
    _key: StringFilter
    _type: StringFilter
    assetImage: LocaleImageFilter
    assetType: StringFilter
    assetVideo: FileFilter
}

input BannerAssetSorting {
    _key: SortOrder
    _type: SortOrder
    assetImage: LocaleImageSorting
    assetType: SortOrder
    assetVideo: FileSorting
}

input BannerFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    banner1920x120: BannerAssetFilter
    banner600x140: BannerAssetFilter
    bannerName: LocaleStringFilter
    bannerOnDesktop: StringFilter
    bannerOnMobileLandscape: StringFilter
    bannerOnMobilePortrait: StringFilter
    bannerType: StringFilter
    code: SlugFilter
    game: GameFilter
    isActive: BooleanFilter
    url: UrlTargetFilter
    urlType: StringFilter
}

input BannerSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    banner1920x120: BannerAssetSorting
    banner600x140: BannerAssetSorting
    bannerName: LocaleStringSorting
    bannerOnDesktop: SortOrder
    bannerOnMobileLandscape: SortOrder
    bannerOnMobilePortrait: SortOrder
    bannerType: SortOrder
    code: SlugSorting
    isActive: SortOrder
    url: UrlTargetSorting
    urlType: SortOrder
}

input BannersCarouselFilter {
    _key: StringFilter
    _type: StringFilter
    autoPlay: FloatFilter
    isActive: BooleanFilter
    isLoop: BooleanFilter
}

input BannersCarouselSorting {
    _key: SortOrder
    _type: SortOrder
    autoPlay: SortOrder
    isActive: SortOrder
    isLoop: SortOrder
}

input BooleanFilter {
    "Checks if the value is equal to the given input."
    eq: Boolean
    "Checks if the value is not equal to the given input."
    neq: Boolean
}

input ColorFilter {
    _key: StringFilter
    _type: StringFilter
    alpha: FloatFilter
    hex: StringFilter
    hsl: HslaColorFilter
    hsv: HsvaColorFilter
    rgb: RgbaColorFilter
}

input ColorSorting {
    _key: SortOrder
    _type: SortOrder
    alpha: SortOrder
    hex: SortOrder
    hsl: HslaColorSorting
    hsv: HsvaColorSorting
    rgb: RgbaColorSorting
}

input CountryFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    countryCode: StringFilter
    countryIcon: LocaleImageFilter
    countryName: StringFilter
    countryNativeName: StringFilter
}

input CountryGamesFilter {
    _key: StringFilter
    _type: StringFilter
    country: CountryFilter
}

input CountryGamesSorting {
    _key: SortOrder
    _type: SortOrder
}

input CountrySorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    countryCode: SortOrder
    countryIcon: LocaleImageSorting
    countryName: SortOrder
    countryNativeName: SortOrder
}

input CurrencyFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    currencyCode: StringFilter
    currencyIcon: LocaleImageFilter
    currencyName: StringFilter
    currencyNativeName: StringFilter
}

input CurrencySorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    currencyCode: SortOrder
    currencyIcon: LocaleImageSorting
    currencyName: SortOrder
    currencyNativeName: SortOrder
}

input DateFilter {
    "Checks if the value is equal to the given input."
    eq: Date
    "Checks if the value is greater than the given input."
    gt: Date
    "Checks if the value is greater than or equal to the given input."
    gte: Date
    "Checks if the value is lesser than the given input."
    lt: Date
    "Checks if the value is lesser than or equal to the given input."
    lte: Date
    "Checks if the value is not equal to the given input."
    neq: Date
}

input DatetimeFilter {
    "Checks if the value is equal to the given input."
    eq: DateTime
    "Checks if the value is greater than the given input."
    gt: DateTime
    "Checks if the value is greater than or equal to the given input."
    gte: DateTime
    "Checks if the value is lesser than the given input."
    lt: DateTime
    "Checks if the value is lesser than or equal to the given input."
    lte: DateTime
    "Checks if the value is not equal to the given input."
    neq: DateTime
}

input DocumentFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
}

input DocumentSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
}

input EmailAddressFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    code: SlugFilter
    emailAddress: StringFilter
    emailTitle: LocaleStringFilter
    isActive: BooleanFilter
}

input EmailAddressSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    code: SlugSorting
    emailAddress: SortOrder
    emailTitle: LocaleStringSorting
    isActive: SortOrder
}

input EmployeeFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    employeeDescription: LocaleTextFilter
    employeeName: LocaleStringFilter
    employeePhoto: LocaleImageFilter
    employeePosition: LocaleStringFilter
    isActive: BooleanFilter
}

input EmployeeSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    employeeDescription: LocaleTextSorting
    employeeName: LocaleStringSorting
    employeePhoto: LocaleImageSorting
    employeePosition: LocaleStringSorting
    isActive: SortOrder
}

input EngagementSlideFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    header1: LocaleStringFilter
    header2: LocaleStringFilter
    image: LocaleImageFilter
    imageType: StringFilter
    isActive: BooleanFilter
    text1: LocaleTextFilter
    text2: LocaleTextFilter
}

input EngagementSlideSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    header1: LocaleStringSorting
    header2: LocaleStringSorting
    image: LocaleImageSorting
    imageType: SortOrder
    isActive: SortOrder
    text1: LocaleTextSorting
    text2: LocaleTextSorting
}

input FileFilter {
    _key: StringFilter
    _type: StringFilter
    asset: SanityFileAssetFilter
}

input FileSorting {
    _key: SortOrder
    _type: SortOrder
}

input FloatFilter {
    "Checks if the value is equal to the given input."
    eq: Float
    "Checks if the value is greater than the given input."
    gt: Float
    "Checks if the value is greater than or equal to the given input."
    gte: Float
    "Checks if the value is lesser than the given input."
    lt: Float
    "Checks if the value is lesser than or equal to the given input."
    lte: Float
    "Checks if the value is not equal to the given input."
    neq: Float
}

input GameAPIFilter {
    _key: StringFilter
    _type: StringFilter
    playerApiUrl: StringFilter
    siteApiUrl: StringFilter
    siteToken: StringFilter
}

input GameAPISorting {
    _key: SortOrder
    _type: SortOrder
    playerApiUrl: SortOrder
    siteApiUrl: SortOrder
    siteToken: SortOrder
}

input GameCategoryFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    categoryCode: SlugFilter
    categoryName: LocaleStringFilter
    categoryRegion: StringFilter
    categoryType: StringFilter
    isActive: BooleanFilter
    showGamesFrom: StringFilter
}

input GameCategorySorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    categoryCode: SlugSorting
    categoryName: LocaleStringSorting
    categoryRegion: SortOrder
    categoryType: SortOrder
    isActive: SortOrder
    showGamesFrom: SortOrder
}

input GameCertificateFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    gameCertificateCode: SlugFilter
    gameCertificateName: LocaleStringFilter
}

input GameCertificateSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    gameCertificateCode: SlugSorting
    gameCertificateName: LocaleStringSorting
}

input GameCodeFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    gameCode: StringFilter
    gameInfo: StringFilter
    gameProvider: ProviderFilter
    marketingMaterials: StringFilter
}

input GameCodeSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    gameCode: SortOrder
    gameInfo: SortOrder
    marketingMaterials: SortOrder
}

input GameFeaturesFilter {
    _key: StringFilter
    _type: StringFilter
    gameFeatureDescription: LocaleTextFilter
    gameFeaturePoster: LocaleImageFilter
    gameFeatureTitle: LocaleStringFilter
}

input GameFeaturesSorting {
    _key: SortOrder
    _type: SortOrder
    gameFeatureDescription: LocaleTextSorting
    gameFeaturePoster: LocaleImageSorting
    gameFeatureTitle: LocaleStringSorting
}

input GameFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    gameBillboard: LocaleImageFilter
    gameBrandedPoster: LocaleImageFilter
    gameDescription: LocaleTextFilter
    gameHDPoster: LocaleImageFilter
    gameIcon: LocaleImageFilter
    gameId: SlugFilter
    gameImage: LocaleImageFilter
    gameInfo: GameInfoFilter
    gameLogo: LocaleImageFilter
    gameManage: GameManageFilter
    gameName: LocaleStringFilter
    gameOrientation: StringFilter
    gamePoster: LocaleImageFilter
    gameRibbon: GameRibbonFilter
    gameTop4Poster: LocaleImageFilter
    gameTowerPoster: LocaleImageFilter
    gameVideo: VideoFilter
    header: StringFilter
    isActive: BooleanFilter
    paUrl: StringFilter
    releaseNote: FileFilter
    saleSheet: FileFilter
    showGameCode: StringFilter
    showGameRTP: StringFilter
    siteDescription: LocaleTextFilter
}

input GameInfoFilter {
    _key: StringFilter
    _type: StringFilter
    formatOfReleaseDate: StringFilter
    isFreeBets: BooleanFilter
    isLuckyEnvelopes: BooleanFilter
    isMustWinJackpot: BooleanFilter
    isTournaments: BooleanFilter
    layout: StringFilter
    rtp: FloatFilter
    volatility: StringFilter
    ways: StringFilter
}

input GameInfoSorting {
    _key: SortOrder
    _type: SortOrder
    formatOfReleaseDate: SortOrder
    isFreeBets: SortOrder
    isLuckyEnvelopes: SortOrder
    isMustWinJackpot: SortOrder
    isTournaments: SortOrder
    layout: SortOrder
    rtp: SortOrder
    volatility: SortOrder
    ways: SortOrder
}

input GameLineFilter {
    _key: StringFilter
    _type: StringFilter
    game: GameFilter
    gameRibbon: GameRibbonFilter
    layout: StringFilter
}

input GameLineSorting {
    _key: SortOrder
    _type: SortOrder
    layout: SortOrder
}

input GameManageFilter {
    _key: StringFilter
    _type: StringFilter
    releaseDate: DateFilter
    releaseText: LocaleStringFilter
    status: StringFilter
}

input GameManageSorting {
    _key: SortOrder
    _type: SortOrder
    releaseDate: SortOrder
    releaseText: LocaleStringSorting
    status: SortOrder
}

input GameMarketCertificatesFilter {
    _key: StringFilter
    _type: StringFilter
    certificate: FileFilter
    certificate_1: FileFilter
    certificate_2: FileFilter
    comingSoon: BooleanFilter
    gameMarket: MarketFilter
}

input GameMarketCertificatesSorting {
    _key: SortOrder
    _type: SortOrder
    certificate: FileSorting
    certificate_1: FileSorting
    certificate_2: FileSorting
    comingSoon: SortOrder
}

input GamePlaylistFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    isActive: BooleanFilter
    playlistBanner: LocaleImageFilter
    playlistCode: SlugFilter
    playlistDescription: LocaleTextFilter
    playlistName: LocaleStringFilter
    playlistPoster: LocaleImageFilter
    playlistRegion: StringFilter
}

input GamePlaylistSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    isActive: SortOrder
    playlistBanner: LocaleImageSorting
    playlistCode: SlugSorting
    playlistDescription: LocaleTextSorting
    playlistName: LocaleStringSorting
    playlistPoster: LocaleImageSorting
    playlistRegion: SortOrder
}

input GameRTPFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    gameRTP: StringFilter
}

input GameRTPSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    gameRTP: SortOrder
}

input GameReleaseDateFilter {
    _key: StringFilter
    _type: StringFilter
    gameMarket: MarketFilter
    releaseDate: DateFilter
}

input GameReleaseDateSorting {
    _key: SortOrder
    _type: SortOrder
    releaseDate: SortOrder
}

input GameRibbonFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    gameRibbonCode: SlugFilter
    gameRibbonImage: LocaleImageFilter
    gameRibbonName: LocaleStringFilter
}

input GameRibbonSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    gameRibbonCode: SlugSorting
    gameRibbonImage: LocaleImageSorting
    gameRibbonName: LocaleStringSorting
}

input GameSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    gameBillboard: LocaleImageSorting
    gameBrandedPoster: LocaleImageSorting
    gameDescription: LocaleTextSorting
    gameHDPoster: LocaleImageSorting
    gameIcon: LocaleImageSorting
    gameId: SlugSorting
    gameImage: LocaleImageSorting
    gameInfo: GameInfoSorting
    gameLogo: LocaleImageSorting
    gameManage: GameManageSorting
    gameName: LocaleStringSorting
    gameOrientation: SortOrder
    gamePoster: LocaleImageSorting
    gameTop4Poster: LocaleImageSorting
    gameTowerPoster: LocaleImageSorting
    gameVideo: VideoSorting
    header: SortOrder
    isActive: SortOrder
    paUrl: SortOrder
    releaseNote: FileSorting
    saleSheet: FileSorting
    showGameCode: SortOrder
    showGameRTP: SortOrder
    siteDescription: LocaleTextSorting
}

input GameTypeFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    gameTypeCode: SlugFilter
    gameTypeTitle: LocaleStringFilter
}

input GameTypeSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    gameTypeCode: SlugSorting
    gameTypeTitle: LocaleStringSorting
}

input GeopointFilter {
    _key: StringFilter
    _type: StringFilter
    alt: FloatFilter
    lat: FloatFilter
    lng: FloatFilter
}

input GeopointSorting {
    _key: SortOrder
    _type: SortOrder
    alt: SortOrder
    lat: SortOrder
    lng: SortOrder
}

input HeaderTextFilter {
    _key: StringFilter
    _type: StringFilter
    headerTextArea: LocaleTextFilter
    headerTitle: HeaderTitleFilter
}

input HeaderTextSorting {
    _key: SortOrder
    _type: SortOrder
    headerTextArea: LocaleTextSorting
    headerTitle: HeaderTitleSorting
}

input HeaderTickerFilter {
    _key: StringFilter
    _type: StringFilter
    isActive: BooleanFilter
    tickerDate: DateFilter
}

input HeaderTickerSorting {
    _key: SortOrder
    _type: SortOrder
    isActive: SortOrder
    tickerDate: SortOrder
}

input HeaderTitleFilter {
    _key: StringFilter
    _type: StringFilter
    title1: LocaleStringFilter
    title2: LocaleStringFilter
    title3: LocaleStringFilter
}

input HeaderTitleSorting {
    _key: SortOrder
    _type: SortOrder
    title1: LocaleStringSorting
    title2: LocaleStringSorting
    title3: LocaleStringSorting
}

input HslaColorFilter {
    _key: StringFilter
    _type: StringFilter
    a: FloatFilter
    h: FloatFilter
    l: FloatFilter
    s: FloatFilter
}

input HslaColorSorting {
    _key: SortOrder
    _type: SortOrder
    a: SortOrder
    h: SortOrder
    l: SortOrder
    s: SortOrder
}

input HsvaColorFilter {
    _key: StringFilter
    _type: StringFilter
    a: FloatFilter
    h: FloatFilter
    s: FloatFilter
    v: FloatFilter
}

input HsvaColorSorting {
    _key: SortOrder
    _type: SortOrder
    a: SortOrder
    h: SortOrder
    s: SortOrder
    v: SortOrder
}

input IDFilter {
    "Checks if the value is equal to the given input."
    eq: ID
    in: [ID!]
    "Checks if the value matches the given word/words."
    matches: ID
    "Checks if the value is not equal to the given input."
    neq: ID
    nin: [ID!]
}

input ImageFilter {
    _key: StringFilter
    _type: StringFilter
    asset: SanityImageAssetFilter
    crop: SanityImageCropFilter
    hotspot: SanityImageHotspotFilter
}

input ImageSorting {
    _key: SortOrder
    _type: SortOrder
    crop: SanityImageCropSorting
    hotspot: SanityImageHotspotSorting
}

input IntFilter {
    "Checks if the value is equal to the given input."
    eq: Int
    "Checks if the value is greater than the given input."
    gt: Int
    "Checks if the value is greater than or equal to the given input."
    gte: Int
    "Checks if the value is lesser than the given input."
    lt: Int
    "Checks if the value is lesser than or equal to the given input."
    lte: Int
    "Checks if the value is not equal to the given input."
    neq: Int
}

input JackpotInfoFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    code: SlugFilter
    description: LocaleTextFilter
    header: LocaleStringFilter
    icon: LocaleImageFilter
    iconHover: LocaleImageFilter
    isActive: BooleanFilter
}

input JackpotInfoSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    code: SlugSorting
    description: LocaleTextSorting
    header: LocaleStringSorting
    icon: LocaleImageSorting
    iconHover: LocaleImageSorting
    isActive: SortOrder
}

input LanguageFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    languageCode: StringFilter
    languageIcon: LocaleImageFilter
    languageName: StringFilter
    languageNativeName: StringFilter
}

input LanguageSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    languageCode: SortOrder
    languageIcon: LocaleImageSorting
    languageName: SortOrder
    languageNativeName: SortOrder
}

input LicenseFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    isActive: BooleanFilter
    jurisdiction: LocaleStringFilter
    licenseDescription: LocaleTextFilter
    licenseNo: StringFilter
    licensePhoto: LocaleImageFilter
    licenseUrl: StringFilter
    regulator: LocaleStringFilter
}

input LicenseSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    isActive: SortOrder
    jurisdiction: LocaleStringSorting
    licenseDescription: LocaleTextSorting
    licenseNo: SortOrder
    licensePhoto: LocaleImageSorting
    licenseUrl: SortOrder
    regulator: LocaleStringSorting
}

input LinkFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    code: SlugFilter
    isActive: BooleanFilter
    isExternal: BooleanFilter
    title: LocaleStringFilter
}

input LinkSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    code: SlugSorting
    isActive: SortOrder
    isExternal: SortOrder
    title: LocaleStringSorting
}

input LocaleBlockFilter {
    _key: StringFilter
    _type: StringFilter
}

input LocaleBlockSorting {
    _key: SortOrder
    _type: SortOrder
}

input LocaleImageFilter {
    _key: StringFilter
    _type: StringFilter
    en: ImageFilter
    zhCn: ImageFilter
}

input LocaleImageSorting {
    _key: SortOrder
    _type: SortOrder
    en: ImageSorting
    zhCn: ImageSorting
}

input LocaleStringFilter {
    _key: StringFilter
    _type: StringFilter
    en: StringFilter
    zhCn: StringFilter
}

input LocaleStringSorting {
    _key: SortOrder
    _type: SortOrder
    en: SortOrder
    zhCn: SortOrder
}

input LocaleTextFilter {
    _key: StringFilter
    _type: StringFilter
    en: StringFilter
    zhCn: StringFilter
}

input LocaleTextSorting {
    _key: SortOrder
    _type: SortOrder
    en: SortOrder
    zhCn: SortOrder
}

input LocaleUrlFilter {
    _key: StringFilter
    _type: StringFilter
    en: StringFilter
    zhCn: StringFilter
}

input LocaleUrlSorting {
    _key: SortOrder
    _type: SortOrder
    en: SortOrder
    zhCn: SortOrder
}

input LocalizationFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    textCode: SlugFilter
}

input LocalizationSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    textCode: SlugSorting
}

input MaintenanceFilter {
    _key: StringFilter
    _type: StringFilter
    isActive: BooleanFilter
    landscapeImage: LocaleImageFilter
    portraitImage: LocaleImageFilter
    textMessage: LocaleStringFilter
}

input MaintenanceSorting {
    _key: SortOrder
    _type: SortOrder
    isActive: SortOrder
    landscapeImage: LocaleImageSorting
    portraitImage: LocaleImageSorting
    textMessage: LocaleStringSorting
}

input MarketFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    code: SlugFilter
    isActive: BooleanFilter
    marketIcon: LocaleImageFilter
    marketName: LocaleStringFilter
}

input MarketSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    code: SlugSorting
    isActive: SortOrder
    marketIcon: LocaleImageSorting
    marketName: LocaleStringSorting
}

input MediaServicesBlockFilter {
    _key: StringFilter
    _type: StringFilter
    blockCode: SlugFilter
    header: LocaleStringFilter
    icon: LocaleImageFilter
    iconHover: LocaleImageFilter
    text: LocaleTextFilter
}

input MediaServicesBlockSorting {
    _key: SortOrder
    _type: SortOrder
    blockCode: SlugSorting
    header: LocaleStringSorting
    icon: LocaleImageSorting
    iconHover: LocaleImageSorting
    text: LocaleTextSorting
}

input MediaServicesTechFilter {
    _key: StringFilter
    _type: StringFilter
    blockCode: SlugFilter
    cardType: StringFilter
    header: LocaleStringFilter
    icon: LocaleImageFilter
    iconHover: LocaleImageFilter
    text: LocaleTextFilter
}

input MediaServicesTechSorting {
    _key: SortOrder
    _type: SortOrder
    blockCode: SlugSorting
    cardType: SortOrder
    header: LocaleStringSorting
    icon: LocaleImageSorting
    iconHover: LocaleImageSorting
    text: LocaleTextSorting
}

input MediaTagFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    name: SlugFilter
}

input MediaTagSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    name: SlugSorting
}

input NewsCategoryFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    newsCategoryCode: SlugFilter
    newsCategoryTitle: LocaleStringFilter
}

input NewsCategorySorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    newsCategoryCode: SlugSorting
    newsCategoryTitle: LocaleStringSorting
}

input NewsFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    block: LocaleBlockFilter
    game: GameFilter
    headerPromoVideo: VideoFilter
    highlightedTitle: LocaleStringFilter
    isActive: BooleanFilter
    newsDate: DateFilter
    newsPreviewImage: LocaleImageFilter
    newsType: StringFilter
    region: StringFilter
    text: LocaleTextFilter
    textType: StringFilter
    title: LocaleStringFilter
}

input NewsSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    block: LocaleBlockSorting
    headerPromoVideo: VideoSorting
    highlightedTitle: LocaleStringSorting
    isActive: SortOrder
    newsDate: SortOrder
    newsPreviewImage: LocaleImageSorting
    newsType: SortOrder
    region: SortOrder
    text: LocaleTextSorting
    textType: SortOrder
    title: LocaleStringSorting
}

input OfficeFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    address: LocaleTextFilter
    code: SlugFilter
    companyName: LocaleStringFilter
    country: LocaleStringFilter
    email: StringFilter
    isActive: BooleanFilter
    leftOnMap: FloatFilter
    officeName: LocaleStringFilter
    officePhoto: LocaleImageFilter
    phone1: StringFilter
    phone2: StringFilter
    topOnMap: FloatFilter
}

input OfficeSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    address: LocaleTextSorting
    code: SlugSorting
    companyName: LocaleStringSorting
    country: LocaleStringSorting
    email: SortOrder
    isActive: SortOrder
    leftOnMap: SortOrder
    officeName: LocaleStringSorting
    officePhoto: LocaleImageSorting
    phone1: SortOrder
    phone2: SortOrder
    topOnMap: SortOrder
}

input PageAboutUsFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    canDeploy: BooleanFilter
    headerPromoVideo: VideoFilter
    headerText: HeaderTextFilter
    isActive: BooleanFilter
    pageTitle: LocaleStringFilter
}

input PageAboutUsSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    canDeploy: SortOrder
    headerPromoVideo: VideoSorting
    headerText: HeaderTextSorting
    isActive: SortOrder
    pageTitle: LocaleStringSorting
}

input PageGameSearchFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    banners: BannersCarouselFilter
    pageTitle: LocaleStringFilter
}

input PageGameSearchSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    banners: BannersCarouselSorting
    pageTitle: LocaleStringSorting
}

input PageGamesFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    canDeploy: BooleanFilter
    headerPromoVideo: VideoFilter
    headerText: HeaderTextFilter
    isActive: BooleanFilter
    pageTitle: LocaleStringFilter
}

input PageGamesSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    canDeploy: SortOrder
    headerPromoVideo: VideoSorting
    headerText: HeaderTextSorting
    isActive: SortOrder
    pageTitle: LocaleStringSorting
}

input PageGamingSolutionsFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    canDeploy: BooleanFilter
    headerText: HeaderTextFilter
    isActive: BooleanFilter
    pageTitle: LocaleStringFilter
}

input PageGamingSolutionsSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    canDeploy: SortOrder
    headerText: HeaderTextSorting
    isActive: SortOrder
    pageTitle: LocaleStringSorting
}

input PageHomeFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    canDeploy: BooleanFilter
    headerPromoVideo: VideoFilter
    headerText: HeaderTextFilter
    isActive: BooleanFilter
    pageTitle: LocaleStringFilter
    topGame1: GameLineFilter
    topGame2: GameLineFilter
    topGame3: GameLineFilter
    topGame4: GameLineFilter
}

input PageHomeSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    canDeploy: SortOrder
    headerPromoVideo: VideoSorting
    headerText: HeaderTextSorting
    isActive: SortOrder
    pageTitle: LocaleStringSorting
    topGame1: GameLineSorting
    topGame2: GameLineSorting
    topGame3: GameLineSorting
    topGame4: GameLineSorting
}

input PageLiveCasinoFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    canDeploy: BooleanFilter
    headerPromoVideo: VideoFilter
    headerText: HeaderTextFilter
    isActive: BooleanFilter
    pageTitle: LocaleStringFilter
}

input PageLiveCasinoSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    canDeploy: SortOrder
    headerPromoVideo: VideoSorting
    headerText: HeaderTextSorting
    isActive: SortOrder
    pageTitle: LocaleStringSorting
}

input PageMediaServicesFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    canDeploy: BooleanFilter
    headerText: HeaderTextFilter
    isActive: BooleanFilter
    pageTitle: LocaleStringFilter
}

input PageMediaServicesSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    canDeploy: SortOrder
    headerText: HeaderTextSorting
    isActive: SortOrder
    pageTitle: LocaleStringSorting
}

input PageNewsFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    banners: BannersCarouselFilter
    canDeploy: BooleanFilter
    headerPromoVideo: VideoFilter
    headerText: HeaderTextFilter
    isActive: BooleanFilter
    pageTitle: LocaleStringFilter
}

input PageNewsSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    banners: BannersCarouselSorting
    canDeploy: SortOrder
    headerPromoVideo: VideoSorting
    headerText: HeaderTextSorting
    isActive: SortOrder
    pageTitle: LocaleStringSorting
}

input PageRoadmapFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    banners: BannersCarouselFilter
    defaultMarket: MarketFilter
    latestReleasesMonth: FloatFilter
    pageTitle: LocaleStringFilter
    showPerMonthToolbar: BooleanFilter
    upcomingMonthThirdParty: FloatFilter
}

input PageRoadmapSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    banners: BannersCarouselSorting
    latestReleasesMonth: SortOrder
    pageTitle: LocaleStringSorting
    showPerMonthToolbar: SortOrder
    upcomingMonthThirdParty: SortOrder
}

input PageToolsBonusCoinFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    canDeploy: BooleanFilter
    headerPromoVideo: VideoFilter
    headerText: HeaderTextFilter
    isActive: BooleanFilter
    pageTitle: LocaleStringFilter
}

input PageToolsBonusCoinSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    canDeploy: SortOrder
    headerPromoVideo: VideoSorting
    headerText: HeaderTextSorting
    isActive: SortOrder
    pageTitle: LocaleStringSorting
}

input PageToolsGRCFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    canDeploy: BooleanFilter
    headerText: HeaderTextFilter
    isActive: BooleanFilter
    pageTitle: LocaleStringFilter
}

input PageToolsGRCSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    canDeploy: SortOrder
    headerText: HeaderTextSorting
    isActive: SortOrder
    pageTitle: LocaleStringSorting
}

input PageToolsJackpotFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    canDeploy: BooleanFilter
    headerText: HeaderTextFilter
    isActive: BooleanFilter
    pageTitle: LocaleStringFilter
}

input PageToolsJackpotSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    canDeploy: SortOrder
    headerText: HeaderTextSorting
    isActive: SortOrder
    pageTitle: LocaleStringSorting
}

input PageToolsTournamentFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    canDeploy: BooleanFilter
    headerText: HeaderTextFilter
    isActive: BooleanFilter
    pageTitle: LocaleStringFilter
}

input PageToolsTournamentSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    canDeploy: SortOrder
    headerText: HeaderTextSorting
    isActive: SortOrder
    pageTitle: LocaleStringSorting
}

input PageTournamentsFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    maintenance: MaintenanceFilter
    pageTitle: LocaleStringFilter
}

input PageTournamentsSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    maintenance: MaintenanceSorting
    pageTitle: LocaleStringSorting
}

input PartnerFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    isActive: BooleanFilter
    partnerCode: SlugFilter
    partnerImage: LocaleImageFilter
    partnerName: LocaleStringFilter
    partnerUrl: StringFilter
}

input PartnerSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    isActive: SortOrder
    partnerCode: SlugSorting
    partnerImage: LocaleImageSorting
    partnerName: LocaleStringSorting
    partnerUrl: SortOrder
}

input PhoneFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    code: SlugFilter
    isActive: BooleanFilter
    phoneNumber: StringFilter
    phoneTitle: LocaleStringFilter
}

input PhoneSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    code: SlugSorting
    isActive: SortOrder
    phoneNumber: SortOrder
    phoneTitle: LocaleStringSorting
}

input ProviderFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    providerCode: StringFilter
    providerName: StringFilter
}

input ProviderSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    providerCode: SortOrder
    providerName: SortOrder
}

input RegionFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    regionCode: StringFilter
}

input RegionSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    regionCode: SortOrder
}

input RgbaColorFilter {
    _key: StringFilter
    _type: StringFilter
    a: FloatFilter
    b: FloatFilter
    g: FloatFilter
    r: FloatFilter
}

input RgbaColorSorting {
    _key: SortOrder
    _type: SortOrder
    a: SortOrder
    b: SortOrder
    g: SortOrder
    r: SortOrder
}

input RoadmapGameFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    comingSoon: BooleanFilter
    game: GameFilter
    hideInAllMarkets: BooleanFilter
}

input RoadmapGameMarketFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    endDate: DateFilter
    gameMarket: MarketFilter
    startDate: DateFilter
}

input RoadmapGameMarketSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    endDate: SortOrder
    startDate: SortOrder
}

input RoadmapGameSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    comingSoon: SortOrder
    hideInAllMarkets: SortOrder
}

input SanityAssetSourceDataFilter {
    _key: StringFilter
    _type: StringFilter
    id: StringFilter
    name: StringFilter
    url: StringFilter
}

input SanityAssetSourceDataSorting {
    _key: SortOrder
    _type: SortOrder
    id: SortOrder
    name: SortOrder
    url: SortOrder
}

input SanityFileAssetFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    altText: StringFilter
    assetId: StringFilter
    description: StringFilter
    extension: StringFilter
    label: StringFilter
    mimeType: StringFilter
    originalFilename: StringFilter
    path: StringFilter
    sha1hash: StringFilter
    size: FloatFilter
    source: SanityAssetSourceDataFilter
    title: StringFilter
    url: StringFilter
}

input SanityFileAssetSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    altText: SortOrder
    assetId: SortOrder
    description: SortOrder
    extension: SortOrder
    label: SortOrder
    mimeType: SortOrder
    originalFilename: SortOrder
    path: SortOrder
    sha1hash: SortOrder
    size: SortOrder
    source: SanityAssetSourceDataSorting
    title: SortOrder
    url: SortOrder
}

input SanityImageAssetFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    altText: StringFilter
    assetId: StringFilter
    description: StringFilter
    extension: StringFilter
    label: StringFilter
    metadata: SanityImageMetadataFilter
    mimeType: StringFilter
    originalFilename: StringFilter
    path: StringFilter
    sha1hash: StringFilter
    size: FloatFilter
    source: SanityAssetSourceDataFilter
    title: StringFilter
    uploadId: StringFilter
    url: StringFilter
}

input SanityImageAssetSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    altText: SortOrder
    assetId: SortOrder
    description: SortOrder
    extension: SortOrder
    label: SortOrder
    metadata: SanityImageMetadataSorting
    mimeType: SortOrder
    originalFilename: SortOrder
    path: SortOrder
    sha1hash: SortOrder
    size: SortOrder
    source: SanityAssetSourceDataSorting
    title: SortOrder
    uploadId: SortOrder
    url: SortOrder
}

input SanityImageCropFilter {
    _key: StringFilter
    _type: StringFilter
    bottom: FloatFilter
    left: FloatFilter
    right: FloatFilter
    top: FloatFilter
}

input SanityImageCropSorting {
    _key: SortOrder
    _type: SortOrder
    bottom: SortOrder
    left: SortOrder
    right: SortOrder
    top: SortOrder
}

input SanityImageDimensionsFilter {
    _key: StringFilter
    _type: StringFilter
    aspectRatio: FloatFilter
    height: FloatFilter
    width: FloatFilter
}

input SanityImageDimensionsSorting {
    _key: SortOrder
    _type: SortOrder
    aspectRatio: SortOrder
    height: SortOrder
    width: SortOrder
}

input SanityImageHotspotFilter {
    _key: StringFilter
    _type: StringFilter
    height: FloatFilter
    width: FloatFilter
    x: FloatFilter
    y: FloatFilter
}

input SanityImageHotspotSorting {
    _key: SortOrder
    _type: SortOrder
    height: SortOrder
    width: SortOrder
    x: SortOrder
    y: SortOrder
}

input SanityImageMetadataFilter {
    _key: StringFilter
    _type: StringFilter
    blurHash: StringFilter
    dimensions: SanityImageDimensionsFilter
    hasAlpha: BooleanFilter
    isOpaque: BooleanFilter
    location: GeopointFilter
    lqip: StringFilter
    palette: SanityImagePaletteFilter
}

input SanityImageMetadataSorting {
    _key: SortOrder
    _type: SortOrder
    blurHash: SortOrder
    dimensions: SanityImageDimensionsSorting
    hasAlpha: SortOrder
    isOpaque: SortOrder
    location: GeopointSorting
    lqip: SortOrder
    palette: SanityImagePaletteSorting
}

input SanityImagePaletteFilter {
    _key: StringFilter
    _type: StringFilter
    darkMuted: SanityImagePaletteSwatchFilter
    darkVibrant: SanityImagePaletteSwatchFilter
    dominant: SanityImagePaletteSwatchFilter
    lightMuted: SanityImagePaletteSwatchFilter
    lightVibrant: SanityImagePaletteSwatchFilter
    muted: SanityImagePaletteSwatchFilter
    vibrant: SanityImagePaletteSwatchFilter
}

input SanityImagePaletteSorting {
    _key: SortOrder
    _type: SortOrder
    darkMuted: SanityImagePaletteSwatchSorting
    darkVibrant: SanityImagePaletteSwatchSorting
    dominant: SanityImagePaletteSwatchSorting
    lightMuted: SanityImagePaletteSwatchSorting
    lightVibrant: SanityImagePaletteSwatchSorting
    muted: SanityImagePaletteSwatchSorting
    vibrant: SanityImagePaletteSwatchSorting
}

input SanityImagePaletteSwatchFilter {
    _key: StringFilter
    _type: StringFilter
    background: StringFilter
    foreground: StringFilter
    population: FloatFilter
    title: StringFilter
}

input SanityImagePaletteSwatchSorting {
    _key: SortOrder
    _type: SortOrder
    background: SortOrder
    foreground: SortOrder
    population: SortOrder
    title: SortOrder
}

input Sanity_DocumentFilter {
    "All documents that are drafts."
    is_draft: Boolean
    "All documents referencing the given document ID."
    references: ID
}

input SectionGroupSliderGamesFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    groupCode: SlugFilter
    groupName: LocaleStringFilter
    isActive: BooleanFilter
    region: StringFilter
}

input SectionGroupSliderGamesSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    groupCode: SlugSorting
    groupName: LocaleStringSorting
    isActive: SortOrder
    region: SortOrder
}

input SectionSliderGamesFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    isActive: BooleanFilter
    isShowGamesNumber: BooleanFilter
    isVisibleSectionLogo: BooleanFilter
    layout: StringFilter
    region: StringFilter
    sectionCode: SlugFilter
    sectionLogo: LocaleImageFilter
    sectionName: LocaleStringFilter
    sortGames: StringFilter
}

input SectionSliderGamesSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    isActive: SortOrder
    isShowGamesNumber: SortOrder
    isVisibleSectionLogo: SortOrder
    layout: SortOrder
    region: SortOrder
    sectionCode: SlugSorting
    sectionLogo: LocaleImageSorting
    sectionName: LocaleStringSorting
    sortGames: SortOrder
}

input SingleLine1Filter {
    _key: StringFilter
    _type: StringFilter
    text: LocaleStringFilter
    textCode: SlugFilter
}

input SingleLine1Sorting {
    _key: SortOrder
    _type: SortOrder
    text: LocaleStringSorting
    textCode: SlugSorting
}

input SingleLine2Filter {
    _key: StringFilter
    _type: StringFilter
    text1: LocaleStringFilter
    text2: LocaleStringFilter
    textCode: SlugFilter
}

input SingleLine2Sorting {
    _key: SortOrder
    _type: SortOrder
    text1: LocaleStringSorting
    text2: LocaleStringSorting
    textCode: SlugSorting
}

input SingleLine3Filter {
    _key: StringFilter
    _type: StringFilter
    text1: LocaleStringFilter
    text2: LocaleStringFilter
    text3: LocaleStringFilter
    textCode: SlugFilter
}

input SingleLine3Sorting {
    _key: SortOrder
    _type: SortOrder
    text1: LocaleStringSorting
    text2: LocaleStringSorting
    text3: LocaleStringSorting
    textCode: SlugSorting
}

input SiteConfigFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    homePage: SitePageFilter
    metaDescription: StringFilter
    metaKeywords: StringFilter
    metaRobots: StringFilter
    region: StringFilter
    title: LocaleStringFilter
}

input SiteConfigSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    metaDescription: SortOrder
    metaKeywords: SortOrder
    metaRobots: SortOrder
    region: SortOrder
    title: LocaleStringSorting
}

input SitePageFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    code: SlugFilter
    isActive: BooleanFilter
    name: StringFilter
    region: StringFilter
    title: LocaleStringFilter
    version: StringFilter
}

input SitePageSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    code: SlugSorting
    isActive: SortOrder
    name: SortOrder
    region: SortOrder
    title: LocaleStringSorting
    version: SortOrder
}

input SlugFilter {
    _key: StringFilter
    _type: StringFilter
    current: StringFilter
}

input SlugSorting {
    _key: SortOrder
    _type: SortOrder
    current: SortOrder
}

input SocialNetworkFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    code: SlugFilter
    isActive: BooleanFilter
    snIcon: LocaleImageFilter
    snName: LocaleStringFilter
    snUrl: StringFilter
}

input SocialNetworkSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    code: SlugSorting
    isActive: SortOrder
    snIcon: LocaleImageSorting
    snName: LocaleStringSorting
    snUrl: SortOrder
}

input StringFilter {
    "Checks if the value is equal to the given input."
    eq: String
    in: [String!]
    "Checks if the value matches the given word/words."
    matches: String
    "Checks if the value is not equal to the given input."
    neq: String
    nin: [String!]
}

input StudioFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    code: SlugFilter
    studioName: LocaleStringFilter
    studioPhoto: LocaleImageFilter
}

input StudioSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    code: SlugSorting
    studioName: LocaleStringSorting
    studioPhoto: LocaleImageSorting
}

input TextareaFilter {
    _key: StringFilter
    _type: StringFilter
    text: LocaleTextFilter
    textCode: SlugFilter
}

input TextareaSorting {
    _key: SortOrder
    _type: SortOrder
    text: LocaleTextSorting
    textCode: SlugSorting
}

input TournamentFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    code: SlugFilter
    isActive: BooleanFilter
    name: LocaleStringFilter
}

input TournamentSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    code: SlugSorting
    isActive: SortOrder
    name: LocaleStringSorting
}

input UrlTargetFilter {
    _key: StringFilter
    _type: StringFilter
    url: StringFilter
    urlTarget: StringFilter
}

input UrlTargetSorting {
    _key: SortOrder
    _type: SortOrder
    url: SortOrder
    urlTarget: SortOrder
}

input VideoFilter {
    _key: StringFilter
    _type: StringFilter
    file: FileFilter
    headerTicker: HeaderTickerFilter
    height: FloatFilter
    mobileVideoPoster: LocaleImageFilter
    type: StringFilter
    url: StringFilter
    videoPoster: LocaleImageFilter
    width: FloatFilter
}

input VideoSorting {
    _key: SortOrder
    _type: SortOrder
    file: FileSorting
    headerTicker: HeaderTickerSorting
    height: SortOrder
    mobileVideoPoster: LocaleImageSorting
    type: SortOrder
    url: SortOrder
    videoPoster: LocaleImageSorting
    width: SortOrder
}

input VideoUrlFilter {
    _key: StringFilter
    _type: StringFilter
    file: FileFilter
    languageCode: StringFilter
    type: StringFilter
    url: StringFilter
}

input VideoUrlSorting {
    _key: SortOrder
    _type: SortOrder
    file: FileSorting
    languageCode: SortOrder
    type: SortOrder
    url: SortOrder
}

input WebsiteConfigFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    facebookUrl: StringFilter
    gameAPI: GameAPIFilter
    instagramUrl: StringFilter
    linkedInUrl: StringFilter
    metaDescription: StringFilter
    metaKeywords: StringFilter
    metaRobots: StringFilter
    title: StringFilter
    youtubeUrl: StringFilter
}

input WebsiteConfigSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    facebookUrl: SortOrder
    gameAPI: GameAPISorting
    instagramUrl: SortOrder
    linkedInUrl: SortOrder
    metaDescription: SortOrder
    metaKeywords: SortOrder
    metaRobots: SortOrder
    title: SortOrder
    youtubeUrl: SortOrder
}

input WidgetPartnersFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    code: SlugFilter
    isActive: BooleanFilter
    name: StringFilter
    title: LocaleStringFilter
}

input WidgetPartnersSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    code: SlugSorting
    isActive: SortOrder
    name: SortOrder
    title: LocaleStringSorting
}

input WidgetSwiperFilter {
    "Apply filters on document level"
    _: Sanity_DocumentFilter
    _createdAt: DatetimeFilter
    _id: IDFilter
    _key: StringFilter
    _rev: StringFilter
    _type: StringFilter
    _updatedAt: DatetimeFilter
    code: SlugFilter
    isActive: BooleanFilter
    name: StringFilter
    swiperParameters: WidgetSwiperParametersFilter
}

input WidgetSwiperParametersFilter {
    _key: StringFilter
    _type: StringFilter
    allowSlideNext: BooleanFilter
    allowSlidePrev: BooleanFilter
    allowTouchMove: BooleanFilter
    autoHeight: BooleanFilter
    centeredSlides: BooleanFilter
    direction: StringFilter
}

input WidgetSwiperParametersSorting {
    _key: SortOrder
    _type: SortOrder
    allowSlideNext: SortOrder
    allowSlidePrev: SortOrder
    allowTouchMove: SortOrder
    autoHeight: SortOrder
    centeredSlides: SortOrder
    direction: SortOrder
}

input WidgetSwiperSorting {
    _createdAt: SortOrder
    _id: SortOrder
    _key: SortOrder
    _rev: SortOrder
    _type: SortOrder
    _updatedAt: SortOrder
    code: SlugSorting
    isActive: SortOrder
    name: SortOrder
    swiperParameters: WidgetSwiperParametersSorting
}
