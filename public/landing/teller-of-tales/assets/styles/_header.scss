@import "variables";
.tournament-landing {

  .banner {
    &-figure-container {
      display: flex;
      width: 100%;
      margin-bottom: -17vw;
    }
    &-figure {
      display: block;
      object-fit: cover;
      object-position: top center;
      width: 100%;
    }
  }
  .header {
    position: relative;
    height: 41.667vw;
    width: 100%;
    display: flex;
    margin-bottom: 0;
    z-index: 2;

    &:before {
      content: "";
      position: absolute;
      left: 0;
      right: 0;
      top:-4vw;
      height: 30vw;
    }

    &-img {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      height: 100%;
      display: block;
    }

    & .sw-logo, .sw-logo-effect, & .sw-symbol {
      position: absolute;
    }

    & .sw-logo {
      top: 43.9%;
      left: 46.9%;
      max-width: 940px;
      width: 49vw;
      transform: translateX(-50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    & .sw-logo-effect {
      top: 26%;
      will-change: transform;
      -webkit-animation: heartbeat 12s ease-in-out infinite both;
      animation: heartbeat 12s ease-in-out infinite both;

      & img {
        mask-image: linear-gradient(to right, transparent 0%, black 10%, black 90%, transparent 100%);
      }
    }

    & .sw-symbol {
      opacity: 0.9;
      will-change: transform, opacity;

      &.s1 {
        left: -5%;
        max-width: 496px;
        top: 62%;
        width: 25.833vw;
        animation: vibrate-1 10s ease-in-out infinite both;
      }

      &.s2 {
        max-width: 460px;
        right: -5.5%;
        top: 28%;
        width: 23.958vw;
        animation: vibrate-1 12s ease-in-out infinite both;
      }
    }
  }
}
