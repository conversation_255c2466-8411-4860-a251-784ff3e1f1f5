@import "variables";
@import "keyframes";
@import "header";
@import "section";
@import "fire";

.tournament-landing {
  overflow: hidden;
  background-color: $backgroundColor;

  .sw-bg .container, header .container, section .container, footer .container {
    position: relative;
    max-width: 1920px;
    width: 100%;
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .sw-bg {
    position: relative;
    width: 100%;
    height: 100%;
  }

  img {
    width: 100%;
  }

  .sw-bg .container {
    & > img {
      mask-image: linear-gradient(90deg, transparent 0, #000 10%, #000 90%, transparent);
      width: 100%;
    }
  }

  .device-desktop {
    .flip-countdown.size-medium .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec {
      font-size: 6.6vw;

      @media (min-width: 1920px) {
        font-size: 128px;
      }
    }

    .flip-countdown.size-medium .flip-countdown-piece .flip-countdown-title {
      font-size: 3vw;
    }
  }

  .device-mobile.in-landscape {
    .flip-countdown.size-small .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec {
      font-size: 6vw;
    }

    .flip-countdown.size-small .flip-countdown-piece .flip-countdown-title {
      font-size: 3vw;
    }
  }
}
