.tournament-landing {
  .section {
    padding-top: 7.708vw;
    font-family: 'Noto Sans Display', sans-serif;
    background: #250804 url('/landing/teller-of-tales/assets/images/bg-2.jpg') no-repeat 0 0;
    background-size: 100% auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    margin-bottom: 0;
    z-index: 1;

    &:after {
      content: "";
      display: table;
      clear: both;
    }
    
    & .sw-ticker {
      min-width: 30vw;
      position: relative;;
      border-radius: 1.563vw;
      border: #de7118 6px solid;
      background-color: #000;
      margin-bottom: 1.563vw;
      padding: 20px 20px 14px;

      .device-mobile & {
        border-width: 2px;
        padding: 10px 20px 7px;
      }
      .device-tablet & {
        border-width: 6px;
        padding: 20px 20px 14px;
      }

      &-title {
        font-size: 2.813vw;
        font-weight: 900;
        color: white;
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        border-radius: 1.042vw 1.042vw 0 0;
        background-color: #de7118;
        padding: 2px 16px 8px;

        .device-mobile & {
          border-radius: 1.563vw 1.563vw 0 0;
          font-size: 14px;
          padding: 2px 16px 4px;
        }
        .device-tablet & {
          border-radius: 1.042vw 1.042vw 0 0;
          font-size: 2.813vw;
          padding: 2px 16px 8px;
        }
      }

      &-dots {
        left: 83%;
        max-width: 577px;
        position: absolute;
        top: 100%;
        width: 30.052vw;
        margin-top: 0.313vw;
      }

      .device-mobile & {
        width: auto;
      }

      .s3 {
        position: absolute;
        z-index: 1;
        opacity: 0.9;
        will-change: transform, opacity;
        bottom: 78%;
        left: -3%;
        max-width: 157px;
        width: 10vw;
        animation: vibrate-1 10s ease-in-out infinite both;
      }
    }

    & .sw-pa-with-le {
      font-size: 5.469vw;
      line-height: 1.2;
      text-align: center;
      font-weight: 900;
      margin-bottom: 0;

      .device-mobile & {
        font-size: 28px;
      }
      .device-tablet & {
        font-size: 5.469vw;
      }
    }

    & .sw-date {
      max-width: 757px;
      width: 39.427vw;
      margin-bottom: 7.604vw;

      .device-mobile & {
        width: 50vw;
        max-width: 200px;
      }
      .device-tablet & {
        width: 39.427vw;
        max-width: 757px;
      }
    }
  }
  .section-yellow {
    position: relative;
    text-align: center;
    margin-bottom: 3.333vw;

    strong {
      font-weight: 900;
    }

    &-body {
      margin: -1px 0;
      padding: 3.646vw 0 5.208vw;
      background: #ffa524;
      font-size: 1.979vw;
      font-family: "Noto Sans Display", sans-serif;;
      color: #1d1c1c;
      line-height: 1.5;
      text-align: center;

      .device-mobile & {
        padding: 20px 0 35px;
      }
      .device-tablet & {
        padding: 3.646vw 0 5.208vw;
      }

       p {
         margin: 0 auto;
         max-width: 80vw;

         .device-mobile & {
           font-size: 16px;
         }
         .device-tablet & {
           font-size: 1.979vw;
           max-width: 70vw;
         }

         &.text-lg {
           font-size: 3.021vw;
           line-height: 1.2;
           margin: 2.917vw auto;

           .device-mobile & {
             font-size: 20px;
           }
           .device-tablet & {
             font-size: 3.021vw;
           }
         }
         &.text-sm {
           font-weight: 400;
           line-height: 1.4;
           font-size: 1.927vw;

           .device-mobile & {
             font-size: 15px;
           }
           .device-tablet & {
             font-size: 1.927vw;
           }
         }

         br {
           display: none;

           .device-desktop & {
             display: block;
           }
         }
       }
    }
    &-divider {
      border-radius: 3px;
      background-color: #fcb045;
      box-shadow: 0 4px 10px 1px rgba(#020302, 0.32);
      height: 1.667vw;
      max-height: 32px;
      margin: 6.146vw 0 7.031vw;

      .device-mobile & {
        height: 10px;
        margin: 3vw 0 4vw;
        box-shadow: 0 2px 5px 1px rgba(#020302, 0.15);
      }
      .device-tablet {
        height: 1.667vw;
        margin: 6.146vw 0 7.031vw;
        box-shadow: 0 4px 10px 1px rgba(#020302, 0.32);
      }
    }
    & .sw-symbol {
      position: absolute;
      opacity: 0.9;
      will-change: transform, opacity;
    }
    .s4 {
      max-width: 433px;
      right: -7.9%;
      top: 29.3%;
      width: 22.552vw;
      animation: vibrate-1 12s ease-in-out infinite both;
    }
    .s5 {
      bottom: 14%;
      left: 0;
      width: 16.302vw;
      max-width: 313px;
      animation: vibrate-1 10s ease-in-out infinite both;

      .device-mobile & {
        left: -2%;
        bottom: 16%;
        opacity: .7;
      }
      .device-mobile.in-landscape & {
        opacity: 0;
      }
      .device-tablet &,
      .device-tablet.in-landscape & {
        bottom: 14%;
        left: 0;
        opacity: 1;
      }
    }
    .s6, .s7 {
      bottom: .5%;
      max-width: 339px;
      width: 17.656vw;
    }
    .s7 {
      right: 14%;
      animation: vibrate-1 11s ease-in-out infinite both;
    }
    .s6 {
      left: 21%;
      animation: vibrate-1 13s ease-in-out infinite both;
    }
    .s8 {
      max-width: 420px;
      right: -7.4%;
      top: 7%;
      width: 21.875vw;
      animation: vibrate-1 12s ease-in-out infinite both;
    }
    .s9 {
      bottom: -17%;
      max-width: 465px;
      right: -7.5%;
      width: 24.219vw;
      animation: vibrate-1 9s ease-in-out infinite both;
    }
    .s10, .s11 {
      bottom: .5%;
      max-width: 207px;
      width: 10.781vw;
    }
    .s10 {
      right: 22%;
      animation: vibrate-1 13s ease-in-out infinite both;
    }
    .s11 {
      left: 21%;
      animation: vibrate-1 11s ease-in-out infinite both;
    }
    .s12 {
      bottom: 17%;
      max-width: 262px;
      width: 13.646vw;

      .device-mobile & {
        bottom: 8%;
      }
      .device-mobile.in-landscape & {
        opacity: 0;
      }
      .device-tablet &,
      .device-tablet.in-landscape & {
        bottom: 17%;
        opacity: 1;
      }
    }
  }
  .sw-green-btn,
  .sw-black-btn,
  .sw-purple-btn,
  .sw-green-line {
    display: inline-block;
    text-transform: uppercase;
    text-align: center;
    color:#fff;
    font-size: 2.813vw;
    line-height: 1.6;
    font-weight: 900;
    margin-bottom: 1.354vw;
    border-radius: 1.563vw;
    padding: 1vw 2vw;

    .device-mobile & {
      font-size: 16px;
      padding: 10px 20px;
      margin-bottom: 1.563vw;
    }
    .device-tablet & {
      font-size: 2.813vw;
      padding: 1vw 2vw;
      margin-bottom: 1.354vw;
    }
  }

  .sw-green-btn {
    width: 39.583vw;
    max-width: 760px;
    background-color: #255a15;
    margin-bottom: 2.813vw;

    .device-mobile & {
      width: auto;
      margin-bottom: 20px;
    }
    .device-tablet & {
      width: 39.583vw;
      margin-bottom: 2.813vw;
    }

    &.lg {
      font-size: 4.792vw;
      width: 75vw;
      max-width: 1440px;
      border-radius: 3.438vw;
      box-shadow: 0 20px 11px 2px rgba(#020302, 0.32);
      padding: 2vw;
      margin-bottom: 6.510vw;
      animation: pulsate-bck 9s ease-in-out infinite both;

      .device-mobile & {
        width: auto;
        margin-bottom: 3vw;
        padding: 2vw 3vw;
        box-shadow: 0 5px 2px 1px rgba(#020302, 0.15);
      }
      .device-tablet & {
        width: 75vw;
        margin-bottom: 6.510vw;
        padding: 2vw;
        box-shadow: 0 20px 11px 2px rgba(#020302, 0.32);
      }
    }
  }
  .sw-black-btn {
    font-size: 2.813vw;
    border-radius: 100px;
    min-width: 45.417vw;
    max-width: 60vw;
    border: 5px solid rgb(222, 113, 24);
    background-color: #8ed06a;
    box-shadow: inset 1px 1px 3px rgba(#fff,.5), 0 20px 11.18px 1.82px rgba(2, 3, 2, 0.32);

    .device-mobile & {
      max-width: 90vw;
    }
    .device-tablet & {
      max-width: 60vw;
    }
  }
  .sw-purple-btn {
    font-size: 3.906vw;
    border-radius: 2.083vw;
    min-width: 50vw;
    max-width: 60vw;
    background-color: #8c3891;

    .device-mobile & {
      max-width: 90vw;
      min-width: 250px;
      margin-bottom: 15px;
    }
    .device-tablet & {
      max-width: 60vw;
      min-width: 50vw;
      margin-bottom: 2.083vw;
    }

    &.sm {
      font-size: 2.604vw;
      border-radius: 100px;
      box-shadow: 0 20px 11.18px 1.82px rgba(2, 3, 2, 0.32);
      padding: 1.4vw 2vw;
      min-width: 56vw;

      .device-mobile & {
        font-size: 14px;
        margin: 20px auto 0;
        padding: 1.4vw 3vw;
        min-width: 250px;
      }
      .device-tablet & {
        font-size: 2.604vw;
        margin: 20px auto 2.083vw;;
        padding: 1.4vw 2vw;
        min-width: 56vw;
      }
    }
  }
  .sw-green-line {
    background-color: rgba(#255a15, .88);
    display: block;
    border-radius: 0;
    margin: 2.708vw 0;
    line-height: 1.5;
    padding: 10px;

    .device-mobile & {
      margin: 10px 0;
    }
    .device-tablet & {
      margin: 2.708vw 0;
    }
  }
  .sw-good-luck {
    color: #fff;
    font-weight: 900;
    font-size: 3.906vw;

    .device-mobile & {
      font-size: 18px;
    }
    .device-tablet & {
      font-size: 3.906vw;
    }
  }
  .sw-list {
    display: flex;
    flex-direction: column;
    margin: 4.167vw auto;

    .device-mobile & {
      margin: 30px auto;
    }
    .device-tablet & {
      margin: 4.167vw auto;
    }

    &-item {
      position: relative;
      border-bottom: 4px solid #de7118;
      color: #fff;
      font-size: 1.953vw;
      padding: 1.250vw;
      text-align: center;

      .device-mobile & {
        font-size: 16px;
        padding: 10px;
        border-bottom: 2px solid #de7118;
      }
      .device-tablet & {
        font-size: 1.953vw;
        padding: 1.250vw;
        border-bottom-width: 4px;
      }

      &:after {
        content:'';
        position: absolute;
        bottom: calc((-1.250vw/2) - 2px);
        left: -1.250vw;
        width: 1.250vw;
        height: 1.250vw;
        border-radius: 50%;
        background: #de7118;

        .device-mobile & {
          bottom: calc((-1.250vw/2) - 1px);
        }
        .device-tablet & {
          bottom: calc((-1.250vw/2) - 2px);
        }
      }
    }

    &.simple {
      .sw-list-item {
        color: #cacaca;
        border: none;
        padding: .250vw;

        &:after {
          display: none;
        }
      }
    }
  }
  .sw-prizes {
    width: 59.219vw;
    max-width: 1137px;
    margin: 2.083vw 0;

    .device-mobile & {
      width: 100%;
      max-width: 80%;
    }
    .device-tablet & {
      width: 59.219vw;
      max-width: 1137px;
    }

    &.lg {
      width: 61.458vw;
      max-width: 1180px;
      margin: 0 0 3.802vw;

      .device-tablet & {
        width: 61.458vw;
        max-width: 1180px;
      }
    }
  }

  .flip-countdown {
    .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec {
      .card__top,
      .card__bottom {
        min-width: 3.125vw;

        .device-desktop & {
          min-width: 60px;
        }
      }
    }
  }
}

.device-desktop .tournament-landing .flip-countdown.size-medium .flip-countdown-piece .flip-countdown-title {
  font-size: 2vw;
}
