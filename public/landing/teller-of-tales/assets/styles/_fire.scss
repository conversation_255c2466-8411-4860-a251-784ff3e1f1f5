.sw-fire {
  .particle {
    animation: rise 1s ease-in infinite;
    background-image: radial-gradient(#ff5000 20%, rgba(255, 80, 0, 0) 70%);
    border-radius: 50%;
    mix-blend-mode: screen;
    opacity: 0;
    position: absolute;
    bottom: 0;
    width: 8vw;
    height: 6vw;
  }

  .particle:nth-of-type(1) {
    animation-delay: 0.2978669529s;
    left: calc((100% - 5vw) * 0);
  }

  .particle:nth-of-type(2) {
    animation-delay: 0.6482163147s;
    left: calc((100% - 5vw) * 0.02);
  }

  .particle:nth-of-type(3) {
    animation-delay: 0.1254242557s;
    left: calc((100% - 5vw) * 0.04);
  }

  .particle:nth-of-type(4) {
    animation-delay: 0.7286764127s;
    left: calc((100% - 5vw) * 0.06);
  }

  .particle:nth-of-type(5) {
    animation-delay: 0.4249997055s;
    left: calc((100% - 5vw) * 0.08);
  }

  .particle:nth-of-type(6) {
    animation-delay: 0.3791831866s;
    left: calc((100% - 5vw) * 0.1);
  }

  .particle:nth-of-type(7) {
    animation-delay: 0.113537683s;
    left: calc((100% - 5vw) * 0.12);
  }

  .particle:nth-of-type(8) {
    animation-delay: 0.8107000566s;
    left: calc((100% - 5vw) * 0.14);
  }

  .particle:nth-of-type(9) {
    animation-delay: 0.7044211728s;
    left: calc((100% - 5vw) * 0.16);
  }

  .particle:nth-of-type(10) {
    animation-delay: 0.5520876879s;
    left: calc((100% - 5vw) * 0.18);
  }

  .particle:nth-of-type(11) {
    animation-delay: 0.2435225772s;
    left: calc((100% - 5vw) * 0.2);
  }

  .particle:nth-of-type(12) {
    animation-delay: 0.7347984972s;
    left: calc((100% - 5vw) * 0.22);
  }

  .particle:nth-of-type(13) {
    animation-delay: 0.7698950079s;
    left: calc((100% - 5vw) * 0.24);
  }

  .particle:nth-of-type(14) {
    animation-delay: 0.7621350564s;
    left: calc((100% - 5vw) * 0.26);
  }

  .particle:nth-of-type(15) {
    animation-delay: 0.7835331397s;
    left: calc((100% - 5vw) * 0.28);
  }

  .particle:nth-of-type(16) {
    animation-delay: 0.1863204711s;
    left: calc((100% - 5vw) * 0.3);
  }

  .particle:nth-of-type(17) {
    animation-delay: 0.882522548s;
    left: calc((100% - 5vw) * 0.32);
  }

  .particle:nth-of-type(18) {
    animation-delay: 0.524897339s;
    left: calc((100% - 5vw) * 0.34);
  }

  .particle:nth-of-type(19) {
    animation-delay: 0.0893295039s;
    left: calc((100% - 5vw) * 0.36);
  }

  .particle:nth-of-type(20) {
    animation-delay: 0.1112894982s;
    left: calc((100% - 5vw) * 0.38);
  }

  .particle:nth-of-type(21) {
    animation-delay: 0.2350502728s;
    left: calc((100% - 5vw) * 0.4);
  }

  .particle:nth-of-type(22) {
    animation-delay: 0.4273660142s;
    left: calc((100% - 5vw) * 0.42);
  }

  .particle:nth-of-type(23) {
    animation-delay: 0.2542263168s;
    left: calc((100% - 5vw) * 0.44);
  }

  .particle:nth-of-type(24) {
    animation-delay: 0.5467992518s;
    left: calc((100% - 5vw) * 0.46);
  }

  .particle:nth-of-type(25) {
    animation-delay: 0.5279011287s;
    left: calc((100% - 5vw) * 0.48);
  }

  .particle:nth-of-type(26) {
    animation-delay: 0.6151815472s;
    left: calc((100% - 5vw) * 0.5);
  }

  .particle:nth-of-type(27) {
    animation-delay: 0.5564357353s;
    left: calc((100% - 5vw) * 0.52);
  }

  .particle:nth-of-type(28) {
    animation-delay: 0.5662248778s;
    left: calc((100% - 5vw) * 0.54);
  }

  .particle:nth-of-type(29) {
    animation-delay: 0.7546163176s;
    left: calc((100% - 5vw) * 0.56);
  }

  .particle:nth-of-type(30) {
    animation-delay: 0.5406056994s;
    left: calc((100% - 5vw) * 0.58);
  }

  .particle:nth-of-type(31) {
    animation-delay: 0.2152847227s;
    left: calc((100% - 5vw) * 0.6);
  }

  .particle:nth-of-type(32) {
    animation-delay: 0.2463678965s;
    left: calc((100% - 5vw) * 0.62);
  }

  .particle:nth-of-type(33) {
    animation-delay: 0.7535113268s;
    left: calc((100% - 5vw) * 0.64);
  }

  .particle:nth-of-type(34) {
    animation-delay: 0.348762432s;
    left: calc((100% - 5vw) * 0.66);
  }

  .particle:nth-of-type(35) {
    animation-delay: 0.346672212s;
    left: calc((100% - 5vw) * 0.68);
  }

  .particle:nth-of-type(36) {
    animation-delay: 0.76217575s;
    left: calc((100% - 5vw) * 0.7);
  }

  .particle:nth-of-type(37) {
    animation-delay: 0.4126767066s;
    left: calc((100% - 5vw) * 0.72);
  }

  .particle:nth-of-type(38) {
    animation-delay: 0.2156161158s;
    left: calc((100% - 5vw) * 0.74);
  }

  .particle:nth-of-type(39) {
    animation-delay: 0.4067539864s;
    left: calc((100% - 5vw) * 0.76);
  }

  .particle:nth-of-type(40) {
    animation-delay: 0.4332445871s;
    left: calc((100% - 5vw) * 0.78);
  }

  .particle:nth-of-type(41) {
    animation-delay: 0.8880111518s;
    left: calc((100% - 5vw) * 0.8);
  }

  .particle:nth-of-type(42) {
    animation-delay: 0.9998599014s;
    left: calc((100% - 5vw) * 0.82);
  }

  .particle:nth-of-type(43) {
    animation-delay: 0.3079801554s;
    left: calc((100% - 5vw) * 0.84);
  }

  .particle:nth-of-type(44) {
    animation-delay: 0.0914599995s;
    left: calc((100% - 5vw) * 0.86);
  }

  .particle:nth-of-type(45) {
    animation-delay: 0.3381472071s;
    left: calc((100% - 5vw) * 0.88);
  }

  .particle:nth-of-type(46) {
    animation-delay: 0.3290850258s;
    left: calc((100% - 5vw) * 0.9);
  }

  .particle:nth-of-type(47) {
    animation-delay: 0.7484752967s;
    left: calc((100% - 5vw) * 0.92);
  }

  .particle:nth-of-type(48) {
    animation-delay: 0.7112399253s;
    left: calc((100% - 5vw) * 0.94);
  }

  .particle:nth-of-type(49) {
    animation-delay: 0.3559621962s;
    left: calc((100% - 5vw) * 0.96);
  }

  .particle:nth-of-type(50) {
    animation-delay: 0.5725174743s;
    left: calc((100% - 5vw) * 0.98);
  }

  @keyframes rise {
    from {
      opacity: 0;
      transform: translateY(0) scale(1);
    }
    25% {
      opacity: 1;
    }
    to {
      opacity: 0;
      transform: translateY(-10em) scale(0);
    }
  }
}