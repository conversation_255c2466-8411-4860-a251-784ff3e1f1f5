:root {
  --container-max-width: 1920;
  --bg-width: var(--container-max-width);
  --bg-height: 3240;
  --bg-aspect-ratio: calc(var(--bg-width) * var(--bg-height));
  --header-max-height: 590;
  --height-aspect-ratio: calc(var(--container-max-width) / var(--header-max-height));
  --width-aspect-ratio: calc(100vw / var(--bg-width));
  --main-section-max-height: calc( var(--bg-height) - var(--header-max-height) );
  --main-section-aspect-ration: calc( var(--container-max-width) / var(--main-section-max-height) );
  --painting-max-bootom: 656;
}

/* ----------------------------------------------
 * Generated by Animista on 2022-3-18 1:15:40
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info.
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */
/**
 * ----------------------------------------
 * animation vibrate-1
 * ----------------------------------------
 */
@-webkit-keyframes vibrate-1 {
  0% {
    -webkit-transform: translate(0);
    transform: translate(0);
  }
  20% {
    -webkit-transform: translate(-2px, 2px);
    transform: translate(-2px, 2px);
  }
  40% {
    -webkit-transform: translate(-2px, -2px);
    transform: translate(-2px, -2px);
  }
  60% {
    -webkit-transform: translate(2px, 2px);
    transform: translate(2px, 2px);
  }
  80% {
    -webkit-transform: translate(2px, -2px);
    transform: translate(2px, -2px);
  }
  100% {
    -webkit-transform: translate(0);
    transform: translate(0);
  }
}
@keyframes vibrate-1 {
  0% {
    -webkit-transform: translate(0);
    transform: translate(0);
  }
  20% {
    -webkit-transform: translate(-2px, 2px);
    transform: translate(-2px, 2px);
  }
  40% {
    -webkit-transform: translate(-2px, -2px);
    transform: translate(-2px, -2px);
  }
  60% {
    -webkit-transform: translate(2px, 2px);
    transform: translate(2px, 2px);
  }
  80% {
    -webkit-transform: translate(2px, -2px);
    transform: translate(2px, -2px);
  }
  100% {
    -webkit-transform: translate(0);
    transform: translate(0);
  }
}
@-webkit-keyframes heartbeat {
  from {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  10% {
    -webkit-transform: scale(0.91);
    transform: scale(0.91);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  17% {
    -webkit-transform: scale(0.98);
    transform: scale(0.98);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  33% {
    -webkit-transform: scale(0.87);
    transform: scale(0.87);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  45% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
}
@keyframes heartbeat {
  from {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  10% {
    -webkit-transform: scale(0.91);
    transform: scale(0.91);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  17% {
    -webkit-transform: scale(0.98);
    transform: scale(0.98);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  33% {
    -webkit-transform: scale(0.87);
    transform: scale(0.87);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  45% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
}
:root {
  --container-max-width: 1920;
  --bg-width: var(--container-max-width);
  --bg-height: 3240;
  --bg-aspect-ratio: calc(var(--bg-width) * var(--bg-height));
  --header-max-height: 590;
  --height-aspect-ratio: calc(var(--container-max-width) / var(--header-max-height));
  --width-aspect-ratio: calc(100vw / var(--bg-width));
  --main-section-max-height: calc( var(--bg-height) - var(--header-max-height) );
  --main-section-aspect-ration: calc( var(--container-max-width) / var(--main-section-max-height) );
  --painting-max-bootom: 656;
}

.tournament-landing .header {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  height: calc(100vw / var(--height-aspect-ratio));
  display: flex;
}
@media (min-width: 1920px) {
  .tournament-landing .header {
    height: calc(1px * var(--header-max-height));
  }
}
.tournament-landing .header .sw-logo, .tournament-landing .header .sw-logo-effect, .tournament-landing .header .sw-symbol {
  position: absolute;
}
.tournament-landing .header .sw-logo {
  top: 9.6%;
  left: 48.6%;
  width: 47vw;
  max-width: 905px;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.tournament-landing .header .sw-logo-effect {
  will-change: transform;
  -webkit-animation: heartbeat 12s ease-in-out infinite both;
  animation: heartbeat 12s ease-in-out infinite both;
}
.tournament-landing .header .sw-logo-effect img {
  mask-image: linear-gradient(to right, transparent 0%, black 10%, black 90%, transparent 100%);
}
.tournament-landing .header .sw-symbol {
  opacity: 0.9;
  will-change: transform, opacity;
}
.tournament-landing .header .sw-symbol.s1 {
  top: 42%;
  right: 13.1%;
  width: 12vw;
  max-width: 260px;
  -webkit-animation: vibrate-1 10s ease-in-out infinite both;
  animation: vibrate-1 10s ease-in-out infinite both;
}
.tournament-landing .header .sw-symbol.s2 {
  left: 18%;
  top: 50%;
  width: 10vw;
  max-width: 170px;
  -webkit-animation: vibrate-1 12s ease-in-out infinite both;
  animation: vibrate-1 12s ease-in-out infinite both;
}
.tournament-landing .header .sw-symbol.s3 {
  right: -11.2%;
  top: 3%;
  width: 21vw;
  max-width: 400px;
  -webkit-animation: vibrate-1 10s ease-in-out infinite both;
  animation: vibrate-1 10s ease-in-out infinite both;
}
.tournament-landing .header .sw-symbol.s4 {
  left: -4%;
  top: 44%;
  width: 14vw;
  max-width: 246px;
  -webkit-animation: vibrate-1 12s ease-in-out infinite both;
  animation: vibrate-1 12s ease-in-out infinite both;
}
.tournament-landing .header .sw-symbol.s5 {
  left: 8.1%;
  top: 16.1%;
  width: 10vw;
  max-width: 176px;
  -webkit-animation: vibrate-1 10s ease-in-out infinite both;
  animation: vibrate-1 10s ease-in-out infinite both;
}
.tournament-landing .header .sw-symbol.s6 {
  right: 10%;
  top: 20%;
  width: 8vw;
  max-width: 140px;
  -webkit-animation: vibrate-1 12s ease-in-out infinite both;
  animation: vibrate-1 12s ease-in-out infinite both;
}

.tournament-landing .section {
  position: absolute;
  top: calc(100vw / var(--height-aspect-ratio));
  left: 0;
  right: 0;
  bottom: 0;
  margin-bottom: 0;
}
@media (min-width: 1920px) {
  .tournament-landing .section {
    top: calc(1px * var(--bg-width) / var(--height-aspect-ratio));
  }
}
.tournament-landing .section .sw-coming-soon {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 60vw;
  max-width: 1167px;
}
.tournament-landing .section .sw-ticker {
  position: absolute;
  top: 12.8%;
  left: 50%;
  transform: translateX(-50%);
  width: 62vw;
  max-width: 1284px;
  max-height: 219px;
  border-radius: 18px;
  border: #de7118 2px solid;
  background-color: #000;
}
.tournament-landing .section .sw-pa-with-le {
  position: absolute;
  top: 23.5%;
  left: 50%;
  transform: translateX(-50%);
  width: 51vw;
  max-width: 993px;
}
.tournament-landing .section .sw-date {
  position: absolute;
  top: 33%;
  left: 50%;
  transform: translateX(-50%);
  width: 48vw;
  max-width: 931px;
}
.tournament-landing .section .sw-fire {
  bottom: 16%;
  filter: blur(0.72em);
  -webkit-filter: blur(0.72em);
  font-size: 24px;
  height: 18vw;
  margin: 0;
  position: absolute;
  right: 4vw;
  width: 20vw;
}
.tournament-landing .section .sw-painting {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 64vw;
  max-width: 1242px;
  bottom: 24%;
}
@media (min-width: 1920px) {
  .tournament-landing .section .sw-painting {
    bottom: calc(1px * var(--painting-max-bootom));
  }
}
.tournament-landing .section .sw-man {
  position: absolute;
  width: 39vw;
  max-width: 751px;
  bottom: 1.6%;
  left: 7.8%;
}
@media (min-width: 1920px) {
  .tournament-landing .section .sw-man {
    bottom: 71px;
  }
}
.tournament-landing .section .sw-box {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  padding: 4px;
  background-color: #260804;
  display: flex;
  align-items: center;
  justify-content: center;
}
.tournament-landing .section .sw-box a {
  font-family: "Noto Sans Display", sans-serif;
  color: #fff;
  font-size: 2vw;
  letter-spacing: 4.3px;
  text-decoration: none;
  text-align: center;
}
@media (max-width: 576px) {
  .tournament-landing .section .sw-box a {
    font-size: 3vw;
  }
}
.tournament-landing .section .sw-box a:hover {
  text-decoration: underline;
}

.sw-fire .particle {
  animation: rise 1s ease-in infinite;
  background-image: radial-gradient(#ff5000 20%, rgba(255, 80, 0, 0) 70%);
  border-radius: 50%;
  mix-blend-mode: screen;
  opacity: 0;
  position: absolute;
  bottom: 0;
  width: 8vw;
  height: 6vw;
}
.sw-fire .particle:nth-of-type(1) {
  animation-delay: 0.2978669529s;
  left: calc((100% - 5vw) * 0);
}
.sw-fire .particle:nth-of-type(2) {
  animation-delay: 0.6482163147s;
  left: calc((100% - 5vw) * 0.02);
}
.sw-fire .particle:nth-of-type(3) {
  animation-delay: 0.1254242557s;
  left: calc((100% - 5vw) * 0.04);
}
.sw-fire .particle:nth-of-type(4) {
  animation-delay: 0.7286764127s;
  left: calc((100% - 5vw) * 0.06);
}
.sw-fire .particle:nth-of-type(5) {
  animation-delay: 0.4249997055s;
  left: calc((100% - 5vw) * 0.08);
}
.sw-fire .particle:nth-of-type(6) {
  animation-delay: 0.3791831866s;
  left: calc((100% - 5vw) * 0.1);
}
.sw-fire .particle:nth-of-type(7) {
  animation-delay: 0.113537683s;
  left: calc((100% - 5vw) * 0.12);
}
.sw-fire .particle:nth-of-type(8) {
  animation-delay: 0.8107000566s;
  left: calc((100% - 5vw) * 0.14);
}
.sw-fire .particle:nth-of-type(9) {
  animation-delay: 0.7044211728s;
  left: calc((100% - 5vw) * 0.16);
}
.sw-fire .particle:nth-of-type(10) {
  animation-delay: 0.5520876879s;
  left: calc((100% - 5vw) * 0.18);
}
.sw-fire .particle:nth-of-type(11) {
  animation-delay: 0.2435225772s;
  left: calc((100% - 5vw) * 0.2);
}
.sw-fire .particle:nth-of-type(12) {
  animation-delay: 0.7347984972s;
  left: calc((100% - 5vw) * 0.22);
}
.sw-fire .particle:nth-of-type(13) {
  animation-delay: 0.7698950079s;
  left: calc((100% - 5vw) * 0.24);
}
.sw-fire .particle:nth-of-type(14) {
  animation-delay: 0.7621350564s;
  left: calc((100% - 5vw) * 0.26);
}
.sw-fire .particle:nth-of-type(15) {
  animation-delay: 0.7835331397s;
  left: calc((100% - 5vw) * 0.28);
}
.sw-fire .particle:nth-of-type(16) {
  animation-delay: 0.1863204711s;
  left: calc((100% - 5vw) * 0.3);
}
.sw-fire .particle:nth-of-type(17) {
  animation-delay: 0.882522548s;
  left: calc((100% - 5vw) * 0.32);
}
.sw-fire .particle:nth-of-type(18) {
  animation-delay: 0.524897339s;
  left: calc((100% - 5vw) * 0.34);
}
.sw-fire .particle:nth-of-type(19) {
  animation-delay: 0.0893295039s;
  left: calc((100% - 5vw) * 0.36);
}
.sw-fire .particle:nth-of-type(20) {
  animation-delay: 0.1112894982s;
  left: calc((100% - 5vw) * 0.38);
}
.sw-fire .particle:nth-of-type(21) {
  animation-delay: 0.2350502728s;
  left: calc((100% - 5vw) * 0.4);
}
.sw-fire .particle:nth-of-type(22) {
  animation-delay: 0.4273660142s;
  left: calc((100% - 5vw) * 0.42);
}
.sw-fire .particle:nth-of-type(23) {
  animation-delay: 0.2542263168s;
  left: calc((100% - 5vw) * 0.44);
}
.sw-fire .particle:nth-of-type(24) {
  animation-delay: 0.5467992518s;
  left: calc((100% - 5vw) * 0.46);
}
.sw-fire .particle:nth-of-type(25) {
  animation-delay: 0.5279011287s;
  left: calc((100% - 5vw) * 0.48);
}
.sw-fire .particle:nth-of-type(26) {
  animation-delay: 0.6151815472s;
  left: calc((100% - 5vw) * 0.5);
}
.sw-fire .particle:nth-of-type(27) {
  animation-delay: 0.5564357353s;
  left: calc((100% - 5vw) * 0.52);
}
.sw-fire .particle:nth-of-type(28) {
  animation-delay: 0.5662248778s;
  left: calc((100% - 5vw) * 0.54);
}
.sw-fire .particle:nth-of-type(29) {
  animation-delay: 0.7546163176s;
  left: calc((100% - 5vw) * 0.56);
}
.sw-fire .particle:nth-of-type(30) {
  animation-delay: 0.5406056994s;
  left: calc((100% - 5vw) * 0.58);
}
.sw-fire .particle:nth-of-type(31) {
  animation-delay: 0.2152847227s;
  left: calc((100% - 5vw) * 0.6);
}
.sw-fire .particle:nth-of-type(32) {
  animation-delay: 0.2463678965s;
  left: calc((100% - 5vw) * 0.62);
}
.sw-fire .particle:nth-of-type(33) {
  animation-delay: 0.7535113268s;
  left: calc((100% - 5vw) * 0.64);
}
.sw-fire .particle:nth-of-type(34) {
  animation-delay: 0.348762432s;
  left: calc((100% - 5vw) * 0.66);
}
.sw-fire .particle:nth-of-type(35) {
  animation-delay: 0.346672212s;
  left: calc((100% - 5vw) * 0.68);
}
.sw-fire .particle:nth-of-type(36) {
  animation-delay: 0.76217575s;
  left: calc((100% - 5vw) * 0.7);
}
.sw-fire .particle:nth-of-type(37) {
  animation-delay: 0.4126767066s;
  left: calc((100% - 5vw) * 0.72);
}
.sw-fire .particle:nth-of-type(38) {
  animation-delay: 0.2156161158s;
  left: calc((100% - 5vw) * 0.74);
}
.sw-fire .particle:nth-of-type(39) {
  animation-delay: 0.4067539864s;
  left: calc((100% - 5vw) * 0.76);
}
.sw-fire .particle:nth-of-type(40) {
  animation-delay: 0.4332445871s;
  left: calc((100% - 5vw) * 0.78);
}
.sw-fire .particle:nth-of-type(41) {
  animation-delay: 0.8880111518s;
  left: calc((100% - 5vw) * 0.8);
}
.sw-fire .particle:nth-of-type(42) {
  animation-delay: 0.9998599014s;
  left: calc((100% - 5vw) * 0.82);
}
.sw-fire .particle:nth-of-type(43) {
  animation-delay: 0.3079801554s;
  left: calc((100% - 5vw) * 0.84);
}
.sw-fire .particle:nth-of-type(44) {
  animation-delay: 0.0914599995s;
  left: calc((100% - 5vw) * 0.86);
}
.sw-fire .particle:nth-of-type(45) {
  animation-delay: 0.3381472071s;
  left: calc((100% - 5vw) * 0.88);
}
.sw-fire .particle:nth-of-type(46) {
  animation-delay: 0.3290850258s;
  left: calc((100% - 5vw) * 0.9);
}
.sw-fire .particle:nth-of-type(47) {
  animation-delay: 0.7484752967s;
  left: calc((100% - 5vw) * 0.92);
}
.sw-fire .particle:nth-of-type(48) {
  animation-delay: 0.7112399253s;
  left: calc((100% - 5vw) * 0.94);
}
.sw-fire .particle:nth-of-type(49) {
  animation-delay: 0.3559621962s;
  left: calc((100% - 5vw) * 0.96);
}
.sw-fire .particle:nth-of-type(50) {
  animation-delay: 0.5725174743s;
  left: calc((100% - 5vw) * 0.98);
}
@keyframes rise {
  from {
    opacity: 0;
    transform: translateY(0) scale(1);
  }
  25% {
    opacity: 1;
  }
  to {
    opacity: 0;
    transform: translateY(-10em) scale(0);
  }
}

.tournament-landing {
  overflow: hidden;
  background-color: #260804;
}

.sw-bg .container, header .container, section .container, footer .container {
  position: relative;
  max-width: 1920px;
  width: 100%;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.sw-bg {
  position: relative;
  width: 100%;
  height: 100%;
}

img {
  width: 100%;
}

.sw-bg .container > img {
  mask-image: linear-gradient(90deg, transparent 0, #000 10%, #000 90%, transparent);
  width: 100%;
}

.device-desktop .flip-countdown.size-medium .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec {
  font-size: 6.6vw;
}
@media (min-width: 1920px) {
  .device-desktop .flip-countdown.size-medium .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec {
    font-size: 128px;
  }
}
.device-desktop .flip-countdown.size-medium .flip-countdown-piece .flip-countdown-title {
  font-size: 3vw;
}

.device-mobile.in-landscape .flip-countdown.size-small .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec {
  font-size: 6vw;
}
.device-mobile.in-landscape .flip-countdown.size-small .flip-countdown-piece .flip-countdown-title {
  font-size: 3vw;
}

/*# sourceMappingURL=global.css.map */
