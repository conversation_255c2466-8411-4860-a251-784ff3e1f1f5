@import "variables";

.tournament-landing {
  & section.ended {
    background-color: $headerBGColor;
    color: $sectionTextColor;
    min-height: 30vh;

    & .container {
      max-width: 1920px;
      position: relative;
      width: 100%;
      min-height: 30vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding-top: 20px;

      & .head-1 {
        position: absolute;
        left: -8vw;
        top: -8vw;
        width: 16vw;
        max-width: 518px;
        min-width: 180px;
        z-index: 100;

        @media (max-width: 976px) {
          top: -60px;
          left: -100px;
        }
        //
        //@media (max-width: 624px) {
        //  top: 50px;
        //}

        & img {
          width: 100%;
        }
      }

      & .head-2 {
        position: absolute;
        right: -6vw;
        bottom: 30px;
        width: 16vw;
        max-width: 518px;
        min-width: 180px;
        z-index: 100;

        @media (max-width: 976px) {
          bottom: 64px;
          right: -60px;
        }

        & img {
          width: 100%;
        }
      }

      & .lucky-envelopes-logo {
        position: relative;
        z-index: 21;
        max-width: 470px;
        min-width: 280px;
        min-height: 600px;
        width: 38vw;

        & img {
          width: 100%;
          left: 0;
          right: 0;
        }
      }

      & .bottom-ellipse {
        position: absolute;
        z-index: 10;
        bottom: -16px;
        width: 768px;
        max-width: 1920px;
        //transform: scale(200%);

        & img {
          width: 100%;
        }

        @media (min-width: 768px) {
          bottom: -26px;
          width: 976px;
        }
        @media (min-width: 976px) {
          bottom: -50px;
          width: 1368px;
        }
        @media (min-width: 1368px) {
          bottom: -60px;
          width: 1680px;
        }
        @media (min-width: 1680px) {
          bottom: -80px;
          width: 1920px;
        }
      }

      & .button-ended {
        position: absolute;
        z-index: 21;
        bottom: -10px;
        width: 36vw;
        padding-top: 20px;
        padding-bottom: 40px;
        max-width: 480px;
        min-width: 280px;

        & img {
          width: 100%;
        }
      }

    }
  }

  & section.footer {
    background-color: $footerBGColor;
    color: $sectionTextColor;
    min-height: 20px;

    & .container {
      max-width: 1920px;
      position: relative;
      width: 100%;
      min-height: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding-top: 20px;

      & .button-see-next-time {
        position: absolute;
        z-index: 21;
        top: -150px;
        width: 50vw;
        padding-top: 20px;
        padding-bottom: 40px;
        max-width: 580px;
        min-width: 280px;

        & img {
          width: 100%;
        }

        @media (max-width: 768px) {
          top: -130px;
        }
      }
    }
  }

  & section.landing {
    background-color: $sectionBGColor;
    color: $sectionTextColor;
    min-height: 30vh;

    & .container {
      max-width: 1920px;
      position: relative;
      width: 100%;
      min-height: 30vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding-top: 20px;

      & .head-1 {
        position: absolute;
        left: -8vw;
        top: -8vw;
        width: 16vw;
        max-width: 518px;
        min-width: 180px;
        z-index: 100;

        @media (max-width: 976px) {
          top: -60px;
          left: -100px;
        }
        //
        //@media (max-width: 624px) {
        //  top: 50px;
        //}

        & img {
          width: 100%;
        }
      }

      & .head-2 {
        position: absolute;
        right: -6vw;
        bottom: 10vw;
        width: 16vw;
        max-width: 518px;
        min-width: 180px;
        z-index: 100;

        @media (max-width: 976px) {
          bottom: 64px;
          right: -60px;
        }

        & img {
          width: 100%;
        }
      }

      & .bottom-ellipse-large {
        position: absolute;
        z-index: 10;
        left: 0;
        right: 0;
        bottom: -4vw;
        width: 100vw;
        max-width: 1920px;

        & img {
          width: 100%;
        }

        @media (min-width: 1910px) {
          bottom: -140px;
        }
      }

      & .bottom-ellipse-small {
        position: absolute;
        z-index: 11;
        left: 50%;
        transform: translateX(-50%);
        bottom: -9vw;
        width: 50vw;
        max-width: 756px;
        min-width: 280px;

        & img {
          width: 100%;
        }

        @media (max-width: 576px) {
          bottom: -60px;
        }
      }

      .section-header-title {
        background-color: #e000ff;
        padding: 8px 16px;
        border-radius: 10px;
        margin-top: 40px;
        text-align: center;

        @media (max-width: 576px) {
          background-color: transparent;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          & .text {
            margin-top: 10px !important;
          }
        }
      }

      & .section-title-h1 {
        @media (max-width: 576px) {
          align-self: flex-end !important;
        }
      }

      & .section-title-h1, .section-header-title .text {
        position: relative;
        white-space: nowrap;
        z-index: 20;
        font-family: 'GROBOLD', sans-serif;
        font-weight: 500;
        font-style: normal;
        font-size: 2em;
        line-height: 2.6em;
        max-width: 748px;
        color: $sectionBGColor;
        background-color: #e000ff;
        padding: 8px 16px;
        border-radius: 10px;

        @media (max-width: 576px) {
          font-size: 14pt;
          line-height: 18pt;
          padding: 4px 8px;
        }
      }

      & .section-title-h1 {
        @media (max-width: 624px) {
          display: none !important;
        }
      }

      & .prizes {
        position: relative;
        padding-top: 20px;
        font-family: 'GROBOLD', sans-serif;
        font-weight: 500;
        font-style: normal;
        font-size: 2em;
        line-height: 2.4em;
        width: 70vw;
        max-width: 968px;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: space-between;

        @media (max-width: 624px) {
          flex-direction: column;
          .prize {
            &.prz-1 {
              align-self: center;
            }

            &.prz-2 {
              align-self: center;
            }

            &.prz-3 {
              align-self: center;
            }

          }
        }

        .prize {
          width: 20vw;
          max-width: 260px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          align-items: center;

          .prize-title {
            justify-content: center;
            text-align: center;
            white-space: nowrap;
            padding: 6px;
          }

          img {
            width: 100%;
            min-width: 160px;
          }

          &.prz-2 {
            img {
              width: 85%;
              min-width: 140px;
            }
          }

          &.prz-3 {
            img {
              width: 70%;
              min-width: 120px;
            }
          }
        }

        @media (max-width: 768px) {
          font-size: 20pt;
          line-height: 24pt;
        }
      }

      & .bottom-block {
        padding-bottom: 260px;
      }

    }
  }
}