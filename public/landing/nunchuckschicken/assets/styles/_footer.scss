@import "variables";

.tournament-landing {
  & footer {
    background-color: $footerBGColor;
    color: $footerTextColor;
    min-height: 30vh;

    & .container {
      max-width: 1920px;
      position: relative;
      padding-bottom: 60px;
      width: 100%;
      min-height: 30vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      & .top-block {
        padding-top: 240px;
      }

      & .button-enter-tournament {
        position: relative;
        z-index: 21;
        width: 36vw;
        padding-top: 20px;
        padding-bottom: 40px;
        max-width: 690px;
        min-width: 280px;

        & img {
          width: 100%;
        }
      }

      & .lucky-envelopes-logo {
        position: absolute;
        top: -10vw;
        z-index: 21;
        width: 50vw;
        max-width: 370px;
        min-width: 280px;

        & img {
          width: 100%;
          left: 0;
          right: 0;
        }
      }

      & .footer-title {
        position: relative;
        white-space: nowrap;
        z-index: 20;
        font-family: 'GROBOLD', sans-serif;
        font-weight: 500;
        font-style: normal;
        font-size: 3em;
        padding-top: 20px;
        max-width: 790px;

        @media (max-width: 768px) {
          font-size: 20pt;
          line-height: 24pt;
        }
      }

      & .tournament-timer {
        position: relative;
        z-index: 21;
        overflow: hidden;
        margin-bottom: 30px;
        align-items: center;
        text-align: center;
      }

      & .text-gray, & .text-gray a {
        position: relative;
        font-family: 'Roboto', sans-serif;
        font-size: $robotoFontSize;
        font-weight: normal;
        justify-content: center;
        text-align: center;
        color: #8c8c8c;

        &:hover {
          text-decoration: underline;
        }
      }

    }
  }
}