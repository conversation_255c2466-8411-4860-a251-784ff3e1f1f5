@import "variables";
@import "header";
@import "section";
@import "footer";

@font-face {
  font-family: 'GROBOLD';
  src: url('/landing/nunchuckschicken/assets/fonts/grobold/GROBOLD.woff2') format('woff2'),
  url('/landing/nunchuckschicken/assets/fonts/grobold/GROBOLD.woff') format('woff'),
  url('/landing/nunchuckschicken/assets/fonts/grobold/GROBOLD.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

html, body {
  overflow-x: hidden;
}

.tournament-landing {
  overflow: hidden;
}

.section-text {
  position: relative;
  width: 50vw;
  max-width: 768px;
  padding-top: 40px;
  font-family: 'Roboto', sans-serif;
  font-size: $robotoFontSize;
  font-weight: 400;
  justify-content: center;
  text-align: center;

  & .bold {
    font-weight: bold;
  }

  @media (max-width: 976px) {
    width: 70vw;
  }
}
