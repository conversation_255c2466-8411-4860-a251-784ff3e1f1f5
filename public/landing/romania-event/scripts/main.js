"use strict";$(function(){var e;function n(){new Swiper(".swiper-container",{observer:!0,observeParents:!0,threshold:5,loop:!0,slidesPerView:2,spaceBetween:20,navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev",disabledClass:"swiper-button-disabled"},breakpoints:{500:{slidesPerView:2},768:{slidesPerView:4},1440:{slidesPerView:5}}})}$(window).on("resize orientationChanged",function(){clearTimeout(e),e=setTimeout(function(){n(),$("body").removeClass("modal-open"),$(".modal").addClass("hidden")},250)}),n(),$("#modalOpen").on("click",function(e){e.preventDefault(),$("body").addClass("modal-open"),$(".modal").removeClass("hidden")}),$("#modalClose").on("click",function(e){e.preventDefault(),$("body").removeClass("modal-open"),$(".modal").addClass("hidden")}),AOS.init()}),document.addEventListener("DOMContentLoaded",function(){var e=document.getElementById("flip-clock");new FlipClock(e,new Date(parseInt(e.dataset.eventDate,10)),JSON.parse(e.dataset.options))});