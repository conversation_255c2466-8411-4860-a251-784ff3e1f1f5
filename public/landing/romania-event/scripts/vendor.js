!function(e,t){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(E,e){"use strict";var t=[],i=Object.getPrototypeOf,o=t.slice,v=t.flat?function(e){return t.flat.call(e)}:function(e){return t.concat.apply([],e)},l=t.push,a=t.indexOf,n={},r=n.toString,m=n.hasOwnProperty,s=m.toString,u=s.call(Object),g={},y=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType&&"function"!=typeof e.item},b=function(e){return null!=e&&e===e.window},C=E.document,c={type:!0,src:!0,nonce:!0,noModule:!0};function w(e,t,n){var i,a,r=(n=n||C).createElement("script");if(r.text=e,t)for(i in c)(a=t[i]||t.getAttribute&&t.getAttribute(i))&&r.setAttribute(i,a);n.head.appendChild(r).parentNode.removeChild(r)}function x(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?n[r.call(e)]||"object":typeof e}var d="3.6.0",S=function(e,t){return new S.fn.init(e,t)};function p(e){var t=!!e&&"length"in e&&e.length,n=x(e);return!y(e)&&!b(e)&&("array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)}S.fn=S.prototype={jquery:d,constructor:S,length:0,toArray:function(){return o.call(this)},get:function(e){return null==e?o.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=S.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return S.each(this,e)},map:function(n){return this.pushStack(S.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(o.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(S.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(S.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(0<=n&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:l,sort:t.sort,splice:t.splice},S.extend=S.fn.extend=function(){var e,t,n,i,a,r,s=arguments[0]||{},o=1,l=arguments.length,u=!1;for("boolean"==typeof s&&(u=s,s=arguments[o]||{},o++),"object"==typeof s||y(s)||(s={}),o===l&&(s=this,o--);o<l;o++)if(null!=(e=arguments[o]))for(t in e)i=e[t],"__proto__"!==t&&s!==i&&(u&&i&&(S.isPlainObject(i)||(a=Array.isArray(i)))?(n=s[t],r=a&&!Array.isArray(n)?[]:a||S.isPlainObject(n)?n:{},a=!1,s[t]=S.extend(u,r,i)):void 0!==i&&(s[t]=i));return s},S.extend({expando:"jQuery"+(d+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==r.call(e)||(t=i(e))&&("function"!=typeof(n=m.call(t,"constructor")&&t.constructor)||s.call(n)!==u))},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t,n){w(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,i=0;if(p(e))for(n=e.length;i<n&&!1!==t.call(e[i],i,e[i]);i++);else for(i in e)if(!1===t.call(e[i],i,e[i]))break;return e},makeArray:function(e,t){var n=t||[];return null!=e&&(p(Object(e))?S.merge(n,"string"==typeof e?[e]:e):l.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:a.call(t,e,n)},merge:function(e,t){for(var n=+t.length,i=0,a=e.length;i<n;i++)e[a++]=t[i];return e.length=a,e},grep:function(e,t,n){for(var i=[],a=0,r=e.length,s=!n;a<r;a++)!t(e[a],a)!==s&&i.push(e[a]);return i},map:function(e,t,n){var i,a,r=0,s=[];if(p(e))for(i=e.length;r<i;r++)null!=(a=t(e[r],r,n))&&s.push(a);else for(r in e)null!=(a=t(e[r],r,n))&&s.push(a);return v(s)},guid:1,support:g}),"function"==typeof Symbol&&(S.fn[Symbol.iterator]=t[Symbol.iterator]),S.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){n["[object "+t+"]"]=t.toLowerCase()});var f=function(n){var e,f,w,r,a,h,d,v,x,l,u,T,E,s,C,m,o,c,g,S="sizzle"+1*new Date,p=n.document,k=0,i=0,y=le(),b=le(),M=le(),L=le(),D=function(e,t){return e===t&&(u=!0),0},A={}.hasOwnProperty,t=[],P=t.pop,z=t.push,N=t.push,O=t.slice,$=function(e,t){for(var n=0,i=e.length;n<i;n++)if(e[n]===t)return n;return-1},I="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",j="[\\x20\\t\\r\\n\\f]",H="(?:\\\\[\\da-fA-F]{1,6}"+j+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",R="\\["+j+"*("+H+")(?:"+j+"*([*^$|!~]?=)"+j+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+H+"))|)"+j+"*\\]",q=":("+H+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+R+")*)|.*)\\)|)",B=new RegExp(j+"+","g"),G=new RegExp("^"+j+"+|((?:^|[^\\\\])(?:\\\\.)*)"+j+"+$","g"),F=new RegExp("^"+j+"*,"+j+"*"),W=new RegExp("^"+j+"*([>+~]|"+j+")"+j+"*"),V=new RegExp(j+"|>"),X=new RegExp(q),_=new RegExp("^"+H+"$"),Y={ID:new RegExp("^#("+H+")"),CLASS:new RegExp("^\\.("+H+")"),TAG:new RegExp("^("+H+"|[*])"),ATTR:new RegExp("^"+R),PSEUDO:new RegExp("^"+q),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+j+"*(even|odd|(([+-]|)(\\d*)n|)"+j+"*(?:([+-]|)"+j+"*(\\d+)|))"+j+"*\\)|)","i"),bool:new RegExp("^(?:"+I+")$","i"),needsContext:new RegExp("^"+j+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+j+"*((?:-\\d)?\\d*)"+j+"*\\)|)(?=[^-]|$)","i")},U=/HTML$/i,K=/^(?:input|select|textarea|button)$/i,J=/^h\d$/i,Z=/^[^{]+\{\s*\[native \w/,Q=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ee=/[+~]/,te=new RegExp("\\\\[\\da-fA-F]{1,6}"+j+"?|\\\\([^\\r\\n\\f])","g"),ne=function(e,t){var n="0x"+e.slice(1)-65536;return t||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},ie=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,ae=function(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e},re=function(){T()},se=we(function(e){return!0===e.disabled&&"fieldset"===e.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{N.apply(t=O.call(p.childNodes),p.childNodes),t[p.childNodes.length].nodeType}catch(e){N={apply:t.length?function(e,t){z.apply(e,O.call(t))}:function(e,t){for(var n=e.length,i=0;e[n++]=t[i++];);e.length=n-1}}}function oe(e,t,n,i){var a,r,s,o,l,u,c,d=t&&t.ownerDocument,p=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==p&&9!==p&&11!==p)return n;if(!i&&(T(t),t=t||E,C)){if(11!==p&&(l=Q.exec(e)))if(a=l[1]){if(9===p){if(!(s=t.getElementById(a)))return n;if(s.id===a)return n.push(s),n}else if(d&&(s=d.getElementById(a))&&g(t,s)&&s.id===a)return n.push(s),n}else{if(l[2])return N.apply(n,t.getElementsByTagName(e)),n;if((a=l[3])&&f.getElementsByClassName&&t.getElementsByClassName)return N.apply(n,t.getElementsByClassName(a)),n}if(f.qsa&&!L[e+" "]&&(!m||!m.test(e))&&(1!==p||"object"!==t.nodeName.toLowerCase())){if(c=e,d=t,1===p&&(V.test(e)||W.test(e))){for((d=ee.test(e)&&ge(t.parentNode)||t)===t&&f.scope||((o=t.getAttribute("id"))?o=o.replace(ie,ae):t.setAttribute("id",o=S)),r=(u=h(e)).length;r--;)u[r]=(o?"#"+o:":scope")+" "+be(u[r]);c=u.join(",")}try{return N.apply(n,d.querySelectorAll(c)),n}catch(t){L(e,!0)}finally{o===S&&t.removeAttribute("id")}}}return v(e.replace(G,"$1"),t,n,i)}function le(){var i=[];return function e(t,n){return i.push(t+" ")>w.cacheLength&&delete e[i.shift()],e[t+" "]=n}}function ue(e){return e[S]=!0,e}function ce(e){var t=E.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function de(e,t){for(var n=e.split("|"),i=n.length;i--;)w.attrHandle[n[i]]=t}function pe(e,t){var n=t&&e,i=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(i)return i;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function fe(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}function he(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}function ve(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&se(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function me(s){return ue(function(r){return r=+r,ue(function(e,t){for(var n,i=s([],e.length,r),a=i.length;a--;)e[n=i[a]]&&(e[n]=!(t[n]=e[n]))})})}function ge(e){return e&&void 0!==e.getElementsByTagName&&e}for(e in f=oe.support={},a=oe.isXML=function(e){var t=e&&e.namespaceURI,n=e&&(e.ownerDocument||e).documentElement;return!U.test(t||n&&n.nodeName||"HTML")},T=oe.setDocument=function(e){var t,n,i=e?e.ownerDocument||e:p;return i!=E&&9===i.nodeType&&i.documentElement&&(s=(E=i).documentElement,C=!a(E),p!=E&&(n=E.defaultView)&&n.top!==n&&(n.addEventListener?n.addEventListener("unload",re,!1):n.attachEvent&&n.attachEvent("onunload",re)),f.scope=ce(function(e){return s.appendChild(e).appendChild(E.createElement("div")),void 0!==e.querySelectorAll&&!e.querySelectorAll(":scope fieldset div").length}),f.attributes=ce(function(e){return e.className="i",!e.getAttribute("className")}),f.getElementsByTagName=ce(function(e){return e.appendChild(E.createComment("")),!e.getElementsByTagName("*").length}),f.getElementsByClassName=Z.test(E.getElementsByClassName),f.getById=ce(function(e){return s.appendChild(e).id=S,!E.getElementsByName||!E.getElementsByName(S).length}),f.getById?(w.filter.ID=function(e){var t=e.replace(te,ne);return function(e){return e.getAttribute("id")===t}},w.find.ID=function(e,t){if(void 0!==t.getElementById&&C){var n=t.getElementById(e);return n?[n]:[]}}):(w.filter.ID=function(e){var n=e.replace(te,ne);return function(e){var t=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return t&&t.value===n}},w.find.ID=function(e,t){if(void 0!==t.getElementById&&C){var n,i,a,r=t.getElementById(e);if(r){if((n=r.getAttributeNode("id"))&&n.value===e)return[r];for(a=t.getElementsByName(e),i=0;r=a[i++];)if((n=r.getAttributeNode("id"))&&n.value===e)return[r]}return[]}}),w.find.TAG=f.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):f.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,i=[],a=0,r=t.getElementsByTagName(e);if("*"!==e)return r;for(;n=r[a++];)1===n.nodeType&&i.push(n);return i},w.find.CLASS=f.getElementsByClassName&&function(e,t){if(void 0!==t.getElementsByClassName&&C)return t.getElementsByClassName(e)},o=[],m=[],(f.qsa=Z.test(E.querySelectorAll))&&(ce(function(e){var t;s.appendChild(e).innerHTML="<a id='"+S+"'></a><select id='"+S+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&m.push("[*^$]="+j+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||m.push("\\["+j+"*(?:value|"+I+")"),e.querySelectorAll("[id~="+S+"-]").length||m.push("~="),(t=E.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||m.push("\\["+j+"*name"+j+"*="+j+"*(?:''|\"\")"),e.querySelectorAll(":checked").length||m.push(":checked"),e.querySelectorAll("a#"+S+"+*").length||m.push(".#.+[+~]"),e.querySelectorAll("\\\f"),m.push("[\\r\\n\\f]")}),ce(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=E.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&m.push("name"+j+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&m.push(":enabled",":disabled"),s.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&m.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),m.push(",.*:")})),(f.matchesSelector=Z.test(c=s.matches||s.webkitMatchesSelector||s.mozMatchesSelector||s.oMatchesSelector||s.msMatchesSelector))&&ce(function(e){f.disconnectedMatch=c.call(e,"*"),c.call(e,"[s!='']:x"),o.push("!=",q)}),m=m.length&&new RegExp(m.join("|")),o=o.length&&new RegExp(o.join("|")),t=Z.test(s.compareDocumentPosition),g=t||Z.test(s.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,i=t&&t.parentNode;return e===i||!(!i||1!==i.nodeType||!(n.contains?n.contains(i):e.compareDocumentPosition&&16&e.compareDocumentPosition(i)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},D=t?function(e,t){if(e===t)return u=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!f.sortDetached&&t.compareDocumentPosition(e)===n?e==E||e.ownerDocument==p&&g(p,e)?-1:t==E||t.ownerDocument==p&&g(p,t)?1:l?$(l,e)-$(l,t):0:4&n?-1:1)}:function(e,t){if(e===t)return u=!0,0;var n,i=0,a=e.parentNode,r=t.parentNode,s=[e],o=[t];if(!a||!r)return e==E?-1:t==E?1:a?-1:r?1:l?$(l,e)-$(l,t):0;if(a===r)return pe(e,t);for(n=e;n=n.parentNode;)s.unshift(n);for(n=t;n=n.parentNode;)o.unshift(n);for(;s[i]===o[i];)i++;return i?pe(s[i],o[i]):s[i]==p?-1:o[i]==p?1:0}),E},oe.matches=function(e,t){return oe(e,null,null,t)},oe.matchesSelector=function(e,t){if(T(e),f.matchesSelector&&C&&!L[t+" "]&&(!o||!o.test(t))&&(!m||!m.test(t)))try{var n=c.call(e,t);if(n||f.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){L(t,!0)}return 0<oe(t,E,null,[e]).length},oe.contains=function(e,t){return(e.ownerDocument||e)!=E&&T(e),g(e,t)},oe.attr=function(e,t){(e.ownerDocument||e)!=E&&T(e);var n=w.attrHandle[t.toLowerCase()],i=n&&A.call(w.attrHandle,t.toLowerCase())?n(e,t,!C):void 0;return void 0!==i?i:f.attributes||!C?e.getAttribute(t):(i=e.getAttributeNode(t))&&i.specified?i.value:null},oe.escape=function(e){return(e+"").replace(ie,ae)},oe.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},oe.uniqueSort=function(e){var t,n=[],i=0,a=0;if(u=!f.detectDuplicates,l=!f.sortStable&&e.slice(0),e.sort(D),u){for(;t=e[a++];)t===e[a]&&(i=n.push(a));for(;i--;)e.splice(n[i],1)}return l=null,e},r=oe.getText=function(e){var t,n="",i=0,a=e.nodeType;if(a){if(1===a||9===a||11===a){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=r(e)}else if(3===a||4===a)return e.nodeValue}else for(;t=e[i++];)n+=r(t);return n},(w=oe.selectors={cacheLength:50,createPseudo:ue,match:Y,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(te,ne),e[3]=(e[3]||e[4]||e[5]||"").replace(te,ne),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||oe.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&oe.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return Y.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&X.test(n)&&(t=h(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(te,ne).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=y[e+" "];return t||(t=new RegExp("(^|"+j+")"+e+"("+j+"|$)"))&&y(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(n,i,a){return function(e){var t=oe.attr(e,n);return null==t?"!="===i:!i||(t+="","="===i?t===a:"!="===i?t!==a:"^="===i?a&&0===t.indexOf(a):"*="===i?a&&-1<t.indexOf(a):"$="===i?a&&t.slice(-a.length)===a:"~="===i?-1<(" "+t.replace(B," ")+" ").indexOf(a):"|="===i&&(t===a||t.slice(0,a.length+1)===a+"-"))}},CHILD:function(h,e,t,v,m){var g="nth"!==h.slice(0,3),y="last"!==h.slice(-4),b="of-type"===e;return 1===v&&0===m?function(e){return!!e.parentNode}:function(e,t,n){var i,a,r,s,o,l,u=g!==y?"nextSibling":"previousSibling",c=e.parentNode,d=b&&e.nodeName.toLowerCase(),p=!n&&!b,f=!1;if(c){if(g){for(;u;){for(s=e;s=s[u];)if(b?s.nodeName.toLowerCase()===d:1===s.nodeType)return!1;l=u="only"===h&&!l&&"nextSibling"}return!0}if(l=[y?c.firstChild:c.lastChild],y&&p){for(f=(o=(i=(a=(r=(s=c)[S]||(s[S]={}))[s.uniqueID]||(r[s.uniqueID]={}))[h]||[])[0]===k&&i[1])&&i[2],s=o&&c.childNodes[o];s=++o&&s&&s[u]||(f=o=0)||l.pop();)if(1===s.nodeType&&++f&&s===e){a[h]=[k,o,f];break}}else if(p&&(f=o=(i=(a=(r=(s=e)[S]||(s[S]={}))[s.uniqueID]||(r[s.uniqueID]={}))[h]||[])[0]===k&&i[1]),!1===f)for(;(s=++o&&s&&s[u]||(f=o=0)||l.pop())&&((b?s.nodeName.toLowerCase()!==d:1!==s.nodeType)||!++f||(p&&((a=(r=s[S]||(s[S]={}))[s.uniqueID]||(r[s.uniqueID]={}))[h]=[k,f]),s!==e)););return(f-=m)===v||f%v==0&&0<=f/v}}},PSEUDO:function(e,r){var t,s=w.pseudos[e]||w.setFilters[e.toLowerCase()]||oe.error("unsupported pseudo: "+e);return s[S]?s(r):1<s.length?(t=[e,e,"",r],w.setFilters.hasOwnProperty(e.toLowerCase())?ue(function(e,t){for(var n,i=s(e,r),a=i.length;a--;)e[n=$(e,i[a])]=!(t[n]=i[a])}):function(e){return s(e,0,t)}):s}},pseudos:{not:ue(function(e){var i=[],a=[],o=d(e.replace(G,"$1"));return o[S]?ue(function(e,t,n,i){for(var a,r=o(e,null,i,[]),s=e.length;s--;)(a=r[s])&&(e[s]=!(t[s]=a))}):function(e,t,n){return i[0]=e,o(i,null,n,a),i[0]=null,!a.pop()}}),has:ue(function(t){return function(e){return 0<oe(t,e).length}}),contains:ue(function(t){return t=t.replace(te,ne),function(e){return-1<(e.textContent||r(e)).indexOf(t)}}),lang:ue(function(n){return _.test(n||"")||oe.error("unsupported lang: "+n),n=n.replace(te,ne).toLowerCase(),function(e){var t;do{if(t=C?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=n.location&&n.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===s},focus:function(e){return e===E.activeElement&&(!E.hasFocus||E.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:ve(!1),disabled:ve(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!w.pseudos.empty(e)},header:function(e){return J.test(e.nodeName)},input:function(e){return K.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:me(function(){return[0]}),last:me(function(e,t){return[t-1]}),eq:me(function(e,t,n){return[n<0?n+t:n]}),even:me(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:me(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:me(function(e,t,n){for(var i=n<0?n+t:t<n?t:n;0<=--i;)e.push(i);return e}),gt:me(function(e,t,n){for(var i=n<0?n+t:n;++i<t;)e.push(i);return e})}}).pseudos.nth=w.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})w.pseudos[e]=fe(e);for(e in{submit:!0,reset:!0})w.pseudos[e]=he(e);function ye(){}function be(e){for(var t=0,n=e.length,i="";t<n;t++)i+=e[t].value;return i}function we(o,e,t){var l=e.dir,u=e.next,c=u||l,d=t&&"parentNode"===c,p=i++;return e.first?function(e,t,n){for(;e=e[l];)if(1===e.nodeType||d)return o(e,t,n);return!1}:function(e,t,n){var i,a,r,s=[k,p];if(n){for(;e=e[l];)if((1===e.nodeType||d)&&o(e,t,n))return!0}else for(;e=e[l];)if(1===e.nodeType||d)if(a=(r=e[S]||(e[S]={}))[e.uniqueID]||(r[e.uniqueID]={}),u&&u===e.nodeName.toLowerCase())e=e[l]||e;else{if((i=a[c])&&i[0]===k&&i[1]===p)return s[2]=i[2];if((a[c]=s)[2]=o(e,t,n))return!0}return!1}}function xe(a){return 1<a.length?function(e,t,n){for(var i=a.length;i--;)if(!a[i](e,t,n))return!1;return!0}:a[0]}function Te(e,t,n,i,a){for(var r,s=[],o=0,l=e.length,u=null!=t;o<l;o++)(r=e[o])&&(n&&!n(r,i,a)||(s.push(r),u&&t.push(o)));return s}function Ee(f,h,v,m,g,e){return m&&!m[S]&&(m=Ee(m)),g&&!g[S]&&(g=Ee(g,e)),ue(function(e,t,n,i){var a,r,s,o=[],l=[],u=t.length,c=e||function(e,t,n){for(var i=0,a=t.length;i<a;i++)oe(e,t[i],n);return n}(h||"*",n.nodeType?[n]:n,[]),d=!f||!e&&h?c:Te(c,o,f,n,i),p=v?g||(e?f:u||m)?[]:t:d;if(v&&v(d,p,n,i),m)for(a=Te(p,l),m(a,[],n,i),r=a.length;r--;)(s=a[r])&&(p[l[r]]=!(d[l[r]]=s));if(e){if(g||f){if(g){for(a=[],r=p.length;r--;)(s=p[r])&&a.push(d[r]=s);g(null,p=[],a,i)}for(r=p.length;r--;)(s=p[r])&&-1<(a=g?$(e,s):o[r])&&(e[a]=!(t[a]=s))}}else p=Te(p===t?p.splice(u,p.length):p),g?g(null,t,p,i):N.apply(t,p)})}function Ce(e){for(var a,t,n,i=e.length,r=w.relative[e[0].type],s=r||w.relative[" "],o=r?1:0,l=we(function(e){return e===a},s,!0),u=we(function(e){return-1<$(a,e)},s,!0),c=[function(e,t,n){var i=!r&&(n||t!==x)||((a=t).nodeType?l(e,t,n):u(e,t,n));return a=null,i}];o<i;o++)if(t=w.relative[e[o].type])c=[we(xe(c),t)];else{if((t=w.filter[e[o].type].apply(null,e[o].matches))[S]){for(n=++o;n<i&&!w.relative[e[n].type];n++);return Ee(1<o&&xe(c),1<o&&be(e.slice(0,o-1).concat({value:" "===e[o-2].type?"*":""})).replace(G,"$1"),t,o<n&&Ce(e.slice(o,n)),n<i&&Ce(e=e.slice(n)),n<i&&be(e))}c.push(t)}return xe(c)}return ye.prototype=w.filters=w.pseudos,w.setFilters=new ye,h=oe.tokenize=function(e,t){var n,i,a,r,s,o,l,u=b[e+" "];if(u)return t?0:u.slice(0);for(s=e,o=[],l=w.preFilter;s;){for(r in n&&!(i=F.exec(s))||(i&&(s=s.slice(i[0].length)||s),o.push(a=[])),n=!1,(i=W.exec(s))&&(n=i.shift(),a.push({value:n,type:i[0].replace(G," ")}),s=s.slice(n.length)),w.filter)!(i=Y[r].exec(s))||l[r]&&!(i=l[r](i))||(n=i.shift(),a.push({value:n,type:r,matches:i}),s=s.slice(n.length));if(!n)break}return t?s.length:s?oe.error(e):b(e,o).slice(0)},d=oe.compile=function(e,t){var n,m,g,y,b,i,a=[],r=[],s=M[e+" "];if(!s){for(t||(t=h(e)),n=t.length;n--;)(s=Ce(t[n]))[S]?a.push(s):r.push(s);(s=M(e,(m=r,y=0<(g=a).length,b=0<m.length,i=function(e,t,n,i,a){var r,s,o,l=0,u="0",c=e&&[],d=[],p=x,f=e||b&&w.find.TAG("*",a),h=k+=null==p?1:Math.random()||.1,v=f.length;for(a&&(x=t==E||t||a);u!==v&&null!=(r=f[u]);u++){if(b&&r){for(s=0,t||r.ownerDocument==E||(T(r),n=!C);o=m[s++];)if(o(r,t||E,n)){i.push(r);break}a&&(k=h)}y&&((r=!o&&r)&&l--,e&&c.push(r))}if(l+=u,y&&u!==l){for(s=0;o=g[s++];)o(c,d,t,n);if(e){if(0<l)for(;u--;)c[u]||d[u]||(d[u]=P.call(i));d=Te(d)}N.apply(i,d),a&&!e&&0<d.length&&1<l+g.length&&oe.uniqueSort(i)}return a&&(k=h,x=p),c},y?ue(i):i))).selector=e}return s},v=oe.select=function(e,t,n,i){var a,r,s,o,l,u="function"==typeof e&&e,c=!i&&h(e=u.selector||e);if(n=n||[],1===c.length){if(2<(r=c[0]=c[0].slice(0)).length&&"ID"===(s=r[0]).type&&9===t.nodeType&&C&&w.relative[r[1].type]){if(!(t=(w.find.ID(s.matches[0].replace(te,ne),t)||[])[0]))return n;u&&(t=t.parentNode),e=e.slice(r.shift().value.length)}for(a=Y.needsContext.test(e)?0:r.length;a--&&(s=r[a],!w.relative[o=s.type]);)if((l=w.find[o])&&(i=l(s.matches[0].replace(te,ne),ee.test(r[0].type)&&ge(t.parentNode)||t))){if(r.splice(a,1),!(e=i.length&&be(r)))return N.apply(n,i),n;break}}return(u||d(e,c))(i,t,!C,n,!t||ee.test(e)&&ge(t.parentNode)||t),n},f.sortStable=S.split("").sort(D).join("")===S,f.detectDuplicates=!!u,T(),f.sortDetached=ce(function(e){return 1&e.compareDocumentPosition(E.createElement("fieldset"))}),ce(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||de("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),f.attributes&&ce(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||de("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),ce(function(e){return null==e.getAttribute("disabled")})||de(I,function(e,t,n){var i;if(!n)return!0===e[t]?t.toLowerCase():(i=e.getAttributeNode(t))&&i.specified?i.value:null}),oe}(E);S.find=f,S.expr=f.selectors,S.expr[":"]=S.expr.pseudos,S.uniqueSort=S.unique=f.uniqueSort,S.text=f.getText,S.isXMLDoc=f.isXML,S.contains=f.contains,S.escapeSelector=f.escape;var h=function(e,t,n){for(var i=[],a=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(a&&S(e).is(n))break;i.push(e)}return i},T=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},k=S.expr.match.needsContext;function M(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}var L=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function D(e,n,i){return y(n)?S.grep(e,function(e,t){return!!n.call(e,t,e)!==i}):n.nodeType?S.grep(e,function(e){return e===n!==i}):"string"!=typeof n?S.grep(e,function(e){return-1<a.call(n,e)!==i}):S.filter(n,e,i)}S.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?S.find.matchesSelector(i,e)?[i]:[]:S.find.matches(e,S.grep(t,function(e){return 1===e.nodeType}))},S.fn.extend({find:function(e){var t,n,i=this.length,a=this;if("string"!=typeof e)return this.pushStack(S(e).filter(function(){for(t=0;t<i;t++)if(S.contains(a[t],this))return!0}));for(n=this.pushStack([]),t=0;t<i;t++)S.find(e,a[t],n);return 1<i?S.uniqueSort(n):n},filter:function(e){return this.pushStack(D(this,e||[],!1))},not:function(e){return this.pushStack(D(this,e||[],!0))},is:function(e){return!!D(this,"string"==typeof e&&k.test(e)?S(e):e||[],!1).length}});var A,P=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(S.fn.init=function(e,t,n){var i,a;if(!e)return this;if(n=n||A,"string"!=typeof e)return e.nodeType?(this[0]=e,this.length=1,this):y(e)?void 0!==n.ready?n.ready(e):e(S):S.makeArray(e,this);if(!(i="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:P.exec(e))||!i[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(i[1]){if(t=t instanceof S?t[0]:t,S.merge(this,S.parseHTML(i[1],t&&t.nodeType?t.ownerDocument||t:C,!0)),L.test(i[1])&&S.isPlainObject(t))for(i in t)y(this[i])?this[i](t[i]):this.attr(i,t[i]);return this}return(a=C.getElementById(i[2]))&&(this[0]=a,this.length=1),this}).prototype=S.fn,A=S(C);var z=/^(?:parents|prev(?:Until|All))/,N={children:!0,contents:!0,next:!0,prev:!0};function O(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}S.fn.extend({has:function(e){var t=S(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(S.contains(this,t[e]))return!0})},closest:function(e,t){var n,i=0,a=this.length,r=[],s="string"!=typeof e&&S(e);if(!k.test(e))for(;i<a;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(s?-1<s.index(n):1===n.nodeType&&S.find.matchesSelector(n,e))){r.push(n);break}return this.pushStack(1<r.length?S.uniqueSort(r):r)},index:function(e){return e?"string"==typeof e?a.call(S(e),this[0]):a.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(S.uniqueSort(S.merge(this.get(),S(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),S.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return h(e,"parentNode")},parentsUntil:function(e,t,n){return h(e,"parentNode",n)},next:function(e){return O(e,"nextSibling")},prev:function(e){return O(e,"previousSibling")},nextAll:function(e){return h(e,"nextSibling")},prevAll:function(e){return h(e,"previousSibling")},nextUntil:function(e,t,n){return h(e,"nextSibling",n)},prevUntil:function(e,t,n){return h(e,"previousSibling",n)},siblings:function(e){return T((e.parentNode||{}).firstChild,e)},children:function(e){return T(e.firstChild)},contents:function(e){return null!=e.contentDocument&&i(e.contentDocument)?e.contentDocument:(M(e,"template")&&(e=e.content||e),S.merge([],e.childNodes))}},function(i,a){S.fn[i]=function(e,t){var n=S.map(this,a,e);return"Until"!==i.slice(-5)&&(t=e),t&&"string"==typeof t&&(n=S.filter(t,n)),1<this.length&&(N[i]||S.uniqueSort(n),z.test(i)&&n.reverse()),this.pushStack(n)}});var $=/[^\x20\t\r\n\f]+/g;function I(e){return e}function j(e){throw e}function H(e,t,n,i){var a;try{e&&y(a=e.promise)?a.call(e).done(t).fail(n):e&&y(a=e.then)?a.call(e,t,n):t.apply(void 0,[e].slice(i))}catch(e){n.apply(void 0,[e])}}S.Callbacks=function(i){var e,n;i="string"==typeof i?(e=i,n={},S.each(e.match($)||[],function(e,t){n[t]=!0}),n):S.extend({},i);var a,t,r,s,o=[],l=[],u=-1,c=function(){for(s=s||i.once,r=a=!0;l.length;u=-1)for(t=l.shift();++u<o.length;)!1===o[u].apply(t[0],t[1])&&i.stopOnFalse&&(u=o.length,t=!1);i.memory||(t=!1),a=!1,s&&(o=t?[]:"")},d={add:function(){return o&&(t&&!a&&(u=o.length-1,l.push(t)),function n(e){S.each(e,function(e,t){y(t)?i.unique&&d.has(t)||o.push(t):t&&t.length&&"string"!==x(t)&&n(t)})}(arguments),t&&!a&&c()),this},remove:function(){return S.each(arguments,function(e,t){for(var n;-1<(n=S.inArray(t,o,n));)o.splice(n,1),n<=u&&u--}),this},has:function(e){return e?-1<S.inArray(e,o):0<o.length},empty:function(){return o&&(o=[]),this},disable:function(){return s=l=[],o=t="",this},disabled:function(){return!o},lock:function(){return s=l=[],t||a||(o=t=""),this},locked:function(){return!!s},fireWith:function(e,t){return s||(t=[e,(t=t||[]).slice?t.slice():t],l.push(t),a||c()),this},fire:function(){return d.fireWith(this,arguments),this},fired:function(){return!!r}};return d},S.extend({Deferred:function(e){var r=[["notify","progress",S.Callbacks("memory"),S.Callbacks("memory"),2],["resolve","done",S.Callbacks("once memory"),S.Callbacks("once memory"),0,"resolved"],["reject","fail",S.Callbacks("once memory"),S.Callbacks("once memory"),1,"rejected"]],a="pending",s={state:function(){return a},always:function(){return o.done(arguments).fail(arguments),this},catch:function(e){return s.then(null,e)},pipe:function(){var a=arguments;return S.Deferred(function(i){S.each(r,function(e,t){var n=y(a[t[4]])&&a[t[4]];o[t[1]](function(){var e=n&&n.apply(this,arguments);e&&y(e.promise)?e.promise().progress(i.notify).done(i.resolve).fail(i.reject):i[t[0]+"With"](this,n?[e]:arguments)})}),a=null}).promise()},then:function(t,n,i){var l=0;function u(a,r,s,o){return function(){var n=this,i=arguments,e=function(){var e,t;if(!(a<l)){if((e=s.apply(n,i))===r.promise())throw new TypeError("Thenable self-resolution");t=e&&("object"==typeof e||"function"==typeof e)&&e.then,y(t)?o?t.call(e,u(l,r,I,o),u(l,r,j,o)):(l++,t.call(e,u(l,r,I,o),u(l,r,j,o),u(l,r,I,r.notifyWith))):(s!==I&&(n=void 0,i=[e]),(o||r.resolveWith)(n,i))}},t=o?e:function(){try{e()}catch(e){S.Deferred.exceptionHook&&S.Deferred.exceptionHook(e,t.stackTrace),l<=a+1&&(s!==j&&(n=void 0,i=[e]),r.rejectWith(n,i))}};a?t():(S.Deferred.getStackHook&&(t.stackTrace=S.Deferred.getStackHook()),E.setTimeout(t))}}return S.Deferred(function(e){r[0][3].add(u(0,e,y(i)?i:I,e.notifyWith)),r[1][3].add(u(0,e,y(t)?t:I)),r[2][3].add(u(0,e,y(n)?n:j))}).promise()},promise:function(e){return null!=e?S.extend(e,s):s}},o={};return S.each(r,function(e,t){var n=t[2],i=t[5];s[t[1]]=n.add,i&&n.add(function(){a=i},r[3-e][2].disable,r[3-e][3].disable,r[0][2].lock,r[0][3].lock),n.add(t[3].fire),o[t[0]]=function(){return o[t[0]+"With"](this===o?void 0:this,arguments),this},o[t[0]+"With"]=n.fireWith}),s.promise(o),e&&e.call(o,o),o},when:function(e){var n=arguments.length,t=n,i=Array(t),a=o.call(arguments),r=S.Deferred(),s=function(t){return function(e){i[t]=this,a[t]=1<arguments.length?o.call(arguments):e,--n||r.resolveWith(i,a)}};if(n<=1&&(H(e,r.done(s(t)).resolve,r.reject,!n),"pending"===r.state()||y(a[t]&&a[t].then)))return r.then();for(;t--;)H(a[t],s(t),r.reject);return r.promise()}});var R=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;S.Deferred.exceptionHook=function(e,t){E.console&&E.console.warn&&e&&R.test(e.name)&&E.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},S.readyException=function(e){E.setTimeout(function(){throw e})};var q=S.Deferred();function B(){C.removeEventListener("DOMContentLoaded",B),E.removeEventListener("load",B),S.ready()}S.fn.ready=function(e){return q.then(e).catch(function(e){S.readyException(e)}),this},S.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--S.readyWait:S.isReady)||(S.isReady=!0)!==e&&0<--S.readyWait||q.resolveWith(C,[S])}}),S.ready.then=q.then,"complete"===C.readyState||"loading"!==C.readyState&&!C.documentElement.doScroll?E.setTimeout(S.ready):(C.addEventListener("DOMContentLoaded",B),E.addEventListener("load",B));var G=function(e,t,n,i,a,r,s){var o=0,l=e.length,u=null==n;if("object"===x(n))for(o in a=!0,n)G(e,t,o,n[o],!0,r,s);else if(void 0!==i&&(a=!0,y(i)||(s=!0),u&&(t=s?(t.call(e,i),null):(u=t,function(e,t,n){return u.call(S(e),n)})),t))for(;o<l;o++)t(e[o],n,s?i:i.call(e[o],o,t(e[o],n)));return a?e:u?t.call(e):l?t(e[0],n):r},F=/^-ms-/,W=/-([a-z])/g;function V(e,t){return t.toUpperCase()}function X(e){return e.replace(F,"ms-").replace(W,V)}var _=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function Y(){this.expando=S.expando+Y.uid++}Y.uid=1,Y.prototype={cache:function(e){var t=e[this.expando];return t||(t={},_(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var i,a=this.cache(e);if("string"==typeof t)a[X(t)]=n;else for(i in t)a[X(i)]=t[i];return a},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][X(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,i=e[this.expando];if(void 0!==i){if(void 0!==t){n=(t=Array.isArray(t)?t.map(X):(t=X(t))in i?[t]:t.match($)||[]).length;for(;n--;)delete i[t[n]]}(void 0===t||S.isEmptyObject(i))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!S.isEmptyObject(t)}};var U=new Y,K=new Y,J=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Z=/[A-Z]/g;function Q(e,t,n){var i,a;if(void 0===n&&1===e.nodeType)if(i="data-"+t.replace(Z,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(i))){try{n="true"===(a=n)||"false"!==a&&("null"===a?null:a===+a+""?+a:J.test(a)?JSON.parse(a):a)}catch(e){}K.set(e,t,n)}else n=void 0;return n}S.extend({hasData:function(e){return K.hasData(e)||U.hasData(e)},data:function(e,t,n){return K.access(e,t,n)},removeData:function(e,t){K.remove(e,t)},_data:function(e,t,n){return U.access(e,t,n)},_removeData:function(e,t){U.remove(e,t)}}),S.fn.extend({data:function(n,e){var t,i,a,r=this[0],s=r&&r.attributes;if(void 0!==n)return"object"==typeof n?this.each(function(){K.set(this,n)}):G(this,function(e){var t;if(r&&void 0===e)return void 0!==(t=K.get(r,n))?t:void 0!==(t=Q(r,n))?t:void 0;this.each(function(){K.set(this,n,e)})},null,e,1<arguments.length,null,!0);if(this.length&&(a=K.get(r),1===r.nodeType&&!U.get(r,"hasDataAttrs"))){for(t=s.length;t--;)s[t]&&0===(i=s[t].name).indexOf("data-")&&(i=X(i.slice(5)),Q(r,i,a[i]));U.set(r,"hasDataAttrs",!0)}return a},removeData:function(e){return this.each(function(){K.remove(this,e)})}}),S.extend({queue:function(e,t,n){var i;if(e)return t=(t||"fx")+"queue",i=U.get(e,t),n&&(!i||Array.isArray(n)?i=U.access(e,t,S.makeArray(n)):i.push(n)),i||[]},dequeue:function(e,t){t=t||"fx";var n=S.queue(e,t),i=n.length,a=n.shift(),r=S._queueHooks(e,t);"inprogress"===a&&(a=n.shift(),i--),a&&("fx"===t&&n.unshift("inprogress"),delete r.stop,a.call(e,function(){S.dequeue(e,t)},r)),!i&&r&&r.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return U.get(e,n)||U.access(e,n,{empty:S.Callbacks("once memory").add(function(){U.remove(e,[t+"queue",n])})})}}),S.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),arguments.length<e?S.queue(this[0],t):void 0===n?this:this.each(function(){var e=S.queue(this,t,n);S._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&S.dequeue(this,t)})},dequeue:function(e){return this.each(function(){S.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,i=1,a=S.Deferred(),r=this,s=this.length,o=function(){--i||a.resolveWith(r,[r])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)(n=U.get(r[s],e+"queueHooks"))&&n.empty&&(i++,n.empty.add(o));return o(),a.promise(t)}});var ee=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,te=new RegExp("^(?:([+-])=|)("+ee+")([a-z%]*)$","i"),ne=["Top","Right","Bottom","Left"],ie=C.documentElement,ae=function(e){return S.contains(e.ownerDocument,e)},re={composed:!0};ie.getRootNode&&(ae=function(e){return S.contains(e.ownerDocument,e)||e.getRootNode(re)===e.ownerDocument});var se=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&ae(e)&&"none"===S.css(e,"display")};function oe(e,t,n,i){var a,r,s=20,o=i?function(){return i.cur()}:function(){return S.css(e,t,"")},l=o(),u=n&&n[3]||(S.cssNumber[t]?"":"px"),c=e.nodeType&&(S.cssNumber[t]||"px"!==u&&+l)&&te.exec(S.css(e,t));if(c&&c[3]!==u){for(l/=2,u=u||c[3],c=+l||1;s--;)S.style(e,t,c+u),(1-r)*(1-(r=o()/l||.5))<=0&&(s=0),c/=r;c*=2,S.style(e,t,c+u),n=n||[]}return n&&(c=+c||+l||0,a=n[1]?c+(n[1]+1)*n[2]:+n[2],i&&(i.unit=u,i.start=c,i.end=a)),a}var le={};function ue(e,t){for(var n,i,a,r,s,o,l,u=[],c=0,d=e.length;c<d;c++)(i=e[c]).style&&(n=i.style.display,t?("none"===n&&(u[c]=U.get(i,"display")||null,u[c]||(i.style.display="")),""===i.style.display&&se(i)&&(u[c]=(l=s=r=void 0,s=(a=i).ownerDocument,o=a.nodeName,(l=le[o])||(r=s.body.appendChild(s.createElement(o)),l=S.css(r,"display"),r.parentNode.removeChild(r),"none"===l&&(l="block"),le[o]=l)))):"none"!==n&&(u[c]="none",U.set(i,"display",n)));for(c=0;c<d;c++)null!=u[c]&&(e[c].style.display=u[c]);return e}S.fn.extend({show:function(){return ue(this,!0)},hide:function(){return ue(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){se(this)?S(this).show():S(this).hide()})}});var ce,de,pe=/^(?:checkbox|radio)$/i,fe=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,he=/^$|^module$|\/(?:java|ecma)script/i;ce=C.createDocumentFragment().appendChild(C.createElement("div")),(de=C.createElement("input")).setAttribute("type","radio"),de.setAttribute("checked","checked"),de.setAttribute("name","t"),ce.appendChild(de),g.checkClone=ce.cloneNode(!0).cloneNode(!0).lastChild.checked,ce.innerHTML="<textarea>x</textarea>",g.noCloneChecked=!!ce.cloneNode(!0).lastChild.defaultValue,ce.innerHTML="<option></option>",g.option=!!ce.lastChild;var ve={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function me(e,t){var n;return n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&M(e,t)?S.merge([e],n):n}function ge(e,t){for(var n=0,i=e.length;n<i;n++)U.set(e[n],"globalEval",!t||U.get(t[n],"globalEval"))}ve.tbody=ve.tfoot=ve.colgroup=ve.caption=ve.thead,ve.th=ve.td,g.option||(ve.optgroup=ve.option=[1,"<select multiple='multiple'>","</select>"]);var ye=/<|&#?\w+;/;function be(e,t,n,i,a){for(var r,s,o,l,u,c,d=t.createDocumentFragment(),p=[],f=0,h=e.length;f<h;f++)if((r=e[f])||0===r)if("object"===x(r))S.merge(p,r.nodeType?[r]:r);else if(ye.test(r)){for(s=s||d.appendChild(t.createElement("div")),o=(fe.exec(r)||["",""])[1].toLowerCase(),l=ve[o]||ve._default,s.innerHTML=l[1]+S.htmlPrefilter(r)+l[2],c=l[0];c--;)s=s.lastChild;S.merge(p,s.childNodes),(s=d.firstChild).textContent=""}else p.push(t.createTextNode(r));for(d.textContent="",f=0;r=p[f++];)if(i&&-1<S.inArray(r,i))a&&a.push(r);else if(u=ae(r),s=me(d.appendChild(r),"script"),u&&ge(s),n)for(c=0;r=s[c++];)he.test(r.type||"")&&n.push(r);return d}var we=/^([^.]*)(?:\.(.+)|)/;function xe(){return!0}function Te(){return!1}function Ee(e,t){return e===function(){try{return C.activeElement}catch(e){}}()==("focus"===t)}function Ce(e,t,n,i,a,r){var s,o;if("object"==typeof t){for(o in"string"!=typeof n&&(i=i||n,n=void 0),t)Ce(e,o,n,i,t[o],r);return e}if(null==i&&null==a?(a=n,i=n=void 0):null==a&&("string"==typeof n?(a=i,i=void 0):(a=i,i=n,n=void 0)),!1===a)a=Te;else if(!a)return e;return 1===r&&(s=a,(a=function(e){return S().off(e),s.apply(this,arguments)}).guid=s.guid||(s.guid=S.guid++)),e.each(function(){S.event.add(this,t,a,i,n)})}function Se(e,a,r){r?(U.set(e,a,!1),S.event.add(e,a,{namespace:!1,handler:function(e){var t,n,i=U.get(this,a);if(1&e.isTrigger&&this[a]){if(i.length)(S.event.special[a]||{}).delegateType&&e.stopPropagation();else if(i=o.call(arguments),U.set(this,a,i),t=r(this,a),this[a](),i!==(n=U.get(this,a))||t?U.set(this,a,!1):n={},i!==n)return e.stopImmediatePropagation(),e.preventDefault(),n&&n.value}else i.length&&(U.set(this,a,{value:S.event.trigger(S.extend(i[0],S.Event.prototype),i.slice(1),this)}),e.stopImmediatePropagation())}})):void 0===U.get(e,a)&&S.event.add(e,a,xe)}S.event={global:{},add:function(t,e,n,i,a){var r,s,o,l,u,c,d,p,f,h,v,m=U.get(t);if(_(t))for(n.handler&&(n=(r=n).handler,a=r.selector),a&&S.find.matchesSelector(ie,a),n.guid||(n.guid=S.guid++),(l=m.events)||(l=m.events=Object.create(null)),(s=m.handle)||(s=m.handle=function(e){return void 0!==S&&S.event.triggered!==e.type?S.event.dispatch.apply(t,arguments):void 0}),u=(e=(e||"").match($)||[""]).length;u--;)f=v=(o=we.exec(e[u])||[])[1],h=(o[2]||"").split(".").sort(),f&&(d=S.event.special[f]||{},f=(a?d.delegateType:d.bindType)||f,d=S.event.special[f]||{},c=S.extend({type:f,origType:v,data:i,handler:n,guid:n.guid,selector:a,needsContext:a&&S.expr.match.needsContext.test(a),namespace:h.join(".")},r),(p=l[f])||((p=l[f]=[]).delegateCount=0,d.setup&&!1!==d.setup.call(t,i,h,s)||t.addEventListener&&t.addEventListener(f,s)),d.add&&(d.add.call(t,c),c.handler.guid||(c.handler.guid=n.guid)),a?p.splice(p.delegateCount++,0,c):p.push(c),S.event.global[f]=!0)},remove:function(e,t,n,i,a){var r,s,o,l,u,c,d,p,f,h,v,m=U.hasData(e)&&U.get(e);if(m&&(l=m.events)){for(u=(t=(t||"").match($)||[""]).length;u--;)if(f=v=(o=we.exec(t[u])||[])[1],h=(o[2]||"").split(".").sort(),f){for(d=S.event.special[f]||{},p=l[f=(i?d.delegateType:d.bindType)||f]||[],o=o[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=r=p.length;r--;)c=p[r],!a&&v!==c.origType||n&&n.guid!==c.guid||o&&!o.test(c.namespace)||i&&i!==c.selector&&("**"!==i||!c.selector)||(p.splice(r,1),c.selector&&p.delegateCount--,d.remove&&d.remove.call(e,c));s&&!p.length&&(d.teardown&&!1!==d.teardown.call(e,h,m.handle)||S.removeEvent(e,f,m.handle),delete l[f])}else for(f in l)S.event.remove(e,f+t[u],n,i,!0);S.isEmptyObject(l)&&U.remove(e,"handle events")}},dispatch:function(e){var t,n,i,a,r,s,o=new Array(arguments.length),l=S.event.fix(e),u=(U.get(this,"events")||Object.create(null))[l.type]||[],c=S.event.special[l.type]||{};for(o[0]=l,t=1;t<arguments.length;t++)o[t]=arguments[t];if(l.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,l)){for(s=S.event.handlers.call(this,l,u),t=0;(a=s[t++])&&!l.isPropagationStopped();)for(l.currentTarget=a.elem,n=0;(r=a.handlers[n++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==r.namespace&&!l.rnamespace.test(r.namespace)||(l.handleObj=r,l.data=r.data,void 0!==(i=((S.event.special[r.origType]||{}).handle||r.handler).apply(a.elem,o))&&!1===(l.result=i)&&(l.preventDefault(),l.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,l),l.result}},handlers:function(e,t){var n,i,a,r,s,o=[],l=t.delegateCount,u=e.target;if(l&&u.nodeType&&!("click"===e.type&&1<=e.button))for(;u!==this;u=u.parentNode||this)if(1===u.nodeType&&("click"!==e.type||!0!==u.disabled)){for(r=[],s={},n=0;n<l;n++)void 0===s[a=(i=t[n]).selector+" "]&&(s[a]=i.needsContext?-1<S(a,this).index(u):S.find(a,this,null,[u]).length),s[a]&&r.push(i);r.length&&o.push({elem:u,handlers:r})}return u=this,l<t.length&&o.push({elem:u,handlers:t.slice(l)}),o},addProp:function(t,e){Object.defineProperty(S.Event.prototype,t,{enumerable:!0,configurable:!0,get:y(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(e){return e[S.expando]?e:new S.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return pe.test(t.type)&&t.click&&M(t,"input")&&Se(t,"click",xe),!1},trigger:function(e){var t=this||e;return pe.test(t.type)&&t.click&&M(t,"input")&&Se(t,"click"),!0},_default:function(e){var t=e.target;return pe.test(t.type)&&t.click&&M(t,"input")&&U.get(t,"click")||M(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},S.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},S.Event=function(e,t){if(!(this instanceof S.Event))return new S.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?xe:Te,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&S.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[S.expando]=!0},S.Event.prototype={constructor:S.Event,isDefaultPrevented:Te,isPropagationStopped:Te,isImmediatePropagationStopped:Te,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=xe,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=xe,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=xe,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},S.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},S.event.addProp),S.each({focus:"focusin",blur:"focusout"},function(e,t){S.event.special[e]={setup:function(){return Se(this,e,Ee),!1},trigger:function(){return Se(this,e),!0},_default:function(){return!0},delegateType:t}}),S.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,a){S.event.special[e]={delegateType:a,bindType:a,handle:function(e){var t,n=e.relatedTarget,i=e.handleObj;return n&&(n===this||S.contains(this,n))||(e.type=i.origType,t=i.handler.apply(this,arguments),e.type=a),t}}}),S.fn.extend({on:function(e,t,n,i){return Ce(this,e,t,n,i)},one:function(e,t,n,i){return Ce(this,e,t,n,i,1)},off:function(e,t,n){var i,a;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,S(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"!=typeof e)return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=Te),this.each(function(){S.event.remove(this,e,n,t)});for(a in e)this.off(a,t,e[a]);return this}});var ke=/<script|<style|<link/i,Me=/checked\s*(?:[^=]|=\s*.checked.)/i,Le=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function De(e,t){return M(e,"table")&&M(11!==t.nodeType?t:t.firstChild,"tr")&&S(e).children("tbody")[0]||e}function Ae(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Pe(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function ze(e,t){var n,i,a,r,s,o;if(1===t.nodeType){if(U.hasData(e)&&(o=U.get(e).events))for(a in U.remove(t,"handle events"),o)for(n=0,i=o[a].length;n<i;n++)S.event.add(t,a,o[a][n]);K.hasData(e)&&(r=K.access(e),s=S.extend({},r),K.set(t,s))}}function Ne(n,i,a,r){i=v(i);var e,t,s,o,l,u,c=0,d=n.length,p=d-1,f=i[0],h=y(f);if(h||1<d&&"string"==typeof f&&!g.checkClone&&Me.test(f))return n.each(function(e){var t=n.eq(e);h&&(i[0]=f.call(this,e,t.html())),Ne(t,i,a,r)});if(d&&(t=(e=be(i,n[0].ownerDocument,!1,n,r)).firstChild,1===e.childNodes.length&&(e=t),t||r)){for(o=(s=S.map(me(e,"script"),Ae)).length;c<d;c++)l=e,c!==p&&(l=S.clone(l,!0,!0),o&&S.merge(s,me(l,"script"))),a.call(n[c],l,c);if(o)for(u=s[s.length-1].ownerDocument,S.map(s,Pe),c=0;c<o;c++)l=s[c],he.test(l.type||"")&&!U.access(l,"globalEval")&&S.contains(u,l)&&(l.src&&"module"!==(l.type||"").toLowerCase()?S._evalUrl&&!l.noModule&&S._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")},u):w(l.textContent.replace(Le,""),l,u))}return n}function Oe(e,t,n){for(var i,a=t?S.filter(t,e):e,r=0;null!=(i=a[r]);r++)n||1!==i.nodeType||S.cleanData(me(i)),i.parentNode&&(n&&ae(i)&&ge(me(i,"script")),i.parentNode.removeChild(i));return e}S.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var i,a,r,s,o,l,u,c=e.cloneNode(!0),d=ae(e);if(!(g.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||S.isXMLDoc(e)))for(s=me(c),i=0,a=(r=me(e)).length;i<a;i++)o=r[i],"input"===(u=(l=s[i]).nodeName.toLowerCase())&&pe.test(o.type)?l.checked=o.checked:"input"!==u&&"textarea"!==u||(l.defaultValue=o.defaultValue);if(t)if(n)for(r=r||me(e),s=s||me(c),i=0,a=r.length;i<a;i++)ze(r[i],s[i]);else ze(e,c);return 0<(s=me(c,"script")).length&&ge(s,!d&&me(e,"script")),c},cleanData:function(e){for(var t,n,i,a=S.event.special,r=0;void 0!==(n=e[r]);r++)if(_(n)){if(t=n[U.expando]){if(t.events)for(i in t.events)a[i]?S.event.remove(n,i):S.removeEvent(n,i,t.handle);n[U.expando]=void 0}n[K.expando]&&(n[K.expando]=void 0)}}}),S.fn.extend({detach:function(e){return Oe(this,e,!0)},remove:function(e){return Oe(this,e)},text:function(e){return G(this,function(e){return void 0===e?S.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return Ne(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||De(this,e).appendChild(e)})},prepend:function(){return Ne(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=De(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return Ne(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return Ne(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(S.cleanData(me(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return S.clone(this,e,t)})},html:function(e){return G(this,function(e){var t=this[0]||{},n=0,i=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!ke.test(e)&&!ve[(fe.exec(e)||["",""])[1].toLowerCase()]){e=S.htmlPrefilter(e);try{for(;n<i;n++)1===(t=this[n]||{}).nodeType&&(S.cleanData(me(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var n=[];return Ne(this,arguments,function(e){var t=this.parentNode;S.inArray(this,n)<0&&(S.cleanData(me(this)),t&&t.replaceChild(e,this))},n)}}),S.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,s){S.fn[e]=function(e){for(var t,n=[],i=S(e),a=i.length-1,r=0;r<=a;r++)t=r===a?this:this.clone(!0),S(i[r])[s](t),l.apply(n,t.get());return this.pushStack(n)}});var $e=new RegExp("^("+ee+")(?!px)[a-z%]+$","i"),Ie=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=E),t.getComputedStyle(e)},je=function(e,t,n){var i,a,r={};for(a in t)r[a]=e.style[a],e.style[a]=t[a];for(a in i=n.call(e),t)e.style[a]=r[a];return i},He=new RegExp(ne.join("|"),"i");function Re(e,t,n){var i,a,r,s,o=e.style;return(n=n||Ie(e))&&(""!==(s=n.getPropertyValue(t)||n[t])||ae(e)||(s=S.style(e,t)),!g.pixelBoxStyles()&&$e.test(s)&&He.test(t)&&(i=o.width,a=o.minWidth,r=o.maxWidth,o.minWidth=o.maxWidth=o.width=s,s=n.width,o.width=i,o.minWidth=a,o.maxWidth=r)),void 0!==s?s+"":s}function qe(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}!function(){function e(){if(u){l.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",u.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",ie.appendChild(l).appendChild(u);var e=E.getComputedStyle(u);n="1%"!==e.top,o=12===t(e.marginLeft),u.style.right="60%",r=36===t(e.right),i=36===t(e.width),u.style.position="absolute",a=12===t(u.offsetWidth/3),ie.removeChild(l),u=null}}function t(e){return Math.round(parseFloat(e))}var n,i,a,r,s,o,l=C.createElement("div"),u=C.createElement("div");u.style&&(u.style.backgroundClip="content-box",u.cloneNode(!0).style.backgroundClip="",g.clearCloneStyle="content-box"===u.style.backgroundClip,S.extend(g,{boxSizingReliable:function(){return e(),i},pixelBoxStyles:function(){return e(),r},pixelPosition:function(){return e(),n},reliableMarginLeft:function(){return e(),o},scrollboxSize:function(){return e(),a},reliableTrDimensions:function(){var e,t,n,i;return null==s&&(e=C.createElement("table"),t=C.createElement("tr"),n=C.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",t.style.cssText="border:1px solid",t.style.height="1px",n.style.height="9px",n.style.display="block",ie.appendChild(e).appendChild(t).appendChild(n),i=E.getComputedStyle(t),s=parseInt(i.height,10)+parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10)===t.offsetHeight,ie.removeChild(e)),s}}))}();var Be=["Webkit","Moz","ms"],Ge=C.createElement("div").style,Fe={};function We(e){return S.cssProps[e]||Fe[e]||(e in Ge?e:Fe[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),n=Be.length;n--;)if((e=Be[n]+t)in Ge)return e}(e)||e)}var Ve=/^(none|table(?!-c[ea]).+)/,Xe=/^--/,_e={position:"absolute",visibility:"hidden",display:"block"},Ye={letterSpacing:"0",fontWeight:"400"};function Ue(e,t,n){var i=te.exec(t);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):t}function Ke(e,t,n,i,a,r){var s="width"===t?1:0,o=0,l=0;if(n===(i?"border":"content"))return 0;for(;s<4;s+=2)"margin"===n&&(l+=S.css(e,n+ne[s],!0,a)),i?("content"===n&&(l-=S.css(e,"padding"+ne[s],!0,a)),"margin"!==n&&(l-=S.css(e,"border"+ne[s]+"Width",!0,a))):(l+=S.css(e,"padding"+ne[s],!0,a),"padding"!==n?l+=S.css(e,"border"+ne[s]+"Width",!0,a):o+=S.css(e,"border"+ne[s]+"Width",!0,a));return!i&&0<=r&&(l+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-r-l-o-.5))||0),l}function Je(e,t,n){var i=Ie(e),a=(!g.boxSizingReliable()||n)&&"border-box"===S.css(e,"boxSizing",!1,i),r=a,s=Re(e,t,i),o="offset"+t[0].toUpperCase()+t.slice(1);if($e.test(s)){if(!n)return s;s="auto"}return(!g.boxSizingReliable()&&a||!g.reliableTrDimensions()&&M(e,"tr")||"auto"===s||!parseFloat(s)&&"inline"===S.css(e,"display",!1,i))&&e.getClientRects().length&&(a="border-box"===S.css(e,"boxSizing",!1,i),(r=o in e)&&(s=e[o])),(s=parseFloat(s)||0)+Ke(e,t,n||(a?"border":"content"),r,i,s)+"px"}function Ze(e,t,n,i,a){return new Ze.prototype.init(e,t,n,i,a)}S.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Re(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var a,r,s,o=X(t),l=Xe.test(t),u=e.style;if(l||(t=We(o)),s=S.cssHooks[t]||S.cssHooks[o],void 0===n)return s&&"get"in s&&void 0!==(a=s.get(e,!1,i))?a:u[t];"string"==(r=typeof n)&&(a=te.exec(n))&&a[1]&&(n=oe(e,t,a),r="number"),null!=n&&n==n&&("number"!==r||l||(n+=a&&a[3]||(S.cssNumber[o]?"":"px")),g.clearCloneStyle||""!==n||0!==t.indexOf("background")||(u[t]="inherit"),s&&"set"in s&&void 0===(n=s.set(e,n,i))||(l?u.setProperty(t,n):u[t]=n))}},css:function(e,t,n,i){var a,r,s,o=X(t);return Xe.test(t)||(t=We(o)),(s=S.cssHooks[t]||S.cssHooks[o])&&"get"in s&&(a=s.get(e,!0,n)),void 0===a&&(a=Re(e,t,i)),"normal"===a&&t in Ye&&(a=Ye[t]),""===n||n?(r=parseFloat(a),!0===n||isFinite(r)?r||0:a):a}}),S.each(["height","width"],function(e,l){S.cssHooks[l]={get:function(e,t,n){if(t)return!Ve.test(S.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?Je(e,l,n):je(e,_e,function(){return Je(e,l,n)})},set:function(e,t,n){var i,a=Ie(e),r=!g.scrollboxSize()&&"absolute"===a.position,s=(r||n)&&"border-box"===S.css(e,"boxSizing",!1,a),o=n?Ke(e,l,n,s,a):0;return s&&r&&(o-=Math.ceil(e["offset"+l[0].toUpperCase()+l.slice(1)]-parseFloat(a[l])-Ke(e,l,"border",!1,a)-.5)),o&&(i=te.exec(t))&&"px"!==(i[3]||"px")&&(e.style[l]=t,t=S.css(e,l)),Ue(0,t,o)}}}),S.cssHooks.marginLeft=qe(g.reliableMarginLeft,function(e,t){if(t)return(parseFloat(Re(e,"marginLeft"))||e.getBoundingClientRect().left-je(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),S.each({margin:"",padding:"",border:"Width"},function(a,r){S.cssHooks[a+r]={expand:function(e){for(var t=0,n={},i="string"==typeof e?e.split(" "):[e];t<4;t++)n[a+ne[t]+r]=i[t]||i[t-2]||i[0];return n}},"margin"!==a&&(S.cssHooks[a+r].set=Ue)}),S.fn.extend({css:function(e,t){return G(this,function(e,t,n){var i,a,r={},s=0;if(Array.isArray(t)){for(i=Ie(e),a=t.length;s<a;s++)r[t[s]]=S.css(e,t[s],!1,i);return r}return void 0!==n?S.style(e,t,n):S.css(e,t)},e,t,1<arguments.length)}}),((S.Tween=Ze).prototype={constructor:Ze,init:function(e,t,n,i,a,r){this.elem=e,this.prop=n,this.easing=a||S.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=r||(S.cssNumber[n]?"":"px")},cur:function(){var e=Ze.propHooks[this.prop];return e&&e.get?e.get(this):Ze.propHooks._default.get(this)},run:function(e){var t,n=Ze.propHooks[this.prop];return this.options.duration?this.pos=t=S.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):Ze.propHooks._default.set(this),this}}).init.prototype=Ze.prototype,(Ze.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=S.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){S.fx.step[e.prop]?S.fx.step[e.prop](e):1!==e.elem.nodeType||!S.cssHooks[e.prop]&&null==e.elem.style[We(e.prop)]?e.elem[e.prop]=e.now:S.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=Ze.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},S.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},S.fx=Ze.prototype.init,S.fx.step={};var Qe,et,tt,nt,it=/^(?:toggle|show|hide)$/,at=/queueHooks$/;function rt(){et&&(!1===C.hidden&&E.requestAnimationFrame?E.requestAnimationFrame(rt):E.setTimeout(rt,S.fx.interval),S.fx.tick())}function st(){return E.setTimeout(function(){Qe=void 0}),Qe=Date.now()}function ot(e,t){var n,i=0,a={height:e};for(t=t?1:0;i<4;i+=2-t)a["margin"+(n=ne[i])]=a["padding"+n]=e;return t&&(a.opacity=a.width=e),a}function lt(e,t,n){for(var i,a=(ut.tweeners[t]||[]).concat(ut.tweeners["*"]),r=0,s=a.length;r<s;r++)if(i=a[r].call(n,t,e))return i}function ut(r,e,t){var n,s,i=0,a=ut.prefilters.length,o=S.Deferred().always(function(){delete l.elem}),l=function(){if(s)return!1;for(var e=Qe||st(),t=Math.max(0,u.startTime+u.duration-e),n=1-(t/u.duration||0),i=0,a=u.tweens.length;i<a;i++)u.tweens[i].run(n);return o.notifyWith(r,[u,n,t]),n<1&&a?t:(a||o.notifyWith(r,[u,1,0]),o.resolveWith(r,[u]),!1)},u=o.promise({elem:r,props:S.extend({},e),opts:S.extend(!0,{specialEasing:{},easing:S.easing._default},t),originalProperties:e,originalOptions:t,startTime:Qe||st(),duration:t.duration,tweens:[],createTween:function(e,t){var n=S.Tween(r,u.opts,e,t,u.opts.specialEasing[e]||u.opts.easing);return u.tweens.push(n),n},stop:function(e){var t=0,n=e?u.tweens.length:0;if(s)return this;for(s=!0;t<n;t++)u.tweens[t].run(1);return e?(o.notifyWith(r,[u,1,0]),o.resolveWith(r,[u,e])):o.rejectWith(r,[u,e]),this}}),c=u.props;for(function(e,t){var n,i,a,r,s;for(n in e)if(a=t[i=X(n)],r=e[n],Array.isArray(r)&&(a=r[1],r=e[n]=r[0]),n!==i&&(e[i]=r,delete e[n]),(s=S.cssHooks[i])&&"expand"in s)for(n in r=s.expand(r),delete e[i],r)n in e||(e[n]=r[n],t[n]=a);else t[i]=a}(c,u.opts.specialEasing);i<a;i++)if(n=ut.prefilters[i].call(u,r,c,u.opts))return y(n.stop)&&(S._queueHooks(u.elem,u.opts.queue).stop=n.stop.bind(n)),n;return S.map(c,lt,u),y(u.opts.start)&&u.opts.start.call(r,u),u.progress(u.opts.progress).done(u.opts.done,u.opts.complete).fail(u.opts.fail).always(u.opts.always),S.fx.timer(S.extend(l,{elem:r,anim:u,queue:u.opts.queue})),u}S.Animation=S.extend(ut,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return oe(n.elem,e,te.exec(t),n),n}]},tweener:function(e,t){for(var n,i=0,a=(e=y(e)?(t=e,["*"]):e.match($)).length;i<a;i++)n=e[i],ut.tweeners[n]=ut.tweeners[n]||[],ut.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var i,a,r,s,o,l,u,c,d="width"in t||"height"in t,p=this,f={},h=e.style,v=e.nodeType&&se(e),m=U.get(e,"fxshow");for(i in n.queue||(null==(s=S._queueHooks(e,"fx")).unqueued&&(s.unqueued=0,o=s.empty.fire,s.empty.fire=function(){s.unqueued||o()}),s.unqueued++,p.always(function(){p.always(function(){s.unqueued--,S.queue(e,"fx").length||s.empty.fire()})})),t)if(a=t[i],it.test(a)){if(delete t[i],r=r||"toggle"===a,a===(v?"hide":"show")){if("show"!==a||!m||void 0===m[i])continue;v=!0}f[i]=m&&m[i]||S.style(e,i)}if((l=!S.isEmptyObject(t))||!S.isEmptyObject(f))for(i in d&&1===e.nodeType&&(n.overflow=[h.overflow,h.overflowX,h.overflowY],null==(u=m&&m.display)&&(u=U.get(e,"display")),"none"===(c=S.css(e,"display"))&&(u?c=u:(ue([e],!0),u=e.style.display||u,c=S.css(e,"display"),ue([e]))),("inline"===c||"inline-block"===c&&null!=u)&&"none"===S.css(e,"float")&&(l||(p.done(function(){h.display=u}),null==u&&(c=h.display,u="none"===c?"":c)),h.display="inline-block")),n.overflow&&(h.overflow="hidden",p.always(function(){h.overflow=n.overflow[0],h.overflowX=n.overflow[1],h.overflowY=n.overflow[2]})),l=!1,f)l||(m?"hidden"in m&&(v=m.hidden):m=U.access(e,"fxshow",{display:u}),r&&(m.hidden=!v),v&&ue([e],!0),p.done(function(){for(i in v||ue([e]),U.remove(e,"fxshow"),f)S.style(e,i,f[i])})),l=lt(v?m[i]:0,i,p),i in m||(m[i]=l.start,v&&(l.end=l.start,l.start=0))}],prefilter:function(e,t){t?ut.prefilters.unshift(e):ut.prefilters.push(e)}}),S.speed=function(e,t,n){var i=e&&"object"==typeof e?S.extend({},e):{complete:n||!n&&t||y(e)&&e,duration:e,easing:n&&t||t&&!y(t)&&t};return S.fx.off?i.duration=0:"number"!=typeof i.duration&&(i.duration in S.fx.speeds?i.duration=S.fx.speeds[i.duration]:i.duration=S.fx.speeds._default),null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){y(i.old)&&i.old.call(this),i.queue&&S.dequeue(this,i.queue)},i},S.fn.extend({fadeTo:function(e,t,n,i){return this.filter(se).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(t,e,n,i){var a=S.isEmptyObject(t),r=S.speed(e,n,i),s=function(){var e=ut(this,S.extend({},t),r);(a||U.get(this,"finish"))&&e.stop(!0)};return s.finish=s,a||!1===r.queue?this.each(s):this.queue(r.queue,s)},stop:function(a,e,r){var s=function(e){var t=e.stop;delete e.stop,t(r)};return"string"!=typeof a&&(r=e,e=a,a=void 0),e&&this.queue(a||"fx",[]),this.each(function(){var e=!0,t=null!=a&&a+"queueHooks",n=S.timers,i=U.get(this);if(t)i[t]&&i[t].stop&&s(i[t]);else for(t in i)i[t]&&i[t].stop&&at.test(t)&&s(i[t]);for(t=n.length;t--;)n[t].elem!==this||null!=a&&n[t].queue!==a||(n[t].anim.stop(r),e=!1,n.splice(t,1));!e&&r||S.dequeue(this,a)})},finish:function(s){return!1!==s&&(s=s||"fx"),this.each(function(){var e,t=U.get(this),n=t[s+"queue"],i=t[s+"queueHooks"],a=S.timers,r=n?n.length:0;for(t.finish=!0,S.queue(this,s,[]),i&&i.stop&&i.stop.call(this,!0),e=a.length;e--;)a[e].elem===this&&a[e].queue===s&&(a[e].anim.stop(!0),a.splice(e,1));for(e=0;e<r;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete t.finish})}}),S.each(["toggle","show","hide"],function(e,i){var a=S.fn[i];S.fn[i]=function(e,t,n){return null==e||"boolean"==typeof e?a.apply(this,arguments):this.animate(ot(i,!0),e,t,n)}}),S.each({slideDown:ot("show"),slideUp:ot("hide"),slideToggle:ot("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,i){S.fn[e]=function(e,t,n){return this.animate(i,e,t,n)}}),S.timers=[],S.fx.tick=function(){var e,t=0,n=S.timers;for(Qe=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||S.fx.stop(),Qe=void 0},S.fx.timer=function(e){S.timers.push(e),S.fx.start()},S.fx.interval=13,S.fx.start=function(){et||(et=!0,rt())},S.fx.stop=function(){et=null},S.fx.speeds={slow:600,fast:200,_default:400},S.fn.delay=function(i,e){return i=S.fx&&S.fx.speeds[i]||i,e=e||"fx",this.queue(e,function(e,t){var n=E.setTimeout(e,i);t.stop=function(){E.clearTimeout(n)}})},tt=C.createElement("input"),nt=C.createElement("select").appendChild(C.createElement("option")),tt.type="checkbox",g.checkOn=""!==tt.value,g.optSelected=nt.selected,(tt=C.createElement("input")).value="t",tt.type="radio",g.radioValue="t"===tt.value;var ct,dt=S.expr.attrHandle;S.fn.extend({attr:function(e,t){return G(this,S.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){S.removeAttr(this,e)})}}),S.extend({attr:function(e,t,n){var i,a,r=e.nodeType;if(3!==r&&8!==r&&2!==r)return void 0===e.getAttribute?S.prop(e,t,n):(1===r&&S.isXMLDoc(e)||(a=S.attrHooks[t.toLowerCase()]||(S.expr.match.bool.test(t)?ct:void 0)),void 0!==n?null===n?void S.removeAttr(e,t):a&&"set"in a&&void 0!==(i=a.set(e,n,t))?i:(e.setAttribute(t,n+""),n):a&&"get"in a&&null!==(i=a.get(e,t))?i:null==(i=S.find.attr(e,t))?void 0:i)},attrHooks:{type:{set:function(e,t){if(!g.radioValue&&"radio"===t&&M(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,i=0,a=t&&t.match($);if(a&&1===e.nodeType)for(;n=a[i++];)e.removeAttribute(n)}}),ct={set:function(e,t,n){return!1===t?S.removeAttr(e,n):e.setAttribute(n,n),n}},S.each(S.expr.match.bool.source.match(/\w+/g),function(e,t){var s=dt[t]||S.find.attr;dt[t]=function(e,t,n){var i,a,r=t.toLowerCase();return n||(a=dt[r],dt[r]=i,i=null!=s(e,t,n)?r:null,dt[r]=a),i}});var pt=/^(?:input|select|textarea|button)$/i,ft=/^(?:a|area)$/i;function ht(e){return(e.match($)||[]).join(" ")}function vt(e){return e.getAttribute&&e.getAttribute("class")||""}function mt(e){return Array.isArray(e)?e:"string"==typeof e&&e.match($)||[]}S.fn.extend({prop:function(e,t){return G(this,S.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[S.propFix[e]||e]})}}),S.extend({prop:function(e,t,n){var i,a,r=e.nodeType;if(3!==r&&8!==r&&2!==r)return 1===r&&S.isXMLDoc(e)||(t=S.propFix[t]||t,a=S.propHooks[t]),void 0!==n?a&&"set"in a&&void 0!==(i=a.set(e,n,t))?i:e[t]=n:a&&"get"in a&&null!==(i=a.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=S.find.attr(e,"tabindex");return t?parseInt(t,10):pt.test(e.nodeName)||ft.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),g.optSelected||(S.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),S.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){S.propFix[this.toLowerCase()]=this}),S.fn.extend({addClass:function(t){var e,n,i,a,r,s,o,l=0;if(y(t))return this.each(function(e){S(this).addClass(t.call(this,e,vt(this)))});if((e=mt(t)).length)for(;n=this[l++];)if(a=vt(n),i=1===n.nodeType&&" "+ht(a)+" "){for(s=0;r=e[s++];)i.indexOf(" "+r+" ")<0&&(i+=r+" ");a!==(o=ht(i))&&n.setAttribute("class",o)}return this},removeClass:function(t){var e,n,i,a,r,s,o,l=0;if(y(t))return this.each(function(e){S(this).removeClass(t.call(this,e,vt(this)))});if(!arguments.length)return this.attr("class","");if((e=mt(t)).length)for(;n=this[l++];)if(a=vt(n),i=1===n.nodeType&&" "+ht(a)+" "){for(s=0;r=e[s++];)for(;-1<i.indexOf(" "+r+" ");)i=i.replace(" "+r+" "," ");a!==(o=ht(i))&&n.setAttribute("class",o)}return this},toggleClass:function(a,t){var r=typeof a,s="string"===r||Array.isArray(a);return"boolean"==typeof t&&s?t?this.addClass(a):this.removeClass(a):y(a)?this.each(function(e){S(this).toggleClass(a.call(this,e,vt(this),t),t)}):this.each(function(){var e,t,n,i;if(s)for(t=0,n=S(this),i=mt(a);e=i[t++];)n.hasClass(e)?n.removeClass(e):n.addClass(e);else void 0!==a&&"boolean"!==r||((e=vt(this))&&U.set(this,"__className__",e),this.setAttribute&&this.setAttribute("class",e||!1===a?"":U.get(this,"__className__")||""))})},hasClass:function(e){var t,n,i=0;for(t=" "+e+" ";n=this[i++];)if(1===n.nodeType&&-1<(" "+ht(vt(n))+" ").indexOf(t))return!0;return!1}});var gt=/\r/g;S.fn.extend({val:function(n){var i,e,a,t=this[0];return arguments.length?(a=y(n),this.each(function(e){var t;1===this.nodeType&&(null==(t=a?n.call(this,e,S(this).val()):n)?t="":"number"==typeof t?t+="":Array.isArray(t)&&(t=S.map(t,function(e){return null==e?"":e+""})),(i=S.valHooks[this.type]||S.valHooks[this.nodeName.toLowerCase()])&&"set"in i&&void 0!==i.set(this,t,"value")||(this.value=t))})):t?(i=S.valHooks[t.type]||S.valHooks[t.nodeName.toLowerCase()])&&"get"in i&&void 0!==(e=i.get(t,"value"))?e:"string"==typeof(e=t.value)?e.replace(gt,""):null==e?"":e:void 0}}),S.extend({valHooks:{option:{get:function(e){var t=S.find.attr(e,"value");return null!=t?t:ht(S.text(e))}},select:{get:function(e){var t,n,i,a=e.options,r=e.selectedIndex,s="select-one"===e.type,o=s?null:[],l=s?r+1:a.length;for(i=r<0?l:s?r:0;i<l;i++)if(((n=a[i]).selected||i===r)&&!n.disabled&&(!n.parentNode.disabled||!M(n.parentNode,"optgroup"))){if(t=S(n).val(),s)return t;o.push(t)}return o},set:function(e,t){for(var n,i,a=e.options,r=S.makeArray(t),s=a.length;s--;)((i=a[s]).selected=-1<S.inArray(S.valHooks.option.get(i),r))&&(n=!0);return n||(e.selectedIndex=-1),r}}}}),S.each(["radio","checkbox"],function(){S.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=-1<S.inArray(S(e).val(),t)}},g.checkOn||(S.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),g.focusin="onfocusin"in E;var yt=/^(?:focusinfocus|focusoutblur)$/,bt=function(e){e.stopPropagation()};S.extend(S.event,{trigger:function(e,t,n,i){var a,r,s,o,l,u,c,d,p=[n||C],f=m.call(e,"type")?e.type:e,h=m.call(e,"namespace")?e.namespace.split("."):[];if(r=d=s=n=n||C,3!==n.nodeType&&8!==n.nodeType&&!yt.test(f+S.event.triggered)&&(-1<f.indexOf(".")&&(f=(h=f.split(".")).shift(),h.sort()),l=f.indexOf(":")<0&&"on"+f,(e=e[S.expando]?e:new S.Event(f,"object"==typeof e&&e)).isTrigger=i?2:3,e.namespace=h.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:S.makeArray(t,[e]),c=S.event.special[f]||{},i||!c.trigger||!1!==c.trigger.apply(n,t))){if(!i&&!c.noBubble&&!b(n)){for(o=c.delegateType||f,yt.test(o+f)||(r=r.parentNode);r;r=r.parentNode)p.push(r),s=r;s===(n.ownerDocument||C)&&p.push(s.defaultView||s.parentWindow||E)}for(a=0;(r=p[a++])&&!e.isPropagationStopped();)d=r,e.type=1<a?o:c.bindType||f,(u=(U.get(r,"events")||Object.create(null))[e.type]&&U.get(r,"handle"))&&u.apply(r,t),(u=l&&r[l])&&u.apply&&_(r)&&(e.result=u.apply(r,t),!1===e.result&&e.preventDefault());return e.type=f,i||e.isDefaultPrevented()||c._default&&!1!==c._default.apply(p.pop(),t)||!_(n)||l&&y(n[f])&&!b(n)&&((s=n[l])&&(n[l]=null),S.event.triggered=f,e.isPropagationStopped()&&d.addEventListener(f,bt),n[f](),e.isPropagationStopped()&&d.removeEventListener(f,bt),S.event.triggered=void 0,s&&(n[l]=s)),e.result}},simulate:function(e,t,n){var i=S.extend(new S.Event,n,{type:e,isSimulated:!0});S.event.trigger(i,null,t)}}),S.fn.extend({trigger:function(e,t){return this.each(function(){S.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return S.event.trigger(e,t,n,!0)}}),g.focusin||S.each({focus:"focusin",blur:"focusout"},function(n,i){var a=function(e){S.event.simulate(i,e.target,S.event.fix(e))};S.event.special[i]={setup:function(){var e=this.ownerDocument||this.document||this,t=U.access(e,i);t||e.addEventListener(n,a,!0),U.access(e,i,(t||0)+1)},teardown:function(){var e=this.ownerDocument||this.document||this,t=U.access(e,i)-1;t?U.access(e,i,t):(e.removeEventListener(n,a,!0),U.remove(e,i))}}});var wt=E.location,xt={guid:Date.now()},Tt=/\?/;S.parseXML=function(e){var t,n;if(!e||"string"!=typeof e)return null;try{t=(new E.DOMParser).parseFromString(e,"text/xml")}catch(e){}return n=t&&t.getElementsByTagName("parsererror")[0],t&&!n||S.error("Invalid XML: "+(n?S.map(n.childNodes,function(e){return e.textContent}).join("\n"):e)),t};var Et=/\[\]$/,Ct=/\r?\n/g,St=/^(?:submit|button|image|reset|file)$/i,kt=/^(?:input|select|textarea|keygen)/i;function Mt(n,e,i,a){var t;if(Array.isArray(e))S.each(e,function(e,t){i||Et.test(n)?a(n,t):Mt(n+"["+("object"==typeof t&&null!=t?e:"")+"]",t,i,a)});else if(i||"object"!==x(e))a(n,e);else for(t in e)Mt(n+"["+t+"]",e[t],i,a)}S.param=function(e,t){var n,i=[],a=function(e,t){var n=y(t)?t():t;i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!S.isPlainObject(e))S.each(e,function(){a(this.name,this.value)});else for(n in e)Mt(n,e[n],t,a);return i.join("&")},S.fn.extend({serialize:function(){return S.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=S.prop(this,"elements");return e?S.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!S(this).is(":disabled")&&kt.test(this.nodeName)&&!St.test(e)&&(this.checked||!pe.test(e))}).map(function(e,t){var n=S(this).val();return null==n?null:Array.isArray(n)?S.map(n,function(e){return{name:t.name,value:e.replace(Ct,"\r\n")}}):{name:t.name,value:n.replace(Ct,"\r\n")}}).get()}});var Lt=/%20/g,Dt=/#.*$/,At=/([?&])_=[^&]*/,Pt=/^(.*?):[ \t]*([^\r\n]*)$/gm,zt=/^(?:GET|HEAD)$/,Nt=/^\/\//,Ot={},$t={},It="*/".concat("*"),jt=C.createElement("a");function Ht(r){return function(e,t){"string"!=typeof e&&(t=e,e="*");var n,i=0,a=e.toLowerCase().match($)||[];if(y(t))for(;n=a[i++];)"+"===n[0]?(n=n.slice(1)||"*",(r[n]=r[n]||[]).unshift(t)):(r[n]=r[n]||[]).push(t)}}function Rt(t,a,r,s){var o={},l=t===$t;function u(e){var i;return o[e]=!0,S.each(t[e]||[],function(e,t){var n=t(a,r,s);return"string"!=typeof n||l||o[n]?l?!(i=n):void 0:(a.dataTypes.unshift(n),u(n),!1)}),i}return u(a.dataTypes[0])||!o["*"]&&u("*")}function qt(e,t){var n,i,a=S.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((a[n]?e:i||(i={}))[n]=t[n]);return i&&S.extend(!0,e,i),e}jt.href=wt.href,S.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:wt.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(wt.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":It,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":S.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?qt(qt(e,S.ajaxSettings),t):qt(S.ajaxSettings,e)},ajaxPrefilter:Ht(Ot),ajaxTransport:Ht($t),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0),t=t||{};var c,d,p,n,f,i,h,v,a,r,m=S.ajaxSetup({},t),g=m.context||m,y=m.context&&(g.nodeType||g.jquery)?S(g):S.event,b=S.Deferred(),w=S.Callbacks("once memory"),x=m.statusCode||{},s={},o={},l="canceled",T={readyState:0,getResponseHeader:function(e){var t;if(h){if(!n)for(n={};t=Pt.exec(p);)n[t[1].toLowerCase()+" "]=(n[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=n[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return h?p:null},setRequestHeader:function(e,t){return null==h&&(e=o[e.toLowerCase()]=o[e.toLowerCase()]||e,s[e]=t),this},overrideMimeType:function(e){return null==h&&(m.mimeType=e),this},statusCode:function(e){var t;if(e)if(h)T.always(e[T.status]);else for(t in e)x[t]=[x[t],e[t]];return this},abort:function(e){var t=e||l;return c&&c.abort(t),u(0,t),this}};if(b.promise(T),m.url=((e||m.url||wt.href)+"").replace(Nt,wt.protocol+"//"),m.type=t.method||t.type||m.method||m.type,m.dataTypes=(m.dataType||"*").toLowerCase().match($)||[""],null==m.crossDomain){i=C.createElement("a");try{i.href=m.url,i.href=i.href,m.crossDomain=jt.protocol+"//"+jt.host!=i.protocol+"//"+i.host}catch(e){m.crossDomain=!0}}if(m.data&&m.processData&&"string"!=typeof m.data&&(m.data=S.param(m.data,m.traditional)),Rt(Ot,m,t,T),h)return T;for(a in(v=S.event&&m.global)&&0==S.active++&&S.event.trigger("ajaxStart"),m.type=m.type.toUpperCase(),m.hasContent=!zt.test(m.type),d=m.url.replace(Dt,""),m.hasContent?m.data&&m.processData&&0===(m.contentType||"").indexOf("application/x-www-form-urlencoded")&&(m.data=m.data.replace(Lt,"+")):(r=m.url.slice(d.length),m.data&&(m.processData||"string"==typeof m.data)&&(d+=(Tt.test(d)?"&":"?")+m.data,delete m.data),!1===m.cache&&(d=d.replace(At,"$1"),r=(Tt.test(d)?"&":"?")+"_="+xt.guid+++r),m.url=d+r),m.ifModified&&(S.lastModified[d]&&T.setRequestHeader("If-Modified-Since",S.lastModified[d]),S.etag[d]&&T.setRequestHeader("If-None-Match",S.etag[d])),(m.data&&m.hasContent&&!1!==m.contentType||t.contentType)&&T.setRequestHeader("Content-Type",m.contentType),T.setRequestHeader("Accept",m.dataTypes[0]&&m.accepts[m.dataTypes[0]]?m.accepts[m.dataTypes[0]]+("*"!==m.dataTypes[0]?", "+It+"; q=0.01":""):m.accepts["*"]),m.headers)T.setRequestHeader(a,m.headers[a]);if(m.beforeSend&&(!1===m.beforeSend.call(g,T,m)||h))return T.abort();if(l="abort",w.add(m.complete),T.done(m.success),T.fail(m.error),c=Rt($t,m,t,T)){if(T.readyState=1,v&&y.trigger("ajaxSend",[T,m]),h)return T;m.async&&0<m.timeout&&(f=E.setTimeout(function(){T.abort("timeout")},m.timeout));try{h=!1,c.send(s,u)}catch(e){if(h)throw e;u(-1,e)}}else u(-1,"No Transport");function u(e,t,n,i){var a,r,s,o,l,u=t;h||(h=!0,f&&E.clearTimeout(f),c=void 0,p=i||"",T.readyState=0<e?4:0,a=200<=e&&e<300||304===e,n&&(o=function(e,t,n){for(var i,a,r,s,o=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===i&&(i=e.mimeType||t.getResponseHeader("Content-Type"));if(i)for(a in o)if(o[a]&&o[a].test(i)){l.unshift(a);break}if(l[0]in n)r=l[0];else{for(a in n){if(!l[0]||e.converters[a+" "+l[0]]){r=a;break}s||(s=a)}r=r||s}if(r)return r!==l[0]&&l.unshift(r),n[r]}(m,T,n)),!a&&-1<S.inArray("script",m.dataTypes)&&S.inArray("json",m.dataTypes)<0&&(m.converters["text script"]=function(){}),o=function(e,t,n,i){var a,r,s,o,l,u={},c=e.dataTypes.slice();if(c[1])for(s in e.converters)u[s.toLowerCase()]=e.converters[s];for(r=c.shift();r;)if(e.responseFields[r]&&(n[e.responseFields[r]]=t),!l&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=r,r=c.shift())if("*"===r)r=l;else if("*"!==l&&l!==r){if(!(s=u[l+" "+r]||u["* "+r]))for(a in u)if((o=a.split(" "))[1]===r&&(s=u[l+" "+o[0]]||u["* "+o[0]])){!0===s?s=u[a]:!0!==u[a]&&(r=o[0],c.unshift(o[1]));break}if(!0!==s)if(s&&e.throws)t=s(t);else try{t=s(t)}catch(e){return{state:"parsererror",error:s?e:"No conversion from "+l+" to "+r}}}return{state:"success",data:t}}(m,o,T,a),a?(m.ifModified&&((l=T.getResponseHeader("Last-Modified"))&&(S.lastModified[d]=l),(l=T.getResponseHeader("etag"))&&(S.etag[d]=l)),204===e||"HEAD"===m.type?u="nocontent":304===e?u="notmodified":(u=o.state,r=o.data,a=!(s=o.error))):(s=u,!e&&u||(u="error",e<0&&(e=0))),T.status=e,T.statusText=(t||u)+"",a?b.resolveWith(g,[r,u,T]):b.rejectWith(g,[T,u,s]),T.statusCode(x),x=void 0,v&&y.trigger(a?"ajaxSuccess":"ajaxError",[T,m,a?r:s]),w.fireWith(g,[T,u]),v&&(y.trigger("ajaxComplete",[T,m]),--S.active||S.event.trigger("ajaxStop")))}return T},getJSON:function(e,t,n){return S.get(e,t,n,"json")},getScript:function(e,t){return S.get(e,void 0,t,"script")}}),S.each(["get","post"],function(e,a){S[a]=function(e,t,n,i){return y(t)&&(i=i||n,n=t,t=void 0),S.ajax(S.extend({url:e,type:a,dataType:i,data:t,success:n},S.isPlainObject(e)&&e))}}),S.ajaxPrefilter(function(e){var t;for(t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),S._evalUrl=function(e,t,n){return S.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){S.globalEval(e,t,n)}})},S.fn.extend({wrapAll:function(e){var t;return this[0]&&(y(e)&&(e=e.call(this[0])),t=S(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(n){return y(n)?this.each(function(e){S(this).wrapInner(n.call(this,e))}):this.each(function(){var e=S(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=y(t);return this.each(function(e){S(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(e){return this.parent(e).not("body").each(function(){S(this).replaceWith(this.childNodes)}),this}}),S.expr.pseudos.hidden=function(e){return!S.expr.pseudos.visible(e)},S.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},S.ajaxSettings.xhr=function(){try{return new E.XMLHttpRequest}catch(e){}};var Bt={0:200,1223:204},Gt=S.ajaxSettings.xhr();g.cors=!!Gt&&"withCredentials"in Gt,g.ajax=Gt=!!Gt,S.ajaxTransport(function(a){var r,s;if(g.cors||Gt&&!a.crossDomain)return{send:function(e,t){var n,i=a.xhr();if(i.open(a.type,a.url,a.async,a.username,a.password),a.xhrFields)for(n in a.xhrFields)i[n]=a.xhrFields[n];for(n in a.mimeType&&i.overrideMimeType&&i.overrideMimeType(a.mimeType),a.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest"),e)i.setRequestHeader(n,e[n]);r=function(e){return function(){r&&(r=s=i.onload=i.onerror=i.onabort=i.ontimeout=i.onreadystatechange=null,"abort"===e?i.abort():"error"===e?"number"!=typeof i.status?t(0,"error"):t(i.status,i.statusText):t(Bt[i.status]||i.status,i.statusText,"text"!==(i.responseType||"text")||"string"!=typeof i.responseText?{binary:i.response}:{text:i.responseText},i.getAllResponseHeaders()))}},i.onload=r(),s=i.onerror=i.ontimeout=r("error"),void 0!==i.onabort?i.onabort=s:i.onreadystatechange=function(){4===i.readyState&&E.setTimeout(function(){r&&s()})},r=r("abort");try{i.send(a.hasContent&&a.data||null)}catch(e){if(r)throw e}},abort:function(){r&&r()}}}),S.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),S.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return S.globalEval(e),e}}}),S.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),S.ajaxTransport("script",function(n){var i,a;if(n.crossDomain||n.scriptAttrs)return{send:function(e,t){i=S("<script>").attr(n.scriptAttrs||{}).prop({charset:n.scriptCharset,src:n.url}).on("load error",a=function(e){i.remove(),a=null,e&&t("error"===e.type?404:200,e.type)}),C.head.appendChild(i[0])},abort:function(){a&&a()}}});var Ft,Wt=[],Vt=/(=)\?(?=&|$)|\?\?/;S.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Wt.pop()||S.expando+"_"+xt.guid++;return this[e]=!0,e}}),S.ajaxPrefilter("json jsonp",function(e,t,n){var i,a,r,s=!1!==e.jsonp&&(Vt.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Vt.test(e.data)&&"data");if(s||"jsonp"===e.dataTypes[0])return i=e.jsonpCallback=y(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,s?e[s]=e[s].replace(Vt,"$1"+i):!1!==e.jsonp&&(e.url+=(Tt.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return r||S.error(i+" was not called"),r[0]},e.dataTypes[0]="json",a=E[i],E[i]=function(){r=arguments},n.always(function(){void 0===a?S(E).removeProp(i):E[i]=a,e[i]&&(e.jsonpCallback=t.jsonpCallback,Wt.push(i)),r&&y(a)&&a(r[0]),r=a=void 0}),"script"}),g.createHTMLDocument=((Ft=C.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===Ft.childNodes.length),S.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(g.createHTMLDocument?((i=(t=C.implementation.createHTMLDocument("")).createElement("base")).href=C.location.href,t.head.appendChild(i)):t=C),r=!n&&[],(a=L.exec(e))?[t.createElement(a[1])]:(a=be([e],t,r),r&&r.length&&S(r).remove(),S.merge([],a.childNodes)));var i,a,r},S.fn.load=function(e,t,n){var i,a,r,s=this,o=e.indexOf(" ");return-1<o&&(i=ht(e.slice(o)),e=e.slice(0,o)),y(t)?(n=t,t=void 0):t&&"object"==typeof t&&(a="POST"),0<s.length&&S.ajax({url:e,type:a||"GET",dataType:"html",data:t}).done(function(e){r=arguments,s.html(i?S("<div>").append(S.parseHTML(e)).find(i):e)}).always(n&&function(e,t){s.each(function(){n.apply(this,r||[e.responseText,t,e])})}),this},S.expr.pseudos.animated=function(t){return S.grep(S.timers,function(e){return t===e.elem}).length},S.offset={setOffset:function(e,t,n){var i,a,r,s,o,l,u=S.css(e,"position"),c=S(e),d={};"static"===u&&(e.style.position="relative"),o=c.offset(),r=S.css(e,"top"),l=S.css(e,"left"),a=("absolute"===u||"fixed"===u)&&-1<(r+l).indexOf("auto")?(s=(i=c.position()).top,i.left):(s=parseFloat(r)||0,parseFloat(l)||0),y(t)&&(t=t.call(e,n,S.extend({},o))),null!=t.top&&(d.top=t.top-o.top+s),null!=t.left&&(d.left=t.left-o.left+a),"using"in t?t.using.call(e,d):c.css(d)}},S.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){S.offset.setOffset(this,t,e)});var e,n,i=this[0];return i?i.getClientRects().length?(e=i.getBoundingClientRect(),n=i.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,i=this[0],a={top:0,left:0};if("fixed"===S.css(i,"position"))t=i.getBoundingClientRect();else{for(t=this.offset(),n=i.ownerDocument,e=i.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===S.css(e,"position");)e=e.parentNode;e&&e!==i&&1===e.nodeType&&((a=S(e).offset()).top+=S.css(e,"borderTopWidth",!0),a.left+=S.css(e,"borderLeftWidth",!0))}return{top:t.top-a.top-S.css(i,"marginTop",!0),left:t.left-a.left-S.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===S.css(e,"position");)e=e.offsetParent;return e||ie})}}),S.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,a){var r="pageYOffset"===a;S.fn[t]=function(e){return G(this,function(e,t,n){var i;if(b(e)?i=e:9===e.nodeType&&(i=e.defaultView),void 0===n)return i?i[a]:e[t];i?i.scrollTo(r?i.pageXOffset:n,r?n:i.pageYOffset):e[t]=n},t,e,arguments.length)}}),S.each(["top","left"],function(e,n){S.cssHooks[n]=qe(g.pixelPosition,function(e,t){if(t)return t=Re(e,n),$e.test(t)?S(e).position()[n]+"px":t})}),S.each({Height:"height",Width:"width"},function(s,o){S.each({padding:"inner"+s,content:o,"":"outer"+s},function(i,r){S.fn[r]=function(e,t){var n=arguments.length&&(i||"boolean"!=typeof e),a=i||(!0===e||!0===t?"margin":"border");return G(this,function(e,t,n){var i;return b(e)?0===r.indexOf("outer")?e["inner"+s]:e.document.documentElement["client"+s]:9===e.nodeType?(i=e.documentElement,Math.max(e.body["scroll"+s],i["scroll"+s],e.body["offset"+s],i["offset"+s],i["client"+s])):void 0===n?S.css(e,t,a):S.style(e,t,n,a)},o,n?e:void 0,n)}})}),S.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){S.fn[t]=function(e){return this.on(t,e)}}),S.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),S.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,n){S.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}});var Xt=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;S.proxy=function(e,t){var n,i,a;if("string"==typeof t&&(n=e[t],t=e,e=n),y(e))return i=o.call(arguments,2),(a=function(){return e.apply(t||this,i.concat(o.call(arguments)))}).guid=e.guid=e.guid||S.guid++,a},S.holdReady=function(e){e?S.readyWait++:S.ready(!0)},S.isArray=Array.isArray,S.parseJSON=JSON.parse,S.nodeName=M,S.isFunction=y,S.isWindow=b,S.camelCase=X,S.type=x,S.now=Date.now,S.isNumeric=function(e){var t=S.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},S.trim=function(e){return null==e?"":(e+"").replace(Xt,"")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return S});var _t=E.jQuery,Yt=E.$;return S.noConflict=function(e){return E.$===S&&(E.$=Yt),e&&E.jQuery===S&&(E.jQuery=_t),S},void 0===e&&(E.jQuery=E.$=S),S}),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).Swiper=t()}(this,function(){"use strict";function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function e(){return(e=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(this,arguments)}function a(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function r(t,n){void 0===t&&(t={}),void 0===n&&(n={}),Object.keys(n).forEach(function(e){void 0===t[e]?t[e]=n[e]:a(n[e])&&a(t[e])&&0<Object.keys(n[e]).length&&r(t[e],n[e])})}var t={body:{},addEventListener:function(){},removeEventListener:function(){},activeElement:{blur:function(){},nodeName:""},querySelector:function(){return null},querySelectorAll:function(){return[]},getElementById:function(){return null},createEvent:function(){return{initEvent:function(){}}},createElement:function(){return{children:[],childNodes:[],style:{},setAttribute:function(){},getElementsByTagName:function(){return[]}}},createElementNS:function(){return{}},importNode:function(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function C(){var e="undefined"!=typeof document?document:{};return r(e,t),e}var n={document:t,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState:function(){},pushState:function(){},go:function(){},back:function(){}},CustomEvent:function(){return this},addEventListener:function(){},removeEventListener:function(){},getComputedStyle:function(){return{getPropertyValue:function(){return""}}},Image:function(){},Date:function(){},screen:{},setTimeout:function(){},clearTimeout:function(){},matchMedia:function(){return{}},requestAnimationFrame:function(e){return"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0)},cancelAnimationFrame:function(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function L(){var e="undefined"!=typeof window?window:{};return r(e,n),e}function s(e){return(s=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function l(e,t,n){return(l=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(e){return!1}}()?Reflect.construct:function(e,t,n){var i=[null];i.push.apply(i,t);var a=new(Function.bind.apply(e,i));return n&&o(a,n.prototype),a}).apply(null,arguments)}function u(e){var i="function"==typeof Map?new Map:void 0;return(u=function(e){if(null===e||(t=e,-1===Function.toString.call(t).indexOf("[native code]")))return e;var t;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==i){if(i.has(e))return i.get(e);i.set(e,n)}function n(){return l(e,arguments,s(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),o(n,e)})(e)}var c=function(a){var e,t;function n(e){var t,n,i;return n=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(t=a.call.apply(a,[this].concat(e))||this),i=n.__proto__,Object.defineProperty(n,"__proto__",{get:function(){return i},set:function(e){i.__proto__=e}}),t}return t=a,(e=n).prototype=Object.create(t.prototype),(e.prototype.constructor=e).__proto__=t,n}(u(Array));function d(e){void 0===e&&(e=[]);var t=[];return e.forEach(function(e){Array.isArray(e)?t.push.apply(t,d(e)):t.push(e)}),t}function p(e,t){return Array.prototype.filter.call(e,t)}function z(e,r){var t=L(),s=C(),n=[];if(!r&&e instanceof c)return e;if(!e)return new c(n);if("string"==typeof e){var i=e.trim();if(0<=i.indexOf("<")&&0<=i.indexOf(">")){var a="div";0===i.indexOf("<li")&&(a="ul"),0===i.indexOf("<tr")&&(a="tbody"),0!==i.indexOf("<td")&&0!==i.indexOf("<th")||(a="tr"),0===i.indexOf("<tbody")&&(a="table"),0===i.indexOf("<option")&&(a="select");var o=s.createElement(a);o.innerHTML=i;for(var l=0;l<o.childNodes.length;l+=1)n.push(o.childNodes[l])}else n=function(e,t){if("string"!=typeof e)return[e];for(var n=[],i=(r||s).querySelectorAll(e),a=0;a<i.length;a+=1)n.push(i[a]);return n}(e.trim())}else if(e.nodeType||e===t||e===s)n.push(e);else if(Array.isArray(e)){if(e instanceof c)return e;n=e}return new c(function(e){for(var t=[],n=0;n<e.length;n+=1)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(n))}z.fn=c.prototype;var f,D,A,h={addClass:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=d(t.map(function(e){return e.split(" ")}));return this.forEach(function(e){var t;(t=e.classList).add.apply(t,i)}),this},removeClass:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=d(t.map(function(e){return e.split(" ")}));return this.forEach(function(e){var t;(t=e.classList).remove.apply(t,i)}),this},hasClass:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=d(t.map(function(e){return e.split(" ")}));return 0<p(this,function(t){return 0<i.filter(function(e){return t.classList.contains(e)}).length}).length},toggleClass:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=d(t.map(function(e){return e.split(" ")}));this.forEach(function(t){i.forEach(function(e){t.classList.toggle(e)})})},attr:function(e,t){if(1===arguments.length&&"string"==typeof e)return this[0]?this[0].getAttribute(e):void 0;for(var n=0;n<this.length;n+=1)if(2===arguments.length)this[n].setAttribute(e,t);else for(var i in e)this[n][i]=e[i],this[n].setAttribute(i,e[i]);return this},removeAttr:function(e){for(var t=0;t<this.length;t+=1)this[t].removeAttribute(e);return this},transform:function(e){for(var t=0;t<this.length;t+=1)this[t].style.transform=e;return this},transition:function(e){for(var t=0;t<this.length;t+=1)this[t].style.transitionDuration="string"!=typeof e?e+"ms":e;return this},on:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=t[0],r=t[1],s=t[2],a=t[3];function o(e){var t=e.target;if(t){var n=e.target.dom7EventData||[];if(n.indexOf(e)<0&&n.unshift(e),z(t).is(r))s.apply(t,n);else for(var i=z(t).parents(),a=0;a<i.length;a+=1)z(i[a]).is(r)&&s.apply(i[a],n)}}function l(e){var t=e&&e.target&&e.target.dom7EventData||[];t.indexOf(e)<0&&t.unshift(e),s.apply(this,t)}"function"==typeof t[1]&&(i=t[0],s=t[1],a=t[2],r=void 0),a||(a=!1);for(var u,c=i.split(" "),d=0;d<this.length;d+=1){var p=this[d];if(r)for(u=0;u<c.length;u+=1){var f=c[u];p.dom7LiveListeners||(p.dom7LiveListeners={}),p.dom7LiveListeners[f]||(p.dom7LiveListeners[f]=[]),p.dom7LiveListeners[f].push({listener:s,proxyListener:o}),p.addEventListener(f,o,a)}else for(u=0;u<c.length;u+=1){var h=c[u];p.dom7Listeners||(p.dom7Listeners={}),p.dom7Listeners[h]||(p.dom7Listeners[h]=[]),p.dom7Listeners[h].push({listener:s,proxyListener:l}),p.addEventListener(h,l,a)}}return this},off:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var i=t[0],a=t[1],r=t[2],s=t[3];"function"==typeof t[1]&&(i=t[0],r=t[1],s=t[2],a=void 0),s||(s=!1);for(var o=i.split(" "),l=0;l<o.length;l+=1)for(var u=o[l],c=0;c<this.length;c+=1){var d=this[c],p=void 0;if(!a&&d.dom7Listeners?p=d.dom7Listeners[u]:a&&d.dom7LiveListeners&&(p=d.dom7LiveListeners[u]),p&&p.length)for(var f=p.length-1;0<=f;f-=1){var h=p[f];r&&h.listener===r||r&&h.listener&&h.listener.dom7proxy&&h.listener.dom7proxy===r?(d.removeEventListener(u,h.proxyListener,s),p.splice(f,1)):r||(d.removeEventListener(u,h.proxyListener,s),p.splice(f,1))}}return this},trigger:function(){for(var e=L(),t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];for(var a=n[0].split(" "),r=n[1],s=0;s<a.length;s+=1)for(var o=a[s],l=0;l<this.length;l+=1){var u=this[l];if(e.CustomEvent){var c=new e.CustomEvent(o,{detail:r,bubbles:!0,cancelable:!0});u.dom7EventData=n.filter(function(e,t){return 0<t}),u.dispatchEvent(c),u.dom7EventData=[],delete u.dom7EventData}}return this},transitionEnd:function(n){var i=this;return n&&i.on("transitionend",function e(t){t.target===this&&(n.call(this,t),i.off("transitionend",e))}),this},outerWidth:function(e){if(0<this.length){if(e){var t=this.styles();return this[0].offsetWidth+parseFloat(t.getPropertyValue("margin-right"))+parseFloat(t.getPropertyValue("margin-left"))}return this[0].offsetWidth}return null},outerHeight:function(e){if(0<this.length){if(e){var t=this.styles();return this[0].offsetHeight+parseFloat(t.getPropertyValue("margin-top"))+parseFloat(t.getPropertyValue("margin-bottom"))}return this[0].offsetHeight}return null},styles:function(){var e=L();return this[0]?e.getComputedStyle(this[0],null):{}},offset:function(){if(0<this.length){var e=L(),t=C(),n=this[0],i=n.getBoundingClientRect(),a=t.body,r=n.clientTop||a.clientTop||0,s=n.clientLeft||a.clientLeft||0,o=n===e?e.scrollY:n.scrollTop,l=n===e?e.scrollX:n.scrollLeft;return{top:i.top+o-r,left:i.left+l-s}}return null},css:function(e,t){var n,i=L();if(1===arguments.length){if("string"!=typeof e){for(n=0;n<this.length;n+=1)for(var a in e)this[n].style[a]=e[a];return this}if(this[0])return i.getComputedStyle(this[0],null).getPropertyValue(e)}if(2!==arguments.length||"string"!=typeof e)return this;for(n=0;n<this.length;n+=1)this[n].style[e]=t;return this},each:function(n){return n&&this.forEach(function(e,t){n.apply(e,[e,t])}),this},html:function(e){if(void 0===e)return this[0]?this[0].innerHTML:null;for(var t=0;t<this.length;t+=1)this[t].innerHTML=e;return this},text:function(e){if(void 0===e)return this[0]?this[0].textContent.trim():null;for(var t=0;t<this.length;t+=1)this[t].textContent=e;return this},is:function(e){var t,n,i=L(),a=C(),r=this[0];if(!r||void 0===e)return!1;if("string"==typeof e){if(r.matches)return r.matches(e);if(r.webkitMatchesSelector)return r.webkitMatchesSelector(e);if(r.msMatchesSelector)return r.msMatchesSelector(e);for(t=z(e),n=0;n<t.length;n+=1)if(t[n]===r)return!0;return!1}if(e===a)return r===a;if(e===i)return r===i;if(e.nodeType||e instanceof c){for(t=e.nodeType?[e]:e,n=0;n<t.length;n+=1)if(t[n]===r)return!0;return!1}return!1},index:function(){var e,t=this[0];if(t){for(e=0;null!==(t=t.previousSibling);)1===t.nodeType&&(e+=1);return e}},eq:function(e){if(void 0===e)return this;var t=this.length;if(t-1<e)return z([]);if(e<0){var n=t+e;return z(n<0?[]:[this[n]])}return z([this[e]])},append:function(){for(var e,t=C(),n=0;n<arguments.length;n+=1){e=n<0||arguments.length<=n?void 0:arguments[n];for(var i=0;i<this.length;i+=1)if("string"==typeof e){var a=t.createElement("div");for(a.innerHTML=e;a.firstChild;)this[i].appendChild(a.firstChild)}else if(e instanceof c)for(var r=0;r<e.length;r+=1)this[i].appendChild(e[r]);else this[i].appendChild(e)}return this},prepend:function(e){var t,n,i=C();for(t=0;t<this.length;t+=1)if("string"==typeof e){var a=i.createElement("div");for(a.innerHTML=e,n=a.childNodes.length-1;0<=n;n-=1)this[t].insertBefore(a.childNodes[n],this[t].childNodes[0])}else if(e instanceof c)for(n=0;n<e.length;n+=1)this[t].insertBefore(e[n],this[t].childNodes[0]);else this[t].insertBefore(e,this[t].childNodes[0]);return this},next:function(e){return 0<this.length?e?this[0].nextElementSibling&&z(this[0].nextElementSibling).is(e)?z([this[0].nextElementSibling]):z([]):this[0].nextElementSibling?z([this[0].nextElementSibling]):z([]):z([])},nextAll:function(e){var t=[],n=this[0];if(!n)return z([]);for(;n.nextElementSibling;){var i=n.nextElementSibling;e?z(i).is(e)&&t.push(i):t.push(i),n=i}return z(t)},prev:function(e){if(0<this.length){var t=this[0];return e?t.previousElementSibling&&z(t.previousElementSibling).is(e)?z([t.previousElementSibling]):z([]):t.previousElementSibling?z([t.previousElementSibling]):z([])}return z([])},prevAll:function(e){var t=[],n=this[0];if(!n)return z([]);for(;n.previousElementSibling;){var i=n.previousElementSibling;e?z(i).is(e)&&t.push(i):t.push(i),n=i}return z(t)},parent:function(e){for(var t=[],n=0;n<this.length;n+=1)null!==this[n].parentNode&&(e?z(this[n].parentNode).is(e)&&t.push(this[n].parentNode):t.push(this[n].parentNode));return z(t)},parents:function(e){for(var t=[],n=0;n<this.length;n+=1)for(var i=this[n].parentNode;i;)e?z(i).is(e)&&t.push(i):t.push(i),i=i.parentNode;return z(t)},closest:function(e){var t=this;return void 0===e?z([]):(t.is(e)||(t=t.parents(e).eq(0)),t)},find:function(e){for(var t=[],n=0;n<this.length;n+=1)for(var i=this[n].querySelectorAll(e),a=0;a<i.length;a+=1)t.push(i[a]);return z(t)},children:function(e){for(var t=[],n=0;n<this.length;n+=1)for(var i=this[n].children,a=0;a<i.length;a+=1)e&&!z(i[a]).is(e)||t.push(i[a]);return z(t)},filter:function(e){return z(p(this,e))},remove:function(){for(var e=0;e<this.length;e+=1)this[e].parentNode&&this[e].parentNode.removeChild(this[e]);return this}};function O(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function $(){return Date.now()}function v(e,t){void 0===t&&(t="x");var n,i,a,r,s,o,l=L(),u=(r=e,(o=L()).getComputedStyle&&(s=o.getComputedStyle(r,null)),!s&&r.currentStyle&&(s=r.currentStyle),s||(s=r.style),s);return l.WebKitCSSMatrix?(6<(i=u.transform||u.webkitTransform).split(",").length&&(i=i.split(", ").map(function(e){return e.replace(",",".")}).join(", ")),a=new l.WebKitCSSMatrix("none"===i?"":i)):n=(a=u.MozTransform||u.OTransform||u.MsTransform||u.msTransform||u.transform||u.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,")).toString().split(","),"x"===t&&(i=l.WebKitCSSMatrix?a.m41:16===n.length?parseFloat(n[12]):parseFloat(n[4])),"y"===t&&(i=l.WebKitCSSMatrix?a.m42:16===n.length?parseFloat(n[13]):parseFloat(n[5])),i||0}function m(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function se(){for(var e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"],n=1;n<arguments.length;n+=1){var i=n<0||arguments.length<=n?void 0:arguments[n];if(null!=i&&(u=i,!("undefined"!=typeof window&&void 0!==window.HTMLElement?u instanceof HTMLElement:u&&(1===u.nodeType||11===u.nodeType))))for(var a=Object.keys(Object(i)).filter(function(e){return t.indexOf(e)<0}),r=0,s=a.length;r<s;r+=1){var o=a[r],l=Object.getOwnPropertyDescriptor(i,o);void 0!==l&&l.enumerable&&(m(e[o])&&m(i[o])?i[o].__swiper__?e[o]=i[o]:se(e[o],i[o]):!m(e[o])&&m(i[o])?(e[o]={},i[o].__swiper__?e[o]=i[o]:se(e[o],i[o])):e[o]=i[o])}}var u;return e}function g(n,i){Object.keys(i).forEach(function(t){m(i[t])&&Object.keys(i[t]).forEach(function(e){"function"==typeof i[t][e]&&(i[t][e]=i[t][e].bind(n))}),n[t]=i[t]})}function S(e){return void 0===e&&(e=""),"."+e.trim().replace(/([\.:!\/])/g,"\\$1").replace(/ /g,".")}function y(n,i,e,a){var r=C();return e&&Object.keys(a).forEach(function(e){if(!i[e]&&!0===i.auto){var t=r.createElement("div");t.className=a[e],n.append(t),i[e]=t}}),i}function P(){return f||(n=L(),e=C(),f={touch:!!("ontouchstart"in n||n.DocumentTouch&&e instanceof n.DocumentTouch),pointerEvents:!!n.PointerEvent&&"maxTouchPoints"in n.navigator&&0<=n.navigator.maxTouchPoints,observer:"MutationObserver"in n||"WebkitMutationObserver"in n,passiveListener:function(){var e=!1;try{var t=Object.defineProperty({},"passive",{get:function(){e=!0}});n.addEventListener("testPassiveListener",null,t)}catch(e){}return e}(),gestures:"ongesturestart"in n}),f;var n,e}Object.keys(h).forEach(function(e){Object.defineProperty(z.fn,e,{value:h[e],writable:!0})});var b={name:"resize",create:function(){var s=this;se(s,{resize:{observer:null,createObserver:function(){s&&!s.destroyed&&s.initialized&&(s.resize.observer=new ResizeObserver(function(e){var t=s.width,n=s.height,a=t,r=n;e.forEach(function(e){var t=e.contentBoxSize,n=e.contentRect,i=e.target;i&&i!==s.el||(a=n?n.width:(t[0]||t).inlineSize,r=n?n.height:(t[0]||t).blockSize)}),a===t&&r===n||s.resize.resizeHandler()}),s.resize.observer.observe(s.el))},removeObserver:function(){s.resize.observer&&s.resize.observer.unobserve&&s.el&&(s.resize.observer.unobserve(s.el),s.resize.observer=null)},resizeHandler:function(){s&&!s.destroyed&&s.initialized&&(s.emit("beforeResize"),s.emit("resize"))},orientationChangeHandler:function(){s&&!s.destroyed&&s.initialized&&s.emit("orientationchange")}}})},on:{init:function(e){var t=L();e.params.resizeObserver&&void 0!==L().ResizeObserver?e.resize.createObserver():(t.addEventListener("resize",e.resize.resizeHandler),t.addEventListener("orientationchange",e.resize.orientationChangeHandler))},destroy:function(e){var t=L();e.resize.removeObserver(),t.removeEventListener("resize",e.resize.resizeHandler),t.removeEventListener("orientationchange",e.resize.orientationChangeHandler)}}},w={attach:function(e,t){void 0===t&&(t={});var n=L(),i=this,a=new(n.MutationObserver||n.WebkitMutationObserver)(function(e){if(1!==e.length){var t=function(){i.emit("observerUpdate",e[0])};n.requestAnimationFrame?n.requestAnimationFrame(t):n.setTimeout(t,0)}else i.emit("observerUpdate",e[0])});a.observe(e,{attributes:void 0===t.attributes||t.attributes,childList:void 0===t.childList||t.childList,characterData:void 0===t.characterData||t.characterData}),i.observer.observers.push(a)},init:function(){var e=this;if(e.support.observer&&e.params.observer){if(e.params.observeParents)for(var t=e.$el.parents(),n=0;n<t.length;n+=1)e.observer.attach(t[n]);e.observer.attach(e.$el[0],{childList:e.params.observeSlideChildren}),e.observer.attach(e.$wrapperEl[0],{attributes:!1})}},destroy:function(){this.observer.observers.forEach(function(e){e.disconnect()}),this.observer.observers=[]}},x={name:"observer",params:{observer:!1,observeParents:!1,observeSlideChildren:!1},create:function(){g(this,{observer:e({},w,{observers:[]})})},on:{init:function(e){e.observer.init()},destroy:function(e){e.observer.destroy()}}};function T(){var e=this,t=e.params,n=e.el;if(!n||0!==n.offsetWidth){t.breakpoints&&e.setBreakpoint();var i=e.allowSlideNext,a=e.allowSlidePrev,r=e.snapGrid;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses(),("auto"===t.slidesPerView||1<t.slidesPerView)&&e.isEnd&&!e.isBeginning&&!e.params.centeredSlides?e.slideTo(e.slides.length-1,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.run(),e.allowSlidePrev=a,e.allowSlideNext=i,e.params.watchOverflow&&r!==e.snapGrid&&e.checkOverflow()}}var E=!1;function k(){}var N={init:!0,direction:"horizontal",touchEventsTarget:"container",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!1,nested:!1,createElements:!1,enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,freeMode:!1,freeModeMomentum:!0,freeModeMomentumRatio:1,freeModeMomentumBounce:!0,freeModeMomentumBounceRatio:1,freeModeMomentumVelocityRatio:1,freeModeSticky:!1,freeModeMinimumVelocity:.02,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerColumn:1,slidesPerColumnFill:"column",slidesPerGroup:1,slidesPerGroupSkip:0,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!1,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:0,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,watchSlidesVisibility:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,preloadImages:!0,updateOnImagesReady:!0,loop:!1,loopAdditionalSlides:0,loopedSlides:null,loopFillGroupWithBlank:!1,loopPreventsSlide:!0,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,containerModifierClass:"swiper-container-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-invisible-blank",slideActiveClass:"swiper-slide-active",slideDuplicateActiveClass:"swiper-slide-duplicate-active",slideVisibleClass:"swiper-slide-visible",slideDuplicateClass:"swiper-slide-duplicate",slideNextClass:"swiper-slide-next",slideDuplicateNextClass:"swiper-slide-duplicate-next",slidePrevClass:"swiper-slide-prev",slideDuplicatePrevClass:"swiper-slide-duplicate-prev",wrapperClass:"swiper-wrapper",runCallbacksOnInit:!0,_emitClasses:!1},M={modular:{useParams:function(n){var i=this;i.modules&&Object.keys(i.modules).forEach(function(e){var t=i.modules[e];t.params&&se(n,t.params)})},useModules:function(i){void 0===i&&(i={});var a=this;a.modules&&Object.keys(a.modules).forEach(function(e){var t=a.modules[e],n=i[e]||{};t.on&&a.on&&Object.keys(t.on).forEach(function(e){a.on(e,t.on[e])}),t.create&&t.create.bind(a)(n)})}},eventsEmitter:{on:function(e,t,n){var i=this;if("function"!=typeof t)return i;var a=n?"unshift":"push";return e.split(" ").forEach(function(e){i.eventsListeners[e]||(i.eventsListeners[e]=[]),i.eventsListeners[e][a](t)}),i},once:function(i,a,e){var r=this;if("function"!=typeof a)return r;function s(){r.off(i,s),s.__emitterProxy&&delete s.__emitterProxy;for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];a.apply(r,t)}return s.__emitterProxy=a,r.on(i,s,e)},onAny:function(e,t){if("function"!=typeof e)return this;var n=t?"unshift":"push";return this.eventsAnyListeners.indexOf(e)<0&&this.eventsAnyListeners[n](e),this},offAny:function(e){var t=this;if(!t.eventsAnyListeners)return t;var n=t.eventsAnyListeners.indexOf(e);return 0<=n&&t.eventsAnyListeners.splice(n,1),t},off:function(e,i){var a=this;return a.eventsListeners&&e.split(" ").forEach(function(n){void 0===i?a.eventsListeners[n]=[]:a.eventsListeners[n]&&a.eventsListeners[n].forEach(function(e,t){(e===i||e.__emitterProxy&&e.__emitterProxy===i)&&a.eventsListeners[n].splice(t,1)})}),a},emit:function(){var e,n,i,a=this;if(!a.eventsListeners)return a;for(var t=arguments.length,r=new Array(t),s=0;s<t;s++)r[s]=arguments[s];return i="string"==typeof r[0]||Array.isArray(r[0])?(e=r[0],n=r.slice(1,r.length),a):(e=r[0].events,n=r[0].data,r[0].context||a),n.unshift(i),(Array.isArray(e)?e:e.split(" ")).forEach(function(t){a.eventsAnyListeners&&a.eventsAnyListeners.length&&a.eventsAnyListeners.forEach(function(e){e.apply(i,[t].concat(n))}),a.eventsListeners&&a.eventsListeners[t]&&a.eventsListeners[t].forEach(function(e){e.apply(i,n)})}),a}},update:{updateSize:function(){var e,t,n=this,i=n.$el;e=void 0!==n.params.width&&null!==n.params.width?n.params.width:i[0].clientWidth,t=void 0!==n.params.height&&null!==n.params.height?n.params.height:i[0].clientHeight,0===e&&n.isHorizontal()||0===t&&n.isVertical()||(e=e-parseInt(i.css("padding-left")||0,10)-parseInt(i.css("padding-right")||0,10),t=t-parseInt(i.css("padding-top")||0,10)-parseInt(i.css("padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),se(n,{width:e,height:t,size:n.isHorizontal()?e:t}))},updateSlides:function(){var t=this;function n(e){return t.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}function e(e,t){return parseFloat(e.getPropertyValue(n(t))||0)}var i=t.params,a=t.$wrapperEl,r=t.size,s=t.rtlTranslate,o=t.wrongRTL,l=t.virtual&&i.virtual.enabled,u=l?t.virtual.slides.length:t.slides.length,c=a.children("."+t.params.slideClass),d=l?t.virtual.slides.length:c.length,p=[],f=[],h=[],v=i.slidesOffsetBefore;"function"==typeof v&&(v=i.slidesOffsetBefore.call(t));var m=i.slidesOffsetAfter;"function"==typeof m&&(m=i.slidesOffsetAfter.call(t));var g=t.snapGrid.length,y=t.slidesGrid.length,b=i.spaceBetween,w=-v,x=0,T=0;if(void 0!==r){var E,C;"string"==typeof b&&0<=b.indexOf("%")&&(b=parseFloat(b.replace("%",""))/100*r),t.virtualSize=-b,s?c.css({marginLeft:"",marginBottom:"",marginTop:""}):c.css({marginRight:"",marginBottom:"",marginTop:""}),1<i.slidesPerColumn&&(E=Math.floor(d/i.slidesPerColumn)===d/t.params.slidesPerColumn?d:Math.ceil(d/i.slidesPerColumn)*i.slidesPerColumn,"auto"!==i.slidesPerView&&"row"===i.slidesPerColumnFill&&(E=Math.max(E,i.slidesPerView*i.slidesPerColumn)));for(var S,k,M,L=i.slidesPerColumn,D=E/L,A=Math.floor(d/i.slidesPerColumn),P=0;P<d;P+=1){C=0;var z=c.eq(P);if(1<i.slidesPerColumn){var N=void 0,O=void 0,$=void 0;if("row"===i.slidesPerColumnFill&&1<i.slidesPerGroup){var I=Math.floor(P/(i.slidesPerGroup*i.slidesPerColumn)),j=P-i.slidesPerColumn*i.slidesPerGroup*I,H=0===I?i.slidesPerGroup:Math.min(Math.ceil((d-I*L*i.slidesPerGroup)/L),i.slidesPerGroup);N=(O=j-($=Math.floor(j/H))*H+I*i.slidesPerGroup)+$*E/L,z.css({"-webkit-box-ordinal-group":N,"-moz-box-ordinal-group":N,"-ms-flex-order":N,"-webkit-order":N,order:N})}else"column"===i.slidesPerColumnFill?($=P-(O=Math.floor(P/L))*L,(A<O||O===A&&$===L-1)&&($+=1)>=L&&($=0,O+=1)):O=P-($=Math.floor(P/D))*D;z.css(n("margin-top"),0!==$?i.spaceBetween&&i.spaceBetween+"px":"")}if("none"!==z.css("display")){if("auto"===i.slidesPerView){var R=getComputedStyle(z[0]),q=z[0].style.transform,B=z[0].style.webkitTransform;if(q&&(z[0].style.transform="none"),B&&(z[0].style.webkitTransform="none"),i.roundLengths)C=t.isHorizontal()?z.outerWidth(!0):z.outerHeight(!0);else{var G=e(R,"width"),F=e(R,"padding-left"),W=e(R,"padding-right"),V=e(R,"margin-left"),X=e(R,"margin-right"),_=R.getPropertyValue("box-sizing");if(_&&"border-box"===_)C=G+V+X;else{var Y=z[0],U=Y.clientWidth;C=G+F+W+V+X+(Y.offsetWidth-U)}}q&&(z[0].style.transform=q),B&&(z[0].style.webkitTransform=B),i.roundLengths&&(C=Math.floor(C))}else C=(r-(i.slidesPerView-1)*b)/i.slidesPerView,i.roundLengths&&(C=Math.floor(C)),c[P]&&(c[P].style[n("width")]=C+"px");c[P]&&(c[P].swiperSlideSize=C),h.push(C),i.centeredSlides?(w=w+C/2+x/2+b,0===x&&0!==P&&(w=w-r/2-b),0===P&&(w=w-r/2-b),Math.abs(w)<.001&&(w=0),i.roundLengths&&(w=Math.floor(w)),T%i.slidesPerGroup==0&&p.push(w),f.push(w)):(i.roundLengths&&(w=Math.floor(w)),(T-Math.min(t.params.slidesPerGroupSkip,T))%t.params.slidesPerGroup==0&&p.push(w),f.push(w),w=w+C+b),t.virtualSize+=C+b,x=C,T+=1}}if(t.virtualSize=Math.max(t.virtualSize,r)+m,s&&o&&("slide"===i.effect||"coverflow"===i.effect)&&a.css({width:t.virtualSize+i.spaceBetween+"px"}),i.setWrapperSize&&a.css(((k={})[n("width")]=t.virtualSize+i.spaceBetween+"px",k)),1<i.slidesPerColumn&&(t.virtualSize=(C+i.spaceBetween)*E,t.virtualSize=Math.ceil(t.virtualSize/i.slidesPerColumn)-i.spaceBetween,a.css(((M={})[n("width")]=t.virtualSize+i.spaceBetween+"px",M)),i.centeredSlides)){S=[];for(var K=0;K<p.length;K+=1){var J=p[K];i.roundLengths&&(J=Math.floor(J)),p[K]<t.virtualSize+p[0]&&S.push(J)}p=S}if(!i.centeredSlides){S=[];for(var Z=0;Z<p.length;Z+=1){var Q=p[Z];i.roundLengths&&(Q=Math.floor(Q)),p[Z]<=t.virtualSize-r&&S.push(Q)}p=S,1<Math.floor(t.virtualSize-r)-Math.floor(p[p.length-1])&&p.push(t.virtualSize-r)}if(0===p.length&&(p=[0]),0!==i.spaceBetween){var ee,te=t.isHorizontal()&&s?"marginLeft":n("marginRight");c.filter(function(e,t){return!i.cssMode||t!==c.length-1}).css(((ee={})[te]=b+"px",ee))}if(i.centeredSlides&&i.centeredSlidesBounds){var ne=0;h.forEach(function(e){ne+=e+(i.spaceBetween?i.spaceBetween:0)});var ie=(ne-=i.spaceBetween)-r;p=p.map(function(e){return e<0?-v:ie<e?ie+m:e})}if(i.centerInsufficientSlides){var ae=0;if(h.forEach(function(e){ae+=e+(i.spaceBetween?i.spaceBetween:0)}),(ae-=i.spaceBetween)<r){var re=(r-ae)/2;p.forEach(function(e,t){p[t]=e-re}),f.forEach(function(e,t){f[t]=e+re})}}se(t,{slides:c,snapGrid:p,slidesGrid:f,slidesSizesGrid:h}),d!==u&&t.emit("slidesLengthChange"),p.length!==g&&(t.params.watchOverflow&&t.checkOverflow(),t.emit("snapGridLengthChange")),f.length!==y&&t.emit("slidesGridLengthChange"),(i.watchSlidesProgress||i.watchSlidesVisibility)&&t.updateSlidesOffset()}},updateAutoHeight:function(e){var t,n=this,i=[],a=n.virtual&&n.params.virtual.enabled,r=0;"number"==typeof e?n.setTransition(e):!0===e&&n.setTransition(n.params.speed);var s=function(t){return a?n.slides.filter(function(e){return parseInt(e.getAttribute("data-swiper-slide-index"),10)===t})[0]:n.slides.eq(t)[0]};if("auto"!==n.params.slidesPerView&&1<n.params.slidesPerView)if(n.params.centeredSlides)n.visibleSlides.each(function(e){i.push(e)});else for(t=0;t<Math.ceil(n.params.slidesPerView);t+=1){var o=n.activeIndex+t;if(o>n.slides.length&&!a)break;i.push(s(o))}else i.push(s(n.activeIndex));for(t=0;t<i.length;t+=1)if(void 0!==i[t]){var l=i[t].offsetHeight;r=r<l?l:r}r&&n.$wrapperEl.css("height",r+"px")},updateSlidesOffset:function(){for(var e=this.slides,t=0;t<e.length;t+=1)e[t].swiperSlideOffset=this.isHorizontal()?e[t].offsetLeft:e[t].offsetTop},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);var t=this,n=t.params,i=t.slides,a=t.rtlTranslate;if(0!==i.length){void 0===i[0].swiperSlideOffset&&t.updateSlidesOffset();var r=-e;a&&(r=e),i.removeClass(n.slideVisibleClass),t.visibleSlidesIndexes=[],t.visibleSlides=[];for(var s=0;s<i.length;s+=1){var o=i[s],l=(r+(n.centeredSlides?t.minTranslate():0)-o.swiperSlideOffset)/(o.swiperSlideSize+n.spaceBetween);if(n.watchSlidesVisibility||n.centeredSlides&&n.autoHeight){var u=-(r-o.swiperSlideOffset),c=u+t.slidesSizesGrid[s];(0<=u&&u<t.size-1||1<c&&c<=t.size||u<=0&&c>=t.size)&&(t.visibleSlides.push(o),t.visibleSlidesIndexes.push(s),i.eq(s).addClass(n.slideVisibleClass))}o.progress=a?-l:l}t.visibleSlides=z(t.visibleSlides)}},updateProgress:function(e){var t=this;if(void 0===e){var n=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*n||0}var i=t.params,a=t.maxTranslate()-t.minTranslate(),r=t.progress,s=t.isBeginning,o=t.isEnd,l=s,u=o;o=0===a?s=!(r=0):(s=(r=(e-t.minTranslate())/a)<=0,1<=r),se(t,{progress:r,isBeginning:s,isEnd:o}),(i.watchSlidesProgress||i.watchSlidesVisibility||i.centeredSlides&&i.autoHeight)&&t.updateSlidesProgress(e),s&&!l&&t.emit("reachBeginning toEdge"),o&&!u&&t.emit("reachEnd toEdge"),(l&&!s||u&&!o)&&t.emit("fromEdge"),t.emit("progress",r)},updateSlidesClasses:function(){var e,t=this,n=t.slides,i=t.params,a=t.$wrapperEl,r=t.activeIndex,s=t.realIndex,o=t.virtual&&i.virtual.enabled;n.removeClass(i.slideActiveClass+" "+i.slideNextClass+" "+i.slidePrevClass+" "+i.slideDuplicateActiveClass+" "+i.slideDuplicateNextClass+" "+i.slideDuplicatePrevClass),(e=o?t.$wrapperEl.find("."+i.slideClass+'[data-swiper-slide-index="'+r+'"]'):n.eq(r)).addClass(i.slideActiveClass),i.loop&&(e.hasClass(i.slideDuplicateClass)?a.children("."+i.slideClass+":not(."+i.slideDuplicateClass+')[data-swiper-slide-index="'+s+'"]').addClass(i.slideDuplicateActiveClass):a.children("."+i.slideClass+"."+i.slideDuplicateClass+'[data-swiper-slide-index="'+s+'"]').addClass(i.slideDuplicateActiveClass));var l=e.nextAll("."+i.slideClass).eq(0).addClass(i.slideNextClass);i.loop&&0===l.length&&(l=n.eq(0)).addClass(i.slideNextClass);var u=e.prevAll("."+i.slideClass).eq(0).addClass(i.slidePrevClass);i.loop&&0===u.length&&(u=n.eq(-1)).addClass(i.slidePrevClass),i.loop&&(l.hasClass(i.slideDuplicateClass)?a.children("."+i.slideClass+":not(."+i.slideDuplicateClass+')[data-swiper-slide-index="'+l.attr("data-swiper-slide-index")+'"]').addClass(i.slideDuplicateNextClass):a.children("."+i.slideClass+"."+i.slideDuplicateClass+'[data-swiper-slide-index="'+l.attr("data-swiper-slide-index")+'"]').addClass(i.slideDuplicateNextClass),u.hasClass(i.slideDuplicateClass)?a.children("."+i.slideClass+":not(."+i.slideDuplicateClass+')[data-swiper-slide-index="'+u.attr("data-swiper-slide-index")+'"]').addClass(i.slideDuplicatePrevClass):a.children("."+i.slideClass+"."+i.slideDuplicateClass+'[data-swiper-slide-index="'+u.attr("data-swiper-slide-index")+'"]').addClass(i.slideDuplicatePrevClass)),t.emitSlidesClasses()},updateActiveIndex:function(e){var t,n=this,i=n.rtlTranslate?n.translate:-n.translate,a=n.slidesGrid,r=n.snapGrid,s=n.params,o=n.activeIndex,l=n.realIndex,u=n.snapIndex,c=e;if(void 0===c){for(var d=0;d<a.length;d+=1)void 0!==a[d+1]?i>=a[d]&&i<a[d+1]-(a[d+1]-a[d])/2?c=d:i>=a[d]&&i<a[d+1]&&(c=d+1):i>=a[d]&&(c=d);s.normalizeSlideIndex&&(c<0||void 0===c)&&(c=0)}if(0<=r.indexOf(i))t=r.indexOf(i);else{var p=Math.min(s.slidesPerGroupSkip,c);t=p+Math.floor((c-p)/s.slidesPerGroup)}if(t>=r.length&&(t=r.length-1),c!==o){var f=parseInt(n.slides.eq(c).attr("data-swiper-slide-index")||c,10);se(n,{snapIndex:t,realIndex:f,previousIndex:o,activeIndex:c}),n.emit("activeIndexChange"),n.emit("snapIndexChange"),l!==f&&n.emit("realIndexChange"),(n.initialized||n.params.runCallbacksOnInit)&&n.emit("slideChange")}else t!==u&&(n.snapIndex=t,n.emit("snapIndexChange"))},updateClickedSlide:function(e){var t,n=this,i=n.params,a=z(e.target).closest("."+i.slideClass)[0],r=!1;if(a)for(var s=0;s<n.slides.length;s+=1)if(n.slides[s]===a){r=!0,t=s;break}if(!a||!r)return n.clickedSlide=void 0,void(n.clickedIndex=void 0);n.clickedSlide=a,n.virtual&&n.params.virtual.enabled?n.clickedIndex=parseInt(z(a).attr("data-swiper-slide-index"),10):n.clickedIndex=t,i.slideToClickedSlide&&void 0!==n.clickedIndex&&n.clickedIndex!==n.activeIndex&&n.slideToClickedSlide()}},translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");var t=this.params,n=this.rtlTranslate,i=this.translate,a=this.$wrapperEl;if(t.virtualTranslate)return n?-i:i;if(t.cssMode)return i;var r=v(a[0],e);return n&&(r=-r),r||0},setTranslate:function(e,t){var n=this,i=n.rtlTranslate,a=n.params,r=n.$wrapperEl,s=n.wrapperEl,o=n.progress,l=0,u=0;n.isHorizontal()?l=i?-e:e:u=e,a.roundLengths&&(l=Math.floor(l),u=Math.floor(u)),a.cssMode?s[n.isHorizontal()?"scrollLeft":"scrollTop"]=n.isHorizontal()?-l:-u:a.virtualTranslate||r.transform("translate3d("+l+"px, "+u+"px, 0px)"),n.previousTranslate=n.translate,n.translate=n.isHorizontal()?l:u;var c=n.maxTranslate()-n.minTranslate();(0===c?0:(e-n.minTranslate())/c)!==o&&n.updateProgress(e),n.emit("setTranslate",n.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,n,i,a){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===n&&(n=!0),void 0===i&&(i=!0);var r=this,s=r.params,o=r.wrapperEl;if(r.animating&&s.preventInteractionOnTransition)return!1;var l,u=r.minTranslate(),c=r.maxTranslate();if(l=i&&u<e?u:i&&e<c?c:e,r.updateProgress(l),s.cssMode){var d,p=r.isHorizontal();return 0===t?o[p?"scrollLeft":"scrollTop"]=-l:o.scrollTo?o.scrollTo(((d={})[p?"left":"top"]=-l,d.behavior="smooth",d)):o[p?"scrollLeft":"scrollTop"]=-l,!0}return 0===t?(r.setTransition(0),r.setTranslate(l),n&&(r.emit("beforeTransitionStart",t,a),r.emit("transitionEnd"))):(r.setTransition(t),r.setTranslate(l),n&&(r.emit("beforeTransitionStart",t,a),r.emit("transitionStart")),r.animating||(r.animating=!0,r.onTranslateToWrapperTransitionEnd||(r.onTranslateToWrapperTransitionEnd=function(e){r&&!r.destroyed&&e.target===this&&(r.$wrapperEl[0].removeEventListener("transitionend",r.onTranslateToWrapperTransitionEnd),r.$wrapperEl[0].removeEventListener("webkitTransitionEnd",r.onTranslateToWrapperTransitionEnd),r.onTranslateToWrapperTransitionEnd=null,delete r.onTranslateToWrapperTransitionEnd,n&&r.emit("transitionEnd"))}),r.$wrapperEl[0].addEventListener("transitionend",r.onTranslateToWrapperTransitionEnd),r.$wrapperEl[0].addEventListener("webkitTransitionEnd",r.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){this.params.cssMode||this.$wrapperEl.transition(e),this.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);var n=this,i=n.activeIndex,a=n.params,r=n.previousIndex;if(!a.cssMode){a.autoHeight&&n.updateAutoHeight();var s=t;if(s||(s=r<i?"next":i<r?"prev":"reset"),n.emit("transitionStart"),e&&i!==r){if("reset"===s)return void n.emit("slideResetTransitionStart");n.emit("slideChangeTransitionStart"),"next"===s?n.emit("slideNextTransitionStart"):n.emit("slidePrevTransitionStart")}}},transitionEnd:function(e,t){void 0===e&&(e=!0);var n=this,i=n.activeIndex,a=n.previousIndex,r=n.params;if(n.animating=!1,!r.cssMode){n.setTransition(0);var s=t;if(s||(s=a<i?"next":i<a?"prev":"reset"),n.emit("transitionEnd"),e&&i!==a){if("reset"===s)return void n.emit("slideResetTransitionEnd");n.emit("slideChangeTransitionEnd"),"next"===s?n.emit("slideNextTransitionEnd"):n.emit("slidePrevTransitionEnd")}}}},slide:{slideTo:function(e,t,n,i,a){if(void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===n&&(n=!0),"number"!=typeof e&&"string"!=typeof e)throw new Error("The 'index' argument cannot have type other than 'number' or 'string'. ["+typeof e+"] given.");if("string"==typeof e){var r=parseInt(e,10);if(!isFinite(r))throw new Error("The passed-in 'index' (string) couldn't be converted to 'number'. ["+e+"] given.");e=r}var s=this,o=e;o<0&&(o=0);var l=s.params,u=s.snapGrid,c=s.slidesGrid,d=s.previousIndex,p=s.activeIndex,f=s.rtlTranslate,h=s.wrapperEl,v=s.enabled;if(s.animating&&l.preventInteractionOnTransition||!v&&!i&&!a)return!1;var m=Math.min(s.params.slidesPerGroupSkip,o),g=m+Math.floor((o-m)/s.params.slidesPerGroup);g>=u.length&&(g=u.length-1),(p||l.initialSlide||0)===(d||0)&&n&&s.emit("beforeSlideChangeStart");var y,b=-u[g];if(s.updateProgress(b),l.normalizeSlideIndex)for(var w=0;w<c.length;w+=1){var x=-Math.floor(100*b),T=Math.floor(100*c[w]),E=Math.floor(100*c[w+1]);void 0!==c[w+1]?T<=x&&x<E-(E-T)/2?o=w:T<=x&&x<E&&(o=w+1):T<=x&&(o=w)}if(s.initialized&&o!==p){if(!s.allowSlideNext&&b<s.translate&&b<s.minTranslate())return!1;if(!s.allowSlidePrev&&b>s.translate&&b>s.maxTranslate()&&(p||0)!==o)return!1}if(y=p<o?"next":o<p?"prev":"reset",f&&-b===s.translate||!f&&b===s.translate)return s.updateActiveIndex(o),l.autoHeight&&s.updateAutoHeight(),s.updateSlidesClasses(),"slide"!==l.effect&&s.setTranslate(b),"reset"!==y&&(s.transitionStart(n,y),s.transitionEnd(n,y)),!1;if(l.cssMode){var C,S=s.isHorizontal(),k=-b;return f&&(k=h.scrollWidth-h.offsetWidth-k),0===t?h[S?"scrollLeft":"scrollTop"]=k:h.scrollTo?h.scrollTo(((C={})[S?"left":"top"]=k,C.behavior="smooth",C)):h[S?"scrollLeft":"scrollTop"]=k,!0}return 0===t?(s.setTransition(0),s.setTranslate(b),s.updateActiveIndex(o),s.updateSlidesClasses(),s.emit("beforeTransitionStart",t,i),s.transitionStart(n,y),s.transitionEnd(n,y)):(s.setTransition(t),s.setTranslate(b),s.updateActiveIndex(o),s.updateSlidesClasses(),s.emit("beforeTransitionStart",t,i),s.transitionStart(n,y),s.animating||(s.animating=!0,s.onSlideToWrapperTransitionEnd||(s.onSlideToWrapperTransitionEnd=function(e){s&&!s.destroyed&&e.target===this&&(s.$wrapperEl[0].removeEventListener("transitionend",s.onSlideToWrapperTransitionEnd),s.$wrapperEl[0].removeEventListener("webkitTransitionEnd",s.onSlideToWrapperTransitionEnd),s.onSlideToWrapperTransitionEnd=null,delete s.onSlideToWrapperTransitionEnd,s.transitionEnd(n,y))}),s.$wrapperEl[0].addEventListener("transitionend",s.onSlideToWrapperTransitionEnd),s.$wrapperEl[0].addEventListener("webkitTransitionEnd",s.onSlideToWrapperTransitionEnd))),!0},slideToLoop:function(e,t,n,i){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===n&&(n=!0);var a=e;return this.params.loop&&(a+=this.loopedSlides),this.slideTo(a,t,n,i)},slideNext:function(e,t,n){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0);var i=this,a=i.params,r=i.animating;if(!i.enabled)return i;var s=i.activeIndex<a.slidesPerGroupSkip?1:a.slidesPerGroup;if(a.loop){if(r&&a.loopPreventsSlide)return!1;i.loopFix(),i._clientLeft=i.$wrapperEl[0].clientLeft}return i.slideTo(i.activeIndex+s,e,t,n)},slidePrev:function(e,t,n){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0);var i=this,a=i.params,r=i.animating,s=i.snapGrid,o=i.slidesGrid,l=i.rtlTranslate;if(!i.enabled)return i;if(a.loop){if(r&&a.loopPreventsSlide)return!1;i.loopFix(),i._clientLeft=i.$wrapperEl[0].clientLeft}function u(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}var c,d=u(l?i.translate:-i.translate),p=s.map(function(e){return u(e)}),f=s[p.indexOf(d)-1];return void 0===f&&a.cssMode&&s.forEach(function(e){!f&&e<=d&&(f=e)}),void 0!==f&&(c=o.indexOf(f))<0&&(c=i.activeIndex-1),i.slideTo(c,e,t,n)},slideReset:function(e,t,n){return void 0===e&&(e=this.params.speed),void 0===t&&(t=!0),this.slideTo(this.activeIndex,e,t,n)},slideToClosest:function(e,t,n,i){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0),void 0===i&&(i=.5);var a=this,r=a.activeIndex,s=Math.min(a.params.slidesPerGroupSkip,r),o=s+Math.floor((r-s)/a.params.slidesPerGroup),l=a.rtlTranslate?a.translate:-a.translate;if(l>=a.snapGrid[o]){var u=a.snapGrid[o];l-u>(a.snapGrid[o+1]-u)*i&&(r+=a.params.slidesPerGroup)}else{var c=a.snapGrid[o-1];l-c<=(a.snapGrid[o]-c)*i&&(r-=a.params.slidesPerGroup)}return r=Math.max(r,0),r=Math.min(r,a.slidesGrid.length-1),a.slideTo(r,e,t,n)},slideToClickedSlide:function(){var e,t=this,n=t.params,i=t.$wrapperEl,a="auto"===n.slidesPerView?t.slidesPerViewDynamic():n.slidesPerView,r=t.clickedIndex;if(n.loop){if(t.animating)return;e=parseInt(z(t.clickedSlide).attr("data-swiper-slide-index"),10),n.centeredSlides?r<t.loopedSlides-a/2||r>t.slides.length-t.loopedSlides+a/2?(t.loopFix(),r=i.children("."+n.slideClass+'[data-swiper-slide-index="'+e+'"]:not(.'+n.slideDuplicateClass+")").eq(0).index(),O(function(){t.slideTo(r)})):t.slideTo(r):r>t.slides.length-a?(t.loopFix(),r=i.children("."+n.slideClass+'[data-swiper-slide-index="'+e+'"]:not(.'+n.slideDuplicateClass+")").eq(0).index(),O(function(){t.slideTo(r)})):t.slideTo(r)}else t.slideTo(r)}},loop:{loopCreate:function(){var i=this,e=C(),t=i.params,n=i.$wrapperEl;n.children("."+t.slideClass+"."+t.slideDuplicateClass).remove();var a=n.children("."+t.slideClass);if(t.loopFillGroupWithBlank){var r=t.slidesPerGroup-a.length%t.slidesPerGroup;if(r!==t.slidesPerGroup){for(var s=0;s<r;s+=1){var o=z(e.createElement("div")).addClass(t.slideClass+" "+t.slideBlankClass);n.append(o)}a=n.children("."+t.slideClass)}}"auto"!==t.slidesPerView||t.loopedSlides||(t.loopedSlides=a.length),i.loopedSlides=Math.ceil(parseFloat(t.loopedSlides||t.slidesPerView,10)),i.loopedSlides+=t.loopAdditionalSlides,i.loopedSlides>a.length&&(i.loopedSlides=a.length);var l=[],u=[];a.each(function(e,t){var n=z(e);t<i.loopedSlides&&u.push(e),t<a.length&&t>=a.length-i.loopedSlides&&l.push(e),n.attr("data-swiper-slide-index",t)});for(var c=0;c<u.length;c+=1)n.append(z(u[c].cloneNode(!0)).addClass(t.slideDuplicateClass));for(var d=l.length-1;0<=d;d-=1)n.prepend(z(l[d].cloneNode(!0)).addClass(t.slideDuplicateClass))},loopFix:function(){var e=this;e.emit("beforeLoopFix");var t,n=e.activeIndex,i=e.slides,a=e.loopedSlides,r=e.allowSlidePrev,s=e.allowSlideNext,o=e.snapGrid,l=e.rtlTranslate;e.allowSlidePrev=!0,e.allowSlideNext=!0;var u=-o[n]-e.getTranslate();n<a?(t=i.length-3*a+n,t+=a,e.slideTo(t,0,!1,!0)&&0!==u&&e.setTranslate((l?-e.translate:e.translate)-u)):n>=i.length-a&&(t=-i.length+n+a,t+=a,e.slideTo(t,0,!1,!0)&&0!==u&&e.setTranslate((l?-e.translate:e.translate)-u)),e.allowSlidePrev=r,e.allowSlideNext=s,e.emit("loopFix")},loopDestroy:function(){var e=this.$wrapperEl,t=this.params,n=this.slides;e.children("."+t.slideClass+"."+t.slideDuplicateClass+",."+t.slideClass+"."+t.slideBlankClass).remove(),n.removeAttr("data-swiper-slide-index")}},grabCursor:{setGrabCursor:function(e){var t=this;if(!(t.support.touch||!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)){var n=t.el;n.style.cursor="move",n.style.cursor=e?"-webkit-grabbing":"-webkit-grab",n.style.cursor=e?"-moz-grabbin":"-moz-grab",n.style.cursor=e?"grabbing":"grab"}},unsetGrabCursor:function(){var e=this;e.support.touch||e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.el.style.cursor="")}},manipulation:{appendSlide:function(e){var t=this,n=t.$wrapperEl,i=t.params;if(i.loop&&t.loopDestroy(),"object"==typeof e&&"length"in e)for(var a=0;a<e.length;a+=1)e[a]&&n.append(e[a]);else n.append(e);i.loop&&t.loopCreate(),i.observer&&t.support.observer||t.update()},prependSlide:function(e){var t=this,n=t.params,i=t.$wrapperEl,a=t.activeIndex;n.loop&&t.loopDestroy();var r=a+1;if("object"==typeof e&&"length"in e){for(var s=0;s<e.length;s+=1)e[s]&&i.prepend(e[s]);r=a+e.length}else i.prepend(e);n.loop&&t.loopCreate(),n.observer&&t.support.observer||t.update(),t.slideTo(r,0,!1)},addSlide:function(e,t){var n=this,i=n.$wrapperEl,a=n.params,r=n.activeIndex;a.loop&&(r-=n.loopedSlides,n.loopDestroy(),n.slides=i.children("."+a.slideClass));var s=n.slides.length;if(e<=0)n.prependSlide(t);else if(s<=e)n.appendSlide(t);else{for(var o=e<r?r+1:r,l=[],u=s-1;e<=u;u-=1){var c=n.slides.eq(u);c.remove(),l.unshift(c)}if("object"==typeof t&&"length"in t){for(var d=0;d<t.length;d+=1)t[d]&&i.append(t[d]);o=e<r?r+t.length:r}else i.append(t);for(var p=0;p<l.length;p+=1)i.append(l[p]);a.loop&&n.loopCreate(),a.observer&&n.support.observer||n.update(),a.loop?n.slideTo(o+n.loopedSlides,0,!1):n.slideTo(o,0,!1)}},removeSlide:function(e){var t=this,n=t.params,i=t.$wrapperEl,a=t.activeIndex;n.loop&&(a-=t.loopedSlides,t.loopDestroy(),t.slides=i.children("."+n.slideClass));var r,s=a;if("object"==typeof e&&"length"in e){for(var o=0;o<e.length;o+=1)r=e[o],t.slides[r]&&t.slides.eq(r).remove(),r<s&&(s-=1);s=Math.max(s,0)}else r=e,t.slides[r]&&t.slides.eq(r).remove(),r<s&&(s-=1),s=Math.max(s,0);n.loop&&t.loopCreate(),n.observer&&t.support.observer||t.update(),n.loop?t.slideTo(s+t.loopedSlides,0,!1):t.slideTo(s,0,!1)},removeAllSlides:function(){for(var e=[],t=0;t<this.slides.length;t+=1)e.push(t);this.removeSlide(e)}},events:{attachEvents:function(){var e=this,t=C(),n=e.params,i=e.touchEvents,a=e.el,r=e.wrapperEl,s=e.device,o=e.support;e.onTouchStart=function(e){var t=this,n=C(),i=L(),a=t.touchEventsData,r=t.params,s=t.touches;if(t.enabled&&(!t.animating||!r.preventInteractionOnTransition)){var o=e;o.originalEvent&&(o=o.originalEvent);var l=z(o.target);if(("wrapper"!==r.touchEventsTarget||l.closest(t.wrapperEl).length)&&(a.isTouchEvent="touchstart"===o.type,(a.isTouchEvent||!("which"in o)||3!==o.which)&&!(!a.isTouchEvent&&"button"in o&&0<o.button||a.isTouched&&a.isMoved))){r.noSwipingClass&&""!==r.noSwipingClass&&o.target&&o.target.shadowRoot&&e.path&&e.path[0]&&(l=z(e.path[0]));var u=r.noSwipingSelector?r.noSwipingSelector:"."+r.noSwipingClass,c=!(!o.target||!o.target.shadowRoot);if(r.noSwiping&&(c?function(n,e){return void 0===e&&(e=this),function e(t){return t&&t!==C()&&t!==L()?(t.assignedSlot&&(t=t.assignedSlot),t.closest(n)||e(t.getRootNode().host)):null}(e)}(u,o.target):l.closest(u)[0]))t.allowClick=!0;else if(!r.swipeHandler||l.closest(r.swipeHandler)[0]){s.currentX="touchstart"===o.type?o.targetTouches[0].pageX:o.pageX,s.currentY="touchstart"===o.type?o.targetTouches[0].pageY:o.pageY;var d=s.currentX,p=s.currentY,f=r.edgeSwipeDetection||r.iOSEdgeSwipeDetection,h=r.edgeSwipeThreshold||r.iOSEdgeSwipeThreshold;if(f&&(d<=h||d>=i.innerWidth-h)){if("prevent"!==f)return;e.preventDefault()}if(se(a,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),s.startX=d,s.startY=p,a.touchStartTime=$(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,0<r.threshold&&(a.allowThresholdMove=!1),"touchstart"!==o.type){var v=!0;l.is(a.focusableElements)&&(v=!1),n.activeElement&&z(n.activeElement).is(a.focusableElements)&&n.activeElement!==l[0]&&n.activeElement.blur();var m=v&&t.allowTouchMove&&r.touchStartPreventDefault;!r.touchStartForcePreventDefault&&!m||l[0].isContentEditable||o.preventDefault()}t.emit("touchStart",o)}}}}.bind(e),e.onTouchMove=function(e){var t=C(),n=this,i=n.touchEventsData,a=n.params,r=n.touches,s=n.rtlTranslate;if(n.enabled){var o=e;if(o.originalEvent&&(o=o.originalEvent),i.isTouched){if(!i.isTouchEvent||"touchmove"===o.type){var l="touchmove"===o.type&&o.targetTouches&&(o.targetTouches[0]||o.changedTouches[0]),u="touchmove"===o.type?l.pageX:o.pageX,c="touchmove"===o.type?l.pageY:o.pageY;if(o.preventedByNestedSwiper)return r.startX=u,void(r.startY=c);if(!n.allowTouchMove)return n.allowClick=!1,void(i.isTouched&&(se(r,{startX:u,startY:c,currentX:u,currentY:c}),i.touchStartTime=$()));if(i.isTouchEvent&&a.touchReleaseOnEdges&&!a.loop)if(n.isVertical()){if(c<r.startY&&n.translate<=n.maxTranslate()||c>r.startY&&n.translate>=n.minTranslate())return i.isTouched=!1,void(i.isMoved=!1)}else if(u<r.startX&&n.translate<=n.maxTranslate()||u>r.startX&&n.translate>=n.minTranslate())return;if(i.isTouchEvent&&t.activeElement&&o.target===t.activeElement&&z(o.target).is(i.focusableElements))return i.isMoved=!0,void(n.allowClick=!1);if(i.allowTouchCallbacks&&n.emit("touchMove",o),!(o.targetTouches&&1<o.targetTouches.length)){r.currentX=u,r.currentY=c;var d,p=r.currentX-r.startX,f=r.currentY-r.startY;if(!(n.params.threshold&&Math.sqrt(Math.pow(p,2)+Math.pow(f,2))<n.params.threshold))if(void 0===i.isScrolling&&(n.isHorizontal()&&r.currentY===r.startY||n.isVertical()&&r.currentX===r.startX?i.isScrolling=!1:25<=p*p+f*f&&(d=180*Math.atan2(Math.abs(f),Math.abs(p))/Math.PI,i.isScrolling=n.isHorizontal()?d>a.touchAngle:90-d>a.touchAngle)),i.isScrolling&&n.emit("touchMoveOpposite",o),void 0===i.startMoving&&(r.currentX===r.startX&&r.currentY===r.startY||(i.startMoving=!0)),i.isScrolling)i.isTouched=!1;else if(i.startMoving){n.allowClick=!1,!a.cssMode&&o.cancelable&&o.preventDefault(),a.touchMoveStopPropagation&&!a.nested&&o.stopPropagation(),i.isMoved||(a.loop&&n.loopFix(),i.startTranslate=n.getTranslate(),n.setTransition(0),n.animating&&n.$wrapperEl.trigger("webkitTransitionEnd transitionend"),i.allowMomentumBounce=!1,!a.grabCursor||!0!==n.allowSlideNext&&!0!==n.allowSlidePrev||n.setGrabCursor(!0),n.emit("sliderFirstMove",o)),n.emit("sliderMove",o),i.isMoved=!0;var h=n.isHorizontal()?p:f;r.diff=h,h*=a.touchRatio,s&&(h=-h),n.swipeDirection=0<h?"prev":"next",i.currentTranslate=h+i.startTranslate;var v=!0,m=a.resistanceRatio;if(a.touchReleaseOnEdges&&(m=0),0<h&&i.currentTranslate>n.minTranslate()?(v=!1,a.resistance&&(i.currentTranslate=n.minTranslate()-1+Math.pow(-n.minTranslate()+i.startTranslate+h,m))):h<0&&i.currentTranslate<n.maxTranslate()&&(v=!1,a.resistance&&(i.currentTranslate=n.maxTranslate()+1-Math.pow(n.maxTranslate()-i.startTranslate-h,m))),v&&(o.preventedByNestedSwiper=!0),!n.allowSlideNext&&"next"===n.swipeDirection&&i.currentTranslate<i.startTranslate&&(i.currentTranslate=i.startTranslate),!n.allowSlidePrev&&"prev"===n.swipeDirection&&i.currentTranslate>i.startTranslate&&(i.currentTranslate=i.startTranslate),n.allowSlidePrev||n.allowSlideNext||(i.currentTranslate=i.startTranslate),0<a.threshold){if(!(Math.abs(h)>a.threshold||i.allowThresholdMove))return void(i.currentTranslate=i.startTranslate);if(!i.allowThresholdMove)return i.allowThresholdMove=!0,r.startX=r.currentX,r.startY=r.currentY,i.currentTranslate=i.startTranslate,void(r.diff=n.isHorizontal()?r.currentX-r.startX:r.currentY-r.startY)}a.followFinger&&!a.cssMode&&((a.freeMode||a.watchSlidesProgress||a.watchSlidesVisibility)&&(n.updateActiveIndex(),n.updateSlidesClasses()),a.freeMode&&(0===i.velocities.length&&i.velocities.push({position:r[n.isHorizontal()?"startX":"startY"],time:i.touchStartTime}),i.velocities.push({position:r[n.isHorizontal()?"currentX":"currentY"],time:$()})),n.updateProgress(i.currentTranslate),n.setTranslate(i.currentTranslate))}}}}else i.startMoving&&i.isScrolling&&n.emit("touchMoveOpposite",o)}}.bind(e),e.onTouchEnd=function(e){var t=this,n=t.touchEventsData,i=t.params,a=t.touches,r=t.rtlTranslate,s=t.$wrapperEl,o=t.slidesGrid,l=t.snapGrid;if(t.enabled){var u=e;if(u.originalEvent&&(u=u.originalEvent),n.allowTouchCallbacks&&t.emit("touchEnd",u),n.allowTouchCallbacks=!1,!n.isTouched)return n.isMoved&&i.grabCursor&&t.setGrabCursor(!1),n.isMoved=!1,void(n.startMoving=!1);i.grabCursor&&n.isMoved&&n.isTouched&&(!0===t.allowSlideNext||!0===t.allowSlidePrev)&&t.setGrabCursor(!1);var c,d=$(),p=d-n.touchStartTime;if(t.allowClick&&(t.updateClickedSlide(u),t.emit("tap click",u),p<300&&d-n.lastClickTime<300&&t.emit("doubleTap doubleClick",u)),n.lastClickTime=$(),O(function(){t.destroyed||(t.allowClick=!0)}),!n.isTouched||!n.isMoved||!t.swipeDirection||0===a.diff||n.currentTranslate===n.startTranslate)return n.isTouched=!1,n.isMoved=!1,void(n.startMoving=!1);if(n.isTouched=!1,n.isMoved=!1,n.startMoving=!1,c=i.followFinger?r?t.translate:-t.translate:-n.currentTranslate,!i.cssMode)if(i.freeMode){if(c<-t.minTranslate())return void t.slideTo(t.activeIndex);if(c>-t.maxTranslate())return void(t.slides.length<l.length?t.slideTo(l.length-1):t.slideTo(t.slides.length-1));if(i.freeModeMomentum){if(1<n.velocities.length){var f=n.velocities.pop(),h=n.velocities.pop(),v=f.position-h.position,m=f.time-h.time;t.velocity=v/m,t.velocity/=2,Math.abs(t.velocity)<i.freeModeMinimumVelocity&&(t.velocity=0),(150<m||300<$()-f.time)&&(t.velocity=0)}else t.velocity=0;t.velocity*=i.freeModeMomentumVelocityRatio,n.velocities.length=0;var g=1e3*i.freeModeMomentumRatio,y=t.velocity*g,b=t.translate+y;r&&(b=-b);var w,x,T=!1,E=20*Math.abs(t.velocity)*i.freeModeMomentumBounceRatio;if(b<t.maxTranslate())i.freeModeMomentumBounce?(b+t.maxTranslate()<-E&&(b=t.maxTranslate()-E),w=t.maxTranslate(),T=!0,n.allowMomentumBounce=!0):b=t.maxTranslate(),i.loop&&i.centeredSlides&&(x=!0);else if(b>t.minTranslate())i.freeModeMomentumBounce?(b-t.minTranslate()>E&&(b=t.minTranslate()+E),w=t.minTranslate(),T=!0,n.allowMomentumBounce=!0):b=t.minTranslate(),i.loop&&i.centeredSlides&&(x=!0);else if(i.freeModeSticky){for(var C,S=0;S<l.length;S+=1)if(l[S]>-b){C=S;break}b=-(b=Math.abs(l[C]-b)<Math.abs(l[C-1]-b)||"next"===t.swipeDirection?l[C]:l[C-1])}if(x&&t.once("transitionEnd",function(){t.loopFix()}),0!==t.velocity){if(g=r?Math.abs((-b-t.translate)/t.velocity):Math.abs((b-t.translate)/t.velocity),i.freeModeSticky){var k=Math.abs((r?-b:b)-t.translate),M=t.slidesSizesGrid[t.activeIndex];g=k<M?i.speed:k<2*M?1.5*i.speed:2.5*i.speed}}else if(i.freeModeSticky)return void t.slideToClosest();i.freeModeMomentumBounce&&T?(t.updateProgress(w),t.setTransition(g),t.setTranslate(b),t.transitionStart(!0,t.swipeDirection),t.animating=!0,s.transitionEnd(function(){t&&!t.destroyed&&n.allowMomentumBounce&&(t.emit("momentumBounce"),t.setTransition(i.speed),setTimeout(function(){t.setTranslate(w),s.transitionEnd(function(){t&&!t.destroyed&&t.transitionEnd()})},0))})):t.velocity?(t.updateProgress(b),t.setTransition(g),t.setTranslate(b),t.transitionStart(!0,t.swipeDirection),t.animating||(t.animating=!0,s.transitionEnd(function(){t&&!t.destroyed&&t.transitionEnd()}))):(t.emit("_freeModeNoMomentumRelease"),t.updateProgress(b)),t.updateActiveIndex(),t.updateSlidesClasses()}else{if(i.freeModeSticky)return void t.slideToClosest();i.freeMode&&t.emit("_freeModeNoMomentumRelease")}(!i.freeModeMomentum||p>=i.longSwipesMs)&&(t.updateProgress(),t.updateActiveIndex(),t.updateSlidesClasses())}else{for(var L=0,D=t.slidesSizesGrid[0],A=0;A<o.length;A+=A<i.slidesPerGroupSkip?1:i.slidesPerGroup){var P=A<i.slidesPerGroupSkip-1?1:i.slidesPerGroup;void 0!==o[A+P]?c>=o[A]&&c<o[A+P]&&(D=o[(L=A)+P]-o[A]):c>=o[A]&&(L=A,D=o[o.length-1]-o[o.length-2])}var z=(c-o[L])/D,N=L<i.slidesPerGroupSkip-1?1:i.slidesPerGroup;if(p>i.longSwipesMs){if(!i.longSwipes)return void t.slideTo(t.activeIndex);"next"===t.swipeDirection&&(z>=i.longSwipesRatio?t.slideTo(L+N):t.slideTo(L)),"prev"===t.swipeDirection&&(z>1-i.longSwipesRatio?t.slideTo(L+N):t.slideTo(L))}else{if(!i.shortSwipes)return void t.slideTo(t.activeIndex);!t.navigation||u.target!==t.navigation.nextEl&&u.target!==t.navigation.prevEl?("next"===t.swipeDirection&&t.slideTo(L+N),"prev"===t.swipeDirection&&t.slideTo(L)):u.target===t.navigation.nextEl?t.slideTo(L+N):t.slideTo(L)}}}}.bind(e),n.cssMode&&(e.onScroll=function(){var e=this,t=e.wrapperEl,n=e.rtlTranslate;if(e.enabled){e.previousTranslate=e.translate,e.isHorizontal()?e.translate=n?t.scrollWidth-t.offsetWidth-t.scrollLeft:-t.scrollLeft:e.translate=-t.scrollTop,-0===e.translate&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();var i=e.maxTranslate()-e.minTranslate();(0===i?0:(e.translate-e.minTranslate())/i)!==e.progress&&e.updateProgress(n?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}}.bind(e)),e.onClick=function(e){var t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}.bind(e);var l=!!n.nested;if(!o.touch&&o.pointerEvents)a.addEventListener(i.start,e.onTouchStart,!1),t.addEventListener(i.move,e.onTouchMove,l),t.addEventListener(i.end,e.onTouchEnd,!1);else{if(o.touch){var u=!("touchstart"!==i.start||!o.passiveListener||!n.passiveListeners)&&{passive:!0,capture:!1};a.addEventListener(i.start,e.onTouchStart,u),a.addEventListener(i.move,e.onTouchMove,o.passiveListener?{passive:!1,capture:l}:l),a.addEventListener(i.end,e.onTouchEnd,u),i.cancel&&a.addEventListener(i.cancel,e.onTouchEnd,u),E||(t.addEventListener("touchstart",k),E=!0)}(n.simulateTouch&&!s.ios&&!s.android||n.simulateTouch&&!o.touch&&s.ios)&&(a.addEventListener("mousedown",e.onTouchStart,!1),t.addEventListener("mousemove",e.onTouchMove,l),t.addEventListener("mouseup",e.onTouchEnd,!1))}(n.preventClicks||n.preventClicksPropagation)&&a.addEventListener("click",e.onClick,!0),n.cssMode&&r.addEventListener("scroll",e.onScroll),n.updateOnWindowResize?e.on(s.ios||s.android?"resize orientationchange observerUpdate":"resize observerUpdate",T,!0):e.on("observerUpdate",T,!0)},detachEvents:function(){var e=this,t=C(),n=e.params,i=e.touchEvents,a=e.el,r=e.wrapperEl,s=e.device,o=e.support,l=!!n.nested;if(!o.touch&&o.pointerEvents)a.removeEventListener(i.start,e.onTouchStart,!1),t.removeEventListener(i.move,e.onTouchMove,l),t.removeEventListener(i.end,e.onTouchEnd,!1);else{if(o.touch){var u=!("onTouchStart"!==i.start||!o.passiveListener||!n.passiveListeners)&&{passive:!0,capture:!1};a.removeEventListener(i.start,e.onTouchStart,u),a.removeEventListener(i.move,e.onTouchMove,l),a.removeEventListener(i.end,e.onTouchEnd,u),i.cancel&&a.removeEventListener(i.cancel,e.onTouchEnd,u)}(n.simulateTouch&&!s.ios&&!s.android||n.simulateTouch&&!o.touch&&s.ios)&&(a.removeEventListener("mousedown",e.onTouchStart,!1),t.removeEventListener("mousemove",e.onTouchMove,l),t.removeEventListener("mouseup",e.onTouchEnd,!1))}(n.preventClicks||n.preventClicksPropagation)&&a.removeEventListener("click",e.onClick,!0),n.cssMode&&r.removeEventListener("scroll",e.onScroll),e.off(s.ios||s.android?"resize orientationchange observerUpdate":"resize observerUpdate",T)}},breakpoints:{setBreakpoint:function(){var e=this,t=e.activeIndex,n=e.initialized,i=e.loopedSlides,a=void 0===i?0:i,r=e.params,s=e.$el,o=r.breakpoints;if(o&&(!o||0!==Object.keys(o).length)){var l=e.getBreakpoint(o,e.params.breakpointsBase,e.el);if(l&&e.currentBreakpoint!==l){var u=l in o?o[l]:void 0;u&&["slidesPerView","spaceBetween","slidesPerGroup","slidesPerGroupSkip","slidesPerColumn"].forEach(function(e){var t=u[e];void 0!==t&&(u[e]="slidesPerView"!==e||"AUTO"!==t&&"auto"!==t?"slidesPerView"===e?parseFloat(t):parseInt(t,10):"auto")});var c=u||e.originalParams,d=1<r.slidesPerColumn,p=1<c.slidesPerColumn,f=r.enabled;d&&!p?(s.removeClass(r.containerModifierClass+"multirow "+r.containerModifierClass+"multirow-column"),e.emitContainerClasses()):!d&&p&&(s.addClass(r.containerModifierClass+"multirow"),(c.slidesPerColumnFill&&"column"===c.slidesPerColumnFill||!c.slidesPerColumnFill&&"column"===r.slidesPerColumnFill)&&s.addClass(r.containerModifierClass+"multirow-column"),e.emitContainerClasses());var h=c.direction&&c.direction!==r.direction,v=r.loop&&(c.slidesPerView!==r.slidesPerView||h);h&&n&&e.changeDirection(),se(e.params,c);var m=e.params.enabled;se(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),f&&!m?e.disable():!f&&m&&e.enable(),e.currentBreakpoint=l,e.emit("_beforeBreakpoint",c),v&&n&&(e.loopDestroy(),e.loopCreate(),e.updateSlides(),e.slideTo(t-a+e.loopedSlides,0,!1)),e.emit("breakpoint",c)}}},getBreakpoint:function(e,t,n){if(void 0===t&&(t="window"),e&&("container"!==t||n)){var i=!1,a=L(),r="window"===t?a.innerHeight:n.clientHeight,s=Object.keys(e).map(function(e){if("string"!=typeof e||0!==e.indexOf("@"))return{value:e,point:e};var t=parseFloat(e.substr(1));return{value:r*t,point:e}});s.sort(function(e,t){return parseInt(e.value,10)-parseInt(t.value,10)});for(var o=0;o<s.length;o+=1){var l=s[o],u=l.point,c=l.value;"window"===t?a.matchMedia("(min-width: "+c+"px)").matches&&(i=u):c<=n.clientWidth&&(i=u)}return i||"max"}}},checkOverflow:{checkOverflow:function(){var e=this,t=e.params,n=e.isLocked,i=0<e.slides.length&&t.slidesOffsetBefore+t.spaceBetween*(e.slides.length-1)+e.slides[0].offsetWidth*e.slides.length;t.slidesOffsetBefore&&t.slidesOffsetAfter&&i?e.isLocked=i<=e.size:e.isLocked=1===e.snapGrid.length,e.allowSlideNext=!e.isLocked,e.allowSlidePrev=!e.isLocked,n!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock"),n&&n!==e.isLocked&&(e.isEnd=!1,e.navigation&&e.navigation.update())}},classes:{addClasses:function(){var e,n,i,t=this,a=t.classNames,r=t.params,s=t.rtl,o=t.$el,l=t.device,u=t.support,c=(e=["initialized",r.direction,{"pointer-events":u.pointerEvents&&!u.touch},{"free-mode":r.freeMode},{autoheight:r.autoHeight},{rtl:s},{multirow:1<r.slidesPerColumn},{"multirow-column":1<r.slidesPerColumn&&"column"===r.slidesPerColumnFill},{android:l.android},{ios:l.ios},{"css-mode":r.cssMode}],n=r.containerModifierClass,i=[],e.forEach(function(t){"object"==typeof t?Object.keys(t).forEach(function(e){t[e]&&i.push(n+e)}):"string"==typeof t&&i.push(n+t)}),i);a.push.apply(a,c),o.addClass([].concat(a).join(" ")),t.emitContainerClasses()},removeClasses:function(){var e=this.$el,t=this.classNames;e.removeClass(t.join(" ")),this.emitContainerClasses()}},images:{loadImage:function(e,t,n,i,a,r){var s,o=L();function l(){r&&r()}z(e).parent("picture")[0]||e.complete&&a?l():t?((s=new o.Image).onload=l,s.onerror=l,i&&(s.sizes=i),n&&(s.srcset=n),t&&(s.src=t)):l()},preloadImages:function(){var e=this;function t(){null!=e&&e&&!e.destroyed&&(void 0!==e.imagesLoaded&&(e.imagesLoaded+=1),e.imagesLoaded===e.imagesToLoad.length&&(e.params.updateOnImagesReady&&e.update(),e.emit("imagesReady")))}e.imagesToLoad=e.$el.find("img");for(var n=0;n<e.imagesToLoad.length;n+=1){var i=e.imagesToLoad[n];e.loadImage(i,i.currentSrc||i.getAttribute("src"),i.srcset||i.getAttribute("srcset"),i.sizes||i.getAttribute("sizes"),!0,t)}}}},I={},j=function(){function M(){for(var e,a,t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];if((a=1===n.length&&n[0].constructor&&"Object"===Object.prototype.toString.call(n[0]).slice(8,-1)?n[0]:(e=n[0],n[1]))||(a={}),a=se({},a),e&&!a.el&&(a.el=e),a.el&&1<z(a.el).length){var r=[];return z(a.el).each(function(e){var t=se({},a,{el:e});r.push(new M(t))}),r}var s,o,l,u,c,d,p,f,h,v,m,g,y,b,w,x,T,E=this;E.__swiper__=!0,E.support=P(),E.device=(void 0===(l={userAgent:a.userAgent})&&(l={}),D||(u=(void 0===l?{}:l).userAgent,c=P(),d=L(),p=d.navigator.platform,f=u||d.navigator.userAgent,h={ios:!1,android:!1},v=d.screen.width,m=d.screen.height,g=f.match(/(Android);?[\s\/]+([\d.]+)?/),y=f.match(/(iPad).*OS\s([\d_]+)/),b=f.match(/(iPod)(.*OS\s([\d_]+))?/),w=!y&&f.match(/(iPhone\sOS|iOS)\s([\d_]+)/),x="Win32"===p,T="MacIntel"===p,!y&&T&&c.touch&&0<=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(v+"x"+m)&&((y=f.match(/(Version)\/([\d.]+)/))||(y=[0,1,"13_0_0"]),T=!1),g&&!x&&(h.os="android",h.android=!0),(y||w||b)&&(h.os="ios",h.ios=!0),D=h),D),E.browser=(A||(o=L(),A={isEdge:!!o.navigator.userAgent.match(/Edge/g),isSafari:(s=o.navigator.userAgent.toLowerCase(),0<=s.indexOf("safari")&&s.indexOf("chrome")<0&&s.indexOf("android")<0),isWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(o.navigator.userAgent)}),A),E.eventsListeners={},E.eventsAnyListeners=[],void 0===E.modules&&(E.modules={}),Object.keys(E.modules).forEach(function(e){var t=E.modules[e];if(t.params){var n=Object.keys(t.params)[0],i=t.params[n];if("object"!=typeof i||null===i)return;if(0<=["navigation","pagination","scrollbar"].indexOf(n)&&!0===a[n]&&(a[n]={auto:!0}),!(n in a&&"enabled"in i))return;!0===a[n]&&(a[n]={enabled:!0}),"object"!=typeof a[n]||"enabled"in a[n]||(a[n].enabled=!0),a[n]||(a[n]={enabled:!1})}});var C,S,k=se({},N);return E.useParams(k),E.params=se({},k,I,a),E.originalParams=se({},E.params),E.passedParams=se({},a),E.params&&E.params.on&&Object.keys(E.params.on).forEach(function(e){E.on(e,E.params.on[e])}),E.params&&E.params.onAny&&E.onAny(E.params.onAny),E.$=z,se(E,{enabled:E.params.enabled,el:e,classNames:[],slides:z(),slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:function(){return"horizontal"===E.params.direction},isVertical:function(){return"vertical"===E.params.direction},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,allowSlideNext:E.params.allowSlideNext,allowSlidePrev:E.params.allowSlidePrev,touchEvents:(C=["touchstart","touchmove","touchend","touchcancel"],S=["mousedown","mousemove","mouseup"],E.support.pointerEvents&&(S=["pointerdown","pointermove","pointerup"]),E.touchEventsTouch={start:C[0],move:C[1],end:C[2],cancel:C[3]},E.touchEventsDesktop={start:S[0],move:S[1],end:S[2]},E.support.touch||!E.params.simulateTouch?E.touchEventsTouch:E.touchEventsDesktop),touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:E.params.focusableElements,lastClickTime:$(),clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,isTouchEvent:void 0,startMoving:void 0},allowClick:!0,allowTouchMove:E.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),E.useModules(),E.emit("_swiper"),E.params.init&&E.init(),E}var e,t,n=M.prototype;return n.enable=function(){var e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))},n.disable=function(){var e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))},n.setProgress=function(e,t){var n=this;e=Math.min(Math.max(e,0),1);var i=n.minTranslate(),a=(n.maxTranslate()-i)*e+i;n.translateTo(a,void 0===t?0:t),n.updateActiveIndex(),n.updateSlidesClasses()},n.emitContainerClasses=function(){var t=this;if(t.params._emitClasses&&t.el){var e=t.el.className.split(" ").filter(function(e){return 0===e.indexOf("swiper-container")||0===e.indexOf(t.params.containerModifierClass)});t.emit("_containerClasses",e.join(" "))}},n.getSlideClasses=function(e){var t=this;return e.className.split(" ").filter(function(e){return 0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)}).join(" ")},n.emitSlidesClasses=function(){var n=this;if(n.params._emitClasses&&n.el){var i=[];n.slides.each(function(e){var t=n.getSlideClasses(e);i.push({slideEl:e,classNames:t}),n.emit("_slideClass",e,t)}),n.emit("_slideClasses",i)}},n.slidesPerViewDynamic=function(){var e=this,t=e.params,n=e.slides,i=e.slidesGrid,a=e.size,r=e.activeIndex,s=1;if(t.centeredSlides){for(var o,l=n[r].swiperSlideSize,u=r+1;u<n.length;u+=1)n[u]&&!o&&(s+=1,(l+=n[u].swiperSlideSize)>a&&(o=!0));for(var c=r-1;0<=c;c-=1)n[c]&&!o&&(s+=1,(l+=n[c].swiperSlideSize)>a&&(o=!0))}else for(var d=r+1;d<n.length;d+=1)i[d]-i[r]<a&&(s+=1);return s},n.update=function(){var n=this;if(n&&!n.destroyed){var e=n.snapGrid,t=n.params;t.breakpoints&&n.setBreakpoint(),n.updateSize(),n.updateSlides(),n.updateProgress(),n.updateSlidesClasses(),n.params.freeMode?(i(),n.params.autoHeight&&n.updateAutoHeight()):(("auto"===n.params.slidesPerView||1<n.params.slidesPerView)&&n.isEnd&&!n.params.centeredSlides?n.slideTo(n.slides.length-1,0,!1,!0):n.slideTo(n.activeIndex,0,!1,!0))||i(),t.watchOverflow&&e!==n.snapGrid&&n.checkOverflow(),n.emit("update")}function i(){var e=n.rtlTranslate?-1*n.translate:n.translate,t=Math.min(Math.max(e,n.maxTranslate()),n.minTranslate());n.setTranslate(t),n.updateActiveIndex(),n.updateSlidesClasses()}},n.changeDirection=function(t,e){void 0===e&&(e=!0);var n=this,i=n.params.direction;return t||(t="horizontal"===i?"vertical":"horizontal"),t===i||"horizontal"!==t&&"vertical"!==t||(n.$el.removeClass(""+n.params.containerModifierClass+i).addClass(""+n.params.containerModifierClass+t),n.emitContainerClasses(),n.params.direction=t,n.slides.each(function(e){"vertical"===t?e.style.width="":e.style.height=""}),n.emit("changeDirection"),e&&n.update()),n},n.mount=function(t){var e=this;if(e.mounted)return!0;var n=z(t||e.params.el);if(!(t=n[0]))return!1;t.swiper=e;var i=function(){return"."+(e.params.wrapperClass||"").trim().split(" ").join(".")},a=function(){if(t&&t.shadowRoot&&t.shadowRoot.querySelector){var e=z(t.shadowRoot.querySelector(i()));return e.children=function(e){return n.children(e)},e}return n.children(i())}();if(0===a.length&&e.params.createElements){var r=C().createElement("div");a=z(r),r.className=e.params.wrapperClass,n.append(r),n.children("."+e.params.slideClass).each(function(e){a.append(e)})}return se(e,{$el:n,el:t,$wrapperEl:a,wrapperEl:a[0],mounted:!0,rtl:"rtl"===t.dir.toLowerCase()||"rtl"===n.css("direction"),rtlTranslate:"horizontal"===e.params.direction&&("rtl"===t.dir.toLowerCase()||"rtl"===n.css("direction")),wrongRTL:"-webkit-box"===a.css("display")}),!0},n.init=function(e){var t=this;return t.initialized||!1===t.mount(e)||(t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.params.loop&&t.loopCreate(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.preloadImages&&t.preloadImages(),t.params.loop?t.slideTo(t.params.initialSlide+t.loopedSlides,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.attachEvents(),t.initialized=!0,t.emit("init"),t.emit("afterInit")),t},n.destroy=function(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);var n,i=this,a=i.params,r=i.$el,s=i.$wrapperEl,o=i.slides;return void 0===i.params||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),a.loop&&i.loopDestroy(),t&&(i.removeClasses(),r.removeAttr("style"),s.removeAttr("style"),o&&o.length&&o.removeClass([a.slideVisibleClass,a.slideActiveClass,a.slideNextClass,a.slidePrevClass].join(" ")).removeAttr("style").removeAttr("data-swiper-slide-index")),i.emit("destroy"),Object.keys(i.eventsListeners).forEach(function(e){i.off(e)}),!1!==e&&(i.$el[0].swiper=null,n=i,Object.keys(n).forEach(function(e){try{n[e]=null}catch(e){}try{delete n[e]}catch(e){}})),i.destroyed=!0),null},M.extendDefaults=function(e){se(I,e)},M.installModule=function(e){M.prototype.modules||(M.prototype.modules={});var t=e.name||Object.keys(M.prototype.modules).length+"_"+$();M.prototype.modules[t]=e},M.use=function(e){return Array.isArray(e)?e.forEach(function(e){return M.installModule(e)}):M.installModule(e),M},e=M,t=[{key:"extendedDefaults",get:function(){return I}},{key:"defaults",get:function(){return N}}],null&&i(e.prototype,null),i(e,t),M}();Object.keys(M).forEach(function(t){Object.keys(M[t]).forEach(function(e){j.prototype[e]=M[t][e]})}),j.use([b,x]);var H={update:function(e){var t=this,n=t.params,i=n.slidesPerView,a=n.slidesPerGroup,r=n.centeredSlides,s=t.params.virtual,o=s.addSlidesBefore,l=s.addSlidesAfter,u=t.virtual,c=u.from,d=u.to,p=u.slides,f=u.slidesGrid,h=u.renderSlide,v=u.offset;t.updateActiveIndex();var m,g,y,b=t.activeIndex||0;m=t.rtlTranslate?"right":t.isHorizontal()?"left":"top",y=r?(g=Math.floor(i/2)+a+l,Math.floor(i/2)+a+o):(g=i+(a-1)+l,a+o);var w=Math.max((b||0)-y,0),x=Math.min((b||0)+g,p.length-1),T=(t.slidesGrid[w]||0)-(t.slidesGrid[0]||0);function E(){t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),t.lazy&&t.params.lazy.enabled&&t.lazy.load()}if(se(t.virtual,{from:w,to:x,offset:T,slidesGrid:t.slidesGrid}),c===w&&d===x&&!e)return t.slidesGrid!==f&&T!==v&&t.slides.css(m,T+"px"),void t.updateProgress();if(t.params.virtual.renderExternal)return t.params.virtual.renderExternal.call(t,{offset:T,from:w,to:x,slides:function(){for(var e=[],t=w;t<=x;t+=1)e.push(p[t]);return e}()}),void(t.params.virtual.renderExternalUpdate&&E());var C=[],S=[];if(e)t.$wrapperEl.find("."+t.params.slideClass).remove();else for(var k=c;k<=d;k+=1)(k<w||x<k)&&t.$wrapperEl.find("."+t.params.slideClass+'[data-swiper-slide-index="'+k+'"]').remove();for(var M=0;M<p.length;M+=1)w<=M&&M<=x&&(void 0===d||e?S.push(M):(d<M&&S.push(M),M<c&&C.push(M)));S.forEach(function(e){t.$wrapperEl.append(h(p[e],e))}),C.sort(function(e,t){return t-e}).forEach(function(e){t.$wrapperEl.prepend(h(p[e],e))}),t.$wrapperEl.children(".swiper-slide").css(m,T+"px"),E()},renderSlide:function(e,t){var n=this,i=n.params.virtual;if(i.cache&&n.virtual.cache[t])return n.virtual.cache[t];var a=i.renderSlide?z(i.renderSlide.call(n,e,t)):z('<div class="'+n.params.slideClass+'" data-swiper-slide-index="'+t+'">'+e+"</div>");return a.attr("data-swiper-slide-index")||a.attr("data-swiper-slide-index",t),i.cache&&(n.virtual.cache[t]=a),a},appendSlide:function(e){if("object"==typeof e&&"length"in e)for(var t=0;t<e.length;t+=1)e[t]&&this.virtual.slides.push(e[t]);else this.virtual.slides.push(e);this.virtual.update(!0)},prependSlide:function(e){var t=this,n=t.activeIndex,i=n+1,a=1;if(Array.isArray(e)){for(var r=0;r<e.length;r+=1)e[r]&&t.virtual.slides.unshift(e[r]);i=n+e.length,a=e.length}else t.virtual.slides.unshift(e);if(t.params.virtual.cache){var s=t.virtual.cache,o={};Object.keys(s).forEach(function(e){var t=s[e],n=t.attr("data-swiper-slide-index");n&&t.attr("data-swiper-slide-index",parseInt(n,10)+1),o[parseInt(e,10)+a]=t}),t.virtual.cache=o}t.virtual.update(!0),t.slideTo(i,0)},removeSlide:function(e){var t=this;if(null!=e){var n=t.activeIndex;if(Array.isArray(e))for(var i=e.length-1;0<=i;i-=1)t.virtual.slides.splice(e[i],1),t.params.virtual.cache&&delete t.virtual.cache[e[i]],e[i]<n&&(n-=1),n=Math.max(n,0);else t.virtual.slides.splice(e,1),t.params.virtual.cache&&delete t.virtual.cache[e],e<n&&(n-=1),n=Math.max(n,0);t.virtual.update(!0),t.slideTo(n,0)}},removeAllSlides:function(){var e=this;e.virtual.slides=[],e.params.virtual.cache&&(e.virtual.cache={}),e.virtual.update(!0),e.slideTo(0,0)}},R={name:"virtual",params:{virtual:{enabled:!1,slides:[],cache:!0,renderSlide:null,renderExternal:null,renderExternalUpdate:!0,addSlidesBefore:0,addSlidesAfter:0}},create:function(){g(this,{virtual:e({},H,{slides:this.params.virtual.slides,cache:{}})})},on:{beforeInit:function(e){if(e.params.virtual.enabled){e.classNames.push(e.params.containerModifierClass+"virtual");var t={watchSlidesProgress:!0};se(e.params,t),se(e.originalParams,t),e.params.initialSlide||e.virtual.update()}},setTranslate:function(e){e.params.virtual.enabled&&e.virtual.update()}}},q={handle:function(e){var t=this;if(t.enabled){var n=L(),i=C(),a=t.rtlTranslate,r=e;r.originalEvent&&(r=r.originalEvent);var s=r.keyCode||r.charCode,o=t.params.keyboard.pageUpDown,l=o&&33===s,u=o&&34===s,c=37===s,d=39===s,p=38===s,f=40===s;if(!t.allowSlideNext&&(t.isHorizontal()&&d||t.isVertical()&&f||u))return!1;if(!t.allowSlidePrev&&(t.isHorizontal()&&c||t.isVertical()&&p||l))return!1;if(!(r.shiftKey||r.altKey||r.ctrlKey||r.metaKey||i.activeElement&&i.activeElement.nodeName&&("input"===i.activeElement.nodeName.toLowerCase()||"textarea"===i.activeElement.nodeName.toLowerCase()))){if(t.params.keyboard.onlyInViewport&&(l||u||c||d||p||f)){var h=!1;if(0<t.$el.parents("."+t.params.slideClass).length&&0===t.$el.parents("."+t.params.slideActiveClass).length)return;var v=t.$el,m=v[0].clientWidth,g=v[0].clientHeight,y=n.innerWidth,b=n.innerHeight,w=t.$el.offset();a&&(w.left-=t.$el[0].scrollLeft);for(var x=[[w.left,w.top],[w.left+m,w.top],[w.left,w.top+g],[w.left+m,w.top+g]],T=0;T<x.length;T+=1){var E=x[T];if(0<=E[0]&&E[0]<=y&&0<=E[1]&&E[1]<=b){if(0===E[0]&&0===E[1])continue;h=!0}}if(!h)return}t.isHorizontal()?((l||u||c||d)&&(r.preventDefault?r.preventDefault():r.returnValue=!1),((u||d)&&!a||(l||c)&&a)&&t.slideNext(),((l||c)&&!a||(u||d)&&a)&&t.slidePrev()):((l||u||p||f)&&(r.preventDefault?r.preventDefault():r.returnValue=!1),(u||f)&&t.slideNext(),(l||p)&&t.slidePrev()),t.emit("keyPress",s)}}},enable:function(){var e=C();this.keyboard.enabled||(z(e).on("keydown",this.keyboard.handle),this.keyboard.enabled=!0)},disable:function(){var e=C();this.keyboard.enabled&&(z(e).off("keydown",this.keyboard.handle),this.keyboard.enabled=!1)}},B={name:"keyboard",params:{keyboard:{enabled:!1,onlyInViewport:!0,pageUpDown:!0}},create:function(){g(this,{keyboard:e({enabled:!1},q)})},on:{init:function(e){e.params.keyboard.enabled&&e.keyboard.enable()},destroy:function(e){e.keyboard.enabled&&e.keyboard.disable()}}},G={lastScrollTime:$(),lastEventBeforeSnap:void 0,recentWheelEvents:[],event:function(){return-1<L().navigator.userAgent.indexOf("firefox")?"DOMMouseScroll":function(){var e=C(),t="onwheel",n=t in e;if(!n){var i=e.createElement("div");i.setAttribute(t,"return;"),n="function"==typeof i.onwheel}return!n&&e.implementation&&e.implementation.hasFeature&&!0!==e.implementation.hasFeature("","")&&(n=e.implementation.hasFeature("Events.wheel","3.0")),n}()?"wheel":"mousewheel"},normalize:function(e){var t=0,n=0,i=0,a=0;return"detail"in e&&(n=e.detail),"wheelDelta"in e&&(n=-e.wheelDelta/120),"wheelDeltaY"in e&&(n=-e.wheelDeltaY/120),"wheelDeltaX"in e&&(t=-e.wheelDeltaX/120),"axis"in e&&e.axis===e.HORIZONTAL_AXIS&&(t=n,n=0),i=10*t,a=10*n,"deltaY"in e&&(a=e.deltaY),"deltaX"in e&&(i=e.deltaX),e.shiftKey&&!i&&(i=a,a=0),(i||a)&&e.deltaMode&&(1===e.deltaMode?(i*=40,a*=40):(i*=800,a*=800)),i&&!t&&(t=i<1?-1:1),a&&!n&&(n=a<1?-1:1),{spinX:t,spinY:n,pixelX:i,pixelY:a}},handleMouseEnter:function(){this.enabled&&(this.mouseEntered=!0)},handleMouseLeave:function(){this.enabled&&(this.mouseEntered=!1)},handle:function(e){var t=e,n=this;if(n.enabled){var i=n.params.mousewheel;n.params.cssMode&&t.preventDefault();var a=n.$el;if("container"!==n.params.mousewheel.eventsTarget&&(a=z(n.params.mousewheel.eventsTarget)),!n.mouseEntered&&!a[0].contains(t.target)&&!i.releaseOnEdges)return!0;t.originalEvent&&(t=t.originalEvent);var r=0,s=n.rtlTranslate?-1:1,o=G.normalize(t);if(i.forceToAxis)if(n.isHorizontal()){if(!(Math.abs(o.pixelX)>Math.abs(o.pixelY)))return!0;r=-o.pixelX*s}else{if(!(Math.abs(o.pixelY)>Math.abs(o.pixelX)))return!0;r=-o.pixelY}else r=Math.abs(o.pixelX)>Math.abs(o.pixelY)?-o.pixelX*s:-o.pixelY;if(0===r)return!0;i.invert&&(r=-r);var l=n.getTranslate()+r*i.sensitivity;if(l>=n.minTranslate()&&(l=n.minTranslate()),l<=n.maxTranslate()&&(l=n.maxTranslate()),(!!n.params.loop||!(l===n.minTranslate()||l===n.maxTranslate()))&&n.params.nested&&t.stopPropagation(),n.params.freeMode){var u={time:$(),delta:Math.abs(r),direction:Math.sign(r)},c=n.mousewheel.lastEventBeforeSnap,d=c&&u.time<c.time+500&&u.delta<=c.delta&&u.direction===c.direction;if(!d){n.mousewheel.lastEventBeforeSnap=void 0,n.params.loop&&n.loopFix();var p=n.getTranslate()+r*i.sensitivity,f=n.isBeginning,h=n.isEnd;if(p>=n.minTranslate()&&(p=n.minTranslate()),p<=n.maxTranslate()&&(p=n.maxTranslate()),n.setTransition(0),n.setTranslate(p),n.updateProgress(),n.updateActiveIndex(),n.updateSlidesClasses(),(!f&&n.isBeginning||!h&&n.isEnd)&&n.updateSlidesClasses(),n.params.freeModeSticky){clearTimeout(n.mousewheel.timeout),n.mousewheel.timeout=void 0;var v=n.mousewheel.recentWheelEvents;15<=v.length&&v.shift();var m=v.length?v[v.length-1]:void 0,g=v[0];if(v.push(u),m&&(u.delta>m.delta||u.direction!==m.direction))v.splice(0);else if(15<=v.length&&u.time-g.time<500&&1<=g.delta-u.delta&&u.delta<=6){var y=0<r?.8:.2;n.mousewheel.lastEventBeforeSnap=u,v.splice(0),n.mousewheel.timeout=O(function(){n.slideToClosest(n.params.speed,!0,void 0,y)},0)}n.mousewheel.timeout||(n.mousewheel.timeout=O(function(){n.mousewheel.lastEventBeforeSnap=u,v.splice(0),n.slideToClosest(n.params.speed,!0,void 0,.5)},500))}if(d||n.emit("scroll",t),n.params.autoplay&&n.params.autoplayDisableOnInteraction&&n.autoplay.stop(),p===n.minTranslate()||p===n.maxTranslate())return!0}}else{var b={time:$(),delta:Math.abs(r),direction:Math.sign(r),raw:e},w=n.mousewheel.recentWheelEvents;2<=w.length&&w.shift();var x=w.length?w[w.length-1]:void 0;if(w.push(b),x?(b.direction!==x.direction||b.delta>x.delta||b.time>x.time+150)&&n.mousewheel.animateSlider(b):n.mousewheel.animateSlider(b),n.mousewheel.releaseScroll(b))return!0}return t.preventDefault?t.preventDefault():t.returnValue=!1,!1}},animateSlider:function(e){var t=this,n=L();return!(this.params.mousewheel.thresholdDelta&&e.delta<this.params.mousewheel.thresholdDelta||this.params.mousewheel.thresholdTime&&$()-t.mousewheel.lastScrollTime<this.params.mousewheel.thresholdTime||!(6<=e.delta&&$()-t.mousewheel.lastScrollTime<60)&&(e.direction<0?t.isEnd&&!t.params.loop||t.animating||(t.slideNext(),t.emit("scroll",e.raw)):t.isBeginning&&!t.params.loop||t.animating||(t.slidePrev(),t.emit("scroll",e.raw)),t.mousewheel.lastScrollTime=(new n.Date).getTime(),1))},releaseScroll:function(e){var t=this,n=t.params.mousewheel;if(e.direction<0){if(t.isEnd&&!t.params.loop&&n.releaseOnEdges)return!0}else if(t.isBeginning&&!t.params.loop&&n.releaseOnEdges)return!0;return!1},enable:function(){var e=this,t=G.event();if(e.params.cssMode)return e.wrapperEl.removeEventListener(t,e.mousewheel.handle),!0;if(!t)return!1;if(e.mousewheel.enabled)return!1;var n=e.$el;return"container"!==e.params.mousewheel.eventsTarget&&(n=z(e.params.mousewheel.eventsTarget)),n.on("mouseenter",e.mousewheel.handleMouseEnter),n.on("mouseleave",e.mousewheel.handleMouseLeave),n.on(t,e.mousewheel.handle),e.mousewheel.enabled=!0},disable:function(){var e=this,t=G.event();if(e.params.cssMode)return e.wrapperEl.addEventListener(t,e.mousewheel.handle),!0;if(!t)return!1;if(!e.mousewheel.enabled)return!1;var n=e.$el;return"container"!==e.params.mousewheel.eventsTarget&&(n=z(e.params.mousewheel.eventsTarget)),n.off(t,e.mousewheel.handle),!(e.mousewheel.enabled=!1)}},F={toggleEl:function(e,t){e[t?"addClass":"removeClass"](this.params.navigation.disabledClass),e[0]&&"BUTTON"===e[0].tagName&&(e[0].disabled=t)},update:function(){var e=this,t=e.params.navigation,n=e.navigation.toggleEl;if(!e.params.loop){var i=e.navigation,a=i.$nextEl,r=i.$prevEl;r&&0<r.length&&(e.isBeginning?n(r,!0):n(r,!1),e.params.watchOverflow&&e.enabled&&r[e.isLocked?"addClass":"removeClass"](t.lockClass)),a&&0<a.length&&(e.isEnd?n(a,!0):n(a,!1),e.params.watchOverflow&&e.enabled&&a[e.isLocked?"addClass":"removeClass"](t.lockClass))}},onPrevClick:function(e){e.preventDefault(),this.isBeginning&&!this.params.loop||this.slidePrev()},onNextClick:function(e){e.preventDefault(),this.isEnd&&!this.params.loop||this.slideNext()},init:function(){var e,t,n=this,i=n.params.navigation;n.params.navigation=y(n.$el,n.params.navigation,n.params.createElements,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),(i.nextEl||i.prevEl)&&(i.nextEl&&(e=z(i.nextEl),n.params.uniqueNavElements&&"string"==typeof i.nextEl&&1<e.length&&1===n.$el.find(i.nextEl).length&&(e=n.$el.find(i.nextEl))),i.prevEl&&(t=z(i.prevEl),n.params.uniqueNavElements&&"string"==typeof i.prevEl&&1<t.length&&1===n.$el.find(i.prevEl).length&&(t=n.$el.find(i.prevEl))),e&&0<e.length&&e.on("click",n.navigation.onNextClick),t&&0<t.length&&t.on("click",n.navigation.onPrevClick),se(n.navigation,{$nextEl:e,nextEl:e&&e[0],$prevEl:t,prevEl:t&&t[0]}),n.enabled||(e&&e.addClass(i.lockClass),t&&t.addClass(i.lockClass)))},destroy:function(){var e=this,t=e.navigation,n=t.$nextEl,i=t.$prevEl;n&&n.length&&(n.off("click",e.navigation.onNextClick),n.removeClass(e.params.navigation.disabledClass)),i&&i.length&&(i.off("click",e.navigation.onPrevClick),i.removeClass(e.params.navigation.disabledClass))}},W={update:function(){var e=this,t=e.rtl,i=e.params.pagination;if(i.el&&e.pagination.el&&e.pagination.$el&&0!==e.pagination.$el.length){var a,n=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,r=e.pagination.$el,s=e.params.loop?Math.ceil((n-2*e.loopedSlides)/e.params.slidesPerGroup):e.snapGrid.length;if(e.params.loop?((a=Math.ceil((e.activeIndex-e.loopedSlides)/e.params.slidesPerGroup))>n-1-2*e.loopedSlides&&(a-=n-2*e.loopedSlides),s-1<a&&(a-=s),a<0&&"bullets"!==e.params.paginationType&&(a=s+a)):a=void 0!==e.snapIndex?e.snapIndex:e.activeIndex||0,"bullets"===i.type&&e.pagination.bullets&&0<e.pagination.bullets.length){var o,l,u,c=e.pagination.bullets;if(i.dynamicBullets&&(e.pagination.bulletSize=c.eq(0)[e.isHorizontal()?"outerWidth":"outerHeight"](!0),r.css(e.isHorizontal()?"width":"height",e.pagination.bulletSize*(i.dynamicMainBullets+4)+"px"),1<i.dynamicMainBullets&&void 0!==e.previousIndex&&(e.pagination.dynamicBulletIndex+=a-e.previousIndex,e.pagination.dynamicBulletIndex>i.dynamicMainBullets-1?e.pagination.dynamicBulletIndex=i.dynamicMainBullets-1:e.pagination.dynamicBulletIndex<0&&(e.pagination.dynamicBulletIndex=0)),o=a-e.pagination.dynamicBulletIndex,u=((l=o+(Math.min(c.length,i.dynamicMainBullets)-1))+o)/2),c.removeClass(i.bulletActiveClass+" "+i.bulletActiveClass+"-next "+i.bulletActiveClass+"-next-next "+i.bulletActiveClass+"-prev "+i.bulletActiveClass+"-prev-prev "+i.bulletActiveClass+"-main"),1<r.length)c.each(function(e){var t=z(e),n=t.index();n===a&&t.addClass(i.bulletActiveClass),i.dynamicBullets&&(o<=n&&n<=l&&t.addClass(i.bulletActiveClass+"-main"),n===o&&t.prev().addClass(i.bulletActiveClass+"-prev").prev().addClass(i.bulletActiveClass+"-prev-prev"),n===l&&t.next().addClass(i.bulletActiveClass+"-next").next().addClass(i.bulletActiveClass+"-next-next"))});else{var d=c.eq(a),p=d.index();if(d.addClass(i.bulletActiveClass),i.dynamicBullets){for(var f=c.eq(o),h=c.eq(l),v=o;v<=l;v+=1)c.eq(v).addClass(i.bulletActiveClass+"-main");if(e.params.loop)if(p>=c.length-i.dynamicMainBullets){for(var m=i.dynamicMainBullets;0<=m;m-=1)c.eq(c.length-m).addClass(i.bulletActiveClass+"-main");c.eq(c.length-i.dynamicMainBullets-1).addClass(i.bulletActiveClass+"-prev")}else f.prev().addClass(i.bulletActiveClass+"-prev").prev().addClass(i.bulletActiveClass+"-prev-prev"),h.next().addClass(i.bulletActiveClass+"-next").next().addClass(i.bulletActiveClass+"-next-next");else f.prev().addClass(i.bulletActiveClass+"-prev").prev().addClass(i.bulletActiveClass+"-prev-prev"),h.next().addClass(i.bulletActiveClass+"-next").next().addClass(i.bulletActiveClass+"-next-next")}}if(i.dynamicBullets){var g=Math.min(c.length,i.dynamicMainBullets+4),y=(e.pagination.bulletSize*g-e.pagination.bulletSize)/2-u*e.pagination.bulletSize,b=t?"right":"left";c.css(e.isHorizontal()?b:"top",y+"px")}}if("fraction"===i.type&&(r.find(S(i.currentClass)).text(i.formatFractionCurrent(a+1)),r.find(S(i.totalClass)).text(i.formatFractionTotal(s))),"progressbar"===i.type){var w;w=i.progressbarOpposite?e.isHorizontal()?"vertical":"horizontal":e.isHorizontal()?"horizontal":"vertical";var x=(a+1)/s,T=1,E=1;"horizontal"===w?T=x:E=x,r.find(S(i.progressbarFillClass)).transform("translate3d(0,0,0) scaleX("+T+") scaleY("+E+")").transition(e.params.speed)}"custom"===i.type&&i.renderCustom?(r.html(i.renderCustom(e,a+1,s)),e.emit("paginationRender",r[0])):e.emit("paginationUpdate",r[0]),e.params.watchOverflow&&e.enabled&&r[e.isLocked?"addClass":"removeClass"](i.lockClass)}},render:function(){var e=this,t=e.params.pagination;if(t.el&&e.pagination.el&&e.pagination.$el&&0!==e.pagination.$el.length){var n=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,i=e.pagination.$el,a="";if("bullets"===t.type){var r=e.params.loop?Math.ceil((n-2*e.loopedSlides)/e.params.slidesPerGroup):e.snapGrid.length;e.params.freeMode&&!e.params.loop&&n<r&&(r=n);for(var s=0;s<r;s+=1)t.renderBullet?a+=t.renderBullet.call(e,s,t.bulletClass):a+="<"+t.bulletElement+' class="'+t.bulletClass+'"></'+t.bulletElement+">";i.html(a),e.pagination.bullets=i.find(S(t.bulletClass))}"fraction"===t.type&&(a=t.renderFraction?t.renderFraction.call(e,t.currentClass,t.totalClass):'<span class="'+t.currentClass+'"></span> / <span class="'+t.totalClass+'"></span>',i.html(a)),"progressbar"===t.type&&(a=t.renderProgressbar?t.renderProgressbar.call(e,t.progressbarFillClass):'<span class="'+t.progressbarFillClass+'"></span>',i.html(a)),"custom"!==t.type&&e.emit("paginationRender",e.pagination.$el[0])}},init:function(){var n=this;n.params.pagination=y(n.$el,n.params.pagination,n.params.createElements,{el:"swiper-pagination"});var e=n.params.pagination;if(e.el){var t=z(e.el);0!==t.length&&(n.params.uniqueNavElements&&"string"==typeof e.el&&1<t.length&&(t=n.$el.find(e.el)),"bullets"===e.type&&e.clickable&&t.addClass(e.clickableClass),t.addClass(e.modifierClass+e.type),"bullets"===e.type&&e.dynamicBullets&&(t.addClass(""+e.modifierClass+e.type+"-dynamic"),n.pagination.dynamicBulletIndex=0,e.dynamicMainBullets<1&&(e.dynamicMainBullets=1)),"progressbar"===e.type&&e.progressbarOpposite&&t.addClass(e.progressbarOppositeClass),e.clickable&&t.on("click",S(e.bulletClass),function(e){e.preventDefault();var t=z(this).index()*n.params.slidesPerGroup;n.params.loop&&(t+=n.loopedSlides),n.slideTo(t)}),se(n.pagination,{$el:t,el:t[0]}),n.enabled||t.addClass(e.lockClass))}},destroy:function(){var e=this,t=e.params.pagination;if(t.el&&e.pagination.el&&e.pagination.$el&&0!==e.pagination.$el.length){var n=e.pagination.$el;n.removeClass(t.hiddenClass),n.removeClass(t.modifierClass+t.type),e.pagination.bullets&&e.pagination.bullets.removeClass(t.bulletActiveClass),t.clickable&&n.off("click",S(t.bulletClass))}}},V={setTranslate:function(){var e=this;if(e.params.scrollbar.el&&e.scrollbar.el){var t=e.scrollbar,n=e.rtlTranslate,i=e.progress,a=t.dragSize,r=t.trackSize,s=t.$dragEl,o=t.$el,l=e.params.scrollbar,u=a,c=(r-a)*i;n?0<(c=-c)?(u=a-c,c=0):r<-c+a&&(u=r+c):c<0?(u=a+c,c=0):r<c+a&&(u=r-c),e.isHorizontal()?(s.transform("translate3d("+c+"px, 0, 0)"),s[0].style.width=u+"px"):(s.transform("translate3d(0px, "+c+"px, 0)"),s[0].style.height=u+"px"),l.hide&&(clearTimeout(e.scrollbar.timeout),o[0].style.opacity=1,e.scrollbar.timeout=setTimeout(function(){o[0].style.opacity=0,o.transition(400)},1e3))}},setTransition:function(e){this.params.scrollbar.el&&this.scrollbar.el&&this.scrollbar.$dragEl.transition(e)},updateSize:function(){var e=this;if(e.params.scrollbar.el&&e.scrollbar.el){var t=e.scrollbar,n=t.$dragEl,i=t.$el;n[0].style.width="",n[0].style.height="";var a,r=e.isHorizontal()?i[0].offsetWidth:i[0].offsetHeight,s=e.size/e.virtualSize,o=s*(r/e.size);a="auto"===e.params.scrollbar.dragSize?r*s:parseInt(e.params.scrollbar.dragSize,10),e.isHorizontal()?n[0].style.width=a+"px":n[0].style.height=a+"px",i[0].style.display=1<=s?"none":"",e.params.scrollbar.hide&&(i[0].style.opacity=0),se(t,{trackSize:r,divider:s,moveDivider:o,dragSize:a}),e.params.watchOverflow&&e.enabled&&t.$el[e.isLocked?"addClass":"removeClass"](e.params.scrollbar.lockClass)}},getPointerPosition:function(e){return this.isHorizontal()?"touchstart"===e.type||"touchmove"===e.type?e.targetTouches[0].clientX:e.clientX:"touchstart"===e.type||"touchmove"===e.type?e.targetTouches[0].clientY:e.clientY},setDragPosition:function(e){var t,n=this,i=n.scrollbar,a=n.rtlTranslate,r=i.$el,s=i.dragSize,o=i.trackSize,l=i.dragStartPos;t=(i.getPointerPosition(e)-r.offset()[n.isHorizontal()?"left":"top"]-(null!==l?l:s/2))/(o-s),t=Math.max(Math.min(t,1),0),a&&(t=1-t);var u=n.minTranslate()+(n.maxTranslate()-n.minTranslate())*t;n.updateProgress(u),n.setTranslate(u),n.updateActiveIndex(),n.updateSlidesClasses()},onDragStart:function(e){var t=this,n=t.params.scrollbar,i=t.scrollbar,a=t.$wrapperEl,r=i.$el,s=i.$dragEl;t.scrollbar.isTouched=!0,t.scrollbar.dragStartPos=e.target===s[0]||e.target===s?i.getPointerPosition(e)-e.target.getBoundingClientRect()[t.isHorizontal()?"left":"top"]:null,e.preventDefault(),e.stopPropagation(),a.transition(100),s.transition(100),i.setDragPosition(e),clearTimeout(t.scrollbar.dragTimeout),r.transition(0),n.hide&&r.css("opacity",1),t.params.cssMode&&t.$wrapperEl.css("scroll-snap-type","none"),t.emit("scrollbarDragStart",e)},onDragMove:function(e){var t=this.scrollbar,n=this.$wrapperEl,i=t.$el,a=t.$dragEl;this.scrollbar.isTouched&&(e.preventDefault?e.preventDefault():e.returnValue=!1,t.setDragPosition(e),n.transition(0),i.transition(0),a.transition(0),this.emit("scrollbarDragMove",e))},onDragEnd:function(e){var t=this,n=t.params.scrollbar,i=t.scrollbar,a=t.$wrapperEl,r=i.$el;t.scrollbar.isTouched&&(t.scrollbar.isTouched=!1,t.params.cssMode&&(t.$wrapperEl.css("scroll-snap-type",""),a.transition("")),n.hide&&(clearTimeout(t.scrollbar.dragTimeout),t.scrollbar.dragTimeout=O(function(){r.css("opacity",0),r.transition(400)},1e3)),t.emit("scrollbarDragEnd",e),n.snapOnRelease&&t.slideToClosest())},enableDraggable:function(){var e=this;if(e.params.scrollbar.el){var t=C(),n=e.scrollbar,i=e.touchEventsTouch,a=e.touchEventsDesktop,r=e.params,s=e.support,o=n.$el[0],l=!(!s.passiveListener||!r.passiveListeners)&&{passive:!1,capture:!1},u=!(!s.passiveListener||!r.passiveListeners)&&{passive:!0,capture:!1};o&&(s.touch?(o.addEventListener(i.start,e.scrollbar.onDragStart,l),o.addEventListener(i.move,e.scrollbar.onDragMove,l),o.addEventListener(i.end,e.scrollbar.onDragEnd,u)):(o.addEventListener(a.start,e.scrollbar.onDragStart,l),t.addEventListener(a.move,e.scrollbar.onDragMove,l),t.addEventListener(a.end,e.scrollbar.onDragEnd,u)))}},disableDraggable:function(){var e=this;if(e.params.scrollbar.el){var t=C(),n=e.scrollbar,i=e.touchEventsTouch,a=e.touchEventsDesktop,r=e.params,s=e.support,o=n.$el[0],l=!(!s.passiveListener||!r.passiveListeners)&&{passive:!1,capture:!1},u=!(!s.passiveListener||!r.passiveListeners)&&{passive:!0,capture:!1};o&&(s.touch?(o.removeEventListener(i.start,e.scrollbar.onDragStart,l),o.removeEventListener(i.move,e.scrollbar.onDragMove,l),o.removeEventListener(i.end,e.scrollbar.onDragEnd,u)):(o.removeEventListener(a.start,e.scrollbar.onDragStart,l),t.removeEventListener(a.move,e.scrollbar.onDragMove,l),t.removeEventListener(a.end,e.scrollbar.onDragEnd,u)))}},init:function(){var e=this,t=e.scrollbar,n=e.$el;e.params.scrollbar=y(n,e.params.scrollbar,e.params.createElements,{el:"swiper-scrollbar"});var i=e.params.scrollbar;if(i.el){var a=z(i.el);e.params.uniqueNavElements&&"string"==typeof i.el&&1<a.length&&1===n.find(i.el).length&&(a=n.find(i.el));var r=a.find("."+e.params.scrollbar.dragClass);0===r.length&&(r=z('<div class="'+e.params.scrollbar.dragClass+'"></div>'),a.append(r)),se(t,{$el:a,el:a[0],$dragEl:r,dragEl:r[0]}),i.draggable&&t.enableDraggable(),a&&a[e.enabled?"removeClass":"addClass"](e.params.scrollbar.lockClass)}},destroy:function(){this.scrollbar.disableDraggable()}},X={setTransform:function(e,t){var n=this.rtl,i=z(e),a=n?-1:1,r=i.attr("data-swiper-parallax")||"0",s=i.attr("data-swiper-parallax-x"),o=i.attr("data-swiper-parallax-y"),l=i.attr("data-swiper-parallax-scale"),u=i.attr("data-swiper-parallax-opacity");if(s||o?(s=s||"0",o=o||"0"):this.isHorizontal()?(s=r,o="0"):(o=r,s="0"),s=0<=s.indexOf("%")?parseInt(s,10)*t*a+"%":s*t*a+"px",o=0<=o.indexOf("%")?parseInt(o,10)*t+"%":o*t+"px",null!=u){var c=u-(u-1)*(1-Math.abs(t));i[0].style.opacity=c}if(null==l)i.transform("translate3d("+s+", "+o+", 0px)");else{var d=l-(l-1)*(1-Math.abs(t));i.transform("translate3d("+s+", "+o+", 0px) scale("+d+")")}},setTranslate:function(){var i=this,e=i.$el,t=i.slides,a=i.progress,r=i.snapGrid;e.children("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]").each(function(e){i.parallax.setTransform(e,a)}),t.each(function(e,t){var n=e.progress;1<i.params.slidesPerGroup&&"auto"!==i.params.slidesPerView&&(n+=Math.ceil(t/2)-a*(r.length-1)),n=Math.min(Math.max(n,-1),1),z(e).find("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]").each(function(e){i.parallax.setTransform(e,n)})})},setTransition:function(i){void 0===i&&(i=this.params.speed),this.$el.find("[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]").each(function(e){var t=z(e),n=parseInt(t.attr("data-swiper-parallax-duration"),10)||i;0===i&&(n=0),t.transition(n)})}},_={getDistanceBetweenTouches:function(e){if(e.targetTouches.length<2)return 1;var t=e.targetTouches[0].pageX,n=e.targetTouches[0].pageY,i=e.targetTouches[1].pageX,a=e.targetTouches[1].pageY;return Math.sqrt(Math.pow(i-t,2)+Math.pow(a-n,2))},onGestureStart:function(e){var t=this,n=t.support,i=t.params.zoom,a=t.zoom,r=a.gesture;if(a.fakeGestureTouched=!1,a.fakeGestureMoved=!1,!n.gestures){if("touchstart"!==e.type||"touchstart"===e.type&&e.targetTouches.length<2)return;a.fakeGestureTouched=!0,r.scaleStart=_.getDistanceBetweenTouches(e)}r.$slideEl&&r.$slideEl.length||(r.$slideEl=z(e.target).closest("."+t.params.slideClass),0===r.$slideEl.length&&(r.$slideEl=t.slides.eq(t.activeIndex)),r.$imageEl=r.$slideEl.find("img, svg, canvas, picture, .swiper-zoom-target"),r.$imageWrapEl=r.$imageEl.parent("."+i.containerClass),r.maxRatio=r.$imageWrapEl.attr("data-swiper-zoom")||i.maxRatio,0!==r.$imageWrapEl.length)?(r.$imageEl&&r.$imageEl.transition(0),t.zoom.isScaling=!0):r.$imageEl=void 0},onGestureChange:function(e){var t=this.support,n=this.params.zoom,i=this.zoom,a=i.gesture;if(!t.gestures){if("touchmove"!==e.type||"touchmove"===e.type&&e.targetTouches.length<2)return;i.fakeGestureMoved=!0,a.scaleMove=_.getDistanceBetweenTouches(e)}a.$imageEl&&0!==a.$imageEl.length?(t.gestures?i.scale=e.scale*i.currentScale:i.scale=a.scaleMove/a.scaleStart*i.currentScale,i.scale>a.maxRatio&&(i.scale=a.maxRatio-1+Math.pow(i.scale-a.maxRatio+1,.5)),i.scale<n.minRatio&&(i.scale=n.minRatio+1-Math.pow(n.minRatio-i.scale+1,.5)),a.$imageEl.transform("translate3d(0,0,0) scale("+i.scale+")")):"gesturechange"===e.type&&i.onGestureStart(e)},onGestureEnd:function(e){var t=this,n=t.device,i=t.support,a=t.params.zoom,r=t.zoom,s=r.gesture;if(!i.gestures){if(!r.fakeGestureTouched||!r.fakeGestureMoved)return;if("touchend"!==e.type||"touchend"===e.type&&e.changedTouches.length<2&&!n.android)return;r.fakeGestureTouched=!1,r.fakeGestureMoved=!1}s.$imageEl&&0!==s.$imageEl.length&&(r.scale=Math.max(Math.min(r.scale,s.maxRatio),a.minRatio),s.$imageEl.transition(t.params.speed).transform("translate3d(0,0,0) scale("+r.scale+")"),r.currentScale=r.scale,r.isScaling=!1,1===r.scale&&(s.$slideEl=void 0))},onTouchStart:function(e){var t=this.device,n=this.zoom,i=n.gesture,a=n.image;i.$imageEl&&0!==i.$imageEl.length&&(a.isTouched||(t.android&&e.cancelable&&e.preventDefault(),a.isTouched=!0,a.touchesStart.x="touchstart"===e.type?e.targetTouches[0].pageX:e.pageX,a.touchesStart.y="touchstart"===e.type?e.targetTouches[0].pageY:e.pageY))},onTouchMove:function(e){var t=this.zoom,n=t.gesture,i=t.image,a=t.velocity;if(n.$imageEl&&0!==n.$imageEl.length&&(this.allowClick=!1,i.isTouched&&n.$slideEl)){i.isMoved||(i.width=n.$imageEl[0].offsetWidth,i.height=n.$imageEl[0].offsetHeight,i.startX=v(n.$imageWrapEl[0],"x")||0,i.startY=v(n.$imageWrapEl[0],"y")||0,n.slideWidth=n.$slideEl[0].offsetWidth,n.slideHeight=n.$slideEl[0].offsetHeight,n.$imageWrapEl.transition(0));var r=i.width*t.scale,s=i.height*t.scale;if(!(r<n.slideWidth&&s<n.slideHeight)){if(i.minX=Math.min(n.slideWidth/2-r/2,0),i.maxX=-i.minX,i.minY=Math.min(n.slideHeight/2-s/2,0),i.maxY=-i.minY,i.touchesCurrent.x="touchmove"===e.type?e.targetTouches[0].pageX:e.pageX,i.touchesCurrent.y="touchmove"===e.type?e.targetTouches[0].pageY:e.pageY,!i.isMoved&&!t.isScaling){if(this.isHorizontal()&&(Math.floor(i.minX)===Math.floor(i.startX)&&i.touchesCurrent.x<i.touchesStart.x||Math.floor(i.maxX)===Math.floor(i.startX)&&i.touchesCurrent.x>i.touchesStart.x))return void(i.isTouched=!1);if(!this.isHorizontal()&&(Math.floor(i.minY)===Math.floor(i.startY)&&i.touchesCurrent.y<i.touchesStart.y||Math.floor(i.maxY)===Math.floor(i.startY)&&i.touchesCurrent.y>i.touchesStart.y))return void(i.isTouched=!1)}e.cancelable&&e.preventDefault(),e.stopPropagation(),i.isMoved=!0,i.currentX=i.touchesCurrent.x-i.touchesStart.x+i.startX,i.currentY=i.touchesCurrent.y-i.touchesStart.y+i.startY,i.currentX<i.minX&&(i.currentX=i.minX+1-Math.pow(i.minX-i.currentX+1,.8)),i.currentX>i.maxX&&(i.currentX=i.maxX-1+Math.pow(i.currentX-i.maxX+1,.8)),i.currentY<i.minY&&(i.currentY=i.minY+1-Math.pow(i.minY-i.currentY+1,.8)),i.currentY>i.maxY&&(i.currentY=i.maxY-1+Math.pow(i.currentY-i.maxY+1,.8)),a.prevPositionX||(a.prevPositionX=i.touchesCurrent.x),a.prevPositionY||(a.prevPositionY=i.touchesCurrent.y),a.prevTime||(a.prevTime=Date.now()),a.x=(i.touchesCurrent.x-a.prevPositionX)/(Date.now()-a.prevTime)/2,a.y=(i.touchesCurrent.y-a.prevPositionY)/(Date.now()-a.prevTime)/2,Math.abs(i.touchesCurrent.x-a.prevPositionX)<2&&(a.x=0),Math.abs(i.touchesCurrent.y-a.prevPositionY)<2&&(a.y=0),a.prevPositionX=i.touchesCurrent.x,a.prevPositionY=i.touchesCurrent.y,a.prevTime=Date.now(),n.$imageWrapEl.transform("translate3d("+i.currentX+"px, "+i.currentY+"px,0)")}}},onTouchEnd:function(){var e=this.zoom,t=e.gesture,n=e.image,i=e.velocity;if(t.$imageEl&&0!==t.$imageEl.length){if(!n.isTouched||!n.isMoved)return n.isTouched=!1,void(n.isMoved=!1);n.isTouched=!1,n.isMoved=!1;var a=300,r=300,s=i.x*a,o=n.currentX+s,l=i.y*r,u=n.currentY+l;0!==i.x&&(a=Math.abs((o-n.currentX)/i.x)),0!==i.y&&(r=Math.abs((u-n.currentY)/i.y));var c=Math.max(a,r);n.currentX=o,n.currentY=u;var d=n.width*e.scale,p=n.height*e.scale;n.minX=Math.min(t.slideWidth/2-d/2,0),n.maxX=-n.minX,n.minY=Math.min(t.slideHeight/2-p/2,0),n.maxY=-n.minY,n.currentX=Math.max(Math.min(n.currentX,n.maxX),n.minX),n.currentY=Math.max(Math.min(n.currentY,n.maxY),n.minY),t.$imageWrapEl.transition(c).transform("translate3d("+n.currentX+"px, "+n.currentY+"px,0)")}},onTransitionEnd:function(){var e=this.zoom,t=e.gesture;t.$slideEl&&this.previousIndex!==this.activeIndex&&(t.$imageEl&&t.$imageEl.transform("translate3d(0,0,0) scale(1)"),t.$imageWrapEl&&t.$imageWrapEl.transform("translate3d(0,0,0)"),e.scale=1,e.currentScale=1,t.$slideEl=void 0,t.$imageEl=void 0,t.$imageWrapEl=void 0)},toggle:function(e){var t=this.zoom;t.scale&&1!==t.scale?t.out():t.in(e)},in:function(e){var t,n,i,a,r,s,o,l,u,c,d,p,f,h,v,m,g=this,y=L(),b=g.zoom,w=g.params.zoom,x=b.gesture,T=b.image;x.$slideEl||(e&&e.target&&(x.$slideEl=z(e.target).closest("."+g.params.slideClass)),x.$slideEl||(g.params.virtual&&g.params.virtual.enabled&&g.virtual?x.$slideEl=g.$wrapperEl.children("."+g.params.slideActiveClass):x.$slideEl=g.slides.eq(g.activeIndex)),x.$imageEl=x.$slideEl.find("img, svg, canvas, picture, .swiper-zoom-target"),x.$imageWrapEl=x.$imageEl.parent("."+w.containerClass)),x.$imageEl&&0!==x.$imageEl.length&&x.$imageWrapEl&&0!==x.$imageWrapEl.length&&(x.$slideEl.addClass(""+w.zoomedSlideClass),n=void 0===T.touchesStart.x&&e?(t="touchend"===e.type?e.changedTouches[0].pageX:e.pageX,"touchend"===e.type?e.changedTouches[0].pageY:e.pageY):(t=T.touchesStart.x,T.touchesStart.y),b.scale=x.$imageWrapEl.attr("data-swiper-zoom")||w.maxRatio,b.currentScale=x.$imageWrapEl.attr("data-swiper-zoom")||w.maxRatio,e?(v=x.$slideEl[0].offsetWidth,m=x.$slideEl[0].offsetHeight,i=x.$slideEl.offset().left+y.scrollX+v/2-t,a=x.$slideEl.offset().top+y.scrollY+m/2-n,o=x.$imageEl[0].offsetWidth,l=x.$imageEl[0].offsetHeight,u=o*b.scale,c=l*b.scale,f=-(d=Math.min(v/2-u/2,0)),h=-(p=Math.min(m/2-c/2,0)),(r=i*b.scale)<d&&(r=d),f<r&&(r=f),(s=a*b.scale)<p&&(s=p),h<s&&(s=h)):s=r=0,x.$imageWrapEl.transition(300).transform("translate3d("+r+"px, "+s+"px,0)"),x.$imageEl.transition(300).transform("translate3d(0,0,0) scale("+b.scale+")"))},out:function(){var e=this,t=e.zoom,n=e.params.zoom,i=t.gesture;i.$slideEl||(e.params.virtual&&e.params.virtual.enabled&&e.virtual?i.$slideEl=e.$wrapperEl.children("."+e.params.slideActiveClass):i.$slideEl=e.slides.eq(e.activeIndex),i.$imageEl=i.$slideEl.find("img, svg, canvas, picture, .swiper-zoom-target"),i.$imageWrapEl=i.$imageEl.parent("."+n.containerClass)),i.$imageEl&&0!==i.$imageEl.length&&i.$imageWrapEl&&0!==i.$imageWrapEl.length&&(t.scale=1,t.currentScale=1,i.$imageWrapEl.transition(300).transform("translate3d(0,0,0)"),i.$imageEl.transition(300).transform("translate3d(0,0,0) scale(1)"),i.$slideEl.removeClass(""+n.zoomedSlideClass),i.$slideEl=void 0)},toggleGestures:function(e){var t=this.zoom,n=t.slideSelector,i=t.passiveListener;this.$wrapperEl[e]("gesturestart",n,t.onGestureStart,i),this.$wrapperEl[e]("gesturechange",n,t.onGestureChange,i),this.$wrapperEl[e]("gestureend",n,t.onGestureEnd,i)},enableGestures:function(){this.zoom.gesturesEnabled||(this.zoom.gesturesEnabled=!0,this.zoom.toggleGestures("on"))},disableGestures:function(){this.zoom.gesturesEnabled&&(this.zoom.gesturesEnabled=!1,this.zoom.toggleGestures("off"))},enable:function(){var e=this,t=e.support,n=e.zoom;if(!n.enabled){n.enabled=!0;var i=!("touchstart"!==e.touchEvents.start||!t.passiveListener||!e.params.passiveListeners)&&{passive:!0,capture:!1},a=!t.passiveListener||{passive:!1,capture:!0},r="."+e.params.slideClass;e.zoom.passiveListener=i,e.zoom.slideSelector=r,t.gestures?(e.$wrapperEl.on(e.touchEvents.start,e.zoom.enableGestures,i),e.$wrapperEl.on(e.touchEvents.end,e.zoom.disableGestures,i)):"touchstart"===e.touchEvents.start&&(e.$wrapperEl.on(e.touchEvents.start,r,n.onGestureStart,i),e.$wrapperEl.on(e.touchEvents.move,r,n.onGestureChange,a),e.$wrapperEl.on(e.touchEvents.end,r,n.onGestureEnd,i),e.touchEvents.cancel&&e.$wrapperEl.on(e.touchEvents.cancel,r,n.onGestureEnd,i)),e.$wrapperEl.on(e.touchEvents.move,"."+e.params.zoom.containerClass,n.onTouchMove,a)}},disable:function(){var e=this,t=e.zoom;if(t.enabled){var n=e.support;e.zoom.enabled=!1;var i=!("touchstart"!==e.touchEvents.start||!n.passiveListener||!e.params.passiveListeners)&&{passive:!0,capture:!1},a=!n.passiveListener||{passive:!1,capture:!0},r="."+e.params.slideClass;n.gestures?(e.$wrapperEl.off(e.touchEvents.start,e.zoom.enableGestures,i),e.$wrapperEl.off(e.touchEvents.end,e.zoom.disableGestures,i)):"touchstart"===e.touchEvents.start&&(e.$wrapperEl.off(e.touchEvents.start,r,t.onGestureStart,i),e.$wrapperEl.off(e.touchEvents.move,r,t.onGestureChange,a),e.$wrapperEl.off(e.touchEvents.end,r,t.onGestureEnd,i),e.touchEvents.cancel&&e.$wrapperEl.off(e.touchEvents.cancel,r,t.onGestureEnd,i)),e.$wrapperEl.off(e.touchEvents.move,"."+e.params.zoom.containerClass,t.onTouchMove,a)}}},Y={loadInSlide:function(e,u){void 0===u&&(u=!0);var c=this,d=c.params.lazy;if(void 0!==e&&0!==c.slides.length){var p=c.virtual&&c.params.virtual.enabled?c.$wrapperEl.children("."+c.params.slideClass+'[data-swiper-slide-index="'+e+'"]'):c.slides.eq(e),t=p.find("."+d.elementClass+":not(."+d.loadedClass+"):not(."+d.loadingClass+")");!p.hasClass(d.elementClass)||p.hasClass(d.loadedClass)||p.hasClass(d.loadingClass)||t.push(p[0]),0!==t.length&&t.each(function(e){var i=z(e);i.addClass(d.loadingClass);var a=i.attr("data-background"),r=i.attr("data-src"),s=i.attr("data-srcset"),o=i.attr("data-sizes"),l=i.parent("picture");c.loadImage(i[0],r||a,s,o,!1,function(){if(null!=c&&c&&(!c||c.params)&&!c.destroyed){if(a?(i.css("background-image",'url("'+a+'")'),i.removeAttr("data-background")):(s&&(i.attr("srcset",s),i.removeAttr("data-srcset")),o&&(i.attr("sizes",o),i.removeAttr("data-sizes")),l.length&&l.children("source").each(function(e){var t=z(e);t.attr("data-srcset")&&(t.attr("srcset",t.attr("data-srcset")),t.removeAttr("data-srcset"))}),r&&(i.attr("src",r),i.removeAttr("data-src"))),i.addClass(d.loadedClass).removeClass(d.loadingClass),p.find("."+d.preloaderClass).remove(),c.params.loop&&u){var e=p.attr("data-swiper-slide-index");if(p.hasClass(c.params.slideDuplicateClass)){var t=c.$wrapperEl.children('[data-swiper-slide-index="'+e+'"]:not(.'+c.params.slideDuplicateClass+")");c.lazy.loadInSlide(t.index(),!1)}else{var n=c.$wrapperEl.children("."+c.params.slideDuplicateClass+'[data-swiper-slide-index="'+e+'"]');c.lazy.loadInSlide(n.index(),!1)}}c.emit("lazyImageReady",p[0],i[0]),c.params.autoHeight&&c.updateAutoHeight()}}),c.emit("lazyImageLoad",p[0],i[0])})}},load:function(){var n=this,t=n.$wrapperEl,i=n.params,a=n.slides,e=n.activeIndex,r=n.virtual&&i.virtual.enabled,s=i.lazy,o=i.slidesPerView;function l(e){if(r){if(t.children("."+i.slideClass+'[data-swiper-slide-index="'+e+'"]').length)return!0}else if(a[e])return!0;return!1}function u(e){return r?z(e).attr("data-swiper-slide-index"):z(e).index()}if("auto"===o&&(o=0),n.lazy.initialImageLoaded||(n.lazy.initialImageLoaded=!0),n.params.watchSlidesVisibility)t.children("."+i.slideVisibleClass).each(function(e){var t=r?z(e).attr("data-swiper-slide-index"):z(e).index();n.lazy.loadInSlide(t)});else if(1<o)for(var c=e;c<e+o;c+=1)l(c)&&n.lazy.loadInSlide(c);else n.lazy.loadInSlide(e);if(s.loadPrevNext)if(1<o||s.loadPrevNextAmount&&1<s.loadPrevNextAmount){for(var d=s.loadPrevNextAmount,p=o,f=Math.min(e+p+Math.max(d,p),a.length),h=Math.max(e-Math.max(p,d),0),v=e+o;v<f;v+=1)l(v)&&n.lazy.loadInSlide(v);for(var m=h;m<e;m+=1)l(m)&&n.lazy.loadInSlide(m)}else{var g=t.children("."+i.slideNextClass);0<g.length&&n.lazy.loadInSlide(u(g));var y=t.children("."+i.slidePrevClass);0<y.length&&n.lazy.loadInSlide(u(y))}},checkInViewOnLoad:function(){var e=L(),t=this;if(t&&!t.destroyed){var n=t.params.lazy.scrollingElement?z(t.params.lazy.scrollingElement):z(e),i=n[0]===e,a=i?e.innerWidth:n[0].offsetWidth,r=i?e.innerHeight:n[0].offsetHeight,s=t.$el.offset(),o=!1;t.rtlTranslate&&(s.left-=t.$el[0].scrollLeft);for(var l=[[s.left,s.top],[s.left+t.width,s.top],[s.left,s.top+t.height],[s.left+t.width,s.top+t.height]],u=0;u<l.length;u+=1){var c=l[u];if(0<=c[0]&&c[0]<=a&&0<=c[1]&&c[1]<=r){if(0===c[0]&&0===c[1])continue;o=!0}}var d=!("touchstart"!==t.touchEvents.start||!t.support.passiveListener||!t.params.passiveListeners)&&{passive:!0,capture:!1};o?(t.lazy.load(),n.off("scroll",t.lazy.checkInViewOnLoad,d)):t.lazy.scrollHandlerAttached||(t.lazy.scrollHandlerAttached=!0,n.on("scroll",t.lazy.checkInViewOnLoad,d))}}},U={LinearSpline:function(e,t){var n,i,a,r,s;return this.x=e,this.y=t,this.lastIndex=e.length-1,this.interpolate=function(e){return e?(s=function(e,t){for(i=-1,n=e.length;1<n-i;)e[a=n+i>>1]<=t?i=a:n=a;return n}(this.x,e),r=s-1,(e-this.x[r])*(this.y[s]-this.y[r])/(this.x[s]-this.x[r])+this.y[r]):0},this},getInterpolateFunction:function(e){var t=this;t.controller.spline||(t.controller.spline=t.params.loop?new U.LinearSpline(t.slidesGrid,e.slidesGrid):new U.LinearSpline(t.snapGrid,e.snapGrid))},setTranslate:function(e,t){var n,i,a=this,r=a.controller.control,s=a.constructor;function o(e){var t=a.rtlTranslate?-a.translate:a.translate;"slide"===a.params.controller.by&&(a.controller.getInterpolateFunction(e),i=-a.controller.spline.interpolate(-t)),i&&"container"!==a.params.controller.by||(n=(e.maxTranslate()-e.minTranslate())/(a.maxTranslate()-a.minTranslate()),i=(t-a.minTranslate())*n+e.minTranslate()),a.params.controller.inverse&&(i=e.maxTranslate()-i),e.updateProgress(i),e.setTranslate(i,a),e.updateActiveIndex(),e.updateSlidesClasses()}if(Array.isArray(r))for(var l=0;l<r.length;l+=1)r[l]!==t&&r[l]instanceof s&&o(r[l]);else r instanceof s&&t!==r&&o(r)},setTransition:function(t,e){var n,i=this,a=i.constructor,r=i.controller.control;function s(e){e.setTransition(t,i),0!==t&&(e.transitionStart(),e.params.autoHeight&&O(function(){e.updateAutoHeight()}),e.$wrapperEl.transitionEnd(function(){r&&(e.params.loop&&"slide"===i.params.controller.by&&e.loopFix(),e.transitionEnd())}))}if(Array.isArray(r))for(n=0;n<r.length;n+=1)r[n]!==e&&r[n]instanceof a&&s(r[n]);else r instanceof a&&e!==r&&s(r)}},K={getRandomNumber:function(e){return void 0===e&&(e=16),"x".repeat(e).replace(/x/g,function(){return Math.round(16*Math.random()).toString(16)})},makeElFocusable:function(e){return e.attr("tabIndex","0"),e},makeElNotFocusable:function(e){return e.attr("tabIndex","-1"),e},addElRole:function(e,t){return e.attr("role",t),e},addElRoleDescription:function(e,t){return e.attr("aria-roledescription",t),e},addElControls:function(e,t){return e.attr("aria-controls",t),e},addElLabel:function(e,t){return e.attr("aria-label",t),e},addElId:function(e,t){return e.attr("id",t),e},addElLive:function(e,t){return e.attr("aria-live",t),e},disableEl:function(e){return e.attr("aria-disabled",!0),e},enableEl:function(e){return e.attr("aria-disabled",!1),e},onEnterOrSpaceKey:function(e){if(13===e.keyCode||32===e.keyCode){var t=this,n=t.params.a11y,i=z(e.target);t.navigation&&t.navigation.$nextEl&&i.is(t.navigation.$nextEl)&&(t.isEnd&&!t.params.loop||t.slideNext(),t.isEnd?t.a11y.notify(n.lastSlideMessage):t.a11y.notify(n.nextSlideMessage)),t.navigation&&t.navigation.$prevEl&&i.is(t.navigation.$prevEl)&&(t.isBeginning&&!t.params.loop||t.slidePrev(),t.isBeginning?t.a11y.notify(n.firstSlideMessage):t.a11y.notify(n.prevSlideMessage)),t.pagination&&i.is(S(t.params.pagination.bulletClass))&&i[0].click()}},notify:function(e){var t=this.a11y.liveRegion;0!==t.length&&(t.html(""),t.html(e))},updateNavigation:function(){var e=this;if(!e.params.loop&&e.navigation){var t=e.navigation,n=t.$nextEl,i=t.$prevEl;i&&0<i.length&&(e.isBeginning?(e.a11y.disableEl(i),e.a11y.makeElNotFocusable(i)):(e.a11y.enableEl(i),e.a11y.makeElFocusable(i))),n&&0<n.length&&(e.isEnd?(e.a11y.disableEl(n),e.a11y.makeElNotFocusable(n)):(e.a11y.enableEl(n),e.a11y.makeElFocusable(n)))}},updatePagination:function(){var n=this,i=n.params.a11y;n.pagination&&n.params.pagination.clickable&&n.pagination.bullets&&n.pagination.bullets.length&&n.pagination.bullets.each(function(e){var t=z(e);n.a11y.makeElFocusable(t),n.params.pagination.renderBullet||(n.a11y.addElRole(t,"button"),n.a11y.addElLabel(t,i.paginationBulletMessage.replace(/\{\{index\}\}/,t.index()+1)))})},init:function(){var r=this,s=r.params.a11y;r.$el.append(r.a11y.liveRegion);var e=r.$el;s.containerRoleDescriptionMessage&&r.a11y.addElRoleDescription(e,s.containerRoleDescriptionMessage),s.containerMessage&&r.a11y.addElLabel(e,s.containerMessage);var t=r.$wrapperEl,n=t.attr("id")||"swiper-wrapper-"+r.a11y.getRandomNumber(16),i=r.params.autoplay&&r.params.autoplay.enabled?"off":"polite";r.a11y.addElId(t,n),r.a11y.addElLive(t,i),s.itemRoleDescriptionMessage&&r.a11y.addElRoleDescription(z(r.slides),s.itemRoleDescriptionMessage),r.a11y.addElRole(z(r.slides),s.slideRole);var a,o,l=r.params.loop?r.slides.filter(function(e){return!e.classList.contains(r.params.slideDuplicateClass)}).length:r.slides.length;r.slides.each(function(e,t){var n=z(e),i=r.params.loop?parseInt(n.attr("data-swiper-slide-index"),10):t,a=s.slideLabelMessage.replace(/\{\{index\}\}/,i+1).replace(/\{\{slidesLength\}\}/,l);r.a11y.addElLabel(n,a)}),r.navigation&&r.navigation.$nextEl&&(a=r.navigation.$nextEl),r.navigation&&r.navigation.$prevEl&&(o=r.navigation.$prevEl),a&&a.length&&(r.a11y.makeElFocusable(a),"BUTTON"!==a[0].tagName&&(r.a11y.addElRole(a,"button"),a.on("keydown",r.a11y.onEnterOrSpaceKey)),r.a11y.addElLabel(a,s.nextSlideMessage),r.a11y.addElControls(a,n)),o&&o.length&&(r.a11y.makeElFocusable(o),"BUTTON"!==o[0].tagName&&(r.a11y.addElRole(o,"button"),o.on("keydown",r.a11y.onEnterOrSpaceKey)),r.a11y.addElLabel(o,s.prevSlideMessage),r.a11y.addElControls(o,n)),r.pagination&&r.params.pagination.clickable&&r.pagination.bullets&&r.pagination.bullets.length&&r.pagination.$el.on("keydown",S(r.params.pagination.bulletClass),r.a11y.onEnterOrSpaceKey)},destroy:function(){var e,t,n=this;n.a11y.liveRegion&&0<n.a11y.liveRegion.length&&n.a11y.liveRegion.remove(),n.navigation&&n.navigation.$nextEl&&(e=n.navigation.$nextEl),n.navigation&&n.navigation.$prevEl&&(t=n.navigation.$prevEl),e&&e.off("keydown",n.a11y.onEnterOrSpaceKey),t&&t.off("keydown",n.a11y.onEnterOrSpaceKey),n.pagination&&n.params.pagination.clickable&&n.pagination.bullets&&n.pagination.bullets.length&&n.pagination.$el.off("keydown",S(n.params.pagination.bulletClass),n.a11y.onEnterOrSpaceKey)}},J={init:function(){var e=this,t=L();if(e.params.history){if(!t.history||!t.history.pushState)return e.params.history.enabled=!1,void(e.params.hashNavigation.enabled=!0);var n=e.history;n.initialized=!0,n.paths=J.getPathValues(e.params.url),(n.paths.key||n.paths.value)&&(n.scrollToSlide(0,n.paths.value,e.params.runCallbacksOnInit),e.params.history.replaceState||t.addEventListener("popstate",e.history.setHistoryPopState))}},destroy:function(){var e=L();this.params.history.replaceState||e.removeEventListener("popstate",this.history.setHistoryPopState)},setHistoryPopState:function(){var e=this;e.history.paths=J.getPathValues(e.params.url),e.history.scrollToSlide(e.params.speed,e.history.paths.value,!1)},getPathValues:function(e){var t=L(),n=(e?new URL(e):t.location).pathname.slice(1).split("/").filter(function(e){return""!==e}),i=n.length;return{key:n[i-2],value:n[i-1]}},setHistory:function(e,t){var n=this,i=L();if(n.history.initialized&&n.params.history.enabled){var a;a=n.params.url?new URL(n.params.url):i.location;var r=n.slides.eq(t),s=J.slugify(r.attr("data-history"));if(0<n.params.history.root.length){var o=n.params.history.root;"/"===o[o.length-1]&&(o=o.slice(0,o.length-1)),s=o+"/"+e+"/"+s}else a.pathname.includes(e)||(s=e+"/"+s);var l=i.history.state;l&&l.value===s||(n.params.history.replaceState?i.history.replaceState({value:s},null,s):i.history.pushState({value:s},null,s))}},slugify:function(e){return e.toString().replace(/\s+/g,"-").replace(/[^\w-]+/g,"").replace(/--+/g,"-").replace(/^-+/,"").replace(/-+$/,"")},scrollToSlide:function(e,t,n){var i=this;if(t)for(var a=0,r=i.slides.length;a<r;a+=1){var s=i.slides.eq(a);if(J.slugify(s.attr("data-history"))===t&&!s.hasClass(i.params.slideDuplicateClass)){var o=s.index();i.slideTo(o,e,n)}}else i.slideTo(0,e,n)}},Z={onHashChange:function(){var e=this,t=C();e.emit("hashChange");var n=t.location.hash.replace("#","");if(n!==e.slides.eq(e.activeIndex).attr("data-hash")){var i=e.$wrapperEl.children("."+e.params.slideClass+'[data-hash="'+n+'"]').index();if(void 0===i)return;e.slideTo(i)}},setHash:function(){var e=this,t=L(),n=C();if(e.hashNavigation.initialized&&e.params.hashNavigation.enabled)if(e.params.hashNavigation.replaceState&&t.history&&t.history.replaceState)t.history.replaceState(null,null,"#"+e.slides.eq(e.activeIndex).attr("data-hash")||""),e.emit("hashSet");else{var i=e.slides.eq(e.activeIndex),a=i.attr("data-hash")||i.attr("data-history");n.location.hash=a||"",e.emit("hashSet")}},init:function(){var e=this,t=C(),n=L();if(!(!e.params.hashNavigation.enabled||e.params.history&&e.params.history.enabled)){e.hashNavigation.initialized=!0;var i=t.location.hash.replace("#","");if(i)for(var a=0,r=e.slides.length;a<r;a+=1){var s=e.slides.eq(a);if((s.attr("data-hash")||s.attr("data-history"))===i&&!s.hasClass(e.params.slideDuplicateClass)){var o=s.index();e.slideTo(o,0,e.params.runCallbacksOnInit,!0)}}e.params.hashNavigation.watchState&&z(n).on("hashchange",e.hashNavigation.onHashChange)}},destroy:function(){var e=L();this.params.hashNavigation.watchState&&z(e).off("hashchange",this.hashNavigation.onHashChange)}},Q={run:function(){var t=this,e=t.slides.eq(t.activeIndex),n=t.params.autoplay.delay;e.attr("data-swiper-autoplay")&&(n=e.attr("data-swiper-autoplay")||t.params.autoplay.delay),clearTimeout(t.autoplay.timeout),t.autoplay.timeout=O(function(){var e;t.params.autoplay.reverseDirection?t.params.loop?(t.loopFix(),e=t.slidePrev(t.params.speed,!0,!0),t.emit("autoplay")):t.isBeginning?t.params.autoplay.stopOnLastSlide?t.autoplay.stop():(e=t.slideTo(t.slides.length-1,t.params.speed,!0,!0),t.emit("autoplay")):(e=t.slidePrev(t.params.speed,!0,!0),t.emit("autoplay")):t.params.loop?(t.loopFix(),e=t.slideNext(t.params.speed,!0,!0),t.emit("autoplay")):t.isEnd?t.params.autoplay.stopOnLastSlide?t.autoplay.stop():(e=t.slideTo(0,t.params.speed,!0,!0),t.emit("autoplay")):(e=t.slideNext(t.params.speed,!0,!0),t.emit("autoplay")),(t.params.cssMode&&t.autoplay.running||!1===e)&&t.autoplay.run()},n)},start:function(){var e=this;return void 0===e.autoplay.timeout&&!e.autoplay.running&&(e.autoplay.running=!0,e.emit("autoplayStart"),e.autoplay.run(),!0)},stop:function(){var e=this;return!!e.autoplay.running&&void 0!==e.autoplay.timeout&&(e.autoplay.timeout&&(clearTimeout(e.autoplay.timeout),e.autoplay.timeout=void 0),e.autoplay.running=!1,e.emit("autoplayStop"),!0)},pause:function(e){var t=this;t.autoplay.running&&(t.autoplay.paused||(t.autoplay.timeout&&clearTimeout(t.autoplay.timeout),t.autoplay.paused=!0,0!==e&&t.params.autoplay.waitForTransition?["transitionend","webkitTransitionEnd"].forEach(function(e){t.$wrapperEl[0].addEventListener(e,t.autoplay.onTransitionEnd)}):(t.autoplay.paused=!1,t.autoplay.run())))},onVisibilityChange:function(){var e=this,t=C();"hidden"===t.visibilityState&&e.autoplay.running&&e.autoplay.pause(),"visible"===t.visibilityState&&e.autoplay.paused&&(e.autoplay.run(),e.autoplay.paused=!1)},onTransitionEnd:function(e){var t=this;t&&!t.destroyed&&t.$wrapperEl&&e.target===t.$wrapperEl[0]&&(["transitionend","webkitTransitionEnd"].forEach(function(e){t.$wrapperEl[0].removeEventListener(e,t.autoplay.onTransitionEnd)}),t.autoplay.paused=!1,t.autoplay.running?t.autoplay.run():t.autoplay.stop())},onMouseEnter:function(){var t=this;t.params.autoplay.disableOnInteraction?t.autoplay.stop():t.autoplay.pause(),["transitionend","webkitTransitionEnd"].forEach(function(e){t.$wrapperEl[0].removeEventListener(e,t.autoplay.onTransitionEnd)})},onMouseLeave:function(){this.params.autoplay.disableOnInteraction||(this.autoplay.paused=!1,this.autoplay.run())},attachMouseEvents:function(){var e=this;e.params.autoplay.pauseOnMouseEnter&&(e.$el.on("mouseenter",e.autoplay.onMouseEnter),e.$el.on("mouseleave",e.autoplay.onMouseLeave))},detachMouseEvents:function(){this.$el.off("mouseenter",this.autoplay.onMouseEnter),this.$el.off("mouseleave",this.autoplay.onMouseLeave)}},ee={setTranslate:function(){for(var e=this,t=e.slides,n=0;n<t.length;n+=1){var i=e.slides.eq(n),a=-i[0].swiperSlideOffset;e.params.virtualTranslate||(a-=e.translate);var r=0;e.isHorizontal()||(r=a,a=0);var s=e.params.fadeEffect.crossFade?Math.max(1-Math.abs(i[0].progress),0):1+Math.min(Math.max(i[0].progress,-1),0);i.css({opacity:s}).transform("translate3d("+a+"px, "+r+"px, 0px)")}},setTransition:function(e){var n=this,t=n.slides,i=n.$wrapperEl;if(t.transition(e),n.params.virtualTranslate&&0!==e){var a=!1;t.transitionEnd(function(){if(!a&&n&&!n.destroyed){a=!0,n.animating=!1;for(var e=["webkitTransitionEnd","transitionend"],t=0;t<e.length;t+=1)i.trigger(e[t])}})}}},te={setTranslate:function(){var e,t=this,n=t.$el,i=t.$wrapperEl,a=t.slides,r=t.width,s=t.height,o=t.rtlTranslate,l=t.size,u=t.browser,c=t.params.cubeEffect,d=t.isHorizontal(),p=t.virtual&&t.params.virtual.enabled,f=0;c.shadow&&(d?(0===(e=i.find(".swiper-cube-shadow")).length&&(e=z('<div class="swiper-cube-shadow"></div>'),i.append(e)),e.css({height:r+"px"})):0===(e=n.find(".swiper-cube-shadow")).length&&(e=z('<div class="swiper-cube-shadow"></div>'),n.append(e)));for(var h=0;h<a.length;h+=1){var v=a.eq(h),m=h;p&&(m=parseInt(v.attr("data-swiper-slide-index"),10));var g=90*m,y=Math.floor(g/360);o&&(g=-g,y=Math.floor(-g/360));var b=Math.max(Math.min(v[0].progress,1),-1),w=0,x=0,T=0;m%4==0?(w=4*-y*l,T=0):(m-1)%4==0?(w=0,T=4*-y*l):(m-2)%4==0?(w=l+4*y*l,T=l):(m-3)%4==0&&(w=-l,T=3*l+4*l*y),o&&(w=-w),d||(x=w,w=0);var E="rotateX("+(d?0:-g)+"deg) rotateY("+(d?g:0)+"deg) translate3d("+w+"px, "+x+"px, "+T+"px)";if(b<=1&&-1<b&&(f=90*m+90*b,o&&(f=90*-m-90*b)),v.transform(E),c.slideShadows){var C=d?v.find(".swiper-slide-shadow-left"):v.find(".swiper-slide-shadow-top"),S=d?v.find(".swiper-slide-shadow-right"):v.find(".swiper-slide-shadow-bottom");0===C.length&&(C=z('<div class="swiper-slide-shadow-'+(d?"left":"top")+'"></div>'),v.append(C)),0===S.length&&(S=z('<div class="swiper-slide-shadow-'+(d?"right":"bottom")+'"></div>'),v.append(S)),C.length&&(C[0].style.opacity=Math.max(-b,0)),S.length&&(S[0].style.opacity=Math.max(b,0))}}if(i.css({"-webkit-transform-origin":"50% 50% -"+l/2+"px","-moz-transform-origin":"50% 50% -"+l/2+"px","-ms-transform-origin":"50% 50% -"+l/2+"px","transform-origin":"50% 50% -"+l/2+"px"}),c.shadow)if(d)e.transform("translate3d(0px, "+(r/2+c.shadowOffset)+"px, "+-r/2+"px) rotateX(90deg) rotateZ(0deg) scale("+c.shadowScale+")");else{var k=Math.abs(f)-90*Math.floor(Math.abs(f)/90),M=1.5-(Math.sin(2*k*Math.PI/360)/2+Math.cos(2*k*Math.PI/360)/2),L=c.shadowScale,D=c.shadowScale/M,A=c.shadowOffset;e.transform("scale3d("+L+", 1, "+D+") translate3d(0px, "+(s/2+A)+"px, "+-s/2/D+"px) rotateX(-90deg)")}var P=u.isSafari||u.isWebView?-l/2:0;i.transform("translate3d(0px,0,"+P+"px) rotateX("+(t.isHorizontal()?0:f)+"deg) rotateY("+(t.isHorizontal()?-f:0)+"deg)")},setTransition:function(e){var t=this.$el;this.slides.transition(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(e),this.params.cubeEffect.shadow&&!this.isHorizontal()&&t.find(".swiper-cube-shadow").transition(e)}},ne={setTranslate:function(){for(var e=this.slides,t=this.rtlTranslate,n=0;n<e.length;n+=1){var i=e.eq(n),a=i[0].progress;this.params.flipEffect.limitRotation&&(a=Math.max(Math.min(i[0].progress,1),-1));var r=-180*a,s=0,o=-i[0].swiperSlideOffset,l=0;if(this.isHorizontal()?t&&(r=-r):(l=o,s=-r,r=o=0),i[0].style.zIndex=-Math.abs(Math.round(a))+e.length,this.params.flipEffect.slideShadows){var u=this.isHorizontal()?i.find(".swiper-slide-shadow-left"):i.find(".swiper-slide-shadow-top"),c=this.isHorizontal()?i.find(".swiper-slide-shadow-right"):i.find(".swiper-slide-shadow-bottom");0===u.length&&(u=z('<div class="swiper-slide-shadow-'+(this.isHorizontal()?"left":"top")+'"></div>'),i.append(u)),0===c.length&&(c=z('<div class="swiper-slide-shadow-'+(this.isHorizontal()?"right":"bottom")+'"></div>'),i.append(c)),u.length&&(u[0].style.opacity=Math.max(-a,0)),c.length&&(c[0].style.opacity=Math.max(a,0))}i.transform("translate3d("+o+"px, "+l+"px, 0px) rotateX("+s+"deg) rotateY("+r+"deg)")}},setTransition:function(e){var n=this,t=n.slides,i=n.activeIndex,a=n.$wrapperEl;if(t.transition(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(e),n.params.virtualTranslate&&0!==e){var r=!1;t.eq(i).transitionEnd(function(){if(!r&&n&&!n.destroyed){r=!0,n.animating=!1;for(var e=["webkitTransitionEnd","transitionend"],t=0;t<e.length;t+=1)a.trigger(e[t])}})}}},ie={setTranslate:function(){for(var e=this.width,t=this.height,n=this.slides,i=this.slidesSizesGrid,a=this.params.coverflowEffect,r=this.isHorizontal(),s=this.translate,o=r?e/2-s:t/2-s,l=r?a.rotate:-a.rotate,u=a.depth,c=0,d=n.length;c<d;c+=1){var p=n.eq(c),f=i[c],h=(o-p[0].swiperSlideOffset-f/2)/f*a.modifier,v=r?l*h:0,m=r?0:l*h,g=-u*Math.abs(h),y=a.stretch;"string"==typeof y&&-1!==y.indexOf("%")&&(y=parseFloat(a.stretch)/100*f);var b=r?0:y*h,w=r?y*h:0,x=1-(1-a.scale)*Math.abs(h);Math.abs(w)<.001&&(w=0),Math.abs(b)<.001&&(b=0),Math.abs(g)<.001&&(g=0),Math.abs(v)<.001&&(v=0),Math.abs(m)<.001&&(m=0),Math.abs(x)<.001&&(x=0);var T="translate3d("+w+"px,"+b+"px,"+g+"px)  rotateX("+m+"deg) rotateY("+v+"deg) scale("+x+")";if(p.transform(T),p[0].style.zIndex=1-Math.abs(Math.round(h)),a.slideShadows){var E=r?p.find(".swiper-slide-shadow-left"):p.find(".swiper-slide-shadow-top"),C=r?p.find(".swiper-slide-shadow-right"):p.find(".swiper-slide-shadow-bottom");0===E.length&&(E=z('<div class="swiper-slide-shadow-'+(r?"left":"top")+'"></div>'),p.append(E)),0===C.length&&(C=z('<div class="swiper-slide-shadow-'+(r?"right":"bottom")+'"></div>'),p.append(C)),E.length&&(E[0].style.opacity=0<h?h:0),C.length&&(C[0].style.opacity=0<-h?-h:0)}}},setTransition:function(e){this.slides.transition(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(e)}},ae={init:function(){var e=this,t=e.params.thumbs;if(e.thumbs.initialized)return!1;e.thumbs.initialized=!0;var n=e.constructor;return t.swiper instanceof n?(e.thumbs.swiper=t.swiper,se(e.thumbs.swiper.originalParams,{watchSlidesProgress:!0,slideToClickedSlide:!1}),se(e.thumbs.swiper.params,{watchSlidesProgress:!0,slideToClickedSlide:!1})):m(t.swiper)&&(e.thumbs.swiper=new n(se({},t.swiper,{watchSlidesVisibility:!0,watchSlidesProgress:!0,slideToClickedSlide:!1})),e.thumbs.swiperCreated=!0),e.thumbs.swiper.$el.addClass(e.params.thumbs.thumbsContainerClass),e.thumbs.swiper.on("tap",e.thumbs.onThumbClick),!0},onThumbClick:function(){var e=this,t=e.thumbs.swiper;if(t){var n=t.clickedIndex,i=t.clickedSlide;if(!(i&&z(i).hasClass(e.params.thumbs.slideThumbActiveClass)||null==n)){var a;if(a=t.params.loop?parseInt(z(t.clickedSlide).attr("data-swiper-slide-index"),10):n,e.params.loop){var r=e.activeIndex;e.slides.eq(r).hasClass(e.params.slideDuplicateClass)&&(e.loopFix(),e._clientLeft=e.$wrapperEl[0].clientLeft,r=e.activeIndex);var s=e.slides.eq(r).prevAll('[data-swiper-slide-index="'+a+'"]').eq(0).index(),o=e.slides.eq(r).nextAll('[data-swiper-slide-index="'+a+'"]').eq(0).index();a=void 0===s?o:void 0===o?s:o-r<r-s?o:s}e.slideTo(a)}}},update:function(e){var t=this,n=t.thumbs.swiper;if(n){var i="auto"===n.params.slidesPerView?n.slidesPerViewDynamic():n.params.slidesPerView,a=t.params.thumbs.autoScrollOffset,r=a&&!n.params.loop;if(t.realIndex!==n.realIndex||r){var s,o,l=n.activeIndex;if(n.params.loop){n.slides.eq(l).hasClass(n.params.slideDuplicateClass)&&(n.loopFix(),n._clientLeft=n.$wrapperEl[0].clientLeft,l=n.activeIndex);var u=n.slides.eq(l).prevAll('[data-swiper-slide-index="'+t.realIndex+'"]').eq(0).index(),c=n.slides.eq(l).nextAll('[data-swiper-slide-index="'+t.realIndex+'"]').eq(0).index();s=void 0===u?c:void 0===c?u:c-l==l-u?1<n.params.slidesPerGroup?c:l:c-l<l-u?c:u,o=t.activeIndex>t.previousIndex?"next":"prev"}else o=(s=t.realIndex)>t.previousIndex?"next":"prev";r&&(s+="next"===o?a:-1*a),n.visibleSlidesIndexes&&n.visibleSlidesIndexes.indexOf(s)<0&&(n.params.centeredSlides?s=l<s?s-Math.floor(i/2)+1:s+Math.floor(i/2)-1:l<s&&n.params.slidesPerGroup,n.slideTo(s,e?0:void 0))}var d=1,p=t.params.thumbs.slideThumbActiveClass;if(1<t.params.slidesPerView&&!t.params.centeredSlides&&(d=t.params.slidesPerView),t.params.thumbs.multipleActiveThumbs||(d=1),d=Math.floor(d),n.slides.removeClass(p),n.params.loop||n.params.virtual&&n.params.virtual.enabled)for(var f=0;f<d;f+=1)n.$wrapperEl.children('[data-swiper-slide-index="'+(t.realIndex+f)+'"]').addClass(p);else for(var h=0;h<d;h+=1)n.slides.eq(t.realIndex+h).addClass(p)}}},re=[R,B,{name:"mousewheel",params:{mousewheel:{enabled:!1,releaseOnEdges:!1,invert:!1,forceToAxis:!1,sensitivity:1,eventsTarget:"container",thresholdDelta:null,thresholdTime:null}},create:function(){g(this,{mousewheel:{enabled:!1,lastScrollTime:$(),lastEventBeforeSnap:void 0,recentWheelEvents:[],enable:G.enable,disable:G.disable,handle:G.handle,handleMouseEnter:G.handleMouseEnter,handleMouseLeave:G.handleMouseLeave,animateSlider:G.animateSlider,releaseScroll:G.releaseScroll}})},on:{init:function(e){!e.params.mousewheel.enabled&&e.params.cssMode&&e.mousewheel.disable(),e.params.mousewheel.enabled&&e.mousewheel.enable()},destroy:function(e){e.params.cssMode&&e.mousewheel.enable(),e.mousewheel.enabled&&e.mousewheel.disable()}}},{name:"navigation",params:{navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock"}},create:function(){g(this,{navigation:e({},F)})},on:{init:function(e){e.navigation.init(),e.navigation.update()},toEdge:function(e){e.navigation.update()},fromEdge:function(e){e.navigation.update()},destroy:function(e){e.navigation.destroy()},"enable disable":function(e){var t=e.navigation,n=t.$nextEl,i=t.$prevEl;n&&n[e.enabled?"removeClass":"addClass"](e.params.navigation.lockClass),i&&i[e.enabled?"removeClass":"addClass"](e.params.navigation.lockClass)},click:function(e,t){var n=e.navigation,i=n.$nextEl,a=n.$prevEl,r=t.target;if(e.params.navigation.hideOnClick&&!z(r).is(a)&&!z(r).is(i)){if(e.pagination&&e.params.pagination&&e.params.pagination.clickable&&(e.pagination.el===r||e.pagination.el.contains(r)))return;var s;i?s=i.hasClass(e.params.navigation.hiddenClass):a&&(s=a.hasClass(e.params.navigation.hiddenClass)),!0===s?e.emit("navigationShow"):e.emit("navigationHide"),i&&i.toggleClass(e.params.navigation.hiddenClass),a&&a.toggleClass(e.params.navigation.hiddenClass)}}}},{name:"pagination",params:{pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:function(e){return e},formatFractionTotal:function(e){return e},bulletClass:"swiper-pagination-bullet",bulletActiveClass:"swiper-pagination-bullet-active",modifierClass:"swiper-pagination-",currentClass:"swiper-pagination-current",totalClass:"swiper-pagination-total",hiddenClass:"swiper-pagination-hidden",progressbarFillClass:"swiper-pagination-progressbar-fill",progressbarOppositeClass:"swiper-pagination-progressbar-opposite",clickableClass:"swiper-pagination-clickable",lockClass:"swiper-pagination-lock"}},create:function(){g(this,{pagination:e({dynamicBulletIndex:0},W)})},on:{init:function(e){e.pagination.init(),e.pagination.render(),e.pagination.update()},activeIndexChange:function(e){(e.params.loop||void 0===e.snapIndex)&&e.pagination.update()},snapIndexChange:function(e){e.params.loop||e.pagination.update()},slidesLengthChange:function(e){e.params.loop&&(e.pagination.render(),e.pagination.update())},snapGridLengthChange:function(e){e.params.loop||(e.pagination.render(),e.pagination.update())},destroy:function(e){e.pagination.destroy()},"enable disable":function(e){var t=e.pagination.$el;t&&t[e.enabled?"removeClass":"addClass"](e.params.pagination.lockClass)},click:function(e,t){var n=t.target;if(e.params.pagination.el&&e.params.pagination.hideOnClick&&0<e.pagination.$el.length&&!z(n).hasClass(e.params.pagination.bulletClass)){if(e.navigation&&(e.navigation.nextEl&&n===e.navigation.nextEl||e.navigation.prevEl&&n===e.navigation.prevEl))return;!0===e.pagination.$el.hasClass(e.params.pagination.hiddenClass)?e.emit("paginationShow"):e.emit("paginationHide"),e.pagination.$el.toggleClass(e.params.pagination.hiddenClass)}}}},{name:"scrollbar",params:{scrollbar:{el:null,dragSize:"auto",hide:!1,draggable:!1,snapOnRelease:!0,lockClass:"swiper-scrollbar-lock",dragClass:"swiper-scrollbar-drag"}},create:function(){g(this,{scrollbar:e({isTouched:!1,timeout:null,dragTimeout:null},V)})},on:{init:function(e){e.scrollbar.init(),e.scrollbar.updateSize(),e.scrollbar.setTranslate()},update:function(e){e.scrollbar.updateSize()},resize:function(e){e.scrollbar.updateSize()},observerUpdate:function(e){e.scrollbar.updateSize()},setTranslate:function(e){e.scrollbar.setTranslate()},setTransition:function(e,t){e.scrollbar.setTransition(t)},"enable disable":function(e){var t=e.scrollbar.$el;t&&t[e.enabled?"removeClass":"addClass"](e.params.scrollbar.lockClass)},destroy:function(e){e.scrollbar.destroy()}}},{name:"parallax",params:{parallax:{enabled:!1}},create:function(){g(this,{parallax:e({},X)})},on:{beforeInit:function(e){e.params.parallax.enabled&&(e.params.watchSlidesProgress=!0,e.originalParams.watchSlidesProgress=!0)},init:function(e){e.params.parallax.enabled&&e.parallax.setTranslate()},setTranslate:function(e){e.params.parallax.enabled&&e.parallax.setTranslate()},setTransition:function(e,t){e.params.parallax.enabled&&e.parallax.setTransition(t)}}},{name:"zoom",params:{zoom:{enabled:!1,maxRatio:3,minRatio:1,toggle:!0,containerClass:"swiper-zoom-container",zoomedSlideClass:"swiper-slide-zoomed"}},create:function(){var i=this;g(i,{zoom:e({enabled:!1,scale:1,currentScale:1,isScaling:!1,gesture:{$slideEl:void 0,slideWidth:void 0,slideHeight:void 0,$imageEl:void 0,$imageWrapEl:void 0,maxRatio:3},image:{isTouched:void 0,isMoved:void 0,currentX:void 0,currentY:void 0,minX:void 0,minY:void 0,maxX:void 0,maxY:void 0,width:void 0,height:void 0,startX:void 0,startY:void 0,touchesStart:{},touchesCurrent:{}},velocity:{x:void 0,y:void 0,prevPositionX:void 0,prevPositionY:void 0,prevTime:void 0}},_)});var a=1;Object.defineProperty(i.zoom,"scale",{get:function(){return a},set:function(e){if(a!==e){var t=i.zoom.gesture.$imageEl?i.zoom.gesture.$imageEl[0]:void 0,n=i.zoom.gesture.$slideEl?i.zoom.gesture.$slideEl[0]:void 0;i.emit("zoomChange",e,t,n)}a=e}})},on:{init:function(e){e.params.zoom.enabled&&e.zoom.enable()},destroy:function(e){e.zoom.disable()},touchStart:function(e,t){e.zoom.enabled&&e.zoom.onTouchStart(t)},touchEnd:function(e,t){e.zoom.enabled&&e.zoom.onTouchEnd(t)},doubleTap:function(e,t){!e.animating&&e.params.zoom.enabled&&e.zoom.enabled&&e.params.zoom.toggle&&e.zoom.toggle(t)},transitionEnd:function(e){e.zoom.enabled&&e.params.zoom.enabled&&e.zoom.onTransitionEnd()},slideChange:function(e){e.zoom.enabled&&e.params.zoom.enabled&&e.params.cssMode&&e.zoom.onTransitionEnd()}}},{name:"lazy",params:{lazy:{checkInView:!1,enabled:!1,loadPrevNext:!1,loadPrevNextAmount:1,loadOnTransitionStart:!1,scrollingElement:"",elementClass:"swiper-lazy",loadingClass:"swiper-lazy-loading",loadedClass:"swiper-lazy-loaded",preloaderClass:"swiper-lazy-preloader"}},create:function(){g(this,{lazy:e({initialImageLoaded:!1},Y)})},on:{beforeInit:function(e){e.params.lazy.enabled&&e.params.preloadImages&&(e.params.preloadImages=!1)},init:function(e){e.params.lazy.enabled&&!e.params.loop&&0===e.params.initialSlide&&(e.params.lazy.checkInView?e.lazy.checkInViewOnLoad():e.lazy.load())},scroll:function(e){e.params.freeMode&&!e.params.freeModeSticky&&e.lazy.load()},"scrollbarDragMove resize _freeModeNoMomentumRelease":function(e){e.params.lazy.enabled&&e.lazy.load()},transitionStart:function(e){e.params.lazy.enabled&&(e.params.lazy.loadOnTransitionStart||!e.params.lazy.loadOnTransitionStart&&!e.lazy.initialImageLoaded)&&e.lazy.load()},transitionEnd:function(e){e.params.lazy.enabled&&!e.params.lazy.loadOnTransitionStart&&e.lazy.load()},slideChange:function(e){var t=e.params,n=t.lazy,i=t.cssMode,a=t.watchSlidesVisibility,r=t.watchSlidesProgress,s=t.touchReleaseOnEdges,o=t.resistanceRatio;n.enabled&&(i||(a||r)&&(s||0===o))&&e.lazy.load()}}},{name:"controller",params:{controller:{control:void 0,inverse:!1,by:"slide"}},create:function(){g(this,{controller:e({control:this.params.controller.control},U)})},on:{update:function(e){e.controller.control&&e.controller.spline&&(e.controller.spline=void 0,delete e.controller.spline)},resize:function(e){e.controller.control&&e.controller.spline&&(e.controller.spline=void 0,delete e.controller.spline)},observerUpdate:function(e){e.controller.control&&e.controller.spline&&(e.controller.spline=void 0,delete e.controller.spline)},setTranslate:function(e,t,n){e.controller.control&&e.controller.setTranslate(t,n)},setTransition:function(e,t,n){e.controller.control&&e.controller.setTransition(t,n)}}},{name:"a11y",params:{a11y:{enabled:!0,notificationClass:"swiper-notification",prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide",paginationBulletMessage:"Go to slide {{index}}",slideLabelMessage:"{{index}} / {{slidesLength}}",containerMessage:null,containerRoleDescriptionMessage:null,itemRoleDescriptionMessage:null,slideRole:"group"}},create:function(){g(this,{a11y:e({},K,{liveRegion:z('<span class="'+this.params.a11y.notificationClass+'" aria-live="assertive" aria-atomic="true"></span>')})})},on:{afterInit:function(e){e.params.a11y.enabled&&(e.a11y.init(),e.a11y.updateNavigation())},toEdge:function(e){e.params.a11y.enabled&&e.a11y.updateNavigation()},fromEdge:function(e){e.params.a11y.enabled&&e.a11y.updateNavigation()},paginationUpdate:function(e){e.params.a11y.enabled&&e.a11y.updatePagination()},destroy:function(e){e.params.a11y.enabled&&e.a11y.destroy()}}},{name:"history",params:{history:{enabled:!1,root:"",replaceState:!1,key:"slides"}},create:function(){g(this,{history:e({},J)})},on:{init:function(e){e.params.history.enabled&&e.history.init()},destroy:function(e){e.params.history.enabled&&e.history.destroy()},"transitionEnd _freeModeNoMomentumRelease":function(e){e.history.initialized&&e.history.setHistory(e.params.history.key,e.activeIndex)},slideChange:function(e){e.history.initialized&&e.params.cssMode&&e.history.setHistory(e.params.history.key,e.activeIndex)}}},{name:"hash-navigation",params:{hashNavigation:{enabled:!1,replaceState:!1,watchState:!1}},create:function(){g(this,{hashNavigation:e({initialized:!1},Z)})},on:{init:function(e){e.params.hashNavigation.enabled&&e.hashNavigation.init()},destroy:function(e){e.params.hashNavigation.enabled&&e.hashNavigation.destroy()},"transitionEnd _freeModeNoMomentumRelease":function(e){e.hashNavigation.initialized&&e.hashNavigation.setHash()},slideChange:function(e){e.hashNavigation.initialized&&e.params.cssMode&&e.hashNavigation.setHash()}}},{name:"autoplay",params:{autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!0,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}},create:function(){g(this,{autoplay:e({},Q,{running:!1,paused:!1})})},on:{init:function(e){e.params.autoplay.enabled&&(e.autoplay.start(),C().addEventListener("visibilitychange",e.autoplay.onVisibilityChange),e.autoplay.attachMouseEvents())},beforeTransitionStart:function(e,t,n){e.autoplay.running&&(n||!e.params.autoplay.disableOnInteraction?e.autoplay.pause(t):e.autoplay.stop())},sliderFirstMove:function(e){e.autoplay.running&&(e.params.autoplay.disableOnInteraction?e.autoplay.stop():e.autoplay.pause())},touchEnd:function(e){e.params.cssMode&&e.autoplay.paused&&!e.params.autoplay.disableOnInteraction&&e.autoplay.run()},destroy:function(e){e.autoplay.detachMouseEvents(),e.autoplay.running&&e.autoplay.stop(),C().removeEventListener("visibilitychange",e.autoplay.onVisibilityChange)}}},{name:"effect-fade",params:{fadeEffect:{crossFade:!1}},create:function(){g(this,{fadeEffect:e({},ee)})},on:{beforeInit:function(e){if("fade"===e.params.effect){e.classNames.push(e.params.containerModifierClass+"fade");var t={slidesPerView:1,slidesPerColumn:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!0};se(e.params,t),se(e.originalParams,t)}},setTranslate:function(e){"fade"===e.params.effect&&e.fadeEffect.setTranslate()},setTransition:function(e,t){"fade"===e.params.effect&&e.fadeEffect.setTransition(t)}}},{name:"effect-cube",params:{cubeEffect:{slideShadows:!0,shadow:!0,shadowOffset:20,shadowScale:.94}},create:function(){g(this,{cubeEffect:e({},te)})},on:{beforeInit:function(e){if("cube"===e.params.effect){e.classNames.push(e.params.containerModifierClass+"cube"),e.classNames.push(e.params.containerModifierClass+"3d");var t={slidesPerView:1,slidesPerColumn:1,slidesPerGroup:1,watchSlidesProgress:!0,resistanceRatio:0,spaceBetween:0,centeredSlides:!1,virtualTranslate:!0};se(e.params,t),se(e.originalParams,t)}},setTranslate:function(e){"cube"===e.params.effect&&e.cubeEffect.setTranslate()},setTransition:function(e,t){"cube"===e.params.effect&&e.cubeEffect.setTransition(t)}}},{name:"effect-flip",params:{flipEffect:{slideShadows:!0,limitRotation:!0}},create:function(){g(this,{flipEffect:e({},ne)})},on:{beforeInit:function(e){if("flip"===e.params.effect){e.classNames.push(e.params.containerModifierClass+"flip"),e.classNames.push(e.params.containerModifierClass+"3d");var t={slidesPerView:1,slidesPerColumn:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!0};se(e.params,t),se(e.originalParams,t)}},setTranslate:function(e){"flip"===e.params.effect&&e.flipEffect.setTranslate()},setTransition:function(e,t){"flip"===e.params.effect&&e.flipEffect.setTransition(t)}}},{name:"effect-coverflow",params:{coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0}},create:function(){g(this,{coverflowEffect:e({},ie)})},on:{beforeInit:function(e){"coverflow"===e.params.effect&&(e.classNames.push(e.params.containerModifierClass+"coverflow"),e.classNames.push(e.params.containerModifierClass+"3d"),e.params.watchSlidesProgress=!0,e.originalParams.watchSlidesProgress=!0)},setTranslate:function(e){"coverflow"===e.params.effect&&e.coverflowEffect.setTranslate()},setTransition:function(e,t){"coverflow"===e.params.effect&&e.coverflowEffect.setTransition(t)}}},{name:"thumbs",params:{thumbs:{swiper:null,multipleActiveThumbs:!0,autoScrollOffset:0,slideThumbActiveClass:"swiper-slide-thumb-active",thumbsContainerClass:"swiper-container-thumbs"}},create:function(){g(this,{thumbs:e({swiper:null,initialized:!1},ae)})},on:{beforeInit:function(e){var t=e.params.thumbs;t&&t.swiper&&(e.thumbs.init(),e.thumbs.update(!0))},slideChange:function(e){e.thumbs.swiper&&e.thumbs.update()},update:function(e){e.thumbs.swiper&&e.thumbs.update()},resize:function(e){e.thumbs.swiper&&e.thumbs.update()},observerUpdate:function(e){e.thumbs.swiper&&e.thumbs.update()},setTransition:function(e,t){var n=e.thumbs.swiper;n&&n.setTransition(t)},beforeDestroy:function(e){var t=e.thumbs.swiper;t&&e.thumbs.swiperCreated&&t&&t.destroy()}}}];return j.use(re),j}),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):e.FlipClock=t()}(this,function(){"use strict";function r(e){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function a(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e}function o(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&u(e,t)}function l(e){return(l=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function u(e,t){return(u=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function c(e,t,n){return(c=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(e){return!1}}()?Reflect.construct:function(e,t,n){var i=[null];i.push.apply(i,t);var a=new(Function.bind.apply(e,i));return n&&u(a,n.prototype),a}).apply(null,arguments)}function d(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function p(e,t,n){return(p="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,n){var i=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=l(e)););return e}(e,t);if(i){var a=Object.getOwnPropertyDescriptor(i,t);return a.get?a.get.call(n):a.value}})(e,t,n||e)}function f(e){throw Error(e)}function h(e){if(C(e)){for(var t=arguments.length,n=new Array(1<t?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];return e.call.apply(e,[this].concat(n))}}function n(e){return y(e=y(t=e)||t<0?Math.ceil(e):Math.floor(e))?("-"+e).toString():e;var t}function v(e){return!w(e)&&!b(e)}function t(t){return function(e){return e.map(t).reduce(function(e,t){return e.concat(t)},[])}}function m(e){return t(function(e){return e})(e)}function g(e){return t(function(e){return Array.isArray(e)?g(e):e})(e)}function y(e){return 1/Math.round(e)==-1/0}function b(e){return null===e}function w(e){return void 0===e}function x(e){return"string"==typeof e}function T(e){return e instanceof Array}function E(e){var t=r(e);return null!=e&&!T(e)&&("object"==t||"function"==t)}function C(e){return e instanceof Function}function S(e){return!isNaN(e)}var k=function(){function t(e){s(this,t),this.setAttribute(Object.assign({events:{}},e))}return a(t,[{key:"emit",value:function(e){for(var t=this,n=arguments.length,i=new Array(1<n?n-1:0),a=1;a<n;a++)i[a-1]=arguments[a];return this.events[e]&&this.events[e].forEach(function(e){e.apply(t,i)}),this}},{key:"on",value:function(e,t){return this.events[e]||(this.events[e]=[]),this.events[e].push(t),this}},{key:"off",value:function(e,t){return this.events[e]&&t?this.events[e]=this.events[e].filter(function(e){return e!==t}):this.events[e]=[],this}},{key:"once",value:function(e,t){var n,i,a=this;return n=t,i=function(){return a.off(e,t)},t=function(){return i(n())},this.on(e,t,!0)}},{key:"getAttribute",value:function(e){return this.hasOwnProperty(e)?this[e]:null}},{key:"getAttributes",value:function(){var t=this,n={};return Object.getOwnPropertyNames(this).forEach(function(e){n[e]=t.getAttribute(e)}),n}},{key:"getPublicAttributes",value:function(){var n=this;return Object.keys(this.getAttributes()).filter(function(e){return!e.match(/^\$/)}).reduce(function(e,t){return e[t]=n.getAttribute(t),e},{})}},{key:"setAttribute",value:function(e,t){E(e)?this.setAttributes(e):this[e]=t}},{key:"setAttributes",value:function(e){for(var t in e)this.setAttribute(t,e[t])}},{key:"callback",value:function(e){return h.call(this,e)}},{key:"name",get:function(){return this.constructor.defineName instanceof Function||f("Every class must define its name."),this.constructor.defineName()}},{key:"events",get:function(){return this.$events||{}},set:function(e){this.$events=e}}],[{key:"make",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return c(this,t)}}]),t}();function M(e,n){return n=Object.assign({minimumDigits:0,prependLeadingZero:!0},n),function(e,t){var n=g(e).length;if(n<t)for(var i=0;i<t-n;i++)e[0].unshift("0");return e}(m([e]).map(function(e){return m(g([e]).map(function(e){return(t=e,(n.prependLeadingZero&&1===t.toString().split("").length?"0":"").concat(t)).split("");var t}))}),n.minimumDigits||0)}var L=[{min:48,max:57},{min:65,max:90},{min:97,max:122}];function D(e,t){switch(t){case"number":return parseFloat(e)}return e}function A(e,t){return String.fromCharCode(t(function(e){for(var t in L){var n=e.toString().charCodeAt(0);if(L[t].min<=n&&L[t].max>=n)return L[t]}return null}(e),e.charCodeAt(0)))}var P=function(e){function i(e,t){var n;return s(this,i),(n=d(this,l(i).call(this,Object.assign({format:function(e){return e},prependLeadingZero:!0,minimumDigits:0},t)))).value||(n.value=e),n}return o(i,k),a(i,[{key:"isNaN",value:function(e){function t(){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}(function(){return isNaN(this.value)})},{key:"isNumber",value:function(){return S()}},{key:"clone",value:function(e,t){return new this.constructor(e,Object.assign(this.getPublicAttributes(),t))}},{key:"digits",get:function(){return this.$digits},set:function(e){this.$digits=e,this.minimumDigits=Math.max(this.minimumDigits,g(e).length)}},{key:"value",get:function(){return this.$value},set:function(e){this.$value=e,this.digits=M(this.format(e),{minimumDigits:this.minimumDigits,prependLeadingZero:this.prependLeadingZero})}}],[{key:"defineName",value:function(){return"FaceValue"}}]),i}();function z(n){for(var i=!1,e=arguments.length,t=new Array(1<e?e-1:0),a=1;a<e;a++)t[a-1]=arguments[a];return m(t).forEach(function(e){var t;(b(n)&&b(e)||E(e)&&n instanceof e||C(e)&&!((t=e)instanceof Function&&t.name)&&!0===e(n)||x(e)&&r(n)===e)&&(i=!0)}),i}var N={className:"The className() is not defined.",items:"The items property must be an array.",theme:"The theme property must be an object.",language:"The language must be an object.",date:"The value must be an instance of a Date.",face:"The face must be an instance of a Face class.",element:"The element must be an instance of an HTMLElement",faceValue:"The face must be an instance of a FaceValue class.",timer:"The timer property must be an instance of a Timer class."},O=function(e){function i(e,t){var n;return s(this,i),e instanceof P||!E(e)||(t=e,e=void 0),(n=d(this,l(i).call(this))).setAttributes(Object.assign({autoStart:!0,countdown:!1,animationRate:500},n.defaultAttributes(),t||{})),(b(e)||w(e))&&(e=n.defaultValue()),e&&(n.value=e),n}return o(i,k),a(i,[{key:"interval",value:function(e,t){return this.countdown?this.decrement(e):this.increment(e),h.call(this,t),this.shouldStop(e)&&e.stop(),this.emit("interval")}},{key:"shouldStop",value:function(e){return!w(this.stopAt)&&this.stopAt===e.value.value}},{key:"format",value:function(e,t){return t}},{key:"defaultValue",value:function(){}},{key:"defaultAttributes",value:function(){}},{key:"defaultDataType",value:function(){}},{key:"increment",value:function(e,t){}},{key:"decrement",value:function(e,t){}},{key:"started",value:function(e){}},{key:"stopped",value:function(e){}},{key:"reset",value:function(e){}},{key:"initialized",value:function(e){}},{key:"rendered",value:function(e){}},{key:"mounted",value:function(e){this.autoStart&&e.timer.isStopped&&window.requestAnimationFrame(function(){return e.start(e)})}},{key:"createFaceValue",value:function(t,e){var n=this;return P.make(C(e)&&!e.name?e():e,{minimumDigits:this.minimumDigits,format:function(e){return n.format(t,e)}})}},{key:"dataType",get:function(){return this.defaultDataType()}},{key:"value",get:function(){return this.$value},set:function(e){e instanceof P||(e=this.createFaceValue(e)),this.$value=e}},{key:"stopAt",get:function(){return this.$stopAt},set:function(e){this.$stopAt=e}},{key:"originalValue",get:function(){return this.$originalValue},set:function(e){this.$originalValue=e}}]),i}(),e=Object.freeze({dictionary:{years:"سنوات",months:"شهور",days:"أيام",hours:"ساعات",minutes:"دقائق",seconds:"ثواني"},aliases:["ar","ar-ar","arabic"]}),$=Object.freeze({dictionary:{years:"Anys",months:"Mesos",days:"Dies",hours:"Hores",minutes:"Minuts",seconds:"Segons"},aliases:["ca","ca-es","catalan"]}),I=Object.freeze({dictionary:{years:"Roky",months:"Měsíce",days:"Dny",hours:"Hodiny",minutes:"Minuty",seconds:"Sekundy"},aliases:["cs","cs-cz","cz","cz-cs","czech"]}),j=Object.freeze({dictionary:{years:"År",months:"Måneder",days:"Dage",hours:"Timer",minutes:"Minutter",seconds:"Sekunder"},aliases:["da","da-dk","danish"]}),H=Object.freeze({dictionary:{years:"Jahre",months:"Monate",days:"Tage",hours:"Stunden",minutes:"Minuten",seconds:"Sekunden"},aliases:["de","de-de","german"]}),R=Object.freeze({dictionary:{years:"Years",months:"Months",days:"Days",hours:"Hours",minutes:"Minutes",seconds:"Seconds"},aliases:["en","en-us","english"]}),q=Object.freeze({dictionary:{years:"Años",months:"Meses",days:"Días",hours:"Horas",minutes:"Minutos",seconds:"Segundos"},aliases:["es","es-es","spanish"]}),B=Object.freeze({dictionary:{years:"سال",months:"ماه",days:"روز",hours:"ساعت",minutes:"دقیقه",seconds:"ثانیه"},aliases:["fa","fa-ir","persian"]}),G=Object.freeze({dictionary:{years:"Vuotta",months:"Kuukautta",days:"Päivää",hours:"Tuntia",minutes:"Minuuttia",seconds:"Sekuntia"},aliases:["fi","fi-fi","finnish"]}),F=Object.freeze({dictionary:{years:"Ans",months:"Mois",days:"Jours",hours:"Heures",minutes:"Minutes",seconds:"Secondes"},aliases:["fr","fr-ca","french"]}),W=Object.freeze({dictionary:{years:"שנים",months:"חודש",days:"ימים",hours:"שעות",minutes:"דקות",seconds:"שניות"},aliases:["il","he-il","hebrew"]}),V=Object.freeze({dictionary:{years:"Év",months:"Hónap",days:"Nap",hours:"Óra",minutes:"Perc",seconds:"Másodperc"},aliases:["hu","hu-hu","hungarian"]}),X=Object.freeze({dictionary:{years:"Anni",months:"Mesi",days:"Giorni",hours:"Ore",minutes:"Minuti",seconds:"Secondi"},aliases:["da","da-dk","danish"]}),_=Object.freeze({dictionary:{years:"年",months:"月",days:"日",hours:"時",minutes:"分",seconds:"秒"},aliases:["jp","ja-jp","japanese"]}),Y=Object.freeze({dictionary:{years:"년",months:"월",days:"일",hours:"시",minutes:"분",seconds:"초"},aliases:["ko","ko-kr","korean"]}),U=Object.freeze({dictionary:{years:"Gadi",months:"Mēneši",days:"Dienas",hours:"Stundas",minutes:"Minūtes",seconds:"Sekundes"},aliases:["lv","lv-lv","latvian"]}),K=Object.freeze({dictionary:{years:"Jaren",months:"Maanden",days:"Dagen",hours:"Uren",minutes:"Minuten",seconds:"Seconden"},aliases:["nl","nl-be","dutch"]}),J=Object.freeze({dictionary:{years:"År",months:"Måneder",days:"Dager",hours:"Timer",minutes:"Minutter",seconds:"Sekunder"},aliases:["no","nb","no-nb","norwegian"]}),Z=Object.freeze({dictionary:{years:"Lat",months:"Miesięcy",days:"Dni",hours:"Godziny",minutes:"Minuty",seconds:"Sekundy"},aliases:["pl","pl-pl","polish"]}),Q=Object.freeze({dictionary:{years:"Anos",months:"Meses",days:"Dias",hours:"Horas",minutes:"Minutos",seconds:"Segundos"},aliases:["pt","pt-br","portuguese"]}),ee=Object.freeze({dictionary:{years:"Ani",months:"Luni",days:"Zile",hours:"Ore",minutes:"Minute",seconds:"sSecunde"},aliases:["ro","ro-ro","romana"]}),te=Object.freeze({dictionary:{years:"лет",months:"месяцев",days:"дней",hours:"часов",minutes:"минут",seconds:"секунд"},aliases:["ru","ru-ru","russian"]}),ne=Object.freeze({dictionary:{years:"Roky",months:"Mesiace",days:"Dni",hours:"Hodiny",minutes:"Minúty",seconds:"Sekundy"},aliases:["sk","sk-sk","slovak"]}),ie=Object.freeze({dictionary:{years:"År",months:"Månader",days:"Dagar",hours:"Timmar",minutes:"Minuter",seconds:"Sekunder"},aliases:["sv","sv-se","swedish"]}),ae=Object.freeze({dictionary:{years:"ปี",months:"เดือน",days:"วัน",hours:"ชั่วโมง",minutes:"นาที",seconds:"วินาที"},aliases:["th","th-th","thai"]}),re=Object.freeze({dictionary:{years:"Yıl",months:"Ay",days:"Gün",hours:"Saat",minutes:"Dakika",seconds:"Saniye"},aliases:["tr","tr-tr","turkish"]}),se=Object.freeze({dictionary:{years:"роки",months:"місяці",days:"дні",hours:"години",minutes:"хвилини",seconds:"секунди"},aliases:["ua","ua-ua","ukraine"]}),oe=Object.freeze({dictionary:{years:"Năm",months:"Tháng",days:"Ngày",hours:"Giờ",minutes:"Phút",seconds:"Giây"},aliases:["vn","vn-vn","vietnamese"]}),le=Object.freeze({dictionary:{years:"年",months:"月",days:"日",hours:"时",minutes:"分",seconds:"秒"},aliases:["zh","zh-cn","chinese"]}),ue=Object.freeze({dictionary:{years:"年",months:"月",days:"日",hours:"時",minutes:"分",seconds:"秒"},aliases:["zh-tw"]}),ce=Object.freeze({Arabic:e,Catalan:$,Czech:I,Danish:j,German:H,English:R,Spanish:q,Persian:B,Finnish:G,French:F,Hebrew:W,Hungarian:V,Italian:X,Japanese:_,Korean:Y,Latvian:U,Dutch:K,Norwegian:J,Polish:Z,Portuguese:Q,Romanian:ee,Russian:te,Slovak:ne,Swedish:ie,Thai:ae,Turkish:re,Ukrainian:se,Vietnamese:oe,Chinese:le,TraditionalChinese:ue});function de(t){return t?ce[t.toLowerCase()]||Object.values(ce).find(function(e){return-1!==e.aliases.indexOf(t)}):null}function pe(t,e){return T(e)&&e.filter(v).forEach(function(e){e instanceof HTMLElement&&t.appendChild(e)}),t}function fe(e,t,n){return e instanceof HTMLElement||(e=document.createElement(e)),function(e,t){if(E(t))for(var n in t)e.setAttribute(n,t[n])}(e,E(t)?t:n),E(t)||T(t)?pe(e,t):e.innerHTML=t,e}var he=function(e){function n(e){var t;if(s(this,n),(t=d(this,l(n).call(this,Object.assign({parent:null},e)))).theme||f("".concat(t.name," does not have a theme defined.")),t.language||f("".concat(t.name," does not have a language defined.")),!t.theme[t.name])throw new Error("".concat(t.name," cannot be rendered because it has no template."));return t}return o(n,k),a(n,[{key:"translate",value:function(e){return t=e,n=this.language,((i=x(n)?de(n):n).dictionary||i)[t]||t;var t,n,i}},{key:"t",value:function(e){return this.translate(e)}},{key:"render",value:function(){var e,t,n=fe("div",{class:"flip-clock"===this.className?this.className:"flip-clock-"+this.className});return this.theme[this.name](n,this),this.el?this.el.innerHTML!==n.innerHTML&&(this.el=(e=n,(t=this.el).parentNode?(t.parentNode.replaceChild(e,t),e):t)):this.el=n,this.el}},{key:"mount",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1];return this.render(),this.parent=e,t?this.parent.insertBefore(this.el,t):this.parent.appendChild(this.el),this.el}},{key:"className",get:function(){return this.constructor.defineName().replace(/([a-z])([A-Z])/g,"$1-$2").replace(/\s+/g,"-").toLowerCase()}},{key:"el",get:function(){return this.$el},set:function(e){z(e,null,HTMLElement)||f(N.element),this.$el=e}},{key:"parent",get:function(){return this.$parent},set:function(e){this.$parent=e}},{key:"theme",get:function(){return this.$theme},set:function(e){z(e,"object")||f(N.value),this.$theme=e}},{key:"language",get:function(){return this.$language},set:function(e){x(e)&&(e=de(e)),z(e,"object")||f(N.language),this.$language=e}}]),n}(),ve=function(e){function t(){return s(this,t),d(this,l(t).apply(this,arguments))}return o(t,he),a(t,null,[{key:"defineName",value:function(){return"Divider"}}]),t}(),me=function(e){function n(e,t){return s(this,n),d(this,l(n).call(this,Object.assign({value:e},E(e)?e:null,t)))}return o(n,he),a(n,null,[{key:"defineName",value:function(){return"ListItem"}}]),n}(),ge=function(e){function n(e,t){return s(this,n),d(this,l(n).call(this,Object.assign({value:e,items:[]},E(e)?e:null,t)))}return o(n,he),a(n,[{key:"createListItem",value:function(e,t){var n=new me(e,Object.assign({theme:this.theme,language:this.language},t));return this.$items.push(n),n}},{key:"value",get:function(){return this.$value},set:function(e){this.$value=e}},{key:"items",get:function(){return this.$items},set:function(e){this.$items=e}}],[{key:"defineName",value:function(){return"List"}}]),n}(),ye=function(e){function n(e,t){return s(this,n),d(this,l(n).call(this,Object.assign({items:T(e)?e:[]},E(e)?e:null,t)))}return o(n,he),a(n,null,[{key:"defineName",value:function(){return"Group"}}]),n}(),be=function(e){function n(e,t){return s(this,n),d(this,l(n).call(this,Object.assign({label:e},E(e)?e:null,t)))}return o(n,he),a(n,null,[{key:"defineName",value:function(){return"Label"}}]),n}(),we=function(e){function t(e){return s(this,t),d(this,l(t).call(this,Object.assign({count:0,handle:null,started:null,running:!1,interval:S(e)?e:null},E(e)?e:null)))}return o(t,k),a(t,[{key:"reset",value:function(e){var t=this;return this.stop(function(){t.count=0,t.start(function(){return h.call(t,e)}),t.emit("reset")}),this}},{key:"start",value:function(t){var n=this;this.started=new Date,this.lastLoop=Date.now(),this.running=!0,this.emit("start");return function e(){return Date.now()-n.lastLoop>=n.interval&&(h.call(n,t),n.lastLoop=Date.now(),n.emit("interval"),n.count++),n.handle=window.requestAnimationFrame(e),n}()}},{key:"stop",value:function(e){var t=this;return this.isRunning&&setTimeout(function(){window.cancelAnimationFrame(t.handle),t.running=!1,h.call(t,e),t.emit("stop")}),this}},{key:"elapsed",get:function(){return this.lastLoop?this.lastLoop-(this.started?this.started.getTime():(new Date).getTime()):0}},{key:"isRunning",get:function(){return!0===this.running}},{key:"isStopped",get:function(){return!1===this.running}}],[{key:"defineName",value:function(){return"Timer"}}]),t}(),xe=function(e){function t(){return s(this,t),d(this,l(t).apply(this,arguments))}return o(t,O),a(t,[{key:"increment",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:1;e.value=this.value.value+t}},{key:"decrement",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:1;e.value=this.value.value-t}}],[{key:"defineName",value:function(){return"Counter"}}]),t}(),Te=function(e){function t(){return s(this,t),d(this,l(t).apply(this,arguments))}return o(t,O),a(t,[{key:"defaultDataType",value:function(){return Date}},{key:"defaultAttributes",value:function(){return{showSeconds:!0,showLabels:!0}}},{key:"shouldStop",value:function(e){if(b(e.stopAt)||w(e.stopAt))return!1;if(this.stopAt instanceof Date)return this.countdown?this.stopAt.getTime()>=this.value.value.getTime():this.stopAt.getTime()<=this.value.value.getTime();if(S(this.stopAt)){var t=Math.floor((this.value.value.getTime()-this.originalValue.getTime())/1e3);return this.countdown?this.stopAt>=t:this.stopAt<=t}throw new Error("the stopAt property must be an instance of Date or Number.")}},{key:"increment",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0;e.value=new Date(this.value.value.getTime()+t+((new Date).getTime()-e.timer.lastLoop))}},{key:"decrement",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0;e.value=new Date(this.value.value.getTime()-t-((new Date).getTime()-e.timer.lastLoop))}},{key:"format",value:function(e,t){var n=e.timer.isRunning?e.timer.started:new Date(Date.now()-50);return[[this.getMinutes(t,n)],this.showSeconds?[this.getSeconds(t,n)]:null].filter(v)}},{key:"getMinutes",value:function(e,t){return n(this.getTotalSeconds(e,t)/60)}},{key:"getSeconds",value:function(e,t){var n=this.getTotalSeconds(e,t);return Math.abs(Math.ceil(60===n?0:n%60))}},{key:"getTotalSeconds",value:function(e,t){return e.getTime()===t.getTime()?0:Math.round((e.getTime()-t.getTime())/1e3)}}],[{key:"defineName",value:function(){return"MinuteCounter"}}]),t}(),Ee=function(e){function n(){return s(this,n),d(this,l(n).apply(this,arguments))}return o(n,Te),a(n,[{key:"format",value:function(e,t){var n=e.timer.started?t:new Date,i=e.originalValue||t,a=this.countdown?i:n,r=this.countdown?n:i,s=[[this.getHours(a,r)],[this.getMinutes(a,r)]];return this.showSeconds&&s.push([this.getSeconds(a,r)]),s}},{key:"getMinutes",value:function(e,t){return Math.abs(p(l(n.prototype),"getMinutes",this).call(this,e,t)%60)}},{key:"getHours",value:function(e,t){return Math.floor(this.getTotalSeconds(e,t)/60/60)}}],[{key:"defineName",value:function(){return"HourCounter"}}]),n}(),Ce=function(e){function n(){return s(this,n),d(this,l(n).apply(this,arguments))}return o(n,Ee),a(n,[{key:"format",value:function(e,t){var n=e.started?t:new Date,i=e.originalValue||t,a=this.countdown?i:n,r=this.countdown?n:i,s=[[this.getDays(a,r)],[this.getHours(a,r)],[this.getMinutes(a,r)]];return this.showSeconds&&s.push([this.getSeconds(a,r)]),s}},{key:"getDays",value:function(e,t){return Math.floor(this.getTotalSeconds(e,t)/60/60/24)}},{key:"getHours",value:function(e,t){return Math.abs(p(l(n.prototype),"getHours",this).call(this,e,t)%24)}}],[{key:"defineName",value:function(){return"DayCounter"}}]),n}(),Se=function(e){function t(){return s(this,t),d(this,l(t).apply(this,arguments))}return o(t,O),a(t,[{key:"defaultDataType",value:function(){return Date}},{key:"defaultValue",value:function(){return new Date}},{key:"defaultAttributes",value:function(){return{showSeconds:!0,showLabels:!1}}},{key:"format",value:function(e,t){t||(t=new Date);var n=[[t.getHours()],[t.getMinutes()]];return this.showSeconds&&n.push([t.getSeconds()]),n}},{key:"increment",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0;e.value=new Date(this.value.value.getTime()+t+((new Date).getTime()-e.timer.lastLoop))}},{key:"decrement",value:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0;e.value=new Date(this.value.value.getTime()-t-((new Date).getTime()-e.timer.lastLoop))}}],[{key:"defineName",value:function(){return"TwentyFourHourClock"}}]),t}(),ke=function(e){function t(){return s(this,t),d(this,l(t).apply(this,arguments))}return o(t,Se),a(t,[{key:"defaultAttributes",value:function(){return{showLabels:!1,showSeconds:!0,showMeridium:!0}}},{key:"format",value:function(e,t){t||(t=new Date);var n=t.getHours(),i=[12<n?n-12:0===n?12:n,t.getMinutes()];return this.meridium=12<n?"pm":"am",this.showSeconds&&i.push(t.getSeconds()),i}}],[{key:"defineName",value:function(){return"TwelveHourClock"}}]),t}(),Me=function(e){function n(){return s(this,n),d(this,l(n).apply(this,arguments))}return o(n,Ce),a(n,[{key:"format",value:function(e,t){var n=e.timer.started?t:new Date,i=e.originalValue||t,a=this.countdown?i:n,r=this.countdown?n:i,s=[[this.getWeeks(a,r)],[this.getDays(a,r)],[this.getHours(a,r)],[this.getMinutes(a,r)]];return this.showSeconds&&s.push([this.getSeconds(a,r)]),s}},{key:"getWeeks",value:function(e,t){return Math.floor(this.getTotalSeconds(e,t)/60/60/24/7)}},{key:"getDays",value:function(e,t){return Math.abs(p(l(n.prototype),"getDays",this).call(this,e,t)%7)}}],[{key:"defineName",value:function(){return"WeekCounter"}}]),n}(),Le=function(e){function n(){return s(this,n),d(this,l(n).apply(this,arguments))}return o(n,Me),a(n,[{key:"format",value:function(e,t){var n=e.timer.started?t:new Date,i=e.originalValue||t,a=this.countdown?i:n,r=this.countdown?n:i,s=[[this.getYears(a,r)],[this.getWeeks(a,r)],[this.getDays(a,r)],[this.getHours(a,r)],[this.getMinutes(a,r)]];return this.showSeconds&&s.push([this.getSeconds(a,r)]),s}},{key:"getYears",value:function(e,t){return Math.floor(Math.max(0,this.getTotalSeconds(e,t)/60/60/24/7/52))}},{key:"getWeeks",value:function(e,t){return Math.abs(p(l(n.prototype),"getWeeks",this).call(this,e,t)%52)}}],[{key:"defineName",value:function(){return"YearCounter"}}]),n}(),De=Object.freeze({Counter:xe,DayCounter:Ce,MinuteCounter:Te,HourCounter:Ee,TwelveHourClock:ke,TwentyFourHourClock:Se,WeekCounter:Me,YearCounter:Le});function Ae(e,t){return e?e.childNodes?e.childNodes[t]:e[t]:null}function Pe(e,t){t.createDivider().mount(e,e.childNodes[1]),t.face.showSeconds&&t.createDivider().mount(e,e.childNodes[3]),t.face.showLabels&&(t.createLabel("hours").mount(e.childNodes[0]),t.createLabel("minutes").mount(e.childNodes[2]),t.face.showSeconds&&t.createLabel("seconds").mount(e.childNodes[4]))}var ze={face:xe,theme:{Divider:function(e,t){pe(e,[fe("div",{class:"flip-clock-dot top"}),fe("div",{class:"flip-clock-dot bottom"})])},FlipClock:function(e,s){pe(e,s.value.digits.map(function(e,t){var r=Ae(s.el?s.el.querySelectorAll(".flip-clock-group"):null,t),n=e.map(function(e,t){var n,i=Ae(r?r.querySelectorAll(".flip-clock-list"):null,t),a=(n=i)?n.querySelector(".flip-clock-list-item:first-child .top").innerHTML:null;return s.createList(e,{domValue:a,countdown:s.countdown,animationRate:s.face.animationRate||s.face.delay})});return s.createGroup(n)}).map(function(e){return e.render()}))},Group:function(e,t){pe(e,t.items.map(function(e){return e.render()}))},Label:function(e,t){e.innerHTML=t.t(t.label)},List:function(e,t){var n,i,a=t.domValue||(t.countdown?D((n=t.value).toString().split("").map(function(e){return A(e,function(e,t){return!e||t<e.max?t+1:e.min})}).join(""),r(n)):D((i=t.value).toString().split("").map(function(e){return A(e,function(e,t){return!e||t>e.min?t-1:e.max})}).join(""),r(i)));t.domValue&&t.domValue!==t.value&&e.classList.add("flip"),e.style.animationDelay="".concat(t.animationRate/2,"ms"),e.style.animationDuration="".concat(t.animationRate/2,"ms"),t.items=[t.createListItem(t.value,{active:!0}),t.createListItem(a,{active:!1})],pe(e,t.items.map(function(e){return e.render()}))},ListItem:function(e,t){var n=!0===t.active?"active":!1===t.active?"before":null;e.classList.add(n),pe(e,[fe("div",[fe("div",t.value,{class:"top"}),fe("div",t.value,{class:"bottom"})],{class:"flip-clock-list-item-inner"})])},faces:Object.freeze({DayCounter:function(e,t){t.createDivider().mount(e,e.childNodes[1]),t.createDivider().mount(e,e.childNodes[3]),t.face.showSeconds&&t.createDivider().mount(e,e.childNodes[5]),t.face.showLabels&&(t.createLabel("days").mount(e.childNodes[0]),t.createLabel("hours").mount(e.childNodes[2]),t.createLabel("minutes").mount(e.childNodes[4]),t.face.showSeconds&&t.createLabel("seconds").mount(e.childNodes[6]))},HourCounter:function(e,t){t.createDivider().mount(e,e.childNodes[1]),t.face.showSeconds&&t.createDivider().mount(e,e.childNodes[3]),t.face.showLabels&&(t.createLabel("hours").mount(e.childNodes[0]),t.createLabel("minutes").mount(e.childNodes[2]),t.face.showSeconds&&t.createLabel("seconds").mount(e.childNodes[4]))},MinuteCounter:function(e,t){t.face.showSeconds&&t.createDivider().mount(e,e.childNodes[1]),t.face.showLabels&&(t.createLabel("minutes").mount(e.childNodes[0]),t.face.showSeconds&&t.createLabel("seconds").mount(e.childNodes[2]))},TwelveHourClock:function(e,t){if(Pe(e,t),t.face.showMeridium&&t.face.meridium){var n=t.createLabel(t.face.meridium),i=e.childNodes[e.childNodes.length-1];n.mount(i).classList.add("flip-clock-meridium")}},TwentyFourHourClock:Pe,WeekCounter:function(e,t){t.createDivider().mount(e,e.childNodes[1]),t.createDivider().mount(e,e.childNodes[3]),t.createDivider().mount(e,e.childNodes[5]),t.face.showSeconds&&t.createDivider().mount(e,e.childNodes[7]),t.face.showLabels&&(t.createLabel("weeks").mount(e.childNodes[0]),t.createLabel("days").mount(e.childNodes[2]),t.createLabel("hours").mount(e.childNodes[4]),t.createLabel("minutes").mount(e.childNodes[6]),t.face.showSeconds&&t.createLabel("seconds").mount(e.childNodes[8]))},YearCounter:function(e,t){t.createDivider().mount(e,e.childNodes[1]),t.createDivider().mount(e,e.childNodes[3]),t.createDivider().mount(e,e.childNodes[5]),t.createDivider().mount(e,e.childNodes[7]),t.face.showSeconds&&t.createDivider().mount(e,e.childNodes[9]),t.face.showLabels&&(t.createLabel("years").mount(e.childNodes[0]),t.createLabel("weeks").mount(e.childNodes[2]),t.createLabel("days").mount(e.childNodes[4]),t.createLabel("hours").mount(e.childNodes[6]),t.createLabel("minutes").mount(e.childNodes[8]),t.face.showSeconds&&t.createLabel("seconds").mount(e.childNodes[10]))}})},language:R};return function(e){function r(e,t,n){var i;s(this,r),z(e,HTMLElement)||f(N.element),E(t)&&!n&&(n=t,t=void 0);var a=n.face||ze.face;return delete n.face,(i=d(this,l(r).call(this,Object.assign({originalValue:t,theme:ze.theme,language:ze.language,timer:we.make(n.interval||1e3)},n)))).face||(i.face=a),i.mount(e),i}return o(r,he),a(r,[{key:"mount",value:function(e){return p(l(r.prototype),"mount",this).call(this,e),this.face.mounted(this),this}},{key:"render",value:function(){return p(l(r.prototype),"render",this).call(this),this.theme.faces[this.face.name]&&this.theme.faces[this.face.name](this.el,this),this.face.rendered(this),this.el}},{key:"start",value:function(e){var t=this;return this.timer.started||(this.value=this.originalValue),w(this.face.stopAt)&&(this.face.stopAt=this.stopAt),w(this.face.originalValue)&&(this.face.originalValue=this.originalValue),this.timer.start(function(){t.face.interval(t,e)}),this.face.started(this),this.emit("start")}},{key:"stop",value:function(e){return this.timer.stop(e),this.face.stopped(this),this.emit("stop")}},{key:"reset",value:function(e){var t=this;return this.value=this.originalValue,this.timer.reset(function(){return t.interval(t,e)}),this.face.reset(this),this.emit("reset")}},{key:"increment",value:function(e){return this.face.increment(this,e),this}},{key:"decrement",value:function(e){return this.face.decrement(this,e),this}},{key:"createDivider",value:function(e){return ve.make(Object.assign({theme:this.theme,language:this.language},e))}},{key:"createList",value:function(e,t){return ge.make(e,Object.assign({theme:this.theme,language:this.language},t))}},{key:"createLabel",value:function(e,t){return be.make(e,Object.assign({theme:this.theme,language:this.language},t))}},{key:"createGroup",value:function(e,t){return ye.make(e,Object.assign({theme:this.theme,language:this.language},t))}},{key:"face",get:function(){return this.$face},set:function(e){z(e,[O,"string","function"])||f(N.face),this.$face=(De[e]||e).make(Object.assign(this.getPublicAttributes(),{originalValue:this.face?this.face.originalValue:void 0})),this.$face.initialized(this),this.value?this.$face.value=this.face.createFaceValue(this,this.value.value):this.value||(this.value=this.originalValue),this.el&&this.render()}},{key:"stopAt",get:function(){return C(this.$stopAt)?this.$stopAt(this):this.$stopAt},set:function(e){this.$stopAt=e}},{key:"timer",get:function(){return this.$timer},set:function(e){z(e,we)||f(N.timer),this.$timer=e}},{key:"value",get:function(){return this.face?this.face.value:null},set:function(e){if(!this.face)throw new Error("A face must be set before setting a value.");e instanceof P?this.face.value=e:this.value?this.face.value=this.face.value.clone(e):this.face.value=this.face.createFaceValue(this,e),this.el&&this.render()}},{key:"originalValue",get:function(){return C(this.$originalValue)&&!this.$originalValue.name?this.$originalValue():w(this.$originalValue)||b(this.$originalValue)?this.face?this.face.defaultValue():void 0:this.$originalValue},set:function(e){this.$originalValue=e}}],[{key:"defineName",value:function(){return"FlipClock"}},{key:"setDefaultFace",value:function(e){z(e,O)||f(N.face),ze.face=e}},{key:"setDefaultTheme",value:function(e){z(e,"object")||f(N.theme),ze.theme=e}},{key:"setDefaultLanguage",value:function(e){z(e,"object")||f(N.language),ze.language=e}},{key:"defaults",get:function(){return ze}}]),r}()});