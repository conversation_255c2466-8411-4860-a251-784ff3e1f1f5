<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>404</title>
    <meta name="viewport" content="width=device-width,maximum-scale=1,minimum-scale=1,initial-scale=1,user-scalable=no,shrink-to-fit=no">
    <style>
        @font-face {
            font-family: 'Open Sans';
            font-style: normal;
            font-weight: 400;
            src: url('/assets/fonts/opensans/open-sans-v16-latin-regular.eot');
            src: local('Open Sans Regular'), local('OpenSans-Regular'),
            url('/assets/fonts/opensans/open-sans-v16-latin-regular.eot?#iefix') format('embedded-opentype'),
            url('/assets/fonts/opensans/open-sans-v16-latin-regular.woff2') format('woff2'),
            url('/assets/fonts/opensans/open-sans-v16-latin-regular.woff') format('woff'),
            url('/assets/fonts/opensans/open-sans-v16-latin-regular.ttf') format('truetype'),
            url('/assets/fonts/opensans/open-sans-v16-latin-regular.svg#OpenSans') format('svg');
            font-display: swap;
        }
        @font-face {
            font-family: 'Open Sans';
            font-style: normal;
            font-weight: 700;
            src: url('/assets/fonts/opensans/open-sans-v16-latin-700.eot');
            src: local('Open Sans Bold'), local('OpenSans-Bold'),
            url('/assets/fonts/opensans/open-sans-v16-latin-700.eot?#iefix') format('embedded-opentype'),
            url('/assets/fonts/opensans/open-sans-v16-latin-700.woff2') format('woff2'),
            url('/assets/fonts/opensans/open-sans-v16-latin-700.woff') format('woff'),
            url('/assets/fonts/opensans/open-sans-v16-latin-700.ttf') format('truetype'),
            url('/assets/fonts/opensans/open-sans-v16-latin-700.svg#OpenSans') format('svg');
            font-display: swap;
        }
        @font-face {
            font-family: 'Open Sans';
            font-style: normal;
            font-weight: 800;
            src: url('/assets/fonts/opensans/open-sans-v16-latin-800.eot');
            src: local('Open Sans ExtraBold'), local('OpenSans-ExtraBold'),
            url('/assets/fonts/opensans/open-sans-v16-latin-800.eot?#iefix') format('embedded-opentype'),
            url('/assets/fonts/opensans/open-sans-v16-latin-800.woff2') format('woff2'),
            url('/assets/fonts/opensans/open-sans-v16-latin-800.woff') format('woff'),
            url('/assets/fonts/opensans/open-sans-v16-latin-800.ttf') format('truetype'),
            url('/assets/fonts/opensans/open-sans-v16-latin-800.svg#OpenSans') format('svg');
            font-display: swap;
        }
        @font-face {
            font-family: 'Oswald';
            font-style: normal;
            font-weight: 400;
            src: url('/assets/fonts/oswald/oswald-v17-latin-regular.eot');
            src: local('Oswald Regular'), local('Oswald-Regular'),
            url('/assets/fonts/oswald/oswald-v17-latin-regular.eot?#iefix') format('embedded-opentype'),
            url('/assets/fonts/oswald/oswald-v17-latin-regular.woff2') format('woff2'),
            url('/assets/fonts/oswald/oswald-v17-latin-regular.woff') format('woff'),
            url('/assets/fonts/oswald/oswald-v17-latin-regular.ttf') format('truetype'),
            url('/assets/fonts/oswald/oswald-v17-latin-regular.svg#Oswald') format('svg');
            font-display: swap;
        }

        * {
            -webkit-tap-highlight-color: transparent;
            -webkit-text-size-adjust: 100%;
            -webkit-font-smoothing: antialiased;
            box-sizing: border-box;
            outline: none;
        }
        body {
            -webkit-font-smoothing: antialiased;
            background-color: #181717;
            color: #fff;
            font-family: 'Open Sans', sans-serif;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.3;
            margin: 0;
            overflow-x: hidden;
            padding: 0;
            position: relative;
        }

        .page404 {
            background: url("https://skywindgroup.com/assets/pa/images/404-maintence.png") no-repeat center/cover;
        }

        .blockcenter {
            display: flex;
            justify-content: center;
            flex-direction: column;
            width: 100%;
            height: 100vh;
            text-align: center;
            max-width: 1920px;
            margin: 0 auto;
        }

        .blockcenter__offer {
            font-size: 20px;
            line-height: 35px;
            max-width: 370px;
            margin-left: auto;
            margin-right: auto;
            margin-top: 8.33vh;
            margin-bottom: 8.33vh;
        }
        @media (min-width: 1921px), (min-height: 1081px) {
            .blockcenter__offer {
                margin-top: 90px;
                margin-bottom: 90px;
            }
        }
        @media (max-width: 640px) and (orientation: landscape) {
            .blockcenter__offer {
                margin-top: 40px;
                margin-bottom: 40px;
            }
        }
        @media (max-width: 480px) {
            .blockcenter__offer {
                width: 100%;
            }
        }
        @media (max-width: 360px) {
            .blockcenter__offer {
                font-size: 18px;
                line-height: 30px;
            }
        }

        .blockcenter__title {
            font-size: 35px;
            line-height: 1;
            letter-spacing: .32em;
            position: relative;
            display: block;
            font-family: Oswald,sans-serif;
            font-weight: 400;
            text-transform: uppercase;
            margin-bottom: 0.5rem;
            margin-top: 0;
        }
        @media (max-width: 410px) {
            .blockcenter__title {
                font-size: 30px;
            }
        }
        @media (max-width: 360px) {
            .blockcenter__title {
                font-size: 26px;
            }
        }

        .blockcenter__title::before {
            z-index: -1;
            text-transform: none;
            white-space: nowrap;
            position: absolute;
            left: 50px;
            bottom: -25px;
            content: attr(data-text);
            font-size: 216px;
            line-height: 1;
            font-family: 'Open Sans', sans-serif;;
            font-weight: 800;
            letter-spacing: normal;
            color: rgba(255, 255, 255, 0.07);
        }
        @media (max-width: 1024px) {
            .blockcenter__title::before {
                left: 50%;
                transform: translateX(-50%);
                font-size: 150px;
            }
        }
        @media (max-width: 812px) {
            .blockcenter__title::before {
                font-size: 100px;
                bottom: -10px;
            }
        }
        @media (max-width: 480px) {
            .blockcenter__title::before {
                font-size: 70px;
            }
        }

        .blockcenter__btn {
            outline: 0;
            cursor: pointer;
            border: none;
            display: inline-block;
            text-decoration: none;
            text-align: center;
            font-size: 14px;
            line-height: 16px;
            letter-spacing: .12em;
            font-weight: 700;
            text-transform: uppercase;
            white-space: nowrap;
            border-radius: 50px;
            transition: all .2s ease;
            padding: 19px 69px;
            position: relative;
            z-index: 4;
            width: 238px;
            background-color: #8dd363;
            color: #181717;
            margin: 0 auto;
        }

        .accent {
            color: #8dd363;
        }
    </style>
</head>
<body>
  <section id="page404" class="page404">
      <div class="blockcenter">
        <h2 class="title blockcenter__title" data-text="404 error">Page not<span class="accent">found</span>
        </h2>
        <p class="blockcenter__offer">The page you are looking for might have been removed or had its name
          changed.</p>
        <a class="button button--primary blockcenter__btn" href="/">go home</a></div>
  </section>
</body>
</html>