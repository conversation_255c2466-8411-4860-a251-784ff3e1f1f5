@import "../../global/variables";

.device-mobile {

  &.in-landscape {
    & .modal-dialog {
      min-height: 98vh;
      margin: auto;
    }
  }

  & .dialog-modal-area .modal-dialog {
    max-width: 98vw;

    & .modal-body {
      padding: 4px;
    }
  }

}

.dialog-modal-area {

  .modal-dialog {
    max-width: 92vw;
    margin-right: auto;
    margin-left: auto;

    & .modal-body p {
      padding: 2px 10px;
    }

    .modal-content {
      background-color: $dialogBackgroundColor;
      box-shadow: 0 0 18.4px 1.6px rgba(0, 0, 0, .7);
      position: absolute;
      top: 14px;
      right: 14px;
      bottom: 14px;
      left: 14px;
      width: auto;
    }

    iframe {
      position: relative;
      width: 100%;
      height: 100%;
      border: none;
    }

    video {
      display: block;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center center;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
    }

    .modal-header {
      background-color: #000;
      align-items: center;
      padding: 6px 8px 6px 20px;
      border: none;

      .modal-title {
        color: $secondaryColor;
        font-size: 16px;
        padding-right: 10px;

        @media (min-width: 768px) {
          font-size: 20px;
        }
      }

      .btn-close {
        color: $secondaryColor;
        background-color: $mainDark;
        border-radius: 50%;
        width: 28px;
        height: 28px;
        position: relative;
        margin: auto 0;
        align-items: center;
        justify-content: center;
        display: flex;
        opacity: 1;

        @media (min-width: 768px) {
          width: 40px;
          height: 40px;
        }

        &:hover, &:active {
          opacity: 1;
        }

        &:after {
          content: '';
          background: url(data:image/png;base64,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) no-repeat 50% 50%;
          background-size: 14px 11px;
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;

          @media (min-width: 768px) {
            background-size: 17px 13px;
          }
        }

        span {
          display: none;
        }
      }
    }

    .modal-body {
      flex: 1;
    }

    .modal-footer {
      border: none;

      &:empty {
        padding: 0;
      }
    }
  }
}

#dialog-marketing-kit {
  .modal-body {
    overflow-y: auto;
  }

  .accordion {
    margin: -1em -1em 0 -1em;
  }
}

#dialog-product-sheet,
#video-dialog {
  .modal-body {
    padding: 0;
  }
}
