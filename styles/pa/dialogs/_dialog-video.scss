.dialog-video {
  & .modal-body {
    display: flex;
    flex-direction: column;
    position: relative;

    & .video-wrapper {
      position: relative;
      width: 100%;
      height: 100%;
      background: linear-gradient(0deg, black, transparent);
    }

    & .list-group {
      flex-shrink: 0;
      padding: 0.75rem;
      flex-direction: row;
      min-width: 240px;
      overflow-x: auto;
      justify-content: center;

      & .list-group-item {
        display: flex;
        background-color: transparent;
        border: 1px solid $secondaryColor;
        border-radius: 8px;
        padding: 0;
        overflow: hidden;
        line-height: 1.4rem;
        min-height: 40px;
        margin: 0 .375rem;

        &.coming-soon {
          opacity: 0.4;
        }

        & a {
          padding: 6px 16px;
          font-size: 0.875rem;
          min-width: 120px;
          box-sizing: border-box;
          font-weight: 500;
          line-height: 1.75;
          border-radius: 4px;
          letter-spacing: 0.02857em;
          background: transparent;
          border-color: $secondaryColor;
          display: flex;
          justify-content: center;
          align-items: center;
          text-transform: uppercase;

          & .video-icon {
            width: 32px;
            margin-right: 14px;
          }

          & .video-title {
            flex-wrap: nowrap;
            white-space: nowrap;
            line-height: 1;
          }

          &:active, &.active, &:hover {
            color: $mainDark;
            text-decoration: none;
            background-color: $secondaryColor;

            & svg {
              color: #000 !important;
            }
          }
        }
      }
    }
  }
}
