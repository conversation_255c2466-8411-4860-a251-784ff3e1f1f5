@import "../../global/variables";

.device-mobile.in-portrait {
  & .modal-body {
    & .certificates-info {
      & .certificates-area {
        flex-direction: column;

        & .list-group {
          flex: 0.23;
        }

        & .certificate-iframe, & .coming-soon-area {
          flex: 0.77;
        }

      }
    }
  }
}

.device-desktop {
  & .certificates-area {
    flex-direction: row;

  }
}

.dialog-certificates {

  & .modal-body {
    display: flex;
    flex-direction: column;
    position: relative;

    & .certificates-info {
      height: 100%;
      position: relative;

      & .certificates-area {
        display: flex;
        //        flex-direction: row;
        position: absolute;
        top: 0;
        left: 4px;
        right: 4px;
        bottom: 10px;

        & .coming-soon-area {
          width: 100%;
          height: 100%;
          text-align: center;
          vertical-align: middle;
          justify-content: center;
          display: flex;
          align-items: center;
          font-size: 30px;
          color: $secondaryColor;
        }

        & .list-group {
          //border-top: 1px solid $secondaryColor;
          border-radius: 8px;
          padding-right: 4px;
          min-width: 240px;
          overflow-x: auto;


          & .list-group-item {
            display: flex;
            background-color: transparent;
            border: 1px solid $secondaryColor;
            //border-bottom: 1px solid $secondaryColor;
            //border-right: 1px solid $secondaryColor;
            //border-left: 1px solid $secondaryColor;
            border-radius: 8px;
            padding: 0;
            margin-bottom: 6px;
            overflow: hidden;
            line-height: 1.4rem;
            min-height: 40px;

            &.coming-soon {
              opacity: 0.4;
            }

            //&:nth-child(even) {
            //  background-color: rgba(141, 211, 99, 0.1);
            //}

            & a {
              width: 100%;
              padding: 0.6rem;
              color: $secondaryColor;
              text-decoration: none;
              font-size: $fontSize;
              display: flex;
              flex-direction: row;
              line-height: 1.4rem;

              & .market-icon {
                width: 32px;
                margin-right: 14px;
              }

              & .market-title {
                flex-wrap: nowrap;
                white-space: nowrap;
              }

              &:hover, &:active, &.active {
                color: #000;
                text-decoration: none;
                background-color: $secondaryColor;

                & svg {
                  color: #000 !important;
                }
              }
            }
          }
        }
      }
    }
  }
}
