@import "../../global/variables";

.mobile-view {
  position: relative;
  width: 100%;
  height: 100%;
  overflow-x: auto;

  & table td {
    color: #fff;
  }

  .card .action-col-btns {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    max-width: 316px;
    margin: 0 auto;

    @media (max-width: 412px) {
      margin: 0 calc(-0.75rem + -9px);
    }

    .btn {
      font-size: 12px;
      min-width: 140px;
      height: 27px;
      padding: 4px;
      margin: 9px 9px;
      border: 2px solid $secondaryColor;
      border-radius: 19px;
      color: #fff;
      font-weight: 700;
      text-transform: uppercase;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      background: #000;
      transition: none;

      @media (max-width: 360px) {
        font-size: 11px;
        min-width: calc(50% - 18px);
      }

      &.accent {
        color: #000;
        border-radius: 19px;
        background-color: $secondaryColor;
      }
    }
  }

  .table>:not(caption)>*>* {
    border-bottom-width: 0;
  }
}

.card {

  & .mobile-card-title {
    color: #fff;
    background-color: transparent;
  }

  & .mobile-card-href {
    display: flex;
    align-items: center;
    align-content: center;
    text-align: center;
    justify-content: center;

    & .mobile-card-poster {
      width: 428px;
      height: 268px;
      max-width: 100%;
    }
  }

  & .card-body {
    &.table td, td {
      color: #fff;
      border-top: 1px solid rgba(81, 81, 81, 1);

      &.td-release-dates {
        border-top: 2px solid rgba(81, 81, 81, 1);
      }

      &.td-markets {
        padding-bottom: 15px !important;
      }

      &.mobile-card-header {
        padding: 0;
      }

      &.mobile-card-header div {
        background-color: $secondaryColor;
        color: #000;
        text-transform: uppercase;
        overflow: hidden;
        font-size: .8rem;
        font-weight: bold;
        width: 100%;
        padding: 14px;
      }

      & .action-col a {
        text-align: left;
        background-color: transparent;
        color: $secondaryColor;
        line-height: 1em;
        padding: 4px;
        border-left: 1px solid rgba(141, 211, 99, 0.5);
        border-top: 1px solid rgba(141, 211, 99, 0.2);
        border-right: 1px solid rgba(141, 211, 99, 0.5);
        border-bottom: 1px solid rgba(141, 211, 99, 0.2);
        margin: 4px;
        width: 100px;

        &:hover {
          color: #000 !important;
          text-decoration: none !important;
          background-color: $secondaryColor !important;
        }

      }

      & .action-col.primary a {
        background-color: $secondaryColor;
        color: #000;
      }
    }
  }
}

.games-table-mob {
  position: relative;
  width: 100%;
  height: 100%;

  & .games-table-mob-header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 40px;
    width: 100%;
  }
  & .games-table-mob-data {
    display: block;
    position: absolute;
    top: 40px;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
  }

  & .gr-row {
    display: grid;
    grid-template-columns: 80px 1fr 1fr 70px 1fr;
    margin: auto;
    border-bottom: 1px solid #515151;

    .td-release-dates {
      flex-direction: column;
    }

    &.thead {
      height: 100%
    }

    .th, .td {
      font-size: 0.6875rem;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: .25rem;
    }

    .th {
      background-color: $secondaryColor;
      color: #000;
      text-transform: uppercase;
      overflow: hidden;
      font-weight: bold;
      border-right: 1px solid #000;

      &:last-child {
        border-right: none;
      }
    }

    .td {
      color: #fff;
      border-top: 1px solid #515151;
      border-right: 1px solid #313131;
      word-break: break-word;
      white-space: break-spaces;
    }
  }

  .text-truncate {
    span {
      display: block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
