@import "../../global/variables";

.games-table {
  position: relative;
  width: 100%;
  height: 100%;

  & .games-table-header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    width: 100%;
  }

  & .games-table-data {
    display: block;
    position: absolute;
    top: 60px;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;

    &-gameCode {
      .game-code {
        max-width: calc(100% - 6px);

        span {
          display: block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }

  & .gt-virtual > div > div > div:nth-child(even) {
    background-color: rgba(141, 211, 99, 0.01);
  }

  & .gr-row {
    display: grid;
    grid-template-columns: 200px 8% 105px 120px 120px 120px 70px 1.1fr 0.6fr 100px;
//    max-width: 1920px;
    margin: auto;
    flex-direction: row; /* change this to row instead of 'column' */
    flex-wrap: wrap;
    border-bottom: 2px solid #515151;


    &.thead {
      grid-template-columns: 200px 8% 105px 120px 120px 120px 70px 1.1fr 0.6fr 116px;
    }

    & div {
      color: #fff;
      padding: 1px;
      border-bottom: 1px solid #515151;
      border-right: 1px solid #313131;
      border-top: none;
      text-align: center;
      -moz-box-align: center;
      align-items: center;
      -moz-box-pack: center;
      justify-content: center;
      vertical-align: middle;
      display: flex;
      flex-direction: column;

      .markets_subtitle {
          text-overflow: inherit;
          white-space: break-spaces;
          text-align: center;
          align-items: center;
          justify-content: center;
          vertical-align: middle;
          padding: 0.75rem;
          display: flex;
          align-content: center;
          border-bottom: none;

        &.markets_subtitle_release_date{
          width: 20%;
          border-right: 1px solid #313131;
        }

        &.markets_subtitle_markets{
          width: 85%;
        }
      }

      &.th {
        background-color: $secondaryColor;
        color: #000;
        text-transform: uppercase;
        overflow: hidden;
        font-size: 0.8rem;
        font-weight: bold;
        border: none;
        text-align: center;
        justify-content: center;
        vertical-align: middle;
        flex-direction: row;
        align-items: stretch;
        border-right: 1px solid #000;

        & span {
          text-overflow: inherit;
          white-space: break-spaces;
          text-align: center;
          align-items: center;
          justify-content: center;
          vertical-align: middle;
          padding: 0.75rem;
          display: inline-block;

          &.marketsAndReleaseDate_subtitle:nth-child(1){
            width: 20%;
            border-right: 1px solid #000;
          }

          &.marketsAndReleaseDate_subtitle:nth-child(2){
            width: 85%;
          }
        }
      }

      & .action-col {
        display: flex;
        flex-direction: column;
        width: 110px;
        min-height: 130px;
        justify-content: flex-start;
        border: none;

        & a {
          text-align: left;
          background-color: transparent;
          color: $secondaryColor;
          line-height: 1em;
          padding: 4px;
          border-left: 1px solid rgba(141, 211, 99, 0.5);
          border-top: 1px solid rgba(141, 211, 99, 0.2);
          border-right: 1px solid rgba(141, 211, 99, 0.5);
          border-bottom: 1px solid rgba(141, 211, 99, 0.2);
          margin-top: 2px;
          margin-bottom: 4px;
          width: 100%;

          &:hover {
            color: #000 !important;
            text-decoration: none !important;
            background-color: $secondaryColor !important;
          }
        }

        &.primary a {
          background-color: $secondaryColor;
          color: #000;
        }

        //& a:nth-child(even) {
        //  background-color: rgba(141, 211, 99, 0.1);
        //}

      }
    }
  }


  & table {
    position: relative;
    margin: auto;
  }

  .action-col {
    display: flex;
    flex-direction: column;
    width: 110px;
    min-height: 130px;

    & a {
      text-align: left;
      background-color: transparent;
      color: $secondaryColor;
      line-height: 1em;
      padding: 4px;
      border-left: 1px solid rgba(141, 211, 99, 0.5);
      border-top: 1px solid rgba(141, 211, 99, 0.2);
      border-right: 1px solid rgba(141, 211, 99, 0.5);
      border-bottom: 1px solid rgba(141, 211, 99, 0.2);
      margin-top: 2px;
      margin-bottom: 4px;

      &:hover {
        color: #000 !important;
        text-decoration: none !important;
        background-color: $secondaryColor !important;
      }
    }

    &.primary a {
      background-color: $secondaryColor;
      color: #000;
    }

    //& a:nth-child(even) {
    //  background-color: rgba(141, 211, 99, 0.1);
    //}

  }

  & .page-link, & .page-item.active .page-link {
    color: #000;
  }

  & table thead th {
    background-color: $secondaryColor;
    color: #000;
    text-transform: uppercase;
    overflow: hidden;
    font-size: 0.8rem;
    border: none;
    border-right: 1px solid #000;
    text-align: center;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
    padding: .75rem;

    & span {
      width: 100%;
      text-overflow: inherit;
      white-space: break-spaces;
      text-align: center;
      align-items: center;
      justify-content: center;
      vertical-align: middle;
    }
  }

  & table tbody tr:nth-child(even) {
    background-color: rgba(81, 81, 81, 0.15);
  }

  & table tbody td {
    color: #fff;
    padding: 1px;
    border-bottom: 1px solid rgba(81, 81, 81, 1);
    border-top: none;
    text-align: center;
    align-items: center;
    justify-content: center;
    vertical-align: middle;

    &:nth-child(even) {
      background-color: rgba(141, 211, 99, 0.01);
    }

    & .markets {
      display: flex;
      flex-direction: column;
      padding-left: 2px;
      white-space: break-spaces;
    }

    & ul.features {
      display: flex;
      justify-content: flex-start;
      flex-direction: column;
      padding-left: 2px;

      & li {
        justify-content: center;
        align-self: center;
        //list-style-type: circle;
      }
    }
  }
}

.p-feature {
  text-overflow: ellipsis;
  overflow: hidden;
  margin-bottom: 0.1rem;
}

.games-table-data-marketsAndReleaseDate .markets {
  flex-direction: row !important;
  border-right: 1px solid #313131 !important;
  align-items: stretch !important;
  width: 100%;
  height: 100%;
}

.games-table-data-marketsAndReleaseDate .markets:last-child {
  border-bottom: none !important;
}
