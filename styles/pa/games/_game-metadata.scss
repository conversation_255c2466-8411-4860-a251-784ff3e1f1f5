@import "../../global/variables";
// transitions
$tr2: all 0.2s ease;

// metadata
.metadata {
  font-size: 16px;
  margin-bottom: 12.96vh;
  @media (min-width: 1921px), (min-height: 1081px) {
    margin-bottom: 140px;
  }

  &__container {
    margin: 60px 0 0;

    @media (max-width: 480px) {
      margin-top: 50px;
    }
    @media (max-width: 479px) {
      margin-top: 12.5vw;
    }
  }

  &__item {
    border-radius: 27px;
    margin-bottom: 40px;
    transition: $tr2;
    position: relative;
    background: #181717;

    @media (max-width: 479px) {
      margin-bottom: 8.33vw;
    }

    &::after {
      z-index: -1;
      border-radius: inherit;
      content: "";
      position: absolute;
      left: -2px;
      top: -2px;
      background: linear-gradient(#424242, #1E1D1D);
      width: calc(100% + 4px);
      height: calc(100% + 4px);
    }

    &.open {
      &::after {
        background: $secondaryColor;
      }

      .metadata__title-icon {
        transform: scaleY(-1) rotate(135deg);
      }
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__description {
    display: none;
    border-top: 2px solid rgba(#fff, 0.3);
    padding: 45px 67px 55px;

    .open & {
      display: block;
    }

    @media (max-width: 1024px) {
      padding: 45px 42px 55px;
    }
    @media (max-width: 767px) {
      padding: 45px 24px 55px;
    }
    @media (max-width: 479px) {
      padding: 9.375vw 6.66vw 11.458vw;
    }
  }

  &__title {
    outline: none;
    cursor: pointer;
    padding: 40px 35px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 2.5em;
    line-height: 1;

    @media (max-width: 767px) {
      font-size: 1.875em;
      line-height: 1.2;
      padding: 27px 18px;
    }
    @media (max-width: 479px) {
      font-size: 6.25vw;
      padding: 9.375vw 18px;
    }

    &-icon {
      position: relative;
      display: inline-block;
      width: 26px;
      height: 5px;
      background: #A9A9A9;
      transform: scaleY(1) rotate(135deg);
      transform-origin: center;
      border-radius: 5px;
      will-change: transform;

      @media (max-width: 767px) {
        display: none;
      }

      &::after {
        content: "";
        position: absolute;
        right: 0;
        top: 0;
        background: inherit;
        height: 26px;
        width: 5px;
        border-radius: 5px;
      }
    }
  }
}
.metadata-table {
  font-size: 24px;
  line-height: 1.2;
  text-transform: uppercase;
  width: 100%;
  border-collapse: collapse;

  @media (max-width: 1024px) {
    font-size: 20px;
  }
  @media (max-width: 767px) {
    font-size: 18px;
  }
  @media (max-width: 479px) {
    font-size: 14px;
  }

  td {
    padding: 8px;

    &:first-child {
      @media (min-width: 1024px) {
        width: 35%;
      }
    }
  }
  tr:nth-child(even) {
    background: linear-gradient(to right,#191818 0,#1e1d1d 15%,#1e1d1d 50%,#1e1d1d 85%,#191818 100%);
  }
}

