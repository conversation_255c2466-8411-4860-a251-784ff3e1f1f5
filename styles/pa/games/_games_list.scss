@import "../../global/variables";

div.gameList {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;

  &Item {
    width: 240px;
    margin: 2px;
    > img {
      display: block;
      width: 100%;
      height: 100%;
      object-fit: cover;
      /*border-radius: 20px;*/
      //visibility: hidden;
    }
    //@media (min-width: 768px){
    //  width: 48.5%;
    //  margin: 0 1.5% 1.5% 0;
    //}
    //@media (min-width: 1024px){
    //  width: 31.8%;
    //  margin: 0 1.5% 1.5% 0;
    //}
    //@media (min-width: 1800px){
    //  width: 23.5%;
    //  margin: 0 1.5% 1.5% 0;
    //}
    //@media (min-width: 2200px){
    //  width: 18.5%;
    //  margin: 0 1.5% 1.5% 0;
    //}
    //@media screen and (max-width: 400px) and (-ms-high-contrast: active), screen and (max-width: 400px) and (-ms-high-contrast: none) {
    //  width: calc(100%/2 - 11px);
    //}
    //@media (max-width: 767px){
    //  width: 100%;
    //}
    //@media (max-width: 767px) and (orientation: landscape) {
    //  width: 48.5%;
    //  margin: 0 1.5% 1.5% 0;
    //}
  }
}

.device-mobile {
  & .gameList {
    &Item {
      width: 120px;
    }
  }
}
