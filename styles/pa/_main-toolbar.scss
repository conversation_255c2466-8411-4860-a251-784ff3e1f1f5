@import "../global/variables";

#app-main-header {
  background-color: $mainDark;
  min-height: $header-height;
}

.header-main-toolbar {

  a {
    color: $secondaryColor !important;
    border: 1px solid rgba(141, 211, 99, 0.5);
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.75;
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
    white-space: nowrap;
  }
  .nav-link {
    margin: 0 4px;

    @media (max-width: 767px) {
      margin: 2px 0;
    }
  }
}
.navbar-nav {
  @media (max-width: 767px) {
    margin-top: 4px;
  }
}
.dropdown-container-roadmap {
  .dropdown-toggle-roadmap {
    display: flex;
    align-items: center;
    padding: 0 12px;
    border-radius: 0;
    height: 34px;
    background-color: transparent!important;
    color: #8dd363;
    &.active {
      color: #000;
      background-color: #8dd363 !important;
    }
    a {
      border:none;
    }
    @media (max-width: 767px) {
      width: 100%;
      justify-content: space-between;
    }
    &:hover {
      color: #000;
      background-color: #8dd363 !important;
      a {
        color: #000!important;
      }
    }
  }
  .dropdown-menu {
    margin: 0;
    padding: 0;
    border: none;
  }
  .dropdown-item {
    padding: 0!important;
    margin: 0!important;
    a {
      margin: 0!important;
    }
    .button-main {
      margin: 0!important;
    }
  }
}
