@import "../global/variables";

.header-tools {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  max-width: 100%;
  width: 100%;
  vertical-align: middle;
  height: 80vh;
  max-height: 1200px;
  min-height: 900px;
  overflow: hidden;
  position: relative;

  & img {
    display: block;
    max-width: 100%;
    height: auto;
  }

  & .wrap {
    max-width: 1440px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
  }

  @media (max-width: 1024px) {
    min-height: 500px;
    max-height: 900px;
  }

  @media (max-width: 700px) {
    height: 80vh;
    max-height: 700px;
  }

  @media (max-width: 420px) {
    max-height: 570px;
  }

  //var 4 (effect for text)
  .header-desc__subtitle,
  .header-desc__title {
    text-shadow: 2px 2px 0 #181717, 8px 8px 15px #181717;
  }

  &::before {
    position: absolute;
    z-index: 2;
    left: 0;
    top: 0;
    content: "";
    width: 100%;
    height: 294px;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 100%);

    @media (max-width: 1024px) {
      height: 170px;
    }

    @media (max-width: 480px), (max-width: 812px) and (orientation: landscape) {
      height: 100px;
    }
  }

  &::after {
    position: absolute;
    z-index: 1;
    left: 0;
    bottom: 0;
    content: "";
    width: 100%;
    height: 600px;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(24, 23, 23, 1) 90%);

    @media only screen and (max-width: 1439px) and (max-height: 900px) {
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(24, 23, 23, 1) 75%);
    }

    @media (max-width: 480px), (max-width: 812px) and (orientation: landscape) {
      height: 250px;
    }
  }

  &--tools {
    background-image: none;
    min-height: 80vh;

    @media (min-width: 1921px), (min-height: 1081px) {
      min-height: inherit;
      z-index: 3;
      max-height: 1100px;
    }

    @media (min-width: 900px) and (max-width: 1024px) and (orientation: landscape) {
      min-height: 70vh;
      height: 70vh;
    }

    @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
      min-height: 50vh;
      height: 50vh;
    }

    @media (max-width: 767px) {
      .header-desc {
        display: block;
      }
    }

    @media (max-width: 1023px) {
      .header-desc__subtitle,
      .header-desc__logo {
        display: none;
      }
    }

    @media (max-width: 900px) and (orientation: landscape) {
      .header-desc {
        display: none;
      }
    }

    &::after {
      height: 100px;

      // @media (min-width: 1921px), (min-height: 1081px) {
      //   z-index: -1;
      // }
    }

    .title--h1 {
      letter-spacing: 0.32em;

      @media (max-width: 900px) and (orientation: landscape) {
        text-align: left;
      }

      @media (max-width: 480px) {
        font-size: 35px;
        line-height: 1em;
        letter-spacing: 0.15em;
      }
    }

    .header-desc__subtitle {
      @media (max-width: 900px) and (orientation: landscape) {
        text-align: left;
      }

      @media (max-width: 480px) {
        font-size: 16px;
        line-height: 36px;
        letter-spacing: 0.075em;
        font-weight: 400;
      }
    }

    .gameposter img {
      height: auto;
    }
  }

  & .tools-header-text {
    pointer-events: none;
    width: 100%;
    height: 100%;
    max-height: 1080px;
    max-width: 1920px;
    text-transform: uppercase;
    font-weight: 700;
    font-size: 20px;
    line-height: 1;
    color: rgba($secondaryColor, 1);
    position: absolute;
    left: 48%;
    top: 0;
    transform: translateX(-50%);
    z-index: 2;

    @media (max-width: 1366px) {
      font-size: 18px;
    }

    @media (max-width: 1024px) {
      left: 0;
      transform: none;
    }

    @media (max-width: 1023px) {
      font-size: 16px;
      left: -5%;
    }

    @media (max-width: 480px) {
      top: -17%;
    }

    @media (max-width: 400px) and (max-device-height: 667px) {
      top: -14%;
    }

    @media (min-width: 3400px) {
      left: 49%;
    }

    span {
      display: block;
      position: absolute;
      z-index: 2;

      @media (max-width: 1024px) {
        animation-delay: 0s !important;
      }

      &:nth-child(1) {
        width: 170px;
        left: 48%;
        top: 20%;
        opacity: 0;
        animation: fadeIn 1s linear 0.5s both;

        @media (max-width: 1024px) {
          top: 25%;
          left: 48%;
        }

        @media (max-width: 1024px) and (orientation: portrait) {
          top: 13%;
          left: 47%;
        }

        @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
          left: 48%;
          top: 20%;
        }

        @media (max-width: 1023px) {
          left: 25%;
          top: 45%;
        }

        @media (max-width: 480px) {
          top: 55%;
          left: 25%;
        }

        @media (max-width: 900px) and (orientation: landscape) {
          display: none;
        }
      }

      &:nth-child(2) {
        text-align: right;
        width: 130px;
        left: 43%;
        top: 45%;
        opacity: 0;
        animation: fadeIn 1s linear 1s both;

        @media (max-width: 1440px) {
          left: 40%;
        }

        @media (max-width: 1024px) {
          top: 57%;
        }

        @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
          left: 46%;
          top: 50%;
        }

        @media (max-width: 1023px) {
          text-align: left;
          width: 200px;
          left: 15%;
          top: 65%;
        }

        @media (max-width: 900px) and (orientation: landscape) {
          display: none;
        }

        @media (max-width: 480px) {
          top: 68%;
          left: 10%;
        }
      }

      &:nth-child(3) {
        width: 140px;
        left: 85%;
        top: 25%;
        opacity: 0;
        animation: fadeIn 1s linear 1.5s both;

        @media (max-width: 1440px) {
          left: 82%;
        }

        @media (max-width: 1024px) {
          left: 85%;
          top: 35%;
        }

        @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
          left: 85%;
          top: 25%;
        }

        @media (max-width: 1023px) {
          left: 25%;
          top: 85%;
        }

        @media (max-width: 480px) {
          top: 82%;
        }

        @media (max-width: 900px) and (orientation: landscape) {
          display: none;
        }
      }

      &:nth-child(4) {
        width: 130px;
        left: 82%;
        top: 51%;
        opacity: 0;
        animation: fadeIn 1s linear 2s both;

        @media (max-width: 1440px) {
          top: 54%;
        }

        @media (max-width: 1024px) {
          left: 87%;
          top: 67%;
        }

        @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
          left: 86%;
          top: 55%;
        }

        @media (max-width: 1023px) {
          visibility: hidden;
        }
      }
    }

    &__jp {
      span {
        &:nth-child(1) {
          left: 53%;
          top: 30%;

          @media (min-width: 1921px), (min-height: 1081px) {
            left: 55%;
          }

          @media (max-width: 1600px) {
            top: 25%;
          }

          @media (max-width: 1024px) {
            top: 22%;
            left: 45%;
          }

          @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
            top: 15%;
            left: 45%;
          }

          @media (max-width: 1023px) {
            visibility: hidden;
          }

          @media (max-width: 768px) {
            visibility: visible;
            top: 52%;
            left: 39%;
            width: 190px;
          }

          @media (max-width: 480px) {
            top: 69%;
          }

          @media (max-width: 900px) and (orientation: landscape) {
            display: none;
          }
        }

        &:nth-child(2) {
          width: 200px;
          left: 76%;
          top: 25%;
          text-align: left;

          @media (min-width: 1921px), (min-height: 1081px) {
            left: 77%;
          }

          @media (max-width: 1600px) {
            top: 20%;
          }

          @media (max-width: 1024px) {
            top: 20%;
            left: 75%;
          }

          @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
            top: 20%;
            left: 75%;
          }

          @media (max-width: 1023px) {
            visibility: hidden;
          }

          @media (max-width: 768px) {
            visibility: visible;
            top: 70%;
            left: 55%;
            width: 160px;
          }

          @media (max-width: 480px) {
            top: 82%;
          }

          @media (max-width: 900px) and (orientation: landscape) {
            display: none;
          }
        }

        &:nth-child(3) {
          left: 84%;
          top: 38%;

          @media (min-width: 1921px), (min-height: 1081px) {
            left: 86%;
          }

          @media (max-width: 1600px) {
            top: 32%;
          }

          @media (max-width: 1024px) {
            left: 84%;
            top: 38%;
          }

          @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
            left: 84%;
            top: 38%;
          }

          @media (max-width: 1023px) {
            visibility: hidden;
          }

          @media (max-width: 768px) {
            visibility: visible;
            top: 87%;
            left: 75%;
          }

          @media (max-width: 480px) {
            top: 95%;
          }

          @media (max-width: 480px) {
            left: 78%;
          }

          @media (max-width: 900px) and (orientation: landscape) {
            display: none;
          }
        }
      }

      &--mobile {
        @media (min-width: 769px), (max-width: 1024px) and (orientation: landscape) {
          display: none !important;
        }

        display: block;
        position: absolute;
        top: 35%;
        left: 18%;

        @media (max-width: 480px) {
          top: 52%;
          left: 15%;
        }

        div {
          font-family: $fontOpenSans;
          font-weight: 400;
          text-align: center;
        }

        div:nth-child(1) {
          font-size: 44px;
          color: #f9d876;

          @media (max-width: 480px) {
            font-size: 9vw;
            white-space: nowrap;
          }
        }

        div:nth-child(2) {
          margin-top: 10px;
          font-size: 16px;
          color: #fff1c8;
        }
      }
    }

    &__tournam {
      @media (max-width: 1023px) {
        left: -10%;
      }
      @media (max-width: 480px) {
        top: -7%;
      }

      span {
        &:nth-child(1) {
          width: 120px;
          left: 60%;
          top: 40%;

          @media (min-width: 1921px), (min-height: 1081px) {
            left: 62%;
          }

          @media (max-width: 1600px) {
            top: 33%;
          }

          @media (max-width: 1024px) {
            left: 58%;
            top: 63%;
          }

          @media (max-width: 1023px) {
            visibility: hidden;
          }

          @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
            left: 50%;
            top: 76%;
          }

          @media (max-width: 768px) {
            visibility: visible;
            left: 35%;
            top: 50%;
          }

          @media (max-width: 480px) {
            left: 33%;
            top: 58%;
          }

          @media (max-width: 420px) {
            left: 31%;
          }

          @media (max-width: 900px) and (orientation: landscape) {
            display: none;
          }
        }

        &:nth-child(2) {
          text-align: left;
          width: 190px;
          left: 67%;
          top: 20%;

          @media (min-width: 1921px), (min-height: 1081px) {
            left: 69%;
          }

          @media (max-width: 1600px) {
            top: 16%;
          }

          @media (max-width: 1024px) {
            left: 65%;
            top: 45%;
          }

          @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
            left: 58%;
            top: 46%;
          }

          @media (max-width: 1023px) {
            display: none;
          }

          @media (max-width: 900px) and (orientation: landscape) {
            display: none;
          }
        }

        &:nth-child(3) {
          width: 210px;
          left: 70.5%;
          top: 34%;

          @media (min-width: 1921px), (min-height: 1081px) {
            left: 72%;
          }

          @media (max-width: 1600px) {
            top: 28%;
          }

          @media (max-width: 1024px) {
            left: 70%;
            top: 55%;
          }

          @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
            left: 70%;
            top: 63%;
          }

          @media (max-width: 1023px) {
            display: none;
          }

          @media (max-width: 900px) and (orientation: landscape) {
            display: none;
          }
        }

        &:nth-child(4) {
          left: 83%;
          top: 17%;

          @media (min-width: 1921px), (min-height: 1081px) {
            left: 85%;
          }

          @media (max-width: 1600px) {
            top: 14%;
          }

          @media (max-width: 1024px) {
            left: 82%;
            top: 35%;
          }

          @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
            left: 82%;
            top: 42%;
          }

          @media (max-width: 768px) {
            visibility: visible;
            left: 70%;
            top: 70%;
          }

          @media (max-width: 480px) {
            left: 71%;
            top: 71%;
          }

          @media (max-width: 900px) and (orientation: landscape) {
            visibility: hidden;
          }
        }
      }
    }

    &__ms {
      @media (min-width: 1024px) and (max-width: 1600px) {
        left: 46%;
      }
      @media (min-width: 769px) and (max-width: 1200px) {
        visibility: hidden;
      }
      @media (max-width: 420px) {
        left: -13%;
      }

      span {
        &:nth-child(1) {
          top: 20%;
          left: 44%;
          width: 100px;

          @media (max-width: 768px) {
            top: 40%;
            left: 35%;
          }

          @media (max-width: 480px) {
            top: 48%;
            left: 28%;
            text-align: center;
          }
        }

        &:nth-child(2) {
          top: 43%;
          left: 57%;
          width: 80px;

          @media (max-width: 768px) {
            top: 70%;
            left: 75%;
          }

          @media (max-width: 480px) {
            top: 72%;
            left: 70%;
            text-align: center;
          }
        }

        &:nth-child(3) {
          white-space: nowrap;
          top: 58%;
          left: 43%;
          width: 140px;

          @media (max-width: 768px) {
            top: 88%;
            left: 33%;
          }

          @media (max-width: 480px) {
            top: 85%;
            left: 25%;
            text-align: center;
          }
        }
      }
    }

    &__grc {
      @media (max-width: 1024px) {
        margin-left: -20px;
      }

      @media (max-width: 480px) {
        top: 0;
      }

      @media (max-width: 400px) {
        top: 4%;
      }

      span {
        &:nth-child(1) {
          width: 240px;
          left: 53%;
          top: 24%;

          @media (max-width: 1023px) {
            visibility: hidden;
          }

          @media (max-width: 768px) {
            visibility: visible;
          }

          @media (max-width: 480px) {
            width: 190px;
            left: 45%;
          }

          @media (max-width: 420px) {
            left: 42%;
          }

          @media (max-width: 900px) and (orientation: landscape) {
            display: none;
          }
        }

        &:nth-child(2) {
          text-align: left;
          width: 180px;
          left: 67%;
          top: 36%;

          @media (max-width: 1023px) {
            visibility: hidden;
          }

          @media (max-width: 768px) {
            visibility: visible;
            top: 45%;
            left: 40%;
          }

          @media (max-width: 480px) {
            left: 35%;
            top: 42%;
            width: 160px;
          }

          @media (max-width: 420px) {
            left: 32%;
            width: 150px;
          }

          @media (max-width: 900px) and (orientation: landscape) {
            display: none;
          }
        }

        &:nth-child(3) {
          width: 220px;
          left: 80%;
          top: 24%;

          @media (max-width: 1023px) {
            visibility: hidden;
          }

          @media (max-width: 768px) {
            visibility: visible;
            top: 65%;
            left: 25%;
            width: 180px;
          }

          @media (max-width: 480px) {
            top: 57%;
            left: 22%;
          }

          @media (max-width: 420px) {
            left: 18%;
          }

          @media (max-width: 900px) and (orientation: landscape) {
            display: none;
          }
        }
      }
    }
  }

}

.tools-bg {
  position: absolute;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
  width: 100%;
  height: 100%;
  max-width: 1920px;
  max-height: 1080px;
  // z-index: 1;

  &__bns,
  &__grc {
    position: absolute;
    z-index: 2;
    left: 0;
    top: 0;
  }

  &__tournam {
    @media (max-width: 1024px) and (orientation: landscape) {
      margin-top: 100px;
    }

    // @media (min-width: 1024px) and (max-width: 1366px) and (orientation: portrait) {
    //   margin-top: 250px;
    // }
  }

  &--desktop {
    @media (max-width: 1023px) {
      display: none;
    }
  }

  &--mobile {
    @media (min-width: 1024px) {
      display: none;
    }
    background-repeat: no-repeat;
    background-position-x: center;
    background-position-y: center;
    background-size: cover;
    width: 100%;
    height: 100%;

    &__bns {
      @media (max-width: 992px) {
        background-position-y: 10%;
      }

      @media (max-width: 480px) {
        background-position-x: left;
      }

      @media (max-width: 900px) and (orientation: landscape) {
        background-position-y: center;
      }
    }

    &__jp {
      @media (max-width: 768px) {
        background-position-y: -20%;
      }

      @media (max-width: 480px) {
        background-position-y: 150px;
      }

      @media (max-width: 900px) and (orientation: landscape) {
        background-position-y: center;
      }
    }

    &__tournam {
      @media (max-width: 992px) {
        background-position-y: 0;
      }

      @media (max-width: 480px) {
        background-position-y: 100px;
      }

      @media (max-width: 900px) and (orientation: landscape) {
        background-position-y: center;
      }
    }

    &__ms {
      @media (max-width: 768px) {
        background-position-y: -30px;
      }
      @media (max-width: 480px) {
        background-position-y: inherit;
      }
    }

    &__grc {
      @media (max-width: 768px) {
        background-position-y: -30px;
      }

      @media (max-width: 480px) {
        background-position-y: 50px;
      }

      @media (max-width: 900px) and (orientation: landscape) {
        background-position-y: center;
      }
    }
  }


}

.header-desc {
  position: relative;
  margin-top: 12%;
  z-index: 2;
  height: 50vh;

  @media (min-width: 1921px), (min-height: 1081px) {
    max-height: 600px;
  }

  @media (max-width: 768px) {
    margin-top: 25%;
  }

  @media (max-width: 400px) {
    margin-top: 20%;
  }

  @media (max-width: 1024px) and (orientation: landscape) {
    height: 40vh;
  }

  @media (max-width: 1023px) and (orientation: landscape) {
    margin-top: 5%;
    height: 20vh;
  }

  //fix
  @media (max-width: 767px), (max-width: 900px) and (orientation: landscape) {
    display: none;
  }

  &__title {
    letter-spacing: 0.32em;
  }

  &__logo {
    max-width: 400px;
    pointer-events: none;
  }

  &__subtitle {
    font-size: 2.68vh;
    line-height: 1.8;
    margin-top: 4.44vh;
    padding-right: 4.688vw;
    font-weight: 700;

    @media (min-width: 1921px), (min-height: 1081px) {
      font-size: 29px;
      line-height: 39px;
      margin-top: 48px;
      padding-right: 90px;
    }

    @media (max-width: 1024px) {
      font-size: 20px;
      padding-right: 0;
    }

    @media (max-width: 767px), (max-width: 900px) and (orientation: landscape) {
      font-size: 15px;
      line-height: 36px;
      text-align: center;
    }

    &.grc-header-subtitle {
      font-size: 20px;
      line-height: 36px;
      letter-spacing: 0.075em;
      font-weight: 400;
    }
  }

  &__gs {
    display: flex;
    flex-direction: column;
    margin: auto 0 auto auto;
    max-width: 90%;

    @media (max-width: 1440px) {
      max-width: 100%;
    }

    @media (max-width: 991px) {
      max-width: 80%;
      margin: auto;
    }

    @media (max-width: 767px) {
      max-width: 65%;
    }

    @media (max-width: 480px) {
      max-width: 60%;
      padding-left: 20px;
    }

    @media (max-width: 420px) {
      max-width: 75%;
    }
  }

  &__gsPoints {
    margin-top: 30px;
    font-size: 30px;
    line-height: 36px;
    color: $secondaryColor;
    text-transform: uppercase;

    @media (max-width: 991px) {
      display: none;
    }

    span {
      display: block;
      margin-bottom: 45px;
      letter-spacing: 0.075em;

      @media (max-width: 1024px) {
        margin-bottom: 30px;
        letter-spacing: 0.05em;
      }

      &::after {
        margin-bottom: 0;
      }
    }
  }

  &__gsCross {
    position: absolute;
    top: -80px;
    left: 0;
    width: 12px;
    height: 118px;
    background: $secondaryColor;

    @media (max-width: 1440px) {
      left: -60px;
    }

    @media (max-width: 991px) {
      left: 60px;
    }

    @media (max-width: 1024px) {
      transform: scale(0.75);
    }

    @media (max-width: 991px) {
      transform: scale(0.61);
    }

    @media (max-width: 420px) {
      transform: scale(0.35) translate(-100px, 50px);
    }

    &::after {
      position: absolute;
      content: "";
      height: 12px;
      width: 118px;
      left: -53px;
      top: 53px;
      background: $secondaryColor;
    }
  }

  &__gsTitle {
    font-family: $fontOswald;
    font-size: 122px;
    line-height: 137px;
    font-weight: 400;

    @media (max-width: 1366px) {
      font-size: 110px;
      line-height: 120px;
    }

    @media (max-width: 1200px) {
      font-size: 90px;
      line-height: 100px;
    }

    @media (max-width: 767px), (max-width: 854px) and (orientation: landscape) {
      font-size: 60px;
      line-height: 1.2em;
    }

    strong {
      display: inline-block;
      letter-spacing: 0.23em;
    }
  }

  &__gsSubtitle {
    margin-top: 60px;
    font-size: 20px;
    line-height: 36px;
    letter-spacing: 0.075em;

    @media (max-width: 1024px) {
      letter-spacing: 0.05em;
    }

    @media (max-width: 768px) {
      margin-top: 40px;
      font-size: 16px;
      line-height: 1.4em;
      max-width: 70%;
    }

    @media (max-width: 767px) {
      margin-top: 30px;
    }

    @media (max-width: 480px) {
      max-width: 100%;
      padding-right: 10px;
    }
  }
}

.header-bottom {

  @media (max-width: 767px) {
    .button {
      display: none;
    }
  }

  @media (max-width: 812px) and (orientation: landscape) {
    margin-top: 23vh;
  }
}


.toolpage-offer {
  position: relative;
  margin-top: -5%;
  z-index: 2;

  @media (max-width: 1024px) {
    margin-top: 10%;
  }

  @media (min-width: 1921px), (min-height: 1081px) {
    margin-top: 0;
  }

  &--ms {
    @media (max-width: 767px) {
      margin-top: 0;
    }
  }

  &__big {
    color: rgba($secondaryColor, 0.04);
    font-weight: 400;
    font-size: 37.396vw;
    line-height: 1;
    text-align: center;
    text-transform: uppercase;
    position: relative;
    pointer-events: none;

    @media (min-width: 1921px) {
      font-size: 718px;
    }

    &--grc {
      white-space: pre;
    }

    &--jp {
      font-size: 18.438vw;

      @media (min-width: 1921px) {
        font-size: 354px;
      }
    }

    &--tournam {
      font-size: 11.771vw;
      margin-left: -7vw;

      @media (min-width: 1921px) {
        font-size: 226px;
      }
    }

    &--ms {
      text-align: left;
      font-size: 16.146vw;
      padding-bottom: 9.25vh;

      @media (min-width: 1921px) {
        font-size: 310px;
      }

      @media (min-height: 1081px) {
        padding-bottom: 100px;
      }

      @media (max-width: 1024px) {
        padding-bottom: 0;
      }
    }
  }

  &__same {
    width: 100%;
    position: absolute;
    top: 50%;
    left: 15px;
    transform: translateY(-50%);
    font-size: 2.59vh;
    line-height: 4.26vh;
    z-index: 2;

    @media (min-width: 1921px), (min-height: 1081px) {
      font-size: 28px;
      line-height: 46px;
    }

    @media (max-width: 992px), (max-width: 1024px) and (orientation: portrait) {
      position: relative;
      left: 0;
      top: 0;
      font-size: 16px;
      line-height: 36px;
    }
  }
}
