@import "../../global/variables";

.page404,
.page403,
.pageMaintenance {
  background-image: url("/assets/pa/images/404-maintence.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

.blockcenter {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  text-align: center;
  max-width: 1920px;
  margin: 0 auto;
}

.blockcenter__offer {
  font-size: 20px;
  line-height: 35px;
  max-width: 370px;
  margin-left: auto;
  margin-right: auto;
  margin-top: 8.33vh;
  margin-bottom: 8.33vh;
}

@media (min-width: 1921px), (min-height: 1081px) {
  .blockcenter__offer {
    margin-top: 90px;
    margin-bottom: 90px;
  }
}

.blockcenter__offer--maintenance {
  margin-bottom: 0;
}

@media (max-width: 640px) and (orientation: landscape) {
  .blockcenter__offer {
    margin-top: 40px;
    margin-bottom: 40px;
  }
}

@media (max-width: 480px) {
  .blockcenter__offer {
    width: 100%;
  }
}

@media (max-width: 360px) {
  .blockcenter__offer {
    font-size: 18px;
    line-height: 30px;
  }
}

.blockcenter__title {
  font-size: 35px;
  line-height: 1;
  letter-spacing: .32em;
  position: relative;
}

@media (max-width: 410px) {
  .blockcenter__title {
    font-size: 30px;
  }
}

@media (max-width: 360px) {
  .blockcenter__title {
    font-size: 26px;
  }
}

.blockcenter__title::before {
  z-index: -1;
  text-transform: none;
  white-space: nowrap;
  position: absolute;
  left: 50px;
  bottom: -25px;
  content: attr(data-text);
  font-size: 216px;
  line-height: 1;
  font-family: $fontOpenSans;
  font-weight: 800;
  letter-spacing: normal;
  color: rgba(255, 255, 255, 0.07);
}

@media (max-width: 1024px) {
  .blockcenter__title::before {
    left: 50%;
    transform: translateX(-50%);
    font-size: 150px;
  }
}

@media (max-width: 812px) {
  .blockcenter__title::before {
    font-size: 100px;
    bottom: -10px;
  }
}

@media (max-width: 480px) {
  .blockcenter__title::before {
    font-size: 70px;
  }
}

.button--primary {
  background-color: $secondaryColor;
  color: $mainDark;
}

.button {
  outline: 0;
  cursor: pointer;
  border: none;
  display: inline-block;
  text-decoration: none;
  text-align: center;
  font-size: 14px;
  line-height: 16px;
  letter-spacing: .12em;
  font-weight: 700;
  text-transform: uppercase;
  white-space: nowrap;
  border-radius: 50px;
  transition: all .2s ease;
  padding: 19px 69px;
  position: relative;
  z-index: 4;
}

.blockcenter__btn {
  width: 238px;
  margin: 0 auto;
}
