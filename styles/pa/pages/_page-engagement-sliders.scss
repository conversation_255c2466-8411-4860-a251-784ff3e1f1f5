@import "../../global/variables";
// @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap');

// slider
.engagement-slider {
  position: relative;
  padding: 0 20px;

  .engagement-slide {
    padding: 20px 35px;

    @media (min-width: 600px) {
      width: calc(100%/2);
      padding: 20px;
    }
    @media (min-width: 1024px) {
      width: calc(100%/3);
      padding: 20px;
    }
  }
  &-title {
    font-size: 26px;
    font-family: $fontRoboto;
    color: $secondaryColor;
    font-weight: bold;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    margin-bottom: 16px;
    text-align: center;

    @media (min-width: 1440px) {
      font-size: 44px;
    }
  }
  &-text {
    font-size: 12px;
    font-family: $fontRoboto;

    @media (min-width: 1440px) {
      font-size: 18px;
    }
  }
  &-figure {
    display: block;
    margin: 0 0 46px;
    padding-bottom: 62.54%;
    position: relative;

    &-img {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      top: 0;
      height: 100%;
      width: 100%;
      object-fit: contain;
      object-position: center;
    }
  }
  &-nav {
    display: flex;
    justify-content: space-between;
  }
  &-nav-btn,
  .swiper-button-prev,
  .swiper-button-next {
    cursor: pointer;
    position: absolute;
    top: 50%;
    height: 24px;
    width: 24px;
    transform: translateY(-50%);
    z-index: 10;
    background: no-repeat 50% 50%;
    background-size: auto 100%;
    background-image: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI1LjQuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHZpZXdCb3g9IjAgMCA0My45NSA3My45NSIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNDMuOTUgNzMuOTU7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPHN0eWxlIHR5cGU9InRleHQvY3NzIj4KCS5zdDB7ZmlsbDojRkZGRkZGO30KPC9zdHlsZT4KPGc+Cgk8cGF0aCBjbGFzcz0ic3QwIiBkPSJNMzguODUsNDIuMDdjLTEuMjYsMC0yLjUyLTAuNDYtMy41LTEuMzlMMS42LDguODFDLTAuNDUsNi44Ny0wLjU0LDMuNjUsMS4zOSwxLjYKCQlDMy4zMy0wLjQ1LDYuNTUtMC41NCw4LjYsMS4zOWwzMy43NSwzMS44N2MyLjA1LDEuOTMsMi4xNCw1LjE2LDAuMjEsNy4yMUM0MS41Niw0MS41NCw0MC4yMSw0Mi4wNywzOC44NSw0Mi4wN3oiLz4KPC9nPgo8Zz4KCTxwYXRoIGNsYXNzPSJzdDAiIGQ9Ik01LjEsNzMuOTVjLTEuMzUsMC0yLjcxLTAuNTQtMy43MS0xLjZjLTEuOTMtMi4wNS0xLjg0LTUuMjgsMC4yMS03LjIxbDMzLjc1LTMxLjg3CgkJYzIuMDUtMS45Myw1LjI4LTEuODQsNy4yMSwwLjIxYzEuOTMsMi4wNSwxLjg0LDUuMjgtMC4yMSw3LjIxTDguNiw3Mi41NkM3LjYyLDczLjQ5LDYuMzYsNzMuOTUsNS4xLDczLjk1eiIvPgo8L2c+Cjwvc3ZnPgo=');

    &:after {
      display: none;
    }
    @media (min-width: 1440px) {
      height: 52px;
      width: 52px;
    }

    &.swiper-button-prev {
      left: 0;
      transform: translateY(-50%) scale(-1,-1);
    }
    &.swiper-button-next {
      right: 0;
    }
  }


  & .tools-slider-item {
    color: rgba(255,255,255, 1);
    font-size: 1.85vh;
    line-height: 3.33vh;

    //@media (min-width: 1921px), (min-height: 1081px) {
    //  font-size: 20px;
    //  line-height: 36px;
    //}
    //
    //@media (max-width: 1440px) {
    //  font-size: 16px;
    //  line-height: 30px;
    //}

    &__name {
      font-weight: 700;
      font-size: 2.68vh;
      line-height: 1;
      text-transform: uppercase;

      //@media (min-width: 1921px), (min-height: 1081px) {
      //  font-size: 83px;
      //}
      //
      //@media (max-width: 992px), (max-width: 1024px) and (orientation: portrait) {
      //  font-size: 38px;
      //  line-height: 50px;
      //}
      //
      //@media (max-width: 767px) {
      //  font-size: 30px;
      //  line-height: 27px;
      //}
    }

    &__subname {
      text-transform: uppercase;
      font-weight: 700;
      font-size: 2.59vh;
      line-height: 1;

      //@media (min-width: 1921px), (min-height: 1081px) {
      //  font-size: 28px;
      //}
      //
      //@media (max-width: 992px), (max-width: 1024px) and (orientation: portrait) {
      //  font-size: 15px;
      //}
    }

    &__text {
      margin-top: 2.59vh;
      margin-bottom: 12.96vh;
      position: relative;
      padding-right: 10%;

      //@media (min-width: 1921px), (min-height: 1081px) {
      //  margin-top: 28px;
      //  margin-bottom: 140px;
      //}
      //
      //@media (max-width: 992px), (max-width: 1024px) and (orientation: portrait) {
      //  padding-right: 0;
      //  margin-top: 20px;
      //  margin-bottom: 60px;
      //}
      //
      //@media (max-width: 480px) {
      //  margin-bottom: 30px;
      //}

      &::after {
        position: absolute;
        content: "";
        bottom: -6.48vh;
        left: 0;
        width: 8.24vh;
        height: 2px;
        background-color: $secondaryColor;

        @media (min-width: 1921px), (min-height: 1081px) {
          bottom: -70px;
          width: 89px;
        }

        @media (max-width: 992px), (max-width: 1024px) and (orientation: portrait) {
          bottom: -30px;
        }

        @media (max-width: 480px) {
          bottom: -15px;
        }
      }
    }

    &__subtext {
      margin-top: 3.42vh;

      @media (min-width: 1921px), (min-height: 1081px) {
        margin-top: 37px;
      }
    }
  }

}
