@import "../../global/variables";

.skywind-docs {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 10px;
  left: 0;
  width: 100%;

  & .sw-toolbar {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 50px;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;

    & button {
      border: 1px solid rgba(255, 255, 255, 0.23);
      padding: 5px 15px;
      margin: 0 4px;
      color: #fff;
      font-size: 0.875rem;
      min-width: 64px;
      box-sizing: border-box;
      transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
      font-family: $fontRoboto;
      font-weight: 500;
      line-height: 1.75;
      border-radius: 4px;
      letter-spacing: 0.02857em;
      text-transform: uppercase;

      &.active {
        color: #000;
        background-color: $secondaryColor;
        border: 1px solid rgba(141, 211, 99, 0.5);
      }

      &:hover {
        color: $secondaryColor;
        text-decoration: none;
        background-color: transparent;
      }
    }
  }

  & .sw-area {
    position: absolute;
    top: 60px;
    right: 0;
    bottom: 20px;
    left: 0;
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 100%;

    & .list-group.sw-list {
      //border-top: 1px solid $secondaryColor;
      border-radius: 8px;
      overflow: auto;
      min-width: 220px;

      & .list-group-item {
        display: flex;
        background-color: transparent;
        border: 1px solid $secondaryColor;
        //border-bottom: 1px solid $secondaryColor;
        //border-right: 1px solid $secondaryColor;
        //border-left: 1px solid $secondaryColor;
        border-radius: 8px;
        padding: 0;
        margin-bottom: 1em;
        overflow: hidden;
        line-height: 1.4rem;

        &:nth-child(even) {
          background-color: rgba(141, 211, 99, 0.1);
        }

        & a {
          width: 100%;
          padding: 0.6rem;
          color: $secondaryColor;
          text-decoration: none;
          font-size: $fontSize;
          display: flex;
          flex-direction: row;
          line-height: 1.4rem;

          &:hover, &:active, &.active {
            color: #000;
            text-decoration: none;
            background-color: $secondaryColor;
          }
        }
      }
    }

    & .sw-iframe {
      width: 100%;
    }
  }
}