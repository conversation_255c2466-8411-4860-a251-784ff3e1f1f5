@import "../../global/variables";

.jackpot-features {
  &__inner {
    margin-top: 8.33vh;
    margin-bottom: 12.96vh;

    @media (min-width: 1921px), (min-height: 1081px) {
      margin-top: 90px;
      margin-bottom: 140px;
    }
  }
}

.jp-feature {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;

  &__img {
    width: 200px;
    height: 200px;
    background-repeat: no-repeat;
    background-position: center;

    @media (max-width: 1024px) {
      background-size: contain;
      width: 150px;
      height: 150px;
    }
  }

  &__name {
    font-weight: 700;
    font-size: 32px;
    line-height: 1;
    margin-top: -1.04vh;
    margin-bottom: 6.02vh;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      left: 50%;
      bottom: -4vh;
      transform: translate(-50%, -50%);
      width: 37px;
      height: 4px;
      background-color: rgba($secondaryColor, 1);
    }

    @media (max-width: 1024px) {
      font-size: 24px;
    }

    @media (min-width: 1921px), (min-height: 1081px) {
      margin-top: -20px;
      margin-bottom: 65px;

      &::after {
        bottom: -32px;
      }
    }
  }

  &__text {
    width: 100%;
    font-size: 20px;
    line-height: 36px;
    padding: 0 1.5em;

    @media (max-width: 1024px) {
      padding: 0;
      font-size: 15px;
    }
  }
}
