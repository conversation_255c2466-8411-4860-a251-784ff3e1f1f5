@import "../../global/variables";

.tournament-wrapper {
  width: 100%;
  height: calc(100vh - 80px);
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: #76d7ff;

  & a {
    width: 100%;
    //height: 100%;
    //object-fit: cover;
  }

  & .tournament-image {
    width: 100%;
    //height: 100%;
    object-fit: cover;
  }

  & .flip-countdown {
    position: absolute;
    bottom: 20px;
    z-index: 100;
    left: 0;
    right: 50%;
    width: 100vw;

    & .flip-countdown-title {
      color: #000;
    }
  }
}

.device-mobile {
  &.in-portrait {
    & .tournament-wrapper img.mobile-h {
      display: none;
    }

    & .tournament-wrapper img.mobile-v {
      height: auto;
      object-fit: contain;
    }

    & .flip-countdown {
      bottom: 20vh;
    }
  }

  &.in-landscape {
    & .tournament-wrapper {
      & a {
        width: 100%;
        /*
                height: 100%;*/
      }

      & img.mobile-v {
        display: none;
      }

      & img.mobile-h {
        object-fit: cover;
      }
    }
  }

  & .tournament-wrapper {
    & img.desktop {
      display: none;
    }
  }
}

.device-desktop {
  & .tournament-wrapper {
    & a {
      width: 100%;
      /*
            height: 100%;*/
    }

    & img {
      @media (min-width: 1680px) {
        object-fit: contain;
      }
    }

    & img.mobile-h, & img.mobile-v {
      display: none;
    }
  }
}