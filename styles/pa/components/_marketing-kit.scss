@import "../../global/variables";

#dialog-marketing-kit {
  & .modal-body {
    & .mk-list-items {
      & .card {
        & .card-header {
          padding: 2px;
          overflow-anchor: none;
          transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease;

          & button {
            width: 100%;
            text-align: left;
            border: 1px solid $secondaryColor;
            border-radius: 8px;
            background-color: transparent;
            font-size: 1rem;
            color: $secondaryColor;
            padding: 1rem 1.25rem;
          }
        }
        & .card-body {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
        }
      }
    }
  }
}