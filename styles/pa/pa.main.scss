@import "../global/reset";
@import "../global/variables";

html {
  overflow-x: hidden;
  scroll-behavior: smooth;
}

/* ensure all pages have Bootstrap CSS */
@import "../../node_modules/bootstrap/scss/bootstrap";
@import "../global/common";
@import "../global/buttons";
@import "../global/fonts";
@import "../global/forms";
@import "../global/flip-count-down";
@import "../global/footer";
@import "../global/sections";
@import "../global/loading-spinner";
@import "../global/header";
@import "../global/three-d-image";

@import "../global/sections/contact-us";
@import "../global/sections/section-360";
@import "../global/sections/game-features";
@import "../global/sections/engagement-swiper";
@import "../global/sections/engagement-tools-swiper";
@import "../global/sections/jackpot-features";
@import "../global/sections/news-list";
@import "../global/sections/news-article";

@import "pages/page-404";
@import "pages/page-tournament";
@import "pages/page-roadmap";
@import "pages/page-engagement-sliders";
@import "pages/page-jackpot-features";
@import "pages/page-skywind-docs";
@import "main-toolbar";
@import "banners/banner-video";
@import "banners/banner-image";
@import "banners/banner-swiper";
@import "dialogs/dialog-modal";
@import "dialogs/dialog-certificates";
@import "dialogs/dialog-first-entrance";
@import "dialogs/dialog-video";
@import "games/games-browser-view";
@import "games/games-mobile-view";
@import "../global/games-search";
@import "games/game-metadata";
@import "components/marketing-kit";
@import "header-tools";
@import "main-toolbar";
// @import "../../public/landing/nunchuckschicken/assets/styles/global";
@import "../../public/landing/teller-of-tales/assets/styles/global";

.animation-fade-in-1s {
  animation: fadeIn 1s;
}

a {
  color: $secondaryColor;

  &:hover {
    color: $linkHoverColor;
    text-decoration: none;
  }
}

.button-main {
  text-align: left;
  background-color: transparent;
  color: $secondaryColor;
  line-height: 1em;
  padding: 4px;
  margin: 2px 4px;
  border-left: 1px solid rgba($secondaryColor, 0.5);
  border-top: 1px solid rgba($secondaryColor, 0.2);
  border-right: 1px solid rgba($secondaryColor, 0.5);
  border-bottom: 1px solid rgba($secondaryColor, 0.2);

  &.active {
    color: #000 !important;
    text-decoration: none !important;
    background-color: $secondaryColor !important;
  }

  &.highlight {
    color: #000 !important;
    text-decoration: none !important;
    background-color: $tournamentHighlightColor !important;
    border: 1px solid $tournamentHighlightColor;

    &:hover {
      background-color: $tournamentHighlightColor !important;
    }
  }

  &:hover {
    color: $mainDark !important;
    text-decoration: none !important;
    background-color: $secondaryColor !important;
  }
}

.text-h6 {
  font-size: 1.25rem;
  font-weight: 500;
  line-height: 1.6;
  letter-spacing: 0.0075em;
  text-align: center;
}

.text-h5 {
  margin: 0 auto 15px;
  font-weight: 700;
  line-height: 1.5;
  text-align: center;
}

.form-logo {
  width: 100%;
  text-align: center;

  & svg {
    margin: 20px auto;
    max-width: 100%;
    max-height: 43px;
  }
}

.toolbar-logo {
  width: 140px;
  //height: 43px;

  svg {
    margin: 4px 6px 4px 0;
    max-width: 100%;
    //max-height: 43px;
    height: 25px;
  }
}

//.device-mobile {
.main-container.container-fluid {
  padding-left: 0;
  padding-right: 0;
}

//}

.main-container {
  display: block;
  margin-top: $topHeaderHeight;

  &.window-fixed {
    position: fixed;
    width: 100%;
    top: $topHeaderHeight;
    margin-top: 0;
    padding: 0;
    right: 0;
    bottom: 50px;
    left: 0;
    display: flex;
    flex-direction: column;
  }
}

.device-desktop {
  #change-password-after-login {
    max-width: 50vw;
  }
}

.container {
  max-width: 1920px;
  margin-left: auto;
  margin-right: auto;
}

// bootstrap dropdown rewrite style END
.title {
  font-family: $fontOswald;
  text-transform: uppercase;
  display: block;
  font-weight: 400;

  &--h1 {
    font-size: 4.62vh;
    line-height: 7.4vh;
    letter-spacing: 0.14em;

    @media (min-width: 1921px), (min-height: 1081px) {
      font-size: 50px;
      line-height: 80px;
    }

    @media (max-width: 767px), (max-width: 900px) and (orientation: landscape) {
      font-size: 40px;
      line-height: 60px;
      text-align: center;
    }
  }

  &--h2 {
    font-size: 35px;
    line-height: 30px;
    letter-spacing: 0.32em;
    padding: 3.385vw 0 2.083vw 0;

    @media (min-width: 1921px), (min-height: 1081px) {
      padding: 65px 0 40px 0;
    }

    @media (max-width: 1024px) {
      letter-spacing: 0.1em;
    }

    @media (max-width: 1023px) {
      line-height: 1.2em;
    }
  }
}

.h2a {
  position: relative;

  &--invert {
    &::after {
      left: auto !important;
      right: 7.4vh;
    }
  }

  &::after {
    position: absolute;
    left: 7.4vh;
    top: -1.85vh;
    content: attr(data-text);
    font-size: 8.958vw;
    line-height: 8.958vw;
    text-transform: none;
    font-weight: 800;
    letter-spacing: normal;
    color: rgba(#fff, 0.02);
    white-space: nowrap;
    pointer-events: none;

    @media (min-width: 1921px), (min-height: 1081px) {
      font-size: 172px;
      line-height: 172px;
      left: 80px;
      top: -20px;
    }

    @media (max-width: 1024px) {
      left: 3.125vw;
      top: -1vh;
    }

    @media (max-width: 1023px) {
      display: none;
    }
  }
}

// bootstrap overrides
.btn:focus, button:focus {
  box-shadow: none !important;
}

.accordion-button::after,
.accordion-button:not(.collapsed)::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%238dd363'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e")
}

.sw-tournament {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
