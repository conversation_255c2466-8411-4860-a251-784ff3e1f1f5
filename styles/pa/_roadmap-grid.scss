@import "../global/variables";

#roadmap-games-grid, #live-games-grid {
  width: 100%;
  //height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1 1;
}

.device-mobile {
  .roadmap-toolbar {
    .toolbar-root {
      padding: 4px 0;

      button {
        font-size: 0.76rem;
        padding: 2px;
        margin: 2px;
      }
      .dropdown  button {
        height: calc(100% - 4px);
      }
    }
  }
}
.roadmap-category-banner {
  video {
    width: 100%;
  }
}
.roadmap-toolbar {
  position: relative;
  background-color: $mainDark;
  z-index: 1000;
  right: 0;
  left: 0;
  display: flex;
  flex-wrap: wrap;

  & .toolbar-root {
    display: flex;
    position: relative;
    align-items: center;
    padding: 4px 24px;

    & .dropdown {
      height: 100%;

      & button {
        height: 100%;
      }
    }

    & .dropdown-item {
      padding: .25em;
    }

    & .dropdown-menu.show {
      border: 1px solid rgba(141, 211, 99, 0.5);
      min-width: 400px;
      max-width: calc(100vw  - 20px);
      //max-height: 300px;
      max-height: calc(100vh - #{$header-height} - 50px - 6.25vw - 45px);
      overflow: auto;
      //margin-left: 4px;
      margin-left: 0;
      columns: auto 180px;
      column-gap: .75em;
      padding: .75em;

      .device-mobile & {
        max-height: calc(100vh - #{$header-height} - 40px - 45px);
        columns: auto 142px;
        font-size: 13px;
        min-width: calc(100vw - 2px);
        max-width: calc(100vw - 2px);
        column-fill: auto;
      }
      .device-mobile.in-portrait & {
        max-height: calc(100vh - #{$header-height} - 40px - 23.3333333333vw - 45px);
        column-fill: balance;
      }

      .device-tablet.in-landscape &,
      .device-tablet.in-portrait & {
        max-height: calc(100vh - #{$header-height} - 50px - 6.25vw - 45px);
        columns: auto 180px;
        font-size: 16px;
        min-width: 400px;
        max-width: calc(100vw  - 20px);
        column-fill: balance;
      }

      & .dropdown-item {
        color: $textColor;
        //display: flex;
        //flex-direction: row;

        & img {
          max-width: 30px;
          margin-right: 8px;
          font-size: 0;
        }

        //&-txt {
        //  line-height: 30px;
        //  padding-left: 0.5rem;
        //}

        &.active {
          color: $secondaryColor;
          border: 1px solid rgba(141, 211, 99, 0.5);
          background-color: transparent;
        }

        &:hover {
          color: $secondaryColor;
          background-color: rgba(141, 211, 99, 0.2);
        }
      }
    }

    button {
      border: 1px solid rgba($white, 0.23);
      padding: 5px 15px;
      margin: 0 4px;
      color: $white;
      font-size: 0.875rem;
      min-width: 64px;
      box-sizing: border-box;
      transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
      font-family: $fontRoboto;
      font-weight: 500;
      line-height: 1.75;
      border-radius: 4px;
      letter-spacing: 0.02857em;
      text-transform: uppercase;

      &.active {
        color: $black;
        background-color: $secondaryColor;
        border: 1px solid rgba(141, 211, 99, 0.5);
      }

      &:hover {
        color: $secondaryColor;
        text-decoration: none;
        background-color: transparent;
      }
    }

  }
}

div.dropdown-menu.show {
  color: $textColor;
  background-color: $mainDark;

  .dropdown-item {
    background-color: $mainDark;

    &.active {
      color: $black;
      background-color: $secondaryColor;
    }
    &:focus,
    &:hover {
      color: $secondaryColor;
      background-color: $mainDark;
    }
  }
}

//button.header-banner-btn.dropdown-toggle.btn.btn-primary {
//  color: inherit;
//  background-color: inherit;
//  font-size: inherit;
//  border: inherit;
//  line-height: inherit;
//}
