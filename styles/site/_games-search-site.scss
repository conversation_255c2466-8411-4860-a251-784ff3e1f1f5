@import "../global/variables";
$secondaryColor: #b3b3b3;

.sw-search {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1100;
  background-color: rgba($mainDark, .95);
  width: 100%;
  height: 100%;
  transition: opacity, background .2s ease;

  &-control {
    background-color: $secondaryColor;

    & .f_filters_btn {
      background-color: $secondaryColor;

      &:hover, &:active, &:focus, &:not(:disabled):not(.disabled):active {
        background-color: $secondaryColor;
        color: $mainDark;
        box-shadow: none;
      }
    }
  }
  &-submenu {
    background-color: $secondaryColor;
  }
  &__btn {
    height: auto;
    bottom: 0;
  }
  &__input {
    @media (max-width: 480px) {
      border-color: $secondaryColor;
      padding-right: 60px;
    }
  }
  &__close {
    @media (max-width: 480px) {
      svg text {
        fill: $mainDark;
      }
    }
  }
}
.tagify__dropdown__wrapper {
  border-color: $secondaryColor!important;

  .tagify__dropdown__item {
    color: $secondaryColor;
    border: none;
  }
}

.sw-search-container {
  //width: 100%;
  //height: 100%;
  max-width: 1200px;
  padding: 20px var(--bs-gutter-x,.75rem) 70px;
  margin: auto;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  overflow-y: auto;
  -ms-overflow-style: none;
  //  scrollbar-width: none

  @media (max-width: 1199px) {
    padding: 15px
  }

  &.search-col {
    padding: 0;
  }
}

.sw-search-container::-webkit-scrollbar {
  width: 0;
  background: 0 0
}

.sw-search-container__menu {
  width: 100%;
  margin-bottom: 36px;
  position: relative;
  flex: 0.1;

  .sw-search-container__menu {
    margin-bottom: 0;
  }
}

@media (max-width: 480px) {
  .sw-search-container__menu.open .sw-search__close text {
    fill: $mainDark;
  }
}

.sw-search-container__result {
  //margin-top: 50px;
  margin-bottom: auto;
  width: 100%;
  flex: 0.9;
  position: relative;

  //@media (max-width: 480px) {
  //  margin-top: 0;
  //}
}

.sw-search-result__data {
  bottom: 0;
  display: block;
  left: 0;
  position: absolute;
  right: 0;
  top: 40px;
  width: 100%;
  height: 100%;
}

.sw-search-result__title {
  //display: block;
  font-family: 'Arial', sans-serif;
  //font-family: Oswald, sans-serif;
  color: $secondaryColor;
  //text-align: center;
  //margin-bottom: 20px;

  @media (max-width: 480px) {
    color: #fff;
  }

  strong {
    text-transform: capitalize;
    letter-spacing: 0;
    font-weight: bold;
    font-size: $minTitleFontSize;

    @media (min-width: 768px) {
      font-size: $midTitleFontSize;
    }
    @media (min-width: 1200px) {
      font-size: $maxTitleFontSize;
    }
  }
}

.sw-search-result__title strong #search {
  margin-right: 20px
}

.sw-search-result__btnTop {
  margin: auto;
  margin-top: 25px;
  display: flex;
  align-items: center;
  justify-content: center;

  @media (max-width: 480px) {
    margin-top: 30px
  }
}

.sw-search-result__btnTop a {
  text-transform: uppercase;
  font-weight: 700;
  letter-spacing: .12em;
  font-size: 15px;
  line-height: 1;
  white-space: nowrap;
  color: $secondaryColor;
  border-radius: 29px;
  border: 2px solid $secondaryColor;
  padding: 21px 76px;
  transition: all .2s ease
}

.sw-search-result__btnTop a:hover {
  color: #181717;
  background-color: $secondaryColor
}

.sw-search-result .sw-search-result-item:last-of-type {
  border-bottom-color: transparent
}

.sw-search-result-item {
  padding: 10px;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  border-bottom: 1px solid rgba(255, 255, 255, .1);
  transition: all .2s ease;
  position: relative;
  color: $textColor;

  &:hover {
    color: $secondaryColor;

    .search-result-text__title {
      color: #fff;
    }
  }

  @media (max-width: 480px) {
    flex-wrap: wrap;
    padding: 0;
    min-height: 20px;
    margin-bottom: 15px;
  }
}

.sw-search-result-item::after {
  transition: all .2s ease;
  opacity: 0;
  z-index: -1;
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(270deg, rgba(0, 0, 0, 0) 0, rgba(#fff, .1) 100%);

  @media (max-width: 480px) {
    opacity: 1;
  }
}

.sw-search-result-item__img {
  flex-shrink: 0;
  width: 100%;
  max-width: 176px;
  min-height: 100px;
  background-color: $mainDark;
  margin-right: 20px;

  @media (max-width: 480px) {
    max-width: 100%;
    margin-right: 0
  }
}

.sw-search-result-item__desc {
  display: flex;
  flex-direction: column;
  height: 100%;
  margin: auto;
  margin-left: 0;

  @media (max-width: 480px) {
    margin-top: 0
  }
}

.sw-search-result-item:hover {
  opacity: .9
}

.sw-search-result-item:hover::after {
  opacity: 1
}

.device-desktop .sw-search-result-item {
  flex-wrap: nowrap
}

.device-mobile .sw-search-result-item {
  flex-wrap: nowrap;

  @media (max-width: 480px) {
    flex-wrap: wrap
  }
}

.search-result-text {
  height: 100%;
  display: flex;
  flex-direction: column;

  &__title {
    color: $secondaryColor;
    letter-spacing:0;

    @media (max-width: 480px) {
      color: #fff;
    }
    @media (min-width: 1200px) {
      font-size: 24px;
    }
  }
  &__tags {
    margin-top: 10px;

    @media (max-width: 480px) {
      margin-top: 0;
    }
    span {
      letter-spacing:0;
      margin-right: 4px;

      @media (min-width: 1200px) {
        font-size: 16px;
      }
    }
  }

  @media (max-width: 480px) {
    padding: 15px;
  }
}
