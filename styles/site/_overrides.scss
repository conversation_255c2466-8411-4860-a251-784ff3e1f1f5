// font overrides
body,
.game-hover__btn.btn,
.game-hover__text span {
  font-family: 'Arial', sans-serif;
}

// color overrides
.responsible-overlay__title,
.responsible-list__dropdown button:hover,
.blockcenter .accent {
  color: #fff;
}

// header banner
.header {
  .swiper {
    overflow: visible;
  }
}
.header-banner {
  @media (min-width: 768px) {
    .device-mobile & {
      height: 56.25vw;
    }
    .device-desktop & {
      z-index: 0;
      height: calc(100vh - #{$header-height} - 100px);
      max-height: 1000px;
      min-height: 400px;

      .header-banner-figure {
        bottom: -100px
      }
    }
  }
}
.header-banner-content {
  &-title,
  &.static .header-banner-content-title,
  &.static .header-banner-content-text {
    font-family: 'Arial', sans-serif;
    text-transform: none;
    letter-spacing: initial;
  }
  &-title .accent {
    color: #fff;
  }
}
.header-banner-btn {
  &,
  .dropdown & {
    color: #cecccc;

    &.accent {
      border: 1px solid rgba(#fff,.3);
    }

    &:hover {
      color: #989898;
    }
    &:active, &:focus {
      color: #fff;
    }
  }
}

// loading spinner
.loading-spinner .pa-spinner,
.loading-spinner .pa-spinner-text {
  color: #fff;
}

// footer
.footer {
  &__connected ul li a:hover,
  &__contact ul li a:hover {
    color: #b3b3b3;
  }
}

// swiper
.swiper .game-label {
  font-family: 'Arial', sans-serif;
  font-size: $minTextFontSize - 2px;

  @media (min-width: 1900px) {
    font-size: $maxTextFontSize - 2px;
  }
}

// section
.section-title {
  text-transform: none;
  letter-spacing: initial;
  font-family: 'Arial', sans-serif;
}

// news
.news-article {
  max-width: 1200px;
  font-family: 'Arial', sans-serif;

  &__headline {
    line-height: 1.3;
    flex-wrap: wrap;
  }
  &__divider {
    background-color: #fff;
  }

  &__label,
  &__title,
  &__desc a {
    color: #fff;
  }
  &__label {
    font-family: 'Arial', sans-serif;
  }
}
.sw-news__items {
  max-width: 1200px;
  margin-left: auto!important;
  margin-right: auto!important;

  &.news-home {
    max-width: initial;
    margin-bottom: 36px;

    .news-div {
      .news-item {
        @media (max-width: 575px) {
          width: 300px;
        }
      }
    }
  }

  .sw-news-grid {
    .news-div {
      .news-item {
        border: 1px solid rgba(#fff,.3);
        color: #fff;
        background: $mainDark;

        @media (min-width: 768px) {
          margin: 0 11px 65px;
        }

        &:hover {
          color: #989898;
        }
      }
      .news-item__info {
        font-family: 'Arial', sans-serif;
      }
      .news-item__date {
        color: #b3b3b3;
      }
    }
  }
}
#app-news-list {
  overflow: hidden;

  .sw-news-grid {
    margin: 0 -11px;
  }
}
.header-news {
  .header-banner {
    max-width: 1200px;
    margin: auto;
    height: auto;
  }
}
.header-banner-content-news-title,
.news-header-banner-content {
  .header-banner-content-title {
    font-weight: bold;
    font-size: $minTitleFontSize;

    @media (min-width: 768px) {
      font-size: $midTitleFontSize;
    }
    @media (min-width: 1200px) {
      font-size: $maxTitleFontSize;
    }
  }
  .header-banner-content-text {
    color: #b3b3b3;
    font-size: $minTextFontSize;

    @media (min-width: 768px) {
      font-size: $midTextFontSize;
    }
    @media (min-width: 1200px) {
      font-size: $maxTextFontSize;
    }
  }
}

// egt banner
.page-egt {
  @media (max-width: 767px) {
    .header-banner-figure {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      top: 0;
    }
    .header-banner-container {
      min-height: 56.25vw;
    }
    .header-banner-content {
      justify-content: center!important;
    }
    .header-banner-content-text {
      max-width: 300px;
    }
    .header-banner-btn-container {
      width: 100%;
      max-width: none;
    }
  }
}
.jp-odometer {
  color: #fff;
  font-weight: bold;
  font-size: 7.292vw;
  //font-size: $minTitleFontSize;

  @media (max-width: 480px) {
    font-size: 40px;
  }
  @media (min-width: 1900px) {
    font-size: 140px;
  }
}
.total__desc {
  color: #b3b3b3;
  font-size: 2.59vw;

  @media (max-width: 480px) {
    font-size: 17px;
  }
  @media (min-width: 1900px) {
    font-size: 28px;
  }
  .accent {
    color: #fff
  }
}
