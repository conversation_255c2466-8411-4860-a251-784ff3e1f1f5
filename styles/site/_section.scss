@import "../global/variables";

.section-sw-info {
  position: relative;
  display: flex;
  width: 100%;
  flex-direction: row;
  max-width: $sectionInfoMaxWidth;
  margin: 60px auto;

  @media (max-width: 480px) {
    flex-direction: column;
  }

  & .sw-desc {
    flex: 0.7;

    @media (max-width: 480px) {
      order: 2;
      padding-top: 20px;
    }

    & p {
      font-size: 1em;

    }
  }

  & .sw-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 0.3;

    & img {
      display: block;
      width: 50%;
      max-width: 100px;
      height: auto;
    }
  }
}

.sw-section {
  position: relative;
  display: block;
  width: 100%;
  margin-bottom: 36px;

  & .section-header {
    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: 20px;

    @media (max-width: 767px) {
      margin-bottom: 10px;
    }

    & > * {
      width: auto;
    }

    & .btn {
      vertical-align: center;
      display: flex;
      align-items: center;
      font-size: $minButtonFontSize;

      @media (min-width: 768px) {
        font-size: $midButtonFontSize;
      }
      @media (min-width: 1200px) {
        font-size: $maxButtonFontSize;
      }
    }
  }

  &-title {
    display: block;
    width: 100%;
    color: #fff;
    text-align: left;
    padding: 0;
    margin-bottom: 10px;
    font-weight: bold;
    font-size: $minSectionTitleFontSize;

    @media (min-width: 768px) {
      font-size: $midSectionTitleFontSize;
      margin-bottom: 20px;
    }
    @media (min-width: 1200px) {
      font-size: $maxSectionTitleFontSize;
    }

    .section-header & {
      margin: 0;
    }
  }

  &-description {
    position: relative;
    z-index: 1;
    display: block;
    width: 100%;
    color: #b3b3b3;
    text-align: left;
    text-transform: none;
    margin-bottom: 36px;
    font-size: $minTextFontSize;

    @media (min-width: 768px) {
      font-size: $midTextFontSize;
      margin-bottom: 4.167vw;
    }
    @media (min-width: 950px) {
      max-width: 950px;
    }
    @media (min-width: 1200px) {
      font-size: $maxTextFontSize;
      margin-bottom: 60px;
    }

    p {
      margin-bottom: 1em;
    }

    a {
      color: #fff;

      &:hover {
        color: #989898;
      }
    }
    a.btn {
      &, &:hover {
        color: #000;
      }
    }
  }

  &-columns {
    @media (min-width: 600px) and (max-width: 1200px) {
      display: flex;
      margin: 0 -7px;

      .sw-section-col {
        flex: 1;
        margin: 0 7px;
      }
      .sw-section {
        display: flex;
        flex-direction: column;
        height: 100%;
      }
      .section-header {
        flex-shrink: 0;

        .sw-section-title {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-right: 10px;
        }

        .btn {
          flex-shrink: 0;
        }

        & + a {
          flex-grow: 1;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}

.sw-offices-map {
  margin: 3vw auto;
  position: relative;
  max-width: 1631px;

  @media (max-width: 1200px) {
    display: none;
  }

  &-img {
    margin: auto;
  }
}
.sw-offices-locations {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;

  &-pin {
    display: inline-table;
    position: absolute;
    width: 1.563vw;
    height: 1.563vw;
    border-radius: 50%;
    background: $white;
    box-shadow: 0 0 25px $white;
    cursor: pointer;
    transition: all .2s ease;

    @media (min-width: 1921px) {
      width: 30px;
      height: 30px;
    }

    &.active {
      background: #b3b3b3;
      box-shadow: 0 0 25px #b3b3b3;

      .sw-offices-locations-title {
        opacity: 1;
      }
    }
  }

  &-title {
    opacity: 0;
    pointer-events: none;
    position: absolute;
    top: 0;
    left: 0;
    color: #fff;
    white-space: nowrap;
    transition: $tr2;
    text-transform: capitalize;
    will-change: transform;
    //font-size: 1.406vw;
    padding: 6px 18px;
    background-color: $mainDark;
    z-index: 10;
    transform: translate(28%,-79%);
    border-radius: 25px;
    font-weight: bold;
    font-size: $minTextFontSize;

    @media (min-width: 768px) {
      font-size: $midTextFontSize;
    }
    @media (min-width: 1200px) {
      font-size: $maxTextFontSize;
    }

    &.on-left {
      transform: translate(-115%, -50%);
    }

    &.on-right {
      transform: translate(25%, -50%);
    }

    &.on-bottom {
      transform: translate(-50%, 35%);
    }

    &.on-top {
      transform: translate(-50%, -125%);
    }
  }
}

.section-swiper-awards {
  & .swiper-slide {
    min-height: 256px;
    width: auto;
    padding-right: 26px;

    &.img {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      object-position: center;
      object-fit: cover;
    }
  }
}

.section-about {
  &-description {
    display: block;
    width: 100%;
    font-size: 1.4em;
    color: $textColor;
    margin: 4px auto;
    text-align: justify;

    & .accent {
      color: $secondaryColor;
    }
  }
}
