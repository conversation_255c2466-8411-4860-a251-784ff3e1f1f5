@import "../global/variables";

.swiper-header {
  &.swiper {
    width: 100%;
    height: 100%;

    @media (max-width: 767px) {
      padding-bottom: 50px; // space for the pagination;
      margin-bottom: 20px;
    }

    & .swiper-pagination {
      @media (min-width: 768px) {
        bottom: 20px;
      }

      & .swiper-pagination-bullet {
        width: 32px;
        height: 32px;
        background-image: radial-gradient($white, transparent, transparent);
        background-color: transparent;
      }
    }

    & .swiper-slide {

      /* Center slide text vertically */
      display: flex;
      justify-content: center;
      align-items: center;

      & img {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

// swiper section
.swiper-section {
  margin: 0 0 30px;

  @media (min-width: 768px) {
    margin-bottom: 4.167vw;
  }
  @media (min-width: 1440px) {
    margin-bottom: 60px;
  }

  &-title {
    color: #fff;
    margin: 0 0 .25em;//0 4% .25em;
    text-transform: capitalize;
    position: relative;
    font-weight: bold;
    font-size: $minSectionTitleFontSize;

    @media (min-width: 768px) {
      font-size: $midSectionTitleFontSize;
    }
    @media (min-width: 1200px) {
      font-size: $maxSectionTitleFontSize;
    }

    &-text {
      color: #fff;
      font-weight: bold;
      //margin-bottom: .2em;
      font-size: $minSectionTitleFontSize;

      @media (min-width: 768px) {
        font-size: 12px;
      }
      @media (min-width: 801px) {
        font-size: $midSectionTitleFontSize;
      }
      @media (min-width: 1200px) {
        font-size: $maxSectionTitleFontSize;
      }
    }

    &:hover .swiper-section-title-expand-arrow {
      opacity: 1;
      color: #fff!important;
    }
  }

  &-title-link {
    color: #fff;
    font-size: 1em;
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    cursor: pointer;

    &:hover .swiper-section-title-expand-all {
      @media screen and (min-width: 1025px) {
        max-width: 200px;
        opacity: 1;
        margin-left: 10px
      }
    }
  }

  &-title-expand-all {
    font-weight: normal;
    color: #fff!important;
    display: inline-flex;
    font-size: 0.75em;
    line-height: 1;
    margin: .1em 4px 0 0;
    max-width: 0;
    transition: max-width 1s, margin-left 1s, opacity 1s;
    white-space: nowrap;
    cursor: pointer;
    opacity: 0;

    @media screen and (max-width: 800px) {
      font-size: 8px;
    }
  }

  &-title-expand-arrow {
    opacity: 0;
    //background: url(data:image/svg+xml;base64,PHN2ZyBjbGFzcz0ic3ZnLWljb24iIHN0eWxlPSJ3aWR0aDoxZW07aGVpZ2h0OjFlbTt2ZXJ0aWNhbC1hbGlnbjptaWRkbGUiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgZmlsbD0iY3VycmVudENvbG9yIiBvdmVyZmxvdz0iaGlkZGVuIj48cGF0aCBkPSJNMzg0LjIxMyAxNzAuNjY3TDcyNS41NDcgNTEyIDM4NC4yMTMgODUzLjMzMyAyOTguNjY3IDc2OCA1NTQuODggNTEyIDI5OC42NjcgMjU2eiIgZmlsbD0iIzhkZDM2MyIvPjwvc3ZnPg==) no-repeat 50% 50%;
    background: url(data:image/svg+xml;base64,PHN2ZyBjbGFzcz0ic3ZnLWljb24iIHN0eWxlPSJ3aWR0aDoxZW07aGVpZ2h0OjFlbTt2ZXJ0aWNhbC1hbGlnbjptaWRkbGUiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgZmlsbD0iY3VycmVudENvbG9yIiBvdmVyZmxvdz0iaGlkZGVuIj48cGF0aCBkPSJNMzg0LjIxMyAxNzAuNjY3IDcyNS41NDcgNTEyIDM4NC4yMTMgODUzLjMzMyAyOTguNjY3IDc2OCA1NTQuODggNTEyIDI5OC42NjcgMjU2eiIgZmlsbD0iI2ZmZiIvPjwvc3ZnPg==) no-repeat 50% 50%;
    width: .75em;
    height: .75em;
    display: block;
    position: relative;
    will-change: opacity;
    transition: opacity 1s;
    margin-top: .1em;
  }

  &-title-flag-icon {
    height: 1em;
    margin-right: 6px;
    aspect-ratio: 4 / 3;
  }
}

// swiper buttons
.swiper-section .swiper-button {
  .device-type-desktop & {
    transition: all 1s;
    cursor: pointer;
    background: rgba($mainDark, .5);
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    transform: none;
    margin: 0;
    height: 100%;
    width: 4%;
    opacity: 0
  }

  &:after {
    background: url("data:image/svg+xml;base64,PHN2ZyBjbGFzcz0ic3ZnLWljb24iIHN0eWxlPSJ3aWR0aDoxZW07aGVpZ2h0OjFlbTt2ZXJ0aWNhbC1hbGlnbjptaWRkbGUiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgZmlsbD0iY3VycmVudENvbG9yIiBvdmVyZmxvdz0iaGlkZGVuIj48cGF0aCBkPSJNMzg0LjIxMyAxNzAuNjY3TDcyNS41NDcgNTEyIDM4NC4yMTMgODUzLjMzMyAyOTguNjY3IDc2OCA1NTQuODggNTEyIDI5OC42NjcgMjU2eiIgZmlsbD0iI2ZmZiIvPjwvc3ZnPg==") no-repeat 50% 50%;
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;

    .device-type-mobile & {
      opacity: 0
    }
  }

  &:hover {
    &:after {
      transform: scale(1.25)
    }
  }

  &-prev {
    left: 0;
    right: auto;

    &:after {
      transform: scale(-1)
    }

    &:hover:after {
      transform: scale(-1) scale(1.25)
    }
  }

  &-disabled {
    opacity: 0;
    pointer-events: none
  }
}

// swiper container btns
.swiper-section .swiper-container {
  &:hover .swiper-button {
    opacity: 1
  }

  &:hover .swiper-button-disabled {
    opacity: 0;
    pointer-events: none
  }
}

// swiper games
.swiper-games {
  position: relative !important;
  //margin: 0!important;//0 4% !important;
  //padding: 0 4%!important;

  .device-desktop & {
    overflow: visible !important;
    transition: transform 0.2s;

    &:hover {
      .swiper-slide {
        opacity: .5;

        .swiper-slide {
          opacity: 1;
        }
      }
    }
  }

  .swiper-slide {
    width: 19.67%;
    padding: 0 2px;
    position: relative;
    transition: transform 0.2s;

    .device-desktop & {
      &:hover {
        cursor: pointer;
        z-index: 3;
        opacity: 1;
        transform: scale(1.1);
      }
    }

    & .game-title {
      padding-top: 16px;
      line-height: 2em;
      font-family: $fontRoboto;
      font-size: 1.2em;
      color: $secondaryColor !important;
    }

    & .game-desc {
      font-family: $fontRoboto;
      font-size: 1em;
      color: $textColor !important;
    }

    @media screen and (max-width: 500px) {
      width: 50%
    }
    @media screen and (min-width: 501px) and (max-width: 800px) {
      width: 33.33333333%
    }
    @media screen and (min-width: 801px) and (max-width: 1100px) {
      width: 25%
    }
    @media screen and (min-width: 1101px) and (max-width: 1400px) {
      width: 20%
    }
    @media screen and (min-width: 1401px) and (max-width: 1700px) {
      width: 16.66666667%
    }
    @media screen and (min-width: 1701px) and (max-width: 2000px) {
      width: 14.28571429%
    }
    @media screen and (min-width: 2000px) {
      width: 12.5%
    }

    a {
      width: 100%;
      position: relative;
      display: block
    }

    img {
      display: block;
      border-radius: 4px;
      overflow: hidden;
      max-width: 100%;
      height: auto;
    }
  }
  &.spaced {
    .swiper-slide {
      padding: 0 7px;

      .swiper-slide {
        padding: 0;
      }
    }
  }

  .swiper-button-next,
  .swiper-button-prev,
  .swiper-rtl .swiper-button-next,
  .swiper-rtl .swiper-button-prev {
    width: calc(4% + 2px);
    top: 0;
    bottom: 0;
    height: auto;
    margin: 0;
    transition: $tr2;
  }
  .swiper-button-next,
  .swiper-rtl .swiper-button-prev {
    right: 0;
    left: auto;
  }
  .swiper-button-prev,
  .swiper-rtl .swiper-button-next {
    right: auto;
    left: 0;
  }

  .swiper-button-next.swiper-button-disabled,
  .swiper-button-prev.swiper-button-disabled {
    opacity: 0;
  }
}

// swiper wrapper
.swiper-wrapper.flex-wrap {
  flex-wrap: wrap;

  .swiper-slide {
    margin: 0 0 4vw;

    @media screen and (max-width: 499px) {
      width: 50%;
      margin-bottom: 7.5vw
    }
    @media screen and (min-width: 500px) and (max-width: 799px) {
      width: 33.333333%;
      margin-bottom: 7.5vw
    }
    @media screen and (min-width: 800px) and (max-width: 1099px) {
      width: 25%;
      margin-bottom: 5.5vw
    }
    @media screen and (min-width: 1100px) and (max-width: 1399px) {
      width: 20%;
      margin-bottom: 4.5vw
    }
    @media screen and (min-width: 1400px) {
      width: 16.66666667%;
      margin-bottom: 4vw
    }
  }
}

// swiper modal
.swiper-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1050;
  display: none;
  overflow: hidden;
  outline: 0
}

.swiper-modal-open {
  overflow: hidden;
  height: 100vh
}

.swiper-modal-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #141414
}

// swiper modal header
.swiper-modal-header {
  display: flex;
  flex-shrink: 0;
  padding: 0 0 0 16px;
  height: $header-height;
  align-items: center;
  justify-content: space-between;
  background: $mainDark;

  &-logo-img {
    display: block;
    height: 25px
  }

  &-close {
    right: 0;
    top: 0;
    cursor: pointer;
    width: $header-height;
    height: $header-height;
    transition: all .2s ease;
    position: relative;

    @media (max-width: 1365px) {
      transform: scale(.9)
    }

    &-icon {
      width: 37px;
      height: 24px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);

      @media (max-width: 1365px) {
        transform: scale(0.9) translate(-50%, -50%);
      }

      span {
        position: absolute;
        top: 0;
        left: 7px;
        display: block;
        height: 2px;
        width: 31px;
        background-color: $secondaryColor;
        transform-origin: 0 center;

        &:first-child {
          transform: rotate(45deg);
        }

        &:last-child {
          margin-top: 22px;
          transform: rotate(-45deg);
        }
      }
    }
  }

  &-title {
    padding-left: calc(4% - 15px);
    font-size: 20px;
    line-height: normal;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    @media screen and (max-width: 400px) {
      font-size: 16px
    }
  }
}

// swiper modal body
.swiper-modal-body {
  flex-grow: 1;
  overflow: auto;

  .swiper-modal-actions-list {
    display: none
  }

  .swiper-modal-info-list {
    font-size: 18px;
    margin-bottom: 30px
  }

  .swiper-modal-info-list-item {
    padding: 4px 20px
  }

  .device-type-mobile.device-landscape & {
    display: flex;
    align-items: flex-start;
    padding: 15px;

    .swiper-modal-info-list {
      display: none
    }
  }
}

// swiper modal footer
.swiper-modal-footer {
  padding: 5px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  flex-shrink: 0;
  border-top: 1px solid hsla(0, 0%, 100%, .2);

  @media screen and (max-width: 400px) {
    font-size: 14px;
    flex-wrap: wrap;
  };

  .swiper-modal-actions-list {
    margin-right: 0;
    width: 100%
  }

  .swiper-modal-actions-list-item {
    flex-shrink: 0;
    padding: 4px 20px
  }

  .swiper-modal-actions-list-img {
    height: 50px;
    width: 50px;

    @media screen and (max-width: 400px) {
      height: 40px;
      width: 40px
    }
  }

  .swiper-modal-info-list {
    display: none
  }

  .device-type-mobile.device-landscape & {
    justify-content: flex-start;

    .swiper-modal-actions-list {
      width: 300px;
      max-width: 45%;
      margin-right: 15px;
      flex-shrink: 0
    }

    .swiper-modal-actions-list-item {
      padding: 4px 15px
    }

    .swiper-modal-info-list {
      display: inline-flex
    }

    .swiper-modal-info-list-item {
      padding: 4px 20px
    }
  }
}

// swiper modal content
.swiper-modal-content {
  padding: 15px;

  .device-type-mobile.device-landscape & {
    overflow: auto;
    padding: 0
  }
}

.swiper-modal-title {
  font-family: $fontOswald;
  display: inline-block;
  font-size: 26px;
  font-weight: 400;
  margin-bottom: 30px;
  text-align: center;
  position: relative;

  &:after {
    position: absolute;
    z-index: 2;
    content: "";
    width: 350%;
    left: -250%;
    bottom: -15px;
    border-bottom: 2px solid rgba($secondaryColor, 1);

    @media (max-width: 480px) {
      width: 282%
    }
  }
}

.swiper-modal-text {
  font-size: 14px;
  line-height: 1.9;
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 30px
  }
}

.swiper-modal-cert {
  font-size: 14px;
  line-height: 1.9;

  .accent {
    font-size: 16px;
    font-weight: 700
  }
}

.swiper-modal-figure {
  .device-type-mobile.device-landscape & {
    width: 300px;
    max-width: 45%;
    margin-right: 15px;
    flex-shrink: 0;

    @media screen and (max-width: 500px) {
      width: 300px;
      flex-shrink: 0
    }
  }

  img, video {
    width: 100%
  }
}

.swiper-modal-actions-list {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  &-item {
    padding: 4px
  }

  &-img {
    height: 40px;
    width: 40px;
    flex-shrink: 0;

    @media screen and (max-width: 400px) {
      height: 30px;
      width: 30px
    }
  }
}

.swiper-modal-info-list {
  font-family: $fontOswald;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  @media screen and (min-width: 1025px) {
    font-size: 14px
  }

  &-item {
    padding: 4px 8px;
    flex: 1;
    text-align: center;
    border-right: 1px solid $white;

    &:last-child {
      border-right: 0
    }
  }

  .device-type-mobile.device-landscape & {
    @media screen and (max-width: 500px) {
      flex-grow: 0;
      margin-top: 20px
    }
  }
}

.swiper-modal-wrapper {
  display: flex;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  will-change: scroll-position;
  box-sizing: border-box;
}

// swipe modal mini
.swiper-modal-mini {
  position: absolute;
  background: $mainDark;
  width: 420px;
  box-shadow: 0 3px 10px rgba($mainDark, .75);
  border-radius: 6px;
  overflow: hidden;
  opacity: 0;
  //top: -10000px;
  //left: -10000px;
  //transform: scaleX(0) scaleY(0);
  will-change: transform;

  @media screen and (max-width: 400px) {
    width: calc(100vw - 20px)
  }
  @media screen and (max-width: 500px) {
    .device-type-mobile.device-landscape & {
      width: auto;
      max-width: calc(100vw - 20px);

      .swiper-modal-container {
        display: flex;
        flex-direction: row;
      }
    }
  }

  .swiper-modal-content {
    font-family: $fontOswald;
    padding: 16px 12px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    text-align: center;

    @media screen and (max-width: 400px) {
      padding: 16px 10px;
      font-size: 14px;
      flex-direction: column
    }
    @media screen and (max-width: 500px) {
      .device-type-mobile.device-landscape & {
        flex-direction: column;
        justify-content: center
      }
    }
  }

  .swiper-modal-info-list {
    flex-grow: 1;

    @media screen and (max-width: 400px) {
      width: 100%
    }
  }

  .swiper-modal-actions-list {
    @media screen and (max-width: 400px) {
      width: 100%;
      margin-bottom: 10px;

      .swiper-modal-actions-list-item {
        padding: 4px 20px
      }
    }
  }
}

// swiper modal video
.swiper-modal-video {
  padding: 10px;
  background: rgba($mainDark, .5);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: none;
  overflow: hidden;
  outline: 0;
  z-index: 100;

  .swiper-modal-container {
    box-shadow: 0 0 1px 0 hsla(0, 0%, 100%, .4);
    border-radius: 4px
  }
}

.section-swiper-awards {

  .swiper-button-prev,
  .swiper-button-next {
    cursor: pointer;
    position: absolute;
    margin: 0;
    top: 50%;
    height: 24px;
    width: 24px;
    transform: translateY(-50%);
    z-index: 10;
    background: no-repeat 50% 50%;
    background-size: auto 100%;
    background-image: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI1LjQuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHZpZXdCb3g9IjAgMCA0My45NSA3My45NSIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNDMuOTUgNzMuOTU7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPHN0eWxlIHR5cGU9InRleHQvY3NzIj4KCS5zdDB7ZmlsbDojRkZGRkZGO30KPC9zdHlsZT4KPGc+Cgk8cGF0aCBjbGFzcz0ic3QwIiBkPSJNMzguODUsNDIuMDdjLTEuMjYsMC0yLjUyLTAuNDYtMy41LTEuMzlMMS42LDguODFDLTAuNDUsNi44Ny0wLjU0LDMuNjUsMS4zOSwxLjYKCQlDMy4zMy0wLjQ1LDYuNTUtMC41NCw4LjYsMS4zOWwzMy43NSwzMS44N2MyLjA1LDEuOTMsMi4xNCw1LjE2LDAuMjEsNy4yMUM0MS41Niw0MS41NCw0MC4yMSw0Mi4wNywzOC44NSw0Mi4wN3oiLz4KPC9nPgo8Zz4KCTxwYXRoIGNsYXNzPSJzdDAiIGQ9Ik01LjEsNzMuOTVjLTEuMzUsMC0yLjcxLTAuNTQtMy43MS0xLjZjLTEuOTMtMi4wNS0xLjg0LTUuMjgsMC4yMS03LjIxbDMzLjc1LTMxLjg3CgkJYzIuMDUtMS45Myw1LjI4LTEuODQsNy4yMSwwLjIxYzEuOTMsMi4wNSwxLjg0LDUuMjgtMC4yMSw3LjIxTDguNiw3Mi41NkM3LjYyLDczLjQ5LDYuMzYsNzMuOTUsNS4xLDczLjk1eiIvPgo8L2c+Cjwvc3ZnPgo=');

    &:after {
      display: none;
    }

    @media (min-width: 1440px) {
      height: 52px;
      width: 52px;
    }

    &.swiper-button-prev {
      left: 0;
      transform: translateY(-50%) scale(-1, -1);
    }

    &.swiper-button-next {
      right: 0;
    }
  }
}

// partners swiper
.swiper-partner {
  max-width: 1200px;
  width: 100%;
  height: calc(80px * 3 + 20px * 2 + 80px) !important;
  padding-bottom: 80px !important;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
  overflow: hidden;

  @media (min-width: 768px) {
    height: calc(120px * 4 + 30px * 3 + 100px) !important;
    padding-bottom: 100px !important;
    .swiper-slide {
      margin-top: 30px;
    }
  }

  .swiper-slide {
    text-align: center;
    height: 80px;
    display: flex;
    padding: 10px;
    margin-top: 20px;
    justify-content: center;
    align-items: center;

    a {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    @media (min-width: 768px) {
      height: 120px;
      padding: 10px 20px;
    }

    img {
      display: block;
      margin: auto;
      max-height: 100%;
      max-width: 100%;
    }
  }

  .swiper-nav {
    position: relative;
    display: inline-flex;
    margin: 10px auto 0;
    align-items: center;
    justify-content: center;
    height: 40px;

    @media (min-width: 768px) {
      margin: 30px auto 0;
    }

    .swiper-pagination,
    .swiper-button-next,
    .swiper-button-prev {
      position: relative;
      top: auto;
      bottom: auto;
      transform: none;
      margin-top: 0;
    }

    .swiper-pagination-bullet {
      background-color: transparent;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      line-height: 1;
      font-size: 1em;
      color: rgba(#fff, .5);
      margin: 0 3px;
      width: 36px;
      height: 36px;
      border-radius: 2px;
      opacity: 1;
    }

    .swiper-pagination-bullet-active {
      color: $textColor;
    }

    .swiper-button-next,
    .swiper-button-prev {
      margin: 0 10px;

      &:after {
        color: $textColor;
        font-size: 20px;
      }
    }
  }
}

// misc
.livecasion-swiper-section {
  margin: 4vw auto;
}
.swiper-dealer {
  .swiper-wrapper {
    margin: 0 -7px;
  }
  .swiper-slide {
    margin: 0 7px;
  }
}
.swiper-centered,
.swiper-live-games,
.swiper.centered {
  .swiper-wrapper {
    @media (min-width: 481px) {
      width: auto;
      justify-content: center;
    }
  }
}
