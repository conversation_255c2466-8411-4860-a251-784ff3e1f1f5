// header banner btn
.header-banner-btn {
  &,
  .dropdown & {
    border: none;
    color: #fff;
    background: none;

    &:hover, &:active, &:focus, &.accent {
      color: #b3b3b3;
      background: none;
    }
  }
}

// header banner: info
.header-banner-info {
  &-val {
    font-weight: 700;
    color: #fff
  }
}

// header btn dropdown
.header-banner-game {
  .dropdown {
    .dropdown-menu {
      background-color: $mainDark;
      overflow: hidden;
      //  background-color: $secondaryColor;
      //  box-shadow: none;
      //  border-color: transparent;

      @media (max-width: 767px) {
        min-width: 148px;
      }
    }
    //.header-banner-btn.dropdown-toggle+.dropdown-menu {
    //  border-color: transparent;
    //
    //  .dropdown-item {
    //    color: #181717;
    //
    //    &:hover {
    //      background-color: rgba(#fff,.3);
    //    }
    //  }
    //}
  }
}