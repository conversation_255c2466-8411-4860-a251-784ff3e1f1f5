@import "../../global/variables";

.header-navbar {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  background-color: $menuBGColor;
  z-index: 1000;
  min-height: $header-height;

  .navbar-toggler:focus {
    box-shadow: 0 0 0 1px;
  }

  &-container {
    height: 100%;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    @media (min-width: 768px) {
      justify-content: flex-start;
    }
  }

  &-spacer {
    background-color: $menuBGColor;
    height: $header-height;
  }

  &-logo {
    flex-shrink: 0;
    margin: 4px 16px 4px 0;

    &-img {
      display: block;
      height: 25px;
    }
  }

  & .navbar-collapse.collapse.show, .navbar-collapse.collapsing {
    width: 100%;

    & a {
      width: 100%;
      //margin: 0;
    }

    & .nav-item {
      width: 100%;
    }
  }

  &-list {
    //background-color: $menuBGColor;
    list-style-type: none;
    margin: 0;
    padding: 0;
    display: flex;
    align-items: flex-start;

    transition: height 0.15s ease-in-out;

    @media (max-width: 991px) {
      flex-direction: column;
      align-items: stretch;
      max-height: calc(100vh - #{$header-height});
      overflow: auto;
      margin-top: 4px;
    }

    & .inner {
      list-style-type: none;
      margin: 0;
      flex-direction: column;
      padding: 0;
      overflow: hidden;
      display: none;
      background-color: darken($menuDarkenColor, .5);
      transition: height 1s ease-out;
      max-height: 0;

      & .dropdown-item {
        margin: 0;
        border: 0;
        //background-color: darken($menuDarkenColor, .5);
      }

      &.show {
        display: flex;
        transition: height 1s ease-in;
        max-height: 100%;
      }
    }

    & .dropdown-menu {
      background-color: $menuBGColor;
      padding: 0;

      & > a.dropdown-item {
        border: none;
        margin: 0;
        background: none;
      }

      @media (min-width: 992px) {
        left: 4px;
        right: 4px;
        border-radius: 0;
        border: none;
        margin-top: 8px;
      }
    }

    & a.nav-link, & a.dropdown-item {
      display: block;
      color: #cecccc!important;
      //color: $secondaryColor !important;
      padding: 4px 8px;
      font-size: 16px;
      //font-weight: bold;
      line-height: 1.75;
      cursor: pointer;
      //border: 1px solid rgba($secondaryColor, .5);
      margin: 0 4px;
      word-break: keep-all;
      white-space: nowrap;
      border-radius: 4px;

      @media (min-width: 992px) {
        //font-size: 1.210vw;
        margin: 0 2px;
      }
      @media (min-width: 1024px) {
        font-size: 14px;
        margin: 0 2px;
      }
      @media (min-width: 1200px) {
        margin: 0 4px;
      }

      &:hover {
        color: #989898!important;
        //color: #000 !important;
        //background-color: $secondaryColor;
      }
      &.active {
        color: #fff!important;
        font-weight: 700;
      }
      //&.active {
      //  color: #fff !important;
      //  background-color: #222;
      //}

      &.toggle {
        position: relative;
        padding-right: 24px;

        &:after {
          content: '+';
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.25em;
          line-height: 1;
        }

        &.active:after {
          content: '-';
        }
      }

      @media (max-width: 991px) {
        margin: 2px 0;
      }
    }

    .nav-item.dropdown {
      .dropdown-menu {
        @media (max-width: 991px) {
          //border: 1px solid rgba($secondaryColor, .5);
          margin: 4px 0 2px;
          padding: 10px;
          border-radius: 0;
        }
      }
    }
  }
  &.navbar-expand-lg .navbar-nav .nav-link {
    padding: 4px 8px;

    @media (min-width: 768px) {
      padding: 4px 6px;
    }
    @media (min-width: 1200px) {
      padding: 4px 8px;
    }
  }

  &.container {
    .container {
      padding: 0;
    }
  }
}
