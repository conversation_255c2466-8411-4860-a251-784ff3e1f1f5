@import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&amp;display=swap');

.welcome-dealers {
  &-page {
    background: linear-gradient(23deg, #510813 0%, #ac172c 70%, #520813 100%);
    font-family: "Lato",sans-serif;
    font-size: 16px;

    //@media (min-width: 1920px) {
    //  background: linear-gradient(90deg, #d149bf 0, #d149bf 50%, #dc374c 51%, #dc374c 100%);
    //
    //  .welcome-dealers-container {
    //    background: linear-gradient(90deg, #d347bd, #dc374c);
    //  }
    //}

    .header.empty {
      margin-bottom: 0;
    }
  }
  &-banner {
    flex-shrink: 0;
    max-width: 1920px;
    margin: auto;

    &-img {
      width: 100%;
      pointer-events: none;
    }
  }
  &-headline {
    margin: 0 auto 1em;
    text-align: center;

    @media (min-width: 768px) {
      margin: 0 auto 2em;
    }

    &-title {
      color: #fff;
      font-weight: 700;
      font-size: 1.5em;
      margin-bottom: .5em;
      /*text-shadow: 1px 1px 1px #000;*/

      @media (min-width: 768px) {
        font-size: 2em;
        margin-bottom: 1em;
      }
    }

    &-text {
      color: #fff;
      font-size: 1em;
      line-height: 1.5;

      @media (min-width: 768px) {
        font-size: 1.3em;
      }
    }
  }
}
.section-register-dealer {
  padding: 1em 0;

  @media (min-width: 768px) {
    padding: 2em 0;
  }
}
.form-register-dealer {
  z-index: 10;
  margin: 0 auto;
  width: 100%;
  border: 2px solid #a8cd3a;
  padding: 1em;
  max-width: 450px;
  border-radius: 20px;
  font-size: 1em;
  background: none;

  @media (min-width: 768px) {
    padding: 1em 2em;
    border-radius: 30px;
    font-size: 1.4em;
  }

  .card-body {
    display: inline;
    padding: 0;
    margin: 0;
  }
  .form-label {
    color: #fff;
    font-size: 1rem;
    display: block;
    margin-bottom: 0.4em;
    font-weight: 600;

    @media (min-width: 768px) {
      font-size: 1em;
    }
  }
  .form-control, .form-control input {
    height: 3.6em;
    padding: 1rem 0.9rem;
    width: 100%;
    outline: none;
    background: hsla(0,0%,100%,.06);
    border: 2px solid #a8cd3a;
    border-radius: 20px;
    font-size: .9em;
    color: #a8cd3a;

    input {
      border: none;
      background: transparent;
    }

    &::placeholder {
      color: #a8cd3a;
    }
    &:focus {
      box-shadow: none;
    }
  }
  .form-check {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0;
    margin: 0;

    &-input {
      position: relative;
      height: 1.5em;
      width: 1.5em;
      align-self: flex-start;
      cursor: pointer;
      appearance: none;
      border: 2px solid #a8cd3a;
      border-radius: 20px;
      margin: 0;
      background: transparent;
      flex-shrink: 0;

      &:checked {
        background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
        background-size: 1.4em 1.4em;
        background-position: center;
      }
      &:focus {
        box-shadow: none;
      }
    }
    &-label {
      margin-left: 0.8rem;

      a {
        color: #a8cd3a;
      }
    }
  }
  .btn {
    color: #000;
    font-weight: 600;
    border-radius: 1.5rem;
    padding: 1rem 0;
    width: 60% !important;
    align-self: center;
    background: #a7cf38;
    /*box-shadow: 0 0 5px 5px #a8cd3a80;*/
    font-size: 1.3rem;
    cursor: pointer;
    line-height: 1.15;
    margin: auto;
    display: block;
    text-transform: uppercase;
    min-width: 260px;

    @media (min-width: 768px) {
      font-size: 1.3em;
    }
  }
  .invalid-feedback {
    border-radius: 12px;
  }
}