@import "../global/reset";
@import "../global/variables";

html {
  overflow-x: hidden;
  scroll-behavior: smooth;
}

/* ensure all pages have Bootstrap CSS */
@import "../../node_modules/bootstrap/scss/bootstrap";
@import "../global/common";
@import "../global/buttons";
@import "../global/fonts";
@import "../global/forms";
@import "../global/flip-count-down";
@import "../global/footer";
@import "../global/sections";
@import "../global/loading-spinner";
@import "../global/header";
@import "../global/cookie-disclaimer";
@import "../global/age-verification";

// @import "need-to-remove";
@import "dialog";
@import "containers";
@import "header/header-navbar";
@import "header/header-logo";
@import "header/overrides";
@import "modal";
@import "section";
@import "swiper";
@import "../global/games-search";
@import "games-search-site";

@import "../pa/pages/page-404";
@import "../global/sections/contact-us";
@import "../global/sections/section-360";
@import "../global/sections/game-features";
@import "../global/sections/licenses-table";
@import "../global/sections/jackpot-features";
@import "../global/sections/engagement-swiper";
@import "../global/sections/engagement-tools-swiper";
@import "../global/sections/news-list";
@import "../global/sections/news-article";
@import "../global/sections/live-casino-slider";
@import "../global/sections/swiper-top-games";
@import "../global/sections/offices-slider";
@import "../global/scroll-to-top";
@import "../global/cookie-policy";
@import "welcome-dealers";

@import "../global/triple-slider";
@import "overrides";

.base {
  min-height: 100%;
  position: relative;
  overflow-x: hidden;
  max-width: 1920px;
  margin-left: auto;
  margin-right: auto;
}

.container {
  max-width: 1920px;
  margin-left: auto;
  margin-right: auto;
}

//.device-mobile {
.main-container.container-fluid {
  padding-left: 0;
  padding-right: 0;
}

//}

img {
  display: block;
  max-width: 100%;
  height: auto;
}

// misc
.banner-video {
  max-width: 1920px;
  width: 100%;
  margin: 0 auto 15px;

  @media (min-width: 1900px) {
    margin: 0 auto 20px;
  }

  img, video {
    width: 100%;
  }

  .device-mobile.in-landscape & {
    display: none !important;
  }

  .device-tablet.in-landscape & {
    display: block !important;
  }
}

// visibility
.is-mobile {
  @media (min-width: 768px) {
    display: none !important
  }
}

.is-tablet-up {
  @media (max-width: 767px) {
    display: none !important
  }
}

.nav-item.dropdown:hover .dropdown-menu {
  display: block;
}

.sports-header-home-portrait {
  display: none;
}

.device-mobile.in-portrait {
  .sports-header-home-portrait {
    display: block;
  }

  .sports-header-home {
    display: none;
  }
}

.sw-about-tbl {
  margin: 40px auto;
  display: flex;
  flex-wrap: wrap;
  max-width: 900px;
  text-align: center;

  &-item {
    width: 50%;
    padding: 10px;
    flex-shrink: 0;
    position: relative;
    //font-size: 19px;
    font-size: $minTextFontSize;

    &:before {
      content: '';
      position: absolute;
      top: -1px;
      left: 10px;
      right: 10px;
      height: 2px;
      background: #fff;
    }

    &:nth-child(1),
    &:nth-child(2) {
      &:before {
        display: none;
      }
    }

    @media (max-width: 480px) {
      &:nth-child(2n) {
        border-left: 1px solid #fff;
      }
    }
    @media (min-width: 481px) {
      width: 25%;
      border-right: 1px solid #fff;

      &:before {
        left: 15px;
        right: 15px;
      }

      &:nth-child(1),
      &:nth-child(5) {
        border-left: 1px solid #fff;
      }

      &:nth-child(3),
      &:nth-child(4) {
        &:before {
          display: none;
        }
      }
    }
    @media (min-width: 768px) {
      padding: 20px;
      //font-size: 1.4em;
      font-size: $midTextFontSize;
    }
    @media (min-width: 1200px) {
      font-size: $maxTextFontSize;
    }
  }

  &-val {
    font-weight: bold;
    color: #fff;
    font-size: $minTitleFontSize;

    @media (min-width: 768px) {
      font-size: $midTitleFontSize;
    }
    @media (min-width: 1200px) {
      font-size: $maxTitleFontSize;
    }
  }

  &-label {
    color: #b3b3b3;
  }
}

.sw-btn-white-font {
  border: none;
  color: $textColor;
  background-color: transparent;

  &:hover {
    color: #b3b3b3;
    //background-color: $secondaryColor;
  }
  &:focus {
    box-shadow: none;
  }
}
