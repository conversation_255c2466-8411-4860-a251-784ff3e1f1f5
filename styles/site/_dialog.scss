@import "../global/variables";

.device-mobile {

  &.in-landscape {
    & .modal-dialog {
      min-height: 98vh;
      margin: auto;
    }
  }

  & .dialog-modal-area .modal-dialog {
    max-width: 98vw;

    & .modal-body {
      padding: 4px;
    }
  }

}

.dialog-images {
  & .modal-dialog {
    & .modal-body {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.dialog-modal-area {

  .modal-dialog {
    max-width: 92vw;
    margin-right: auto;
    margin-left: auto;

    & .modal-body p {
      padding: 2px 10px;
    }

    .modal-content {
      background-color: $dialogBackgroundColor;
      box-shadow: 0 0 18.4px 1.6px rgba(0, 0, 0, .7);
      position: absolute;
      top: 14px;
      right: 14px;
      bottom: 14px;
      left: 14px;
      width: auto;
    }

    iframe {
      position: relative;
      width: 100%;
      height: 100%;
      border: none;
    }

    video {
      //max-width: 100%;
      //max-height: 98%;
      display: block;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center center;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
      //width: 98%;
      //height: 98%;
      //bottom: 10px;
      //left: 10px;
      //right: 10px;
      //top: 10px;
    }

    .modal-header {
      background-color: #000;
      align-items: center;
      padding: 6px 8px 6px 20px;
      border: none;

      .modal-title {
        color: $secondaryColor;
        font-size: 16px;
        padding-right: 10px;

        @media (min-width: 768px) {
          font-size: 20px;
        }
      }

    }

    .modal-body {
      flex: 1;
      padding: 0;
      overflow: hidden;
    }

    .modal-footer {
      border: none;

      &:empty {
        padding: 0;
      }
    }
  }
}
