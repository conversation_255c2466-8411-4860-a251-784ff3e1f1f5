@import "../global/variables";

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
  overflow: hidden;
  outline: 0;
  z-index: 1060;
  background-color: rgba(#000, .5);

  &.hidden {
    display: none;
  }
}

.modal-open {
  overflow: hidden;

  .modal {
    overflow-x: hidden;
    overflow-y: auto;
  }
}

// modal container
.modal-container {
  min-height: 100%;
  max-width: 100%;
  margin: 0;
  display: flex;
  align-items: center;

  @media (min-width: 768px) {
    min-height: calc(100% - (#{$modal-spacer} * 2));
    max-width: calc(100% - (#{$modal-spacer} * 2));
    margin: $modal-spacer auto;

    .modal-full & {
      min-height: 100%;
      max-width: 100%;
      margin: 0;
    }
  }
}

// modal content container
.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: $mainDark;
  background-clip: padding-box;
  outline: 0;
  box-shadow: 0 0 1px 0 rgba(#fff, .4);
  border-radius: 4px;

  .modal-full & {
    height: 100vh;
  }
}

// modal header
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
//  padding: 0 0 0 16px;
  height: $header-height;
  background: #000;
  border-radius: 4px 4px 0 0;

  &-title {
    color: $secondaryColor;
    font-size: 20px;
    line-height: normal;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    @media screen and (max-width: 400px) {
      font-size: 16px
    }
  }

  &-close {
    right: 0;
    top: 0;
    cursor: pointer;
    width: $header-height;
    height: $header-height;
    transition: all .2s ease;
    position: relative;

    @media (max-width: 1365px) {
      transform: scale(.9)
    }

    &-icon {
      width: 37px;
      height: 24px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);

      @media (max-width: 1365px) {
        transform: scale(0.9) translate(-50%, -50%);
      }

      span {
        position: absolute;
        top: 0;
        left: 7px;
        display: block;
        height: 2px;
        width: 31px;
        background-color: $secondaryColor;
        transform-origin: 0 center;

        &:first-child {
          transform: rotate(45deg);
        }

        &:last-child {
          margin-top: 22px;
          transform: rotate(-45deg);
        }
      }
    }
  }
}

// modal body
.modal-body {
  padding: 16px;
  max-height: calc(100vh - #{$header-height});
  overflow: auto;

  @media (min-width: 768px) {
    max-height: calc(100vh - (#{$modal-spacer} * 2) - #{$header-height});
  }

  video, iframe {
    width: 100%;
    height: 100%;
    border: none;
    //min-height: calc(100vh - #{$header-height});

    //@media (min-width: 768px) {
    //  min-height: calc(100vh - (#{$modal-spacer} * 2) - #{$header-height});
    //}
    //@media (max-width: 767px) {
    //  .device-landscape & {
    //    max-height: calc(100vh - #{$header-height});
    //  }
    //}
  }
}

// modal modifications
.modal-play,
.modal-promo {
  .modal-content {
    min-height: 100vh;

    @media (min-width: 768px) {
      min-height: calc(100vh - (#{$modal-spacer} * 2));
    }
  }

  .modal-body {
    padding: 0;
    line-height: 0;
    border-radius: 0 0 4px 4px;
    overflow: hidden;
  }
}

.modal-explorer {
  &-all {
    padding: 10px;
    background: rgba(#141414, .5);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: none;
    overflow: hidden;
    outline: 0;
    z-index: 100;

    .modal-container {
      box-shadow: 0 0 1px 0 rgba(#fff, .4);
      border-radius: 4px;

      @media (max-width: 767px) {
        max-height: calc(100vh - 10px * 2);
      }
    }

    .swiper-wrapper.flex-wrap .swiper-slide {
      margin-bottom: 4%;
    }

    .modal-body {
      padding: 0;

      @media (max-width: 767px) {
        max-height: calc(100vh - #{$header-height} - 10px * 2);
      }
    }
  }

  &-open {
    overflow: hidden;

    .swiper-modal,
    .swiper-modal-mini {
      z-index: 101 !important
    }
  }
}

.modal-features {
  .modal-container {
    max-width: 380px;
    margin: auto;
  }

  &-content {
    display: flex;
    flex-direction: column;
    justify-content: stretch;
  }

  &-title {
    font-family: $fontRoboto;
    font-size: 20px;
    color: $textColor;
    font-weight: bold;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    margin-bottom: 16px;
    text-align: center;

    @media (min-width: 1440px) {
      font-size: 32px;
    }
  }

  &-figure {
    display: block;
    margin: 0 0 16px;
    padding-bottom: 62.54%;
    position: relative;

    &-img {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      top: 0;
      height: 100%;
      width: 100%;
      object-fit: cover;
      object-position: center;
      border-radius: 4px;
    }
  }

  &-text {
    font-family: $fontRoboto;
    font-size: 14px;

    @media (min-width: 1440px) {
      font-size: 18px;
    }
  }
}
