@import "../global/variables";

// include necessary SCSS of Bootstrap 4
// Required
@import '../../node_modules/bootstrap/scss/functions';
@import '../../node_modules/bootstrap/scss/variables';
@import '../../node_modules/bootstrap/scss/mixins';

// Optional
// @import "../../../node_modules/bootstrap/scss/root";
// @import "../../../node_modules/bootstrap/scss/reboot";
// @import "../../../node_modules/bootstrap/scss/type";
// @import "../../../node_modules/bootstrap/scss/images";
// @import "../../../node_modules/bootstrap/scss/code";
@import '../../node_modules/bootstrap/scss/grid';
// @import "../../../node_modules/bootstrap/scss/tables";
// @import "../../../node_modules/bootstrap/scss/forms";
// @import "../../../node_modules/bootstrap/scss/buttons";
// @import "../../../node_modules/bootstrap/scss/transitions";
@import '../../node_modules/bootstrap/scss/dropdown';
// @import "../../../node_modules/bootstrap/scss/button-group";
// @import "../../../node_modules/bootstrap/scss/input-group";
// @import "../../../node_modules/bootstrap/scss/custom-forms";
// @import "../../../node_modules/bootstrap/scss/nav";
// @import "../../../node_modules/bootstrap/scss/navbar";
// @import "../../../node_modules/bootstrap/scss/card";
// @import "../../../node_modules/bootstrap/scss/breadcrumb";
// @import "../../../node_modules/bootstrap/scss/pagination";
// @import "../../../node_modules/bootstrap/scss/badge";
// @import "../../../node_modules/bootstrap/scss/jumbotron";
// @import "../../../node_modules/bootstrap/scss/alert";
// @import "../../../node_modules/bootstrap/scss/progress";
// @import "../../../node_modules/bootstrap/scss/media";
// @import "../../../node_modules/bootstrap/scss/list-group";
// @import "../../../node_modules/bootstrap/scss/close";
// @import "../../../node_modules/bootstrap/scss/toasts";
// @import "../../../node_modules/bootstrap/scss/modal";
// @import "../../../node_modules/bootstrap/scss/tooltip";
// @import "../../../node_modules/bootstrap/scss/popover";
// @import "../../../node_modules/bootstrap/scss/carousel";
// @import "../../../node_modules/bootstrap/scss/spinners";
// @import '../../node_modules/bootstrap/scss/utilities';
// @import '../../../node_modules/bootstrap/scss/print';

// variables
$tr2: all 0.2s ease;

.dropdown {
  & [data-btn-dropdown] {
    min-width: 250px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #fff;
    transition: none;
    padding: 17px 29px;
    background: #181717;

    &:hover {
      background: #181717;
    }

    @media (max-width: 480px), (max-height: 450px) and (orientation: landscape) {
      min-width: 200px;
    }
  }

  & [data-list-dropdown] {
    transform: translate3d(0, 54px, 0) !important;
    top: -3px !important;
    overflow: hidden;
    padding: 0.9rem 0;
    font-size: 1em;
    background: #181717;
    border: 1px solid $secondaryColor;
    border-radius: initial;
    border-bottom-right-radius: 2em;
    border-bottom-left-radius: 2em;
    width: 100%;
    box-shadow: 0 2px 10px 0 #181717;

    button {
      width: inherit;
      cursor: pointer;
      outline: none;
      border: none;
      color: #fff;
      background: inherit;
      padding: 7px 20px 7px 30px;
      display: flex;
      align-items: center;
      font-size: inherit;
      line-height: 1.3;
      text-transform: uppercase;
      letter-spacing: 0.12em;
      font-weight: 600;

      &:nth-child(odd) {
        background: #1e1d1d;
      }

      &:hover, &.is-active {
        color: $secondaryColor;
      }
    }
  }

  &.show {
    .dropdown-with-icon {
      border-radius: initial;
      border-top-left-radius: 2em;
      border-top-right-radius: 2em;

      .icon {
        transform: scaleY(-1);
      }
    }
  }
}

.wrap {
  max-width: 1440px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
}

.footer {
  border-top: 2px solid rgba(#fff, 0.1);
  text-transform: uppercase;
  padding-top: 80px;
  padding-bottom: 80px;
  color: #aea8a8;
  transition: all 1s ease-in-out;
  background-color: $mainDark;

  &.window-fixed {
    position: fixed;
    width: 100%;
    left: 0;
    right: 0;
    bottom: 0;
    height: 60px;
    padding: 0;
    z-index: 1001;

    &.open {
      padding-top: 80px;
      padding-bottom: 80px;
    }
  }

  & .wrap.open {
    overflow: auto;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0
  }

  .show_all {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    display: flex;
    justify-content: space-between;
    background-color: $mainDark;
    z-index: 1040;

    .f_title {
      color: #fff;
      font-size: 22px;
      margin-bottom: 4.63vh;
      display: flex;
      line-height: 60px;
      vertical-align: middle;
      padding-left: 10px;
      opacity: 0.5;
    }

    .f_show_btn {
      width: 60px;
      height: 50px;
      background-color: $secondaryColor;
      margin-right: 10px;
      z-index: 1010;
      opacity: 0.5;
    }
  }

  a {
    color: inherit;
    outline: 0;
    text-decoration: none;
  }

  &__title {
    color: #fff;
    font-size: 22px;
    margin-bottom: 4.63vh;
    display: inline-block;

    //comment from Vasilij (2/12/19)
    span {
      color: #fff;
    }

    @media (min-width: 1921px), (min-height: 1081px) {
      margin-bottom: 50px;
    }
  }

  &__menu {
    @media (min-width: 992px) {
      display: none;
    }
  }

  &__contact, &__connected {
    @media (max-width: 992px) {
      margin-bottom: 4.63vh;
    }

    @media (max-width: 812px) and (orientation: landscape) {
      width: 400px;
    }

    @media (max-width: 480px) {
      width: auto;
    }

    div {
      display: flex;
      justify-content: space-between;
    }

    ul {
      padding: 0;
      margin: 0;
      list-style: none;

      li {
        // white-space: nowrap;
        color: #aea8a8;
        font-size: 14px;
        margin-bottom: 2.31vh;
        text-transform: none;
        transition: $tr2;

        @media (min-width: 1921px), (min-height: 1081px) {
          margin-bottom: 25px;
        }

        &:last-child {
          margin-bottom: 0;
        }

        a {
          color: #fff;
          transition: $tr2;

          &:hover {
            color: rgba($secondaryColor, 1);
          }
        }
      }
    }

    a {
      word-break: break-word;
    }
  }

  &__connected {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    ul {
      li {
        padding-left: 20px;
        margin-bottom: 10px;
      }
    }

    @media (max-width: 992px) {
      align-items: flex-start;
      margin-top: 4.63vh;
      height: auto;
    }

    .lang {
      margin: 0.5em 0;
    }
  }

  &__social {
    opacity: 0.6;
    margin-bottom: 60px;
  }

  &__copyright {
    margin-top: auto;
    white-space: nowrap;

    a {
      display: block;
      text-align: right;

      &:nth-child(2) {
        margin-bottom: 30px;
      }

      @media (max-width: 992px) {
        text-align: left;
      }
    }

    @media (max-width: 992px) {
      margin-top: 60px;
    }
  }

  svg {
    vertical-align: middle;
  }

  .container-fluid {
    @extend .container;
  }
}

.flicenses {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  width: 100%;
  margin: 0 auto;

  img {
    @media (max-width: 480px), (max-width: 812px) and (orientation: landscape) {
      max-width: 100%;
      max-height: 50px;
    }
  }

  &__item {
    margin-right: 2em;
    margin-bottom: 2em;
    display: flex;
    align-items: center;
    opacity: 0.6;
    max-height: 60px;
    max-width: 260px;

    &:hover {
      filter: sepia(1);
    }

    @media (max-width: 992px) {
      margin: 1em;
    }

    @media (max-width: 600px) {
      width: 40%;
      flex-shrink: 0;
    }
  }
}

.social {
  display: flex;
  justify-content: flex-end;
  position: relative;
  z-index: 2;
  padding: 0;
  margin: 0;

  &--big {
    .social__link {
      width: 4.44vh;
      height: 4.44vh;
      background-size: 2.5vh;

      @media (max-width: 1024px), (max-width: 1024px) and (orientation: landscape) {
        width: 43px;
        height: 43px;
        background-size: 20px;
      }

      @media (min-width: 1921px), (min-height: 1081px) {
        width: 48px;
        height: 48px;
        background-size: 27px;
      }
    }
  }

  &__item {
    list-style: none;
    margin-right: 20px;
    position: relative;

    &:last-child {
      margin-right: 0;
    }
  }

  &__link {
    display: inline-block;
    flex-shrink: 0;
    width: 30px;
    height: 30px;
    padding: 5px;
    border: 2px solid #fff;
    border-radius: 50%;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 16px;
    transition: $tr2;

    &:hover {
      opacity: 0.7;
    }
  }
}

img, svg {
  vertical-align: middle;
}

.title {
  font-family: $fontOswald;
  text-transform: uppercase;
  display: block;
  font-weight: 400;

  .accent {
    color: $secondaryColor;
  }

  &--h1 {
    font-size: 4.62vh;
    line-height: 7.4vh;
    letter-spacing: 0.14em;

    @media (min-width: 1921px), (min-height: 1081px) {
      font-size: 50px;
      line-height: 80px;
    }

    @media (max-width: 767px), (max-width: 900px) and (orientation: landscape) {
      font-size: 40px;
      line-height: 60px;
      text-align: center;
    }
  }

  &--h2 {
    font-size: 35px;
    line-height: 30px;
    letter-spacing: 0.32em;
    padding: 3.385vw 0 2.083vw 0;

    @media (min-width: 1921px), (min-height: 1081px) {
      padding: 65px 0 40px 0;
    }

    @media (max-width: 1024px) {
      letter-spacing: 0.1em;
    }

    @media (max-width: 1023px) {
      line-height: 1.2em;
    }
  }
}

.h2a {
  position: relative;

  &--invert {
    &::after {
      left: auto !important;
      right: 7.4vh;
    }
  }

  &::after {
    position: absolute;
    left: 7.4vh;
    top: -1.85vh;
    content: attr(data-text);
    font-size: 8.958vw;
    line-height: 8.958vw;
    text-transform: none;
    font-family: $fontOpenSans;
    font-weight: 800;
    letter-spacing: normal;
    color: rgba(#fff, 0.02);
    white-space: nowrap;
    pointer-events: none;

    @media (min-width: 1921px), (min-height: 1081px) {
      font-size: 172px;
      line-height: 172px;
      left: 80px;
      top: -20px;
    }

    @media (max-width: 1024px) {
      left: 3.125vw;
      top: -1vh;
    }

    @media (max-width: 1023px) {
      display: none;
    }
  }
}

.button {
  outline: none;
  cursor: pointer;
  border: none;
  display: inline-block;
  text-decoration: none;
  text-align: center;
  font-size: 14px;
  line-height: 16px;
  letter-spacing: 0.12em;
  font-weight: 700;
  text-transform: uppercase;
  white-space: nowrap;
  border-radius: 50px;
  transition: $tr2;
  padding: 19px 69px;
  position: relative;
  z-index: 4;

  @media (max-width: 1024px), (max-width: 1024px) and (orientation: landscape) {
    padding: 17px 63px;
  }

  &--primary {
    background-color: $secondaryColor;
    color: $mainDark;

    &:hover {
      //background-color: $hoverYellow;
    }
  }

  &--secondary {
    border: 2px solid rgba($secondaryColor, .7);
    color: $secondaryColor;
    padding: 17px 69px;
    background-color: rgba(0, 0, 0, 0.7);

    &:hover {
      background-color: rgba(0, 0, 0, 0.8);
    }
  }

  &--like {
    width: 59px;
    height: 52px;
    padding: 0;
    border-radius: 0;
    transition: $tr2;

    &:hover,
    &.like {
      svg {
        path {
          fill: $secondaryColor;
          fill-opacity: 1;
        }
      }
    }
  }
}

.page404,
.pageMaintenance {
  background-image: url("/assets/site/images/cur/404-maintence.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}

.blockcenter {
  display: flex;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  height: calc(100vh - #{$header-height});
  text-align: center;
  max-width: 1920px;
  margin: 0 auto;

  &__offer {
    font-size: 20px;
    line-height: 35px;
    width: 370px;
    margin-left: auto;
    margin-right: auto;
    margin-top: 8.33vh;
    margin-bottom: 8.33vh;
  }

  &__title {
    font-size: 35px;
    line-height: 1;
    letter-spacing: .32em;
    position: relative;
    margin-bottom: .5rem;
  }

  &__btn {
    width: 238px;
    margin: 0 auto;
  }
}

.header-desc {
  position: relative;
  margin-top: 12%;
  z-index: 2;
  height: 50vh;

  @media (min-width: 1921px), (min-height: 1081px) {
    max-height: 600px;
  }

  @media (max-width: 768px) {
    margin-top: 25%;
  }

  @media (max-width: 400px) {
    margin-top: 20%;
  }

  @media (max-width: 1024px) and (orientation: landscape) {
    height: 40vh;
  }

  @media (max-width: 1023px) and (orientation: landscape) {
    margin-top: 5%;
    height: 20vh;
  }

  //fix
  @media (max-width: 767px), (max-width: 900px) and (orientation: landscape) {
    display: none;
  }

  &__title {
    letter-spacing: 0.32em;
  }

  &__logo {
    max-width: 400px;
    pointer-events: none;
  }

  &__subtitle {
    font-size: 2.68vh;
    line-height: 1.8;
    margin-top: 4.44vh;
    padding-right: 4.688vw;
    font-weight: 700;

    @media (min-width: 1921px), (min-height: 1081px) {
      font-size: 29px;
      line-height: 39px;
      margin-top: 48px;
      padding-right: 90px;
    }

    @media (max-width: 1024px) {
      font-size: 20px;
      padding-right: 0;
    }

    @media (max-width: 767px), (max-width: 900px) and (orientation: landscape) {
      font-size: 15px;
      line-height: 36px;
      text-align: center;
    }

    &.grc-header-subtitle {
      font-size: 20px;
      line-height: 36px;
      letter-spacing: 0.075em;
      font-weight: 400;
    }
  }

  &__gs {
    display: flex;
    flex-direction: column;
    margin: auto 0 auto auto;
    max-width: 90%;

    @media (max-width: 1440px) {
      max-width: 100%;
    }

    @media (max-width: 991px) {
      max-width: 80%;
      margin: auto;
    }

    @media (max-width: 767px) {
      max-width: 65%;
    }

    @media (max-width: 480px) {
      max-width: 60%;
      padding-left: 20px;
    }

    @media (max-width: 420px) {
      max-width: 75%;
    }
  }

  &__gsPoints {
    margin-top: 30px;
    font-size: 30px;
    line-height: 36px;
    color: $secondaryColor;
    text-transform: uppercase;

    @media (max-width: 991px) {
      display: none;
    }

    span {
      display: block;
      margin-bottom: 45px;
      letter-spacing: 0.075em;

      @media (max-width: 1024px) {
        margin-bottom: 30px;
        letter-spacing: 0.05em;
      }

      &::after {
        margin-bottom: 0;
      }
    }
  }

  &__gsCross {
    position: absolute;
    top: -80px;
    left: 0;
    width: 12px;
    height: 118px;
    background: $secondaryColor;

    @media (max-width: 1440px) {
      left: -60px;
    }

    @media (max-width: 991px) {
      left: 60px;
    }

    @media (max-width: 1024px) {
      transform: scale(0.75);
    }

    @media (max-width: 991px) {
      transform: scale(0.61);
    }

    @media (max-width: 420px) {
      transform: scale(0.35) translate(-100px, 50px);
    }

    &::after {
      position: absolute;
      content: "";
      height: 12px;
      width: 118px;
      left: -53px;
      top: 53px;
      background: $secondaryColor;
    }
  }

  &__gsTitle {
    font-family: $fontOswald;
    font-size: 122px;
    line-height: 137px;
    font-weight: 400;

    @media (max-width: 1366px) {
      font-size: 110px;
      line-height: 120px;
    }

    @media (max-width: 1200px) {
      font-size: 90px;
      line-height: 100px;
    }

    @media (max-width: 767px), (max-width: 854px) and (orientation: landscape) {
      font-size: 60px;
      line-height: 1.2em;
    }

    strong {
      display: inline-block;
      letter-spacing: 0.23em;
    }
  }

  &__gsSubtitle {
    margin-top: 60px;
    font-size: 20px;
    line-height: 36px;
    letter-spacing: 0.075em;

    @media (max-width: 1024px) {
      letter-spacing: 0.05em;
    }

    @media (max-width: 768px) {
      margin-top: 40px;
      font-size: 16px;
      line-height: 1.4em;
      max-width: 70%;
    }

    @media (max-width: 767px) {
      margin-top: 30px;
    }

    @media (max-width: 480px) {
      max-width: 100%;
      padding-right: 10px;
    }
  }
}

.header-tools {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  max-width: 100%;
  width: 100%;
  vertical-align: middle;
  height: 80vh;
  max-height: 1200px;
  min-height: 900px;
  overflow: hidden;
  position: relative;

  & img {
    display: block;
    max-width: 100%;
    height: auto;
  }

  & .wrap {
    max-width: 1440px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
  }

  @media (max-width: 1024px) {
    min-height: 500px;
    max-height: 900px;
  }

  @media (max-width: 700px) {
    height: 80vh;
    max-height: 700px;
  }

  @media (max-width: 420px) {
    max-height: 570px;
  }

  //var 4 (effect for text)
  .header-desc__subtitle,
  .header-desc__title {
    text-shadow: 2px 2px 0 #181717, 8px 8px 15px #181717;
  }

  &::before {
    position: absolute;
    z-index: 2;
    left: 0;
    top: 0;
    content: "";
    width: 100%;
    height: 294px;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 100%);

    @media (max-width: 1024px) {
      height: 170px;
    }

    @media (max-width: 480px), (max-width: 812px) and (orientation: landscape) {
      height: 100px;
    }
  }

  &::after {
    position: absolute;
    z-index: 1;
    left: 0;
    bottom: 0;
    content: "";
    width: 100%;
    height: 600px;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(24, 23, 23, 1) 90%);

    @media only screen and (max-width: 1439px) and (max-height: 900px) {
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(24, 23, 23, 1) 75%);
    }

    @media (max-width: 480px), (max-width: 812px) and (orientation: landscape) {
      height: 250px;
    }
  }

  .header--tools {
    background-image: none;
    min-height: 80vh;

    @media (min-width: 1921px), (min-height: 1081px) {
      min-height: inherit;
      z-index: 3;
      max-height: 1100px;
    }

    @media (min-width: 900px) and (max-width: 1024px) and (orientation: landscape) {
      min-height: 70vh;
      height: 70vh;
    }

    @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
      min-height: 50vh;
      height: 50vh;
    }

    @media (max-width: 767px) {
      .header-desc {
        display: block;
      }
    }

    @media (max-width: 1023px) {
      .header-desc__subtitle,
      .header-desc__logo {
        display: none;
      }
    }

    @media (max-width: 900px) and (orientation: landscape) {
      .header-desc {
        display: none;
      }
    }

    &::after {
      height: 100px;

      // @media (min-width: 1921px), (min-height: 1081px) {
      //   z-index: -1;
      // }
    }

    .title--h1 {
      letter-spacing: 0.32em;

      @media (max-width: 900px) and (orientation: landscape) {
        text-align: left;
      }

      @media (max-width: 480px) {
        font-size: 35px;
        line-height: 1em;
        letter-spacing: 0.15em;
      }
    }

    .header-desc__subtitle {
      @media (max-width: 900px) and (orientation: landscape) {
        text-align: left;
      }

      @media (max-width: 480px) {
        font-size: 16px;
        line-height: 36px;
        letter-spacing: 0.075em;
        font-weight: 400;
      }
    }

    .gameposter img {
      height: auto;
    }
  }

  & .tools-header-text {
    pointer-events: none;
    width: 100%;
    height: 100%;
    max-height: 1080px;
    max-width: 1920px;
    text-transform: uppercase;
    font-weight: 700;
    font-size: 20px;
    line-height: 1;
    color: rgba($secondaryColor, 1);
    position: absolute;
    left: 48%;
    top: 0;
    transform: translateX(-50%);
    z-index: 2;

    @media (max-width: 1366px) {
      font-size: 18px;
    }

    @media (max-width: 1024px) {
      left: 0;
      transform: none;
    }

    @media (max-width: 1023px) {
      font-size: 16px;
      left: -5%;
    }

    @media (max-width: 480px) {
      top: -17%;
    }

    @media (max-width: 400px) and (max-device-height: 667px) {
      top: -14%;
    }

    @media (min-width: 3400px) {
      left: 49%;
    }

    span {
      display: block;
      position: absolute;
      z-index: 2;

      @media (max-width: 1024px) {
        animation-delay: 0s !important;
      }

      &:nth-child(1) {
        width: 170px;
        left: 48%;
        top: 20%;
        opacity: 0;
        animation: fadeIn 1s linear 0.5s both;

        @media (max-width: 1024px) {
          top: 25%;
          left: 48%;
        }

        @media (max-width: 1024px) and (orientation: portrait) {
          top: 13%;
          left: 47%;
        }

        @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
          left: 48%;
          top: 20%;
        }

        @media (max-width: 1023px) {
          left: 25%;
          top: 45%;
        }

        @media (max-width: 480px) {
          top: 55%;
          left: 25%;
        }

        @media (max-width: 900px) and (orientation: landscape) {
          display: none;
        }
      }

      &:nth-child(2) {
        text-align: right;
        width: 130px;
        left: 43%;
        top: 45%;
        opacity: 0;
        animation: fadeIn 1s linear 1s both;

        @media (max-width: 1440px) {
          left: 40%;
        }

        @media (max-width: 1024px) {
          top: 57%;
        }

        @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
          left: 46%;
          top: 50%;
        }

        @media (max-width: 1023px) {
          text-align: left;
          width: 200px;
          left: 15%;
          top: 65%;
        }

        @media (max-width: 900px) and (orientation: landscape) {
          display: none;
        }

        @media (max-width: 480px) {
          top: 68%;
          left: 10%;
        }
      }

      &:nth-child(3) {
        width: 140px;
        left: 85%;
        top: 25%;
        opacity: 0;
        animation: fadeIn 1s linear 1.5s both;

        @media (max-width: 1440px) {
          left: 82%;
        }

        @media (max-width: 1024px) {
          left: 85%;
          top: 35%;
        }

        @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
          left: 85%;
          top: 25%;
        }

        @media (max-width: 1023px) {
          left: 25%;
          top: 85%;
        }

        @media (max-width: 480px) {
          top: 82%;
        }

        @media (max-width: 900px) and (orientation: landscape) {
          display: none;
        }
      }

      &:nth-child(4) {
        width: 130px;
        left: 82%;
        top: 51%;
        opacity: 0;
        animation: fadeIn 1s linear 2s both;

        @media (max-width: 1440px) {
          top: 54%;
        }

        @media (max-width: 1024px) {
          left: 87%;
          top: 67%;
        }

        @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
          left: 86%;
          top: 55%;
        }

        @media (max-width: 1023px) {
          visibility: hidden;
        }
      }
    }

    &__jp {
      span {
        &:nth-child(1) {
          left: 53%;
          top: 30%;

          @media (min-width: 1921px), (min-height: 1081px) {
            left: 55%;
          }

          @media (max-width: 1600px) {
            top: 25%;
          }

          @media (max-width: 1024px) {
            top: 22%;
            left: 45%;
          }

          @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
            top: 15%;
            left: 45%;
          }

          @media (max-width: 1023px) {
            visibility: hidden;
          }

          @media (max-width: 768px) {
            visibility: visible;
            top: 52%;
            left: 39%;
            width: 190px;
          }

          @media (max-width: 480px) {
            top: 69%;
          }

          @media (max-width: 900px) and (orientation: landscape) {
            display: none;
          }
        }

        &:nth-child(2) {
          width: 200px;
          left: 76%;
          top: 25%;
          text-align: left;

          @media (min-width: 1921px), (min-height: 1081px) {
            left: 77%;
          }

          @media (max-width: 1600px) {
            top: 20%;
          }

          @media (max-width: 1024px) {
            top: 20%;
            left: 75%;
          }

          @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
            top: 20%;
            left: 75%;
          }

          @media (max-width: 1023px) {
            visibility: hidden;
          }

          @media (max-width: 768px) {
            visibility: visible;
            top: 70%;
            left: 55%;
            width: 160px;
          }

          @media (max-width: 480px) {
            top: 82%;
          }

          @media (max-width: 900px) and (orientation: landscape) {
            display: none;
          }
        }

        &:nth-child(3) {
          left: 84%;
          top: 38%;

          @media (min-width: 1921px), (min-height: 1081px) {
            left: 86%;
          }

          @media (max-width: 1600px) {
            top: 32%;
          }

          @media (max-width: 1024px) {
            left: 84%;
            top: 38%;
          }

          @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
            left: 84%;
            top: 38%;
          }

          @media (max-width: 1023px) {
            visibility: hidden;
          }

          @media (max-width: 768px) {
            visibility: visible;
            top: 87%;
            left: 75%;
          }

          @media (max-width: 480px) {
            top: 95%;
          }

          @media (max-width: 480px) {
            left: 78%;
          }

          @media (max-width: 900px) and (orientation: landscape) {
            display: none;
          }
        }
      }

      &--mobile {
        @media (min-width: 769px), (max-width: 1024px) and (orientation: landscape) {
          display: none !important;
        }

        display: block;
        position: absolute;
        top: 35%;
        left: 18%;

        @media (max-width: 480px) {
          top: 52%;
          left: 15%;
        }

        div {
          font-family: $fontOpenSans;
          font-weight: 400;
          text-align: center;
        }

        div:nth-child(1) {
          font-size: 44px;
          color: #f9d876;

          @media (max-width: 480px) {
            font-size: 9vw;
            white-space: nowrap;
          }
        }

        div:nth-child(2) {
          margin-top: 10px;
          font-size: 16px;
          color: #fff1c8;
        }
      }
    }

    &__tournam {
      @media (max-width: 1023px) {
        left: -10%;
      }
      @media (max-width: 480px) {
        top: -7%;
      }

      span {
        &:nth-child(1) {
          width: 120px;
          left: 60%;
          top: 40%;

          @media (min-width: 1921px), (min-height: 1081px) {
            left: 62%;
          }

          @media (max-width: 1600px) {
            top: 33%;
          }

          @media (max-width: 1024px) {
            left: 58%;
            top: 63%;
          }

          @media (max-width: 1023px) {
            visibility: hidden;
          }

          @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
            left: 50%;
            top: 76%;
          }

          @media (max-width: 768px) {
            visibility: visible;
            left: 35%;
            top: 50%;
          }

          @media (max-width: 480px) {
            left: 33%;
            top: 58%;
          }

          @media (max-width: 420px) {
            left: 31%;
          }

          @media (max-width: 900px) and (orientation: landscape) {
            display: none;
          }
        }

        &:nth-child(2) {
          text-align: left;
          width: 190px;
          left: 67%;
          top: 20%;

          @media (min-width: 1921px), (min-height: 1081px) {
            left: 69%;
          }

          @media (max-width: 1600px) {
            top: 16%;
          }

          @media (max-width: 1024px) {
            left: 65%;
            top: 45%;
          }

          @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
            left: 58%;
            top: 46%;
          }

          @media (max-width: 1023px) {
            display: none;
          }

          @media (max-width: 900px) and (orientation: landscape) {
            display: none;
          }
        }

        &:nth-child(3) {
          width: 210px;
          left: 70.5%;
          top: 34%;

          @media (min-width: 1921px), (min-height: 1081px) {
            left: 72%;
          }

          @media (max-width: 1600px) {
            top: 28%;
          }

          @media (max-width: 1024px) {
            left: 70%;
            top: 55%;
          }

          @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
            left: 70%;
            top: 63%;
          }

          @media (max-width: 1023px) {
            display: none;
          }

          @media (max-width: 900px) and (orientation: landscape) {
            display: none;
          }
        }

        &:nth-child(4) {
          left: 83%;
          top: 17%;

          @media (min-width: 1921px), (min-height: 1081px) {
            left: 85%;
          }

          @media (max-width: 1600px) {
            top: 14%;
          }

          @media (max-width: 1024px) {
            left: 82%;
            top: 35%;
          }

          @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
            left: 82%;
            top: 42%;
          }

          @media (max-width: 768px) {
            visibility: visible;
            left: 70%;
            top: 70%;
          }

          @media (max-width: 480px) {
            left: 71%;
            top: 71%;
          }

          @media (max-width: 900px) and (orientation: landscape) {
            visibility: hidden;
          }
        }
      }
    }

    &__ms {
      @media (min-width: 1024px) and (max-width: 1600px) {
        left: 46%;
      }
      @media (min-width: 769px) and (max-width: 1200px) {
        visibility: hidden;
      }
      @media (max-width: 420px) {
        left: -13%;
      }

      span {
        &:nth-child(1) {
          top: 20%;
          left: 44%;
          width: 100px;

          @media (max-width: 768px) {
            top: 40%;
            left: 35%;
          }

          @media (max-width: 480px) {
            top: 48%;
            left: 28%;
            text-align: center;
          }
        }

        &:nth-child(2) {
          top: 43%;
          left: 57%;
          width: 80px;

          @media (max-width: 768px) {
            top: 70%;
            left: 75%;
          }

          @media (max-width: 480px) {
            top: 72%;
            left: 70%;
            text-align: center;
          }
        }

        &:nth-child(3) {
          white-space: nowrap;
          top: 58%;
          left: 43%;
          width: 140px;

          @media (max-width: 768px) {
            top: 88%;
            left: 33%;
          }

          @media (max-width: 480px) {
            top: 85%;
            left: 25%;
            text-align: center;
          }
        }
      }
    }

    &__grc {
      @media (max-width: 1024px) {
        margin-left: -20px;
      }

      @media (max-width: 480px) {
        top: 0;
      }

      @media (max-width: 400px) {
        top: 4%;
      }

      span {
        &:nth-child(1) {
          width: 240px;
          left: 53%;
          top: 24%;

          @media (max-width: 1023px) {
            visibility: hidden;
          }

          @media (max-width: 768px) {
            visibility: visible;
          }

          @media (max-width: 480px) {
            width: 190px;
            left: 45%;
          }

          @media (max-width: 420px) {
            left: 42%;
          }

          @media (max-width: 900px) and (orientation: landscape) {
            display: none;
          }
        }

        &:nth-child(2) {
          text-align: left;
          width: 180px;
          left: 67%;
          top: 36%;

          @media (max-width: 1023px) {
            visibility: hidden;
          }

          @media (max-width: 768px) {
            visibility: visible;
            top: 45%;
            left: 40%;
          }

          @media (max-width: 480px) {
            left: 35%;
            top: 42%;
            width: 160px;
          }

          @media (max-width: 420px) {
            left: 32%;
            width: 150px;
          }

          @media (max-width: 900px) and (orientation: landscape) {
            display: none;
          }
        }

        &:nth-child(3) {
          width: 220px;
          left: 80%;
          top: 24%;

          @media (max-width: 1023px) {
            visibility: hidden;
          }

          @media (max-width: 768px) {
            visibility: visible;
            top: 65%;
            left: 25%;
            width: 180px;
          }

          @media (max-width: 480px) {
            top: 57%;
            left: 22%;
          }

          @media (max-width: 420px) {
            left: 18%;
          }

          @media (max-width: 900px) and (orientation: landscape) {
            display: none;
          }
        }
      }
    }
  }

}

.header--tools {
  background-image: none;
  min-height: 100vh;

  @media (min-width: 1921px), (min-height: 1081px) {
    min-height: inherit;
    z-index: 3;
    max-height: 1100px;
  }

  @media (min-width: 900px) and (max-width: 1024px) and (orientation: landscape) {
    min-height: 70vh;
    height: 70vh;
  }

  @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
    min-height: 50vh;
    height: 50vh;
  }

  @media (max-width: 767px) {
    .header-desc {
      display: block;
    }
  }

  @media (max-width: 1023px) {
    .header-desc__subtitle,
    .header-desc__logo {
      display: none;
    }
  }

  @media (max-width: 900px) and (orientation: landscape) {
    .header-desc {
      display: none;
    }
  }

  &::after {
    height: 100px;

    // @media (min-width: 1921px), (min-height: 1081px) {
    //   z-index: -1;
    // }
  }

  .title--h1 {
    letter-spacing: 0.32em;

    @media (max-width: 900px) and (orientation: landscape) {
      text-align: left;
    }

    @media (max-width: 480px) {
      font-size: 35px;
      line-height: 1em;
      letter-spacing: 0.15em;
    }
  }

  .header-desc__subtitle {
    @media (max-width: 900px) and (orientation: landscape) {
      text-align: left;
    }

    @media (max-width: 480px) {
      font-size: 16px;
      line-height: 36px;
      letter-spacing: 0.075em;
      font-weight: 400;
    }
  }

  .gameposter img {
    height: auto;
  }
}

.tools-bg {
  position: absolute;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
  width: 100%;
  height: 100%;
  max-width: 1920px;
  max-height: 1080px;
  // z-index: 1;

  &__bns,
  &__grc {
    position: absolute;
    z-index: 2;
    left: 0;
    top: 0;
  }

  &__tournam {
    @media (max-width: 1024px) and (orientation: landscape) {
      margin-top: 100px;
    }

    // @media (min-width: 1024px) and (max-width: 1366px) and (orientation: portrait) {
    //   margin-top: 250px;
    // }
  }

  &--desktop {
    @media (max-width: 1023px) {
      display: none;
    }
  }

  &--mobile {
    @media (min-width: 1024px) {
      display: none;
    }
    background-repeat: no-repeat;
    background-position-x: center;
    background-position-y: center;
    background-size: cover;
    width: 100%;
    height: 100%;

    &__bns {
      @media (max-width: 992px) {
        background-position-y: 10%;
      }

      @media (max-width: 480px) {
        background-position-x: left;
      }

      @media (max-width: 900px) and (orientation: landscape) {
        background-position-y: center;
      }
    }

    &__jp {
      @media (max-width: 768px) {
        background-position-y: -20%;
      }

      @media (max-width: 480px) {
        background-position-y: 150px;
      }

      @media (max-width: 900px) and (orientation: landscape) {
        background-position-y: center;
      }
    }

    &__tournam {
      @media (max-width: 992px) {
        background-position-y: 0;
      }

      @media (max-width: 480px) {
        background-position-y: 100px;
      }

      @media (max-width: 900px) and (orientation: landscape) {
        background-position-y: center;
      }
    }

    &__ms {
      @media (max-width: 768px) {
        background-position-y: -30px;
      }
      @media (max-width: 480px) {
        background-position-y: inherit;
      }
    }

    &__grc {
      @media (max-width: 768px) {
        background-position-y: -30px;
      }

      @media (max-width: 480px) {
        background-position-y: 50px;
      }

      @media (max-width: 900px) and (orientation: landscape) {
        background-position-y: center;
      }
    }
  }


}

.header-bottom {

  @media (max-width: 767px) {
    .button {
      display: none;
    }
  }

  @media (max-width: 812px) and (orientation: landscape) {
    margin-top: 23vh;
  }
}

.toolpage-offer {
  position: relative;
  margin-top: -5%;
  z-index: 2;

  @media (max-width: 1024px) {
    margin-top: 10%;
  }

  @media (min-width: 1921px), (min-height: 1081px) {
    margin-top: 0;
  }

  &--ms {
    @media (max-width: 767px) {
      margin-top: 0;
    }
  }

  &__big {
    color: rgba($secondaryColor, 0.04);
    font-weight: 400;
    font-size: 37.396vw;
    line-height: 1;
    text-align: center;
    text-transform: uppercase;
    position: relative;
    pointer-events: none;

    @media (min-width: 1921px) {
      font-size: 718px;
    }

    &--grc {
      white-space: pre;
    }

    &--jp {
      font-size: 18.438vw;

      @media (min-width: 1921px) {
        font-size: 354px;
      }
    }

    &--tournam {
      font-size: 11.771vw;
      margin-left: -7vw;

      @media (min-width: 1921px) {
        font-size: 226px;
      }
    }

    &--ms {
      text-align: left;
      font-size: 16.146vw;
      padding-bottom: 9.25vh;

      @media (min-width: 1921px) {
        font-size: 310px;
      }

      @media (min-height: 1081px) {
        padding-bottom: 100px;
      }

      @media (max-width: 1024px) {
        padding-bottom: 0;
      }
    }
  }

  &__same {
    width: 100%;
    position: absolute;
    top: 50%;
    left: 15px;
    transform: translateY(-50%);
    font-size: 2.59vh;
    line-height: 4.26vh;
    z-index: 2;

    @media (min-width: 1921px), (min-height: 1081px) {
      font-size: 28px;
      line-height: 46px;
    }

    @media (max-width: 992px), (max-width: 1024px) and (orientation: portrait) {
      position: relative;
      left: 0;
      top: 0;
      font-size: 16px;
      line-height: 36px;
    }
  }
}

// slider
.engagement-slider {
  position: relative;
  padding: 0 20px;

  .engagement-slide {
    padding: 20px 35px;

    @media (min-width: 600px) {
      width: calc(100% / 2);
      padding: 20px;
    }
    @media (min-width: 1024px) {
      width: calc(100% / 3);
      padding: 20px;
    }
  }

  &-title {
    font-size: 26px;
    font-family: $fontRoboto;
    color: $secondaryColor;
    font-weight: bold;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    margin-bottom: 16px;
    text-align: center;

    @media (min-width: 1440px) {
      font-size: 44px;
    }
  }

  &-text {
    font-size: 12px;
    font-family: $fontRoboto;

    @media (min-width: 1440px) {
      font-size: 18px;
    }
  }

  &-figure {
    display: block;
    margin: 0 0 46px;
    padding-bottom: 62.54%;
    position: relative;

    &-img {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      top: 0;
      height: 100%;
      width: 100%;
      object-fit: contain;
      object-position: center;
    }
  }

  &-nav {
    display: flex;
    justify-content: space-between;
  }

  &-nav-btn,
  .swiper-button-prev,
  .swiper-button-next {
    cursor: pointer;
    position: absolute;
    top: 50%;
    height: 24px;
    width: 24px;
    transform: translateY(-50%);
    z-index: 10;
    background: no-repeat 50% 50%;
    background-size: auto 100%;
    background-image: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI1LjQuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHZpZXdCb3g9IjAgMCA0My45NSA3My45NSIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNDMuOTUgNzMuOTU7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPHN0eWxlIHR5cGU9InRleHQvY3NzIj4KCS5zdDB7ZmlsbDojRkZGRkZGO30KPC9zdHlsZT4KPGc+Cgk8cGF0aCBjbGFzcz0ic3QwIiBkPSJNMzguODUsNDIuMDdjLTEuMjYsMC0yLjUyLTAuNDYtMy41LTEuMzlMMS42LDguODFDLTAuNDUsNi44Ny0wLjU0LDMuNjUsMS4zOSwxLjYKCQlDMy4zMy0wLjQ1LDYuNTUtMC41NCw4LjYsMS4zOWwzMy43NSwzMS44N2MyLjA1LDEuOTMsMi4xNCw1LjE2LDAuMjEsNy4yMUM0MS41Niw0MS41NCw0MC4yMSw0Mi4wNywzOC44NSw0Mi4wN3oiLz4KPC9nPgo8Zz4KCTxwYXRoIGNsYXNzPSJzdDAiIGQ9Ik01LjEsNzMuOTVjLTEuMzUsMC0yLjcxLTAuNTQtMy43MS0xLjZjLTEuOTMtMi4wNS0xLjg0LTUuMjgsMC4yMS03LjIxbDMzLjc1LTMxLjg3CgkJYzIuMDUtMS45Myw1LjI4LTEuODQsNy4yMSwwLjIxYzEuOTMsMi4wNSwxLjg0LDUuMjgtMC4yMSw3LjIxTDguNiw3Mi41NkM3LjYyLDczLjQ5LDYuMzYsNzMuOTUsNS4xLDczLjk1eiIvPgo8L2c+Cjwvc3ZnPgo=');

    &:after {
      display: none;
    }

    @media (min-width: 1440px) {
      height: 52px;
      width: 52px;
    }

    &.swiper-button-prev {
      left: 0;
      transform: translateY(-50%) scale(-1, -1);
    }

    &.swiper-button-next {
      right: 0;
    }
  }


  & .tools-slider-item {
    color: rgba(255, 255, 255, 1);
    font-size: 1.85vh;
    line-height: 3.33vh;

    //@media (min-width: 1921px), (min-height: 1081px) {
    //  font-size: 20px;
    //  line-height: 36px;
    //}
    //
    //@media (max-width: 1440px) {
    //  font-size: 16px;
    //  line-height: 30px;
    //}

    &__name {
      font-weight: 700;
      font-size: 2.68vh;
      line-height: 1;
      text-transform: uppercase;

      //@media (min-width: 1921px), (min-height: 1081px) {
      //  font-size: 83px;
      //}
      //
      //@media (max-width: 992px), (max-width: 1024px) and (orientation: portrait) {
      //  font-size: 38px;
      //  line-height: 50px;
      //}
      //
      //@media (max-width: 767px) {
      //  font-size: 30px;
      //  line-height: 27px;
      //}
    }

    &__subname {
      text-transform: uppercase;
      font-weight: 700;
      font-size: 2.59vh;
      line-height: 1;

      //@media (min-width: 1921px), (min-height: 1081px) {
      //  font-size: 28px;
      //}
      //
      //@media (max-width: 992px), (max-width: 1024px) and (orientation: portrait) {
      //  font-size: 15px;
      //}
    }

    &__text {
      margin-top: 2.59vh;
      margin-bottom: 12.96vh;
      position: relative;

      @media (min-width: 600px) {
        padding-right: 10%;
      }

      //@media (min-width: 1921px), (min-height: 1081px) {
      //  margin-top: 28px;
      //  margin-bottom: 140px;
      //}
      //
      //@media (max-width: 992px), (max-width: 1024px) and (orientation: portrait) {
      //  padding-right: 0;
      //  margin-top: 20px;
      //  margin-bottom: 60px;
      //}
      //
      //@media (max-width: 480px) {
      //  margin-bottom: 30px;
      //}

      &::after {
        position: absolute;
        content: "";
        bottom: -6.48vh;
        left: 0;
        width: 8.24vh;
        height: 2px;
        background-color: $secondaryColor;

        @media (min-width: 1921px), (min-height: 1081px) {
          bottom: -70px;
          width: 89px;
        }

        @media (max-width: 992px), (max-width: 1024px) and (orientation: portrait) {
          bottom: -30px;
        }

        @media (max-width: 480px) {
          bottom: -15px;
        }
      }
    }

    &__subtext {
      margin-top: 3.42vh;

      @media (min-width: 1921px), (min-height: 1081px) {
        margin-top: 37px;
      }
    }
  }

}

// jackpot features
.jackpot-features {
  &__inner {
    margin-top: 8.33vh;
    margin-bottom: 12.96vh;

    @media (min-width: 1921px), (min-height: 1081px) {
      margin-top: 90px;
      margin-bottom: 140px;
    }
  }
}

.jp-feature {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;

  .accent {
    color: $secondaryColor;
  }

  &__img {
    width: 200px;
    height: 200px;
    background-repeat: no-repeat;
    background-position: center;

    @media (max-width: 1024px) {
      background-size: contain;
      width: 150px;
      height: 150px;
    }
  }

  &__name {
    font-weight: 700;
    font-size: 32px;
    line-height: 1;
    margin-top: -1.04vh;
    margin-bottom: 6.02vh;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      left: 50%;
      bottom: -4vh;
      transform: translate(-50%, -50%);
      width: 37px;
      height: 4px;
      background-color: rgba($secondaryColor, 1);
    }

    @media (max-width: 1024px) {
      font-size: 24px;
    }

    @media (min-width: 1921px), (min-height: 1081px) {
      margin-top: -20px;
      margin-bottom: 65px;

      &::after {
        bottom: -32px;
      }
    }
  }

  &__text {
    width: 100%;
    font-size: 20px;
    line-height: 36px;
    padding: 0 1.5em;

    @media (max-width: 1024px) {
      padding: 0;
      font-size: 15px;
    }
  }
}

.news-articel {
  // overflow: hidden;
  position: relative;
  margin-top: 9.25vh;
  padding-bottom: 13.88vh;
  font-family: Arial,sans-serif;
  line-height: 1.2;
  max-width: 824px;
  margin-right: auto;
  margin-left: auto;

  @media (min-width: 1921px), (min-height: 1081px) {
    margin-top: 100px;
    padding-bottom: 350px;
  }

  &__offer {
    margin-bottom: 100px;

    @media (min-width: 1921px), (min-height: 1081px) {
      margin-bottom: 100px;
    }
  }

  &__date {
    display: inline-block;
    font-size: 24px;
    font-weight: 700;
    color: $secondaryColor;
    margin-bottom: 3vh;

    @media (min-width: 1921px), (min-height: 1081px) {
      margin-bottom: 35px;
    }

    @media (max-width: 768px) {
      font-size: 20px;
    }
  }

  &__title {
    font-family: $fontOswald;
    display: inline-block;
    font-size: 32px;
    font-weight: 400;
    margin-bottom: 5.09vh;
    text-align: center;
    position: relative;

    @media (min-width: 1921px), (min-height: 1081px) {
      margin-bottom: 55px;
    }

    &::after {
      position: absolute;
      z-index: 2;
      content: "";
      width: 350%;
      left: -250%;
      bottom: -15px;
      border-bottom: 2px solid rgba($secondaryColor, 0.6);

      @media (min-width: 1921px) {
        width: 500%;
        left: -400%;
      }

      @media (min-width: 5000px) {
        width: 1100%;
        left: -1000%;
      }

      @media (max-width: 480px) {
        width: 282%;
      }
    }

    &--live {
      &::after {
        width: 450%;

        @media (min-width: 1921px) {
          width: 1000%;
          left: -800%;
        }

        @media (min-width: 5000px) {
          width: 1500%;
          left: -1300%;
        }

        @media (max-width: 480px) {
          width: 282%;
        }
      }
    }
  }

  &__desc {
    font-size: 20px;
    line-height: 36px;
    padding-right: 3.7vh;

    @media (max-width: 1024px), (max-width: 1024px) and (orientation: landscape) {
      font-size: 16px;
      padding-right: 0;
    }

    @media (min-width: 1921px), (min-height: 1081px) {
      padding-right: 40px;
    }

    p {
      &:nth-child(2) {
        margin-top: 50px;
      }
    }
  }

  &__cert {
    margin-top: 9.26vh;

    @media (min-width: 1921px), (min-height: 1081px) {
      margin-top: 100px;
    }

    span {
      display: block;
      font-size: 20px;
      line-height: 36px;

      &.features__cert-links {
        display: inline-flex;
        flex-direction: column;
      }

      @media (max-width: 1024px), (max-width: 1024px) and (orientation: landscape) {
        font-size: 16px;
      }

      &:nth-child(1) {
        font-size: 22px;
        font-weight: 700;

        @media (max-width: 1024px), (max-width: 1024px) and (orientation: landscape) {
          font-size: 18px;
        }
      }
    }

    &-link {
      text-decoration: underline;

      &:hover {
        text-decoration: none;
      }
    }
  }

  &__btn {
    margin-top: 30px;
  }

  &__slider {
    position: relative;
    margin-top: 150px;
    padding-bottom: 3em;

    @media (max-width: 992px) {
      padding-bottom: 1em;
    }

    &::after {
      z-index: -2;
      position: absolute;
      content: "";
      top: -70px;
      left: -50%;
      height: 110%;
      width: 200%;
      background: linear-gradient(45deg, rgba(248, 154, 19, 1) 0%, rgba(250, 206, 43, 1) 100%);
      transform: rotate(-10deg);

      @media (max-width: 992px) {
        top: -100px;
        height: 120%;
      }

      @media (max-width: 480px), (orientation: landscape) {
        top: -70px;
      }
    }
  }
}

.news-articel {
  padding-bottom: 13.88vh;
  position: relative;

  @media (min-width: 1921px), (min-height: 1081px) {
    margin-top: 150px;
    padding-bottom: 150px;
  }

  &__offer {
    margin-bottom: 0;
  }

  &__link {
    cursor: pointer;
  }
}

.about-inner__desc a {
  text-decoration: underline;
  color: $secondaryColor;
}

.link {
  color: #fff;
  text-transform: uppercase;
  font-size: 2.03vh;
  font-weight: 700;
  letter-spacing: 0.02em;
  transition: $tr2;
  display: inline-block;
  position: relative;

  @media (max-width: 1024px), (orientation: landscape) {
    font-size: 18px;
  }

  @media (min-width: 1921px), (min-height: 1081px) {
    font-size: 22px;
  }

  span {
    margin-left: 10px;
    display: inline-block;
    position: relative;
    width: 15px;
    height: 12px;
    margin-bottom: 2px;
    transition: $tr2;

    &::before {
      position: absolute;
      content: "";
      background-color: #fff;
      top: 6px;
      margin-top: -1px;
      width: 15px;
      height: 2px;
    }

    &::after {
      position: absolute;
      top: 1px;
      right: -1px;
      content: "";
      width: 10px;
      height: 10px;
      border-top: 2px solid #fff;
      border-right: 2px solid #fff;
      transform: rotate(45deg);
    }
  }

  &:hover,
  &:focus {
    color: #fff;
    opacity: 0.8;
  }

  &:hover span {
    margin-left: 15px;
  }

  &__more {
    margin-top: 2.31vh;

    @media (min-width: 1921px) {
      margin-top: 25px;
    }
  }
}

.partners {
  margin-top: 80px;
  margin-bottom: 150px;

  &__desc {
    display: none;
    font-size: 1.85vh;
    line-height: 3.33vh;
    margin-bottom: 9.26vh;

    @media (max-width: 1365px) {
      font-size: 15px;
      line-height: 36px;
    }

    @media (max-width: 1023px) {
      text-align: center;
    }

    @media (max-width: 767px), (max-width: 900px) and (orientation: landscape) {
      display: none;
    }

    @media (min-width: 1921px), (min-height: 1081px) {
      font-size: 20px;
      line-height: 36px;
      margin-bottom: 100px;
    }
  }

  &__brand {
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;

    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      width: 0;
      background: transparent;
    }

    @media (max-width: 1000px) {
      overflow-x: scroll;
      overflow-y: auto;
    }

    div {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;

      @media (max-width: 1000px) {
        flex-flow: column wrap;
        height: 300px;
      }

      a {
        height: 75px;
        max-height: 75px;
        text-align: center;
        margin: 2em 0.5em;
        transition: $tr2;
        width: 23%;
        display: flex;
        align-items: center;
        justify-content: center;

        @media (max-width: 1000px) {
          width: 40%;
          height: 120px;
          max-height: 120px;
          margin: 1em;
        }

        @media (max-width: 480px) {
          width: 60%;
        }
      }

      //DON'T DEL!
      // &:hover {
      //   filter: sepia(1) contrast(200%);
      // }

      img {
        display: block;
        margin: auto;
        max-height: 75px;
      }
    }
  }

  &__form {
    @media (max-width: 812px) {
      width: 80%;
      margin: auto;
    }

    @media (max-width: 600px) {
      width: 100%;
    }
  }
}

#offices {
  @media (max-width: 900px) and (orientation: landscape) {
    padding-bottom: 50px;
  }
}

.lc-studios-wrap, .sw-offices-wrap {
  position: relative;
  z-index: 2;
  max-width: 1440px;
  height: 100%;
  width: 100%;
  display: flex;
  margin: 0 auto 20vh;

  @media (min-width: 1921px), (min-height: 1081px) {
    margin-bottom: 220px;
  }

  @media (max-width: 1200px) {
    margin-top: 100px;
  }

  @media (max-width: 450px) {
    flex-direction: column;
    margin-top: 50px;
  }

  &__inner {
    flex-shrink: 0;

    &:nth-child(1) {
      display: flex;
      flex-direction: column;
      width: 30%;
      padding-right: 30px;
      margin-bottom: 25px;

      @media (max-width: 1440px) {
        padding-left: 15px;
      }

      @media (max-width: 800px), (max-width: 900px) and (orientation: landscape) {
        justify-content: center;
        flex-basis: 40%;
      }

      @media (max-width: 480px) {
        width: 40%;
      }

      @media (max-width: 450px) {
        width: 100%;
        padding-right: 15px;
      }
    }

    &:nth-child(2) {
      width: 100%;

      @media (max-width: 450px) {
        padding-left: 15px;
      }
    }
  }
}

.lc-studios-count, .sw-offices-count {
  overflow: hidden;
  font-family: $fontOswald;
  color: rgba($secondaryColor, 1);
  font-size: 5.2vw;
  letter-spacing: 0.011em;

  @media (min-width: 1921px) {
    font-size: 100px;
  }

  @media (max-width: 800px), (max-width: 900px) and (orientation: landscape) {
    text-align: center;
  }

  &__number {
    display: inline-block;
    color: #fff;

    @media (max-width: 1024px) {
      display: block;
    }

    @media (max-width: 800px), (max-width: 900px) and (orientation: landscape) {
      font-size: 136px;
    }
  }

  &__text {
    display: inline-block;

    @media (max-width: 800px), (max-width: 900px) and (orientation: landscape) {
      font-size: 48px;
    }
  }
}

.lc-top-slider,
.lc-studios-slider,
.sw-offices-slider {
  overflow: hidden;
}

.privacy-policy,
.cookie-policy {
  margin-top: 6.94vh;
  margin-bottom: 12.96vh;

  @media (min-width: 1921px), (min-height: 1081px) {
    margin-top: 75px;
    margin-bottom: 140px;
  }

  &__wrap {
    margin-top: 12.5vh;

    @media (min-width: 1921px), (min-height: 1081px) {
      margin-top: 135px;
    }

    &.leftside {
      padding-right: 2.604vw;

      @media (min-width: 1921px) {
        padding-right: 50px;
      }
    }

    &.rightside {
      padding-left: 2.604vw;

      @media (min-width: 1921px) {
        padding-left: 50px;
      }
    }

    @media (max-width: 992px) {
      margin-top: 70px;

      &.leftside {
        padding-right: 0;
      }

      &.rightside {
        padding-left: 0;
      }
    }
  }

  &__article {
    margin-bottom: 6.48vh;

    @media (min-width: 1921px), (min-height: 1081px) {
      margin-bottom: 70px;
    }

    &:last-child {
      margin-bottom: 0;
    }

    &--title {
      display: inline-block;
      font-family: $fontOswald;
      font-size: 36px;
      line-height: initial;
      margin-bottom: 5.27vh;
      position: relative;

      @media (min-width: 1921px), (min-height: 1081px) {
        font-size: 36px;
        margin-bottom: 57px;
      }

      @media (max-width: 992px) {
        font-size: 24px;
        margin-bottom: 50px;
      }

      &::after {
        content: "";
        position: absolute;
        left: 0;
        bottom: -2.13vh;
        width: 100%;
        height: 2px;
        background-color: $secondaryColor;

        @media (min-width: 1921px), (min-height: 1081px) {
          bottom: -23px;
        }

        @media (max-width: 992px) {
          bottom: -25px;
        }
      }
    }

    &--text {
      font-size: 20px;
      line-height: initial;
      //white-space: pre-line;

      @media (min-width: 1921px) {
        font-size: 20px;
      }

      @media (max-width: 992px) {
        font-size: 16px;
        line-height: 36px;
      }

      u {
        display: inline-block;
        margin: 0.5em 0;
      }

      .table-cookie {
        margin: 2em 0;
        white-space: normal;

        table {
          font-size: 16px;
          line-height: 1.4;
          width: 100%;
          border-collapse: collapse;

          thead {
            background-color: #222;
          }

          th,
          tr,
          td {
            border: 1px solid #999;
            vertical-align: middle;
          }

          th {
            text-align: left;
            padding: 0.5em;
            line-height: 1;
          }

          td {
            padding: 0.5em;

            &:nth-child(4) {
              width: 40%;
              @media (max-width: 1300px) {
                width: 100%;
              }
            }

            a {
              display: block;
              text-decoration: underline;
              word-break: break-all;
            }
          }

          @media (max-width: 1300px) {
            table,
            tbody,
            th,
            td,
            tr {
              display: block;
            }

            tr {
              border: none;
            }

            thead {
              background-color: transparent;

              tr {
                position: absolute;
                top: -9999px;
                left: -9999px;
              }
            }

            td {
              border-bottom: none;
              position: relative;
              padding-left: 42%;
              min-height: 60px;

              @media (max-width: 480px) {
                padding-left: 35%;
              }

              &::before {
                background-color: #222;
                position: absolute;
                top: 0;
                left: 0;
                width: 40%;
                padding: 0.5em;
                border-right: 1px solid #999;
                height: 100%;

                @media (max-width: 480px) {
                  width: 33%;
                }
              }

              &:nth-of-type(1)::before {
                content: "Cookie";
              }

              &:nth-of-type(2)::before {
                content: "Third Party Provider";
              }

              &:nth-of-type(3)::before {
                content: "Category of Cookie";
              }

              &:nth-of-type(4)::before {
                content: "Description of what Cookie does / Purpose";
              }

              &:nth-of-type(5)::before {
                content: "Duration of cookie";
              }

              &:nth-of-type(6)::before {
                content: "Further information";
              }
            }

            tbody {
              tr:nth-child(2) {
                td {
                  &:last-child {
                    border-left: none;
                    border-right: none;
                    padding: 0;
                    height: 0;

                    &::before {
                      content: none;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  a {
    color: white;
  }
}


.text-center {
  text-align: center !important;
}

.justify-content-center {
  justify-content: center !important;
}

.d-flex {
  display: flex !important;
}

// animations
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
