/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */
/* Document
   ========================================================================== */
/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */
html {
  line-height: 1.3;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */ }

/* Sections
   ========================================================================== */
/**
 * Remove the margin in all browsers.
 */
body {
  margin: 0; }

/**
 * Render the `main` element consistently in IE.
 */
main {
  display: block; }

/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */
h1, .h1 {
  font-size: 2em;
  margin: 0.67em 0; }

/* Grouping content
   ========================================================================== */
/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */
hr {
  box-sizing: content-box;
  /* 1 */
  height: 0;
  /* 1 */
  overflow: visible;
  /* 2 */ }

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
pre {
  font-family: monospace, monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */ }

/* Text-level semantics
   ========================================================================== */
/**
 * Remove the gray background on active links in IE 10.
 */
a {
  background-color: transparent;
  text-decoration: none; }

/**
 * 1. Remove the bottom border in Chrome 57-
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */
abbr[title] {
  border-bottom: none;
  /* 1 */
  text-decoration: underline;
  /* 2 */ }

/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */
b,
strong {
  font-weight: bolder; }

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
code,
kbd,
samp {
  font-family: monospace, monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */ }

/**
 * Add the correct font size in all browsers.
 */
small, .small {
  font-size: 80%; }

/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline; }

sub {
  bottom: -0.25em; }

sup {
  top: -0.5em; }

/* Embedded content
   ========================================================================== */
/**
 * Remove the border on images inside links in IE 10.
 */
img {
  border-style: none; }

figure {
  margin: 0; }

/* Forms
   ========================================================================== */
/**
 * 1. Change the font styles in all browsers.
 * 2. Remove the margin in Firefox and Safari.
 */
button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  line-height: 1.15;
  /* 1 */
  margin: 0;
  /* 2 */ }

/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */
button,
input {
  /* 1 */
  overflow: visible; }

/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */
button,
select {
  /* 1 */
  text-transform: none; }

/**
 * Correct the inability to style clickable types in iOS and Safari.
 */
button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button; }

/**
 * Remove the inner border and padding in Firefox.
 */
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0; }

/**
 * Restore the focus styles unset by the previous rule.
 */
button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
  outline: 1px dotted ButtonText; }

/**
 * Correct the padding in Firefox.
 */
fieldset {
  padding: 0.35em 0.75em 0.625em; }

/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *    `fieldset` elements in all browsers.
 */
legend {
  box-sizing: border-box;
  /* 1 */
  color: inherit;
  /* 2 */
  display: table;
  /* 1 */
  max-width: 100%;
  /* 1 */
  padding: 0;
  /* 3 */
  white-space: normal;
  /* 1 */ }

/**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */
progress {
  vertical-align: baseline; }

/**
 * Remove the default vertical scrollbar in IE 10+.
 */
textarea {
  overflow: auto; }

/**
 * 1. Add the correct box sizing in IE 10.
 * 2. Remove the padding in IE 10.
 */
[type="checkbox"],
[type="radio"] {
  box-sizing: border-box;
  /* 1 */
  padding: 0;
  /* 2 */ }

/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto; }

/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */
[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */ }

/**
 * Remove the inner padding in Chrome and Safari on macOS.
 */
[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none; }

/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */
::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */ }

/* Interactive
   ========================================================================== */
/*
 * Add the correct display in Edge, IE 10+, and Firefox.
 */
details {
  display: block; }

/*
 * Add the correct display in all browsers.
 */
summary {
  display: list-item; }

/* Misc
   ========================================================================== */
/**
 * Add the correct display in IE 10+.
 */
template {
  display: none; }

/**
 * Add the correct display in IE 10.
 */
[hidden] {
  display: none; }

/* Lists
   ========================================================================== */
ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem; }

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0; }

/* Typography
   ========================================================================== */
h1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6 {
  margin-top: 0;
  font-weight: normal; }

p {
  margin-top: 0; }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

html {
  overflow-x: hidden;
  scroll-behavior: smooth; }

/* ensure all pages have Bootstrap CSS */
/*!
 * Bootstrap  v5.2.1 (https://getbootstrap.com/)
 * Copyright 2011-2022 The Bootstrap Authors
 * Copyright 2011-2022 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 */
:root {
  --bs-blue: #0d6efd;
  --bs-indigo: #6610f2;
  --bs-purple: #6f42c1;
  --bs-pink: #d63384;
  --bs-red: #ff4136;
  --bs-orange: #fd7e14;
  --bs-yellow: #ffc107;
  --bs-green: #198754;
  --bs-teal: #20c997;
  --bs-cyan: #0dcaf0;
  --bs-black: #000;
  --bs-white: #fff;
  --bs-gray: #6c757d;
  --bs-gray-dark: #343a40;
  --bs-gray-100: #f8f9fa;
  --bs-gray-200: #e9ecef;
  --bs-gray-300: #dee2e6;
  --bs-gray-400: #ced4da;
  --bs-gray-500: #adb5bd;
  --bs-gray-600: #6c757d;
  --bs-gray-700: #495057;
  --bs-gray-800: #343a40;
  --bs-gray-900: #212529;
  --bs-primary: #8dd363;
  --bs-danger: #ff4136;
  --bs-primary-rgb: 141, 211, 99;
  --bs-danger-rgb: 255, 65, 54;
  --bs-white-rgb: 255, 255, 255;
  --bs-black-rgb: 0, 0, 0;
  --bs-body-color-rgb: 33, 37, 41;
  --bs-body-bg-rgb: 24, 23, 23;
  --bs-font-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", "Noto Sans", "Liberation Sans", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
  --bs-body-font-family: var(--bs-font-sans-serif);
  --bs-body-font-size: 1rem;
  --bs-body-font-weight: 400;
  --bs-body-line-height: 1.5;
  --bs-body-color: #212529;
  --bs-body-bg: #181717;
  --bs-border-width: 1px;
  --bs-border-style: solid;
  --bs-border-color: #dee2e6;
  --bs-border-color-translucent: rgba(0, 0, 0, 0.175);
  --bs-border-radius: 0.375rem;
  --bs-border-radius-sm: 0.25rem;
  --bs-border-radius-lg: 0.5rem;
  --bs-border-radius-xl: 1rem;
  --bs-border-radius-2xl: 2rem;
  --bs-border-radius-pill: 50rem;
  --bs-link-color: #0d6efd;
  --bs-link-hover-color: #0a58ca;
  --bs-code-color: #d63384;
  --bs-highlight-bg: #fff3cd; }

*,
*::before,
*::after {
  box-sizing: border-box; }

@media (prefers-reduced-motion: no-preference) {
  :root {
    scroll-behavior: smooth; } }

body {
  margin: 0;
  font-family: var(--bs-body-font-family);
  font-size: var(--bs-body-font-size);
  font-weight: var(--bs-body-font-weight);
  line-height: var(--bs-body-line-height);
  color: var(--bs-body-color);
  text-align: var(--bs-body-text-align);
  background-color: var(--bs-body-bg);
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0); }

hr {
  margin: 1rem 0;
  color: inherit;
  border: 0;
  border-top: 1px solid;
  opacity: 0.25; }

h1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 500;
  line-height: 1.2; }

h1, .h1 {
  font-size: calc(1.375rem + 1.5vw); }
  @media (min-width: 1200px) {
    h1, .h1 {
      font-size: 2.5rem; } }

h2, .h2 {
  font-size: calc(1.325rem + 0.9vw); }
  @media (min-width: 1200px) {
    h2, .h2 {
      font-size: 2rem; } }

h3, .h3 {
  font-size: calc(1.3rem + 0.6vw); }
  @media (min-width: 1200px) {
    h3, .h3 {
      font-size: 1.75rem; } }

h4, .h4 {
  font-size: calc(1.275rem + 0.3vw); }
  @media (min-width: 1200px) {
    h4, .h4 {
      font-size: 1.5rem; } }

h5, .h5 {
  font-size: 1.25rem; }

h6, .h6 {
  font-size: 1rem; }

p {
  margin-top: 0;
  margin-bottom: 1rem; }

abbr[title] {
  text-decoration: underline dotted;
  cursor: help;
  text-decoration-skip-ink: none; }

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit; }

ol,
ul {
  padding-left: 2rem; }

ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem; }

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0; }

dt {
  font-weight: 700; }

dd {
  margin-bottom: .5rem;
  margin-left: 0; }

blockquote {
  margin: 0 0 1rem; }

b,
strong {
  font-weight: bolder; }

small, .small {
  font-size: 0.875em; }

mark, .mark {
  padding: 0.1875em;
  background-color: var(--bs-highlight-bg); }

sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline; }

sub {
  bottom: -.25em; }

sup {
  top: -.5em; }

a {
  color: var(--bs-link-color);
  text-decoration: none; }
  a:hover {
    color: var(--bs-link-hover-color);
    text-decoration: none; }

a:not([href]):not([class]), a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none; }

pre,
code,
kbd,
samp {
  font-family: var(--bs-font-monospace);
  font-size: 1em; }

pre {
  display: block;
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  font-size: 0.875em; }
  pre code {
    font-size: inherit;
    color: inherit;
    word-break: normal; }

code {
  font-size: 0.875em;
  color: var(--bs-code-color);
  word-wrap: break-word; }
  a > code {
    color: inherit; }

kbd {
  padding: 0.1875rem 0.375rem;
  font-size: 0.875em;
  color: var(--bs-body-bg);
  background-color: var(--bs-body-color);
  border-radius: 0.25rem; }
  kbd kbd {
    padding: 0;
    font-size: 1em; }

figure {
  margin: 0 0 1rem; }

img,
svg {
  vertical-align: middle; }

table {
  caption-side: bottom;
  border-collapse: collapse; }

caption {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  color: #6c757d;
  text-align: left; }

th {
  text-align: inherit;
  text-align: -webkit-match-parent; }

thead,
tbody,
tfoot,
tr,
td,
th {
  border-color: inherit;
  border-style: solid;
  border-width: 0; }

label {
  display: inline-block; }

button {
  border-radius: 0; }

button:focus:not(:focus-visible) {
  outline: 0; }

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit; }

button,
select {
  text-transform: none; }

[role="button"] {
  cursor: pointer; }

select {
  word-wrap: normal; }
  select:disabled {
    opacity: 1; }

[list]:not([type="date"]):not([type="datetime-local"]):not([type="month"]):not([type="week"]):not([type="time"])::-webkit-calendar-picker-indicator {
  display: none !important; }

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button; }
  button:not(:disabled),
  [type="button"]:not(:disabled),
  [type="reset"]:not(:disabled),
  [type="submit"]:not(:disabled) {
    cursor: pointer; }

::-moz-focus-inner {
  padding: 0;
  border-style: none; }

textarea {
  resize: vertical; }

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0; }

legend {
  float: left;
  width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: calc(1.275rem + 0.3vw);
  line-height: inherit; }
  @media (min-width: 1200px) {
    legend {
      font-size: 1.5rem; } }
  legend + * {
    clear: left; }

::-webkit-datetime-edit-fields-wrapper,
::-webkit-datetime-edit-text,
::-webkit-datetime-edit-minute,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-year-field {
  padding: 0; }

::-webkit-inner-spin-button {
  height: auto; }

[type="search"] {
  outline-offset: -2px;
  -webkit-appearance: textfield; }

/* rtl:raw:
[type="tel"],
[type="url"],
[type="email"],
[type="number"] {
  direction: ltr;
}
*/
::-webkit-search-decoration {
  -webkit-appearance: none; }

::-webkit-color-swatch-wrapper {
  padding: 0; }

::file-selector-button {
  font: inherit;
  -webkit-appearance: button; }

output {
  display: inline-block; }

iframe {
  border: 0; }

summary {
  display: list-item;
  cursor: pointer; }

progress {
  vertical-align: baseline; }

[hidden] {
  display: none !important; }

.lead {
  font-size: 1.25rem;
  font-weight: 300; }

.display-1 {
  font-size: calc(1.625rem + 4.5vw);
  font-weight: 300;
  line-height: 1.2; }
  @media (min-width: 1200px) {
    .display-1 {
      font-size: 5rem; } }

.display-2 {
  font-size: calc(1.575rem + 3.9vw);
  font-weight: 300;
  line-height: 1.2; }
  @media (min-width: 1200px) {
    .display-2 {
      font-size: 4.5rem; } }

.display-3 {
  font-size: calc(1.525rem + 3.3vw);
  font-weight: 300;
  line-height: 1.2; }
  @media (min-width: 1200px) {
    .display-3 {
      font-size: 4rem; } }

.display-4 {
  font-size: calc(1.475rem + 2.7vw);
  font-weight: 300;
  line-height: 1.2; }
  @media (min-width: 1200px) {
    .display-4 {
      font-size: 3.5rem; } }

.display-5 {
  font-size: calc(1.425rem + 2.1vw);
  font-weight: 300;
  line-height: 1.2; }
  @media (min-width: 1200px) {
    .display-5 {
      font-size: 3rem; } }

.display-6 {
  font-size: calc(1.375rem + 1.5vw);
  font-weight: 300;
  line-height: 1.2; }
  @media (min-width: 1200px) {
    .display-6 {
      font-size: 2.5rem; } }

.list-unstyled {
  padding-left: 0;
  list-style: none; }

.list-inline {
  padding-left: 0;
  list-style: none; }

.list-inline-item {
  display: inline-block; }
  .list-inline-item:not(:last-child) {
    margin-right: 0.5rem; }

.initialism {
  font-size: 0.875em;
  text-transform: uppercase; }

.blockquote {
  margin-bottom: 1rem;
  font-size: 1.25rem; }
  .blockquote > :last-child {
    margin-bottom: 0; }

.blockquote-footer {
  margin-top: -1rem;
  margin-bottom: 1rem;
  font-size: 0.875em;
  color: #6c757d; }
  .blockquote-footer::before {
    content: "\2014\00A0"; }

.img-fluid {
  max-width: 100%;
  height: auto; }

.img-thumbnail {
  padding: 0.25rem;
  background-color: #181717;
  border: 1px solid var(--bs-border-color);
  border-radius: 0.375rem;
  max-width: 100%;
  height: auto; }

.figure {
  display: inline-block; }

.figure-img {
  margin-bottom: 0.5rem;
  line-height: 1; }

.figure-caption {
  font-size: 0.875em;
  color: #6c757d; }

.container,
.container-fluid,
.container-sm,
.container-md,
.container-lg,
.container-xl,
.container-xxl {
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 0;
  width: 100%;
  padding-right: calc(var(--bs-gutter-x) * .5);
  padding-left: calc(var(--bs-gutter-x) * .5);
  margin-right: auto;
  margin-left: auto; }

@media (min-width: 576px) {
  .container, .container-sm {
    max-width: 540px; } }
@media (min-width: 768px) {
  .container, .container-sm, .container-md {
    max-width: 720px; } }
@media (min-width: 992px) {
  .container, .container-sm, .container-md, .container-lg {
    max-width: 960px; } }
@media (min-width: 1200px) {
  .container, .container-sm, .container-md, .container-lg, .container-xl {
    max-width: 1140px; } }
@media (min-width: 1400px) {
  .container, .container-sm, .container-md, .container-lg, .container-xl, .container-xxl {
    max-width: 1320px; } }
.row {
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 0;
  display: flex;
  flex-wrap: wrap;
  margin-top: calc(-1 * var(--bs-gutter-y));
  margin-right: calc(-.5 * var(--bs-gutter-x));
  margin-left: calc(-.5 * var(--bs-gutter-x)); }
  .row > * {
    flex-shrink: 0;
    width: 100%;
    max-width: 100%;
    padding-right: calc(var(--bs-gutter-x) * .5);
    padding-left: calc(var(--bs-gutter-x) * .5);
    margin-top: var(--bs-gutter-y); }

.col {
  flex: 1 0 0%; }

.row-cols-auto > * {
  flex: 0 0 auto;
  width: auto; }

.row-cols-1 > * {
  flex: 0 0 auto;
  width: 100%; }

.row-cols-2 > * {
  flex: 0 0 auto;
  width: 50%; }

.row-cols-3 > * {
  flex: 0 0 auto;
  width: 33.3333333333%; }

.row-cols-4 > * {
  flex: 0 0 auto;
  width: 25%; }

.row-cols-5 > * {
  flex: 0 0 auto;
  width: 20%; }

.row-cols-6 > * {
  flex: 0 0 auto;
  width: 16.6666666667%; }

.col-auto {
  flex: 0 0 auto;
  width: auto; }

.col-1 {
  flex: 0 0 auto;
  width: 8.33333333%; }

.col-2 {
  flex: 0 0 auto;
  width: 16.66666667%; }

.col-3 {
  flex: 0 0 auto;
  width: 25%; }

.col-4 {
  flex: 0 0 auto;
  width: 33.33333333%; }

.col-5 {
  flex: 0 0 auto;
  width: 41.66666667%; }

.col-6 {
  flex: 0 0 auto;
  width: 50%; }

.col-7 {
  flex: 0 0 auto;
  width: 58.33333333%; }

.col-8 {
  flex: 0 0 auto;
  width: 66.66666667%; }

.col-9 {
  flex: 0 0 auto;
  width: 75%; }

.col-10 {
  flex: 0 0 auto;
  width: 83.33333333%; }

.col-11 {
  flex: 0 0 auto;
  width: 91.66666667%; }

.col-12 {
  flex: 0 0 auto;
  width: 100%; }

.offset-1 {
  margin-left: 8.33333333%; }

.offset-2 {
  margin-left: 16.66666667%; }

.offset-3 {
  margin-left: 25%; }

.offset-4 {
  margin-left: 33.33333333%; }

.offset-5 {
  margin-left: 41.66666667%; }

.offset-6 {
  margin-left: 50%; }

.offset-7 {
  margin-left: 58.33333333%; }

.offset-8 {
  margin-left: 66.66666667%; }

.offset-9 {
  margin-left: 75%; }

.offset-10 {
  margin-left: 83.33333333%; }

.offset-11 {
  margin-left: 91.66666667%; }

.g-0,
.gx-0 {
  --bs-gutter-x: 0; }

.g-0,
.gy-0 {
  --bs-gutter-y: 0; }

.g-1,
.gx-1 {
  --bs-gutter-x: 0.25rem; }

.g-1,
.gy-1 {
  --bs-gutter-y: 0.25rem; }

.g-2,
.gx-2 {
  --bs-gutter-x: 0.5rem; }

.g-2,
.gy-2 {
  --bs-gutter-y: 0.5rem; }

.g-3,
.gx-3 {
  --bs-gutter-x: 1rem; }

.g-3,
.gy-3 {
  --bs-gutter-y: 1rem; }

.g-4,
.gx-4 {
  --bs-gutter-x: 1.5rem; }

.g-4,
.gy-4 {
  --bs-gutter-y: 1.5rem; }

.g-5,
.gx-5 {
  --bs-gutter-x: 3rem; }

.g-5,
.gy-5 {
  --bs-gutter-y: 3rem; }

@media (min-width: 576px) {
  .col-sm {
    flex: 1 0 0%; }

  .row-cols-sm-auto > * {
    flex: 0 0 auto;
    width: auto; }

  .row-cols-sm-1 > * {
    flex: 0 0 auto;
    width: 100%; }

  .row-cols-sm-2 > * {
    flex: 0 0 auto;
    width: 50%; }

  .row-cols-sm-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%; }

  .row-cols-sm-4 > * {
    flex: 0 0 auto;
    width: 25%; }

  .row-cols-sm-5 > * {
    flex: 0 0 auto;
    width: 20%; }

  .row-cols-sm-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%; }

  .col-sm-auto {
    flex: 0 0 auto;
    width: auto; }

  .col-sm-1 {
    flex: 0 0 auto;
    width: 8.33333333%; }

  .col-sm-2 {
    flex: 0 0 auto;
    width: 16.66666667%; }

  .col-sm-3 {
    flex: 0 0 auto;
    width: 25%; }

  .col-sm-4 {
    flex: 0 0 auto;
    width: 33.33333333%; }

  .col-sm-5 {
    flex: 0 0 auto;
    width: 41.66666667%; }

  .col-sm-6 {
    flex: 0 0 auto;
    width: 50%; }

  .col-sm-7 {
    flex: 0 0 auto;
    width: 58.33333333%; }

  .col-sm-8 {
    flex: 0 0 auto;
    width: 66.66666667%; }

  .col-sm-9 {
    flex: 0 0 auto;
    width: 75%; }

  .col-sm-10 {
    flex: 0 0 auto;
    width: 83.33333333%; }

  .col-sm-11 {
    flex: 0 0 auto;
    width: 91.66666667%; }

  .col-sm-12 {
    flex: 0 0 auto;
    width: 100%; }

  .offset-sm-0 {
    margin-left: 0; }

  .offset-sm-1 {
    margin-left: 8.33333333%; }

  .offset-sm-2 {
    margin-left: 16.66666667%; }

  .offset-sm-3 {
    margin-left: 25%; }

  .offset-sm-4 {
    margin-left: 33.33333333%; }

  .offset-sm-5 {
    margin-left: 41.66666667%; }

  .offset-sm-6 {
    margin-left: 50%; }

  .offset-sm-7 {
    margin-left: 58.33333333%; }

  .offset-sm-8 {
    margin-left: 66.66666667%; }

  .offset-sm-9 {
    margin-left: 75%; }

  .offset-sm-10 {
    margin-left: 83.33333333%; }

  .offset-sm-11 {
    margin-left: 91.66666667%; }

  .g-sm-0,
  .gx-sm-0 {
    --bs-gutter-x: 0; }

  .g-sm-0,
  .gy-sm-0 {
    --bs-gutter-y: 0; }

  .g-sm-1,
  .gx-sm-1 {
    --bs-gutter-x: 0.25rem; }

  .g-sm-1,
  .gy-sm-1 {
    --bs-gutter-y: 0.25rem; }

  .g-sm-2,
  .gx-sm-2 {
    --bs-gutter-x: 0.5rem; }

  .g-sm-2,
  .gy-sm-2 {
    --bs-gutter-y: 0.5rem; }

  .g-sm-3,
  .gx-sm-3 {
    --bs-gutter-x: 1rem; }

  .g-sm-3,
  .gy-sm-3 {
    --bs-gutter-y: 1rem; }

  .g-sm-4,
  .gx-sm-4 {
    --bs-gutter-x: 1.5rem; }

  .g-sm-4,
  .gy-sm-4 {
    --bs-gutter-y: 1.5rem; }

  .g-sm-5,
  .gx-sm-5 {
    --bs-gutter-x: 3rem; }

  .g-sm-5,
  .gy-sm-5 {
    --bs-gutter-y: 3rem; } }
@media (min-width: 768px) {
  .col-md {
    flex: 1 0 0%; }

  .row-cols-md-auto > * {
    flex: 0 0 auto;
    width: auto; }

  .row-cols-md-1 > * {
    flex: 0 0 auto;
    width: 100%; }

  .row-cols-md-2 > * {
    flex: 0 0 auto;
    width: 50%; }

  .row-cols-md-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%; }

  .row-cols-md-4 > * {
    flex: 0 0 auto;
    width: 25%; }

  .row-cols-md-5 > * {
    flex: 0 0 auto;
    width: 20%; }

  .row-cols-md-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%; }

  .col-md-auto {
    flex: 0 0 auto;
    width: auto; }

  .col-md-1 {
    flex: 0 0 auto;
    width: 8.33333333%; }

  .col-md-2 {
    flex: 0 0 auto;
    width: 16.66666667%; }

  .col-md-3 {
    flex: 0 0 auto;
    width: 25%; }

  .col-md-4 {
    flex: 0 0 auto;
    width: 33.33333333%; }

  .col-md-5 {
    flex: 0 0 auto;
    width: 41.66666667%; }

  .col-md-6 {
    flex: 0 0 auto;
    width: 50%; }

  .col-md-7 {
    flex: 0 0 auto;
    width: 58.33333333%; }

  .col-md-8 {
    flex: 0 0 auto;
    width: 66.66666667%; }

  .col-md-9 {
    flex: 0 0 auto;
    width: 75%; }

  .col-md-10 {
    flex: 0 0 auto;
    width: 83.33333333%; }

  .col-md-11 {
    flex: 0 0 auto;
    width: 91.66666667%; }

  .col-md-12 {
    flex: 0 0 auto;
    width: 100%; }

  .offset-md-0 {
    margin-left: 0; }

  .offset-md-1 {
    margin-left: 8.33333333%; }

  .offset-md-2 {
    margin-left: 16.66666667%; }

  .offset-md-3 {
    margin-left: 25%; }

  .offset-md-4 {
    margin-left: 33.33333333%; }

  .offset-md-5 {
    margin-left: 41.66666667%; }

  .offset-md-6 {
    margin-left: 50%; }

  .offset-md-7 {
    margin-left: 58.33333333%; }

  .offset-md-8 {
    margin-left: 66.66666667%; }

  .offset-md-9 {
    margin-left: 75%; }

  .offset-md-10 {
    margin-left: 83.33333333%; }

  .offset-md-11 {
    margin-left: 91.66666667%; }

  .g-md-0,
  .gx-md-0 {
    --bs-gutter-x: 0; }

  .g-md-0,
  .gy-md-0 {
    --bs-gutter-y: 0; }

  .g-md-1,
  .gx-md-1 {
    --bs-gutter-x: 0.25rem; }

  .g-md-1,
  .gy-md-1 {
    --bs-gutter-y: 0.25rem; }

  .g-md-2,
  .gx-md-2 {
    --bs-gutter-x: 0.5rem; }

  .g-md-2,
  .gy-md-2 {
    --bs-gutter-y: 0.5rem; }

  .g-md-3,
  .gx-md-3 {
    --bs-gutter-x: 1rem; }

  .g-md-3,
  .gy-md-3 {
    --bs-gutter-y: 1rem; }

  .g-md-4,
  .gx-md-4 {
    --bs-gutter-x: 1.5rem; }

  .g-md-4,
  .gy-md-4 {
    --bs-gutter-y: 1.5rem; }

  .g-md-5,
  .gx-md-5 {
    --bs-gutter-x: 3rem; }

  .g-md-5,
  .gy-md-5 {
    --bs-gutter-y: 3rem; } }
@media (min-width: 992px) {
  .col-lg {
    flex: 1 0 0%; }

  .row-cols-lg-auto > * {
    flex: 0 0 auto;
    width: auto; }

  .row-cols-lg-1 > * {
    flex: 0 0 auto;
    width: 100%; }

  .row-cols-lg-2 > * {
    flex: 0 0 auto;
    width: 50%; }

  .row-cols-lg-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%; }

  .row-cols-lg-4 > * {
    flex: 0 0 auto;
    width: 25%; }

  .row-cols-lg-5 > * {
    flex: 0 0 auto;
    width: 20%; }

  .row-cols-lg-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%; }

  .col-lg-auto {
    flex: 0 0 auto;
    width: auto; }

  .col-lg-1 {
    flex: 0 0 auto;
    width: 8.33333333%; }

  .col-lg-2 {
    flex: 0 0 auto;
    width: 16.66666667%; }

  .col-lg-3 {
    flex: 0 0 auto;
    width: 25%; }

  .col-lg-4 {
    flex: 0 0 auto;
    width: 33.33333333%; }

  .col-lg-5 {
    flex: 0 0 auto;
    width: 41.66666667%; }

  .col-lg-6 {
    flex: 0 0 auto;
    width: 50%; }

  .col-lg-7 {
    flex: 0 0 auto;
    width: 58.33333333%; }

  .col-lg-8 {
    flex: 0 0 auto;
    width: 66.66666667%; }

  .col-lg-9 {
    flex: 0 0 auto;
    width: 75%; }

  .col-lg-10 {
    flex: 0 0 auto;
    width: 83.33333333%; }

  .col-lg-11 {
    flex: 0 0 auto;
    width: 91.66666667%; }

  .col-lg-12 {
    flex: 0 0 auto;
    width: 100%; }

  .offset-lg-0 {
    margin-left: 0; }

  .offset-lg-1 {
    margin-left: 8.33333333%; }

  .offset-lg-2 {
    margin-left: 16.66666667%; }

  .offset-lg-3 {
    margin-left: 25%; }

  .offset-lg-4 {
    margin-left: 33.33333333%; }

  .offset-lg-5 {
    margin-left: 41.66666667%; }

  .offset-lg-6 {
    margin-left: 50%; }

  .offset-lg-7 {
    margin-left: 58.33333333%; }

  .offset-lg-8 {
    margin-left: 66.66666667%; }

  .offset-lg-9 {
    margin-left: 75%; }

  .offset-lg-10 {
    margin-left: 83.33333333%; }

  .offset-lg-11 {
    margin-left: 91.66666667%; }

  .g-lg-0,
  .gx-lg-0 {
    --bs-gutter-x: 0; }

  .g-lg-0,
  .gy-lg-0 {
    --bs-gutter-y: 0; }

  .g-lg-1,
  .gx-lg-1 {
    --bs-gutter-x: 0.25rem; }

  .g-lg-1,
  .gy-lg-1 {
    --bs-gutter-y: 0.25rem; }

  .g-lg-2,
  .gx-lg-2 {
    --bs-gutter-x: 0.5rem; }

  .g-lg-2,
  .gy-lg-2 {
    --bs-gutter-y: 0.5rem; }

  .g-lg-3,
  .gx-lg-3 {
    --bs-gutter-x: 1rem; }

  .g-lg-3,
  .gy-lg-3 {
    --bs-gutter-y: 1rem; }

  .g-lg-4,
  .gx-lg-4 {
    --bs-gutter-x: 1.5rem; }

  .g-lg-4,
  .gy-lg-4 {
    --bs-gutter-y: 1.5rem; }

  .g-lg-5,
  .gx-lg-5 {
    --bs-gutter-x: 3rem; }

  .g-lg-5,
  .gy-lg-5 {
    --bs-gutter-y: 3rem; } }
@media (min-width: 1200px) {
  .col-xl {
    flex: 1 0 0%; }

  .row-cols-xl-auto > * {
    flex: 0 0 auto;
    width: auto; }

  .row-cols-xl-1 > * {
    flex: 0 0 auto;
    width: 100%; }

  .row-cols-xl-2 > * {
    flex: 0 0 auto;
    width: 50%; }

  .row-cols-xl-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%; }

  .row-cols-xl-4 > * {
    flex: 0 0 auto;
    width: 25%; }

  .row-cols-xl-5 > * {
    flex: 0 0 auto;
    width: 20%; }

  .row-cols-xl-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%; }

  .col-xl-auto {
    flex: 0 0 auto;
    width: auto; }

  .col-xl-1 {
    flex: 0 0 auto;
    width: 8.33333333%; }

  .col-xl-2 {
    flex: 0 0 auto;
    width: 16.66666667%; }

  .col-xl-3 {
    flex: 0 0 auto;
    width: 25%; }

  .col-xl-4 {
    flex: 0 0 auto;
    width: 33.33333333%; }

  .col-xl-5 {
    flex: 0 0 auto;
    width: 41.66666667%; }

  .col-xl-6 {
    flex: 0 0 auto;
    width: 50%; }

  .col-xl-7 {
    flex: 0 0 auto;
    width: 58.33333333%; }

  .col-xl-8 {
    flex: 0 0 auto;
    width: 66.66666667%; }

  .col-xl-9 {
    flex: 0 0 auto;
    width: 75%; }

  .col-xl-10 {
    flex: 0 0 auto;
    width: 83.33333333%; }

  .col-xl-11 {
    flex: 0 0 auto;
    width: 91.66666667%; }

  .col-xl-12 {
    flex: 0 0 auto;
    width: 100%; }

  .offset-xl-0 {
    margin-left: 0; }

  .offset-xl-1 {
    margin-left: 8.33333333%; }

  .offset-xl-2 {
    margin-left: 16.66666667%; }

  .offset-xl-3 {
    margin-left: 25%; }

  .offset-xl-4 {
    margin-left: 33.33333333%; }

  .offset-xl-5 {
    margin-left: 41.66666667%; }

  .offset-xl-6 {
    margin-left: 50%; }

  .offset-xl-7 {
    margin-left: 58.33333333%; }

  .offset-xl-8 {
    margin-left: 66.66666667%; }

  .offset-xl-9 {
    margin-left: 75%; }

  .offset-xl-10 {
    margin-left: 83.33333333%; }

  .offset-xl-11 {
    margin-left: 91.66666667%; }

  .g-xl-0,
  .gx-xl-0 {
    --bs-gutter-x: 0; }

  .g-xl-0,
  .gy-xl-0 {
    --bs-gutter-y: 0; }

  .g-xl-1,
  .gx-xl-1 {
    --bs-gutter-x: 0.25rem; }

  .g-xl-1,
  .gy-xl-1 {
    --bs-gutter-y: 0.25rem; }

  .g-xl-2,
  .gx-xl-2 {
    --bs-gutter-x: 0.5rem; }

  .g-xl-2,
  .gy-xl-2 {
    --bs-gutter-y: 0.5rem; }

  .g-xl-3,
  .gx-xl-3 {
    --bs-gutter-x: 1rem; }

  .g-xl-3,
  .gy-xl-3 {
    --bs-gutter-y: 1rem; }

  .g-xl-4,
  .gx-xl-4 {
    --bs-gutter-x: 1.5rem; }

  .g-xl-4,
  .gy-xl-4 {
    --bs-gutter-y: 1.5rem; }

  .g-xl-5,
  .gx-xl-5 {
    --bs-gutter-x: 3rem; }

  .g-xl-5,
  .gy-xl-5 {
    --bs-gutter-y: 3rem; } }
@media (min-width: 1400px) {
  .col-xxl {
    flex: 1 0 0%; }

  .row-cols-xxl-auto > * {
    flex: 0 0 auto;
    width: auto; }

  .row-cols-xxl-1 > * {
    flex: 0 0 auto;
    width: 100%; }

  .row-cols-xxl-2 > * {
    flex: 0 0 auto;
    width: 50%; }

  .row-cols-xxl-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%; }

  .row-cols-xxl-4 > * {
    flex: 0 0 auto;
    width: 25%; }

  .row-cols-xxl-5 > * {
    flex: 0 0 auto;
    width: 20%; }

  .row-cols-xxl-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%; }

  .col-xxl-auto {
    flex: 0 0 auto;
    width: auto; }

  .col-xxl-1 {
    flex: 0 0 auto;
    width: 8.33333333%; }

  .col-xxl-2 {
    flex: 0 0 auto;
    width: 16.66666667%; }

  .col-xxl-3 {
    flex: 0 0 auto;
    width: 25%; }

  .col-xxl-4 {
    flex: 0 0 auto;
    width: 33.33333333%; }

  .col-xxl-5 {
    flex: 0 0 auto;
    width: 41.66666667%; }

  .col-xxl-6 {
    flex: 0 0 auto;
    width: 50%; }

  .col-xxl-7 {
    flex: 0 0 auto;
    width: 58.33333333%; }

  .col-xxl-8 {
    flex: 0 0 auto;
    width: 66.66666667%; }

  .col-xxl-9 {
    flex: 0 0 auto;
    width: 75%; }

  .col-xxl-10 {
    flex: 0 0 auto;
    width: 83.33333333%; }

  .col-xxl-11 {
    flex: 0 0 auto;
    width: 91.66666667%; }

  .col-xxl-12 {
    flex: 0 0 auto;
    width: 100%; }

  .offset-xxl-0 {
    margin-left: 0; }

  .offset-xxl-1 {
    margin-left: 8.33333333%; }

  .offset-xxl-2 {
    margin-left: 16.66666667%; }

  .offset-xxl-3 {
    margin-left: 25%; }

  .offset-xxl-4 {
    margin-left: 33.33333333%; }

  .offset-xxl-5 {
    margin-left: 41.66666667%; }

  .offset-xxl-6 {
    margin-left: 50%; }

  .offset-xxl-7 {
    margin-left: 58.33333333%; }

  .offset-xxl-8 {
    margin-left: 66.66666667%; }

  .offset-xxl-9 {
    margin-left: 75%; }

  .offset-xxl-10 {
    margin-left: 83.33333333%; }

  .offset-xxl-11 {
    margin-left: 91.66666667%; }

  .g-xxl-0,
  .gx-xxl-0 {
    --bs-gutter-x: 0; }

  .g-xxl-0,
  .gy-xxl-0 {
    --bs-gutter-y: 0; }

  .g-xxl-1,
  .gx-xxl-1 {
    --bs-gutter-x: 0.25rem; }

  .g-xxl-1,
  .gy-xxl-1 {
    --bs-gutter-y: 0.25rem; }

  .g-xxl-2,
  .gx-xxl-2 {
    --bs-gutter-x: 0.5rem; }

  .g-xxl-2,
  .gy-xxl-2 {
    --bs-gutter-y: 0.5rem; }

  .g-xxl-3,
  .gx-xxl-3 {
    --bs-gutter-x: 1rem; }

  .g-xxl-3,
  .gy-xxl-3 {
    --bs-gutter-y: 1rem; }

  .g-xxl-4,
  .gx-xxl-4 {
    --bs-gutter-x: 1.5rem; }

  .g-xxl-4,
  .gy-xxl-4 {
    --bs-gutter-y: 1.5rem; }

  .g-xxl-5,
  .gx-xxl-5 {
    --bs-gutter-x: 3rem; }

  .g-xxl-5,
  .gy-xxl-5 {
    --bs-gutter-y: 3rem; } }
.table {
  --bs-table-color: var(--bs-body-color);
  --bs-table-bg: transparent;
  --bs-table-border-color: var(--bs-border-color);
  --bs-table-accent-bg: transparent;
  --bs-table-striped-color: var(--bs-body-color);
  --bs-table-striped-bg: rgba(0, 0, 0, 0.05);
  --bs-table-active-color: var(--bs-body-color);
  --bs-table-active-bg: rgba(0, 0, 0, 0.1);
  --bs-table-hover-color: var(--bs-body-color);
  --bs-table-hover-bg: rgba(0, 0, 0, 0.075);
  width: 100%;
  margin-bottom: 1rem;
  color: var(--bs-table-color);
  vertical-align: top;
  border-color: var(--bs-table-border-color); }
  .table > :not(caption) > * > * {
    padding: 0.5rem 0.5rem;
    background-color: var(--bs-table-bg);
    border-bottom-width: 1px;
    box-shadow: inset 0 0 0 9999px var(--bs-table-accent-bg); }
  .table > tbody {
    vertical-align: inherit; }
  .table > thead {
    vertical-align: bottom; }

.table-group-divider {
  border-top: 2px solid currentcolor; }

.caption-top {
  caption-side: top; }

.table-sm > :not(caption) > * > * {
  padding: 0.25rem 0.25rem; }

.table-bordered > :not(caption) > * {
  border-width: 1px 0; }
  .table-bordered > :not(caption) > * > * {
    border-width: 0 1px; }

.table-borderless > :not(caption) > * > * {
  border-bottom-width: 0; }
.table-borderless > :not(:first-child) {
  border-top-width: 0; }

.table-striped > tbody > tr:nth-of-type(odd) > * {
  --bs-table-accent-bg: var(--bs-table-striped-bg);
  color: var(--bs-table-striped-color); }

.table-striped-columns > :not(caption) > tr > :nth-child(even) {
  --bs-table-accent-bg: var(--bs-table-striped-bg);
  color: var(--bs-table-striped-color); }

.table-active {
  --bs-table-accent-bg: var(--bs-table-active-bg);
  color: var(--bs-table-active-color); }

.table-hover > tbody > tr:hover > * {
  --bs-table-accent-bg: var(--bs-table-hover-bg);
  color: var(--bs-table-hover-color); }

.table-primary {
  --bs-table-color: #000;
  --bs-table-bg: #cfe2ff;
  --bs-table-border-color: #bacbe6;
  --bs-table-striped-bg: #c5d7f2;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #bacbe6;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #bfd1ec;
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color); }

.table-secondary {
  --bs-table-color: #000;
  --bs-table-bg: #e2e3e5;
  --bs-table-border-color: #cbccce;
  --bs-table-striped-bg: #d7d8da;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #cbccce;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #d1d2d4;
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color); }

.table-success {
  --bs-table-color: #000;
  --bs-table-bg: #d1e7dd;
  --bs-table-border-color: #bcd0c7;
  --bs-table-striped-bg: #c7dbd2;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #bcd0c7;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #c1d6cc;
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color); }

.table-info {
  --bs-table-color: #000;
  --bs-table-bg: #cff4fc;
  --bs-table-border-color: #badce3;
  --bs-table-striped-bg: #c5e8ef;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #badce3;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #bfe2e9;
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color); }

.table-warning {
  --bs-table-color: #000;
  --bs-table-bg: #fff3cd;
  --bs-table-border-color: #e6dbb9;
  --bs-table-striped-bg: #f2e7c3;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #e6dbb9;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #ece1be;
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color); }

.table-danger {
  --bs-table-color: #000;
  --bs-table-bg: #ffd9d7;
  --bs-table-border-color: #e6c3c2;
  --bs-table-striped-bg: #f2cecc;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #e6c3c2;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #ecc9c7;
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color); }

.table-light {
  --bs-table-color: #000;
  --bs-table-bg: #f8f9fa;
  --bs-table-border-color: #dfe0e1;
  --bs-table-striped-bg: #ecedee;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #dfe0e1;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #e5e6e7;
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color); }

.table-dark {
  --bs-table-color: #fff;
  --bs-table-bg: #212529;
  --bs-table-border-color: #373b3e;
  --bs-table-striped-bg: #2c3034;
  --bs-table-striped-color: #fff;
  --bs-table-active-bg: #373b3e;
  --bs-table-active-color: #fff;
  --bs-table-hover-bg: #323539;
  --bs-table-hover-color: #fff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color); }

.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch; }

@media (max-width: 575.98px) {
  .table-responsive-sm {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch; } }
@media (max-width: 767.98px) {
  .table-responsive-md {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch; } }
@media (max-width: 991.98px) {
  .table-responsive-lg {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch; } }
@media (max-width: 1199.98px) {
  .table-responsive-xl {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch; } }
@media (max-width: 1399.98px) {
  .table-responsive-xxl {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch; } }
.form-label {
  margin-bottom: 0.5rem; }

.col-form-label {
  padding-top: calc(0.375rem + 1px);
  padding-bottom: calc(0.375rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.5; }

.col-form-label-lg {
  padding-top: calc(0.5rem + 1px);
  padding-bottom: calc(0.5rem + 1px);
  font-size: 1.25rem; }

.col-form-label-sm {
  padding-top: calc(0.25rem + 1px);
  padding-bottom: calc(0.25rem + 1px);
  font-size: 0.875rem; }

.form-text {
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #6c757d; }

.form-control {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #181717;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  appearance: none;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; }
  @media (prefers-reduced-motion: reduce) {
    .form-control {
      transition: none; } }
  .form-control[type="file"] {
    overflow: hidden; }
    .form-control[type="file"]:not(:disabled):not([readonly]) {
      cursor: pointer; }
  .form-control:focus {
    color: #181717;
    background-color: #fff;
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25); }
  .form-control::-webkit-date-and-time-value {
    height: 1.5em; }
  .form-control::placeholder {
    color: #6c757d;
    opacity: 1; }
  .form-control:disabled {
    background-color: #e9ecef;
    opacity: 1; }
  .form-control::file-selector-button {
    padding: 0.375rem 0.75rem;
    margin: -0.375rem -0.75rem;
    margin-inline-end: 0.75rem;
    color: #181717;
    background-color: #e9ecef;
    pointer-events: none;
    border-color: inherit;
    border-style: solid;
    border-width: 0;
    border-inline-end-width: 1px;
    border-radius: 0;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; }
    @media (prefers-reduced-motion: reduce) {
      .form-control::file-selector-button {
        transition: none; } }
  .form-control:hover:not(:disabled):not([readonly])::file-selector-button {
    background-color: #dde0e3; }

.form-control-plaintext {
  display: block;
  width: 100%;
  padding: 0.375rem 0;
  margin-bottom: 0;
  line-height: 1.5;
  color: #212529;
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0; }
  .form-control-plaintext:focus {
    outline: 0; }
  .form-control-plaintext.form-control-sm, .form-control-plaintext.form-control-lg {
    padding-right: 0;
    padding-left: 0; }

.form-control-sm {
  min-height: calc(1.5em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.25rem; }
  .form-control-sm::file-selector-button {
    padding: 0.25rem 0.5rem;
    margin: -0.25rem -0.5rem;
    margin-inline-end: 0.5rem; }

.form-control-lg {
  min-height: calc(1.5em + 1rem + 2px);
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  border-radius: 0.5rem; }
  .form-control-lg::file-selector-button {
    padding: 0.5rem 1rem;
    margin: -0.5rem -1rem;
    margin-inline-end: 1rem; }

textarea.form-control {
  min-height: calc(1.5em + 0.75rem + 2px); }
textarea.form-control-sm {
  min-height: calc(1.5em + 0.5rem + 2px); }
textarea.form-control-lg {
  min-height: calc(1.5em + 1rem + 2px); }

.form-control-color {
  width: 3rem;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem; }
  .form-control-color:not(:disabled):not([readonly]) {
    cursor: pointer; }
  .form-control-color::-moz-color-swatch {
    border: 0 !important;
    border-radius: 0.375rem; }
  .form-control-color::-webkit-color-swatch {
    border-radius: 0.375rem; }
  .form-control-color.form-control-sm {
    height: calc(1.5em + 0.5rem + 2px); }
  .form-control-color.form-control-lg {
    height: calc(1.5em + 1rem + 2px); }

.form-select {
  display: block;
  width: 100%;
  padding: 0.375rem 2.25rem 0.375rem 0.75rem;
  -moz-padding-start: calc(0.75rem - 3px);
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #181717;
  background-color: #fff;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  appearance: none; }
  @media (prefers-reduced-motion: reduce) {
    .form-select {
      transition: none; } }
  .form-select:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25); }
  .form-select[multiple], .form-select[size]:not([size="1"]) {
    padding-right: 0.75rem;
    background-image: none; }
  .form-select:disabled {
    background-color: #e9ecef; }
  .form-select:-moz-focusring {
    color: transparent;
    text-shadow: 0 0 0 #181717; }

.form-select-sm {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.25rem; }

.form-select-lg {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  font-size: 1.25rem;
  border-radius: 0.5rem; }

.form-check {
  display: block;
  min-height: 1.5rem;
  padding-left: 1.5em;
  margin-bottom: 0.125rem; }
  .form-check .form-check-input {
    float: left;
    margin-left: -1.5em; }

.form-check-reverse {
  padding-right: 1.5em;
  padding-left: 0;
  text-align: right; }
  .form-check-reverse .form-check-input {
    float: right;
    margin-right: -1.5em;
    margin-left: 0; }

.form-check-input {
  width: 1em;
  height: 1em;
  margin-top: 0.25em;
  vertical-align: top;
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: 1px solid rgba(0, 0, 0, 0.25);
  appearance: none;
  print-color-adjust: exact; }
  .form-check-input[type="checkbox"] {
    border-radius: 0.25em; }
  .form-check-input[type="radio"] {
    border-radius: 50%; }
  .form-check-input:active {
    filter: brightness(90%); }
  .form-check-input:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25); }
  .form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd; }
    .form-check-input:checked[type="checkbox"] {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e"); }
    .form-check-input:checked[type="radio"] {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e"); }
  .form-check-input[type="checkbox"]:indeterminate {
    background-color: #0d6efd;
    border-color: #0d6efd;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e"); }
  .form-check-input:disabled {
    pointer-events: none;
    filter: none;
    opacity: 0.5; }
  .form-check-input[disabled] ~ .form-check-label, .form-check-input:disabled ~ .form-check-label {
    cursor: default;
    opacity: 0.5; }

.form-switch {
  padding-left: 2.5em; }
  .form-switch .form-check-input {
    width: 2em;
    margin-left: -2.5em;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
    background-position: left center;
    border-radius: 2em;
    transition: background-position 0.15s ease-in-out; }
    @media (prefers-reduced-motion: reduce) {
      .form-switch .form-check-input {
        transition: none; } }
    .form-switch .form-check-input:focus {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%2386b7fe'/%3e%3c/svg%3e"); }
    .form-switch .form-check-input:checked {
      background-position: right center;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e"); }
  .form-switch.form-check-reverse {
    padding-right: 2.5em;
    padding-left: 0; }
    .form-switch.form-check-reverse .form-check-input {
      margin-right: -2.5em;
      margin-left: 0; }

.form-check-inline {
  display: inline-block;
  margin-right: 1rem; }

.btn-check {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none; }
  .btn-check[disabled] + .btn, .btn-check:disabled + .btn {
    pointer-events: none;
    filter: none;
    opacity: 0.65; }

.form-range {
  width: 100%;
  height: 1.5rem;
  padding: 0;
  background-color: transparent;
  appearance: none; }
  .form-range:focus {
    outline: 0; }
    .form-range:focus::-webkit-slider-thumb {
      box-shadow: 0 0 0 1px #181717, 0 0 0 0.25rem rgba(13, 110, 253, 0.25); }
    .form-range:focus::-moz-range-thumb {
      box-shadow: 0 0 0 1px #181717, 0 0 0 0.25rem rgba(13, 110, 253, 0.25); }
  .form-range::-moz-focus-outer {
    border: 0; }
  .form-range::-webkit-slider-thumb {
    width: 1rem;
    height: 1rem;
    margin-top: -0.25rem;
    background-color: #0d6efd;
    border: 0;
    border-radius: 1rem;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    appearance: none; }
    @media (prefers-reduced-motion: reduce) {
      .form-range::-webkit-slider-thumb {
        transition: none; } }
    .form-range::-webkit-slider-thumb:active {
      background-color: #b6d4fe; }
  .form-range::-webkit-slider-runnable-track {
    width: 100%;
    height: 0.5rem;
    color: transparent;
    cursor: pointer;
    background-color: #dee2e6;
    border-color: transparent;
    border-radius: 1rem; }
  .form-range::-moz-range-thumb {
    width: 1rem;
    height: 1rem;
    background-color: #0d6efd;
    border: 0;
    border-radius: 1rem;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    appearance: none; }
    @media (prefers-reduced-motion: reduce) {
      .form-range::-moz-range-thumb {
        transition: none; } }
    .form-range::-moz-range-thumb:active {
      background-color: #b6d4fe; }
  .form-range::-moz-range-track {
    width: 100%;
    height: 0.5rem;
    color: transparent;
    cursor: pointer;
    background-color: #dee2e6;
    border-color: transparent;
    border-radius: 1rem; }
  .form-range:disabled {
    pointer-events: none; }
    .form-range:disabled::-webkit-slider-thumb {
      background-color: #adb5bd; }
    .form-range:disabled::-moz-range-thumb {
      background-color: #adb5bd; }

.form-floating {
  position: relative; }
  .form-floating > .form-control,
  .form-floating > .form-control-plaintext,
  .form-floating > .form-select {
    height: calc(3.5rem + 2px);
    line-height: 1.25; }
  .form-floating > label {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 1rem 0.75rem;
    overflow: hidden;
    text-align: start;
    text-overflow: ellipsis;
    white-space: nowrap;
    pointer-events: none;
    border: 1px solid transparent;
    transform-origin: 0 0;
    transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out; }
    @media (prefers-reduced-motion: reduce) {
      .form-floating > label {
        transition: none; } }
  .form-floating > .form-control,
  .form-floating > .form-control-plaintext {
    padding: 1rem 0.75rem; }
    .form-floating > .form-control::placeholder,
    .form-floating > .form-control-plaintext::placeholder {
      color: transparent; }
    .form-floating > .form-control:focus, .form-floating > .form-control:not(:placeholder-shown),
    .form-floating > .form-control-plaintext:focus,
    .form-floating > .form-control-plaintext:not(:placeholder-shown) {
      padding-top: 1.625rem;
      padding-bottom: 0.625rem; }
    .form-floating > .form-control:-webkit-autofill,
    .form-floating > .form-control-plaintext:-webkit-autofill {
      padding-top: 1.625rem;
      padding-bottom: 0.625rem; }
  .form-floating > .form-select {
    padding-top: 1.625rem;
    padding-bottom: 0.625rem; }
  .form-floating > .form-control:focus ~ label,
  .form-floating > .form-control:not(:placeholder-shown) ~ label,
  .form-floating > .form-control-plaintext ~ label,
  .form-floating > .form-select ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem); }
  .form-floating > .form-control:-webkit-autofill ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem); }
  .form-floating > .form-control-plaintext ~ label {
    border-width: 1px 0; }

.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%; }
  .input-group > .form-control,
  .input-group > .form-select,
  .input-group > .form-floating {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0; }
  .input-group > .form-control:focus,
  .input-group > .form-select:focus,
  .input-group > .form-floating:focus-within {
    z-index: 5; }
  .input-group .btn {
    position: relative;
    z-index: 2; }
    .input-group .btn:focus {
      z-index: 5; }

.input-group-text {
  display: flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #181717;
  text-align: center;
  white-space: nowrap;
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 0.375rem; }

.input-group-lg > .form-control,
.input-group-lg > .form-select,
.input-group-lg > .input-group-text,
.input-group-lg > .btn {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  border-radius: 0.5rem; }

.input-group-sm > .form-control,
.input-group-sm > .form-select,
.input-group-sm > .input-group-text,
.input-group-sm > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.25rem; }

.input-group-lg > .form-select,
.input-group-sm > .form-select {
  padding-right: 3rem; }

.input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),
.input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n + 3),
.input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-control,
.input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-select {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0; }
.input-group.has-validation > :nth-last-child(n + 3):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),
.input-group.has-validation > .dropdown-toggle:nth-last-child(n + 4),
.input-group.has-validation > .form-floating:nth-last-child(n + 3) > .form-control,
.input-group.has-validation > .form-floating:nth-last-child(n + 3) > .form-select {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0; }
.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  margin-left: -1px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0; }
.input-group > .form-floating:not(:first-child) > .form-control,
.input-group > .form-floating:not(:first-child) > .form-select {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0; }

.valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #198754; }

.valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: .1rem;
  font-size: 0.875rem;
  color: #fff;
  background-color: rgba(25, 135, 84, 0.9);
  border-radius: 0.375rem; }

.was-validated :valid ~ .valid-feedback,
.was-validated :valid ~ .valid-tooltip,
.is-valid ~ .valid-feedback,
.is-valid ~ .valid-tooltip {
  display: block; }

.was-validated .form-control:valid, .form-control.is-valid {
  border-color: #198754;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem); }
  .was-validated .form-control:valid:focus, .form-control.is-valid:focus {
    border-color: #198754;
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25); }

.was-validated textarea.form-control:valid, textarea.form-control.is-valid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem); }

.was-validated .form-select:valid, .form-select.is-valid {
  border-color: #198754; }
  .was-validated .form-select:valid:not([multiple]):not([size]), .was-validated .form-select:valid:not([multiple])[size="1"], .form-select.is-valid:not([multiple]):not([size]), .form-select.is-valid:not([multiple])[size="1"] {
    padding-right: 4.125rem;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
    background-position: right 0.75rem center, center right 2.25rem;
    background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem); }
  .was-validated .form-select:valid:focus, .form-select.is-valid:focus {
    border-color: #198754;
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25); }

.was-validated .form-control-color:valid, .form-control-color.is-valid {
  width: calc(3rem + calc(1.5em + 0.75rem)); }

.was-validated .form-check-input:valid, .form-check-input.is-valid {
  border-color: #198754; }
  .was-validated .form-check-input:valid:checked, .form-check-input.is-valid:checked {
    background-color: #198754; }
  .was-validated .form-check-input:valid:focus, .form-check-input.is-valid:focus {
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25); }
  .was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label {
    color: #198754; }

.form-check-inline .form-check-input ~ .valid-feedback {
  margin-left: .5em; }

.was-validated .input-group > .form-control:not(:focus):valid, .input-group > .form-control:not(:focus).is-valid,
.was-validated .input-group > .form-select:not(:focus):valid,
.input-group > .form-select:not(:focus).is-valid,
.was-validated .input-group > .form-floating:not(:focus-within):valid,
.input-group > .form-floating:not(:focus-within).is-valid {
  z-index: 3; }

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #ff4136; }

.invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: .1rem;
  font-size: 0.875rem;
  color: #000;
  background-color: rgba(255, 65, 54, 0.9);
  border-radius: 0.375rem; }

.was-validated :invalid ~ .invalid-feedback,
.was-validated :invalid ~ .invalid-tooltip,
.is-invalid ~ .invalid-feedback,
.is-invalid ~ .invalid-tooltip {
  display: block; }

.was-validated .form-control:invalid, .form-control.is-invalid {
  border-color: #ff4136;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23ff4136'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23ff4136' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem); }
  .was-validated .form-control:invalid:focus, .form-control.is-invalid:focus {
    border-color: #ff4136;
    box-shadow: 0 0 0 0.25rem rgba(255, 65, 54, 0.25); }

.was-validated textarea.form-control:invalid, textarea.form-control.is-invalid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem); }

.was-validated .form-select:invalid, .form-select.is-invalid {
  border-color: #ff4136; }
  .was-validated .form-select:invalid:not([multiple]):not([size]), .was-validated .form-select:invalid:not([multiple])[size="1"], .form-select.is-invalid:not([multiple]):not([size]), .form-select.is-invalid:not([multiple])[size="1"] {
    padding-right: 4.125rem;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23ff4136'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23ff4136' stroke='none'/%3e%3c/svg%3e");
    background-position: right 0.75rem center, center right 2.25rem;
    background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem); }
  .was-validated .form-select:invalid:focus, .form-select.is-invalid:focus {
    border-color: #ff4136;
    box-shadow: 0 0 0 0.25rem rgba(255, 65, 54, 0.25); }

.was-validated .form-control-color:invalid, .form-control-color.is-invalid {
  width: calc(3rem + calc(1.5em + 0.75rem)); }

.was-validated .form-check-input:invalid, .form-check-input.is-invalid {
  border-color: #ff4136; }
  .was-validated .form-check-input:invalid:checked, .form-check-input.is-invalid:checked {
    background-color: #ff4136; }
  .was-validated .form-check-input:invalid:focus, .form-check-input.is-invalid:focus {
    box-shadow: 0 0 0 0.25rem rgba(255, 65, 54, 0.25); }
  .was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label {
    color: #ff4136; }

.form-check-inline .form-check-input ~ .invalid-feedback {
  margin-left: .5em; }

.was-validated .input-group > .form-control:not(:focus):invalid, .input-group > .form-control:not(:focus).is-invalid,
.was-validated .input-group > .form-select:not(:focus):invalid,
.input-group > .form-select:not(:focus).is-invalid,
.was-validated .input-group > .form-floating:not(:focus-within):invalid,
.input-group > .form-floating:not(:focus-within).is-invalid {
  z-index: 4; }

.btn {
  --bs-btn-padding-x: 0.75rem;
  --bs-btn-padding-y: 0.375rem;
  --bs-btn-font-family: ;
  --bs-btn-font-size: 1rem;
  --bs-btn-font-weight: 400;
  --bs-btn-line-height: 1.5;
  --bs-btn-color: #212529;
  --bs-btn-bg: transparent;
  --bs-btn-border-width: 1px;
  --bs-btn-border-color: transparent;
  --bs-btn-border-radius: 0.375rem;
  --bs-btn-hover-border-color: transparent;
  --bs-btn-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  --bs-btn-disabled-opacity: 0.65;
  --bs-btn-focus-box-shadow: 0 0 0 0.25rem rgba(var(--bs-btn-focus-shadow-rgb), .5);
  display: inline-block;
  padding: var(--bs-btn-padding-y) var(--bs-btn-padding-x);
  font-family: var(--bs-btn-font-family);
  font-size: var(--bs-btn-font-size);
  font-weight: var(--bs-btn-font-weight);
  line-height: var(--bs-btn-line-height);
  color: var(--bs-btn-color);
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: var(--bs-btn-border-width) solid var(--bs-btn-border-color);
  border-radius: var(--bs-btn-border-radius);
  background-color: var(--bs-btn-bg);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; }
  @media (prefers-reduced-motion: reduce) {
    .btn {
      transition: none; } }
  :not(.btn-check) + .btn:hover, .btn:first-child:hover {
    color: var(--bs-btn-hover-color);
    background-color: var(--bs-btn-hover-bg);
    border-color: var(--bs-btn-hover-border-color); }
  .btn:focus-visible {
    color: var(--bs-btn-hover-color);
    background-color: var(--bs-btn-hover-bg);
    border-color: var(--bs-btn-hover-border-color);
    outline: 0;
    box-shadow: var(--bs-btn-focus-box-shadow); }
  .btn-check:focus-visible + .btn {
    border-color: var(--bs-btn-hover-border-color);
    outline: 0;
    box-shadow: var(--bs-btn-focus-box-shadow); }
  .btn-check:checked + .btn, :not(.btn-check) + .btn:active, .btn:first-child:active, .btn.active, .btn.show {
    color: var(--bs-btn-active-color);
    background-color: var(--bs-btn-active-bg);
    border-color: var(--bs-btn-active-border-color); }
    .btn-check:checked + .btn:focus-visible, :not(.btn-check) + .btn:active:focus-visible, .btn:first-child:active:focus-visible, .btn.active:focus-visible, .btn.show:focus-visible {
      box-shadow: var(--bs-btn-focus-box-shadow); }
  .btn:disabled, .btn.disabled, fieldset:disabled .btn {
    color: var(--bs-btn-disabled-color);
    pointer-events: none;
    background-color: var(--bs-btn-disabled-bg);
    border-color: var(--bs-btn-disabled-border-color);
    opacity: var(--bs-btn-disabled-opacity); }

.btn-primary {
  --bs-btn-color: #000;
  --bs-btn-bg: #8dd363;
  --bs-btn-border-color: #8dd363;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #9eda7a;
  --bs-btn-hover-border-color: #98d773;
  --bs-btn-focus-shadow-rgb: 120, 179, 84;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #a4dc82;
  --bs-btn-active-border-color: #98d773;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #8dd363;
  --bs-btn-disabled-border-color: #8dd363; }

.btn-danger {
  --bs-btn-color: #000;
  --bs-btn-bg: #ff4136;
  --bs-btn-border-color: #ff4136;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #ff5e54;
  --bs-btn-hover-border-color: #ff544a;
  --bs-btn-focus-shadow-rgb: 217, 55, 46;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #ff675e;
  --bs-btn-active-border-color: #ff544a;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #ff4136;
  --bs-btn-disabled-border-color: #ff4136; }

.btn-outline-primary {
  --bs-btn-color: #8dd363;
  --bs-btn-border-color: #8dd363;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #8dd363;
  --bs-btn-hover-border-color: #8dd363;
  --bs-btn-focus-shadow-rgb: 141, 211, 99;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #8dd363;
  --bs-btn-active-border-color: #8dd363;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #8dd363;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #8dd363;
  --bs-gradient: none; }

.btn-outline-danger {
  --bs-btn-color: #ff4136;
  --bs-btn-border-color: #ff4136;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #ff4136;
  --bs-btn-hover-border-color: #ff4136;
  --bs-btn-focus-shadow-rgb: 255, 65, 54;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #ff4136;
  --bs-btn-active-border-color: #ff4136;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ff4136;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #ff4136;
  --bs-gradient: none; }

.btn-link {
  --bs-btn-font-weight: 400;
  --bs-btn-color: #8dd363;
  --bs-btn-bg: transparent;
  --bs-btn-border-color: transparent;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-border-color: transparent;
  --bs-btn-active-color: #fff;
  --bs-btn-active-border-color: transparent;
  --bs-btn-disabled-color: #6c757d;
  --bs-btn-disabled-border-color: transparent;
  --bs-btn-box-shadow: none;
  --bs-btn-focus-shadow-rgb: 49, 132, 253;
  text-decoration: none; }
  .btn-link:hover, .btn-link:focus-visible {
    text-decoration: none; }
  .btn-link:focus-visible {
    color: var(--bs-btn-color); }
  .btn-link:hover {
    color: var(--bs-btn-hover-color); }

.btn-lg, .btn-group-lg > .btn {
  --bs-btn-padding-y: 0.5rem;
  --bs-btn-padding-x: 1rem;
  --bs-btn-font-size: 1.25rem;
  --bs-btn-border-radius: 0.5rem; }

.btn-sm, .btn-group-sm > .btn {
  --bs-btn-padding-y: 0.25rem;
  --bs-btn-padding-x: 0.5rem;
  --bs-btn-font-size: 0.875rem;
  --bs-btn-border-radius: 0.25rem; }

.fade {
  transition: opacity 0.15s linear; }
  @media (prefers-reduced-motion: reduce) {
    .fade {
      transition: none; } }
  .fade:not(.show) {
    opacity: 0; }

.collapse:not(.show) {
  display: none; }

.collapsing {
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease; }
  @media (prefers-reduced-motion: reduce) {
    .collapsing {
      transition: none; } }
  .collapsing.collapse-horizontal {
    width: 0;
    height: auto;
    transition: width 0.35s ease; }
    @media (prefers-reduced-motion: reduce) {
      .collapsing.collapse-horizontal {
        transition: none; } }

.dropup,
.dropend,
.dropdown,
.dropstart,
.dropup-center,
.dropdown-center {
  position: relative; }

.dropdown-toggle {
  white-space: nowrap; }
  .dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-bottom: 0;
    border-left: 0.3em solid transparent; }
  .dropdown-toggle:empty::after {
    margin-left: 0; }

.dropdown-menu {
  --bs-dropdown-zindex: 1000;
  --bs-dropdown-min-width: 10rem;
  --bs-dropdown-padding-x: 0;
  --bs-dropdown-padding-y: 0.5rem;
  --bs-dropdown-spacer: 0.125rem;
  --bs-dropdown-font-size: 1rem;
  --bs-dropdown-color: #212529;
  --bs-dropdown-bg: #fff;
  --bs-dropdown-border-color: var(--bs-border-color-translucent);
  --bs-dropdown-border-radius: 0.375rem;
  --bs-dropdown-border-width: 1px;
  --bs-dropdown-inner-border-radius: calc(0.375rem - 1px);
  --bs-dropdown-divider-bg: var(--bs-border-color-translucent);
  --bs-dropdown-divider-margin-y: 0.5rem;
  --bs-dropdown-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --bs-dropdown-link-color: #212529;
  --bs-dropdown-link-hover-color: #1e2125;
  --bs-dropdown-link-hover-bg: #e9ecef;
  --bs-dropdown-link-active-color: #fff;
  --bs-dropdown-link-active-bg: #0d6efd;
  --bs-dropdown-link-disabled-color: #adb5bd;
  --bs-dropdown-item-padding-x: 1rem;
  --bs-dropdown-item-padding-y: 0.25rem;
  --bs-dropdown-header-color: #6c757d;
  --bs-dropdown-header-padding-x: 1rem;
  --bs-dropdown-header-padding-y: 0.5rem;
  position: absolute;
  z-index: var(--bs-dropdown-zindex);
  display: none;
  min-width: var(--bs-dropdown-min-width);
  padding: var(--bs-dropdown-padding-y) var(--bs-dropdown-padding-x);
  margin: 0;
  font-size: var(--bs-dropdown-font-size);
  color: var(--bs-dropdown-color);
  text-align: left;
  list-style: none;
  background-color: var(--bs-dropdown-bg);
  background-clip: padding-box;
  border: var(--bs-dropdown-border-width) solid var(--bs-dropdown-border-color);
  border-radius: var(--bs-dropdown-border-radius); }
  .dropdown-menu[data-bs-popper] {
    top: 100%;
    left: 0;
    margin-top: var(--bs-dropdown-spacer); }

.dropdown-menu-start {
  --bs-position: start; }
  .dropdown-menu-start[data-bs-popper] {
    right: auto;
    left: 0; }

.dropdown-menu-end {
  --bs-position: end; }
  .dropdown-menu-end[data-bs-popper] {
    right: 0;
    left: auto; }

@media (min-width: 576px) {
  .dropdown-menu-sm-start {
    --bs-position: start; }
    .dropdown-menu-sm-start[data-bs-popper] {
      right: auto;
      left: 0; }

  .dropdown-menu-sm-end {
    --bs-position: end; }
    .dropdown-menu-sm-end[data-bs-popper] {
      right: 0;
      left: auto; } }
@media (min-width: 768px) {
  .dropdown-menu-md-start {
    --bs-position: start; }
    .dropdown-menu-md-start[data-bs-popper] {
      right: auto;
      left: 0; }

  .dropdown-menu-md-end {
    --bs-position: end; }
    .dropdown-menu-md-end[data-bs-popper] {
      right: 0;
      left: auto; } }
@media (min-width: 992px) {
  .dropdown-menu-lg-start {
    --bs-position: start; }
    .dropdown-menu-lg-start[data-bs-popper] {
      right: auto;
      left: 0; }

  .dropdown-menu-lg-end {
    --bs-position: end; }
    .dropdown-menu-lg-end[data-bs-popper] {
      right: 0;
      left: auto; } }
@media (min-width: 1200px) {
  .dropdown-menu-xl-start {
    --bs-position: start; }
    .dropdown-menu-xl-start[data-bs-popper] {
      right: auto;
      left: 0; }

  .dropdown-menu-xl-end {
    --bs-position: end; }
    .dropdown-menu-xl-end[data-bs-popper] {
      right: 0;
      left: auto; } }
@media (min-width: 1400px) {
  .dropdown-menu-xxl-start {
    --bs-position: start; }
    .dropdown-menu-xxl-start[data-bs-popper] {
      right: auto;
      left: 0; }

  .dropdown-menu-xxl-end {
    --bs-position: end; }
    .dropdown-menu-xxl-end[data-bs-popper] {
      right: 0;
      left: auto; } }
.dropup .dropdown-menu[data-bs-popper] {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: var(--bs-dropdown-spacer); }
.dropup .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0;
  border-right: 0.3em solid transparent;
  border-bottom: 0.3em solid;
  border-left: 0.3em solid transparent; }
.dropup .dropdown-toggle:empty::after {
  margin-left: 0; }

.dropend .dropdown-menu[data-bs-popper] {
  top: 0;
  right: auto;
  left: 100%;
  margin-top: 0;
  margin-left: var(--bs-dropdown-spacer); }
.dropend .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid; }
.dropend .dropdown-toggle:empty::after {
  margin-left: 0; }
.dropend .dropdown-toggle::after {
  vertical-align: 0; }

.dropstart .dropdown-menu[data-bs-popper] {
  top: 0;
  right: 100%;
  left: auto;
  margin-top: 0;
  margin-right: var(--bs-dropdown-spacer); }
.dropstart .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: ""; }
.dropstart .dropdown-toggle::after {
  display: none; }
.dropstart .dropdown-toggle::before {
  display: inline-block;
  margin-right: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0.3em solid;
  border-bottom: 0.3em solid transparent; }
.dropstart .dropdown-toggle:empty::after {
  margin-left: 0; }
.dropstart .dropdown-toggle::before {
  vertical-align: 0; }

.dropdown-divider {
  height: 0;
  margin: var(--bs-dropdown-divider-margin-y) 0;
  overflow: hidden;
  border-top: 1px solid var(--bs-dropdown-divider-bg);
  opacity: 1; }

.dropdown-item {
  display: block;
  width: 100%;
  padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);
  clear: both;
  font-weight: 400;
  color: var(--bs-dropdown-link-color);
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0; }
  .dropdown-item:hover, .dropdown-item:focus {
    color: var(--bs-dropdown-link-hover-color);
    background-color: var(--bs-dropdown-link-hover-bg); }
  .dropdown-item.active, .dropdown-item:active {
    color: var(--bs-dropdown-link-active-color);
    text-decoration: none;
    background-color: var(--bs-dropdown-link-active-bg); }
  .dropdown-item.disabled, .dropdown-item:disabled {
    color: var(--bs-dropdown-link-disabled-color);
    pointer-events: none;
    background-color: transparent; }

.dropdown-menu.show {
  display: block; }

.dropdown-header {
  display: block;
  padding: var(--bs-dropdown-header-padding-y) var(--bs-dropdown-header-padding-x);
  margin-bottom: 0;
  font-size: 0.875rem;
  color: var(--bs-dropdown-header-color);
  white-space: nowrap; }

.dropdown-item-text {
  display: block;
  padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);
  color: var(--bs-dropdown-link-color); }

.dropdown-menu-dark {
  --bs-dropdown-color: #dee2e6;
  --bs-dropdown-bg: #343a40;
  --bs-dropdown-border-color: var(--bs-border-color-translucent);
  --bs-dropdown-box-shadow: ;
  --bs-dropdown-link-color: #dee2e6;
  --bs-dropdown-link-hover-color: #fff;
  --bs-dropdown-divider-bg: var(--bs-border-color-translucent);
  --bs-dropdown-link-hover-bg: rgba(255, 255, 255, 0.15);
  --bs-dropdown-link-active-color: #fff;
  --bs-dropdown-link-active-bg: #0d6efd;
  --bs-dropdown-link-disabled-color: #adb5bd;
  --bs-dropdown-header-color: #adb5bd; }

.btn-group,
.btn-group-vertical {
  position: relative;
  display: inline-flex;
  vertical-align: middle; }
  .btn-group > .btn,
  .btn-group-vertical > .btn {
    position: relative;
    flex: 1 1 auto; }
  .btn-group > .btn-check:checked + .btn,
  .btn-group > .btn-check:focus + .btn,
  .btn-group > .btn:hover,
  .btn-group > .btn:focus,
  .btn-group > .btn:active,
  .btn-group > .btn.active,
  .btn-group-vertical > .btn-check:checked + .btn,
  .btn-group-vertical > .btn-check:focus + .btn,
  .btn-group-vertical > .btn:hover,
  .btn-group-vertical > .btn:focus,
  .btn-group-vertical > .btn:active,
  .btn-group-vertical > .btn.active {
    z-index: 1; }

.btn-toolbar {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start; }
  .btn-toolbar .input-group {
    width: auto; }

.btn-group {
  border-radius: 0.375rem; }
  .btn-group > :not(.btn-check:first-child) + .btn,
  .btn-group > .btn-group:not(:first-child) {
    margin-left: -1px; }
  .btn-group > .btn:not(:last-child):not(.dropdown-toggle),
  .btn-group > .btn.dropdown-toggle-split:first-child,
  .btn-group > .btn-group:not(:last-child) > .btn {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0; }
  .btn-group > .btn:nth-child(n + 3),
  .btn-group > :not(.btn-check) + .btn,
  .btn-group > .btn-group:not(:first-child) > .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0; }

.dropdown-toggle-split {
  padding-right: 0.5625rem;
  padding-left: 0.5625rem; }
  .dropdown-toggle-split::after, .dropup .dropdown-toggle-split::after, .dropend .dropdown-toggle-split::after {
    margin-left: 0; }
  .dropstart .dropdown-toggle-split::before {
    margin-right: 0; }

.btn-sm + .dropdown-toggle-split, .btn-group-sm > .btn + .dropdown-toggle-split {
  padding-right: 0.375rem;
  padding-left: 0.375rem; }

.btn-lg + .dropdown-toggle-split, .btn-group-lg > .btn + .dropdown-toggle-split {
  padding-right: 0.75rem;
  padding-left: 0.75rem; }

.btn-group-vertical {
  flex-direction: column;
  align-items: flex-start;
  justify-content: center; }
  .btn-group-vertical > .btn,
  .btn-group-vertical > .btn-group {
    width: 100%; }
  .btn-group-vertical > .btn:not(:first-child),
  .btn-group-vertical > .btn-group:not(:first-child) {
    margin-top: -1px; }
  .btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle),
  .btn-group-vertical > .btn-group:not(:last-child) > .btn {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0; }
  .btn-group-vertical > .btn ~ .btn,
  .btn-group-vertical > .btn-group:not(:first-child) > .btn {
    border-top-left-radius: 0;
    border-top-right-radius: 0; }

.nav {
  --bs-nav-link-padding-x: 1rem;
  --bs-nav-link-padding-y: 0.5rem;
  --bs-nav-link-font-weight: ;
  --bs-nav-link-color: var(--bs-link-color);
  --bs-nav-link-hover-color: var(--bs-link-hover-color);
  --bs-nav-link-disabled-color: #6c757d;
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none; }

.nav-link {
  display: block;
  padding: var(--bs-nav-link-padding-y) var(--bs-nav-link-padding-x);
  font-size: var(--bs-nav-link-font-size);
  font-weight: var(--bs-nav-link-font-weight);
  color: var(--bs-nav-link-color);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out; }
  @media (prefers-reduced-motion: reduce) {
    .nav-link {
      transition: none; } }
  .nav-link:hover, .nav-link:focus {
    color: var(--bs-nav-link-hover-color); }
  .nav-link.disabled {
    color: var(--bs-nav-link-disabled-color);
    pointer-events: none;
    cursor: default; }

.nav-tabs {
  --bs-nav-tabs-border-width: 1px;
  --bs-nav-tabs-border-color: #dee2e6;
  --bs-nav-tabs-border-radius: 0.375rem;
  --bs-nav-tabs-link-hover-border-color: #e9ecef #e9ecef #dee2e6;
  --bs-nav-tabs-link-active-color: #495057;
  --bs-nav-tabs-link-active-bg: #181717;
  --bs-nav-tabs-link-active-border-color: #dee2e6 #dee2e6 #181717;
  border-bottom: var(--bs-nav-tabs-border-width) solid var(--bs-nav-tabs-border-color); }
  .nav-tabs .nav-link {
    margin-bottom: calc(-1 * var(--bs-nav-tabs-border-width));
    background: none;
    border: var(--bs-nav-tabs-border-width) solid transparent;
    border-top-left-radius: var(--bs-nav-tabs-border-radius);
    border-top-right-radius: var(--bs-nav-tabs-border-radius); }
    .nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
      isolation: isolate;
      border-color: var(--bs-nav-tabs-link-hover-border-color); }
    .nav-tabs .nav-link.disabled, .nav-tabs .nav-link:disabled {
      color: var(--bs-nav-link-disabled-color);
      background-color: transparent;
      border-color: transparent; }
  .nav-tabs .nav-link.active,
  .nav-tabs .nav-item.show .nav-link {
    color: var(--bs-nav-tabs-link-active-color);
    background-color: var(--bs-nav-tabs-link-active-bg);
    border-color: var(--bs-nav-tabs-link-active-border-color); }
  .nav-tabs .dropdown-menu {
    margin-top: calc(-1 * var(--bs-nav-tabs-border-width));
    border-top-left-radius: 0;
    border-top-right-radius: 0; }

.nav-pills {
  --bs-nav-pills-border-radius: 0.375rem;
  --bs-nav-pills-link-active-color: #fff;
  --bs-nav-pills-link-active-bg: #0d6efd; }
  .nav-pills .nav-link {
    background: none;
    border: 0;
    border-radius: var(--bs-nav-pills-border-radius); }
    .nav-pills .nav-link:disabled {
      color: var(--bs-nav-link-disabled-color);
      background-color: transparent;
      border-color: transparent; }
  .nav-pills .nav-link.active,
  .nav-pills .show > .nav-link {
    color: var(--bs-nav-pills-link-active-color);
    background-color: var(--bs-nav-pills-link-active-bg); }

.nav-fill > .nav-link,
.nav-fill .nav-item {
  flex: 1 1 auto;
  text-align: center; }

.nav-justified > .nav-link,
.nav-justified .nav-item {
  flex-basis: 0;
  flex-grow: 1;
  text-align: center; }

.nav-fill .nav-item .nav-link,
.nav-justified .nav-item .nav-link {
  width: 100%; }

.tab-content > .tab-pane {
  display: none; }
.tab-content > .active {
  display: block; }

.navbar {
  --bs-navbar-padding-x: 0;
  --bs-navbar-padding-y: 0.5rem;
  --bs-navbar-color: rgba(0, 0, 0, 0.55);
  --bs-navbar-hover-color: rgba(0, 0, 0, 0.7);
  --bs-navbar-disabled-color: rgba(0, 0, 0, 0.3);
  --bs-navbar-active-color: rgba(0, 0, 0, 0.9);
  --bs-navbar-brand-padding-y: 0.3125rem;
  --bs-navbar-brand-margin-end: 1rem;
  --bs-navbar-brand-font-size: 1.25rem;
  --bs-navbar-brand-color: rgba(0, 0, 0, 0.9);
  --bs-navbar-brand-hover-color: rgba(0, 0, 0, 0.9);
  --bs-navbar-nav-link-padding-x: 0.5rem;
  --bs-navbar-toggler-padding-y: 0.25rem;
  --bs-navbar-toggler-padding-x: 0.75rem;
  --bs-navbar-toggler-font-size: 1.25rem;
  --bs-navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
  --bs-navbar-toggler-border-color: rgba(0, 0, 0, 0.1);
  --bs-navbar-toggler-border-radius: 0.375rem;
  --bs-navbar-toggler-focus-width: 0.25rem;
  --bs-navbar-toggler-transition: box-shadow 0.15s ease-in-out;
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: var(--bs-navbar-padding-y) var(--bs-navbar-padding-x); }
  .navbar > .container,
  .navbar > .container-fluid,
  .navbar > .container-sm,
  .navbar > .container-md,
  .navbar > .container-lg,
  .navbar > .container-xl,
  .navbar > .container-xxl {
    display: flex;
    flex-wrap: inherit;
    align-items: center;
    justify-content: space-between; }

.navbar-brand {
  padding-top: var(--bs-navbar-brand-padding-y);
  padding-bottom: var(--bs-navbar-brand-padding-y);
  margin-right: var(--bs-navbar-brand-margin-end);
  font-size: var(--bs-navbar-brand-font-size);
  color: var(--bs-navbar-brand-color);
  white-space: nowrap; }
  .navbar-brand:hover, .navbar-brand:focus {
    color: var(--bs-navbar-brand-hover-color); }

.navbar-nav {
  --bs-nav-link-padding-x: 0;
  --bs-nav-link-padding-y: 0.5rem;
  --bs-nav-link-font-weight: ;
  --bs-nav-link-color: var(--bs-navbar-color);
  --bs-nav-link-hover-color: var(--bs-navbar-hover-color);
  --bs-nav-link-disabled-color: var(--bs-navbar-disabled-color);
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none; }
  .navbar-nav .show > .nav-link,
  .navbar-nav .nav-link.active {
    color: var(--bs-navbar-active-color); }
  .navbar-nav .dropdown-menu {
    position: static; }

.navbar-text {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  color: var(--bs-navbar-color); }
  .navbar-text a,
  .navbar-text a:hover,
  .navbar-text a:focus {
    color: var(--bs-navbar-active-color); }

.navbar-collapse {
  flex-basis: 100%;
  flex-grow: 1;
  align-items: center; }

.navbar-toggler {
  padding: var(--bs-navbar-toggler-padding-y) var(--bs-navbar-toggler-padding-x);
  font-size: var(--bs-navbar-toggler-font-size);
  line-height: 1;
  color: var(--bs-navbar-color);
  background-color: transparent;
  border: var(--bs-border-width) solid var(--bs-navbar-toggler-border-color);
  border-radius: var(--bs-navbar-toggler-border-radius);
  transition: var(--bs-navbar-toggler-transition); }
  @media (prefers-reduced-motion: reduce) {
    .navbar-toggler {
      transition: none; } }
  .navbar-toggler:hover {
    text-decoration: none; }
  .navbar-toggler:focus {
    text-decoration: none;
    outline: 0;
    box-shadow: 0 0 0 var(--bs-navbar-toggler-focus-width); }

.navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  background-image: var(--bs-navbar-toggler-icon-bg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100%; }

.navbar-nav-scroll {
  max-height: var(--bs-scroll-height, 75vh);
  overflow-y: auto; }

@media (min-width: 576px) {
  .navbar-expand-sm {
    flex-wrap: nowrap;
    justify-content: flex-start; }
    .navbar-expand-sm .navbar-nav {
      flex-direction: row; }
      .navbar-expand-sm .navbar-nav .dropdown-menu {
        position: absolute; }
      .navbar-expand-sm .navbar-nav .nav-link {
        padding-right: var(--bs-navbar-nav-link-padding-x);
        padding-left: var(--bs-navbar-nav-link-padding-x); }
    .navbar-expand-sm .navbar-nav-scroll {
      overflow: visible; }
    .navbar-expand-sm .navbar-collapse {
      display: flex !important;
      flex-basis: auto; }
    .navbar-expand-sm .navbar-toggler {
      display: none; }
    .navbar-expand-sm .offcanvas {
      position: static;
      z-index: auto;
      flex-grow: 1;
      width: auto !important;
      height: auto !important;
      visibility: visible !important;
      background-color: transparent !important;
      border: 0 !important;
      transform: none !important;
      transition: none; }
      .navbar-expand-sm .offcanvas .offcanvas-header {
        display: none; }
      .navbar-expand-sm .offcanvas .offcanvas-body {
        display: flex;
        flex-grow: 0;
        padding: 0;
        overflow-y: visible; } }
@media (min-width: 768px) {
  .navbar-expand-md {
    flex-wrap: nowrap;
    justify-content: flex-start; }
    .navbar-expand-md .navbar-nav {
      flex-direction: row; }
      .navbar-expand-md .navbar-nav .dropdown-menu {
        position: absolute; }
      .navbar-expand-md .navbar-nav .nav-link {
        padding-right: var(--bs-navbar-nav-link-padding-x);
        padding-left: var(--bs-navbar-nav-link-padding-x); }
    .navbar-expand-md .navbar-nav-scroll {
      overflow: visible; }
    .navbar-expand-md .navbar-collapse {
      display: flex !important;
      flex-basis: auto; }
    .navbar-expand-md .navbar-toggler {
      display: none; }
    .navbar-expand-md .offcanvas {
      position: static;
      z-index: auto;
      flex-grow: 1;
      width: auto !important;
      height: auto !important;
      visibility: visible !important;
      background-color: transparent !important;
      border: 0 !important;
      transform: none !important;
      transition: none; }
      .navbar-expand-md .offcanvas .offcanvas-header {
        display: none; }
      .navbar-expand-md .offcanvas .offcanvas-body {
        display: flex;
        flex-grow: 0;
        padding: 0;
        overflow-y: visible; } }
@media (min-width: 992px) {
  .navbar-expand-lg {
    flex-wrap: nowrap;
    justify-content: flex-start; }
    .navbar-expand-lg .navbar-nav {
      flex-direction: row; }
      .navbar-expand-lg .navbar-nav .dropdown-menu {
        position: absolute; }
      .navbar-expand-lg .navbar-nav .nav-link {
        padding-right: var(--bs-navbar-nav-link-padding-x);
        padding-left: var(--bs-navbar-nav-link-padding-x); }
    .navbar-expand-lg .navbar-nav-scroll {
      overflow: visible; }
    .navbar-expand-lg .navbar-collapse {
      display: flex !important;
      flex-basis: auto; }
    .navbar-expand-lg .navbar-toggler {
      display: none; }
    .navbar-expand-lg .offcanvas {
      position: static;
      z-index: auto;
      flex-grow: 1;
      width: auto !important;
      height: auto !important;
      visibility: visible !important;
      background-color: transparent !important;
      border: 0 !important;
      transform: none !important;
      transition: none; }
      .navbar-expand-lg .offcanvas .offcanvas-header {
        display: none; }
      .navbar-expand-lg .offcanvas .offcanvas-body {
        display: flex;
        flex-grow: 0;
        padding: 0;
        overflow-y: visible; } }
@media (min-width: 1200px) {
  .navbar-expand-xl {
    flex-wrap: nowrap;
    justify-content: flex-start; }
    .navbar-expand-xl .navbar-nav {
      flex-direction: row; }
      .navbar-expand-xl .navbar-nav .dropdown-menu {
        position: absolute; }
      .navbar-expand-xl .navbar-nav .nav-link {
        padding-right: var(--bs-navbar-nav-link-padding-x);
        padding-left: var(--bs-navbar-nav-link-padding-x); }
    .navbar-expand-xl .navbar-nav-scroll {
      overflow: visible; }
    .navbar-expand-xl .navbar-collapse {
      display: flex !important;
      flex-basis: auto; }
    .navbar-expand-xl .navbar-toggler {
      display: none; }
    .navbar-expand-xl .offcanvas {
      position: static;
      z-index: auto;
      flex-grow: 1;
      width: auto !important;
      height: auto !important;
      visibility: visible !important;
      background-color: transparent !important;
      border: 0 !important;
      transform: none !important;
      transition: none; }
      .navbar-expand-xl .offcanvas .offcanvas-header {
        display: none; }
      .navbar-expand-xl .offcanvas .offcanvas-body {
        display: flex;
        flex-grow: 0;
        padding: 0;
        overflow-y: visible; } }
@media (min-width: 1400px) {
  .navbar-expand-xxl {
    flex-wrap: nowrap;
    justify-content: flex-start; }
    .navbar-expand-xxl .navbar-nav {
      flex-direction: row; }
      .navbar-expand-xxl .navbar-nav .dropdown-menu {
        position: absolute; }
      .navbar-expand-xxl .navbar-nav .nav-link {
        padding-right: var(--bs-navbar-nav-link-padding-x);
        padding-left: var(--bs-navbar-nav-link-padding-x); }
    .navbar-expand-xxl .navbar-nav-scroll {
      overflow: visible; }
    .navbar-expand-xxl .navbar-collapse {
      display: flex !important;
      flex-basis: auto; }
    .navbar-expand-xxl .navbar-toggler {
      display: none; }
    .navbar-expand-xxl .offcanvas {
      position: static;
      z-index: auto;
      flex-grow: 1;
      width: auto !important;
      height: auto !important;
      visibility: visible !important;
      background-color: transparent !important;
      border: 0 !important;
      transform: none !important;
      transition: none; }
      .navbar-expand-xxl .offcanvas .offcanvas-header {
        display: none; }
      .navbar-expand-xxl .offcanvas .offcanvas-body {
        display: flex;
        flex-grow: 0;
        padding: 0;
        overflow-y: visible; } }
.navbar-expand {
  flex-wrap: nowrap;
  justify-content: flex-start; }
  .navbar-expand .navbar-nav {
    flex-direction: row; }
    .navbar-expand .navbar-nav .dropdown-menu {
      position: absolute; }
    .navbar-expand .navbar-nav .nav-link {
      padding-right: var(--bs-navbar-nav-link-padding-x);
      padding-left: var(--bs-navbar-nav-link-padding-x); }
  .navbar-expand .navbar-nav-scroll {
    overflow: visible; }
  .navbar-expand .navbar-collapse {
    display: flex !important;
    flex-basis: auto; }
  .navbar-expand .navbar-toggler {
    display: none; }
  .navbar-expand .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none; }
    .navbar-expand .offcanvas .offcanvas-header {
      display: none; }
    .navbar-expand .offcanvas .offcanvas-body {
      display: flex;
      flex-grow: 0;
      padding: 0;
      overflow-y: visible; }

.navbar-dark {
  --bs-navbar-color: rgba(255, 255, 255, 0.55);
  --bs-navbar-hover-color: rgba(255, 255, 255, 0.75);
  --bs-navbar-disabled-color: rgba(255, 255, 255, 0.25);
  --bs-navbar-active-color: #fff;
  --bs-navbar-brand-color: #fff;
  --bs-navbar-brand-hover-color: #fff;
  --bs-navbar-toggler-border-color: rgba(255, 255, 255, 0.1);
  --bs-navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e"); }

.card {
  --bs-card-spacer-y: 1rem;
  --bs-card-spacer-x: 1rem;
  --bs-card-title-spacer-y: 0.5rem;
  --bs-card-border-width: 1px;
  --bs-card-border-color: var(--bs-border-color-translucent);
  --bs-card-border-radius: 0.375rem;
  --bs-card-box-shadow: ;
  --bs-card-inner-border-radius: calc(0.375rem - 1px);
  --bs-card-cap-padding-y: 0.5rem;
  --bs-card-cap-padding-x: 1rem;
  --bs-card-cap-bg: rgba(0, 0, 0, 0.03);
  --bs-card-cap-color: ;
  --bs-card-height: ;
  --bs-card-color: ;
  --bs-card-bg: #181717;
  --bs-card-img-overlay-padding: 1rem;
  --bs-card-group-margin: 0.75rem;
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  height: var(--bs-card-height);
  word-wrap: break-word;
  background-color: var(--bs-card-bg);
  background-clip: border-box;
  border: var(--bs-card-border-width) solid var(--bs-card-border-color);
  border-radius: var(--bs-card-border-radius); }
  .card > hr {
    margin-right: 0;
    margin-left: 0; }
  .card > .list-group {
    border-top: inherit;
    border-bottom: inherit; }
    .card > .list-group:first-child {
      border-top-width: 0;
      border-top-left-radius: var(--bs-card-inner-border-radius);
      border-top-right-radius: var(--bs-card-inner-border-radius); }
    .card > .list-group:last-child {
      border-bottom-width: 0;
      border-bottom-right-radius: var(--bs-card-inner-border-radius);
      border-bottom-left-radius: var(--bs-card-inner-border-radius); }
  .card > .card-header + .list-group,
  .card > .list-group + .card-footer {
    border-top: 0; }

.card-body {
  flex: 1 1 auto;
  padding: var(--bs-card-spacer-y) var(--bs-card-spacer-x);
  color: var(--bs-card-color); }

.card-title {
  margin-bottom: var(--bs-card-title-spacer-y); }

.card-subtitle {
  margin-top: calc(-.5 * var(--bs-card-title-spacer-y));
  margin-bottom: 0; }

.card-text:last-child {
  margin-bottom: 0; }

.card-link + .card-link {
  margin-left: var(--bs-card-spacer-x); }

.card-header {
  padding: var(--bs-card-cap-padding-y) var(--bs-card-cap-padding-x);
  margin-bottom: 0;
  color: var(--bs-card-cap-color);
  background-color: var(--bs-card-cap-bg);
  border-bottom: var(--bs-card-border-width) solid var(--bs-card-border-color); }
  .card-header:first-child {
    border-radius: var(--bs-card-inner-border-radius) var(--bs-card-inner-border-radius) 0 0; }

.card-footer {
  padding: var(--bs-card-cap-padding-y) var(--bs-card-cap-padding-x);
  color: var(--bs-card-cap-color);
  background-color: var(--bs-card-cap-bg);
  border-top: var(--bs-card-border-width) solid var(--bs-card-border-color); }
  .card-footer:last-child {
    border-radius: 0 0 var(--bs-card-inner-border-radius) var(--bs-card-inner-border-radius); }

.card-header-tabs {
  margin-right: calc(-.5 * var(--bs-card-cap-padding-x));
  margin-bottom: calc(-1 * var(--bs-card-cap-padding-y));
  margin-left: calc(-.5 * var(--bs-card-cap-padding-x));
  border-bottom: 0; }
  .card-header-tabs .nav-link.active {
    background-color: var(--bs-card-bg);
    border-bottom-color: var(--bs-card-bg); }

.card-header-pills {
  margin-right: calc(-.5 * var(--bs-card-cap-padding-x));
  margin-left: calc(-.5 * var(--bs-card-cap-padding-x)); }

.card-img-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: var(--bs-card-img-overlay-padding);
  border-radius: var(--bs-card-inner-border-radius); }

.card-img,
.card-img-top,
.card-img-bottom {
  width: 100%; }

.card-img,
.card-img-top {
  border-top-left-radius: var(--bs-card-inner-border-radius);
  border-top-right-radius: var(--bs-card-inner-border-radius); }

.card-img,
.card-img-bottom {
  border-bottom-right-radius: var(--bs-card-inner-border-radius);
  border-bottom-left-radius: var(--bs-card-inner-border-radius); }

.card-group > .card {
  margin-bottom: var(--bs-card-group-margin); }
@media (min-width: 576px) {
  .card-group {
    display: flex;
    flex-flow: row wrap; }
    .card-group > .card {
      flex: 1 0 0%;
      margin-bottom: 0; }
      .card-group > .card + .card {
        margin-left: 0;
        border-left: 0; }
      .card-group > .card:not(:last-child) {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0; }
        .card-group > .card:not(:last-child) .card-img-top,
        .card-group > .card:not(:last-child) .card-header {
          border-top-right-radius: 0; }
        .card-group > .card:not(:last-child) .card-img-bottom,
        .card-group > .card:not(:last-child) .card-footer {
          border-bottom-right-radius: 0; }
      .card-group > .card:not(:first-child) {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0; }
        .card-group > .card:not(:first-child) .card-img-top,
        .card-group > .card:not(:first-child) .card-header {
          border-top-left-radius: 0; }
        .card-group > .card:not(:first-child) .card-img-bottom,
        .card-group > .card:not(:first-child) .card-footer {
          border-bottom-left-radius: 0; } }

.accordion {
  --bs-accordion-color: var(--bs-body-color);
  --bs-accordion-bg: #181717;
  --bs-accordion-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease;
  --bs-accordion-border-color: var(--bs-border-color);
  --bs-accordion-border-width: 1px;
  --bs-accordion-border-radius: 0.375rem;
  --bs-accordion-inner-border-radius: calc(0.375rem - 1px);
  --bs-accordion-btn-padding-x: 1.25rem;
  --bs-accordion-btn-padding-y: 1rem;
  --bs-accordion-btn-color: var(--bs-body-color);
  --bs-accordion-btn-bg: var(--bs-accordion-bg);
  --bs-accordion-btn-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='var%28--bs-body-color%29'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  --bs-accordion-btn-icon-width: 1.25rem;
  --bs-accordion-btn-icon-transform: rotate(-180deg);
  --bs-accordion-btn-icon-transition: transform 0.2s ease-in-out;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%230c63e4'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  --bs-accordion-btn-focus-border-color: #86b7fe;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  --bs-accordion-body-padding-x: 1.25rem;
  --bs-accordion-body-padding-y: 1rem;
  --bs-accordion-active-color: #0c63e4;
  --bs-accordion-active-bg: #e7f1ff; }

.accordion-button {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  padding: var(--bs-accordion-btn-padding-y) var(--bs-accordion-btn-padding-x);
  font-size: 1rem;
  color: var(--bs-accordion-btn-color);
  text-align: left;
  background-color: var(--bs-accordion-btn-bg);
  border: 0;
  border-radius: 0;
  overflow-anchor: none;
  transition: var(--bs-accordion-transition); }
  @media (prefers-reduced-motion: reduce) {
    .accordion-button {
      transition: none; } }
  .accordion-button:not(.collapsed) {
    color: var(--bs-accordion-active-color);
    background-color: var(--bs-accordion-active-bg);
    box-shadow: inset 0 calc(-1 * var(--bs-accordion-border-width)) 0 var(--bs-accordion-border-color); }
    .accordion-button:not(.collapsed)::after {
      background-image: var(--bs-accordion-btn-active-icon);
      transform: var(--bs-accordion-btn-icon-transform); }
  .accordion-button::after {
    flex-shrink: 0;
    width: var(--bs-accordion-btn-icon-width);
    height: var(--bs-accordion-btn-icon-width);
    margin-left: auto;
    content: "";
    background-image: var(--bs-accordion-btn-icon);
    background-repeat: no-repeat;
    background-size: var(--bs-accordion-btn-icon-width);
    transition: var(--bs-accordion-btn-icon-transition); }
    @media (prefers-reduced-motion: reduce) {
      .accordion-button::after {
        transition: none; } }
  .accordion-button:hover {
    z-index: 2; }
  .accordion-button:focus {
    z-index: 3;
    border-color: var(--bs-accordion-btn-focus-border-color);
    outline: 0;
    box-shadow: var(--bs-accordion-btn-focus-box-shadow); }

.accordion-header {
  margin-bottom: 0; }

.accordion-item {
  color: var(--bs-accordion-color);
  background-color: var(--bs-accordion-bg);
  border: var(--bs-accordion-border-width) solid var(--bs-accordion-border-color); }
  .accordion-item:first-of-type {
    border-top-left-radius: var(--bs-accordion-border-radius);
    border-top-right-radius: var(--bs-accordion-border-radius); }
    .accordion-item:first-of-type .accordion-button {
      border-top-left-radius: var(--bs-accordion-inner-border-radius);
      border-top-right-radius: var(--bs-accordion-inner-border-radius); }
  .accordion-item:not(:first-of-type) {
    border-top: 0; }
  .accordion-item:last-of-type {
    border-bottom-right-radius: var(--bs-accordion-border-radius);
    border-bottom-left-radius: var(--bs-accordion-border-radius); }
    .accordion-item:last-of-type .accordion-button.collapsed {
      border-bottom-right-radius: var(--bs-accordion-inner-border-radius);
      border-bottom-left-radius: var(--bs-accordion-inner-border-radius); }
    .accordion-item:last-of-type .accordion-collapse {
      border-bottom-right-radius: var(--bs-accordion-border-radius);
      border-bottom-left-radius: var(--bs-accordion-border-radius); }

.accordion-body {
  padding: var(--bs-accordion-body-padding-y) var(--bs-accordion-body-padding-x); }

.accordion-flush .accordion-collapse {
  border-width: 0; }
.accordion-flush .accordion-item {
  border-right: 0;
  border-left: 0;
  border-radius: 0; }
  .accordion-flush .accordion-item:first-child {
    border-top: 0; }
  .accordion-flush .accordion-item:last-child {
    border-bottom: 0; }
  .accordion-flush .accordion-item .accordion-button, .accordion-flush .accordion-item .accordion-button.collapsed {
    border-radius: 0; }

.breadcrumb {
  --bs-breadcrumb-padding-x: 0;
  --bs-breadcrumb-padding-y: 0;
  --bs-breadcrumb-margin-bottom: 1rem;
  --bs-breadcrumb-bg: ;
  --bs-breadcrumb-border-radius: ;
  --bs-breadcrumb-divider-color: #6c757d;
  --bs-breadcrumb-item-padding-x: 0.5rem;
  --bs-breadcrumb-item-active-color: #6c757d;
  display: flex;
  flex-wrap: wrap;
  padding: var(--bs-breadcrumb-padding-y) var(--bs-breadcrumb-padding-x);
  margin-bottom: var(--bs-breadcrumb-margin-bottom);
  font-size: var(--bs-breadcrumb-font-size);
  list-style: none;
  background-color: var(--bs-breadcrumb-bg);
  border-radius: var(--bs-breadcrumb-border-radius); }

.breadcrumb-item + .breadcrumb-item {
  padding-left: var(--bs-breadcrumb-item-padding-x); }
  .breadcrumb-item + .breadcrumb-item::before {
    float: left;
    padding-right: var(--bs-breadcrumb-item-padding-x);
    color: var(--bs-breadcrumb-divider-color);
    content: var(--bs-breadcrumb-divider, "/") /* rtl: var(--bs-breadcrumb-divider, "/") */; }
.breadcrumb-item.active {
  color: var(--bs-breadcrumb-item-active-color); }

.pagination {
  --bs-pagination-padding-x: 0.75rem;
  --bs-pagination-padding-y: 0.375rem;
  --bs-pagination-font-size: 1rem;
  --bs-pagination-color: var(--bs-link-color);
  --bs-pagination-bg: #fff;
  --bs-pagination-border-width: 1px;
  --bs-pagination-border-color: #dee2e6;
  --bs-pagination-border-radius: 0.375rem;
  --bs-pagination-hover-color: var(--bs-link-hover-color);
  --bs-pagination-hover-bg: #e9ecef;
  --bs-pagination-hover-border-color: #dee2e6;
  --bs-pagination-focus-color: var(--bs-link-hover-color);
  --bs-pagination-focus-bg: #e9ecef;
  --bs-pagination-focus-box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  --bs-pagination-active-color: #fff;
  --bs-pagination-active-bg: #0d6efd;
  --bs-pagination-active-border-color: #0d6efd;
  --bs-pagination-disabled-color: #6c757d;
  --bs-pagination-disabled-bg: #fff;
  --bs-pagination-disabled-border-color: #dee2e6;
  display: flex;
  padding-left: 0;
  list-style: none; }

.page-link {
  position: relative;
  display: block;
  padding: var(--bs-pagination-padding-y) var(--bs-pagination-padding-x);
  font-size: var(--bs-pagination-font-size);
  color: var(--bs-pagination-color);
  background-color: var(--bs-pagination-bg);
  border: var(--bs-pagination-border-width) solid var(--bs-pagination-border-color);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; }
  @media (prefers-reduced-motion: reduce) {
    .page-link {
      transition: none; } }
  .page-link:hover {
    z-index: 2;
    color: var(--bs-pagination-hover-color);
    background-color: var(--bs-pagination-hover-bg);
    border-color: var(--bs-pagination-hover-border-color); }
  .page-link:focus {
    z-index: 3;
    color: var(--bs-pagination-focus-color);
    background-color: var(--bs-pagination-focus-bg);
    outline: 0;
    box-shadow: var(--bs-pagination-focus-box-shadow); }
  .page-link.active, .active > .page-link {
    z-index: 3;
    color: var(--bs-pagination-active-color);
    background-color: var(--bs-pagination-active-bg);
    border-color: var(--bs-pagination-active-border-color); }
  .page-link.disabled, .disabled > .page-link {
    color: var(--bs-pagination-disabled-color);
    pointer-events: none;
    background-color: var(--bs-pagination-disabled-bg);
    border-color: var(--bs-pagination-disabled-border-color); }

.page-item:not(:first-child) .page-link {
  margin-left: -1px; }
.page-item:first-child .page-link {
  border-top-left-radius: var(--bs-pagination-border-radius);
  border-bottom-left-radius: var(--bs-pagination-border-radius); }
.page-item:last-child .page-link {
  border-top-right-radius: var(--bs-pagination-border-radius);
  border-bottom-right-radius: var(--bs-pagination-border-radius); }

.pagination-lg {
  --bs-pagination-padding-x: 1.5rem;
  --bs-pagination-padding-y: 0.75rem;
  --bs-pagination-font-size: 1.25rem;
  --bs-pagination-border-radius: 0.5rem; }

.pagination-sm {
  --bs-pagination-padding-x: 0.5rem;
  --bs-pagination-padding-y: 0.25rem;
  --bs-pagination-font-size: 0.875rem;
  --bs-pagination-border-radius: 0.25rem; }

.badge {
  --bs-badge-padding-x: 0.65em;
  --bs-badge-padding-y: 0.35em;
  --bs-badge-font-size: 0.75em;
  --bs-badge-font-weight: 700;
  --bs-badge-color: #fff;
  --bs-badge-border-radius: 0.375rem;
  display: inline-block;
  padding: var(--bs-badge-padding-y) var(--bs-badge-padding-x);
  font-size: var(--bs-badge-font-size);
  font-weight: var(--bs-badge-font-weight);
  line-height: 1;
  color: var(--bs-badge-color);
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: var(--bs-badge-border-radius); }
  .badge:empty {
    display: none; }

.btn .badge {
  position: relative;
  top: -1px; }

.alert {
  --bs-alert-bg: transparent;
  --bs-alert-padding-x: 1rem;
  --bs-alert-padding-y: 1rem;
  --bs-alert-margin-bottom: 1rem;
  --bs-alert-color: inherit;
  --bs-alert-border-color: transparent;
  --bs-alert-border: 1px solid var(--bs-alert-border-color);
  --bs-alert-border-radius: 0.375rem;
  position: relative;
  padding: var(--bs-alert-padding-y) var(--bs-alert-padding-x);
  margin-bottom: var(--bs-alert-margin-bottom);
  color: var(--bs-alert-color);
  background-color: var(--bs-alert-bg);
  border: var(--bs-alert-border);
  border-radius: var(--bs-alert-border-radius); }

.alert-heading {
  color: inherit; }

.alert-link {
  font-weight: 700; }

.alert-dismissible {
  padding-right: 3rem; }
  .alert-dismissible .btn-close {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    padding: 1.25rem 1rem; }

.alert-primary {
  --bs-alert-color: #385428;
  --bs-alert-bg: #e8f6e0;
  --bs-alert-border-color: #ddf2d0; }
  .alert-primary .alert-link {
    color: #2d4320; }

.alert-danger {
  --bs-alert-color: #992720;
  --bs-alert-bg: #ffd9d7;
  --bs-alert-border-color: #ffc6c3; }
  .alert-danger .alert-link {
    color: #7a1f1a; }

@keyframes progress-bar-stripes {
  0% {
    background-position-x: 1rem; } }
.progress {
  --bs-progress-height: 1rem;
  --bs-progress-font-size: 0.75rem;
  --bs-progress-bg: #e9ecef;
  --bs-progress-border-radius: 0.375rem;
  --bs-progress-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075);
  --bs-progress-bar-color: #fff;
  --bs-progress-bar-bg: #0d6efd;
  --bs-progress-bar-transition: width 0.6s ease;
  display: flex;
  height: var(--bs-progress-height);
  overflow: hidden;
  font-size: var(--bs-progress-font-size);
  background-color: var(--bs-progress-bg);
  border-radius: var(--bs-progress-border-radius); }

.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: var(--bs-progress-bar-color);
  text-align: center;
  white-space: nowrap;
  background-color: var(--bs-progress-bar-bg);
  transition: var(--bs-progress-bar-transition); }
  @media (prefers-reduced-motion: reduce) {
    .progress-bar {
      transition: none; } }

.progress-bar-striped {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: var(--bs-progress-height) var(--bs-progress-height); }

.progress-bar-animated {
  animation: 1s linear infinite progress-bar-stripes; }
  @media (prefers-reduced-motion: reduce) {
    .progress-bar-animated {
      animation: none; } }

.list-group {
  --bs-list-group-color: #212529;
  --bs-list-group-bg: #fff;
  --bs-list-group-border-color: rgba(0, 0, 0, 0.125);
  --bs-list-group-border-width: 1px;
  --bs-list-group-border-radius: 0.375rem;
  --bs-list-group-item-padding-x: 1rem;
  --bs-list-group-item-padding-y: 0.5rem;
  --bs-list-group-action-color: #495057;
  --bs-list-group-action-hover-color: #495057;
  --bs-list-group-action-hover-bg: #f8f9fa;
  --bs-list-group-action-active-color: #212529;
  --bs-list-group-action-active-bg: #e9ecef;
  --bs-list-group-disabled-color: #6c757d;
  --bs-list-group-disabled-bg: #fff;
  --bs-list-group-active-color: #fff;
  --bs-list-group-active-bg: #0d6efd;
  --bs-list-group-active-border-color: #0d6efd;
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  border-radius: var(--bs-list-group-border-radius); }

.list-group-numbered {
  list-style-type: none;
  counter-reset: section; }
  .list-group-numbered > .list-group-item::before {
    content: counters(section,".") ". ";
    counter-increment: section; }

.list-group-item-action {
  width: 100%;
  color: var(--bs-list-group-action-color);
  text-align: inherit; }
  .list-group-item-action:hover, .list-group-item-action:focus {
    z-index: 1;
    color: var(--bs-list-group-action-hover-color);
    text-decoration: none;
    background-color: var(--bs-list-group-action-hover-bg); }
  .list-group-item-action:active {
    color: var(--bs-list-group-action-active-color);
    background-color: var(--bs-list-group-action-active-bg); }

.list-group-item {
  position: relative;
  display: block;
  padding: var(--bs-list-group-item-padding-y) var(--bs-list-group-item-padding-x);
  color: var(--bs-list-group-color);
  background-color: var(--bs-list-group-bg);
  border: var(--bs-list-group-border-width) solid var(--bs-list-group-border-color); }
  .list-group-item:first-child {
    border-top-left-radius: inherit;
    border-top-right-radius: inherit; }
  .list-group-item:last-child {
    border-bottom-right-radius: inherit;
    border-bottom-left-radius: inherit; }
  .list-group-item.disabled, .list-group-item:disabled {
    color: var(--bs-list-group-disabled-color);
    pointer-events: none;
    background-color: var(--bs-list-group-disabled-bg); }
  .list-group-item.active {
    z-index: 2;
    color: var(--bs-list-group-active-color);
    background-color: var(--bs-list-group-active-bg);
    border-color: var(--bs-list-group-active-border-color); }
  .list-group-item + .list-group-item {
    border-top-width: 0; }
    .list-group-item + .list-group-item.active {
      margin-top: calc(-1 * var(--bs-list-group-border-width));
      border-top-width: var(--bs-list-group-border-width); }

.list-group-horizontal {
  flex-direction: row; }
  .list-group-horizontal > .list-group-item:first-child:not(:last-child) {
    border-bottom-left-radius: var(--bs-list-group-border-radius);
    border-top-right-radius: 0; }
  .list-group-horizontal > .list-group-item:last-child:not(:first-child) {
    border-top-right-radius: var(--bs-list-group-border-radius);
    border-bottom-left-radius: 0; }
  .list-group-horizontal > .list-group-item.active {
    margin-top: 0; }
  .list-group-horizontal > .list-group-item + .list-group-item {
    border-top-width: var(--bs-list-group-border-width);
    border-left-width: 0; }
    .list-group-horizontal > .list-group-item + .list-group-item.active {
      margin-left: calc(-1 * var(--bs-list-group-border-width));
      border-left-width: var(--bs-list-group-border-width); }

@media (min-width: 576px) {
  .list-group-horizontal-sm {
    flex-direction: row; }
    .list-group-horizontal-sm > .list-group-item:first-child:not(:last-child) {
      border-bottom-left-radius: var(--bs-list-group-border-radius);
      border-top-right-radius: 0; }
    .list-group-horizontal-sm > .list-group-item:last-child:not(:first-child) {
      border-top-right-radius: var(--bs-list-group-border-radius);
      border-bottom-left-radius: 0; }
    .list-group-horizontal-sm > .list-group-item.active {
      margin-top: 0; }
    .list-group-horizontal-sm > .list-group-item + .list-group-item {
      border-top-width: var(--bs-list-group-border-width);
      border-left-width: 0; }
      .list-group-horizontal-sm > .list-group-item + .list-group-item.active {
        margin-left: calc(-1 * var(--bs-list-group-border-width));
        border-left-width: var(--bs-list-group-border-width); } }
@media (min-width: 768px) {
  .list-group-horizontal-md {
    flex-direction: row; }
    .list-group-horizontal-md > .list-group-item:first-child:not(:last-child) {
      border-bottom-left-radius: var(--bs-list-group-border-radius);
      border-top-right-radius: 0; }
    .list-group-horizontal-md > .list-group-item:last-child:not(:first-child) {
      border-top-right-radius: var(--bs-list-group-border-radius);
      border-bottom-left-radius: 0; }
    .list-group-horizontal-md > .list-group-item.active {
      margin-top: 0; }
    .list-group-horizontal-md > .list-group-item + .list-group-item {
      border-top-width: var(--bs-list-group-border-width);
      border-left-width: 0; }
      .list-group-horizontal-md > .list-group-item + .list-group-item.active {
        margin-left: calc(-1 * var(--bs-list-group-border-width));
        border-left-width: var(--bs-list-group-border-width); } }
@media (min-width: 992px) {
  .list-group-horizontal-lg {
    flex-direction: row; }
    .list-group-horizontal-lg > .list-group-item:first-child:not(:last-child) {
      border-bottom-left-radius: var(--bs-list-group-border-radius);
      border-top-right-radius: 0; }
    .list-group-horizontal-lg > .list-group-item:last-child:not(:first-child) {
      border-top-right-radius: var(--bs-list-group-border-radius);
      border-bottom-left-radius: 0; }
    .list-group-horizontal-lg > .list-group-item.active {
      margin-top: 0; }
    .list-group-horizontal-lg > .list-group-item + .list-group-item {
      border-top-width: var(--bs-list-group-border-width);
      border-left-width: 0; }
      .list-group-horizontal-lg > .list-group-item + .list-group-item.active {
        margin-left: calc(-1 * var(--bs-list-group-border-width));
        border-left-width: var(--bs-list-group-border-width); } }
@media (min-width: 1200px) {
  .list-group-horizontal-xl {
    flex-direction: row; }
    .list-group-horizontal-xl > .list-group-item:first-child:not(:last-child) {
      border-bottom-left-radius: var(--bs-list-group-border-radius);
      border-top-right-radius: 0; }
    .list-group-horizontal-xl > .list-group-item:last-child:not(:first-child) {
      border-top-right-radius: var(--bs-list-group-border-radius);
      border-bottom-left-radius: 0; }
    .list-group-horizontal-xl > .list-group-item.active {
      margin-top: 0; }
    .list-group-horizontal-xl > .list-group-item + .list-group-item {
      border-top-width: var(--bs-list-group-border-width);
      border-left-width: 0; }
      .list-group-horizontal-xl > .list-group-item + .list-group-item.active {
        margin-left: calc(-1 * var(--bs-list-group-border-width));
        border-left-width: var(--bs-list-group-border-width); } }
@media (min-width: 1400px) {
  .list-group-horizontal-xxl {
    flex-direction: row; }
    .list-group-horizontal-xxl > .list-group-item:first-child:not(:last-child) {
      border-bottom-left-radius: var(--bs-list-group-border-radius);
      border-top-right-radius: 0; }
    .list-group-horizontal-xxl > .list-group-item:last-child:not(:first-child) {
      border-top-right-radius: var(--bs-list-group-border-radius);
      border-bottom-left-radius: 0; }
    .list-group-horizontal-xxl > .list-group-item.active {
      margin-top: 0; }
    .list-group-horizontal-xxl > .list-group-item + .list-group-item {
      border-top-width: var(--bs-list-group-border-width);
      border-left-width: 0; }
      .list-group-horizontal-xxl > .list-group-item + .list-group-item.active {
        margin-left: calc(-1 * var(--bs-list-group-border-width));
        border-left-width: var(--bs-list-group-border-width); } }
.list-group-flush {
  border-radius: 0; }
  .list-group-flush > .list-group-item {
    border-width: 0 0 var(--bs-list-group-border-width); }
    .list-group-flush > .list-group-item:last-child {
      border-bottom-width: 0; }

.list-group-item-primary {
  color: #385428;
  background-color: #e8f6e0; }
  .list-group-item-primary.list-group-item-action:hover, .list-group-item-primary.list-group-item-action:focus {
    color: #385428;
    background-color: #d1ddca; }
  .list-group-item-primary.list-group-item-action.active {
    color: #fff;
    background-color: #385428;
    border-color: #385428; }

.list-group-item-danger {
  color: #992720;
  background-color: #ffd9d7; }
  .list-group-item-danger.list-group-item-action:hover, .list-group-item-danger.list-group-item-action:focus {
    color: #992720;
    background-color: #e6c3c2; }
  .list-group-item-danger.list-group-item-action.active {
    color: #fff;
    background-color: #992720;
    border-color: #992720; }

.btn-close {
  box-sizing: content-box;
  width: 1em;
  height: 1em;
  padding: 0.25em 0.25em;
  color: #fff;
  background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
  border: 0;
  border-radius: 0.375rem;
  opacity: 0.5; }
  .btn-close:hover {
    color: #fff;
    text-decoration: none;
    opacity: 0.75; }
  .btn-close:focus {
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    opacity: 1; }
  .btn-close:disabled, .btn-close.disabled {
    pointer-events: none;
    user-select: none;
    opacity: 0.25; }

.btn-close-white {
  filter: invert(1) grayscale(100%) brightness(200%); }

.toast {
  --bs-toast-zindex: 1090;
  --bs-toast-padding-x: 0.75rem;
  --bs-toast-padding-y: 0.5rem;
  --bs-toast-spacing: 1.5rem;
  --bs-toast-max-width: 350px;
  --bs-toast-font-size: 0.875rem;
  --bs-toast-color: ;
  --bs-toast-bg: rgba(255, 255, 255, 0.85);
  --bs-toast-border-width: 1px;
  --bs-toast-border-color: var(--bs-border-color-translucent);
  --bs-toast-border-radius: 0.375rem;
  --bs-toast-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --bs-toast-header-color: #6c757d;
  --bs-toast-header-bg: rgba(255, 255, 255, 0.85);
  --bs-toast-header-border-color: rgba(0, 0, 0, 0.05);
  width: var(--bs-toast-max-width);
  max-width: 100%;
  font-size: var(--bs-toast-font-size);
  color: var(--bs-toast-color);
  pointer-events: auto;
  background-color: var(--bs-toast-bg);
  background-clip: padding-box;
  border: var(--bs-toast-border-width) solid var(--bs-toast-border-color);
  box-shadow: var(--bs-toast-box-shadow);
  border-radius: var(--bs-toast-border-radius); }
  .toast.showing {
    opacity: 0; }
  .toast:not(.show) {
    display: none; }

.toast-container {
  position: absolute;
  z-index: var(--bs-toast-zindex);
  width: max-content;
  max-width: 100%;
  pointer-events: none; }
  .toast-container > :not(:last-child) {
    margin-bottom: var(--bs-toast-spacing); }

.toast-header {
  display: flex;
  align-items: center;
  padding: var(--bs-toast-padding-y) var(--bs-toast-padding-x);
  color: var(--bs-toast-header-color);
  background-color: var(--bs-toast-header-bg);
  background-clip: padding-box;
  border-bottom: var(--bs-toast-border-width) solid var(--bs-toast-header-border-color);
  border-top-left-radius: calc(var(--bs-toast-border-radius) - var(--bs-toast-border-width));
  border-top-right-radius: calc(var(--bs-toast-border-radius) - var(--bs-toast-border-width)); }
  .toast-header .btn-close {
    margin-right: calc(-.5 * var(--bs-toast-padding-x));
    margin-left: var(--bs-toast-padding-x); }

.toast-body {
  padding: var(--bs-toast-padding-x);
  word-wrap: break-word; }

.modal {
  --bs-modal-zindex: 1055;
  --bs-modal-width: 500px;
  --bs-modal-padding: 1rem;
  --bs-modal-margin: 0.5rem;
  --bs-modal-color: ;
  --bs-modal-bg: rgba(24, 23, 23, 0.9);
  --bs-modal-border-color: var(--bs-border-color-translucent);
  --bs-modal-border-width: 1px;
  --bs-modal-border-radius: 0.5rem;
  --bs-modal-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --bs-modal-inner-border-radius: calc(0.5rem - 1px);
  --bs-modal-header-padding-x: 1rem;
  --bs-modal-header-padding-y: 1rem;
  --bs-modal-header-padding: 1rem 1rem;
  --bs-modal-header-border-color: var(--bs-border-color);
  --bs-modal-header-border-width: 1px;
  --bs-modal-title-line-height: 1.5;
  --bs-modal-footer-gap: 0.5rem;
  --bs-modal-footer-bg: ;
  --bs-modal-footer-border-color: var(--bs-border-color);
  --bs-modal-footer-border-width: 1px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--bs-modal-zindex);
  display: none;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0; }

.modal-dialog {
  position: relative;
  width: auto;
  margin: var(--bs-modal-margin);
  pointer-events: none; }
  .modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
    transform: translate(0, -50px); }
    @media (prefers-reduced-motion: reduce) {
      .modal.fade .modal-dialog {
        transition: none; } }
  .modal.show .modal-dialog {
    transform: none; }
  .modal.modal-static .modal-dialog {
    transform: scale(1.02); }

.modal-dialog-scrollable {
  height: calc(100% - var(--bs-modal-margin) * 2); }
  .modal-dialog-scrollable .modal-content {
    max-height: 100%;
    overflow: hidden; }
  .modal-dialog-scrollable .modal-body {
    overflow-y: auto; }

.modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - var(--bs-modal-margin) * 2); }

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  color: var(--bs-modal-color);
  pointer-events: auto;
  background-color: var(--bs-modal-bg);
  background-clip: padding-box;
  border: var(--bs-modal-border-width) solid var(--bs-modal-border-color);
  border-radius: var(--bs-modal-border-radius);
  outline: 0; }

.modal-backdrop {
  --bs-backdrop-zindex: 1050;
  --bs-backdrop-bg: #000;
  --bs-backdrop-opacity: 0.9;
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--bs-backdrop-zindex);
  width: 100vw;
  height: 100vh;
  background-color: var(--bs-backdrop-bg); }
  .modal-backdrop.fade {
    opacity: 0; }
  .modal-backdrop.show {
    opacity: var(--bs-backdrop-opacity); }

.modal-header {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  padding: var(--bs-modal-header-padding);
  border-bottom: var(--bs-modal-header-border-width) solid var(--bs-modal-header-border-color);
  border-top-left-radius: var(--bs-modal-inner-border-radius);
  border-top-right-radius: var(--bs-modal-inner-border-radius); }
  .modal-header .btn-close {
    padding: calc(var(--bs-modal-header-padding-y) * .5) calc(var(--bs-modal-header-padding-x) * .5);
    margin: calc(-.5 * var(--bs-modal-header-padding-y)) calc(-.5 * var(--bs-modal-header-padding-x)) calc(-.5 * var(--bs-modal-header-padding-y)) auto; }

.modal-title {
  margin-bottom: 0;
  line-height: var(--bs-modal-title-line-height); }

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: var(--bs-modal-padding); }

.modal-footer {
  display: flex;
  flex-shrink: 0;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
  padding: calc(var(--bs-modal-padding) - var(--bs-modal-footer-gap) * .5);
  background-color: var(--bs-modal-footer-bg);
  border-top: var(--bs-modal-footer-border-width) solid var(--bs-modal-footer-border-color);
  border-bottom-right-radius: var(--bs-modal-inner-border-radius);
  border-bottom-left-radius: var(--bs-modal-inner-border-radius); }
  .modal-footer > * {
    margin: calc(var(--bs-modal-footer-gap) * .5); }

@media (min-width: 576px) {
  .modal {
    --bs-modal-margin: 1.75rem;
    --bs-modal-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); }

  .modal-dialog {
    max-width: var(--bs-modal-width);
    margin-right: auto;
    margin-left: auto; }

  .modal-sm {
    --bs-modal-width: 300px; } }
@media (min-width: 992px) {
  .modal-lg,
  .modal-xl {
    --bs-modal-width: 800px; } }
@media (min-width: 1200px) {
  .modal-xl {
    --bs-modal-width: 1140px; } }
.modal-fullscreen {
  width: 100vw;
  max-width: none;
  height: 100%;
  margin: 0; }
  .modal-fullscreen .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0; }
  .modal-fullscreen .modal-header,
  .modal-fullscreen .modal-footer {
    border-radius: 0; }
  .modal-fullscreen .modal-body {
    overflow-y: auto; }

@media (max-width: 575.98px) {
  .modal-fullscreen-sm-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0; }
    .modal-fullscreen-sm-down .modal-content {
      height: 100%;
      border: 0;
      border-radius: 0; }
    .modal-fullscreen-sm-down .modal-header,
    .modal-fullscreen-sm-down .modal-footer {
      border-radius: 0; }
    .modal-fullscreen-sm-down .modal-body {
      overflow-y: auto; } }
@media (max-width: 767.98px) {
  .modal-fullscreen-md-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0; }
    .modal-fullscreen-md-down .modal-content {
      height: 100%;
      border: 0;
      border-radius: 0; }
    .modal-fullscreen-md-down .modal-header,
    .modal-fullscreen-md-down .modal-footer {
      border-radius: 0; }
    .modal-fullscreen-md-down .modal-body {
      overflow-y: auto; } }
@media (max-width: 991.98px) {
  .modal-fullscreen-lg-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0; }
    .modal-fullscreen-lg-down .modal-content {
      height: 100%;
      border: 0;
      border-radius: 0; }
    .modal-fullscreen-lg-down .modal-header,
    .modal-fullscreen-lg-down .modal-footer {
      border-radius: 0; }
    .modal-fullscreen-lg-down .modal-body {
      overflow-y: auto; } }
@media (max-width: 1199.98px) {
  .modal-fullscreen-xl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0; }
    .modal-fullscreen-xl-down .modal-content {
      height: 100%;
      border: 0;
      border-radius: 0; }
    .modal-fullscreen-xl-down .modal-header,
    .modal-fullscreen-xl-down .modal-footer {
      border-radius: 0; }
    .modal-fullscreen-xl-down .modal-body {
      overflow-y: auto; } }
@media (max-width: 1399.98px) {
  .modal-fullscreen-xxl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0; }
    .modal-fullscreen-xxl-down .modal-content {
      height: 100%;
      border: 0;
      border-radius: 0; }
    .modal-fullscreen-xxl-down .modal-header,
    .modal-fullscreen-xxl-down .modal-footer {
      border-radius: 0; }
    .modal-fullscreen-xxl-down .modal-body {
      overflow-y: auto; } }
.tooltip {
  --bs-tooltip-zindex: 1080;
  --bs-tooltip-max-width: 200px;
  --bs-tooltip-padding-x: 0.5rem;
  --bs-tooltip-padding-y: 0.25rem;
  --bs-tooltip-margin: ;
  --bs-tooltip-font-size: 0.875rem;
  --bs-tooltip-color: #fff;
  --bs-tooltip-bg: #000;
  --bs-tooltip-border-radius: 0.375rem;
  --bs-tooltip-opacity: 0.9;
  --bs-tooltip-arrow-width: 0.8rem;
  --bs-tooltip-arrow-height: 0.4rem;
  z-index: var(--bs-tooltip-zindex);
  display: block;
  padding: var(--bs-tooltip-arrow-height);
  margin: var(--bs-tooltip-margin);
  font-family: var(--bs-font-sans-serif);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  white-space: normal;
  word-spacing: normal;
  line-break: auto;
  font-size: var(--bs-tooltip-font-size);
  word-wrap: break-word;
  opacity: 0; }
  .tooltip.show {
    opacity: var(--bs-tooltip-opacity); }
  .tooltip .tooltip-arrow {
    display: block;
    width: var(--bs-tooltip-arrow-width);
    height: var(--bs-tooltip-arrow-height); }
    .tooltip .tooltip-arrow::before {
      position: absolute;
      content: "";
      border-color: transparent;
      border-style: solid; }

.bs-tooltip-top .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^="top"] .tooltip-arrow {
  bottom: 0; }
  .bs-tooltip-top .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^="top"] .tooltip-arrow::before {
    top: -1px;
    border-width: var(--bs-tooltip-arrow-height) calc(var(--bs-tooltip-arrow-width) * .5) 0;
    border-top-color: var(--bs-tooltip-bg); }

/* rtl:begin:ignore */
.bs-tooltip-end .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^="right"] .tooltip-arrow {
  left: 0;
  width: var(--bs-tooltip-arrow-height);
  height: var(--bs-tooltip-arrow-width); }
  .bs-tooltip-end .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^="right"] .tooltip-arrow::before {
    right: -1px;
    border-width: calc(var(--bs-tooltip-arrow-width) * .5) var(--bs-tooltip-arrow-height) calc(var(--bs-tooltip-arrow-width) * .5) 0;
    border-right-color: var(--bs-tooltip-bg); }

/* rtl:end:ignore */
.bs-tooltip-bottom .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^="bottom"] .tooltip-arrow {
  top: 0; }
  .bs-tooltip-bottom .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^="bottom"] .tooltip-arrow::before {
    bottom: -1px;
    border-width: 0 calc(var(--bs-tooltip-arrow-width) * .5) var(--bs-tooltip-arrow-height);
    border-bottom-color: var(--bs-tooltip-bg); }

/* rtl:begin:ignore */
.bs-tooltip-start .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^="left"] .tooltip-arrow {
  right: 0;
  width: var(--bs-tooltip-arrow-height);
  height: var(--bs-tooltip-arrow-width); }
  .bs-tooltip-start .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^="left"] .tooltip-arrow::before {
    left: -1px;
    border-width: calc(var(--bs-tooltip-arrow-width) * .5) 0 calc(var(--bs-tooltip-arrow-width) * .5) var(--bs-tooltip-arrow-height);
    border-left-color: var(--bs-tooltip-bg); }

/* rtl:end:ignore */
.tooltip-inner {
  max-width: var(--bs-tooltip-max-width);
  padding: var(--bs-tooltip-padding-y) var(--bs-tooltip-padding-x);
  color: var(--bs-tooltip-color);
  text-align: center;
  background-color: var(--bs-tooltip-bg);
  border-radius: var(--bs-tooltip-border-radius); }

.popover {
  --bs-popover-zindex: 1070;
  --bs-popover-max-width: 276px;
  --bs-popover-font-size: 0.875rem;
  --bs-popover-bg: #fff;
  --bs-popover-border-width: 1px;
  --bs-popover-border-color: var(--bs-border-color-translucent);
  --bs-popover-border-radius: 0.5rem;
  --bs-popover-inner-border-radius: calc(0.5rem - 1px);
  --bs-popover-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --bs-popover-header-padding-x: 1rem;
  --bs-popover-header-padding-y: 0.5rem;
  --bs-popover-header-font-size: 1rem;
  --bs-popover-header-color: ;
  --bs-popover-header-bg: #f0f0f0;
  --bs-popover-body-padding-x: 1rem;
  --bs-popover-body-padding-y: 1rem;
  --bs-popover-body-color: #212529;
  --bs-popover-arrow-width: 1rem;
  --bs-popover-arrow-height: 0.5rem;
  --bs-popover-arrow-border: var(--bs-popover-border-color);
  z-index: var(--bs-popover-zindex);
  display: block;
  max-width: var(--bs-popover-max-width);
  font-family: var(--bs-font-sans-serif);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  white-space: normal;
  word-spacing: normal;
  line-break: auto;
  font-size: var(--bs-popover-font-size);
  word-wrap: break-word;
  background-color: var(--bs-popover-bg);
  background-clip: padding-box;
  border: var(--bs-popover-border-width) solid var(--bs-popover-border-color);
  border-radius: var(--bs-popover-border-radius); }
  .popover .popover-arrow {
    display: block;
    width: var(--bs-popover-arrow-width);
    height: var(--bs-popover-arrow-height); }
    .popover .popover-arrow::before, .popover .popover-arrow::after {
      position: absolute;
      display: block;
      content: "";
      border-color: transparent;
      border-style: solid;
      border-width: 0; }

.bs-popover-top > .popover-arrow, .bs-popover-auto[data-popper-placement^="top"] > .popover-arrow {
  bottom: calc(-1 * (var(--bs-popover-arrow-height)) - var(--bs-popover-border-width)); }
  .bs-popover-top > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::before, .bs-popover-top > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::after {
    border-width: var(--bs-popover-arrow-height) calc(var(--bs-popover-arrow-width) * .5) 0; }
  .bs-popover-top > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::before {
    bottom: 0;
    border-top-color: var(--bs-popover-arrow-border); }
  .bs-popover-top > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::after {
    bottom: var(--bs-popover-border-width);
    border-top-color: var(--bs-popover-bg); }

/* rtl:begin:ignore */
.bs-popover-end > .popover-arrow, .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow {
  left: calc(-1 * (var(--bs-popover-arrow-height)) - var(--bs-popover-border-width));
  width: var(--bs-popover-arrow-height);
  height: var(--bs-popover-arrow-width); }
  .bs-popover-end > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::before, .bs-popover-end > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::after {
    border-width: calc(var(--bs-popover-arrow-width) * .5) var(--bs-popover-arrow-height) calc(var(--bs-popover-arrow-width) * .5) 0; }
  .bs-popover-end > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::before {
    left: 0;
    border-right-color: var(--bs-popover-arrow-border); }
  .bs-popover-end > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::after {
    left: var(--bs-popover-border-width);
    border-right-color: var(--bs-popover-bg); }

/* rtl:end:ignore */
.bs-popover-bottom > .popover-arrow, .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow {
  top: calc(-1 * (var(--bs-popover-arrow-height)) - var(--bs-popover-border-width)); }
  .bs-popover-bottom > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::before, .bs-popover-bottom > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::after {
    border-width: 0 calc(var(--bs-popover-arrow-width) * .5) var(--bs-popover-arrow-height); }
  .bs-popover-bottom > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::before {
    top: 0;
    border-bottom-color: var(--bs-popover-arrow-border); }
  .bs-popover-bottom > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::after {
    top: var(--bs-popover-border-width);
    border-bottom-color: var(--bs-popover-bg); }
.bs-popover-bottom .popover-header::before, .bs-popover-auto[data-popper-placement^="bottom"] .popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: var(--bs-popover-arrow-width);
  margin-left: calc(-.5 * var(--bs-popover-arrow-width));
  content: "";
  border-bottom: var(--bs-popover-border-width) solid var(--bs-popover-header-bg); }

/* rtl:begin:ignore */
.bs-popover-start > .popover-arrow, .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow {
  right: calc(-1 * (var(--bs-popover-arrow-height)) - var(--bs-popover-border-width));
  width: var(--bs-popover-arrow-height);
  height: var(--bs-popover-arrow-width); }
  .bs-popover-start > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::before, .bs-popover-start > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::after {
    border-width: calc(var(--bs-popover-arrow-width) * .5) 0 calc(var(--bs-popover-arrow-width) * .5) var(--bs-popover-arrow-height); }
  .bs-popover-start > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::before {
    right: 0;
    border-left-color: var(--bs-popover-arrow-border); }
  .bs-popover-start > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::after {
    right: var(--bs-popover-border-width);
    border-left-color: var(--bs-popover-bg); }

/* rtl:end:ignore */
.popover-header {
  padding: var(--bs-popover-header-padding-y) var(--bs-popover-header-padding-x);
  margin-bottom: 0;
  font-size: var(--bs-popover-header-font-size);
  color: var(--bs-popover-header-color);
  background-color: var(--bs-popover-header-bg);
  border-bottom: var(--bs-popover-border-width) solid var(--bs-popover-border-color);
  border-top-left-radius: var(--bs-popover-inner-border-radius);
  border-top-right-radius: var(--bs-popover-inner-border-radius); }
  .popover-header:empty {
    display: none; }

.popover-body {
  padding: var(--bs-popover-body-padding-y) var(--bs-popover-body-padding-x);
  color: var(--bs-popover-body-color); }

.carousel {
  position: relative; }

.carousel.pointer-event {
  touch-action: pan-y; }

.carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden; }
  .carousel-inner::after {
    display: block;
    clear: both;
    content: ""; }

.carousel-item {
  position: relative;
  display: none;
  float: left;
  width: 100%;
  margin-right: -100%;
  backface-visibility: hidden;
  transition: transform 0.6s ease-in-out; }
  @media (prefers-reduced-motion: reduce) {
    .carousel-item {
      transition: none; } }

.carousel-item.active,
.carousel-item-next,
.carousel-item-prev {
  display: block; }

/* rtl:begin:ignore */
.carousel-item-next:not(.carousel-item-start),
.active.carousel-item-end {
  transform: translateX(100%); }

.carousel-item-prev:not(.carousel-item-end),
.active.carousel-item-start {
  transform: translateX(-100%); }

/* rtl:end:ignore */
.carousel-fade .carousel-item {
  opacity: 0;
  transition-property: opacity;
  transform: none; }
.carousel-fade .carousel-item.active,
.carousel-fade .carousel-item-next.carousel-item-start,
.carousel-fade .carousel-item-prev.carousel-item-end {
  z-index: 1;
  opacity: 1; }
.carousel-fade .active.carousel-item-start,
.carousel-fade .active.carousel-item-end {
  z-index: 0;
  opacity: 0;
  transition: opacity 0s 0.6s; }
  @media (prefers-reduced-motion: reduce) {
    .carousel-fade .active.carousel-item-start,
    .carousel-fade .active.carousel-item-end {
      transition: none; } }

.carousel-control-prev,
.carousel-control-next {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 15%;
  padding: 0;
  color: #fff;
  text-align: center;
  background: none;
  border: 0;
  opacity: 0.5;
  transition: opacity 0.15s ease; }
  @media (prefers-reduced-motion: reduce) {
    .carousel-control-prev,
    .carousel-control-next {
      transition: none; } }
  .carousel-control-prev:hover, .carousel-control-prev:focus,
  .carousel-control-next:hover,
  .carousel-control-next:focus {
    color: #fff;
    text-decoration: none;
    outline: 0;
    opacity: 0.9; }

.carousel-control-prev {
  left: 0; }

.carousel-control-next {
  right: 0; }

.carousel-control-prev-icon,
.carousel-control-next-icon {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: 100% 100%; }

/* rtl:options: {
  "autoRename": true,
  "stringMap":[ {
    "name"    : "prev-next",
    "search"  : "prev",
    "replace" : "next"
  } ]
} */
.carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3e"); }

.carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e"); }

.carousel-indicators {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  display: flex;
  justify-content: center;
  padding: 0;
  margin-right: 15%;
  margin-bottom: 1rem;
  margin-left: 15%;
  list-style: none; }
  .carousel-indicators [data-bs-target] {
    box-sizing: content-box;
    flex: 0 1 auto;
    width: 30px;
    height: 3px;
    padding: 0;
    margin-right: 3px;
    margin-left: 3px;
    text-indent: -999px;
    cursor: pointer;
    background-color: #fff;
    background-clip: padding-box;
    border: 0;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    opacity: 0.5;
    transition: opacity 0.6s ease; }
    @media (prefers-reduced-motion: reduce) {
      .carousel-indicators [data-bs-target] {
        transition: none; } }
  .carousel-indicators .active {
    opacity: 1; }

.carousel-caption {
  position: absolute;
  right: 15%;
  bottom: 1.25rem;
  left: 15%;
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
  color: #fff;
  text-align: center; }

.carousel-dark .carousel-control-prev-icon,
.carousel-dark .carousel-control-next-icon {
  filter: invert(1) grayscale(100); }
.carousel-dark .carousel-indicators [data-bs-target] {
  background-color: #000; }
.carousel-dark .carousel-caption {
  color: #000; }

.spinner-grow,
.spinner-border {
  display: inline-block;
  width: var(--bs-spinner-width);
  height: var(--bs-spinner-height);
  vertical-align: var(--bs-spinner-vertical-align);
  border-radius: 50%;
  animation: var(--bs-spinner-animation-speed) linear infinite var(--bs-spinner-animation-name); }

@keyframes spinner-border {
  to {
    transform: rotate(360deg) /* rtl:ignore */; } }
.spinner-border {
  --bs-spinner-width: 2rem;
  --bs-spinner-height: 2rem;
  --bs-spinner-vertical-align: -0.125em;
  --bs-spinner-border-width: 0.25em;
  --bs-spinner-animation-speed: 0.75s;
  --bs-spinner-animation-name: spinner-border;
  border: var(--bs-spinner-border-width) solid currentcolor;
  border-right-color: transparent; }

.spinner-border-sm {
  --bs-spinner-width: 1rem;
  --bs-spinner-height: 1rem;
  --bs-spinner-border-width: 0.2em; }

@keyframes spinner-grow {
  0% {
    transform: scale(0); }
  50% {
    opacity: 1;
    transform: none; } }
.spinner-grow {
  --bs-spinner-width: 2rem;
  --bs-spinner-height: 2rem;
  --bs-spinner-vertical-align: -0.125em;
  --bs-spinner-animation-speed: 0.75s;
  --bs-spinner-animation-name: spinner-grow;
  background-color: currentcolor;
  opacity: 0; }

.spinner-grow-sm {
  --bs-spinner-width: 1rem;
  --bs-spinner-height: 1rem; }

@media (prefers-reduced-motion: reduce) {
  .spinner-border,
  .spinner-grow {
    --bs-spinner-animation-speed: 1.5s; } }
.offcanvas-sm, .offcanvas-md, .offcanvas-lg, .offcanvas-xl, .offcanvas-xxl, .offcanvas {
  --bs-offcanvas-zindex: 1045;
  --bs-offcanvas-width: 400px;
  --bs-offcanvas-height: 30vh;
  --bs-offcanvas-padding-x: 1rem;
  --bs-offcanvas-padding-y: 1rem;
  --bs-offcanvas-color: ;
  --bs-offcanvas-bg: rgba(24, 23, 23, 0.9);
  --bs-offcanvas-border-width: 1px;
  --bs-offcanvas-border-color: var(--bs-border-color-translucent);
  --bs-offcanvas-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }

@media (max-width: 575.98px) {
  .offcanvas-sm {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: transform 0.3s ease-in-out; } }
  @media (max-width: 575.98px) and (prefers-reduced-motion: reduce) {
    .offcanvas-sm {
      transition: none; } }
@media (max-width: 575.98px) {
    .offcanvas-sm.offcanvas-start {
      top: 0;
      left: 0;
      width: var(--bs-offcanvas-width);
      border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
      transform: translateX(-100%); }
    .offcanvas-sm.offcanvas-end {
      top: 0;
      right: 0;
      width: var(--bs-offcanvas-width);
      border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
      transform: translateX(100%); }
    .offcanvas-sm.offcanvas-top {
      top: 0;
      right: 0;
      left: 0;
      height: var(--bs-offcanvas-height);
      max-height: 100%;
      border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
      transform: translateY(-100%); }
    .offcanvas-sm.offcanvas-bottom {
      right: 0;
      left: 0;
      height: var(--bs-offcanvas-height);
      max-height: 100%;
      border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
      transform: translateY(100%); }
    .offcanvas-sm.showing, .offcanvas-sm.show:not(.hiding) {
      transform: none; }
    .offcanvas-sm.showing, .offcanvas-sm.hiding, .offcanvas-sm.show {
      visibility: visible; } }
@media (min-width: 576px) {
  .offcanvas-sm {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important; }
    .offcanvas-sm .offcanvas-header {
      display: none; }
    .offcanvas-sm .offcanvas-body {
      display: flex;
      flex-grow: 0;
      padding: 0;
      overflow-y: visible;
      background-color: transparent !important; } }

@media (max-width: 767.98px) {
  .offcanvas-md {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: transform 0.3s ease-in-out; } }
  @media (max-width: 767.98px) and (prefers-reduced-motion: reduce) {
    .offcanvas-md {
      transition: none; } }
@media (max-width: 767.98px) {
    .offcanvas-md.offcanvas-start {
      top: 0;
      left: 0;
      width: var(--bs-offcanvas-width);
      border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
      transform: translateX(-100%); }
    .offcanvas-md.offcanvas-end {
      top: 0;
      right: 0;
      width: var(--bs-offcanvas-width);
      border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
      transform: translateX(100%); }
    .offcanvas-md.offcanvas-top {
      top: 0;
      right: 0;
      left: 0;
      height: var(--bs-offcanvas-height);
      max-height: 100%;
      border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
      transform: translateY(-100%); }
    .offcanvas-md.offcanvas-bottom {
      right: 0;
      left: 0;
      height: var(--bs-offcanvas-height);
      max-height: 100%;
      border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
      transform: translateY(100%); }
    .offcanvas-md.showing, .offcanvas-md.show:not(.hiding) {
      transform: none; }
    .offcanvas-md.showing, .offcanvas-md.hiding, .offcanvas-md.show {
      visibility: visible; } }
@media (min-width: 768px) {
  .offcanvas-md {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important; }
    .offcanvas-md .offcanvas-header {
      display: none; }
    .offcanvas-md .offcanvas-body {
      display: flex;
      flex-grow: 0;
      padding: 0;
      overflow-y: visible;
      background-color: transparent !important; } }

@media (max-width: 991.98px) {
  .offcanvas-lg {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: transform 0.3s ease-in-out; } }
  @media (max-width: 991.98px) and (prefers-reduced-motion: reduce) {
    .offcanvas-lg {
      transition: none; } }
@media (max-width: 991.98px) {
    .offcanvas-lg.offcanvas-start {
      top: 0;
      left: 0;
      width: var(--bs-offcanvas-width);
      border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
      transform: translateX(-100%); }
    .offcanvas-lg.offcanvas-end {
      top: 0;
      right: 0;
      width: var(--bs-offcanvas-width);
      border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
      transform: translateX(100%); }
    .offcanvas-lg.offcanvas-top {
      top: 0;
      right: 0;
      left: 0;
      height: var(--bs-offcanvas-height);
      max-height: 100%;
      border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
      transform: translateY(-100%); }
    .offcanvas-lg.offcanvas-bottom {
      right: 0;
      left: 0;
      height: var(--bs-offcanvas-height);
      max-height: 100%;
      border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
      transform: translateY(100%); }
    .offcanvas-lg.showing, .offcanvas-lg.show:not(.hiding) {
      transform: none; }
    .offcanvas-lg.showing, .offcanvas-lg.hiding, .offcanvas-lg.show {
      visibility: visible; } }
@media (min-width: 992px) {
  .offcanvas-lg {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important; }
    .offcanvas-lg .offcanvas-header {
      display: none; }
    .offcanvas-lg .offcanvas-body {
      display: flex;
      flex-grow: 0;
      padding: 0;
      overflow-y: visible;
      background-color: transparent !important; } }

@media (max-width: 1199.98px) {
  .offcanvas-xl {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: transform 0.3s ease-in-out; } }
  @media (max-width: 1199.98px) and (prefers-reduced-motion: reduce) {
    .offcanvas-xl {
      transition: none; } }
@media (max-width: 1199.98px) {
    .offcanvas-xl.offcanvas-start {
      top: 0;
      left: 0;
      width: var(--bs-offcanvas-width);
      border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
      transform: translateX(-100%); }
    .offcanvas-xl.offcanvas-end {
      top: 0;
      right: 0;
      width: var(--bs-offcanvas-width);
      border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
      transform: translateX(100%); }
    .offcanvas-xl.offcanvas-top {
      top: 0;
      right: 0;
      left: 0;
      height: var(--bs-offcanvas-height);
      max-height: 100%;
      border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
      transform: translateY(-100%); }
    .offcanvas-xl.offcanvas-bottom {
      right: 0;
      left: 0;
      height: var(--bs-offcanvas-height);
      max-height: 100%;
      border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
      transform: translateY(100%); }
    .offcanvas-xl.showing, .offcanvas-xl.show:not(.hiding) {
      transform: none; }
    .offcanvas-xl.showing, .offcanvas-xl.hiding, .offcanvas-xl.show {
      visibility: visible; } }
@media (min-width: 1200px) {
  .offcanvas-xl {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important; }
    .offcanvas-xl .offcanvas-header {
      display: none; }
    .offcanvas-xl .offcanvas-body {
      display: flex;
      flex-grow: 0;
      padding: 0;
      overflow-y: visible;
      background-color: transparent !important; } }

@media (max-width: 1399.98px) {
  .offcanvas-xxl {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: transform 0.3s ease-in-out; } }
  @media (max-width: 1399.98px) and (prefers-reduced-motion: reduce) {
    .offcanvas-xxl {
      transition: none; } }
@media (max-width: 1399.98px) {
    .offcanvas-xxl.offcanvas-start {
      top: 0;
      left: 0;
      width: var(--bs-offcanvas-width);
      border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
      transform: translateX(-100%); }
    .offcanvas-xxl.offcanvas-end {
      top: 0;
      right: 0;
      width: var(--bs-offcanvas-width);
      border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
      transform: translateX(100%); }
    .offcanvas-xxl.offcanvas-top {
      top: 0;
      right: 0;
      left: 0;
      height: var(--bs-offcanvas-height);
      max-height: 100%;
      border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
      transform: translateY(-100%); }
    .offcanvas-xxl.offcanvas-bottom {
      right: 0;
      left: 0;
      height: var(--bs-offcanvas-height);
      max-height: 100%;
      border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
      transform: translateY(100%); }
    .offcanvas-xxl.showing, .offcanvas-xxl.show:not(.hiding) {
      transform: none; }
    .offcanvas-xxl.showing, .offcanvas-xxl.hiding, .offcanvas-xxl.show {
      visibility: visible; } }
@media (min-width: 1400px) {
  .offcanvas-xxl {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important; }
    .offcanvas-xxl .offcanvas-header {
      display: none; }
    .offcanvas-xxl .offcanvas-body {
      display: flex;
      flex-grow: 0;
      padding: 0;
      overflow-y: visible;
      background-color: transparent !important; } }

.offcanvas {
  position: fixed;
  bottom: 0;
  z-index: var(--bs-offcanvas-zindex);
  display: flex;
  flex-direction: column;
  max-width: 100%;
  color: var(--bs-offcanvas-color);
  visibility: hidden;
  background-color: var(--bs-offcanvas-bg);
  background-clip: padding-box;
  outline: 0;
  transition: transform 0.3s ease-in-out; }
  @media (prefers-reduced-motion: reduce) {
    .offcanvas {
      transition: none; } }
  .offcanvas.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%); }
  .offcanvas.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%); }
  .offcanvas.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%); }
  .offcanvas.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%); }
  .offcanvas.showing, .offcanvas.show:not(.hiding) {
    transform: none; }
  .offcanvas.showing, .offcanvas.hiding, .offcanvas.show {
    visibility: visible; }

.offcanvas-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #000; }
  .offcanvas-backdrop.fade {
    opacity: 0; }
  .offcanvas-backdrop.show {
    opacity: 0.9; }

.offcanvas-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--bs-offcanvas-padding-y) var(--bs-offcanvas-padding-x); }
  .offcanvas-header .btn-close {
    padding: calc(var(--bs-offcanvas-padding-y) * .5) calc(var(--bs-offcanvas-padding-x) * .5);
    margin-top: calc(-.5 * var(--bs-offcanvas-padding-y));
    margin-right: calc(-.5 * var(--bs-offcanvas-padding-x));
    margin-bottom: calc(-.5 * var(--bs-offcanvas-padding-y)); }

.offcanvas-title {
  margin-bottom: 0;
  line-height: 1.5; }

.offcanvas-body {
  flex-grow: 1;
  padding: var(--bs-offcanvas-padding-y) var(--bs-offcanvas-padding-x);
  overflow-y: auto; }

.placeholder {
  display: inline-block;
  min-height: 1em;
  vertical-align: middle;
  cursor: wait;
  background-color: currentcolor;
  opacity: 0.5; }
  .placeholder.btn::before {
    display: inline-block;
    content: ""; }

.placeholder-xs {
  min-height: .6em; }

.placeholder-sm {
  min-height: .8em; }

.placeholder-lg {
  min-height: 1.2em; }

.placeholder-glow .placeholder {
  animation: placeholder-glow 2s ease-in-out infinite; }

@keyframes placeholder-glow {
  50% {
    opacity: 0.2; } }
.placeholder-wave {
  mask-image: linear-gradient(130deg, #000 55%, rgba(0, 0, 0, 0.8) 75%, #000 95%);
  mask-size: 200% 100%;
  animation: placeholder-wave 2s linear infinite; }

@keyframes placeholder-wave {
  100% {
    mask-position: -200% 0%; } }
.clearfix::after {
  display: block;
  clear: both;
  content: ""; }

.text-bg-primary {
  color: #000 !important;
  background-color: RGBA(141, 211, 99, var(--bs-bg-opacity, 1)) !important; }

.text-bg-danger {
  color: #000 !important;
  background-color: RGBA(255, 65, 54, var(--bs-bg-opacity, 1)) !important; }

.link-primary {
  color: #8dd363 !important; }
  .link-primary:hover, .link-primary:focus {
    color: #a4dc82 !important; }

.link-danger {
  color: #ff4136 !important; }
  .link-danger:hover, .link-danger:focus {
    color: #ff675e !important; }

.ratio {
  position: relative;
  width: 100%; }
  .ratio::before {
    display: block;
    padding-top: var(--bs-aspect-ratio);
    content: ""; }
  .ratio > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%; }

.ratio-1x1 {
  --bs-aspect-ratio: 100%; }

.ratio-4x3 {
  --bs-aspect-ratio: calc(3 / 4 * 100%); }

.ratio-16x9 {
  --bs-aspect-ratio: calc(9 / 16 * 100%); }

.ratio-21x9 {
  --bs-aspect-ratio: calc(9 / 21 * 100%); }

.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030; }

.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030; }

.sticky-top {
  position: sticky;
  top: 0;
  z-index: 1020; }

.sticky-bottom {
  position: sticky;
  bottom: 0;
  z-index: 1020; }

@media (min-width: 576px) {
  .sticky-sm-top {
    position: sticky;
    top: 0;
    z-index: 1020; }

  .sticky-sm-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020; } }
@media (min-width: 768px) {
  .sticky-md-top {
    position: sticky;
    top: 0;
    z-index: 1020; }

  .sticky-md-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020; } }
@media (min-width: 992px) {
  .sticky-lg-top {
    position: sticky;
    top: 0;
    z-index: 1020; }

  .sticky-lg-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020; } }
@media (min-width: 1200px) {
  .sticky-xl-top {
    position: sticky;
    top: 0;
    z-index: 1020; }

  .sticky-xl-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020; } }
@media (min-width: 1400px) {
  .sticky-xxl-top {
    position: sticky;
    top: 0;
    z-index: 1020; }

  .sticky-xxl-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020; } }
.hstack {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-self: stretch; }

.vstack {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  align-self: stretch; }

.visually-hidden,
.visually-hidden-focusable:not(:focus):not(:focus-within) {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important; }

.stretched-link::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  content: ""; }

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; }

.vr {
  display: inline-block;
  align-self: stretch;
  width: 1px;
  min-height: 1em;
  background-color: currentcolor;
  opacity: 0.25; }

.align-baseline {
  vertical-align: baseline !important; }

.align-top {
  vertical-align: top !important; }

.align-middle {
  vertical-align: middle !important; }

.align-bottom {
  vertical-align: bottom !important; }

.align-text-bottom {
  vertical-align: text-bottom !important; }

.align-text-top {
  vertical-align: text-top !important; }

.float-start {
  float: left !important; }

.float-end {
  float: right !important; }

.float-none {
  float: none !important; }

.opacity-0 {
  opacity: 0 !important; }

.opacity-25 {
  opacity: 0.25 !important; }

.opacity-50 {
  opacity: 0.5 !important; }

.opacity-75 {
  opacity: 0.75 !important; }

.opacity-100 {
  opacity: 1 !important; }

.overflow-auto {
  overflow: auto !important; }

.overflow-hidden {
  overflow: hidden !important; }

.overflow-visible {
  overflow: visible !important; }

.overflow-scroll {
  overflow: scroll !important; }

.d-inline {
  display: inline !important; }

.d-inline-block {
  display: inline-block !important; }

.d-block {
  display: block !important; }

.d-grid {
  display: grid !important; }

.d-table {
  display: table !important; }

.d-table-row {
  display: table-row !important; }

.d-table-cell {
  display: table-cell !important; }

.d-flex {
  display: flex !important; }

.d-inline-flex {
  display: inline-flex !important; }

.d-none {
  display: none !important; }

.shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important; }

.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important; }

.shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important; }

.shadow-none {
  box-shadow: none !important; }

.position-static {
  position: static !important; }

.position-relative {
  position: relative !important; }

.position-absolute {
  position: absolute !important; }

.position-fixed {
  position: fixed !important; }

.position-sticky {
  position: sticky !important; }

.top-0 {
  top: 0 !important; }

.top-50 {
  top: 50% !important; }

.top-100 {
  top: 100% !important; }

.bottom-0 {
  bottom: 0 !important; }

.bottom-50 {
  bottom: 50% !important; }

.bottom-100 {
  bottom: 100% !important; }

.start-0 {
  left: 0 !important; }

.start-50 {
  left: 50% !important; }

.start-100 {
  left: 100% !important; }

.end-0 {
  right: 0 !important; }

.end-50 {
  right: 50% !important; }

.end-100 {
  right: 100% !important; }

.translate-middle {
  transform: translate(-50%, -50%) !important; }

.translate-middle-x {
  transform: translateX(-50%) !important; }

.translate-middle-y {
  transform: translateY(-50%) !important; }

.border {
  border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important; }

.border-0 {
  border: 0 !important; }

.border-top {
  border-top: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important; }

.border-top-0 {
  border-top: 0 !important; }

.border-end {
  border-right: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important; }

.border-end-0 {
  border-right: 0 !important; }

.border-bottom {
  border-bottom: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important; }

.border-bottom-0 {
  border-bottom: 0 !important; }

.border-start {
  border-left: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important; }

.border-start-0 {
  border-left: 0 !important; }

.border-primary {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-primary-rgb), var(--bs-border-opacity)) !important; }

.border-danger {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-danger-rgb), var(--bs-border-opacity)) !important; }

.border-white {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-white-rgb), var(--bs-border-opacity)) !important; }

.border-1 {
  --bs-border-width: 1px; }

.border-2 {
  --bs-border-width: 2px; }

.border-3 {
  --bs-border-width: 3px; }

.border-4 {
  --bs-border-width: 4px; }

.border-5 {
  --bs-border-width: 5px; }

.border-opacity-10 {
  --bs-border-opacity: 0.1; }

.border-opacity-25 {
  --bs-border-opacity: 0.25; }

.border-opacity-50 {
  --bs-border-opacity: 0.5; }

.border-opacity-75 {
  --bs-border-opacity: 0.75; }

.border-opacity-100 {
  --bs-border-opacity: 1; }

.w-25 {
  width: 25% !important; }

.w-50 {
  width: 50% !important; }

.w-75 {
  width: 75% !important; }

.w-100 {
  width: 100% !important; }

.w-auto {
  width: auto !important; }

.mw-100 {
  max-width: 100% !important; }

.vw-100 {
  width: 100vw !important; }

.min-vw-100 {
  min-width: 100vw !important; }

.h-25 {
  height: 25% !important; }

.h-50 {
  height: 50% !important; }

.h-75 {
  height: 75% !important; }

.h-100 {
  height: 100% !important; }

.h-auto {
  height: auto !important; }

.mh-100 {
  max-height: 100% !important; }

.vh-100 {
  height: 100vh !important; }

.min-vh-100 {
  min-height: 100vh !important; }

.flex-fill {
  flex: 1 1 auto !important; }

.flex-row {
  flex-direction: row !important; }

.flex-column {
  flex-direction: column !important; }

.flex-row-reverse {
  flex-direction: row-reverse !important; }

.flex-column-reverse {
  flex-direction: column-reverse !important; }

.flex-grow-0 {
  flex-grow: 0 !important; }

.flex-grow-1 {
  flex-grow: 1 !important; }

.flex-shrink-0 {
  flex-shrink: 0 !important; }

.flex-shrink-1 {
  flex-shrink: 1 !important; }

.flex-wrap {
  flex-wrap: wrap !important; }

.flex-nowrap {
  flex-wrap: nowrap !important; }

.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important; }

.justify-content-start {
  justify-content: flex-start !important; }

.justify-content-end {
  justify-content: flex-end !important; }

.justify-content-center {
  justify-content: center !important; }

.justify-content-between {
  justify-content: space-between !important; }

.justify-content-around {
  justify-content: space-around !important; }

.justify-content-evenly {
  justify-content: space-evenly !important; }

.align-items-start {
  align-items: flex-start !important; }

.align-items-end {
  align-items: flex-end !important; }

.align-items-center {
  align-items: center !important; }

.align-items-baseline {
  align-items: baseline !important; }

.align-items-stretch {
  align-items: stretch !important; }

.align-content-start {
  align-content: flex-start !important; }

.align-content-end {
  align-content: flex-end !important; }

.align-content-center {
  align-content: center !important; }

.align-content-between {
  align-content: space-between !important; }

.align-content-around {
  align-content: space-around !important; }

.align-content-stretch {
  align-content: stretch !important; }

.align-self-auto {
  align-self: auto !important; }

.align-self-start {
  align-self: flex-start !important; }

.align-self-end {
  align-self: flex-end !important; }

.align-self-center {
  align-self: center !important; }

.align-self-baseline {
  align-self: baseline !important; }

.align-self-stretch {
  align-self: stretch !important; }

.order-first {
  order: -1 !important; }

.order-0 {
  order: 0 !important; }

.order-1 {
  order: 1 !important; }

.order-2 {
  order: 2 !important; }

.order-3 {
  order: 3 !important; }

.order-4 {
  order: 4 !important; }

.order-5 {
  order: 5 !important; }

.order-last {
  order: 6 !important; }

.m-0 {
  margin: 0 !important; }

.m-1 {
  margin: 0.25rem !important; }

.m-2 {
  margin: 0.5rem !important; }

.m-3 {
  margin: 1rem !important; }

.m-4 {
  margin: 1.5rem !important; }

.m-5 {
  margin: 3rem !important; }

.m-auto {
  margin: auto !important; }

.mx-0 {
  margin-right: 0 !important;
  margin-left: 0 !important; }

.mx-1 {
  margin-right: 0.25rem !important;
  margin-left: 0.25rem !important; }

.mx-2 {
  margin-right: 0.5rem !important;
  margin-left: 0.5rem !important; }

.mx-3 {
  margin-right: 1rem !important;
  margin-left: 1rem !important; }

.mx-4 {
  margin-right: 1.5rem !important;
  margin-left: 1.5rem !important; }

.mx-5 {
  margin-right: 3rem !important;
  margin-left: 3rem !important; }

.mx-auto {
  margin-right: auto !important;
  margin-left: auto !important; }

.my-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important; }

.my-1 {
  margin-top: 0.25rem !important;
  margin-bottom: 0.25rem !important; }

.my-2 {
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important; }

.my-3 {
  margin-top: 1rem !important;
  margin-bottom: 1rem !important; }

.my-4 {
  margin-top: 1.5rem !important;
  margin-bottom: 1.5rem !important; }

.my-5 {
  margin-top: 3rem !important;
  margin-bottom: 3rem !important; }

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important; }

.mt-0 {
  margin-top: 0 !important; }

.mt-1 {
  margin-top: 0.25rem !important; }

.mt-2 {
  margin-top: 0.5rem !important; }

.mt-3 {
  margin-top: 1rem !important; }

.mt-4 {
  margin-top: 1.5rem !important; }

.mt-5 {
  margin-top: 3rem !important; }

.mt-auto {
  margin-top: auto !important; }

.me-0 {
  margin-right: 0 !important; }

.me-1 {
  margin-right: 0.25rem !important; }

.me-2 {
  margin-right: 0.5rem !important; }

.me-3 {
  margin-right: 1rem !important; }

.me-4 {
  margin-right: 1.5rem !important; }

.me-5 {
  margin-right: 3rem !important; }

.me-auto {
  margin-right: auto !important; }

.mb-0 {
  margin-bottom: 0 !important; }

.mb-1 {
  margin-bottom: 0.25rem !important; }

.mb-2 {
  margin-bottom: 0.5rem !important; }

.mb-3 {
  margin-bottom: 1rem !important; }

.mb-4 {
  margin-bottom: 1.5rem !important; }

.mb-5 {
  margin-bottom: 3rem !important; }

.mb-auto {
  margin-bottom: auto !important; }

.ms-0 {
  margin-left: 0 !important; }

.ms-1 {
  margin-left: 0.25rem !important; }

.ms-2 {
  margin-left: 0.5rem !important; }

.ms-3 {
  margin-left: 1rem !important; }

.ms-4 {
  margin-left: 1.5rem !important; }

.ms-5 {
  margin-left: 3rem !important; }

.ms-auto {
  margin-left: auto !important; }

.p-0 {
  padding: 0 !important; }

.p-1 {
  padding: 0.25rem !important; }

.p-2 {
  padding: 0.5rem !important; }

.p-3 {
  padding: 1rem !important; }

.p-4 {
  padding: 1.5rem !important; }

.p-5 {
  padding: 3rem !important; }

.px-0 {
  padding-right: 0 !important;
  padding-left: 0 !important; }

.px-1 {
  padding-right: 0.25rem !important;
  padding-left: 0.25rem !important; }

.px-2 {
  padding-right: 0.5rem !important;
  padding-left: 0.5rem !important; }

.px-3 {
  padding-right: 1rem !important;
  padding-left: 1rem !important; }

.px-4 {
  padding-right: 1.5rem !important;
  padding-left: 1.5rem !important; }

.px-5 {
  padding-right: 3rem !important;
  padding-left: 3rem !important; }

.py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important; }

.py-1 {
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important; }

.py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important; }

.py-3 {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important; }

.py-4 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important; }

.py-5 {
  padding-top: 3rem !important;
  padding-bottom: 3rem !important; }

.pt-0 {
  padding-top: 0 !important; }

.pt-1 {
  padding-top: 0.25rem !important; }

.pt-2 {
  padding-top: 0.5rem !important; }

.pt-3 {
  padding-top: 1rem !important; }

.pt-4 {
  padding-top: 1.5rem !important; }

.pt-5 {
  padding-top: 3rem !important; }

.pe-0 {
  padding-right: 0 !important; }

.pe-1 {
  padding-right: 0.25rem !important; }

.pe-2 {
  padding-right: 0.5rem !important; }

.pe-3 {
  padding-right: 1rem !important; }

.pe-4 {
  padding-right: 1.5rem !important; }

.pe-5 {
  padding-right: 3rem !important; }

.pb-0 {
  padding-bottom: 0 !important; }

.pb-1 {
  padding-bottom: 0.25rem !important; }

.pb-2 {
  padding-bottom: 0.5rem !important; }

.pb-3 {
  padding-bottom: 1rem !important; }

.pb-4 {
  padding-bottom: 1.5rem !important; }

.pb-5 {
  padding-bottom: 3rem !important; }

.ps-0 {
  padding-left: 0 !important; }

.ps-1 {
  padding-left: 0.25rem !important; }

.ps-2 {
  padding-left: 0.5rem !important; }

.ps-3 {
  padding-left: 1rem !important; }

.ps-4 {
  padding-left: 1.5rem !important; }

.ps-5 {
  padding-left: 3rem !important; }

.gap-0 {
  gap: 0 !important; }

.gap-1 {
  gap: 0.25rem !important; }

.gap-2 {
  gap: 0.5rem !important; }

.gap-3 {
  gap: 1rem !important; }

.gap-4 {
  gap: 1.5rem !important; }

.gap-5 {
  gap: 3rem !important; }

.font-monospace {
  font-family: var(--bs-font-monospace) !important; }

.fs-1 {
  font-size: calc(1.375rem + 1.5vw) !important; }

.fs-2 {
  font-size: calc(1.325rem + 0.9vw) !important; }

.fs-3 {
  font-size: calc(1.3rem + 0.6vw) !important; }

.fs-4 {
  font-size: calc(1.275rem + 0.3vw) !important; }

.fs-5 {
  font-size: 1.25rem !important; }

.fs-6 {
  font-size: 1rem !important; }

.fst-italic {
  font-style: italic !important; }

.fst-normal {
  font-style: normal !important; }

.fw-light {
  font-weight: 300 !important; }

.fw-lighter {
  font-weight: lighter !important; }

.fw-normal {
  font-weight: 400 !important; }

.fw-bold {
  font-weight: 700 !important; }

.fw-semibold {
  font-weight: 600 !important; }

.fw-bolder {
  font-weight: bolder !important; }

.lh-1 {
  line-height: 1 !important; }

.lh-sm {
  line-height: 1.25 !important; }

.lh-base {
  line-height: 1.5 !important; }

.lh-lg {
  line-height: 2 !important; }

.text-start {
  text-align: left !important; }

.text-end {
  text-align: right !important; }

.text-center {
  text-align: center !important; }

.text-decoration-none {
  text-decoration: none !important; }

.text-decoration-underline {
  text-decoration: underline !important; }

.text-decoration-line-through {
  text-decoration: line-through !important; }

.text-lowercase {
  text-transform: lowercase !important; }

.text-uppercase {
  text-transform: uppercase !important; }

.text-capitalize {
  text-transform: capitalize !important; }

.text-wrap {
  white-space: normal !important; }

.text-nowrap {
  white-space: nowrap !important; }

/* rtl:begin:remove */
.text-break {
  word-wrap: break-word !important;
  word-break: break-word !important; }

/* rtl:end:remove */
.text-primary {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-primary-rgb), var(--bs-text-opacity)) !important; }

.text-danger {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-danger-rgb), var(--bs-text-opacity)) !important; }

.text-black {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-black-rgb), var(--bs-text-opacity)) !important; }

.text-white {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-white-rgb), var(--bs-text-opacity)) !important; }

.text-body {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-body-color-rgb), var(--bs-text-opacity)) !important; }

.text-muted {
  --bs-text-opacity: 1;
  color: #6c757d !important; }

.text-black-50 {
  --bs-text-opacity: 1;
  color: rgba(0, 0, 0, 0.5) !important; }

.text-white-50 {
  --bs-text-opacity: 1;
  color: rgba(255, 255, 255, 0.5) !important; }

.text-reset {
  --bs-text-opacity: 1;
  color: inherit !important; }

.text-opacity-25 {
  --bs-text-opacity: 0.25; }

.text-opacity-50 {
  --bs-text-opacity: 0.5; }

.text-opacity-75 {
  --bs-text-opacity: 0.75; }

.text-opacity-100 {
  --bs-text-opacity: 1; }

.bg-primary {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-primary-rgb), var(--bs-bg-opacity)) !important; }

.bg-danger {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-danger-rgb), var(--bs-bg-opacity)) !important; }

.bg-black {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-black-rgb), var(--bs-bg-opacity)) !important; }

.bg-white {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-white-rgb), var(--bs-bg-opacity)) !important; }

.bg-body {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-body-bg-rgb), var(--bs-bg-opacity)) !important; }

.bg-transparent {
  --bs-bg-opacity: 1;
  background-color: transparent !important; }

.bg-opacity-10 {
  --bs-bg-opacity: 0.1; }

.bg-opacity-25 {
  --bs-bg-opacity: 0.25; }

.bg-opacity-50 {
  --bs-bg-opacity: 0.5; }

.bg-opacity-75 {
  --bs-bg-opacity: 0.75; }

.bg-opacity-100 {
  --bs-bg-opacity: 1; }

.bg-gradient {
  background-image: var(--bs-gradient) !important; }

.user-select-all {
  user-select: all !important; }

.user-select-auto {
  user-select: auto !important; }

.user-select-none {
  user-select: none !important; }

.pe-none {
  pointer-events: none !important; }

.pe-auto {
  pointer-events: auto !important; }

.rounded {
  border-radius: var(--bs-border-radius) !important; }

.rounded-0 {
  border-radius: 0 !important; }

.rounded-1 {
  border-radius: var(--bs-border-radius-sm) !important; }

.rounded-2 {
  border-radius: var(--bs-border-radius) !important; }

.rounded-3 {
  border-radius: var(--bs-border-radius-lg) !important; }

.rounded-4 {
  border-radius: var(--bs-border-radius-xl) !important; }

.rounded-5 {
  border-radius: var(--bs-border-radius-2xl) !important; }

.rounded-circle {
  border-radius: 50% !important; }

.rounded-pill {
  border-radius: var(--bs-border-radius-pill) !important; }

.rounded-top {
  border-top-left-radius: var(--bs-border-radius) !important;
  border-top-right-radius: var(--bs-border-radius) !important; }

.rounded-end {
  border-top-right-radius: var(--bs-border-radius) !important;
  border-bottom-right-radius: var(--bs-border-radius) !important; }

.rounded-bottom {
  border-bottom-right-radius: var(--bs-border-radius) !important;
  border-bottom-left-radius: var(--bs-border-radius) !important; }

.rounded-start {
  border-bottom-left-radius: var(--bs-border-radius) !important;
  border-top-left-radius: var(--bs-border-radius) !important; }

.visible {
  visibility: visible !important; }

.invisible {
  visibility: hidden !important; }

@media (min-width: 576px) {
  .float-sm-start {
    float: left !important; }

  .float-sm-end {
    float: right !important; }

  .float-sm-none {
    float: none !important; }

  .d-sm-inline {
    display: inline !important; }

  .d-sm-inline-block {
    display: inline-block !important; }

  .d-sm-block {
    display: block !important; }

  .d-sm-grid {
    display: grid !important; }

  .d-sm-table {
    display: table !important; }

  .d-sm-table-row {
    display: table-row !important; }

  .d-sm-table-cell {
    display: table-cell !important; }

  .d-sm-flex {
    display: flex !important; }

  .d-sm-inline-flex {
    display: inline-flex !important; }

  .d-sm-none {
    display: none !important; }

  .flex-sm-fill {
    flex: 1 1 auto !important; }

  .flex-sm-row {
    flex-direction: row !important; }

  .flex-sm-column {
    flex-direction: column !important; }

  .flex-sm-row-reverse {
    flex-direction: row-reverse !important; }

  .flex-sm-column-reverse {
    flex-direction: column-reverse !important; }

  .flex-sm-grow-0 {
    flex-grow: 0 !important; }

  .flex-sm-grow-1 {
    flex-grow: 1 !important; }

  .flex-sm-shrink-0 {
    flex-shrink: 0 !important; }

  .flex-sm-shrink-1 {
    flex-shrink: 1 !important; }

  .flex-sm-wrap {
    flex-wrap: wrap !important; }

  .flex-sm-nowrap {
    flex-wrap: nowrap !important; }

  .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important; }

  .justify-content-sm-start {
    justify-content: flex-start !important; }

  .justify-content-sm-end {
    justify-content: flex-end !important; }

  .justify-content-sm-center {
    justify-content: center !important; }

  .justify-content-sm-between {
    justify-content: space-between !important; }

  .justify-content-sm-around {
    justify-content: space-around !important; }

  .justify-content-sm-evenly {
    justify-content: space-evenly !important; }

  .align-items-sm-start {
    align-items: flex-start !important; }

  .align-items-sm-end {
    align-items: flex-end !important; }

  .align-items-sm-center {
    align-items: center !important; }

  .align-items-sm-baseline {
    align-items: baseline !important; }

  .align-items-sm-stretch {
    align-items: stretch !important; }

  .align-content-sm-start {
    align-content: flex-start !important; }

  .align-content-sm-end {
    align-content: flex-end !important; }

  .align-content-sm-center {
    align-content: center !important; }

  .align-content-sm-between {
    align-content: space-between !important; }

  .align-content-sm-around {
    align-content: space-around !important; }

  .align-content-sm-stretch {
    align-content: stretch !important; }

  .align-self-sm-auto {
    align-self: auto !important; }

  .align-self-sm-start {
    align-self: flex-start !important; }

  .align-self-sm-end {
    align-self: flex-end !important; }

  .align-self-sm-center {
    align-self: center !important; }

  .align-self-sm-baseline {
    align-self: baseline !important; }

  .align-self-sm-stretch {
    align-self: stretch !important; }

  .order-sm-first {
    order: -1 !important; }

  .order-sm-0 {
    order: 0 !important; }

  .order-sm-1 {
    order: 1 !important; }

  .order-sm-2 {
    order: 2 !important; }

  .order-sm-3 {
    order: 3 !important; }

  .order-sm-4 {
    order: 4 !important; }

  .order-sm-5 {
    order: 5 !important; }

  .order-sm-last {
    order: 6 !important; }

  .m-sm-0 {
    margin: 0 !important; }

  .m-sm-1 {
    margin: 0.25rem !important; }

  .m-sm-2 {
    margin: 0.5rem !important; }

  .m-sm-3 {
    margin: 1rem !important; }

  .m-sm-4 {
    margin: 1.5rem !important; }

  .m-sm-5 {
    margin: 3rem !important; }

  .m-sm-auto {
    margin: auto !important; }

  .mx-sm-0 {
    margin-right: 0 !important;
    margin-left: 0 !important; }

  .mx-sm-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important; }

  .mx-sm-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important; }

  .mx-sm-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important; }

  .mx-sm-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important; }

  .mx-sm-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important; }

  .mx-sm-auto {
    margin-right: auto !important;
    margin-left: auto !important; }

  .my-sm-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important; }

  .my-sm-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important; }

  .my-sm-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important; }

  .my-sm-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important; }

  .my-sm-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important; }

  .my-sm-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important; }

  .my-sm-auto {
    margin-top: auto !important;
    margin-bottom: auto !important; }

  .mt-sm-0 {
    margin-top: 0 !important; }

  .mt-sm-1 {
    margin-top: 0.25rem !important; }

  .mt-sm-2 {
    margin-top: 0.5rem !important; }

  .mt-sm-3 {
    margin-top: 1rem !important; }

  .mt-sm-4 {
    margin-top: 1.5rem !important; }

  .mt-sm-5 {
    margin-top: 3rem !important; }

  .mt-sm-auto {
    margin-top: auto !important; }

  .me-sm-0 {
    margin-right: 0 !important; }

  .me-sm-1 {
    margin-right: 0.25rem !important; }

  .me-sm-2 {
    margin-right: 0.5rem !important; }

  .me-sm-3 {
    margin-right: 1rem !important; }

  .me-sm-4 {
    margin-right: 1.5rem !important; }

  .me-sm-5 {
    margin-right: 3rem !important; }

  .me-sm-auto {
    margin-right: auto !important; }

  .mb-sm-0 {
    margin-bottom: 0 !important; }

  .mb-sm-1 {
    margin-bottom: 0.25rem !important; }

  .mb-sm-2 {
    margin-bottom: 0.5rem !important; }

  .mb-sm-3 {
    margin-bottom: 1rem !important; }

  .mb-sm-4 {
    margin-bottom: 1.5rem !important; }

  .mb-sm-5 {
    margin-bottom: 3rem !important; }

  .mb-sm-auto {
    margin-bottom: auto !important; }

  .ms-sm-0 {
    margin-left: 0 !important; }

  .ms-sm-1 {
    margin-left: 0.25rem !important; }

  .ms-sm-2 {
    margin-left: 0.5rem !important; }

  .ms-sm-3 {
    margin-left: 1rem !important; }

  .ms-sm-4 {
    margin-left: 1.5rem !important; }

  .ms-sm-5 {
    margin-left: 3rem !important; }

  .ms-sm-auto {
    margin-left: auto !important; }

  .p-sm-0 {
    padding: 0 !important; }

  .p-sm-1 {
    padding: 0.25rem !important; }

  .p-sm-2 {
    padding: 0.5rem !important; }

  .p-sm-3 {
    padding: 1rem !important; }

  .p-sm-4 {
    padding: 1.5rem !important; }

  .p-sm-5 {
    padding: 3rem !important; }

  .px-sm-0 {
    padding-right: 0 !important;
    padding-left: 0 !important; }

  .px-sm-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important; }

  .px-sm-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important; }

  .px-sm-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important; }

  .px-sm-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important; }

  .px-sm-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important; }

  .py-sm-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important; }

  .py-sm-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important; }

  .py-sm-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important; }

  .py-sm-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important; }

  .py-sm-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important; }

  .py-sm-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important; }

  .pt-sm-0 {
    padding-top: 0 !important; }

  .pt-sm-1 {
    padding-top: 0.25rem !important; }

  .pt-sm-2 {
    padding-top: 0.5rem !important; }

  .pt-sm-3 {
    padding-top: 1rem !important; }

  .pt-sm-4 {
    padding-top: 1.5rem !important; }

  .pt-sm-5 {
    padding-top: 3rem !important; }

  .pe-sm-0 {
    padding-right: 0 !important; }

  .pe-sm-1 {
    padding-right: 0.25rem !important; }

  .pe-sm-2 {
    padding-right: 0.5rem !important; }

  .pe-sm-3 {
    padding-right: 1rem !important; }

  .pe-sm-4 {
    padding-right: 1.5rem !important; }

  .pe-sm-5 {
    padding-right: 3rem !important; }

  .pb-sm-0 {
    padding-bottom: 0 !important; }

  .pb-sm-1 {
    padding-bottom: 0.25rem !important; }

  .pb-sm-2 {
    padding-bottom: 0.5rem !important; }

  .pb-sm-3 {
    padding-bottom: 1rem !important; }

  .pb-sm-4 {
    padding-bottom: 1.5rem !important; }

  .pb-sm-5 {
    padding-bottom: 3rem !important; }

  .ps-sm-0 {
    padding-left: 0 !important; }

  .ps-sm-1 {
    padding-left: 0.25rem !important; }

  .ps-sm-2 {
    padding-left: 0.5rem !important; }

  .ps-sm-3 {
    padding-left: 1rem !important; }

  .ps-sm-4 {
    padding-left: 1.5rem !important; }

  .ps-sm-5 {
    padding-left: 3rem !important; }

  .gap-sm-0 {
    gap: 0 !important; }

  .gap-sm-1 {
    gap: 0.25rem !important; }

  .gap-sm-2 {
    gap: 0.5rem !important; }

  .gap-sm-3 {
    gap: 1rem !important; }

  .gap-sm-4 {
    gap: 1.5rem !important; }

  .gap-sm-5 {
    gap: 3rem !important; }

  .text-sm-start {
    text-align: left !important; }

  .text-sm-end {
    text-align: right !important; }

  .text-sm-center {
    text-align: center !important; } }
@media (min-width: 768px) {
  .float-md-start {
    float: left !important; }

  .float-md-end {
    float: right !important; }

  .float-md-none {
    float: none !important; }

  .d-md-inline {
    display: inline !important; }

  .d-md-inline-block {
    display: inline-block !important; }

  .d-md-block {
    display: block !important; }

  .d-md-grid {
    display: grid !important; }

  .d-md-table {
    display: table !important; }

  .d-md-table-row {
    display: table-row !important; }

  .d-md-table-cell {
    display: table-cell !important; }

  .d-md-flex {
    display: flex !important; }

  .d-md-inline-flex {
    display: inline-flex !important; }

  .d-md-none {
    display: none !important; }

  .flex-md-fill {
    flex: 1 1 auto !important; }

  .flex-md-row {
    flex-direction: row !important; }

  .flex-md-column {
    flex-direction: column !important; }

  .flex-md-row-reverse {
    flex-direction: row-reverse !important; }

  .flex-md-column-reverse {
    flex-direction: column-reverse !important; }

  .flex-md-grow-0 {
    flex-grow: 0 !important; }

  .flex-md-grow-1 {
    flex-grow: 1 !important; }

  .flex-md-shrink-0 {
    flex-shrink: 0 !important; }

  .flex-md-shrink-1 {
    flex-shrink: 1 !important; }

  .flex-md-wrap {
    flex-wrap: wrap !important; }

  .flex-md-nowrap {
    flex-wrap: nowrap !important; }

  .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important; }

  .justify-content-md-start {
    justify-content: flex-start !important; }

  .justify-content-md-end {
    justify-content: flex-end !important; }

  .justify-content-md-center {
    justify-content: center !important; }

  .justify-content-md-between {
    justify-content: space-between !important; }

  .justify-content-md-around {
    justify-content: space-around !important; }

  .justify-content-md-evenly {
    justify-content: space-evenly !important; }

  .align-items-md-start {
    align-items: flex-start !important; }

  .align-items-md-end {
    align-items: flex-end !important; }

  .align-items-md-center {
    align-items: center !important; }

  .align-items-md-baseline {
    align-items: baseline !important; }

  .align-items-md-stretch {
    align-items: stretch !important; }

  .align-content-md-start {
    align-content: flex-start !important; }

  .align-content-md-end {
    align-content: flex-end !important; }

  .align-content-md-center {
    align-content: center !important; }

  .align-content-md-between {
    align-content: space-between !important; }

  .align-content-md-around {
    align-content: space-around !important; }

  .align-content-md-stretch {
    align-content: stretch !important; }

  .align-self-md-auto {
    align-self: auto !important; }

  .align-self-md-start {
    align-self: flex-start !important; }

  .align-self-md-end {
    align-self: flex-end !important; }

  .align-self-md-center {
    align-self: center !important; }

  .align-self-md-baseline {
    align-self: baseline !important; }

  .align-self-md-stretch {
    align-self: stretch !important; }

  .order-md-first {
    order: -1 !important; }

  .order-md-0 {
    order: 0 !important; }

  .order-md-1 {
    order: 1 !important; }

  .order-md-2 {
    order: 2 !important; }

  .order-md-3 {
    order: 3 !important; }

  .order-md-4 {
    order: 4 !important; }

  .order-md-5 {
    order: 5 !important; }

  .order-md-last {
    order: 6 !important; }

  .m-md-0 {
    margin: 0 !important; }

  .m-md-1 {
    margin: 0.25rem !important; }

  .m-md-2 {
    margin: 0.5rem !important; }

  .m-md-3 {
    margin: 1rem !important; }

  .m-md-4 {
    margin: 1.5rem !important; }

  .m-md-5 {
    margin: 3rem !important; }

  .m-md-auto {
    margin: auto !important; }

  .mx-md-0 {
    margin-right: 0 !important;
    margin-left: 0 !important; }

  .mx-md-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important; }

  .mx-md-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important; }

  .mx-md-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important; }

  .mx-md-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important; }

  .mx-md-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important; }

  .mx-md-auto {
    margin-right: auto !important;
    margin-left: auto !important; }

  .my-md-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important; }

  .my-md-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important; }

  .my-md-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important; }

  .my-md-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important; }

  .my-md-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important; }

  .my-md-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important; }

  .my-md-auto {
    margin-top: auto !important;
    margin-bottom: auto !important; }

  .mt-md-0 {
    margin-top: 0 !important; }

  .mt-md-1 {
    margin-top: 0.25rem !important; }

  .mt-md-2 {
    margin-top: 0.5rem !important; }

  .mt-md-3 {
    margin-top: 1rem !important; }

  .mt-md-4 {
    margin-top: 1.5rem !important; }

  .mt-md-5 {
    margin-top: 3rem !important; }

  .mt-md-auto {
    margin-top: auto !important; }

  .me-md-0 {
    margin-right: 0 !important; }

  .me-md-1 {
    margin-right: 0.25rem !important; }

  .me-md-2 {
    margin-right: 0.5rem !important; }

  .me-md-3 {
    margin-right: 1rem !important; }

  .me-md-4 {
    margin-right: 1.5rem !important; }

  .me-md-5 {
    margin-right: 3rem !important; }

  .me-md-auto {
    margin-right: auto !important; }

  .mb-md-0 {
    margin-bottom: 0 !important; }

  .mb-md-1 {
    margin-bottom: 0.25rem !important; }

  .mb-md-2 {
    margin-bottom: 0.5rem !important; }

  .mb-md-3 {
    margin-bottom: 1rem !important; }

  .mb-md-4 {
    margin-bottom: 1.5rem !important; }

  .mb-md-5 {
    margin-bottom: 3rem !important; }

  .mb-md-auto {
    margin-bottom: auto !important; }

  .ms-md-0 {
    margin-left: 0 !important; }

  .ms-md-1 {
    margin-left: 0.25rem !important; }

  .ms-md-2 {
    margin-left: 0.5rem !important; }

  .ms-md-3 {
    margin-left: 1rem !important; }

  .ms-md-4 {
    margin-left: 1.5rem !important; }

  .ms-md-5 {
    margin-left: 3rem !important; }

  .ms-md-auto {
    margin-left: auto !important; }

  .p-md-0 {
    padding: 0 !important; }

  .p-md-1 {
    padding: 0.25rem !important; }

  .p-md-2 {
    padding: 0.5rem !important; }

  .p-md-3 {
    padding: 1rem !important; }

  .p-md-4 {
    padding: 1.5rem !important; }

  .p-md-5 {
    padding: 3rem !important; }

  .px-md-0 {
    padding-right: 0 !important;
    padding-left: 0 !important; }

  .px-md-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important; }

  .px-md-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important; }

  .px-md-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important; }

  .px-md-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important; }

  .px-md-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important; }

  .py-md-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important; }

  .py-md-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important; }

  .py-md-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important; }

  .py-md-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important; }

  .py-md-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important; }

  .py-md-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important; }

  .pt-md-0 {
    padding-top: 0 !important; }

  .pt-md-1 {
    padding-top: 0.25rem !important; }

  .pt-md-2 {
    padding-top: 0.5rem !important; }

  .pt-md-3 {
    padding-top: 1rem !important; }

  .pt-md-4 {
    padding-top: 1.5rem !important; }

  .pt-md-5 {
    padding-top: 3rem !important; }

  .pe-md-0 {
    padding-right: 0 !important; }

  .pe-md-1 {
    padding-right: 0.25rem !important; }

  .pe-md-2 {
    padding-right: 0.5rem !important; }

  .pe-md-3 {
    padding-right: 1rem !important; }

  .pe-md-4 {
    padding-right: 1.5rem !important; }

  .pe-md-5 {
    padding-right: 3rem !important; }

  .pb-md-0 {
    padding-bottom: 0 !important; }

  .pb-md-1 {
    padding-bottom: 0.25rem !important; }

  .pb-md-2 {
    padding-bottom: 0.5rem !important; }

  .pb-md-3 {
    padding-bottom: 1rem !important; }

  .pb-md-4 {
    padding-bottom: 1.5rem !important; }

  .pb-md-5 {
    padding-bottom: 3rem !important; }

  .ps-md-0 {
    padding-left: 0 !important; }

  .ps-md-1 {
    padding-left: 0.25rem !important; }

  .ps-md-2 {
    padding-left: 0.5rem !important; }

  .ps-md-3 {
    padding-left: 1rem !important; }

  .ps-md-4 {
    padding-left: 1.5rem !important; }

  .ps-md-5 {
    padding-left: 3rem !important; }

  .gap-md-0 {
    gap: 0 !important; }

  .gap-md-1 {
    gap: 0.25rem !important; }

  .gap-md-2 {
    gap: 0.5rem !important; }

  .gap-md-3 {
    gap: 1rem !important; }

  .gap-md-4 {
    gap: 1.5rem !important; }

  .gap-md-5 {
    gap: 3rem !important; }

  .text-md-start {
    text-align: left !important; }

  .text-md-end {
    text-align: right !important; }

  .text-md-center {
    text-align: center !important; } }
@media (min-width: 992px) {
  .float-lg-start {
    float: left !important; }

  .float-lg-end {
    float: right !important; }

  .float-lg-none {
    float: none !important; }

  .d-lg-inline {
    display: inline !important; }

  .d-lg-inline-block {
    display: inline-block !important; }

  .d-lg-block {
    display: block !important; }

  .d-lg-grid {
    display: grid !important; }

  .d-lg-table {
    display: table !important; }

  .d-lg-table-row {
    display: table-row !important; }

  .d-lg-table-cell {
    display: table-cell !important; }

  .d-lg-flex {
    display: flex !important; }

  .d-lg-inline-flex {
    display: inline-flex !important; }

  .d-lg-none {
    display: none !important; }

  .flex-lg-fill {
    flex: 1 1 auto !important; }

  .flex-lg-row {
    flex-direction: row !important; }

  .flex-lg-column {
    flex-direction: column !important; }

  .flex-lg-row-reverse {
    flex-direction: row-reverse !important; }

  .flex-lg-column-reverse {
    flex-direction: column-reverse !important; }

  .flex-lg-grow-0 {
    flex-grow: 0 !important; }

  .flex-lg-grow-1 {
    flex-grow: 1 !important; }

  .flex-lg-shrink-0 {
    flex-shrink: 0 !important; }

  .flex-lg-shrink-1 {
    flex-shrink: 1 !important; }

  .flex-lg-wrap {
    flex-wrap: wrap !important; }

  .flex-lg-nowrap {
    flex-wrap: nowrap !important; }

  .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important; }

  .justify-content-lg-start {
    justify-content: flex-start !important; }

  .justify-content-lg-end {
    justify-content: flex-end !important; }

  .justify-content-lg-center {
    justify-content: center !important; }

  .justify-content-lg-between {
    justify-content: space-between !important; }

  .justify-content-lg-around {
    justify-content: space-around !important; }

  .justify-content-lg-evenly {
    justify-content: space-evenly !important; }

  .align-items-lg-start {
    align-items: flex-start !important; }

  .align-items-lg-end {
    align-items: flex-end !important; }

  .align-items-lg-center {
    align-items: center !important; }

  .align-items-lg-baseline {
    align-items: baseline !important; }

  .align-items-lg-stretch {
    align-items: stretch !important; }

  .align-content-lg-start {
    align-content: flex-start !important; }

  .align-content-lg-end {
    align-content: flex-end !important; }

  .align-content-lg-center {
    align-content: center !important; }

  .align-content-lg-between {
    align-content: space-between !important; }

  .align-content-lg-around {
    align-content: space-around !important; }

  .align-content-lg-stretch {
    align-content: stretch !important; }

  .align-self-lg-auto {
    align-self: auto !important; }

  .align-self-lg-start {
    align-self: flex-start !important; }

  .align-self-lg-end {
    align-self: flex-end !important; }

  .align-self-lg-center {
    align-self: center !important; }

  .align-self-lg-baseline {
    align-self: baseline !important; }

  .align-self-lg-stretch {
    align-self: stretch !important; }

  .order-lg-first {
    order: -1 !important; }

  .order-lg-0 {
    order: 0 !important; }

  .order-lg-1 {
    order: 1 !important; }

  .order-lg-2 {
    order: 2 !important; }

  .order-lg-3 {
    order: 3 !important; }

  .order-lg-4 {
    order: 4 !important; }

  .order-lg-5 {
    order: 5 !important; }

  .order-lg-last {
    order: 6 !important; }

  .m-lg-0 {
    margin: 0 !important; }

  .m-lg-1 {
    margin: 0.25rem !important; }

  .m-lg-2 {
    margin: 0.5rem !important; }

  .m-lg-3 {
    margin: 1rem !important; }

  .m-lg-4 {
    margin: 1.5rem !important; }

  .m-lg-5 {
    margin: 3rem !important; }

  .m-lg-auto {
    margin: auto !important; }

  .mx-lg-0 {
    margin-right: 0 !important;
    margin-left: 0 !important; }

  .mx-lg-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important; }

  .mx-lg-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important; }

  .mx-lg-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important; }

  .mx-lg-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important; }

  .mx-lg-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important; }

  .mx-lg-auto {
    margin-right: auto !important;
    margin-left: auto !important; }

  .my-lg-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important; }

  .my-lg-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important; }

  .my-lg-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important; }

  .my-lg-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important; }

  .my-lg-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important; }

  .my-lg-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important; }

  .my-lg-auto {
    margin-top: auto !important;
    margin-bottom: auto !important; }

  .mt-lg-0 {
    margin-top: 0 !important; }

  .mt-lg-1 {
    margin-top: 0.25rem !important; }

  .mt-lg-2 {
    margin-top: 0.5rem !important; }

  .mt-lg-3 {
    margin-top: 1rem !important; }

  .mt-lg-4 {
    margin-top: 1.5rem !important; }

  .mt-lg-5 {
    margin-top: 3rem !important; }

  .mt-lg-auto {
    margin-top: auto !important; }

  .me-lg-0 {
    margin-right: 0 !important; }

  .me-lg-1 {
    margin-right: 0.25rem !important; }

  .me-lg-2 {
    margin-right: 0.5rem !important; }

  .me-lg-3 {
    margin-right: 1rem !important; }

  .me-lg-4 {
    margin-right: 1.5rem !important; }

  .me-lg-5 {
    margin-right: 3rem !important; }

  .me-lg-auto {
    margin-right: auto !important; }

  .mb-lg-0 {
    margin-bottom: 0 !important; }

  .mb-lg-1 {
    margin-bottom: 0.25rem !important; }

  .mb-lg-2 {
    margin-bottom: 0.5rem !important; }

  .mb-lg-3 {
    margin-bottom: 1rem !important; }

  .mb-lg-4 {
    margin-bottom: 1.5rem !important; }

  .mb-lg-5 {
    margin-bottom: 3rem !important; }

  .mb-lg-auto {
    margin-bottom: auto !important; }

  .ms-lg-0 {
    margin-left: 0 !important; }

  .ms-lg-1 {
    margin-left: 0.25rem !important; }

  .ms-lg-2 {
    margin-left: 0.5rem !important; }

  .ms-lg-3 {
    margin-left: 1rem !important; }

  .ms-lg-4 {
    margin-left: 1.5rem !important; }

  .ms-lg-5 {
    margin-left: 3rem !important; }

  .ms-lg-auto {
    margin-left: auto !important; }

  .p-lg-0 {
    padding: 0 !important; }

  .p-lg-1 {
    padding: 0.25rem !important; }

  .p-lg-2 {
    padding: 0.5rem !important; }

  .p-lg-3 {
    padding: 1rem !important; }

  .p-lg-4 {
    padding: 1.5rem !important; }

  .p-lg-5 {
    padding: 3rem !important; }

  .px-lg-0 {
    padding-right: 0 !important;
    padding-left: 0 !important; }

  .px-lg-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important; }

  .px-lg-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important; }

  .px-lg-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important; }

  .px-lg-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important; }

  .px-lg-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important; }

  .py-lg-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important; }

  .py-lg-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important; }

  .py-lg-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important; }

  .py-lg-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important; }

  .py-lg-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important; }

  .py-lg-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important; }

  .pt-lg-0 {
    padding-top: 0 !important; }

  .pt-lg-1 {
    padding-top: 0.25rem !important; }

  .pt-lg-2 {
    padding-top: 0.5rem !important; }

  .pt-lg-3 {
    padding-top: 1rem !important; }

  .pt-lg-4 {
    padding-top: 1.5rem !important; }

  .pt-lg-5 {
    padding-top: 3rem !important; }

  .pe-lg-0 {
    padding-right: 0 !important; }

  .pe-lg-1 {
    padding-right: 0.25rem !important; }

  .pe-lg-2 {
    padding-right: 0.5rem !important; }

  .pe-lg-3 {
    padding-right: 1rem !important; }

  .pe-lg-4 {
    padding-right: 1.5rem !important; }

  .pe-lg-5 {
    padding-right: 3rem !important; }

  .pb-lg-0 {
    padding-bottom: 0 !important; }

  .pb-lg-1 {
    padding-bottom: 0.25rem !important; }

  .pb-lg-2 {
    padding-bottom: 0.5rem !important; }

  .pb-lg-3 {
    padding-bottom: 1rem !important; }

  .pb-lg-4 {
    padding-bottom: 1.5rem !important; }

  .pb-lg-5 {
    padding-bottom: 3rem !important; }

  .ps-lg-0 {
    padding-left: 0 !important; }

  .ps-lg-1 {
    padding-left: 0.25rem !important; }

  .ps-lg-2 {
    padding-left: 0.5rem !important; }

  .ps-lg-3 {
    padding-left: 1rem !important; }

  .ps-lg-4 {
    padding-left: 1.5rem !important; }

  .ps-lg-5 {
    padding-left: 3rem !important; }

  .gap-lg-0 {
    gap: 0 !important; }

  .gap-lg-1 {
    gap: 0.25rem !important; }

  .gap-lg-2 {
    gap: 0.5rem !important; }

  .gap-lg-3 {
    gap: 1rem !important; }

  .gap-lg-4 {
    gap: 1.5rem !important; }

  .gap-lg-5 {
    gap: 3rem !important; }

  .text-lg-start {
    text-align: left !important; }

  .text-lg-end {
    text-align: right !important; }

  .text-lg-center {
    text-align: center !important; } }
@media (min-width: 1200px) {
  .float-xl-start {
    float: left !important; }

  .float-xl-end {
    float: right !important; }

  .float-xl-none {
    float: none !important; }

  .d-xl-inline {
    display: inline !important; }

  .d-xl-inline-block {
    display: inline-block !important; }

  .d-xl-block {
    display: block !important; }

  .d-xl-grid {
    display: grid !important; }

  .d-xl-table {
    display: table !important; }

  .d-xl-table-row {
    display: table-row !important; }

  .d-xl-table-cell {
    display: table-cell !important; }

  .d-xl-flex {
    display: flex !important; }

  .d-xl-inline-flex {
    display: inline-flex !important; }

  .d-xl-none {
    display: none !important; }

  .flex-xl-fill {
    flex: 1 1 auto !important; }

  .flex-xl-row {
    flex-direction: row !important; }

  .flex-xl-column {
    flex-direction: column !important; }

  .flex-xl-row-reverse {
    flex-direction: row-reverse !important; }

  .flex-xl-column-reverse {
    flex-direction: column-reverse !important; }

  .flex-xl-grow-0 {
    flex-grow: 0 !important; }

  .flex-xl-grow-1 {
    flex-grow: 1 !important; }

  .flex-xl-shrink-0 {
    flex-shrink: 0 !important; }

  .flex-xl-shrink-1 {
    flex-shrink: 1 !important; }

  .flex-xl-wrap {
    flex-wrap: wrap !important; }

  .flex-xl-nowrap {
    flex-wrap: nowrap !important; }

  .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important; }

  .justify-content-xl-start {
    justify-content: flex-start !important; }

  .justify-content-xl-end {
    justify-content: flex-end !important; }

  .justify-content-xl-center {
    justify-content: center !important; }

  .justify-content-xl-between {
    justify-content: space-between !important; }

  .justify-content-xl-around {
    justify-content: space-around !important; }

  .justify-content-xl-evenly {
    justify-content: space-evenly !important; }

  .align-items-xl-start {
    align-items: flex-start !important; }

  .align-items-xl-end {
    align-items: flex-end !important; }

  .align-items-xl-center {
    align-items: center !important; }

  .align-items-xl-baseline {
    align-items: baseline !important; }

  .align-items-xl-stretch {
    align-items: stretch !important; }

  .align-content-xl-start {
    align-content: flex-start !important; }

  .align-content-xl-end {
    align-content: flex-end !important; }

  .align-content-xl-center {
    align-content: center !important; }

  .align-content-xl-between {
    align-content: space-between !important; }

  .align-content-xl-around {
    align-content: space-around !important; }

  .align-content-xl-stretch {
    align-content: stretch !important; }

  .align-self-xl-auto {
    align-self: auto !important; }

  .align-self-xl-start {
    align-self: flex-start !important; }

  .align-self-xl-end {
    align-self: flex-end !important; }

  .align-self-xl-center {
    align-self: center !important; }

  .align-self-xl-baseline {
    align-self: baseline !important; }

  .align-self-xl-stretch {
    align-self: stretch !important; }

  .order-xl-first {
    order: -1 !important; }

  .order-xl-0 {
    order: 0 !important; }

  .order-xl-1 {
    order: 1 !important; }

  .order-xl-2 {
    order: 2 !important; }

  .order-xl-3 {
    order: 3 !important; }

  .order-xl-4 {
    order: 4 !important; }

  .order-xl-5 {
    order: 5 !important; }

  .order-xl-last {
    order: 6 !important; }

  .m-xl-0 {
    margin: 0 !important; }

  .m-xl-1 {
    margin: 0.25rem !important; }

  .m-xl-2 {
    margin: 0.5rem !important; }

  .m-xl-3 {
    margin: 1rem !important; }

  .m-xl-4 {
    margin: 1.5rem !important; }

  .m-xl-5 {
    margin: 3rem !important; }

  .m-xl-auto {
    margin: auto !important; }

  .mx-xl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important; }

  .mx-xl-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important; }

  .mx-xl-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important; }

  .mx-xl-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important; }

  .mx-xl-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important; }

  .mx-xl-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important; }

  .mx-xl-auto {
    margin-right: auto !important;
    margin-left: auto !important; }

  .my-xl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important; }

  .my-xl-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important; }

  .my-xl-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important; }

  .my-xl-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important; }

  .my-xl-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important; }

  .my-xl-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important; }

  .my-xl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important; }

  .mt-xl-0 {
    margin-top: 0 !important; }

  .mt-xl-1 {
    margin-top: 0.25rem !important; }

  .mt-xl-2 {
    margin-top: 0.5rem !important; }

  .mt-xl-3 {
    margin-top: 1rem !important; }

  .mt-xl-4 {
    margin-top: 1.5rem !important; }

  .mt-xl-5 {
    margin-top: 3rem !important; }

  .mt-xl-auto {
    margin-top: auto !important; }

  .me-xl-0 {
    margin-right: 0 !important; }

  .me-xl-1 {
    margin-right: 0.25rem !important; }

  .me-xl-2 {
    margin-right: 0.5rem !important; }

  .me-xl-3 {
    margin-right: 1rem !important; }

  .me-xl-4 {
    margin-right: 1.5rem !important; }

  .me-xl-5 {
    margin-right: 3rem !important; }

  .me-xl-auto {
    margin-right: auto !important; }

  .mb-xl-0 {
    margin-bottom: 0 !important; }

  .mb-xl-1 {
    margin-bottom: 0.25rem !important; }

  .mb-xl-2 {
    margin-bottom: 0.5rem !important; }

  .mb-xl-3 {
    margin-bottom: 1rem !important; }

  .mb-xl-4 {
    margin-bottom: 1.5rem !important; }

  .mb-xl-5 {
    margin-bottom: 3rem !important; }

  .mb-xl-auto {
    margin-bottom: auto !important; }

  .ms-xl-0 {
    margin-left: 0 !important; }

  .ms-xl-1 {
    margin-left: 0.25rem !important; }

  .ms-xl-2 {
    margin-left: 0.5rem !important; }

  .ms-xl-3 {
    margin-left: 1rem !important; }

  .ms-xl-4 {
    margin-left: 1.5rem !important; }

  .ms-xl-5 {
    margin-left: 3rem !important; }

  .ms-xl-auto {
    margin-left: auto !important; }

  .p-xl-0 {
    padding: 0 !important; }

  .p-xl-1 {
    padding: 0.25rem !important; }

  .p-xl-2 {
    padding: 0.5rem !important; }

  .p-xl-3 {
    padding: 1rem !important; }

  .p-xl-4 {
    padding: 1.5rem !important; }

  .p-xl-5 {
    padding: 3rem !important; }

  .px-xl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important; }

  .px-xl-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important; }

  .px-xl-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important; }

  .px-xl-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important; }

  .px-xl-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important; }

  .px-xl-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important; }

  .py-xl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important; }

  .py-xl-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important; }

  .py-xl-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important; }

  .py-xl-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important; }

  .py-xl-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important; }

  .py-xl-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important; }

  .pt-xl-0 {
    padding-top: 0 !important; }

  .pt-xl-1 {
    padding-top: 0.25rem !important; }

  .pt-xl-2 {
    padding-top: 0.5rem !important; }

  .pt-xl-3 {
    padding-top: 1rem !important; }

  .pt-xl-4 {
    padding-top: 1.5rem !important; }

  .pt-xl-5 {
    padding-top: 3rem !important; }

  .pe-xl-0 {
    padding-right: 0 !important; }

  .pe-xl-1 {
    padding-right: 0.25rem !important; }

  .pe-xl-2 {
    padding-right: 0.5rem !important; }

  .pe-xl-3 {
    padding-right: 1rem !important; }

  .pe-xl-4 {
    padding-right: 1.5rem !important; }

  .pe-xl-5 {
    padding-right: 3rem !important; }

  .pb-xl-0 {
    padding-bottom: 0 !important; }

  .pb-xl-1 {
    padding-bottom: 0.25rem !important; }

  .pb-xl-2 {
    padding-bottom: 0.5rem !important; }

  .pb-xl-3 {
    padding-bottom: 1rem !important; }

  .pb-xl-4 {
    padding-bottom: 1.5rem !important; }

  .pb-xl-5 {
    padding-bottom: 3rem !important; }

  .ps-xl-0 {
    padding-left: 0 !important; }

  .ps-xl-1 {
    padding-left: 0.25rem !important; }

  .ps-xl-2 {
    padding-left: 0.5rem !important; }

  .ps-xl-3 {
    padding-left: 1rem !important; }

  .ps-xl-4 {
    padding-left: 1.5rem !important; }

  .ps-xl-5 {
    padding-left: 3rem !important; }

  .gap-xl-0 {
    gap: 0 !important; }

  .gap-xl-1 {
    gap: 0.25rem !important; }

  .gap-xl-2 {
    gap: 0.5rem !important; }

  .gap-xl-3 {
    gap: 1rem !important; }

  .gap-xl-4 {
    gap: 1.5rem !important; }

  .gap-xl-5 {
    gap: 3rem !important; }

  .text-xl-start {
    text-align: left !important; }

  .text-xl-end {
    text-align: right !important; }

  .text-xl-center {
    text-align: center !important; } }
@media (min-width: 1400px) {
  .float-xxl-start {
    float: left !important; }

  .float-xxl-end {
    float: right !important; }

  .float-xxl-none {
    float: none !important; }

  .d-xxl-inline {
    display: inline !important; }

  .d-xxl-inline-block {
    display: inline-block !important; }

  .d-xxl-block {
    display: block !important; }

  .d-xxl-grid {
    display: grid !important; }

  .d-xxl-table {
    display: table !important; }

  .d-xxl-table-row {
    display: table-row !important; }

  .d-xxl-table-cell {
    display: table-cell !important; }

  .d-xxl-flex {
    display: flex !important; }

  .d-xxl-inline-flex {
    display: inline-flex !important; }

  .d-xxl-none {
    display: none !important; }

  .flex-xxl-fill {
    flex: 1 1 auto !important; }

  .flex-xxl-row {
    flex-direction: row !important; }

  .flex-xxl-column {
    flex-direction: column !important; }

  .flex-xxl-row-reverse {
    flex-direction: row-reverse !important; }

  .flex-xxl-column-reverse {
    flex-direction: column-reverse !important; }

  .flex-xxl-grow-0 {
    flex-grow: 0 !important; }

  .flex-xxl-grow-1 {
    flex-grow: 1 !important; }

  .flex-xxl-shrink-0 {
    flex-shrink: 0 !important; }

  .flex-xxl-shrink-1 {
    flex-shrink: 1 !important; }

  .flex-xxl-wrap {
    flex-wrap: wrap !important; }

  .flex-xxl-nowrap {
    flex-wrap: nowrap !important; }

  .flex-xxl-wrap-reverse {
    flex-wrap: wrap-reverse !important; }

  .justify-content-xxl-start {
    justify-content: flex-start !important; }

  .justify-content-xxl-end {
    justify-content: flex-end !important; }

  .justify-content-xxl-center {
    justify-content: center !important; }

  .justify-content-xxl-between {
    justify-content: space-between !important; }

  .justify-content-xxl-around {
    justify-content: space-around !important; }

  .justify-content-xxl-evenly {
    justify-content: space-evenly !important; }

  .align-items-xxl-start {
    align-items: flex-start !important; }

  .align-items-xxl-end {
    align-items: flex-end !important; }

  .align-items-xxl-center {
    align-items: center !important; }

  .align-items-xxl-baseline {
    align-items: baseline !important; }

  .align-items-xxl-stretch {
    align-items: stretch !important; }

  .align-content-xxl-start {
    align-content: flex-start !important; }

  .align-content-xxl-end {
    align-content: flex-end !important; }

  .align-content-xxl-center {
    align-content: center !important; }

  .align-content-xxl-between {
    align-content: space-between !important; }

  .align-content-xxl-around {
    align-content: space-around !important; }

  .align-content-xxl-stretch {
    align-content: stretch !important; }

  .align-self-xxl-auto {
    align-self: auto !important; }

  .align-self-xxl-start {
    align-self: flex-start !important; }

  .align-self-xxl-end {
    align-self: flex-end !important; }

  .align-self-xxl-center {
    align-self: center !important; }

  .align-self-xxl-baseline {
    align-self: baseline !important; }

  .align-self-xxl-stretch {
    align-self: stretch !important; }

  .order-xxl-first {
    order: -1 !important; }

  .order-xxl-0 {
    order: 0 !important; }

  .order-xxl-1 {
    order: 1 !important; }

  .order-xxl-2 {
    order: 2 !important; }

  .order-xxl-3 {
    order: 3 !important; }

  .order-xxl-4 {
    order: 4 !important; }

  .order-xxl-5 {
    order: 5 !important; }

  .order-xxl-last {
    order: 6 !important; }

  .m-xxl-0 {
    margin: 0 !important; }

  .m-xxl-1 {
    margin: 0.25rem !important; }

  .m-xxl-2 {
    margin: 0.5rem !important; }

  .m-xxl-3 {
    margin: 1rem !important; }

  .m-xxl-4 {
    margin: 1.5rem !important; }

  .m-xxl-5 {
    margin: 3rem !important; }

  .m-xxl-auto {
    margin: auto !important; }

  .mx-xxl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important; }

  .mx-xxl-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important; }

  .mx-xxl-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important; }

  .mx-xxl-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important; }

  .mx-xxl-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important; }

  .mx-xxl-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important; }

  .mx-xxl-auto {
    margin-right: auto !important;
    margin-left: auto !important; }

  .my-xxl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important; }

  .my-xxl-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important; }

  .my-xxl-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important; }

  .my-xxl-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important; }

  .my-xxl-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important; }

  .my-xxl-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important; }

  .my-xxl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important; }

  .mt-xxl-0 {
    margin-top: 0 !important; }

  .mt-xxl-1 {
    margin-top: 0.25rem !important; }

  .mt-xxl-2 {
    margin-top: 0.5rem !important; }

  .mt-xxl-3 {
    margin-top: 1rem !important; }

  .mt-xxl-4 {
    margin-top: 1.5rem !important; }

  .mt-xxl-5 {
    margin-top: 3rem !important; }

  .mt-xxl-auto {
    margin-top: auto !important; }

  .me-xxl-0 {
    margin-right: 0 !important; }

  .me-xxl-1 {
    margin-right: 0.25rem !important; }

  .me-xxl-2 {
    margin-right: 0.5rem !important; }

  .me-xxl-3 {
    margin-right: 1rem !important; }

  .me-xxl-4 {
    margin-right: 1.5rem !important; }

  .me-xxl-5 {
    margin-right: 3rem !important; }

  .me-xxl-auto {
    margin-right: auto !important; }

  .mb-xxl-0 {
    margin-bottom: 0 !important; }

  .mb-xxl-1 {
    margin-bottom: 0.25rem !important; }

  .mb-xxl-2 {
    margin-bottom: 0.5rem !important; }

  .mb-xxl-3 {
    margin-bottom: 1rem !important; }

  .mb-xxl-4 {
    margin-bottom: 1.5rem !important; }

  .mb-xxl-5 {
    margin-bottom: 3rem !important; }

  .mb-xxl-auto {
    margin-bottom: auto !important; }

  .ms-xxl-0 {
    margin-left: 0 !important; }

  .ms-xxl-1 {
    margin-left: 0.25rem !important; }

  .ms-xxl-2 {
    margin-left: 0.5rem !important; }

  .ms-xxl-3 {
    margin-left: 1rem !important; }

  .ms-xxl-4 {
    margin-left: 1.5rem !important; }

  .ms-xxl-5 {
    margin-left: 3rem !important; }

  .ms-xxl-auto {
    margin-left: auto !important; }

  .p-xxl-0 {
    padding: 0 !important; }

  .p-xxl-1 {
    padding: 0.25rem !important; }

  .p-xxl-2 {
    padding: 0.5rem !important; }

  .p-xxl-3 {
    padding: 1rem !important; }

  .p-xxl-4 {
    padding: 1.5rem !important; }

  .p-xxl-5 {
    padding: 3rem !important; }

  .px-xxl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important; }

  .px-xxl-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important; }

  .px-xxl-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important; }

  .px-xxl-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important; }

  .px-xxl-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important; }

  .px-xxl-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important; }

  .py-xxl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important; }

  .py-xxl-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important; }

  .py-xxl-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important; }

  .py-xxl-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important; }

  .py-xxl-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important; }

  .py-xxl-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important; }

  .pt-xxl-0 {
    padding-top: 0 !important; }

  .pt-xxl-1 {
    padding-top: 0.25rem !important; }

  .pt-xxl-2 {
    padding-top: 0.5rem !important; }

  .pt-xxl-3 {
    padding-top: 1rem !important; }

  .pt-xxl-4 {
    padding-top: 1.5rem !important; }

  .pt-xxl-5 {
    padding-top: 3rem !important; }

  .pe-xxl-0 {
    padding-right: 0 !important; }

  .pe-xxl-1 {
    padding-right: 0.25rem !important; }

  .pe-xxl-2 {
    padding-right: 0.5rem !important; }

  .pe-xxl-3 {
    padding-right: 1rem !important; }

  .pe-xxl-4 {
    padding-right: 1.5rem !important; }

  .pe-xxl-5 {
    padding-right: 3rem !important; }

  .pb-xxl-0 {
    padding-bottom: 0 !important; }

  .pb-xxl-1 {
    padding-bottom: 0.25rem !important; }

  .pb-xxl-2 {
    padding-bottom: 0.5rem !important; }

  .pb-xxl-3 {
    padding-bottom: 1rem !important; }

  .pb-xxl-4 {
    padding-bottom: 1.5rem !important; }

  .pb-xxl-5 {
    padding-bottom: 3rem !important; }

  .ps-xxl-0 {
    padding-left: 0 !important; }

  .ps-xxl-1 {
    padding-left: 0.25rem !important; }

  .ps-xxl-2 {
    padding-left: 0.5rem !important; }

  .ps-xxl-3 {
    padding-left: 1rem !important; }

  .ps-xxl-4 {
    padding-left: 1.5rem !important; }

  .ps-xxl-5 {
    padding-left: 3rem !important; }

  .gap-xxl-0 {
    gap: 0 !important; }

  .gap-xxl-1 {
    gap: 0.25rem !important; }

  .gap-xxl-2 {
    gap: 0.5rem !important; }

  .gap-xxl-3 {
    gap: 1rem !important; }

  .gap-xxl-4 {
    gap: 1.5rem !important; }

  .gap-xxl-5 {
    gap: 3rem !important; }

  .text-xxl-start {
    text-align: left !important; }

  .text-xxl-end {
    text-align: right !important; }

  .text-xxl-center {
    text-align: center !important; } }
@media (min-width: 1200px) {
  .fs-1 {
    font-size: 2.5rem !important; }

  .fs-2 {
    font-size: 2rem !important; }

  .fs-3 {
    font-size: 1.75rem !important; }

  .fs-4 {
    font-size: 1.5rem !important; } }
@media print {
  .d-print-inline {
    display: inline !important; }

  .d-print-inline-block {
    display: inline-block !important; }

  .d-print-block {
    display: block !important; }

  .d-print-grid {
    display: grid !important; }

  .d-print-table {
    display: table !important; }

  .d-print-table-row {
    display: table-row !important; }

  .d-print-table-cell {
    display: table-cell !important; }

  .d-print-flex {
    display: flex !important; }

  .d-print-inline-flex {
    display: inline-flex !important; }

  .d-print-none {
    display: none !important; } }
:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

* {
  -webkit-tap-highlight-color: transparent;
  -webkit-font-smoothing: antialiased;
  -webkit-text-size-adjust: 100%;
  box-sizing: border-box;
  outline: none; }

body {
  font-family: "Open Sans", sans-serif;
  background-color: #181717;
  font-weight: 400;
  line-height: 1.3;
  padding: 0;
  margin: 0;
  color: #fff;
  font-size: 14px;
  position: relative;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

.main-container.app-loading {
  display: flex;
  flex-direction: column;
  position: fixed;
  z-index: 1;
  pointer-events: none;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  margin: 0; }
  .main-container.app-loading .loading-logo {
    max-width: 200px;
    opacity: 0.7;
    display: flex;
    flex-direction: row; }
    .main-container.app-loading .loading-logo .logo-name {
      width: 160px;
      margin-left: -30px;
      overflow: visible; }

.accent {
  color: #8dd363; }

.no-scroll {
  overflow: hidden; }

.heading-wrapper {
  position: relative;
  overflow: hidden; }
  .heading-wrapper h1, .heading-wrapper .h1, .heading-wrapper .heading-content {
    font-weight: bold;
    position: absolute;
    top: -100%;
    left: -100%; }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.button-main {
  text-align: left;
  background-color: transparent;
  color: #8dd363;
  line-height: 1em;
  padding: 4px;
  margin: 2px 4px;
  border-left: 1px solid rgba(141, 211, 99, 0.5);
  border-top: 1px solid rgba(141, 211, 99, 0.2);
  border-right: 1px solid rgba(141, 211, 99, 0.5);
  border-bottom: 1px solid rgba(141, 211, 99, 0.2); }
  .button-main.active {
    color: #181717 !important;
    text-decoration: none !important;
    background-color: #8dd363 !important; }
  .button-main:hover {
    color: #181717 !important;
    text-decoration: none !important;
    background-color: #8dd363 !important; }

.btn-primary {
  padding: 6px 16px;
  font-size: 0.875rem;
  min-width: 64px;
  box-sizing: border-box;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  font-weight: 500;
  line-height: 1.75;
  border-radius: 4px;
  letter-spacing: 0.02857em;
  text-transform: uppercase; }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  src: url("/assets/fonts/opensans/open-sans-v16-latin-regular.eot");
  /* IE9 Compat Modes */
  src: local("Open Sans Regular"), local("OpenSans-Regular"), url("/assets/fonts/opensans/open-sans-v16-latin-regular.eot?#iefix") format("embedded-opentype"), url("/assets/fonts/opensans/open-sans-v16-latin-regular.woff2") format("woff2"), url("/assets/fonts/opensans/open-sans-v16-latin-regular.woff") format("woff"), url("/assets/fonts/opensans/open-sans-v16-latin-regular.ttf") format("truetype"), url("/assets/fonts/opensans/open-sans-v16-latin-regular.svg#OpenSans") format("svg");
  /* Legacy iOS */
  font-display: swap; }
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 700;
  src: url("/assets/fonts/opensans/open-sans-v16-latin-700.eot");
  /* IE9 Compat Modes */
  src: local("Open Sans Bold"), local("OpenSans-Bold"), url("/assets/fonts/opensans/open-sans-v16-latin-700.eot?#iefix") format("embedded-opentype"), url("/assets/fonts/opensans/open-sans-v16-latin-700.woff2") format("woff2"), url("/assets/fonts/opensans/open-sans-v16-latin-700.woff") format("woff"), url("/assets/fonts/opensans/open-sans-v16-latin-700.ttf") format("truetype"), url("/assets/fonts/opensans/open-sans-v16-latin-700.svg#OpenSans") format("svg");
  /* Legacy iOS */
  font-display: swap; }
@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 800;
  src: url("/assets/fonts/opensans/open-sans-v16-latin-800.eot");
  /* IE9 Compat Modes */
  src: local("Open Sans ExtraBold"), local("OpenSans-ExtraBold"), url("/assets/fonts/opensans/open-sans-v16-latin-800.eot?#iefix") format("embedded-opentype"), url("/assets/fonts/opensans/open-sans-v16-latin-800.woff2") format("woff2"), url("/assets/fonts/opensans/open-sans-v16-latin-800.woff") format("woff"), url("/assets/fonts/opensans/open-sans-v16-latin-800.ttf") format("truetype"), url("/assets/fonts/opensans/open-sans-v16-latin-800.svg#OpenSans") format("svg");
  /* Legacy iOS */
  font-display: swap; }
@font-face {
  font-family: 'Oswald';
  font-style: normal;
  font-weight: 400;
  src: url("/assets/fonts/oswald/oswald-v17-latin-regular.eot");
  /* IE9 Compat Modes */
  src: local("Oswald Regular"), local("Oswald-Regular"), url("/assets/fonts/oswald/oswald-v17-latin-regular.eot?#iefix") format("embedded-opentype"), url("/assets/fonts/oswald/oswald-v17-latin-regular.woff2") format("woff2"), url("/assets/fonts/oswald/oswald-v17-latin-regular.woff") format("woff"), url("/assets/fonts/oswald/oswald-v17-latin-regular.ttf") format("truetype"), url("/assets/fonts/oswald/oswald-v17-latin-regular.svg#Oswald") format("svg");
  /* Legacy iOS */
  font-display: swap; }
@font-face {
  font-family: 'Roboto';
  src: local("Roboto Regular"), url("/assets/fonts/roboto/Roboto-Regular.ttf") format("truetype"); }
@font-face {
  font-family: 'Roboto Black';
  src: local("Roboto Black"), url("/assets/fonts/roboto/Roboto-Black.ttf") format("truetype"); }
:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.line-divider {
  margin-top: 24px;
  margin-bottom: 16px;
  border-bottom: 1px dashed rgba(255, 255, 255, 0.85);
  flex-shrink: 0;
  background-color: transparent; }

.invalid-feedback {
  background-color: #ff4136;
  font-size: 1rem;
  color: #fff;
  padding: 8px; }

.sign-in-footer {
  display: flex;
  -webkit-box-pack: justify;
  justify-content: space-between;
  margin: 0;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0.00938em; }

.flip-countdown {
  text-align: center;
  perspective: 400px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }
  .flip-countdown *,
  .flip-countdown *:before,
  .flip-countdown *:after {
    box-sizing: border-box; }
  .flip-countdown .flip-countdown-piece {
    display: inline-block;
    margin: 0 0.42em; }
    .flip-countdown .flip-countdown-piece .flip-countdown-title {
      font-size: 2vw; }
    .flip-countdown .flip-countdown-piece .flip-countdown-card {
      display: flex; }
      .flip-countdown .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec {
        display: block;
        position: relative;
        margin: 0 0.04em;
        padding-bottom: 0.52em;
        font-size: 9vw;
        line-height: 0.95;
        border-radius: 0.15em;
        box-shadow: 0 3px 4px 0 rgba(0, 0, 0, 0.2), inset 2px 4px 0 0 rgba(255, 255, 255, 0.08); }
        .flip-countdown .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec .card__top,
        .flip-countdown .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec .card__bottom,
        .flip-countdown .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec .card__back::before,
        .flip-countdown .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec .card__back::after {
          display: block;
          height: 0.52em;
          color: #ccc;
          background: #222;
          padding: 0 0.1em;
          border-radius: 0.15em 0.15em 0 0;
          transform-style: preserve-3d;
          transform: translateZ(0); }
        .flip-countdown .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec .card__top {
          border-bottom: solid 1px #000; }
        .flip-countdown .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec .card__bottom {
          color: #fff;
          position: absolute;
          top: 50%;
          left: 0;
          background: #393939;
          border-radius: 0 0 0.15em 0.15em;
          pointer-events: none;
          overflow: hidden; }
          .flip-countdown .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec .card__bottom::after {
            display: block;
            margin-top: -0.52em;
            content: attr(data-value); }
        .flip-countdown .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec .card__back {
          position: absolute;
          top: 0;
          height: 100%;
          left: 0;
          pointer-events: none; }
          .flip-countdown .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec .card__back::before {
            position: relative;
            content: attr(data-value);
            z-index: -1;
            overflow: hidden; }
        .flip-countdown .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec.flip .card__back .card__bottom {
          transform-origin: center top;
          animation-fill-mode: both;
          animation: flipBottom 0.6s cubic-bezier(0.15, 0.45, 0.28, 1); }
        .flip-countdown .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec.flip .card__back::before {
          animation: flipTop 0.3s cubic-bezier(0.37, 0.01, 0.94, 0.35);
          animation-fill-mode: both;
          transform-origin: center bottom; }
  .flip-countdown .flip-countdown-card-divider {
    font-size: 2vw; }
  .flip-countdown.theme-light .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec .card__top,
  .flip-countdown.theme-light .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec .card__bottom,
  .flip-countdown.theme-light .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec .card__back::before,
  .flip-countdown.theme-light .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec .card__back::after {
    color: #de4848;
    background: #f7f7f7; }
  .flip-countdown.theme-light .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec .card__top {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1); }
  .flip-countdown.theme-light .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec .card__bottom {
    color: #de4848;
    background: #ffffff; }
  .flip-countdown.size-medium .flip-countdown-piece {
    margin: 0 0.42em; }
    .flip-countdown.size-medium .flip-countdown-piece .flip-countdown-title {
      font-size: 24px; }
    .flip-countdown.size-medium .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec {
      margin: 0 0.025em;
      font-size: 72px; }
  .flip-countdown.size-small .flip-countdown-piece {
    margin: 0 0.25em; }
    .flip-countdown.size-small .flip-countdown-piece .flip-countdown-title {
      font-size: 14px; }
    .flip-countdown.size-small .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec {
      margin: 0 0.023em;
      font-size: 32px; }
  .flip-countdown.size-extra-small .flip-countdown-piece {
    margin: 0 0.15em; }
    .flip-countdown.size-extra-small .flip-countdown-piece .flip-countdown-title {
      font-size: 10px; }
    .flip-countdown.size-extra-small .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec {
      margin: 0 0.025em;
      font-size: 20px; }

@keyframes flipTop {
  0% {
    transform: rotateX(0deg);
    z-index: 2; }
  0%,
  99% {
    opacity: 0.99; }
  100% {
    transform: rotateX(-90deg);
    opacity: 0; } }
@keyframes flipBottom {
  0%,
  50% {
    z-index: -1;
    transform: rotateX(90deg);
    opacity: 0; }
  51% {
    opacity: 0.99; }
  100% {
    opacity: 0.99;
    transform: rotateX(0deg);
    z-index: 5; } }
:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.device-desktop .footer .wrap.open {
  overflow: hidden !important; }
.device-desktop .footer.open {
  height: 400px; }

.device-mobile .footer.open {
  bottom: 0;
  top: 60px;
  height: calc(100vh - 60px); }

.footer {
  border-top: 2px solid rgba(255, 255, 255, 0.1);
  text-transform: uppercase;
  padding-top: 80px;
  padding-bottom: 80px;
  color: #aea8a8;
  transition: all 0.2s ease-in-out;
  background-color: #181717; }
  .footer.window-fixed {
    position: fixed;
    width: 100%;
    left: 0;
    right: 0;
    bottom: 0;
    height: 50px;
    padding: 0;
    z-index: 1001; }
    .footer.window-fixed.open {
      padding-top: 80px;
      padding-bottom: 80px; }
      .footer.window-fixed.open .show_all {
        background: none; }
  .footer .wrap.open {
    overflow: auto;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0; }
  .footer .show_all {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50px;
    display: flex;
    justify-content: space-around;
    background-color: #181717;
    z-index: 1040;
    align-items: center; }
    .footer .show_all .f_title {
      color: #fff;
      font-size: 14px;
      margin-left: auto;
      display: flex;
      vertical-align: middle;
      opacity: 0.6;
      text-transform: initial;
      justify-content: center;
      align-items: center; }
    .footer .show_all .f_show_btn {
      background-color: #8dd363;
      z-index: 1010;
      opacity: 0.5;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      font-size: 14px;
      margin: 5px auto 5px 15px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center; }
  .footer a {
    color: inherit;
    outline: 0;
    text-decoration: none; }
  .footer__title {
    color: #fff;
    font-size: 22px;
    margin-bottom: 2.63vh;
    display: inline-block;
    padding-top: 10px; }
    .footer__title span {
      color: #fff; }
  @media (min-width: 992px) {
    .footer__menu {
      display: none; } }
  @media (max-width: 992px) {
    .footer__contact, .footer__connected {
      margin-bottom: 4.63vh; } }
  @media (max-width: 812px) and (orientation: landscape) {
    .footer__contact, .footer__connected {
      width: 400px; } }
  @media (max-width: 480px) {
    .footer__contact, .footer__connected {
      width: auto; } }
  .footer__contact div, .footer__connected div {
    display: flex;
    justify-content: space-between; }
  .footer__contact ul, .footer__connected ul {
    padding: 0;
    margin: 0;
    list-style: none; }
    .footer__contact ul li, .footer__connected ul li {
      color: #aea8a8;
      font-size: 14px;
      margin-bottom: 2.31vh;
      text-transform: none;
      transition: all 0.2s ease; }
      @media (min-width: 1921px), (min-height: 1081px) {
        .footer__contact ul li, .footer__connected ul li {
          margin-bottom: 25px; } }
      .footer__contact ul li:last-child, .footer__connected ul li:last-child {
        margin-bottom: 0; }
      .footer__contact ul li a, .footer__connected ul li a {
        color: #fff;
        transition: all 0.2s ease;
        display: flex;
        flex-direction: row;
        align-items: center; }
        .footer__contact ul li a img, .footer__connected ul li a img {
          width: 20px;
          margin: 0 4px;
          filter: invert(200%) sepia(0%) brightness(119%) contrast(122%); }
        .footer__contact ul li a:hover, .footer__connected ul li a:hover {
          color: #8dd363; }
  .footer__connected {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start; }
    .footer__connected ul li {
      padding-left: 0;
      margin-bottom: 10px; }
    @media (max-width: 992px) {
      .footer__connected {
        align-items: flex-start;
        margin-top: 4.63vh;
        height: auto; } }
    .footer__connected .lang {
      margin: 0.5em 0; }
  .footer__social {
    opacity: 0.6;
    margin-bottom: 60px; }
  .footer__copyright {
    margin-top: auto;
    white-space: nowrap;
    margin-top: 60px; }
    .footer__copyright a {
      display: block;
      text-align: right; }
      .footer__copyright a:nth-child(2) {
        margin-bottom: 30px; }

.flicenses {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  width: 100%;
  margin: 0 auto; }
  @media (max-width: 480px), (max-width: 812px) and (orientation: landscape) {
    .flicenses img {
      max-width: 100%;
      max-height: 50px; } }
  .flicenses__item {
    margin-right: 2em;
    margin-bottom: 2em;
    display: flex;
    align-items: center;
    opacity: 0.6;
    max-height: 60px;
    max-width: 220px; }
    .flicenses__item img {
      width: 100%; }
    .flicenses__item:hover {
      filter: sepia(1); }
    @media (max-width: 992px) {
      .flicenses__item {
        margin: 1em; } }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.section {
  margin-bottom: 20px;
  position: relative; }
  @media (min-width: 1024px) {
    .section {
      margin-bottom: 60px; } }
  .section-title {
    font-family: "Oswald", sans-serif;
    text-transform: uppercase;
    display: block;
    letter-spacing: .32em;
    padding: 0 0 3.125vw 0;
    text-align: center;
    font-weight: bold;
    font-size: 19px; }
    @media (min-width: 768px) {
      .section-title {
        font-size: 24px; } }
    @media (min-width: 1200px) {
      .section-title {
        font-size: 38px; } }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.loading-spinner {
  display: flex;
  flex-direction: row;
  justify-content: flex-start; }
  .loading-spinner .pa-spinner {
    margin: 1rem;
    width: 2rem;
    height: 2rem;
    color: #8dd363; }
  .loading-spinner .pa-spinner-text {
    padding: 1rem;
    font-size: 1.5rem;
    color: #8dd363; }

@keyframes logoRotate {
  0% {
    transform: rotateZ(0deg); }
  100% {
    transform: rotateZ(360deg); } }
.logo-spinner {
  width: 40px;
  animation: logoRotate 4s infinite linear; }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.header {
  position: relative;
  min-height: 60px; }
  @media (min-width: 1900px) {
    .header {
      margin-bottom: 50px; } }

.header-nav {
  display: flex;
  padding: 12px 18px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 61px;
  z-index: 1; }
  @media (min-width: 1024px) {
    .header-nav {
      padding: 7px 22px;
      height: 92px; } }

.header-logo {
  display: inline-flex; }
  .header-logo-img {
    display: block;
    height: 37px; }
    @media (min-width: 1024px) {
      .header-logo-img {
        height: 77px; } }

.header-banner {
  position: relative;
  display: flex;
  flex-direction: column;
  margin-left: auto;
  margin-right: auto;
  max-width: 1920px; }
  .header-banner .header-banner {
    margin-bottom: 0; }
  .header-banner:empty {
    display: none; }
  .header-banner-link {
    background: transparent;
    font-size: 0;
    color: transparent;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    cursor: pointer; }
  .header-banner-shadow {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    display: none; }
    @media (min-width: 768px) {
      .header-banner-shadow {
        display: block;
        background: linear-gradient(23deg, #181717, transparent 45%); } }
    .header-banner-shadow:before, .header-banner-shadow:after {
      content: "";
      position: absolute;
      left: 0;
      right: 0; }
    .header-banner-shadow:before {
      top: 0;
      height: 36%;
      background: -webkit-gradient(linear, left bottom, left top, from(transparent), to(rgba(24, 23, 23, 0.9)));
      background: linear-gradient(0deg, transparent 0, rgba(24, 23, 23, 0.9));
      display: none; }
    .header-banner-shadow:after {
      bottom: 0;
      height: 30%;
      background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(rgba(24, 23, 23, 0.98)));
      background: linear-gradient(180deg, transparent 0, rgba(24, 23, 23, 0.98)); }
    @media (min-width: 768px) {
      .show-shadow .header-banner-shadow {
        background: radial-gradient(circle farthest-corner at left, rgba(24, 23, 23, 0.95) 0, transparent 40%); } }
    .show-shadow .header-banner-shadow:after {
      height: 50%; }
    .show-shadow .header-banner-shadow:before {
      display: block; }
  .header-banner-figure {
    position: relative;
    display: block;
    margin-bottom: 0; }
    @media (min-width: 768px) {
      .header-banner-figure {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        top: 0; } }
    .header-banner-figure-img, .header-banner-figure-video {
      display: block;
      width: 100%;
      object-fit: cover;
      object-position: top center; }
      @media (max-width: 767px) {
        .device-landscape .header-banner-figure-img, .device-landscape .header-banner-figure-video {
          max-height: 70vh; } }
      @media (min-width: 768px) {
        .header-banner-figure-img, .header-banner-figure-video {
          height: 100%; } }
    .header-banner-figure-container {
      display: inline; }
  .header-banner-container {
    width: 100%;
    position: relative;
    padding: 20px var(--bs-gutter-x, 0.75rem);
    display: flex;
    flex-direction: column;
    margin-right: auto;
    margin-left: auto; }
    @media (min-width: 768px) {
      .header-banner-container {
        padding: 30px 45px;
        max-width: 1920px;
        flex-grow: 1; } }
    @media (min-width: 1440px) {
      .header-banner-container {
        padding: 30px 60px; } }
  .header-banner-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    margin-bottom: 0;
    z-index: 10; }
    @media (min-width: 768px) {
      .header-banner-content {
        justify-content: center; } }
    .header-banner-content-figure {
      display: flex;
      justify-content: center;
      margin-bottom: 15px; }
      @media (min-width: 768px) {
        .header-banner-content-figure {
          display: flex;
          justify-content: flex-start; } }
      @media (min-width: 1900px) {
        .header-banner-content-figure {
          margin-bottom: 25px; } }
      .header-banner-content-figure-img {
        display: block;
        max-width: 100%;
        max-height: 100px;
        cursor: pointer; }
        @media (min-width: 768px) {
          .header-banner-content-figure-img {
            max-height: 6.927vw; } }
        @media (min-width: 1900px) {
          .header-banner-content-figure-img {
            max-height: 133px; } }
    .header-banner-content-title {
      font-family: "Oswald", sans-serif;
      line-height: 1.1;
      letter-spacing: .14em;
      font-weight: bold;
      margin-bottom: 5px;
      text-shadow: 1px 1px 0 #181717;
      font-size: 19px; }
      @media (min-width: 768px) {
        .header-banner-content-title {
          font-size: 24px; } }
      @media (min-width: 1200px) {
        .header-banner-content-title {
          font-size: 38px; } }
    .header-banner-content-text {
      text-shadow: 1px 1px 0 #181717;
      margin-bottom: 0;
      font-size: 13px; }
      @media (min-width: 768px) {
        .header-banner-content-text {
          font-size: 16px;
          max-width: 39.063vw; } }
      @media (min-width: 1024px) {
        .header-banner-content-text {
          max-width: 34.896vw; } }
      @media (min-width: 1200px) {
        .header-banner-content-text {
          font-size: 23px;
          max-width: 670px; } }
  .header-banner-content.bottom {
    justify-content: flex-end !important; }
  .header-banner-content.static {
    display: inline-flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-end !important; }
    @media (min-width: 768px) {
      .header-banner-content.static {
        max-width: 30.208vw; } }
    @media (min-width: 1900px) {
      .header-banner-content.static {
        max-width: 580px;
        padding: 100px 0; } }
    .header-banner-content.static .header-banner-content-figure {
      margin-bottom: 10px; }
    .header-banner-content.static .header-banner-content-figure-img {
      height: 30.208vw;
      max-height: 115px;
      width: 100%;
      object-fit: contain;
      object-position: 50%; }
      @media (min-width: 768px) {
        .header-banner-content.static .header-banner-content-figure-img {
          max-height: 17.552vw; } }
      @media (min-width: 1900px) {
        .header-banner-content.static .header-banner-content-figure-img {
          max-height: 337px; } }
    .header-banner-content.static .header-banner-content-title {
      font-family: "Oswald", sans-serif;
      line-height: 1.1;
      letter-spacing: .14em;
      text-align: left;
      margin-bottom: 5px;
      text-transform: uppercase;
      text-shadow: 1px 1px 0 #181717; }
    .header-banner-content.static .header-banner-content-text {
      font-family: "Oswald", sans-serif;
      text-align: left;
      margin-bottom: 15px;
      text-shadow: 1px 1px 0 #181717; }
  @media (min-width: 768px) {
    .header-banner-description {
      width: 50vw;
      max-width: 670px; } }
  .header-banner-description-static-title {
    font-family: "Oswald", sans-serif;
    text-transform: uppercase;
    line-height: 0.814;
    text-align: left;
    margin-bottom: 2.396vw;
    letter-spacing: 2px;
    font-size: 38px; }
    .header-banner-description-static-title .header-small {
      text-decoration: none;
      text-transform: none;
      font-size: 24px;
      line-height: 1.4em; }
      .header-banner-description-static-title .header-small.smaller {
        max-width: 300px;
        font-size: 19px; }
    .header-banner-description-static-title.column {
      display: flex;
      flex-direction: column; }
    @media (min-width: 768px) {
      .header-banner-description-static-title {
        font-size: 3em; } }
    @media (min-width: 1900px) {
      .header-banner-description-static-title {
        margin-bottom: 46px; } }
    .header-banner-description-static-title .accent {
      color: #8dd363; }
  .header-banner-description-title {
    font-family: "Roboto", sans-serif;
    text-transform: uppercase;
    font-weight: 400;
    line-height: 1.4;
    text-shadow: 2px 2px 0 #181717, 8px 8px 15px #181717;
    margin-bottom: 2.396vw;
    letter-spacing: 0.14em;
    font-size: 19px; }
    @media (min-width: 768px) {
      .header-banner-description-title {
        font-size: 24px; } }
    @media (min-width: 1200px) {
      .header-banner-description-title {
        font-size: 38px;
        margin-bottom: 46px; } }
    .header-banner-description-title .accent {
      color: #8dd363; }
  .header-banner-description-text, .header-banner-description-link {
    font-weight: 700;
    font-size: 13px;
    line-height: 1.4;
    text-shadow: 2px 2px 0 #181717, 8px 8px 15px #181717; }
    @media (min-width: 768px) {
      .header-banner-description-text, .header-banner-description-link {
        font-size: 20px; } }
    @media (min-width: 1900px) {
      .header-banner-description-text, .header-banner-description-link {
        font-size: 29px; } }
  .header-banner-description-link {
    color: #8dd363; }
  .header-banner-btn, .dropdown .header-banner-btn {
    font-size: 14px;
    min-width: 148px;
    height: 27px;
    padding: 4px;
    margin: 0 16px 18px;
    border: 2px solid #8dd363;
    border-radius: 19px;
    color: #fff;
    font-weight: 700;
    text-transform: uppercase;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: #181717;
    transition: none; }
    .header-banner-btn:after, .dropdown .header-banner-btn:after {
      display: none; }
    @media (min-width: 768px) {
      .header-banner-btn, .dropdown .header-banner-btn {
        background: transparent;
        font-size: 16px;
        min-width: 250px;
        height: 40px;
        padding: 8px;
        margin: 0 0 10px;
        justify-content: center;
        border: none; } }
    @media (min-width: 1024px) {
      .header-banner-btn, .dropdown .header-banner-btn {
        font-size: 20px;
        min-width: 296px;
        margin: 0 0 15px;
        height: 50px; } }
    .header-banner-btn:hover, .header-banner-btn:active, .header-banner-btn:focus, .dropdown .header-banner-btn:hover, .dropdown .header-banner-btn:active, .dropdown .header-banner-btn:focus {
      color: #181717;
      background-color: #8dd363;
      border-color: #8dd363;
      box-shadow: none !important; }
    .header-banner-btn.accent, .dropdown .header-banner-btn.accent {
      color: #181717;
      border-radius: 19px;
      background-color: #8dd363;
      justify-content: center; }
  .header-banner-game {
    display: flex;
    flex-direction: column;
    height: 100%; }
    @media (min-width: 1440px) {
      .header-banner-game .header-banner-content {
        margin-bottom: 60px; } }
    .header-banner-game .header-banner-content-text {
      margin-bottom: 25px; }
      @media (min-width: 1900px) {
        .header-banner-game .header-banner-content-text {
          margin-bottom: 35px; } }

.header-banner-btn-container .dropdown {
  margin-bottom: 0; }
.dropdown .header-banner-btn.dropdown-toggle {
  font-family: "Open Sans", sans-serif; }
  @media (min-width: 768px) {
    .dropdown .header-banner-btn.dropdown-toggle {
      background: transparent;
      border: none; } }
  .dropdown .header-banner-btn.dropdown-toggle + .dropdown-menu {
    overflow: hidden;
    border-radius: 19px;
    border: 1px solid rgba(141, 211, 99, 0.5); }
    @media (min-width: 768px) {
      .dropdown .header-banner-btn.dropdown-toggle + .dropdown-menu {
        width: 100%; } }
    .dropdown .header-banner-btn.dropdown-toggle + .dropdown-menu .dropdown-item {
      color: #fff; }
      .dropdown .header-banner-btn.dropdown-toggle + .dropdown-menu .dropdown-item:hover {
        color: #8dd363;
        background-color: rgba(141, 211, 99, 0.2); }
.dropdown .header-banner-btn:hover, .dropdown .header-banner-btn:focus,
.dropdown .header-banner-btn.btn-primary:not(:disabled):not(.disabled):active,
.dropdown .header-banner-btn.btn-primary:not(:disabled):not(.disabled).active, .dropdown.show > .header-banner-btn.dropdown-toggle.btn {
  color: #181717;
  background-color: #8dd363;
  border-color: #8dd363;
  box-shadow: none; }

.header-banner-btn-list {
  display: flex;
  flex-direction: column;
  align-items: center; }
  @media (min-width: 768px) {
    .header-banner-btn-list {
      align-items: flex-start; } }

.header-banner-btn-container {
  display: flex;
  flex-wrap: wrap;
  max-width: calc(148px * 2 + 16px * 4); }
  @media (min-width: 768px) {
    .header-banner-btn-container {
      flex-direction: column;
      flex-wrap: nowrap; } }
  @media (max-width: 399px) {
    .header-banner-btn-container {
      justify-content: center; } }

.header-banner-info {
  border: solid #8dd363;
  border-width: 2px 0;
  text-shadow: 1px 1px 0 #181717; }
  @media (min-width: 768px) {
    .header-banner-info {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      flex-wrap: wrap;
      border: none; } }
  .header-banner-info-list {
    display: flex;
    justify-content: flex-start;
    font-size: 14px;
    line-height: 1.3;
    color: #fff;
    overflow-x: auto;
    overflow-y: hidden;
    max-width: 100%; }
    @media (min-width: 768px) {
      .header-banner-info-list {
        width: auto;
        border: none;
        font-size: 14px;
        justify-content: space-between; } }
    @media (min-width: 1024px) {
      .header-banner-info-list {
        font-size: 16px;
        flex-wrap: nowrap; } }
    @media (min-width: 1440px) {
      .header-banner-info-list {
        font-size: 20px;
        min-width: 44vw; } }
  .header-banner-info-list-container {
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column; }
    @media (min-width: 768px) {
      .header-banner-info-list-container {
        font-size: 14px;
        justify-content: space-between;
        flex-wrap: nowrap;
        flex-direction: row; } }
  .header-banner-info-item {
    text-align: center;
    white-space: nowrap;
    margin: 10px 14px; }
    @media (min-width: 400px) {
      .header-banner-info-item {
        margin: 10px 16px; } }
    @media (min-width: 500px) {
      .header-banner-info-item {
        margin: 10px 20px; } }
    @media (min-width: 768px) {
      .header-banner-info-item {
        margin: 6px 10px; }
        .header-banner-info-item:first-child {
          margin-left: 0; } }
    @media (min-width: 1024px) {
      .header-banner-info-item {
        margin: 6px 12px; } }
    @media (min-width: 1440px) {
      .header-banner-info-item {
        margin: 12px 20px; } }
  .header-banner-info-val {
    font-weight: 700;
    color: #8dd363; }
  .header-banner-info-label {
    margin-bottom: 7px; }
    @media (min-width: 1024px) {
      .header-banner-info-label {
        margin-bottom: 11px; } }
  .header-banner-info-divider {
    background-color: #8dd363;
    height: 2px;
    width: 100%; }

.header-banner-tools {
  padding: 10px; }
  @media (min-width: 768px) {
    .header-banner-tools {
      padding: 0; } }
  .header-banner-tools-list {
    display: flex;
    align-items: center;
    justify-content: center; }
    @media (min-width: 768px) {
      .header-banner-tools-list {
        justify-content: space-between; } }
    @media (min-width: 1440px) {
      .header-banner-tools-list {
        min-width: 406px; } }
  .header-banner-tools-title {
    font-size: 14px;
    text-align: center;
    margin-bottom: 5px; }
    @media (min-width: 768px) {
      .header-banner-tools-title {
        padding: 4px 10px; } }
    @media (min-width: 1024px) {
      .header-banner-tools-title {
        font-size: 16px; } }
    @media (min-width: 1440px) {
      .header-banner-tools-title {
        font-size: 20px; } }
  .header-banner-tools-item {
    padding: 4px 12px; }
    @media (min-width: 768px) {
      .header-banner-tools-item {
        padding: 6px; } }
    @media (min-width: 1024px) {
      .header-banner-tools-item {
        padding: 6px 12px; } }
    @media (min-width: 1440px) {
      .header-banner-tools-item {
        padding: 12px 20px; } }
  .header-banner-tools-figure {
    width: 30px;
    margin: 0; }
    @media (min-width: 1024px) {
      .header-banner-tools-figure {
        width: 35px; } }
    @media (min-width: 1440px) {
      .header-banner-tools-figure {
        width: 50px; } }
    .header-banner-tools-figure-img {
      width: 100%; }

@media (max-height: 650px) and (min-width: 768px) {
  .header-banner-content-figure {
    transform: scale(0.9) !important; } }
@media (min-height: 400px) and (max-height: 900px) and (min-width: 768px) {
  .header-banner-content {
    margin-bottom: 0; }

  .header-banner-btn.accent {
    margin-bottom: 10px; }

  .header-banner-btn,
  .dropdown .header-banner-btn {
    height: 30px;
    font-size: 14px;
    margin-bottom: 5px;
    min-width: 200px; }

  .header-banner-content-figure-img {
    max-height: 75px; }

  .header-banner-tools-figure {
    width: 25px; } }
@media (min-height: 500px) and (max-height: 900px) and (min-width: 768px) {
  .header-banner-btn,
  .dropdown .header-banner-btn {
    height: 38px;
    font-size: 15px;
    min-width: 220px; } }
@media (min-height: 600px) and (max-height: 900px) and (min-width: 768px) {
  .header-banner-tools-figure {
    width: 35px; } }
@media (min-height: 699px) and (max-height: 1100px) and (min-width: 768px) {
  .header-banner-content-text {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden; }

  .header-banner-content {
    margin-bottom: 20px; } }
@media (min-height: 900px) and (max-height: 1100px) and (min-width: 1900px) {
  .header-banner-content-figure-img {
    max-height: 110px; } }
.animation-container {
  will-change: transform;
  max-width: 500px; }
  @media (min-width: 768px) {
    .animation-container {
      max-width: none; } }

.animation-figure {
  will-change: transform;
  transform-origin: center bottom;
  transition-duration: 1.3s; }
  @media (min-width: 768px) {
    .animation-figure {
      transform-origin: left bottom; } }
  .animation-figure img {
    transition: all 0.5s ease; }

.animation-text {
  will-change: transform;
  transition-duration: 1.1s; }

@media (min-width: 768px) {
  .is-mobile {
    display: none !important; } }

@media (max-width: 767px) {
  .is-tablet-up {
    display: none !important; } }

.header-news,
.header-banner-content-news-title {
  max-width: 1200px;
  margin: 0 auto 26px;
  position: relative; }
  .header-news .header-banner,
  .header-banner-content-news-title .header-banner {
    min-height: auto;
    margin-bottom: 26px; }
  .header-news .header-banner-figure,
  .header-banner-content-news-title .header-banner-figure {
    margin-bottom: 0;
    position: static; }
    .header-news .header-banner-figure-img, .header-news .header-banner-figure-video,
    .header-banner-content-news-title .header-banner-figure-img,
    .header-banner-content-news-title .header-banner-figure-video {
      height: auto;
      width: 100%;
      object-position: center; }
  .header-news .header-banner-shadow,
  .header-banner-content-news-title .header-banner-shadow {
    background: none; }
  .header-news .header-banner-container,
  .header-banner-content-news-title .header-banner-container {
    padding-top: 26px;
    padding-bottom: 0; }
    @media (min-width: 768px) {
      .header-news .header-banner-container,
      .header-banner-content-news-title .header-banner-container {
        padding-left: calc(var(--bs-gutter-x,.75rem) + 11px/2);
        padding-right: calc(var(--bs-gutter-x,.75rem) + 11px/2); } }
  .header-news .header-banner-content,
  .header-banner-content-news-title .header-banner-content {
    margin-bottom: 0;
    padding-bottom: 0; }
  .header-news .header-banner-content-text,
  .header-banner-content-news-title .header-banner-content-text {
    max-width: none; }
    .header-news .header-banner-content-text:last-child,
    .header-banner-content-news-title .header-banner-content-text:last-child {
      margin-bottom: 0; }

.news-header-banner-content .bottom {
  position: absolute;
  left: 20px;
  right: 20px;
  bottom: 20px;
  align-items: center;
  text-align: center; }
  @media (max-width: 480px) {
    .news-header-banner-content .bottom {
      left: 10px;
      right: 10px;
      bottom: 10px; } }
  .news-header-banner-content .bottom .header-banner-content-title {
    line-height: 1.2;
    margin-bottom: 0; }
    @media (max-width: 480px) {
      .news-header-banner-content .bottom .header-banner-content-title {
        font-size: 18px; } }

.three-d-image > .main-img {
  opacity: 0; }

.three-d-image > .left-img {
  perspective-origin: left center;
  clip-path: polygon(0 0, 50% 0, 50% 100%, 0 100%);
  position: absolute;
  top: 0;
  animation: anim-left-side 2s ease-in-out infinite;
  animation-direction: alternate; }

.three-d-image > img.right-img {
  perspective-origin: right center;
  clip-path: polygon(50% 0, 100% 0, 100% 100%, 50% 100%);
  position: absolute;
  top: 0;
  animation: anim-right-side 2s ease-in-out infinite;
  animation-direction: alternate; }

@keyframes anim-left-side {
  0% {
    transform: rotateY(-1deg) scaleX(0.92); }
  100% {
    transform: rotateY(0deg) scaleX(1); } }
@keyframes anim-right-side {
  0% {
    transform: rotateY(0deg) scaleX(1); }
  100% {
    transform: rotateY(-1deg) scaleX(0.92); } }
:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.section-contact-us {
  position: relative;
  display: block;
  width: 100%; }
  .section-contact-us .wrap {
    display: flex;
    flex-direction: row;
    max-width: 1440px;
    margin: 0 auto; }
    .section-contact-us .wrap .sw-products {
      display: flex;
      flex-direction: column;
      justify-content: stretch;
      margin: 0 20px 56px 0;
      flex: 0.5; }
      .section-contact-us .wrap .sw-products-title {
        display: block;
        width: 100%;
        color: #8dd363;
        text-align: left;
        font-size: 19px; }
        @media (min-width: 768px) {
          .section-contact-us .wrap .sw-products-title {
            font-size: 24px; } }
        @media (min-width: 1200px) {
          .section-contact-us .wrap .sw-products-title {
            font-size: 38px; } }
      .section-contact-us .wrap .sw-products-description {
        flex-shrink: 0;
        display: block;
        width: 100%;
        color: #fff;
        text-align: left;
        margin: 1em auto;
        font-size: 13px; }
        @media (min-width: 768px) {
          .section-contact-us .wrap .sw-products-description {
            font-size: 16px; } }
        @media (min-width: 1200px) {
          .section-contact-us .wrap .sw-products-description {
            font-size: 23px; } }
      .section-contact-us .wrap .sw-products-items {
        flex-grow: 1;
        align-items: center;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: auto auto;
        column-gap: 3px;
        row-gap: 3px; }
        .section-contact-us .wrap .sw-products-items > div, .section-contact-us .wrap .sw-products-items > a {
          position: relative;
          margin: 6px;
          align-items: center;
          justify-content: flex-start;
          display: flex;
          flex-direction: column;
          flex-wrap: wrap; }
          .section-contact-us .wrap .sw-products-items > div img, .section-contact-us .wrap .sw-products-items > a img {
            width: 60px;
            height: 60px;
            object-fit: contain;
            object-position: center; }
          .section-contact-us .wrap .sw-products-items > div .item-title, .section-contact-us .wrap .sw-products-items > a .item-title {
            font-size: 14px;
            white-space: normal;
            text-align: center;
            color: #fff;
            padding: 10px 6px; }
            @media (min-width: 768px) {
              .section-contact-us .wrap .sw-products-items > div .item-title, .section-contact-us .wrap .sw-products-items > a .item-title {
                font-size: 13px; } }
            @media (min-width: 1440px) {
              .section-contact-us .wrap .sw-products-items > div .item-title, .section-contact-us .wrap .sw-products-items > a .item-title {
                font-size: 16px; } }
        .section-contact-us .wrap .sw-products-items a:hover .item-title {
          color: #989898; }
    .section-contact-us .wrap .sw-contact-us {
      display: flex;
      flex-direction: column;
      margin: 0 auto;
      margin-left: 20px;
      flex: 0.5;
      width: 100%; }
      .device-mobile .section-contact-us .wrap .sw-contact-us {
        max-width: 320px; }
      .device-tablet .section-contact-us .wrap .sw-contact-us {
        max-width: 100%; }
      .section-contact-us .wrap .sw-contact-us-title {
        display: block;
        width: 100%;
        font-size: 24px;
        color: #8dd363;
        text-align: left; }
        @media (min-width: 768px) {
          .section-contact-us .wrap .sw-contact-us-title {
            font-size: 24px; } }
        @media (min-width: 1200px) {
          .section-contact-us .wrap .sw-contact-us-title {
            font-size: 38px; } }
      .section-contact-us .wrap .sw-contact-us-description {
        display: block;
        width: 100%;
        font-size: 13px;
        color: #fff;
        text-align: left;
        margin: 1em auto; }
        @media (min-width: 768px) {
          .section-contact-us .wrap .sw-contact-us-description {
            font-size: 16px; } }
        @media (min-width: 1200px) {
          .section-contact-us .wrap .sw-contact-us-description {
            font-size: 23px; } }
      .section-contact-us .wrap .sw-contact-us-form .card-body {
        padding: 0; }
      .section-contact-us .wrap .sw-contact-us-form .btn {
        width: 100%;
        max-width: 320px;
        margin-left: auto;
        margin-right: auto;
        display: block; }
      .section-contact-us .wrap .sw-contact-us-form .form-label {
        color: #b3b3b3;
        font-size: 13px; }
        @media (min-width: 768px) {
          .section-contact-us .wrap .sw-contact-us-form .form-label {
            font-size: 16px; } }
        @media (min-width: 1200px) {
          .section-contact-us .wrap .sw-contact-us-form .form-label {
            font-size: 23px; } }
    @media (max-width: 976px) {
      .section-contact-us .wrap {
        flex-direction: column; }
        .section-contact-us .wrap .sw-products {
          margin: 0; }
        .section-contact-us .wrap .sw-contact-us {
          margin: 0 auto; } }

.section-sw360 {
  position: relative;
  display: block;
  width: 100%;
  margin-bottom: 40px; }
  @media (min-width: 768px) {
    .section-sw360 {
      margin-top: 20px; } }
  .section-sw360 .wrap {
    display: flex;
    flex-direction: row;
    max-width: 1440px;
    margin: 0 auto; }
    @media (max-width: 976px) {
      .section-sw360 .wrap {
        flex-direction: column; } }
  .section-sw360 .sw-360 {
    display: flex;
    flex-direction: column;
    margin-right: 40px;
    flex: 0.5; }
    @media (max-width: 976px) {
      .section-sw360 .sw-360 {
        margin-right: 0; } }
    .section-sw360 .sw-360-title {
      display: block;
      width: 100%;
      color: #fff;
      text-align: left;
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 5px; }
      @media (min-width: 768px) {
        .section-sw360 .sw-360-title {
          font-size: 19px; } }
      @media (min-width: 1200px) {
        .section-sw360 .sw-360-title {
          font-size: 27px; } }
    .section-sw360 .sw-360-description {
      display: block;
      width: 100%;
      color: #b3b3b3;
      margin: 0;
      text-align: justify;
      font-size: 13px; }
      @media (min-width: 768px) {
        .section-sw360 .sw-360-description {
          font-size: 16px; } }
      @media (min-width: 1200px) {
        .section-sw360 .sw-360-description {
          font-size: 23px; } }
      .section-sw360 .sw-360-description .accent {
        color: #8dd363; }
      .section-sw360 .sw-360-description p {
        margin-bottom: 1em; }
  .section-sw360 .sw-360-provides {
    display: flex;
    flex-direction: column;
    margin: 0;
    flex: 0.5; }
  .section-sw360 .btn {
    width: 100%;
    max-width: 320px;
    margin-right: auto; }
    @media (max-width: 976px) {
      .section-sw360 .btn {
        margin-left: auto; } }
  .section-sw360 ul {
    margin-bottom: 1em; }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.features-slider {
  position: relative;
  padding: 0 20px; }
  .device-desktop .features-slider {
    overflow: visible !important;
    transition: transform 0.2s; }
    .device-desktop .features-slider:hover .features-slide {
      opacity: .5; }
  .features-slider .features-slide {
    padding: 20px 35px;
    max-width: 428px;
    transition: transform 0.2s; }
    @media (min-width: 600px) {
      .features-slider .features-slide {
        width: calc(100%/2);
        padding: 20px; } }
    @media (min-width: 1024px) {
      .features-slider .features-slide {
        width: calc(100%/3);
        padding: 20px; } }
    .device-desktop .features-slider .features-slide:hover {
      cursor: pointer;
      z-index: 3;
      opacity: 1;
      transform: scale(1.1); }
  .features-slider-title {
    font-size: 16px;
    font-family: "Roboto", sans-serif;
    color: #fff;
    font-weight: bold;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    margin-bottom: 16px;
    text-align: center; }
    @media (min-width: 768px) {
      .features-slider-title {
        font-size: 19px; } }
    @media (min-width: 1200px) {
      .features-slider-title {
        font-size: 27px; } }
  .features-slider-text {
    font-size: 14px;
    font-family: "Roboto", sans-serif; }
    @media (min-width: 768px) {
      .features-slider-text {
        font-size: 16px; } }
    @media (min-width: 1200px) {
      .features-slider-text {
        font-size: 23px; } }
  .features-slider-figure {
    transition: transform 0.2s;
    display: block;
    margin: 0 0 16px;
    padding-bottom: 62.54%;
    position: relative; }
    .features-slider-figure-img {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      top: 0;
      height: 100%;
      width: 100%;
      object-fit: cover;
      object-position: center;
      border-radius: 18px; }
  .features-slider-nav {
    display: flex;
    justify-content: space-between; }
  .features-slider-nav-btn,
  .features-slider .swiper-button-prev,
  .features-slider .swiper-button-next {
    cursor: pointer;
    position: absolute;
    top: 50%;
    height: 24px;
    width: 24px;
    transform: translateY(-50%);
    z-index: 10;
    background: no-repeat 50% 50%;
    background-size: auto 100%;
    background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI1LjQuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHZpZXdCb3g9IjAgMCA0My45NSA3My45NSIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNDMuOTUgNzMuOTU7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPHN0eWxlIHR5cGU9InRleHQvY3NzIj4KCS5zdDB7ZmlsbDojRkZGRkZGO30KPC9zdHlsZT4KPGc+Cgk8cGF0aCBjbGFzcz0ic3QwIiBkPSJNMzguODUsNDIuMDdjLTEuMjYsMC0yLjUyLTAuNDYtMy41LTEuMzlMMS42LDguODFDLTAuNDUsNi44Ny0wLjU0LDMuNjUsMS4zOSwxLjYKCQlDMy4zMy0wLjQ1LDYuNTUtMC41NCw4LjYsMS4zOWwzMy43NSwzMS44N2MyLjA1LDEuOTMsMi4xNCw1LjE2LDAuMjEsNy4yMUM0MS41Niw0MS41NCw0MC4yMSw0Mi4wNywzOC44NSw0Mi4wN3oiLz4KPC9nPgo8Zz4KCTxwYXRoIGNsYXNzPSJzdDAiIGQ9Ik01LjEsNzMuOTVjLTEuMzUsMC0yLjcxLTAuNTQtMy43MS0xLjZjLTEuOTMtMi4wNS0xLjg0LTUuMjgsMC4yMS03LjIxbDMzLjc1LTMxLjg3CgkJYzIuMDUtMS45Myw1LjI4LTEuODQsNy4yMSwwLjIxYzEuOTMsMi4wNSwxLjg0LDUuMjgtMC4yMSw3LjIxTDguNiw3Mi41NkM3LjYyLDczLjQ5LDYuMzYsNzMuOTUsNS4xLDczLjk1eiIvPgo8L2c+Cjwvc3ZnPgo="); }
    .features-slider-nav-btn:after,
    .features-slider .swiper-button-prev:after,
    .features-slider .swiper-button-next:after {
      display: none; }
    @media (min-width: 1440px) {
      .features-slider-nav-btn,
      .features-slider .swiper-button-prev,
      .features-slider .swiper-button-next {
        height: 52px;
        width: 52px; } }
    .features-slider-nav-btn.swiper-button-prev,
    .features-slider .swiper-button-prev.swiper-button-prev,
    .features-slider .swiper-button-next.swiper-button-prev {
      left: 0;
      transform: translateY(-50%) scale(-1, -1); }
    .features-slider-nav-btn.swiper-button-next,
    .features-slider .swiper-button-prev.swiper-button-next,
    .features-slider .swiper-button-next.swiper-button-next {
      right: 0; }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.engagement-swiper-section {
  margin: 0 auto 50px; }
.engagement-swiper-title {
  font-size: 19px;
  font-weight: bold;
  color: #fff;
  line-height: 1.25;
  margin: 0 auto 8px;
  text-align: center; }
  @media (min-width: 768px) {
    .engagement-swiper-title {
      font-size: 24px;
      margin-bottom: 18px; } }
  @media (min-width: 1200px) {
    .engagement-swiper-title {
      font-size: 38px;
      margin-bottom: 85px; } }
.engagement-swiper .swiper-wrapper {
  align-items: center; }
.engagement-swiper .swiper-slide {
  padding: 0 28px; }
  @media (min-width: 480px) {
    .engagement-swiper .swiper-slide {
      display: flex;
      align-items: center;
      justify-content: center; } }
  @media (min-width: 768px) {
    .engagement-swiper .swiper-slide {
      padding: 0 54px; } }
  @media (min-width: 1900px) {
    .engagement-swiper .swiper-slide {
      padding: 0 100px; } }
  .engagement-swiper .swiper-slide-figure {
    display: block;
    flex-shrink: 0;
    margin-bottom: 20px; }
    @media (min-width: 480px) {
      .engagement-swiper .swiper-slide-figure {
        width: 45%;
        margin-right: 3.125vw;
        margin-bottom: 0; } }
    @media (min-width: 1900px) {
      .engagement-swiper .swiper-slide-figure {
        width: 40.313vw;
        max-width: 774px; } }
    .engagement-swiper .swiper-slide-figure-img {
      display: block;
      width: 200px;
      margin: auto; }
      @media (min-width: 480px) {
        .engagement-swiper .swiper-slide-figure-img {
          max-width: 600px;
          max-height: 600px;
          object-fit: contain;
          width: 100%;
          margin: 0 auto; } }
  .engagement-swiper .swiper-slide-content {
    text-align: center;
    max-width: 500px;
    margin: auto; }
    @media (min-width: 480px) {
      .engagement-swiper .swiper-slide-content {
        text-align: left;
        max-width: calc(100% - 45% - 3.125vw);
        margin: 0; } }
  .engagement-swiper .swiper-slide-title, .engagement-swiper .swiper-slide-subtitle {
    margin-bottom: 10px;
    color: #fff;
    font-weight: bold;
    font-size: 19px; }
    @media (min-width: 768px) {
      .engagement-swiper .swiper-slide-title, .engagement-swiper .swiper-slide-subtitle {
        font-size: 24px; } }
    @media (min-width: 1200px) {
      .engagement-swiper .swiper-slide-title, .engagement-swiper .swiper-slide-subtitle {
        font-size: 38px; } }
  .engagement-swiper .swiper-slide-subtitle {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden; }
  .engagement-swiper .swiper-slide-text {
    font-size: 13px; }
    @media (min-width: 768px) {
      .engagement-swiper .swiper-slide-text {
        font-size: 16px; } }
    @media (min-width: 1200px) {
      .engagement-swiper .swiper-slide-text {
        font-size: 23px; } }
    .engagement-swiper .swiper-slide-text.spaced {
      margin-bottom: 30px; }
      @media (min-width: 768px) {
        .engagement-swiper .swiper-slide-text.spaced {
          margin-bottom: 40px; } }
.engagement-swiper .swiper-button-prev,
.engagement-swiper .swiper-button-next {
  cursor: pointer;
  position: absolute;
  margin: 0;
  top: 50%;
  height: 24px;
  width: 24px;
  transform: translateY(-50%);
  z-index: 10;
  background: no-repeat 50% 50%;
  background-size: auto 100%;
  background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI1LjQuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHZpZXdCb3g9IjAgMCA0My45NSA3My45NSIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNDMuOTUgNzMuOTU7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPHN0eWxlIHR5cGU9InRleHQvY3NzIj4KCS5zdDB7ZmlsbDojRkZGRkZGO30KPC9zdHlsZT4KPGc+Cgk8cGF0aCBjbGFzcz0ic3QwIiBkPSJNMzguODUsNDIuMDdjLTEuMjYsMC0yLjUyLTAuNDYtMy41LTEuMzlMMS42LDguODFDLTAuNDUsNi44Ny0wLjU0LDMuNjUsMS4zOSwxLjYKCQlDMy4zMy0wLjQ1LDYuNTUtMC41NCw4LjYsMS4zOWwzMy43NSwzMS44N2MyLjA1LDEuOTMsMi4xNCw1LjE2LDAuMjEsNy4yMUM0MS41Niw0MS41NCw0MC4yMSw0Mi4wNywzOC44NSw0Mi4wN3oiLz4KPC9nPgo8Zz4KCTxwYXRoIGNsYXNzPSJzdDAiIGQ9Ik01LjEsNzMuOTVjLTEuMzUsMC0yLjcxLTAuNTQtMy43MS0xLjZjLTEuOTMtMi4wNS0xLjg0LTUuMjgsMC4yMS03LjIxbDMzLjc1LTMxLjg3CgkJYzIuMDUtMS45Myw1LjI4LTEuODQsNy4yMSwwLjIxYzEuOTMsMi4wNSwxLjg0LDUuMjgtMC4yMSw3LjIxTDguNiw3Mi41NkM3LjYyLDczLjQ5LDYuMzYsNzMuOTUsNS4xLDczLjk1eiIvPgo8L2c+Cjwvc3ZnPgo="); }
  .engagement-swiper .swiper-button-prev:after,
  .engagement-swiper .swiper-button-next:after {
    display: none; }
  @media (min-width: 1440px) {
    .engagement-swiper .swiper-button-prev,
    .engagement-swiper .swiper-button-next {
      height: 52px;
      width: 52px; } }
  .engagement-swiper .swiper-button-prev.swiper-button-prev,
  .engagement-swiper .swiper-button-next.swiper-button-prev {
    left: 0;
    transform: translateY(-50%) scale(-1, -1); }
  .engagement-swiper .swiper-button-prev.swiper-button-next,
  .engagement-swiper .swiper-button-next.swiper-button-next {
    right: 0; }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.engagement-multi-swiper {
  overflow: visible !important; }
  .device-desktop .engagement-multi-swiper {
    transition: transform 0.2s; }
    .device-desktop .engagement-multi-swiper:hover .swiper-slide {
      opacity: .5; }
  .engagement-multi-swiper .swiper-slide {
    text-align: center;
    padding: 0 20px;
    transition: transform 0.2s; }
    @media (min-width: 600px) {
      .engagement-multi-swiper .swiper-slide {
        width: calc(100% / 2); } }
    @media (min-width: 1024px) {
      .engagement-multi-swiper .swiper-slide {
        width: calc(100% / 3);
        padding: 0 35px 35px; } }
    .device-desktop .engagement-multi-swiper .swiper-slide:hover {
      cursor: pointer;
      z-index: 3;
      opacity: 1;
      transform: scale(1.1); }
  .engagement-multi-swiper .swiper-slide-title {
    color: #fff;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    margin-bottom: 16px;
    text-align: center;
    font-weight: bold;
    font-size: 16px; }
    @media (min-width: 768px) {
      .engagement-multi-swiper .swiper-slide-title {
        font-size: 19px;
        margin-bottom: 35px; } }
    @media (min-width: 1200px) {
      .engagement-multi-swiper .swiper-slide-title {
        font-size: 27px; } }
  .engagement-multi-swiper .swiper-slide-figure {
    display: block;
    position: relative; }
    .engagement-multi-swiper .swiper-slide-figure-img {
      width: 100%;
      border-radius: 18px; }
  .engagement-multi-swiper-btns {
    position: relative; }
    .engagement-multi-swiper-btns .swiper-button-prev,
    .engagement-multi-swiper-btns .swiper-button-next {
      cursor: pointer;
      position: absolute;
      top: 50%;
      height: 24px;
      width: 24px;
      transform: translateY(-50%);
      z-index: 10;
      background: no-repeat 50% 50%;
      background-size: auto 100%;
      background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI1LjQuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHZpZXdCb3g9IjAgMCA0My45NSA3My45NSIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNDMuOTUgNzMuOTU7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPHN0eWxlIHR5cGU9InRleHQvY3NzIj4KCS5zdDB7ZmlsbDojRkZGRkZGO30KPC9zdHlsZT4KPGc+Cgk8cGF0aCBjbGFzcz0ic3QwIiBkPSJNMzguODUsNDIuMDdjLTEuMjYsMC0yLjUyLTAuNDYtMy41LTEuMzlMMS42LDguODFDLTAuNDUsNi44Ny0wLjU0LDMuNjUsMS4zOSwxLjYKCQlDMy4zMy0wLjQ1LDYuNTUtMC41NCw4LjYsMS4zOWwzMy43NSwzMS44N2MyLjA1LDEuOTMsMi4xNCw1LjE2LDAuMjEsNy4yMUM0MS41Niw0MS41NCw0MC4yMSw0Mi4wNywzOC44NSw0Mi4wN3oiLz4KPC9nPgo8Zz4KCTxwYXRoIGNsYXNzPSJzdDAiIGQ9Ik01LjEsNzMuOTVjLTEuMzUsMC0yLjcxLTAuNTQtMy43MS0xLjZjLTEuOTMtMi4wNS0xLjg0LTUuMjgsMC4yMS03LjIxbDMzLjc1LTMxLjg3CgkJYzIuMDUtMS45Myw1LjI4LTEuODQsNy4yMSwwLjIxYzEuOTMsMi4wNSwxLjg0LDUuMjgtMC4yMSw3LjIxTDguNiw3Mi41NkM3LjYyLDczLjQ5LDYuMzYsNzMuOTUsNS4xLDczLjk1eiIvPgo8L2c+Cjwvc3ZnPgo=");
      margin-top: 21px; }
      @media (min-width: 1900px) {
        .engagement-multi-swiper-btns .swiper-button-prev,
        .engagement-multi-swiper-btns .swiper-button-next {
          margin-top: 46px; } }
      .engagement-multi-swiper-btns .swiper-button-prev:after,
      .engagement-multi-swiper-btns .swiper-button-next:after {
        display: none; }
      @media (min-width: 1440px) {
        .engagement-multi-swiper-btns .swiper-button-prev,
        .engagement-multi-swiper-btns .swiper-button-next {
          height: 52px;
          width: 52px; } }
      .engagement-multi-swiper-btns .swiper-button-prev.swiper-button-prev,
      .engagement-multi-swiper-btns .swiper-button-next.swiper-button-prev {
        left: -10px;
        transform: translateY(-50%) scale(-1, -1); }
        @media (min-width: 1024px) {
          .engagement-multi-swiper-btns .swiper-button-prev.swiper-button-prev,
          .engagement-multi-swiper-btns .swiper-button-next.swiper-button-prev {
            left: 0; } }
      .engagement-multi-swiper-btns .swiper-button-prev.swiper-button-next,
      .engagement-multi-swiper-btns .swiper-button-next.swiper-button-next {
        right: -10px; }
        @media (min-width: 1024px) {
          .engagement-multi-swiper-btns .swiper-button-prev.swiper-button-next,
          .engagement-multi-swiper-btns .swiper-button-next.swiper-button-next {
            right: 0; } }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.jp-feature-section {
  background: #8dd363;
  position: relative;
  margin-top: 18vw;
  padding: 9.375vw 0; }
  @media (min-width: 1024px) {
    .jp-feature-section {
      padding: 130px 0;
      margin-top: 0; } }
  @media (min-width: 768px) {
    .jp-feature-section {
      margin-top: -9.479vw; } }
  .jp-feature-section:before {
    content: '';
    position: absolute;
    top: calc(-9.479vw + 1px);
    left: 0;
    right: 0;
    height: 9.479vw;
    background: url("/assets/site/images/misc/mwjp-header-bg.png") no-repeat 100% 0;
    background-size: 100% auto;
    background-position: bottom; }
  .jp-feature-section-title {
    font-family: "Roboto", sans-serif;
    text-align: center;
    font-size: 19px;
    font-weight: bold;
    color: #000;
    margin-bottom: 4.375vw;
    line-height: 1.25; }
    @media (min-width: 768px) {
      .jp-feature-section-title {
        font-size: 24px;
        margin-bottom: 4.375vw; } }
    @media (min-width: 1200px) {
      .jp-feature-section-title {
        font-size: 38px;
        margin-bottom: 84px; } }
.jp-feature-list {
  color: #000;
  font-family: "Roboto", sans-serif;
  max-width: 1400px;
  margin: auto; }
  @media (min-width: 600px) {
    .jp-feature-list {
      display: flex;
      align-items: flex-start;
      justify-content: space-between; } }
  .jp-feature-list-item {
    flex: 1;
    text-align: center;
    margin: 0 0 50px; }
    .jp-feature-list-item:last-child {
      margin-bottom: 0; }
    @media (min-width: 600px) {
      .jp-feature-list-item {
        margin: 0 20px; } }
  .jp-feature-list-figure {
    text-align: center;
    height: 60px;
    margin-bottom: 8px;
    display: flex;
    align-items: flex-end; }
    @media (min-width: 768px) {
      .jp-feature-list-figure {
        height: 8.333vw;
        margin-bottom: 1vw; } }
    @media (min-width: 1900px) {
      .jp-feature-list-figure {
        height: 160px;
        margin-bottom: 18px; } }
    .jp-feature-list-figure-img {
      max-height: 100%;
      width: auto;
      display: block;
      margin: auto; }
  .jp-feature-list-content {
    max-width: 310px;
    margin: auto; }
  .jp-feature-list-title {
    font-weight: bold;
    font-size: 19px;
    margin-bottom: 10px; }
    @media (min-width: 768px) {
      .jp-feature-list-title {
        font-size: 24px; } }
    @media (min-width: 1200px) {
      .jp-feature-list-title {
        font-size: 38px;
        margin-bottom: 15px; } }
    .jp-feature-list-title .accent {
      display: block;
      color: #fff; }
  .jp-feature-list-text {
    font-size: 13px; }
    @media (min-width: 768px) {
      .jp-feature-list-text {
        font-size: 16px; } }
    @media (min-width: 1200px) {
      .jp-feature-list-text {
        font-size: 23px; } }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.sw-news__items {
  font-size: 20px;
  overflow: auto;
  display: block;
  flex: 1 1;
  max-width: 1942px;
  margin: 0 auto; }
  .sw-news__items .sw-news-grid {
    display: flex;
    align-items: stretch;
    flex-wrap: wrap; }
    .sw-news__items .sw-news-grid .news-div {
      display: flex;
      flex-direction: column;
      width: 100%; }
      @media (min-width: 500px) {
        .sw-news__items .sw-news-grid .news-div {
          width: 50%; } }
      @media (min-width: 768px) {
        .sw-news__items .sw-news-grid .news-div {
          width: calc(100%/3); } }
      @media (min-width: 1200px) {
        .sw-news__items .sw-news-grid .news-div {
          width: calc(100%/4); } }
      .sw-news__items .sw-news-grid .news-div .news-item {
        display: flex;
        flex-direction: column;
        margin: 0 11px 15px 11px;
        color: #000;
        text-decoration: none;
        background: #fff;
        height: 100%; }
        @media (min-width: 768px) {
          .sw-news__items .sw-news-grid .news-div .news-item {
            margin: 0 11px 35px; } }
        .sw-news__items .sw-news-grid .news-div .news-item__img {
          padding-top: 55.55%;
          width: 100%;
          height: 0;
          position: relative;
          overflow: hidden; }
          @media (max-width: 767px) {
            .sw-news__items .sw-news-grid .news-div .news-item__img {
              max-width: 100%; } }
          .sw-news__items .sw-news-grid .news-div .news-item__img img {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
            max-width: 100%;
            max-height: 100%;
            transition: all 0.2s ease;
            object-fit: cover; }
        .sw-news__items .sw-news-grid .news-div .news-item:hover {
          color: #000;
          text-decoration: none; }
          .sw-news__items .sw-news-grid .news-div .news-item:hover img {
            transform: scale(1.05); }
        .sw-news__items .sw-news-grid .news-div .news-item__info {
          font-family: "Open Sans", sans-serif;
          line-height: 1.2;
          display: flex;
          flex-direction: column;
          height: 100%;
          padding: 20px 16px 16px; }
          @media (min-width: 1024px) {
            .sw-news__items .sw-news-grid .news-div .news-item__info {
              padding: 30px 18px 18px; } }
        .sw-news__items .sw-news-grid .news-div .news-item__title {
          font-size: 16px;
          flex-grow: 1;
          margin-bottom: 16px;
          word-break: break-word; }
          @media (min-width: 1200px) {
            .sw-news__items .sw-news-grid .news-div .news-item__title {
              font-size: 23px;
              margin-bottom: 12px; } }
        .sw-news__items .sw-news-grid .news-div .news-item__label-list {
          display: inline-flex;
          flex-wrap: wrap;
          margin-bottom: 16px; }
          @media (min-width: 1024px) {
            .sw-news__items .sw-news-grid .news-div .news-item__label-list {
              margin-bottom: 22px; } }
          .sw-news__items .sw-news-grid .news-div .news-item__label-list:empty {
            display: none; }
        .sw-news__items .sw-news-grid .news-div .news-item__label {
          font-weight: bold;
          font-size: 12px;
          line-height: 1;
          display: inline-flex;
          align-self: flex-start;
          color: #fff;
          background: #8dd363;
          border-radius: 5px;
          padding: 6px;
          text-transform: uppercase;
          margin: 2px 4px 2px 0; }
          @media (min-width: 768px) {
            .sw-news__items .sw-news-grid .news-div .news-item__label {
              font-size: 14px;
              padding: 8px 10px; } }
          @media (min-width: 1024px) {
            .sw-news__items .sw-news-grid .news-div .news-item__label {
              font-size: 15px; } }
          .sw-news__items .sw-news-grid .news-div .news-item__label.live {
            background-color: purple; }
          .sw-news__items .sw-news-grid .news-div .news-item__label.partnership, .sw-news__items .sw-news-grid .news-div .news-item__label.green {
            background: #8dd363; }
          .sw-news__items .sw-news-grid .news-div .news-item__label.partnerships {
            background: #4169e1; }
          .sw-news__items .sw-news-grid .news-div .news-item__label.markets, .sw-news__items .sw-news-grid .news-div .news-item__label.blue {
            background: #6bc1d3; }
          .sw-news__items .sw-news-grid .news-div .news-item__label.games, .sw-news__items .sw-news-grid .news-div .news-item__label.red {
            background: #f34f4d; }
        .sw-news__items .sw-news-grid .news-div .news-item__date {
          font-size: 12px;
          color: #8dd363; }
          @media (min-width: 768px) {
            .sw-news__items .sw-news-grid .news-div .news-item__date {
              font-size: 15px; } }

.news-article {
  font-family: "Open Sans", sans-serif;
  max-width: 824px;
  margin-right: auto;
  margin-left: auto;
  position: relative; }
  .news-article__headline {
    color: #fff;
    display: flex;
    align-items: center;
    line-height: 1;
    margin-bottom: 20px;
    font-size: 14px; }
    @media (min-width: 768px) {
      .news-article__headline {
        font-size: 16px; } }
    @media (min-width: 1200px) {
      .news-article__headline {
        font-size: 20px; } }
  .news-article__divider {
    display: inline-flex;
    margin: 0 12px;
    background-color: #8dd363;
    width: 1px;
    align-self: stretch;
    flex-shrink: 0; }
  .news-article__label {
    text-transform: uppercase;
    color: #8dd363; }
  .news-article__title {
    position: relative;
    color: #8dd363;
    margin-bottom: 10px;
    font-weight: bold;
    font-size: 19px; }
    @media (min-width: 768px) {
      .news-article__title {
        font-size: 24px; } }
    @media (min-width: 1200px) {
      .news-article__title {
        font-size: 38px; } }
  .news-article__desc {
    text-align: justify;
    margin-bottom: 26px;
    font-size: 13px; }
    @media (min-width: 768px) {
      .news-article__desc {
        font-size: 16px; } }
    @media (min-width: 1200px) {
      .news-article__desc {
        font-size: 23px; } }
    .news-article__desc img {
      max-width: 100%; }
    .news-article__desc p {
      margin-bottom: 26px; }
    .news-article__desc a {
      color: #8dd363;
      background-image: linear-gradient(rgba(24, 23, 23, 0) calc(99% - 1px), #8dd363 1px);
      background-repeat: no-repeat;
      background-size: 0 100%;
      transition: background-size .5s; }
      .news-article__desc a:hover {
        text-decoration: none;
        background-size: 100% 100%; }
  .news-article__link {
    cursor: pointer; }
  .news-article__pagination {
    position: relative;
    margin-bottom: 26px;
    display: flex;
    justify-content: space-between; }
    .news-article__pagination-label {
      margin: 0 10px; }
    .news-article__pagination-label, .news-article__pagination-btn-prev:before, .news-article__pagination-btn-next:after {
      font-size: 13px; }
      @media (min-width: 768px) {
        .news-article__pagination-label, .news-article__pagination-btn-prev:before, .news-article__pagination-btn-next:after {
          font-size: 16px; } }
      @media (min-width: 1200px) {
        .news-article__pagination-label, .news-article__pagination-btn-prev:before, .news-article__pagination-btn-next:after {
          font-size: 23px; } }
    .news-article__pagination-btn-prev, .news-article__pagination-btn-next {
      color: #fff;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative; }
      .news-article__pagination-btn-prev:hover, .news-article__pagination-btn-next:hover {
        color: rgba(255, 255, 255, 0.9); }
    .news-article__pagination-btn-prev:before, .news-article__pagination-btn-next:after {
      content: 'prev';
      color: #fff;
      font-family: swiper-icons;
      text-transform: none !important;
      letter-spacing: 0;
      font-variant: initial;
      line-height: 1; }
    .news-article__pagination-btn-next:after {
      content: 'next'; }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.page404,
.page403,
.pageMaintenance {
  background-image: url("/assets/pa/images/404-maintence.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover; }

.blockcenter {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  text-align: center;
  max-width: 1920px;
  margin: 0 auto; }

.blockcenter__offer {
  font-size: 20px;
  line-height: 35px;
  max-width: 370px;
  margin-left: auto;
  margin-right: auto;
  margin-top: 8.33vh;
  margin-bottom: 8.33vh; }

@media (min-width: 1921px), (min-height: 1081px) {
  .blockcenter__offer {
    margin-top: 90px;
    margin-bottom: 90px; } }
.blockcenter__offer--maintenance {
  margin-bottom: 0; }

@media (max-width: 640px) and (orientation: landscape) {
  .blockcenter__offer {
    margin-top: 40px;
    margin-bottom: 40px; } }
@media (max-width: 480px) {
  .blockcenter__offer {
    width: 100%; } }
@media (max-width: 360px) {
  .blockcenter__offer {
    font-size: 18px;
    line-height: 30px; } }
.blockcenter__title {
  font-size: 35px;
  line-height: 1;
  letter-spacing: .32em;
  position: relative; }

@media (max-width: 410px) {
  .blockcenter__title {
    font-size: 30px; } }
@media (max-width: 360px) {
  .blockcenter__title {
    font-size: 26px; } }
.blockcenter__title::before {
  z-index: -1;
  text-transform: none;
  white-space: nowrap;
  position: absolute;
  left: 50px;
  bottom: -25px;
  content: attr(data-text);
  font-size: 216px;
  line-height: 1;
  font-family: "Open Sans", sans-serif;
  font-weight: 800;
  letter-spacing: normal;
  color: rgba(255, 255, 255, 0.07); }

@media (max-width: 1024px) {
  .blockcenter__title::before {
    left: 50%;
    transform: translateX(-50%);
    font-size: 150px; } }
@media (max-width: 812px) {
  .blockcenter__title::before {
    font-size: 100px;
    bottom: -10px; } }
@media (max-width: 480px) {
  .blockcenter__title::before {
    font-size: 70px; } }
.button--primary {
  background-color: #8dd363;
  color: #181717; }

.button {
  outline: 0;
  cursor: pointer;
  border: none;
  display: inline-block;
  text-decoration: none;
  text-align: center;
  font-size: 14px;
  line-height: 16px;
  letter-spacing: .12em;
  font-weight: 700;
  text-transform: uppercase;
  white-space: nowrap;
  border-radius: 50px;
  transition: all .2s ease;
  padding: 19px 69px;
  position: relative;
  z-index: 4; }

.blockcenter__btn {
  width: 238px;
  margin: 0 auto; }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.tournament-wrapper {
  width: 100%;
  height: calc(100vh - 80px);
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: #76d7ff; }
  .tournament-wrapper a {
    width: 100%; }
  .tournament-wrapper .tournament-image {
    width: 100%;
    object-fit: cover; }
  .tournament-wrapper .flip-countdown {
    position: absolute;
    bottom: 20px;
    z-index: 100;
    left: 0;
    right: 50%;
    width: 100vw; }
    .tournament-wrapper .flip-countdown .flip-countdown-title {
      color: #000; }

.device-mobile.in-portrait .tournament-wrapper img.mobile-h {
  display: none; }
.device-mobile.in-portrait .tournament-wrapper img.mobile-v {
  height: auto;
  object-fit: contain; }
.device-mobile.in-portrait .flip-countdown {
  bottom: 20vh; }
.device-mobile.in-landscape .tournament-wrapper a {
  width: 100%;
  /*
          height: 100%;*/ }
.device-mobile.in-landscape .tournament-wrapper img.mobile-v {
  display: none; }
.device-mobile.in-landscape .tournament-wrapper img.mobile-h {
  object-fit: cover; }
.device-mobile .tournament-wrapper img.desktop {
  display: none; }

.device-desktop .tournament-wrapper a {
  width: 100%;
  /*
        height: 100%;*/ }
@media (min-width: 1680px) {
  .device-desktop .tournament-wrapper img {
    object-fit: contain; } }
.device-desktop .tournament-wrapper img.mobile-h, .device-desktop .tournament-wrapper img.mobile-v {
  display: none; }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

#roadmap-games-grid, #live-games-grid {
  width: 100%;
  display: flex;
  flex-direction: column;
  flex: 1 1; }

.device-mobile .roadmap-toolbar .toolbar-root {
  padding: 4px 0; }
  .device-mobile .roadmap-toolbar .toolbar-root button {
    font-size: 0.76rem;
    padding: 2px;
    margin: 2px; }
  .device-mobile .roadmap-toolbar .toolbar-root .dropdown button {
    height: calc(100% - 4px); }

.roadmap-toolbar {
  position: relative;
  background-color: #181717;
  z-index: 1000;
  right: 0;
  left: 0;
  display: flex;
  flex-wrap: wrap; }
  .roadmap-toolbar .toolbar-root {
    display: flex;
    position: relative;
    align-items: center;
    padding: 4px 24px; }
    .roadmap-toolbar .toolbar-root .dropdown {
      height: 100%; }
      .roadmap-toolbar .toolbar-root .dropdown button {
        height: 100%; }
    .roadmap-toolbar .toolbar-root .dropdown-item {
      padding: .25em; }
    .roadmap-toolbar .toolbar-root .dropdown-menu.show {
      border: 1px solid rgba(141, 211, 99, 0.5);
      min-width: 400px;
      max-width: calc(100vw  - 20px);
      max-height: calc(100vh - 60px - 50px - 6.25vw - 45px);
      overflow: auto;
      margin-left: 0;
      columns: auto 180px;
      column-gap: .75em;
      padding: .75em; }
      .device-mobile .roadmap-toolbar .toolbar-root .dropdown-menu.show {
        max-height: calc(100vh - 60px - 40px - 45px);
        columns: auto 142px;
        font-size: 13px;
        min-width: calc(100vw - 2px);
        max-width: calc(100vw - 2px);
        column-fill: auto; }
      .device-mobile.in-portrait .roadmap-toolbar .toolbar-root .dropdown-menu.show {
        max-height: calc(100vh - 60px - 40px - 23.3333333333vw - 45px);
        column-fill: balance; }
      .device-tablet.in-landscape .roadmap-toolbar .toolbar-root .dropdown-menu.show, .device-tablet.in-portrait .roadmap-toolbar .toolbar-root .dropdown-menu.show {
        max-height: calc(100vh - 60px - 50px - 6.25vw - 45px);
        columns: auto 180px;
        font-size: 16px;
        min-width: 400px;
        max-width: calc(100vw  - 20px);
        column-fill: balance; }
      .roadmap-toolbar .toolbar-root .dropdown-menu.show .dropdown-item {
        color: #fff; }
        .roadmap-toolbar .toolbar-root .dropdown-menu.show .dropdown-item img {
          max-width: 30px;
          margin-right: 8px;
          font-size: 0; }
        .roadmap-toolbar .toolbar-root .dropdown-menu.show .dropdown-item.active {
          color: #8dd363;
          border: 1px solid rgba(141, 211, 99, 0.5);
          background-color: transparent; }
        .roadmap-toolbar .toolbar-root .dropdown-menu.show .dropdown-item:hover {
          color: #8dd363;
          background-color: rgba(141, 211, 99, 0.2); }
    .roadmap-toolbar .toolbar-root button {
      border: 1px solid rgba(255, 255, 255, 0.23);
      padding: 5px 15px;
      margin: 0 4px;
      color: #fff;
      font-size: 0.875rem;
      min-width: 64px;
      box-sizing: border-box;
      transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
      font-family: "Roboto", sans-serif;
      font-weight: 500;
      line-height: 1.75;
      border-radius: 4px;
      letter-spacing: 0.02857em;
      text-transform: uppercase; }
      .roadmap-toolbar .toolbar-root button.active {
        color: #000;
        background-color: #8dd363;
        border: 1px solid rgba(141, 211, 99, 0.5); }
      .roadmap-toolbar .toolbar-root button:hover {
        color: #8dd363;
        text-decoration: none;
        background-color: transparent; }

div.dropdown-menu.show {
  color: #fff;
  background-color: #181717; }
  div.dropdown-menu.show .dropdown-item {
    background-color: #181717; }
    div.dropdown-menu.show .dropdown-item.active {
      color: #000;
      background-color: #8dd363; }
    div.dropdown-menu.show .dropdown-item:focus, div.dropdown-menu.show .dropdown-item:hover {
      color: #8dd363;
      background-color: #181717; }

.roadmap-banner {
  display: block;
  min-height: 1px;
  align-self: flex-start;
  margin-bottom: 15px; }
  .roadmap-banner video {
    width: 100%;
    display: block;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    min-height: 0; }

.device-desktop .roadmap-banner .desktop-video {
  display: block; }
.device-desktop .roadmap-banner .mobile-landscape-video {
  display: none; }
.device-desktop .roadmap-banner .mobile-portrait-video {
  display: none; }

.device-mobile.in-landscape .roadmap-banner .desktop-video {
  display: none; }
.device-mobile.in-landscape .roadmap-banner .mobile-landscape-video, .device-mobile.in-landscape .roadmap-banner .banner-landscape-image {
  display: block; }
.device-mobile.in-landscape .roadmap-banner .mobile-portrait-video, .device-mobile.in-landscape .roadmap-banner .banner-portrait-image {
  display: none; }

.device-mobile.in-portrait .roadmap-banner .desktop-video {
  display: none; }
.device-mobile.in-portrait .roadmap-banner .mobile-landscape-video, .device-mobile.in-portrait .roadmap-banner .banner-landscape-image {
  display: none; }
.device-mobile.in-portrait .roadmap-banner .mobile-portrait-video, .device-mobile.in-portrait .roadmap-banner .banner-portrait-image {
  display: block; }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.engagement-slider {
  position: relative;
  padding: 0 20px; }
  .engagement-slider .engagement-slide {
    padding: 20px 35px; }
    @media (min-width: 600px) {
      .engagement-slider .engagement-slide {
        width: calc(100%/2);
        padding: 20px; } }
    @media (min-width: 1024px) {
      .engagement-slider .engagement-slide {
        width: calc(100%/3);
        padding: 20px; } }
  .engagement-slider-title {
    font-size: 26px;
    font-family: "Roboto", sans-serif;
    color: #8dd363;
    font-weight: bold;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    margin-bottom: 16px;
    text-align: center; }
    @media (min-width: 1440px) {
      .engagement-slider-title {
        font-size: 44px; } }
  .engagement-slider-text {
    font-size: 12px;
    font-family: "Roboto", sans-serif; }
    @media (min-width: 1440px) {
      .engagement-slider-text {
        font-size: 18px; } }
  .engagement-slider-figure {
    display: block;
    margin: 0 0 46px;
    padding-bottom: 62.54%;
    position: relative; }
    .engagement-slider-figure-img {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      top: 0;
      height: 100%;
      width: 100%;
      object-fit: contain;
      object-position: center; }
  .engagement-slider-nav {
    display: flex;
    justify-content: space-between; }
  .engagement-slider-nav-btn,
  .engagement-slider .swiper-button-prev,
  .engagement-slider .swiper-button-next {
    cursor: pointer;
    position: absolute;
    top: 50%;
    height: 24px;
    width: 24px;
    transform: translateY(-50%);
    z-index: 10;
    background: no-repeat 50% 50%;
    background-size: auto 100%;
    background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI1LjQuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHZpZXdCb3g9IjAgMCA0My45NSA3My45NSIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNDMuOTUgNzMuOTU7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPHN0eWxlIHR5cGU9InRleHQvY3NzIj4KCS5zdDB7ZmlsbDojRkZGRkZGO30KPC9zdHlsZT4KPGc+Cgk8cGF0aCBjbGFzcz0ic3QwIiBkPSJNMzguODUsNDIuMDdjLTEuMjYsMC0yLjUyLTAuNDYtMy41LTEuMzlMMS42LDguODFDLTAuNDUsNi44Ny0wLjU0LDMuNjUsMS4zOSwxLjYKCQlDMy4zMy0wLjQ1LDYuNTUtMC41NCw4LjYsMS4zOWwzMy43NSwzMS44N2MyLjA1LDEuOTMsMi4xNCw1LjE2LDAuMjEsNy4yMUM0MS41Niw0MS41NCw0MC4yMSw0Mi4wNywzOC44NSw0Mi4wN3oiLz4KPC9nPgo8Zz4KCTxwYXRoIGNsYXNzPSJzdDAiIGQ9Ik01LjEsNzMuOTVjLTEuMzUsMC0yLjcxLTAuNTQtMy43MS0xLjZjLTEuOTMtMi4wNS0xLjg0LTUuMjgsMC4yMS03LjIxbDMzLjc1LTMxLjg3CgkJYzIuMDUtMS45Myw1LjI4LTEuODQsNy4yMSwwLjIxYzEuOTMsMi4wNSwxLjg0LDUuMjgtMC4yMSw3LjIxTDguNiw3Mi41NkM3LjYyLDczLjQ5LDYuMzYsNzMuOTUsNS4xLDczLjk1eiIvPgo8L2c+Cjwvc3ZnPgo="); }
    .engagement-slider-nav-btn:after,
    .engagement-slider .swiper-button-prev:after,
    .engagement-slider .swiper-button-next:after {
      display: none; }
    @media (min-width: 1440px) {
      .engagement-slider-nav-btn,
      .engagement-slider .swiper-button-prev,
      .engagement-slider .swiper-button-next {
        height: 52px;
        width: 52px; } }
    .engagement-slider-nav-btn.swiper-button-prev,
    .engagement-slider .swiper-button-prev.swiper-button-prev,
    .engagement-slider .swiper-button-next.swiper-button-prev {
      left: 0;
      transform: translateY(-50%) scale(-1, -1); }
    .engagement-slider-nav-btn.swiper-button-next,
    .engagement-slider .swiper-button-prev.swiper-button-next,
    .engagement-slider .swiper-button-next.swiper-button-next {
      right: 0; }
  .engagement-slider .tools-slider-item {
    color: white;
    font-size: 1.85vh;
    line-height: 3.33vh; }
    .engagement-slider .tools-slider-item__name {
      font-weight: 700;
      font-size: 2.68vh;
      line-height: 1;
      text-transform: uppercase; }
    .engagement-slider .tools-slider-item__subname {
      text-transform: uppercase;
      font-weight: 700;
      font-size: 2.59vh;
      line-height: 1; }
    .engagement-slider .tools-slider-item__text {
      margin-top: 2.59vh;
      margin-bottom: 12.96vh;
      position: relative;
      padding-right: 10%; }
      .engagement-slider .tools-slider-item__text::after {
        position: absolute;
        content: "";
        bottom: -6.48vh;
        left: 0;
        width: 8.24vh;
        height: 2px;
        background-color: #8dd363; }
        @media (min-width: 1921px), (min-height: 1081px) {
          .engagement-slider .tools-slider-item__text::after {
            bottom: -70px;
            width: 89px; } }
        @media (max-width: 992px), (max-width: 1024px) and (orientation: portrait) {
          .engagement-slider .tools-slider-item__text::after {
            bottom: -30px; } }
        @media (max-width: 480px) {
          .engagement-slider .tools-slider-item__text::after {
            bottom: -15px; } }
    .engagement-slider .tools-slider-item__subtext {
      margin-top: 3.42vh; }
      @media (min-width: 1921px), (min-height: 1081px) {
        .engagement-slider .tools-slider-item__subtext {
          margin-top: 37px; } }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.jackpot-features__inner {
  margin-top: 8.33vh;
  margin-bottom: 12.96vh; }
  @media (min-width: 1921px), (min-height: 1081px) {
    .jackpot-features__inner {
      margin-top: 90px;
      margin-bottom: 140px; } }

.jp-feature {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center; }
  .jp-feature__img {
    width: 200px;
    height: 200px;
    background-repeat: no-repeat;
    background-position: center; }
    @media (max-width: 1024px) {
      .jp-feature__img {
        background-size: contain;
        width: 150px;
        height: 150px; } }
  .jp-feature__name {
    font-weight: 700;
    font-size: 32px;
    line-height: 1;
    margin-top: -1.04vh;
    margin-bottom: 6.02vh;
    position: relative; }
    .jp-feature__name::after {
      content: "";
      position: absolute;
      left: 50%;
      bottom: -4vh;
      transform: translate(-50%, -50%);
      width: 37px;
      height: 4px;
      background-color: #8dd363; }
    @media (max-width: 1024px) {
      .jp-feature__name {
        font-size: 24px; } }
    @media (min-width: 1921px), (min-height: 1081px) {
      .jp-feature__name {
        margin-top: -20px;
        margin-bottom: 65px; }
        .jp-feature__name::after {
          bottom: -32px; } }
  .jp-feature__text {
    width: 100%;
    font-size: 20px;
    line-height: 36px;
    padding: 0 1.5em; }
    @media (max-width: 1024px) {
      .jp-feature__text {
        padding: 0;
        font-size: 15px; } }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.skywind-docs {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 10px;
  left: 0;
  width: 100%; }
  .skywind-docs .sw-toolbar {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 50px;
    display: flex;
    flex-direction: row;
    justify-content: flex-end; }
    .skywind-docs .sw-toolbar button {
      border: 1px solid rgba(255, 255, 255, 0.23);
      padding: 5px 15px;
      margin: 0 4px;
      color: #fff;
      font-size: 0.875rem;
      min-width: 64px;
      box-sizing: border-box;
      transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
      font-family: "Roboto", sans-serif;
      font-weight: 500;
      line-height: 1.75;
      border-radius: 4px;
      letter-spacing: 0.02857em;
      text-transform: uppercase; }
      .skywind-docs .sw-toolbar button.active {
        color: #000;
        background-color: #8dd363;
        border: 1px solid rgba(141, 211, 99, 0.5); }
      .skywind-docs .sw-toolbar button:hover {
        color: #8dd363;
        text-decoration: none;
        background-color: transparent; }
  .skywind-docs .sw-area {
    position: absolute;
    top: 60px;
    right: 0;
    bottom: 20px;
    left: 0;
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 100%; }
    .skywind-docs .sw-area .list-group.sw-list {
      border-radius: 8px;
      overflow: auto;
      min-width: 220px; }
      .skywind-docs .sw-area .list-group.sw-list .list-group-item {
        display: flex;
        background-color: transparent;
        border: 1px solid #8dd363;
        border-radius: 8px;
        padding: 0;
        margin-bottom: 1em;
        overflow: hidden;
        line-height: 1.4rem; }
        .skywind-docs .sw-area .list-group.sw-list .list-group-item:nth-child(even) {
          background-color: rgba(141, 211, 99, 0.1); }
        .skywind-docs .sw-area .list-group.sw-list .list-group-item a {
          width: 100%;
          padding: 0.6rem;
          color: #8dd363;
          text-decoration: none;
          font-size: 14px;
          display: flex;
          flex-direction: row;
          line-height: 1.4rem; }
          .skywind-docs .sw-area .list-group.sw-list .list-group-item a:hover, .skywind-docs .sw-area .list-group.sw-list .list-group-item a:active, .skywind-docs .sw-area .list-group.sw-list .list-group-item a.active {
            color: #000;
            text-decoration: none;
            background-color: #8dd363; }
    .skywind-docs .sw-area .sw-iframe {
      width: 100%; }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

#app-main-header {
  background-color: #181717;
  min-height: 60px; }

.header-main-toolbar a {
  color: #8dd363 !important;
  border: 1px solid rgba(141, 211, 99, 0.5);
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.75;
  padding-right: 0.5rem !important;
  padding-left: 0.5rem !important;
  white-space: nowrap; }
.header-main-toolbar .nav-link {
  margin: 0 4px; }
  @media (max-width: 767px) {
    .header-main-toolbar .nav-link {
      margin: 2px 0; } }

@media (max-width: 767px) {
  .navbar-nav {
    margin-top: 4px; } }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.banner-video.window-fixed {
  position: fixed;
  z-index: 1000;
  right: 0;
  left: 0; }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.device-mobile.in-landscape .banner-image {
  display: none; }
  .device-mobile.in-landscape .banner-image.window-fixed {
    display: none; }

.banner-image {
  position: relative;
  overflow: hidden; }
  .banner-image img {
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    height: 100%;
    width: 100%;
    object-fit: contain;
    object-position: center; }
  .banner-image.window-fixed {
    position: fixed;
    z-index: 1000;
    right: 0;
    left: 0; }

.banner-swiper.roadmap-banner {
  max-width: 1920px;
  width: 100%; }
  .banner-swiper.roadmap-banner .banner-slide img, .banner-swiper.roadmap-banner .banner-slide video {
    width: 100%; }

.banner-swiper.search-banner {
  height: auto;
  max-height: 140px; }

.device-desktop .banner-swiper {
  max-height: 120px;
  height: calc(100vw / (1920 / 120)); }
.device-desktop .banner-swiper.search-banner {
  height: auto; }

.device-mobile.in-portrait .banner-swiper {
  max-height: 140px;
  height: calc(100vw / (600 / 140)); }

.device-mobile.in-landscape .banner-swiper {
  display: none; }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.device-mobile.in-landscape .modal-dialog {
  min-height: 98vh;
  margin: auto; }
.device-mobile .dialog-modal-area .modal-dialog {
  max-width: 98vw; }
  .device-mobile .dialog-modal-area .modal-dialog .modal-body {
    padding: 4px; }

.dialog-modal-area .modal-dialog {
  max-width: 92vw;
  margin-right: auto;
  margin-left: auto; }
  .dialog-modal-area .modal-dialog .modal-body p {
    padding: 2px 10px; }
  .dialog-modal-area .modal-dialog .modal-content {
    background-color: #181717;
    box-shadow: 0 0 18.4px 1.6px rgba(0, 0, 0, 0.7);
    position: absolute;
    top: 14px;
    right: 14px;
    bottom: 14px;
    left: 14px;
    width: auto; }
  .dialog-modal-area .modal-dialog iframe {
    position: relative;
    width: 100%;
    height: 100%;
    border: none; }
  .dialog-modal-area .modal-dialog video {
    display: block;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%; }
  .dialog-modal-area .modal-dialog .modal-header {
    background-color: #000;
    align-items: center;
    padding: 6px 8px 6px 20px;
    border: none; }
    .dialog-modal-area .modal-dialog .modal-header .modal-title {
      color: #8dd363;
      font-size: 16px;
      padding-right: 10px; }
      @media (min-width: 768px) {
        .dialog-modal-area .modal-dialog .modal-header .modal-title {
          font-size: 20px; } }
    .dialog-modal-area .modal-dialog .modal-header .btn-close {
      color: #8dd363;
      background-color: #181717;
      border-radius: 50%;
      width: 28px;
      height: 28px;
      position: relative;
      margin: auto 0;
      align-items: center;
      justify-content: center;
      display: flex;
      opacity: 1; }
      @media (min-width: 768px) {
        .dialog-modal-area .modal-dialog .modal-header .btn-close {
          width: 40px;
          height: 40px; } }
      .dialog-modal-area .modal-dialog .modal-header .btn-close:hover, .dialog-modal-area .modal-dialog .modal-header .btn-close:active {
        opacity: 1; }
      .dialog-modal-area .modal-dialog .modal-header .btn-close:after {
        content: '';
        background: url(data:image/png;base64,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) no-repeat 50% 50%;
        background-size: 14px 11px;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0; }
        @media (min-width: 768px) {
          .dialog-modal-area .modal-dialog .modal-header .btn-close:after {
            background-size: 17px 13px; } }
      .dialog-modal-area .modal-dialog .modal-header .btn-close span {
        display: none; }
  .dialog-modal-area .modal-dialog .modal-body {
    flex: 1; }
  .dialog-modal-area .modal-dialog .modal-footer {
    border: none; }
    .dialog-modal-area .modal-dialog .modal-footer:empty {
      padding: 0; }

#dialog-marketing-kit .modal-body {
  overflow-y: auto; }
#dialog-marketing-kit .accordion {
  margin: -1em -1em 0 -1em; }

#dialog-product-sheet .modal-body,
#video-dialog .modal-body {
  padding: 0; }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.device-mobile.in-portrait .modal-body .certificates-info .certificates-area {
  flex-direction: column; }
  .device-mobile.in-portrait .modal-body .certificates-info .certificates-area .list-group {
    flex: 0.23; }
  .device-mobile.in-portrait .modal-body .certificates-info .certificates-area .certificate-iframe, .device-mobile.in-portrait .modal-body .certificates-info .certificates-area .coming-soon-area {
    flex: 0.77; }

.device-desktop .certificates-area {
  flex-direction: row; }

.dialog-certificates .modal-body {
  display: flex;
  flex-direction: column;
  position: relative; }
  .dialog-certificates .modal-body .certificates-info {
    height: 100%;
    position: relative; }
    .dialog-certificates .modal-body .certificates-info .certificates-area {
      display: flex;
      position: absolute;
      top: 0;
      left: 4px;
      right: 4px;
      bottom: 10px; }
      .dialog-certificates .modal-body .certificates-info .certificates-area .coming-soon-area {
        width: 100%;
        height: 100%;
        text-align: center;
        vertical-align: middle;
        justify-content: center;
        display: flex;
        align-items: center;
        font-size: 30px;
        color: #8dd363; }
      .dialog-certificates .modal-body .certificates-info .certificates-area .list-group {
        border-radius: 8px;
        padding-right: 4px;
        min-width: 240px;
        overflow-x: auto; }
        .dialog-certificates .modal-body .certificates-info .certificates-area .list-group .list-group-item {
          display: flex;
          background-color: transparent;
          border: 1px solid #8dd363;
          border-radius: 8px;
          padding: 0;
          margin-bottom: 6px;
          overflow: hidden;
          line-height: 1.4rem;
          min-height: 40px; }
          .dialog-certificates .modal-body .certificates-info .certificates-area .list-group .list-group-item.coming-soon {
            opacity: 0.4; }
          .dialog-certificates .modal-body .certificates-info .certificates-area .list-group .list-group-item a {
            width: 100%;
            padding: 0.6rem;
            color: #8dd363;
            text-decoration: none;
            font-size: 14px;
            display: flex;
            flex-direction: row;
            line-height: 1.4rem; }
            .dialog-certificates .modal-body .certificates-info .certificates-area .list-group .list-group-item a .market-icon {
              width: 32px;
              margin-right: 14px; }
            .dialog-certificates .modal-body .certificates-info .certificates-area .list-group .list-group-item a .market-title {
              flex-wrap: nowrap;
              white-space: nowrap; }
            .dialog-certificates .modal-body .certificates-info .certificates-area .list-group .list-group-item a:hover, .dialog-certificates .modal-body .certificates-info .certificates-area .list-group .list-group-item a:active, .dialog-certificates .modal-body .certificates-info .certificates-area .list-group .list-group-item a.active {
              color: #000;
              text-decoration: none;
              background-color: #8dd363; }
              .dialog-certificates .modal-body .certificates-info .certificates-area .list-group .list-group-item a:hover svg, .dialog-certificates .modal-body .certificates-info .certificates-area .list-group .list-group-item a:active svg, .dialog-certificates .modal-body .certificates-info .certificates-area .list-group .list-group-item a.active svg {
                color: #000 !important; }

#first-entrance .btn-close {
  background-color: #fff; }

.dialog-video .modal-body {
  display: flex;
  flex-direction: column;
  position: relative; }
  .dialog-video .modal-body .video-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    background: linear-gradient(0deg, black, transparent); }
  .dialog-video .modal-body .list-group {
    flex-shrink: 0;
    padding: 0.75rem;
    flex-direction: row;
    min-width: 240px;
    overflow-x: auto;
    justify-content: center; }
    .dialog-video .modal-body .list-group .list-group-item {
      display: flex;
      background-color: transparent;
      border: 1px solid #8dd363;
      border-radius: 8px;
      padding: 0;
      overflow: hidden;
      line-height: 1.4rem;
      min-height: 40px;
      margin: 0 .375rem; }
      .dialog-video .modal-body .list-group .list-group-item.coming-soon {
        opacity: 0.4; }
      .dialog-video .modal-body .list-group .list-group-item a {
        padding: 6px 16px;
        font-size: 0.875rem;
        min-width: 120px;
        box-sizing: border-box;
        font-weight: 500;
        line-height: 1.75;
        border-radius: 4px;
        letter-spacing: 0.02857em;
        background: transparent;
        border-color: #8dd363;
        display: flex;
        justify-content: center;
        align-items: center;
        text-transform: uppercase; }
        .dialog-video .modal-body .list-group .list-group-item a .video-icon {
          width: 32px;
          margin-right: 14px; }
        .dialog-video .modal-body .list-group .list-group-item a .video-title {
          flex-wrap: nowrap;
          white-space: nowrap;
          line-height: 1; }
        .dialog-video .modal-body .list-group .list-group-item a:active, .dialog-video .modal-body .list-group .list-group-item a.active, .dialog-video .modal-body .list-group .list-group-item a:hover {
          color: #181717;
          text-decoration: none;
          background-color: #8dd363; }
          .dialog-video .modal-body .list-group .list-group-item a:active svg, .dialog-video .modal-body .list-group .list-group-item a.active svg, .dialog-video .modal-body .list-group .list-group-item a:hover svg {
            color: #000 !important; }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.games-table {
  position: relative;
  width: 100%;
  height: 100%; }
  .games-table .games-table-header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    width: 100%; }
  .games-table .games-table-data {
    display: block;
    position: absolute;
    top: 60px;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%; }
    .games-table .games-table-data-gameCode .game-code {
      max-width: calc(100% - 6px); }
      .games-table .games-table-data-gameCode .game-code span {
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis; }
  .games-table .gt-virtual > div > div > div:nth-child(even) {
    background-color: rgba(141, 211, 99, 0.01); }
  .games-table .gr-row {
    display: grid;
    grid-template-columns: 200px 8% 120px 120px 120px 70px 250px 1fr 100px;
    margin: auto;
    flex-direction: row;
    /* change this to row instead of 'column' */
    flex-wrap: wrap; }
    .games-table .gr-row.thead {
      grid-template-columns: 200px 8% 120px 120px 120px 70px 250px 1fr 100px 20px; }
    .games-table .gr-row div {
      color: #fff;
      padding: 1px;
      border-bottom: 1px solid #515151;
      border-right: 1px solid #313131;
      border-top: none;
      text-align: center;
      -moz-box-align: center;
      align-items: center;
      -moz-box-pack: center;
      justify-content: center;
      vertical-align: middle;
      display: flex;
      flex-direction: column; }
      .games-table .gr-row div .markets_subtitle {
        width: 125px;
        text-overflow: inherit;
        white-space: break-spaces;
        text-align: center;
        align-items: center;
        justify-content: center;
        vertical-align: middle;
        padding: 0.75rem;
        display: inline-block;
        border-right: 1px solid #000; }
      .games-table .gr-row div.th {
        background-color: #8dd363;
        color: #000;
        text-transform: uppercase;
        overflow: hidden;
        font-size: 0.8rem;
        font-weight: bold;
        border: none;
        text-align: center;
        justify-content: center;
        vertical-align: middle;
        flex-direction: row;
        align-items: stretch; }
        .games-table .gr-row div.th span {
          width: 100%;
          text-overflow: inherit;
          white-space: break-spaces;
          text-align: center;
          align-items: center;
          justify-content: center;
          vertical-align: middle;
          padding: 0.75rem;
          display: inline-block;
          border-right: 1px solid #000; }
          .games-table .gr-row div.th span.marketsAndReleaseDate_subtitle {
            width: 125px; }
      .games-table .gr-row div .action-col {
        display: flex;
        flex-direction: column;
        width: 110px;
        min-height: 130px;
        justify-content: flex-start;
        border: none; }
        .games-table .gr-row div .action-col a {
          text-align: left;
          background-color: transparent;
          color: #8dd363;
          line-height: 1em;
          padding: 4px;
          border-left: 1px solid rgba(141, 211, 99, 0.5);
          border-top: 1px solid rgba(141, 211, 99, 0.2);
          border-right: 1px solid rgba(141, 211, 99, 0.5);
          border-bottom: 1px solid rgba(141, 211, 99, 0.2);
          margin-top: 2px;
          margin-bottom: 4px;
          width: 100%; }
          .games-table .gr-row div .action-col a:hover {
            color: #000 !important;
            text-decoration: none !important;
            background-color: #8dd363 !important; }
        .games-table .gr-row div .action-col.primary a {
          background-color: #8dd363;
          color: #000; }
  .games-table table {
    position: relative;
    margin: auto; }
  .games-table .action-col {
    display: flex;
    flex-direction: column;
    width: 110px;
    min-height: 130px; }
    .games-table .action-col a {
      text-align: left;
      background-color: transparent;
      color: #8dd363;
      line-height: 1em;
      padding: 4px;
      border-left: 1px solid rgba(141, 211, 99, 0.5);
      border-top: 1px solid rgba(141, 211, 99, 0.2);
      border-right: 1px solid rgba(141, 211, 99, 0.5);
      border-bottom: 1px solid rgba(141, 211, 99, 0.2);
      margin-top: 2px;
      margin-bottom: 4px; }
      .games-table .action-col a:hover {
        color: #000 !important;
        text-decoration: none !important;
        background-color: #8dd363 !important; }
    .games-table .action-col.primary a {
      background-color: #8dd363;
      color: #000; }
  .games-table .page-link, .games-table .page-item.active .page-link {
    color: #000; }
  .games-table table thead th {
    background-color: #8dd363;
    color: #000;
    text-transform: uppercase;
    overflow: hidden;
    font-size: 0.8rem;
    border: none;
    border-right: 1px solid #000;
    text-align: center;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
    padding: .75rem; }
    .games-table table thead th span {
      width: 100%;
      text-overflow: inherit;
      white-space: break-spaces;
      text-align: center;
      align-items: center;
      justify-content: center;
      vertical-align: middle; }
  .games-table table tbody tr:nth-child(even) {
    background-color: rgba(81, 81, 81, 0.15); }
  .games-table table tbody td {
    color: #fff;
    padding: 1px;
    border-bottom: 1px solid #515151;
    border-top: none;
    text-align: center;
    align-items: center;
    justify-content: center;
    vertical-align: middle; }
    .games-table table tbody td:nth-child(even) {
      background-color: rgba(141, 211, 99, 0.01); }
    .games-table table tbody td .markets {
      display: flex;
      flex-direction: column;
      padding-left: 2px;
      white-space: break-spaces; }
    .games-table table tbody td ul.features {
      display: flex;
      justify-content: flex-start;
      flex-direction: column;
      padding-left: 2px; }
      .games-table table tbody td ul.features li {
        justify-content: center;
        align-self: center; }

.p-feature {
  text-overflow: ellipsis;
  overflow: hidden;
  margin-bottom: 0.1rem; }

.games-table-data-marketsAndReleaseDate .markets {
  flex-direction: row !important;
  border-right: 1px solid #313131 !important;
  align-items: stretch !important; }

.games-table-data-marketsAndReleaseDate .markets:last-child {
  border-bottom: none !important; }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.mobile-view {
  position: relative;
  width: 100%;
  height: 100%;
  overflow-x: auto; }
  .mobile-view table td {
    color: #fff; }
  .mobile-view .card .action-col-btns {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    max-width: 316px;
    margin: 0 auto; }
    @media (max-width: 412px) {
      .mobile-view .card .action-col-btns {
        margin: 0 calc(-0.75rem + -9px); } }
    .mobile-view .card .action-col-btns .btn {
      font-size: 12px;
      min-width: 140px;
      height: 27px;
      padding: 4px;
      margin: 9px 9px;
      border: 2px solid #8dd363;
      border-radius: 19px;
      color: #fff;
      font-weight: 700;
      text-transform: uppercase;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      background: #000;
      transition: none; }
      @media (max-width: 360px) {
        .mobile-view .card .action-col-btns .btn {
          font-size: 11px;
          min-width: calc(50% - 18px); } }
      .mobile-view .card .action-col-btns .btn.accent {
        color: #000;
        border-radius: 19px;
        background-color: #8dd363; }
  .mobile-view .table > :not(caption) > * > * {
    border-bottom-width: 0; }

.card .mobile-card-title {
  color: #fff;
  background-color: transparent; }
.card .mobile-card-href {
  display: flex;
  align-items: center;
  align-content: center;
  text-align: center;
  justify-content: center; }
  .card .mobile-card-href .mobile-card-poster {
    width: 428px;
    height: 268px;
    max-width: 100%; }
.card .card-body.table td, .card .card-body td {
  color: #fff;
  border-top: 1px solid #515151; }
  .card .card-body.table td.mobile-card-header, .card .card-body td.mobile-card-header {
    padding: 0; }
  .card .card-body.table td.mobile-card-header div, .card .card-body td.mobile-card-header div {
    background-color: #8dd363;
    color: #000;
    text-transform: uppercase;
    overflow: hidden;
    font-size: .8rem;
    font-weight: bold;
    width: 100%;
    padding: 14px; }
  .card .card-body.table td .action-col a, .card .card-body td .action-col a {
    text-align: left;
    background-color: transparent;
    color: #8dd363;
    line-height: 1em;
    padding: 4px;
    border-left: 1px solid rgba(141, 211, 99, 0.5);
    border-top: 1px solid rgba(141, 211, 99, 0.2);
    border-right: 1px solid rgba(141, 211, 99, 0.5);
    border-bottom: 1px solid rgba(141, 211, 99, 0.2);
    margin: 4px;
    width: 100px; }
    .card .card-body.table td .action-col a:hover, .card .card-body td .action-col a:hover {
      color: #000 !important;
      text-decoration: none !important;
      background-color: #8dd363 !important; }
  .card .card-body.table td .action-col.primary a, .card .card-body td .action-col.primary a {
    background-color: #8dd363;
    color: #000; }

.games-table-mob {
  position: relative;
  width: 100%;
  height: 100%; }
  .games-table-mob .games-table-mob-header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 40px;
    width: 100%; }
  .games-table-mob .games-table-mob-data {
    display: block;
    position: absolute;
    top: 40px;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%; }
  .games-table-mob .gr-row {
    display: grid;
    grid-template-columns: 80px 1fr 1fr 70px 1fr;
    margin: auto; }
    .games-table-mob .gr-row.thead {
      height: 100%; }
    .games-table-mob .gr-row .th, .games-table-mob .gr-row .td {
      font-size: 0.6875rem;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding: .25rem; }
    .games-table-mob .gr-row .th {
      background-color: #8dd363;
      color: #000;
      text-transform: uppercase;
      overflow: hidden;
      font-weight: bold;
      border-right: 1px solid #000; }
      .games-table-mob .gr-row .th:last-child {
        border-right: none; }
    .games-table-mob .gr-row .td {
      color: #fff;
      border-top: 1px solid #515151;
      border-right: 1px solid #313131;
      word-break: break-word;
      white-space: break-spaces; }
  .games-table-mob .text-truncate span {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.tagify {
  border: none !important; }

.tagify--outside {
  border: 0;
  width: 100%; }
  @media (min-width: 420px) {
    .tagify--outside {
      width: 90%; } }
  .tagify--outside .tagify__input {
    order: -1;
    flex: 100%;
    border: 2px solid #181717;
    margin-bottom: 1em;
    transition: 0.1s; }
  .tagify--outside .tagify__input:hover {
    border-color: #181717; }

.tagify__dropdown {
  position: fixed !important; }
  .tagify__dropdown.tagify__dropdown--text {
    background-color: transparent;
    color: #181717;
    border: none; }
  .tagify__dropdown__wrapper {
    background-color: transparent;
    border: none; }
    .tagify__dropdown__wrapper .tagify__dropdown__item {
      background-color: #181717;
      color: #8dd363;
      border: 1px solid #8dd363; }

.tagify__tag {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #181717;
  border: 2px solid #181717;
  background-color: transparent;
  padding: 2px 8px;
  border-radius: 29px;
  min-width: initial;
  margin: 2px 4px;
  transition: all 0.2s ease;
  cursor: pointer;
  text-transform: uppercase;
  text-align: center;
  font-size: 14px;
  font-weight: 700;
  line-height: 1; }
  .tagify__tag > div::before {
    box-shadow: none !important; }
  .tagify__tag:hover:not([readonly]) div::before {
    box-shadow: none !important; }

.device-desktop .search-banner {
  max-width: 800px;
  max-height: 190px; }
  @media (max-width: 1280px) {
    .device-desktop .search-banner {
      display: none; } }
@media (min-width: 1279px) {
  .device-desktop .search-banner-top {
    display: none;
    height: 0; } }
.device-desktop .sw-search .sw-search-container {
  min-width: 800px; }

.device-mobile .search-banner {
  display: none;
  height: 0;
  width: 0; }
.device-mobile .sw-search-container {
  flex: initial; }

.search-banner {
  display: block; }
  .search-banner video {
    width: 100%;
    display: block;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center; }

.device-desktop .search-banner .desktop-video {
  display: block; }
.device-desktop .search-banner .mobile-landscape-video {
  display: none; }
.device-desktop .search-banner .mobile-portrait-video {
  display: none; }

.device-mobile.in-landscape .search-banner .desktop-video {
  display: none; }
.device-mobile.in-landscape .search-banner .mobile-landscape-video {
  display: block; }
.device-mobile.in-landscape .search-banner .mobile-portrait-video {
  display: none; }

.device-mobile.in-portrait .search-banner .desktop-video {
  display: none; }
.device-mobile.in-portrait .search-banner .mobile-landscape-video {
  display: none; }
.device-mobile.in-portrait .search-banner .mobile-portrait-video {
  display: block; }

#partners-search {
  flex: 1;
  display: flex;
  flex-direction: column; }
  #partners-search .banner-image {
    flex-shrink: 0; }
  @media (min-width: 1281px) {
    .device-desktop #partners-search .banner-swiper.search-banner {
      margin-left: 0; }
    .device-desktop #partners-search .swiper:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      width: 1px;
      background: #181717;
      z-index: 1000; } }

.sw-search {
  background-color: rgba(24, 23, 23, 0.95);
  width: 100%;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: stretch;
  flex-shrink: 0; }
  .sw-search .banner-image {
    flex: .5;
    margin-right: 20px; }
  .sw-search__input {
    display: block;
    text-transform: uppercase;
    color: #fff;
    background-color: #1e1d1d;
    border-radius: 18px;
    width: 100%;
    height: 100%;
    margin: 2px 0 2px 2px;
    font-size: 14px;
    font-weight: 700;
    letter-spacing: 0.012em;
    flex-shrink: 0;
    border: 2px solid transparent;
    padding: 10px 85px 10px 20px; }
    @media (max-width: 480px) {
      .sw-search__input {
        margin: 0;
        border-color: #8dd363;
        padding: 17px 80px 17px 20px; } }
  .sw-search__close {
    flex-shrink: 0;
    width: 70px;
    height: 57px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer; }
    @media (max-width: 480px) {
      .sw-search__close {
        width: 60px;
        height: 57px; }
        .sw-search__close svg text {
          fill: #8dd363; } }
  .sw-search__btn {
    cursor: pointer;
    width: 60px;
    height: 50px;
    position: absolute;
    right: 0;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1; }
    @media (max-width: 480px) {
      .sw-search__btn {
        top: 3px;
        height: 53px; } }

#openResult {
  visibility: hidden;
  position: relative;
  left: 50%;
  top: 5px;
  transform: translateX(-50%);
  z-index: 99999;
  border: none;
  outline: none;
  text-transform: uppercase;
  background-color: firebrick;
  font-weight: 700;
  font-size: 16px;
  margin-bottom: 40px;
  width: 200px;
  cursor: pointer; }
  #openResult.on {
    visibility: visible; }

.sw-search-container {
  width: 100%;
  height: 100%;
  padding: 4px;
  flex-direction: column;
  overflow-x: hidden;
  overflow-y: auto;
  -ms-overflow-style: none; }
  @media (max-width: 1199px) {
    .sw-search-container {
      padding: 4px; } }
  @media (max-width: 480px) {
    .sw-search-container {
      padding-bottom: 4px; } }
  .sw-search-container::-webkit-scrollbar {
    width: 0;
    background: transparent; }
  .sw-search-container__menu {
    width: 100%;
    margin-bottom: auto;
    position: relative; }
    @media (max-width: 480px) {
      .sw-search-container__menu.open .sw-search__close text {
        fill: #1e1d1d; } }
  .sw-search-container__result {
    margin-top: 10px;
    margin-bottom: auto;
    width: 100%; }

.sw-search-control {
  border-radius: 18px;
  margin-top: auto;
  width: 100%;
  background-color: #8dd363;
  transition: all 0.2s ease;
  position: relative;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center; }
  .sw-search-control .f_filters_btn {
    background-color: #8dd363;
    color: #000;
    text-transform: uppercase;
    overflow: hidden;
    font-size: 0.8rem;
    font-weight: bold;
    border: none;
    border-radius: 18px; }
    .sw-search-control .f_filters_btn:hover, .sw-search-control .f_filters_btn:active, .sw-search-control .f_filters_btn:focus, .sw-search-control .f_filters_btn:not(:disabled):not(.disabled):active {
      background-color: #8dd363;
      color: #FFFFFF; }
  .sw-search-control label {
    width: 100%;
    display: flex;
    position: relative;
    margin-bottom: 0; }
  @media (max-width: 480px) {
    .sw-search-control {
      background-color: transparent; } }

.sw-search-submenu {
  border-radius: 18px;
  background-color: #8dd363;
  width: 100%;
  height: 100%;
  display: none;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  color: #181717;
  padding: 72px 12px 18px; }
  .sw-search-submenu.open {
    display: flex; }
    @media (max-width: 480px) {
      .sw-search-submenu.open {
        padding: 70px 7px 18px; } }
  .sw-search-submenu__close {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-width: 100px;
    max-width: 120px;
    flex-shrink: 0;
    border-left: 1px solid rgba(0, 0, 0, 0.5);
    cursor: pointer;
    margin-left: 4px; }
    @media (max-width: 480px) {
      .sw-search-submenu__close {
        min-width: 88px; } }
    .sw-search-submenu__close div {
      text-transform: uppercase;
      font-size: 14px;
      font-weight: bold;
      display: inline-block;
      text-align: center; }
      .sw-search-submenu__close div span {
        display: block; }
  .sw-search-submenu__types {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center; }

.sw-search-filter {
  position: relative;
  width: 100%;
  height: calc(100% - 57px);
  min-height: 80px;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-top: 26px; }
  @media (max-width: 1250px) {
    .sw-search-filter {
      flex-wrap: wrap; } }
  .sw-search-filter__item {
    overflow: hidden;
    border-radius: 18px;
    height: 80px;
    min-width: 66px;
    max-width: 130px;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    position: relative;
    padding: 1px;
    z-index: 1;
    flex-shrink: 0;
    margin: auto; }
    .sw-search-filter__item:hover {
      opacity: 0.9; }
    .sw-search-filter__item--logo {
      position: relative;
      min-height: 40px;
      max-height: 40px;
      width: 40px;
      margin-bottom: 10px; }
      .sw-search-filter__item--logo img {
        display: inline-block;
        transition: all 0.2s ease;
        filter: grayscale(80%) brightness(50%) contrast(50%);
        margin: auto;
        max-width: 100%;
        height: 100%;
        object-fit: contain; }
    .sw-search-filter__item--text {
      width: 100%;
      text-transform: uppercase;
      color: #fff;
      font-size: 13px;
      font-weight: 700;
      margin: 0 4px; }
      @media (max-width: 1024px) {
        .sw-search-filter__item--text {
          font-size: 12px; } }
    .sw-search-filter__item--close {
      display: none;
      align-items: center;
      justify-content: center;
      position: absolute;
      right: 0;
      top: 0;
      z-index: 2;
      width: 30px;
      height: 30px;
      cursor: pointer; }
      .sw-search-filter__item--close:hover {
        opacity: 0.7; }
    .sw-search-filter__item.clear-all-filter {
      opacity: 0.5;
      cursor: auto; }
    .sw-search-filter__item.active.clear-all-filter {
      opacity: 1;
      cursor: pointer; }
    .sw-search-filter__item.active .sw-search-filter__item--close {
      display: flex; }
    .sw-search-filter__item.active .sw-search-filter__item--logo img {
      filter: none; }

.sw-search-submenu-inner {
  display: none; }
  .sw-search-submenu-inner.open {
    display: flex;
    width: 100%;
    padding: 0 15px; }
    @media (max-width: 480px) {
      .sw-search-submenu-inner.open {
        padding: 0 10px; } }
  .sw-search-submenu-inner div {
    animation: 0.15s visibleTools 0.15s linear both; }

.sw-search-tags {
  display: flex;
  align-items: center;
  flex-wrap: wrap; }

.sw-search-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1e1d1d;
  border: 2px solid #1e1d1d;
  padding: 4px 6px;
  border-radius: 29px;
  min-width: 110px;
  margin: 6px;
  transition: all 0.2s ease;
  cursor: pointer;
  text-transform: uppercase;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
  line-height: 1; }
  @media (max-width: 480px) {
    .sw-search-tag {
      /*margin: 15px 10px;*/
      min-width: auto; } }
  .sw-search-tag--small {
    min-width: initial; }
  .sw-search-tag.active {
    color: #fff;
    background-color: #1e1d1d; }

.sw-search-range {
  min-width: 210px;
  width: 100%;
  margin: auto auto auto 0; }
  .sw-search-range__slider {
    height: 50px; }
  .sw-search-range__values {
    display: flex;
    align-items: center; }
    .sw-search-range__values--from, .sw-search-range__values--to {
      font-weight: bold;
      letter-spacing: 0.12em;
      display: flex;
      align-items: center;
      position: relative; }
      .sw-search-range__values--from::after, .sw-search-range__values--to::after {
        content: "%"; }
    .sw-search-range__values--to {
      margin-left: auto; }
  .sw-search-range .irs-line {
    height: 6px;
    border-radius: 0;
    left: 14px;
    width: calc(100% - 29px);
    background-color: rgba(23, 22, 22, 0.2); }
    @media (max-width: 480px) {
      .sw-search-range .irs-line {
        width: calc(100% - 27px); } }
  .sw-search-range .irs-bar {
    height: 6px;
    background-color: #171616; }
  .sw-search-range .irs-handle {
    cursor: pointer;
    width: 30px;
    height: 30px;
    top: 13px; }
    .sw-search-range .irs-handle i:first-child {
      width: 6px;
      height: 26px;
      background-color: #171616;
      margin-left: -3px;
      top: 2px; }
  .sw-search-range .irs-handle.state_hover > i:first-child,
  .sw-search-range .irs-handle:hover > i:first-child {
    background-color: #000; }
  .sw-search-range .irs-grid-pol {
    background-color: #000;
    width: 2px;
    height: 4px;
    top: auto;
    bottom: 0; }
  .sw-search-range .irs-grid-text {
    display: none; }
  .sw-search-range .irs-grid {
    top: 5px; }

.sw-search-checkboxes-group {
  margin: 0 10px; }

.sw-search-checkbox {
  position: relative;
  display: block;
  margin-bottom: 2px; }
  .sw-search-checkbox:last-child {
    margin-bottom: 0; }
  .sw-search-checkbox input {
    vertical-align: top;
    width: 18px;
    height: 18px; }
    .sw-search-checkbox input + label {
      text-transform: uppercase;
      font-size: 16px;
      font-weight: 700;
      cursor: pointer; }
    .sw-search-checkbox input:not(checked) {
      position: absolute;
      z-index: -1;
      opacity: 0; }
      .sw-search-checkbox input:not(checked) + label {
        position: relative;
        padding: 0 0 0 28px; }
        .sw-search-checkbox input:not(checked) + label::before {
          content: "";
          position: absolute;
          top: 2px;
          left: 0;
          width: 18px;
          height: 18px;
          border: 1px solid #cdd0d8;
          border-radius: 50%;
          background-color: #fff; }
        .sw-search-checkbox input:not(checked) + label::after {
          content: "";
          position: absolute;
          top: 7px;
          left: 5px;
          width: 8px;
          height: 8px;
          background: #181717;
          border-radius: 50%;
          opacity: 0;
          transition: 0.2s; }
    .sw-search-checkbox input:checked + label::before {
      border-color: #181717; }
    .sw-search-checkbox input:checked + label::after {
      opacity: 1; }

.sw-search--inner {
  display: block;
  position: static; }
  .sw-search--inner .sw-search-container {
    max-width: 100%; }

.sw-search-result__title {
  display: block;
  font-family: "Oswald", sans-serif;
  color: #8dd363;
  text-align: center;
  margin-bottom: 20px; }
  .sw-search-result__title strong {
    font-weight: normal;
    text-transform: uppercase;
    font-size: 35px;
    line-height: 30px;
    letter-spacing: 0.32em; }
    .sw-search-result__title strong #search {
      margin-right: 20px; }
.sw-search-result__btnTop {
  margin: 25px auto auto;
  display: flex;
  align-items: center;
  justify-content: center; }
  @media (max-width: 480px) {
    .sw-search-result__btnTop {
      margin-top: 30px; } }
  .sw-search-result__btnTop a {
    text-transform: uppercase;
    font-weight: bold;
    letter-spacing: 0.12em;
    font-size: 15px;
    line-height: 1;
    white-space: nowrap;
    color: #8dd363;
    border-radius: 29px;
    border: 2px solid #8dd363;
    padding: 21px 76px;
    transition: all 0.2s ease; }
    .sw-search-result__btnTop a:hover {
      color: #181717;
      background-color: #8dd363; }
.sw-search-result .sw-search-result-item:last-of-type {
  border-bottom-color: transparent; }

.sw-search-result-item {
  padding: 20px 0;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
  position: relative; }
  .sw-search-result-item::after {
    transition: all 0.2s ease;
    opacity: 0;
    z-index: -1;
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(270deg, rgba(0, 0, 0, 0) 0%, rgba(249, 189, 33, 0.1) 100%); }
  .sw-search-result-item__img {
    flex-shrink: 0;
    width: 100%;
    max-width: 176px;
    min-height: 100px;
    background-color: #1e1d1d;
    margin-right: 30px; }
    @media (max-width: 568px) {
      .sw-search-result-item__img {
        margin-right: 0; } }
  .sw-search-result-item__desc {
    display: flex;
    flex-direction: column;
    height: 100%;
    margin: auto auto auto 0; }
    @media (max-width: 568px) {
      .sw-search-result-item__desc {
        margin-top: 20px; } }
  .sw-search-result-item:hover {
    opacity: 0.9; }
    .sw-search-result-item:hover::after {
      opacity: 1; }

.device-desktop .sw-search-result-item {
  flex-wrap: nowrap; }

.device-type-mobile .sw-search-result-item {
  flex-wrap: nowrap; }
  @media (max-width: 767px) {
    .device-type-mobile .sw-search-result-item {
      flex-wrap: wrap; } }

.search-result-text {
  height: 100%;
  display: flex;
  flex-direction: column; }
  .search-result-text__title {
    text-transform: uppercase;
    font-weight: bold;
    font-size: 14px;
    letter-spacing: 0.12em; }
  .search-result-text__tags {
    margin-top: 20px; }
    .search-result-text__tags span {
      color: #aea8a8;
      font-size: 14px;
      letter-spacing: 0.12em;
      display: inline-block;
      margin-right: 15px;
      position: relative; }
      .search-result-text__tags span::after {
        content: ";"; }

.sw-search-range.adv-rtp .sw-search-range__values {
  display: none; }

.sw-search-range.adv-rtp .irs--flat .irs-to,
.sw-search-range.adv-rtp .irs--flat .irs-from,
.sw-search-range.adv-rtp .irs--flat .irs-single,
.sw-search-range.adv-rtp .irs--flat .irs-to,
.sw-search-range.adv-rtp .irs--flat .irs-max,
.sw-search-range.adv-rtp .irs--flat .irs-min {
  font-size: 14px;
  font-weight: 700;
  background-color: transparent;
  letter-spacing: 0.12em;
  color: #000;
  margin-top: -10px; }

.sw-search-range.adv-rtp .irs--flat .irs-from:before,
.sw-search-range.adv-rtp .irs--flat .irs-single:before,
.sw-search-range.adv-rtp .irs--flat .irs-to:before {
  border-top-color: #000; }

.sw-search-tags.adv-tags {
  flex: 1;
  width: 100%; }

.search-item-disabled {
  cursor: not-allowed;
  opacity: 0.4; }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.metadata {
  font-size: 16px;
  margin-bottom: 12.96vh; }
  @media (min-width: 1921px), (min-height: 1081px) {
    .metadata {
      margin-bottom: 140px; } }
  .metadata__container {
    margin: 60px 0 0; }
    @media (max-width: 480px) {
      .metadata__container {
        margin-top: 50px; } }
    @media (max-width: 479px) {
      .metadata__container {
        margin-top: 12.5vw; } }
  .metadata__item {
    border-radius: 27px;
    margin-bottom: 40px;
    transition: all 0.2s ease;
    position: relative;
    background: #181717; }
    @media (max-width: 479px) {
      .metadata__item {
        margin-bottom: 8.33vw; } }
    .metadata__item::after {
      z-index: -1;
      border-radius: inherit;
      content: "";
      position: absolute;
      left: -2px;
      top: -2px;
      background: linear-gradient(#424242, #1E1D1D);
      width: calc(100% + 4px);
      height: calc(100% + 4px); }
    .metadata__item.open::after {
      background: #8dd363; }
    .metadata__item.open .metadata__title-icon {
      transform: scaleY(-1) rotate(135deg); }
    .metadata__item:last-child {
      margin-bottom: 0; }
  .metadata__description {
    display: none;
    border-top: 2px solid rgba(255, 255, 255, 0.3);
    padding: 45px 67px 55px; }
    .open .metadata__description {
      display: block; }
    @media (max-width: 1024px) {
      .metadata__description {
        padding: 45px 42px 55px; } }
    @media (max-width: 767px) {
      .metadata__description {
        padding: 45px 24px 55px; } }
    @media (max-width: 479px) {
      .metadata__description {
        padding: 9.375vw 6.66vw 11.458vw; } }
  .metadata__title {
    outline: none;
    cursor: pointer;
    padding: 40px 35px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 2.5em;
    line-height: 1; }
    @media (max-width: 767px) {
      .metadata__title {
        font-size: 1.875em;
        line-height: 1.2;
        padding: 27px 18px; } }
    @media (max-width: 479px) {
      .metadata__title {
        font-size: 6.25vw;
        padding: 9.375vw 18px; } }
    .metadata__title-icon {
      position: relative;
      display: inline-block;
      width: 26px;
      height: 5px;
      background: #A9A9A9;
      transform: scaleY(1) rotate(135deg);
      transform-origin: center;
      border-radius: 5px;
      will-change: transform; }
      @media (max-width: 767px) {
        .metadata__title-icon {
          display: none; } }
      .metadata__title-icon::after {
        content: "";
        position: absolute;
        right: 0;
        top: 0;
        background: inherit;
        height: 26px;
        width: 5px;
        border-radius: 5px; }

.metadata-table {
  font-size: 24px;
  line-height: 1.2;
  text-transform: uppercase;
  width: 100%;
  border-collapse: collapse; }
  @media (max-width: 1024px) {
    .metadata-table {
      font-size: 20px; } }
  @media (max-width: 767px) {
    .metadata-table {
      font-size: 18px; } }
  @media (max-width: 479px) {
    .metadata-table {
      font-size: 14px; } }
  .metadata-table td {
    padding: 8px; }
    @media (min-width: 1024px) {
      .metadata-table td:first-child {
        width: 35%; } }
  .metadata-table tr:nth-child(even) {
    background: linear-gradient(to right, #191818 0, #1e1d1d 15%, #1e1d1d 50%, #1e1d1d 85%, #191818 100%); }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

#dialog-marketing-kit .modal-body .mk-list-items .card .card-header {
  padding: 2px;
  overflow-anchor: none;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease; }
  #dialog-marketing-kit .modal-body .mk-list-items .card .card-header button {
    width: 100%;
    text-align: left;
    border: 1px solid #8dd363;
    border-radius: 8px;
    background-color: transparent;
    font-size: 1rem;
    color: #8dd363;
    padding: 1rem 1.25rem; }
#dialog-marketing-kit .modal-body .mk-list-items .card .card-body {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column; }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

.header-tools {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  max-width: 100%;
  width: 100%;
  vertical-align: middle;
  height: 80vh;
  max-height: 1200px;
  min-height: 900px;
  overflow: hidden;
  position: relative; }
  .header-tools img {
    display: block;
    max-width: 100%;
    height: auto; }
  .header-tools .wrap {
    max-width: 1440px;
    margin-left: auto;
    margin-right: auto;
    position: relative; }
  @media (max-width: 1024px) {
    .header-tools {
      min-height: 500px;
      max-height: 900px; } }
  @media (max-width: 700px) {
    .header-tools {
      height: 80vh;
      max-height: 700px; } }
  @media (max-width: 420px) {
    .header-tools {
      max-height: 570px; } }
  .header-tools .header-desc__subtitle,
  .header-tools .header-desc__title {
    text-shadow: 2px 2px 0 #181717, 8px 8px 15px #181717; }
  .header-tools::before {
    position: absolute;
    z-index: 2;
    left: 0;
    top: 0;
    content: "";
    width: 100%;
    height: 294px;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0) 0%, black 100%); }
    @media (max-width: 1024px) {
      .header-tools::before {
        height: 170px; } }
    @media (max-width: 480px), (max-width: 812px) and (orientation: landscape) {
      .header-tools::before {
        height: 100px; } }
  .header-tools::after {
    position: absolute;
    z-index: 1;
    left: 0;
    bottom: 0;
    content: "";
    width: 100%;
    height: 600px;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #181717 90%); }
    @media only screen and (max-width: 1439px) and (max-height: 900px) {
      .header-tools::after {
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #181717 75%); } }
    @media (max-width: 480px), (max-width: 812px) and (orientation: landscape) {
      .header-tools::after {
        height: 250px; } }
  .header-tools--tools {
    background-image: none;
    min-height: 80vh; }
    @media (min-width: 1921px), (min-height: 1081px) {
      .header-tools--tools {
        min-height: inherit;
        z-index: 3;
        max-height: 1100px; } }
    @media (min-width: 900px) and (max-width: 1024px) and (orientation: landscape) {
      .header-tools--tools {
        min-height: 70vh;
        height: 70vh; } }
    @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
      .header-tools--tools {
        min-height: 50vh;
        height: 50vh; } }
    @media (max-width: 767px) {
      .header-tools--tools .header-desc {
        display: block; } }
    @media (max-width: 1023px) {
      .header-tools--tools .header-desc__subtitle,
      .header-tools--tools .header-desc__logo {
        display: none; } }
    @media (max-width: 900px) and (orientation: landscape) {
      .header-tools--tools .header-desc {
        display: none; } }
    .header-tools--tools::after {
      height: 100px; }
    .header-tools--tools .title--h1 {
      letter-spacing: 0.32em; }
      @media (max-width: 900px) and (orientation: landscape) {
        .header-tools--tools .title--h1 {
          text-align: left; } }
      @media (max-width: 480px) {
        .header-tools--tools .title--h1 {
          font-size: 35px;
          line-height: 1em;
          letter-spacing: 0.15em; } }
    @media (max-width: 900px) and (orientation: landscape) {
      .header-tools--tools .header-desc__subtitle {
        text-align: left; } }
    @media (max-width: 480px) {
      .header-tools--tools .header-desc__subtitle {
        font-size: 16px;
        line-height: 36px;
        letter-spacing: 0.075em;
        font-weight: 400; } }
    .header-tools--tools .gameposter img {
      height: auto; }
  .header-tools .tools-header-text {
    pointer-events: none;
    width: 100%;
    height: 100%;
    max-height: 1080px;
    max-width: 1920px;
    text-transform: uppercase;
    font-weight: 700;
    font-size: 20px;
    line-height: 1;
    color: #8dd363;
    position: absolute;
    left: 48%;
    top: 0;
    transform: translateX(-50%);
    z-index: 2; }
    @media (max-width: 1366px) {
      .header-tools .tools-header-text {
        font-size: 18px; } }
    @media (max-width: 1024px) {
      .header-tools .tools-header-text {
        left: 0;
        transform: none; } }
    @media (max-width: 1023px) {
      .header-tools .tools-header-text {
        font-size: 16px;
        left: -5%; } }
    @media (max-width: 480px) {
      .header-tools .tools-header-text {
        top: -17%; } }
    @media (max-width: 400px) and (max-device-height: 667px) {
      .header-tools .tools-header-text {
        top: -14%; } }
    @media (min-width: 3400px) {
      .header-tools .tools-header-text {
        left: 49%; } }
    .header-tools .tools-header-text span {
      display: block;
      position: absolute;
      z-index: 2; }
      @media (max-width: 1024px) {
        .header-tools .tools-header-text span {
          animation-delay: 0s !important; } }
      .header-tools .tools-header-text span:nth-child(1) {
        width: 170px;
        left: 48%;
        top: 20%;
        opacity: 0;
        animation: fadeIn 1s linear 0.5s both; }
        @media (max-width: 1024px) {
          .header-tools .tools-header-text span:nth-child(1) {
            top: 25%;
            left: 48%; } }
        @media (max-width: 1024px) and (orientation: portrait) {
          .header-tools .tools-header-text span:nth-child(1) {
            top: 13%;
            left: 47%; } }
        @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
          .header-tools .tools-header-text span:nth-child(1) {
            left: 48%;
            top: 20%; } }
        @media (max-width: 1023px) {
          .header-tools .tools-header-text span:nth-child(1) {
            left: 25%;
            top: 45%; } }
        @media (max-width: 480px) {
          .header-tools .tools-header-text span:nth-child(1) {
            top: 55%;
            left: 25%; } }
        @media (max-width: 900px) and (orientation: landscape) {
          .header-tools .tools-header-text span:nth-child(1) {
            display: none; } }
      .header-tools .tools-header-text span:nth-child(2) {
        text-align: right;
        width: 130px;
        left: 43%;
        top: 45%;
        opacity: 0;
        animation: fadeIn 1s linear 1s both; }
        @media (max-width: 1440px) {
          .header-tools .tools-header-text span:nth-child(2) {
            left: 40%; } }
        @media (max-width: 1024px) {
          .header-tools .tools-header-text span:nth-child(2) {
            top: 57%; } }
        @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
          .header-tools .tools-header-text span:nth-child(2) {
            left: 46%;
            top: 50%; } }
        @media (max-width: 1023px) {
          .header-tools .tools-header-text span:nth-child(2) {
            text-align: left;
            width: 200px;
            left: 15%;
            top: 65%; } }
        @media (max-width: 900px) and (orientation: landscape) {
          .header-tools .tools-header-text span:nth-child(2) {
            display: none; } }
        @media (max-width: 480px) {
          .header-tools .tools-header-text span:nth-child(2) {
            top: 68%;
            left: 10%; } }
      .header-tools .tools-header-text span:nth-child(3) {
        width: 140px;
        left: 85%;
        top: 25%;
        opacity: 0;
        animation: fadeIn 1s linear 1.5s both; }
        @media (max-width: 1440px) {
          .header-tools .tools-header-text span:nth-child(3) {
            left: 82%; } }
        @media (max-width: 1024px) {
          .header-tools .tools-header-text span:nth-child(3) {
            left: 85%;
            top: 35%; } }
        @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
          .header-tools .tools-header-text span:nth-child(3) {
            left: 85%;
            top: 25%; } }
        @media (max-width: 1023px) {
          .header-tools .tools-header-text span:nth-child(3) {
            left: 25%;
            top: 85%; } }
        @media (max-width: 480px) {
          .header-tools .tools-header-text span:nth-child(3) {
            top: 82%; } }
        @media (max-width: 900px) and (orientation: landscape) {
          .header-tools .tools-header-text span:nth-child(3) {
            display: none; } }
      .header-tools .tools-header-text span:nth-child(4) {
        width: 130px;
        left: 82%;
        top: 51%;
        opacity: 0;
        animation: fadeIn 1s linear 2s both; }
        @media (max-width: 1440px) {
          .header-tools .tools-header-text span:nth-child(4) {
            top: 54%; } }
        @media (max-width: 1024px) {
          .header-tools .tools-header-text span:nth-child(4) {
            left: 87%;
            top: 67%; } }
        @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
          .header-tools .tools-header-text span:nth-child(4) {
            left: 86%;
            top: 55%; } }
        @media (max-width: 1023px) {
          .header-tools .tools-header-text span:nth-child(4) {
            visibility: hidden; } }
    .header-tools .tools-header-text__jp span:nth-child(1) {
      left: 53%;
      top: 30%; }
      @media (min-width: 1921px), (min-height: 1081px) {
        .header-tools .tools-header-text__jp span:nth-child(1) {
          left: 55%; } }
      @media (max-width: 1600px) {
        .header-tools .tools-header-text__jp span:nth-child(1) {
          top: 25%; } }
      @media (max-width: 1024px) {
        .header-tools .tools-header-text__jp span:nth-child(1) {
          top: 22%;
          left: 45%; } }
      @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
        .header-tools .tools-header-text__jp span:nth-child(1) {
          top: 15%;
          left: 45%; } }
      @media (max-width: 1023px) {
        .header-tools .tools-header-text__jp span:nth-child(1) {
          visibility: hidden; } }
      @media (max-width: 768px) {
        .header-tools .tools-header-text__jp span:nth-child(1) {
          visibility: visible;
          top: 52%;
          left: 39%;
          width: 190px; } }
      @media (max-width: 480px) {
        .header-tools .tools-header-text__jp span:nth-child(1) {
          top: 69%; } }
      @media (max-width: 900px) and (orientation: landscape) {
        .header-tools .tools-header-text__jp span:nth-child(1) {
          display: none; } }
    .header-tools .tools-header-text__jp span:nth-child(2) {
      width: 200px;
      left: 76%;
      top: 25%;
      text-align: left; }
      @media (min-width: 1921px), (min-height: 1081px) {
        .header-tools .tools-header-text__jp span:nth-child(2) {
          left: 77%; } }
      @media (max-width: 1600px) {
        .header-tools .tools-header-text__jp span:nth-child(2) {
          top: 20%; } }
      @media (max-width: 1024px) {
        .header-tools .tools-header-text__jp span:nth-child(2) {
          top: 20%;
          left: 75%; } }
      @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
        .header-tools .tools-header-text__jp span:nth-child(2) {
          top: 20%;
          left: 75%; } }
      @media (max-width: 1023px) {
        .header-tools .tools-header-text__jp span:nth-child(2) {
          visibility: hidden; } }
      @media (max-width: 768px) {
        .header-tools .tools-header-text__jp span:nth-child(2) {
          visibility: visible;
          top: 70%;
          left: 55%;
          width: 160px; } }
      @media (max-width: 480px) {
        .header-tools .tools-header-text__jp span:nth-child(2) {
          top: 82%; } }
      @media (max-width: 900px) and (orientation: landscape) {
        .header-tools .tools-header-text__jp span:nth-child(2) {
          display: none; } }
    .header-tools .tools-header-text__jp span:nth-child(3) {
      left: 84%;
      top: 38%; }
      @media (min-width: 1921px), (min-height: 1081px) {
        .header-tools .tools-header-text__jp span:nth-child(3) {
          left: 86%; } }
      @media (max-width: 1600px) {
        .header-tools .tools-header-text__jp span:nth-child(3) {
          top: 32%; } }
      @media (max-width: 1024px) {
        .header-tools .tools-header-text__jp span:nth-child(3) {
          left: 84%;
          top: 38%; } }
      @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
        .header-tools .tools-header-text__jp span:nth-child(3) {
          left: 84%;
          top: 38%; } }
      @media (max-width: 1023px) {
        .header-tools .tools-header-text__jp span:nth-child(3) {
          visibility: hidden; } }
      @media (max-width: 768px) {
        .header-tools .tools-header-text__jp span:nth-child(3) {
          visibility: visible;
          top: 87%;
          left: 75%; } }
      @media (max-width: 480px) {
        .header-tools .tools-header-text__jp span:nth-child(3) {
          top: 95%; } }
      @media (max-width: 480px) {
        .header-tools .tools-header-text__jp span:nth-child(3) {
          left: 78%; } }
      @media (max-width: 900px) and (orientation: landscape) {
        .header-tools .tools-header-text__jp span:nth-child(3) {
          display: none; } }
    .header-tools .tools-header-text__jp--mobile {
      display: block;
      position: absolute;
      top: 35%;
      left: 18%; }
      @media (min-width: 769px), (max-width: 1024px) and (orientation: landscape) {
        .header-tools .tools-header-text__jp--mobile {
          display: none !important; } }
      @media (max-width: 480px) {
        .header-tools .tools-header-text__jp--mobile {
          top: 52%;
          left: 15%; } }
      .header-tools .tools-header-text__jp--mobile div {
        font-family: "Open Sans", sans-serif;
        font-weight: 400;
        text-align: center; }
      .header-tools .tools-header-text__jp--mobile div:nth-child(1) {
        font-size: 44px;
        color: #f9d876; }
        @media (max-width: 480px) {
          .header-tools .tools-header-text__jp--mobile div:nth-child(1) {
            font-size: 9vw;
            white-space: nowrap; } }
      .header-tools .tools-header-text__jp--mobile div:nth-child(2) {
        margin-top: 10px;
        font-size: 16px;
        color: #fff1c8; }
    @media (max-width: 1023px) {
      .header-tools .tools-header-text__tournam {
        left: -10%; } }
    @media (max-width: 480px) {
      .header-tools .tools-header-text__tournam {
        top: -7%; } }
    .header-tools .tools-header-text__tournam span:nth-child(1) {
      width: 120px;
      left: 60%;
      top: 40%; }
      @media (min-width: 1921px), (min-height: 1081px) {
        .header-tools .tools-header-text__tournam span:nth-child(1) {
          left: 62%; } }
      @media (max-width: 1600px) {
        .header-tools .tools-header-text__tournam span:nth-child(1) {
          top: 33%; } }
      @media (max-width: 1024px) {
        .header-tools .tools-header-text__tournam span:nth-child(1) {
          left: 58%;
          top: 63%; } }
      @media (max-width: 1023px) {
        .header-tools .tools-header-text__tournam span:nth-child(1) {
          visibility: hidden; } }
      @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
        .header-tools .tools-header-text__tournam span:nth-child(1) {
          left: 50%;
          top: 76%; } }
      @media (max-width: 768px) {
        .header-tools .tools-header-text__tournam span:nth-child(1) {
          visibility: visible;
          left: 35%;
          top: 50%; } }
      @media (max-width: 480px) {
        .header-tools .tools-header-text__tournam span:nth-child(1) {
          left: 33%;
          top: 58%; } }
      @media (max-width: 420px) {
        .header-tools .tools-header-text__tournam span:nth-child(1) {
          left: 31%; } }
      @media (max-width: 900px) and (orientation: landscape) {
        .header-tools .tools-header-text__tournam span:nth-child(1) {
          display: none; } }
    .header-tools .tools-header-text__tournam span:nth-child(2) {
      text-align: left;
      width: 190px;
      left: 67%;
      top: 20%; }
      @media (min-width: 1921px), (min-height: 1081px) {
        .header-tools .tools-header-text__tournam span:nth-child(2) {
          left: 69%; } }
      @media (max-width: 1600px) {
        .header-tools .tools-header-text__tournam span:nth-child(2) {
          top: 16%; } }
      @media (max-width: 1024px) {
        .header-tools .tools-header-text__tournam span:nth-child(2) {
          left: 65%;
          top: 45%; } }
      @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
        .header-tools .tools-header-text__tournam span:nth-child(2) {
          left: 58%;
          top: 46%; } }
      @media (max-width: 1023px) {
        .header-tools .tools-header-text__tournam span:nth-child(2) {
          display: none; } }
      @media (max-width: 900px) and (orientation: landscape) {
        .header-tools .tools-header-text__tournam span:nth-child(2) {
          display: none; } }
    .header-tools .tools-header-text__tournam span:nth-child(3) {
      width: 210px;
      left: 70.5%;
      top: 34%; }
      @media (min-width: 1921px), (min-height: 1081px) {
        .header-tools .tools-header-text__tournam span:nth-child(3) {
          left: 72%; } }
      @media (max-width: 1600px) {
        .header-tools .tools-header-text__tournam span:nth-child(3) {
          top: 28%; } }
      @media (max-width: 1024px) {
        .header-tools .tools-header-text__tournam span:nth-child(3) {
          left: 70%;
          top: 55%; } }
      @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
        .header-tools .tools-header-text__tournam span:nth-child(3) {
          left: 70%;
          top: 63%; } }
      @media (max-width: 1023px) {
        .header-tools .tools-header-text__tournam span:nth-child(3) {
          display: none; } }
      @media (max-width: 900px) and (orientation: landscape) {
        .header-tools .tools-header-text__tournam span:nth-child(3) {
          display: none; } }
    .header-tools .tools-header-text__tournam span:nth-child(4) {
      left: 83%;
      top: 17%; }
      @media (min-width: 1921px), (min-height: 1081px) {
        .header-tools .tools-header-text__tournam span:nth-child(4) {
          left: 85%; } }
      @media (max-width: 1600px) {
        .header-tools .tools-header-text__tournam span:nth-child(4) {
          top: 14%; } }
      @media (max-width: 1024px) {
        .header-tools .tools-header-text__tournam span:nth-child(4) {
          left: 82%;
          top: 35%; } }
      @media (min-width: 1024px) and (max-height: 1366px) and (orientation: portrait) {
        .header-tools .tools-header-text__tournam span:nth-child(4) {
          left: 82%;
          top: 42%; } }
      @media (max-width: 768px) {
        .header-tools .tools-header-text__tournam span:nth-child(4) {
          visibility: visible;
          left: 70%;
          top: 70%; } }
      @media (max-width: 480px) {
        .header-tools .tools-header-text__tournam span:nth-child(4) {
          left: 71%;
          top: 71%; } }
      @media (max-width: 900px) and (orientation: landscape) {
        .header-tools .tools-header-text__tournam span:nth-child(4) {
          visibility: hidden; } }
    @media (min-width: 1024px) and (max-width: 1600px) {
      .header-tools .tools-header-text__ms {
        left: 46%; } }
    @media (min-width: 769px) and (max-width: 1200px) {
      .header-tools .tools-header-text__ms {
        visibility: hidden; } }
    @media (max-width: 420px) {
      .header-tools .tools-header-text__ms {
        left: -13%; } }
    .header-tools .tools-header-text__ms span:nth-child(1) {
      top: 20%;
      left: 44%;
      width: 100px; }
      @media (max-width: 768px) {
        .header-tools .tools-header-text__ms span:nth-child(1) {
          top: 40%;
          left: 35%; } }
      @media (max-width: 480px) {
        .header-tools .tools-header-text__ms span:nth-child(1) {
          top: 48%;
          left: 28%;
          text-align: center; } }
    .header-tools .tools-header-text__ms span:nth-child(2) {
      top: 43%;
      left: 57%;
      width: 80px; }
      @media (max-width: 768px) {
        .header-tools .tools-header-text__ms span:nth-child(2) {
          top: 70%;
          left: 75%; } }
      @media (max-width: 480px) {
        .header-tools .tools-header-text__ms span:nth-child(2) {
          top: 72%;
          left: 70%;
          text-align: center; } }
    .header-tools .tools-header-text__ms span:nth-child(3) {
      white-space: nowrap;
      top: 58%;
      left: 43%;
      width: 140px; }
      @media (max-width: 768px) {
        .header-tools .tools-header-text__ms span:nth-child(3) {
          top: 88%;
          left: 33%; } }
      @media (max-width: 480px) {
        .header-tools .tools-header-text__ms span:nth-child(3) {
          top: 85%;
          left: 25%;
          text-align: center; } }
    @media (max-width: 1024px) {
      .header-tools .tools-header-text__grc {
        margin-left: -20px; } }
    @media (max-width: 480px) {
      .header-tools .tools-header-text__grc {
        top: 0; } }
    @media (max-width: 400px) {
      .header-tools .tools-header-text__grc {
        top: 4%; } }
    .header-tools .tools-header-text__grc span:nth-child(1) {
      width: 240px;
      left: 53%;
      top: 24%; }
      @media (max-width: 1023px) {
        .header-tools .tools-header-text__grc span:nth-child(1) {
          visibility: hidden; } }
      @media (max-width: 768px) {
        .header-tools .tools-header-text__grc span:nth-child(1) {
          visibility: visible; } }
      @media (max-width: 480px) {
        .header-tools .tools-header-text__grc span:nth-child(1) {
          width: 190px;
          left: 45%; } }
      @media (max-width: 420px) {
        .header-tools .tools-header-text__grc span:nth-child(1) {
          left: 42%; } }
      @media (max-width: 900px) and (orientation: landscape) {
        .header-tools .tools-header-text__grc span:nth-child(1) {
          display: none; } }
    .header-tools .tools-header-text__grc span:nth-child(2) {
      text-align: left;
      width: 180px;
      left: 67%;
      top: 36%; }
      @media (max-width: 1023px) {
        .header-tools .tools-header-text__grc span:nth-child(2) {
          visibility: hidden; } }
      @media (max-width: 768px) {
        .header-tools .tools-header-text__grc span:nth-child(2) {
          visibility: visible;
          top: 45%;
          left: 40%; } }
      @media (max-width: 480px) {
        .header-tools .tools-header-text__grc span:nth-child(2) {
          left: 35%;
          top: 42%;
          width: 160px; } }
      @media (max-width: 420px) {
        .header-tools .tools-header-text__grc span:nth-child(2) {
          left: 32%;
          width: 150px; } }
      @media (max-width: 900px) and (orientation: landscape) {
        .header-tools .tools-header-text__grc span:nth-child(2) {
          display: none; } }
    .header-tools .tools-header-text__grc span:nth-child(3) {
      width: 220px;
      left: 80%;
      top: 24%; }
      @media (max-width: 1023px) {
        .header-tools .tools-header-text__grc span:nth-child(3) {
          visibility: hidden; } }
      @media (max-width: 768px) {
        .header-tools .tools-header-text__grc span:nth-child(3) {
          visibility: visible;
          top: 65%;
          left: 25%;
          width: 180px; } }
      @media (max-width: 480px) {
        .header-tools .tools-header-text__grc span:nth-child(3) {
          top: 57%;
          left: 22%; } }
      @media (max-width: 420px) {
        .header-tools .tools-header-text__grc span:nth-child(3) {
          left: 18%; } }
      @media (max-width: 900px) and (orientation: landscape) {
        .header-tools .tools-header-text__grc span:nth-child(3) {
          display: none; } }

.tools-bg {
  position: absolute;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
  width: 100%;
  height: 100%;
  max-width: 1920px;
  max-height: 1080px; }
  .tools-bg__bns, .tools-bg__grc {
    position: absolute;
    z-index: 2;
    left: 0;
    top: 0; }
  @media (max-width: 1024px) and (orientation: landscape) {
    .tools-bg__tournam {
      margin-top: 100px; } }
  @media (max-width: 1023px) {
    .tools-bg--desktop {
      display: none; } }
  .tools-bg--mobile {
    background-repeat: no-repeat;
    background-position-x: center;
    background-position-y: center;
    background-size: cover;
    width: 100%;
    height: 100%; }
    @media (min-width: 1024px) {
      .tools-bg--mobile {
        display: none; } }
    @media (max-width: 992px) {
      .tools-bg--mobile__bns {
        background-position-y: 10%; } }
    @media (max-width: 480px) {
      .tools-bg--mobile__bns {
        background-position-x: left; } }
    @media (max-width: 900px) and (orientation: landscape) {
      .tools-bg--mobile__bns {
        background-position-y: center; } }
    @media (max-width: 768px) {
      .tools-bg--mobile__jp {
        background-position-y: -20%; } }
    @media (max-width: 480px) {
      .tools-bg--mobile__jp {
        background-position-y: 150px; } }
    @media (max-width: 900px) and (orientation: landscape) {
      .tools-bg--mobile__jp {
        background-position-y: center; } }
    @media (max-width: 992px) {
      .tools-bg--mobile__tournam {
        background-position-y: 0; } }
    @media (max-width: 480px) {
      .tools-bg--mobile__tournam {
        background-position-y: 100px; } }
    @media (max-width: 900px) and (orientation: landscape) {
      .tools-bg--mobile__tournam {
        background-position-y: center; } }
    @media (max-width: 768px) {
      .tools-bg--mobile__ms {
        background-position-y: -30px; } }
    @media (max-width: 480px) {
      .tools-bg--mobile__ms {
        background-position-y: inherit; } }
    @media (max-width: 768px) {
      .tools-bg--mobile__grc {
        background-position-y: -30px; } }
    @media (max-width: 480px) {
      .tools-bg--mobile__grc {
        background-position-y: 50px; } }
    @media (max-width: 900px) and (orientation: landscape) {
      .tools-bg--mobile__grc {
        background-position-y: center; } }

.header-desc {
  position: relative;
  margin-top: 12%;
  z-index: 2;
  height: 50vh; }
  @media (min-width: 1921px), (min-height: 1081px) {
    .header-desc {
      max-height: 600px; } }
  @media (max-width: 768px) {
    .header-desc {
      margin-top: 25%; } }
  @media (max-width: 400px) {
    .header-desc {
      margin-top: 20%; } }
  @media (max-width: 1024px) and (orientation: landscape) {
    .header-desc {
      height: 40vh; } }
  @media (max-width: 1023px) and (orientation: landscape) {
    .header-desc {
      margin-top: 5%;
      height: 20vh; } }
  @media (max-width: 767px), (max-width: 900px) and (orientation: landscape) {
    .header-desc {
      display: none; } }
  .header-desc__title {
    letter-spacing: 0.32em; }
  .header-desc__logo {
    max-width: 400px;
    pointer-events: none; }
  .header-desc__subtitle {
    font-size: 2.68vh;
    line-height: 1.8;
    margin-top: 4.44vh;
    padding-right: 4.688vw;
    font-weight: 700; }
    @media (min-width: 1921px), (min-height: 1081px) {
      .header-desc__subtitle {
        font-size: 29px;
        line-height: 39px;
        margin-top: 48px;
        padding-right: 90px; } }
    @media (max-width: 1024px) {
      .header-desc__subtitle {
        font-size: 20px;
        padding-right: 0; } }
    @media (max-width: 767px), (max-width: 900px) and (orientation: landscape) {
      .header-desc__subtitle {
        font-size: 15px;
        line-height: 36px;
        text-align: center; } }
    .header-desc__subtitle.grc-header-subtitle {
      font-size: 20px;
      line-height: 36px;
      letter-spacing: 0.075em;
      font-weight: 400; }
  .header-desc__gs {
    display: flex;
    flex-direction: column;
    margin: auto 0 auto auto;
    max-width: 90%; }
    @media (max-width: 1440px) {
      .header-desc__gs {
        max-width: 100%; } }
    @media (max-width: 991px) {
      .header-desc__gs {
        max-width: 80%;
        margin: auto; } }
    @media (max-width: 767px) {
      .header-desc__gs {
        max-width: 65%; } }
    @media (max-width: 480px) {
      .header-desc__gs {
        max-width: 60%;
        padding-left: 20px; } }
    @media (max-width: 420px) {
      .header-desc__gs {
        max-width: 75%; } }
  .header-desc__gsPoints {
    margin-top: 30px;
    font-size: 30px;
    line-height: 36px;
    color: #8dd363;
    text-transform: uppercase; }
    @media (max-width: 991px) {
      .header-desc__gsPoints {
        display: none; } }
    .header-desc__gsPoints span {
      display: block;
      margin-bottom: 45px;
      letter-spacing: 0.075em; }
      @media (max-width: 1024px) {
        .header-desc__gsPoints span {
          margin-bottom: 30px;
          letter-spacing: 0.05em; } }
      .header-desc__gsPoints span::after {
        margin-bottom: 0; }
  .header-desc__gsCross {
    position: absolute;
    top: -80px;
    left: 0;
    width: 12px;
    height: 118px;
    background: #8dd363; }
    @media (max-width: 1440px) {
      .header-desc__gsCross {
        left: -60px; } }
    @media (max-width: 991px) {
      .header-desc__gsCross {
        left: 60px; } }
    @media (max-width: 1024px) {
      .header-desc__gsCross {
        transform: scale(0.75); } }
    @media (max-width: 991px) {
      .header-desc__gsCross {
        transform: scale(0.61); } }
    @media (max-width: 420px) {
      .header-desc__gsCross {
        transform: scale(0.35) translate(-100px, 50px); } }
    .header-desc__gsCross::after {
      position: absolute;
      content: "";
      height: 12px;
      width: 118px;
      left: -53px;
      top: 53px;
      background: #8dd363; }
  .header-desc__gsTitle {
    font-family: "Oswald", sans-serif;
    font-size: 122px;
    line-height: 137px;
    font-weight: 400; }
    @media (max-width: 1366px) {
      .header-desc__gsTitle {
        font-size: 110px;
        line-height: 120px; } }
    @media (max-width: 1200px) {
      .header-desc__gsTitle {
        font-size: 90px;
        line-height: 100px; } }
    @media (max-width: 767px), (max-width: 854px) and (orientation: landscape) {
      .header-desc__gsTitle {
        font-size: 60px;
        line-height: 1.2em; } }
    .header-desc__gsTitle strong {
      display: inline-block;
      letter-spacing: 0.23em; }
  .header-desc__gsSubtitle {
    margin-top: 60px;
    font-size: 20px;
    line-height: 36px;
    letter-spacing: 0.075em; }
    @media (max-width: 1024px) {
      .header-desc__gsSubtitle {
        letter-spacing: 0.05em; } }
    @media (max-width: 768px) {
      .header-desc__gsSubtitle {
        margin-top: 40px;
        font-size: 16px;
        line-height: 1.4em;
        max-width: 70%; } }
    @media (max-width: 767px) {
      .header-desc__gsSubtitle {
        margin-top: 30px; } }
    @media (max-width: 480px) {
      .header-desc__gsSubtitle {
        max-width: 100%;
        padding-right: 10px; } }

@media (max-width: 767px) {
  .header-bottom .button {
    display: none; } }
@media (max-width: 812px) and (orientation: landscape) {
  .header-bottom {
    margin-top: 23vh; } }

.toolpage-offer {
  position: relative;
  margin-top: -5%;
  z-index: 2; }
  @media (max-width: 1024px) {
    .toolpage-offer {
      margin-top: 10%; } }
  @media (min-width: 1921px), (min-height: 1081px) {
    .toolpage-offer {
      margin-top: 0; } }
  @media (max-width: 767px) {
    .toolpage-offer--ms {
      margin-top: 0; } }
  .toolpage-offer__big {
    color: rgba(141, 211, 99, 0.04);
    font-weight: 400;
    font-size: 37.396vw;
    line-height: 1;
    text-align: center;
    text-transform: uppercase;
    position: relative;
    pointer-events: none; }
    @media (min-width: 1921px) {
      .toolpage-offer__big {
        font-size: 718px; } }
    .toolpage-offer__big--grc {
      white-space: pre; }
    .toolpage-offer__big--jp {
      font-size: 18.438vw; }
      @media (min-width: 1921px) {
        .toolpage-offer__big--jp {
          font-size: 354px; } }
    .toolpage-offer__big--tournam {
      font-size: 11.771vw;
      margin-left: -7vw; }
      @media (min-width: 1921px) {
        .toolpage-offer__big--tournam {
          font-size: 226px; } }
    .toolpage-offer__big--ms {
      text-align: left;
      font-size: 16.146vw;
      padding-bottom: 9.25vh; }
      @media (min-width: 1921px) {
        .toolpage-offer__big--ms {
          font-size: 310px; } }
      @media (min-height: 1081px) {
        .toolpage-offer__big--ms {
          padding-bottom: 100px; } }
      @media (max-width: 1024px) {
        .toolpage-offer__big--ms {
          padding-bottom: 0; } }
  .toolpage-offer__same {
    width: 100%;
    position: absolute;
    top: 50%;
    left: 15px;
    transform: translateY(-50%);
    font-size: 2.59vh;
    line-height: 4.26vh;
    z-index: 2; }
    @media (min-width: 1921px), (min-height: 1081px) {
      .toolpage-offer__same {
        font-size: 28px;
        line-height: 46px; } }
    @media (max-width: 992px), (max-width: 1024px) and (orientation: portrait) {
      .toolpage-offer__same {
        position: relative;
        left: 0;
        top: 0;
        font-size: 16px;
        line-height: 36px; } }

:root {
  --font-size-index: calc(1vw + 1vh); }

@keyframes fadeIn {
  0% {
    opacity: 0; }
  100% {
    opacity: 1; } }
.navbar {
  --bs-navbar-padding-x: 0.5rem; }

#app-main-header {
  background-color: #181717;
  min-height: 60px; }

.header-main-toolbar a {
  color: #8dd363 !important;
  border: 1px solid rgba(141, 211, 99, 0.5);
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.75;
  padding-right: 0.5rem !important;
  padding-left: 0.5rem !important;
  white-space: nowrap; }
.header-main-toolbar .nav-link {
  margin: 0 4px; }
  @media (max-width: 767px) {
    .header-main-toolbar .nav-link {
      margin: 2px 0; } }

@media (max-width: 767px) {
  .navbar-nav {
    margin-top: 4px; } }

:root {
  --container-max-width: 1920;
  --bg-width: var(--container-max-width);
  --bg-height: 3240;
  --bg-aspect-ratio: calc(var(--bg-width) * var(--bg-height));
  --header-max-height: 590;
  --height-aspect-ratio: calc(var(--container-max-width) / var(--header-max-height));
  --width-aspect-ratio: calc(100vw / var(--bg-width));
  --main-section-max-height: calc( var(--bg-height) - var(--header-max-height) );
  --main-section-aspect-ration: calc( var(--container-max-width) / var(--main-section-max-height) );
  --painting-max-bootom: 656; }

/* ----------------------------------------------
 * Generated by Animista on 2022-3-18 1:15:40
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info.
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */
/**
 * ----------------------------------------
 * animation vibrate-1
 * ----------------------------------------
 */
@-webkit-keyframes vibrate-1 {
  0% {
    -webkit-transform: translate(0);
    transform: translate(0); }
  20% {
    -webkit-transform: translate(-2px, 2px);
    transform: translate(-2px, 2px); }
  40% {
    -webkit-transform: translate(-2px, -2px);
    transform: translate(-2px, -2px); }
  60% {
    -webkit-transform: translate(2px, 2px);
    transform: translate(2px, 2px); }
  80% {
    -webkit-transform: translate(2px, -2px);
    transform: translate(2px, -2px); }
  100% {
    -webkit-transform: translate(0);
    transform: translate(0); } }
@keyframes vibrate-1 {
  0% {
    -webkit-transform: translate(0);
    transform: translate(0); }
  20% {
    -webkit-transform: translate(-2px, 2px);
    transform: translate(-2px, 2px); }
  40% {
    -webkit-transform: translate(-2px, -2px);
    transform: translate(-2px, -2px); }
  60% {
    -webkit-transform: translate(2px, 2px);
    transform: translate(2px, 2px); }
  80% {
    -webkit-transform: translate(2px, -2px);
    transform: translate(2px, -2px); }
  100% {
    -webkit-transform: translate(0);
    transform: translate(0); } }
@-webkit-keyframes heartbeat {
  from {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out; }
  10% {
    -webkit-transform: scale(0.91);
    transform: scale(0.91);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in; }
  17% {
    -webkit-transform: scale(0.98);
    transform: scale(0.98);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out; }
  33% {
    -webkit-transform: scale(0.87);
    transform: scale(0.87);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in; }
  45% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out; } }
@keyframes heartbeat {
  from {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transform-origin: center center;
    transform-origin: center center;
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out; }
  10% {
    -webkit-transform: scale(0.91);
    transform: scale(0.91);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in; }
  17% {
    -webkit-transform: scale(0.98);
    transform: scale(0.98);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out; }
  33% {
    -webkit-transform: scale(0.87);
    transform: scale(0.87);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in; }
  45% {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out; } }
@-webkit-keyframes pulsate-bck {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1); }
  50% {
    -webkit-transform: scale(0.95);
    transform: scale(0.95); }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1); } }
@keyframes pulsate-bck {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1); }
  50% {
    -webkit-transform: scale(0.95);
    transform: scale(0.95); }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1); } }
:root {
  --container-max-width: 1920;
  --bg-width: var(--container-max-width);
  --bg-height: 3240;
  --bg-aspect-ratio: calc(var(--bg-width) * var(--bg-height));
  --header-max-height: 590;
  --height-aspect-ratio: calc(var(--container-max-width) / var(--header-max-height));
  --width-aspect-ratio: calc(100vw / var(--bg-width));
  --main-section-max-height: calc( var(--bg-height) - var(--header-max-height) );
  --main-section-aspect-ration: calc( var(--container-max-width) / var(--main-section-max-height) );
  --painting-max-bootom: 656; }

.tournament-landing .banner-figure-container {
  display: flex;
  width: 100%;
  margin-bottom: -17vw; }
.tournament-landing .banner-figure {
  display: block;
  object-fit: cover;
  object-position: top center;
  width: 100%; }
.tournament-landing .header {
  position: relative;
  height: 41.667vw;
  width: 100%;
  display: flex;
  margin-bottom: 0;
  z-index: 2; }
  .tournament-landing .header:before {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    top: -4vw;
    height: 30vw; }
  .tournament-landing .header-img {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    height: 100%;
    display: block; }
  .tournament-landing .header .sw-logo, .tournament-landing .header .sw-logo-effect, .tournament-landing .header .sw-symbol {
    position: absolute; }
  .tournament-landing .header .sw-logo {
    top: 43.9%;
    left: 46.9%;
    max-width: 940px;
    width: 49vw;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center; }
  .tournament-landing .header .sw-logo-effect {
    top: 26%;
    will-change: transform;
    -webkit-animation: heartbeat 12s ease-in-out infinite both;
    animation: heartbeat 12s ease-in-out infinite both; }
    .tournament-landing .header .sw-logo-effect img {
      mask-image: linear-gradient(to right, transparent 0%, black 10%, black 90%, transparent 100%); }
  .tournament-landing .header .sw-symbol {
    opacity: 0.9;
    will-change: transform, opacity; }
    .tournament-landing .header .sw-symbol.s1 {
      left: -5%;
      max-width: 496px;
      top: 62%;
      width: 25.833vw;
      animation: vibrate-1 10s ease-in-out infinite both; }
    .tournament-landing .header .sw-symbol.s2 {
      max-width: 460px;
      right: -5.5%;
      top: 28%;
      width: 23.958vw;
      animation: vibrate-1 12s ease-in-out infinite both; }

.tournament-landing .section {
  padding-top: 7.708vw;
  font-family: 'Noto Sans Display', sans-serif;
  background: #250804 url("/landing/teller-of-tales/assets/images/bg-2.jpg") no-repeat 0 0;
  background-size: 100% auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin-bottom: 0;
  z-index: 1; }
  .tournament-landing .section:after {
    content: "";
    display: table;
    clear: both; }
  .tournament-landing .section .sw-ticker {
    min-width: 30vw;
    position: relative;
    border-radius: 1.563vw;
    border: #de7118 6px solid;
    background-color: #000;
    margin-bottom: 1.563vw;
    padding: 20px 20px 14px; }
    .device-mobile .tournament-landing .section .sw-ticker {
      border-width: 2px;
      padding: 10px 20px 7px; }
    .device-tablet .tournament-landing .section .sw-ticker {
      border-width: 6px;
      padding: 20px 20px 14px; }
    .tournament-landing .section .sw-ticker-title {
      font-size: 2.813vw;
      font-weight: 900;
      color: white;
      position: absolute;
      bottom: 100%;
      left: 50%;
      transform: translateX(-50%);
      border-radius: 1.042vw 1.042vw 0 0;
      background-color: #de7118;
      padding: 2px 16px 8px; }
      .device-mobile .tournament-landing .section .sw-ticker-title {
        border-radius: 1.563vw 1.563vw 0 0;
        font-size: 14px;
        padding: 2px 16px 4px; }
      .device-tablet .tournament-landing .section .sw-ticker-title {
        border-radius: 1.042vw 1.042vw 0 0;
        font-size: 2.813vw;
        padding: 2px 16px 8px; }
    .tournament-landing .section .sw-ticker-dots {
      left: 83%;
      max-width: 577px;
      position: absolute;
      top: 100%;
      width: 30.052vw;
      margin-top: 0.313vw; }
    .device-mobile .tournament-landing .section .sw-ticker {
      width: auto; }
    .tournament-landing .section .sw-ticker .s3 {
      position: absolute;
      z-index: 1;
      opacity: 0.9;
      will-change: transform, opacity;
      bottom: 78%;
      left: -3%;
      max-width: 157px;
      width: 10vw;
      animation: vibrate-1 10s ease-in-out infinite both; }
  .tournament-landing .section .sw-pa-with-le {
    font-size: 5.469vw;
    line-height: 1.2;
    text-align: center;
    font-weight: 900;
    margin-bottom: 0; }
    .device-mobile .tournament-landing .section .sw-pa-with-le {
      font-size: 28px; }
    .device-tablet .tournament-landing .section .sw-pa-with-le {
      font-size: 5.469vw; }
  .tournament-landing .section .sw-date {
    max-width: 757px;
    width: 39.427vw;
    margin-bottom: 7.604vw; }
    .device-mobile .tournament-landing .section .sw-date {
      width: 50vw;
      max-width: 200px; }
    .device-tablet .tournament-landing .section .sw-date {
      width: 39.427vw;
      max-width: 757px; }
.tournament-landing .section-yellow {
  position: relative;
  text-align: center;
  margin-bottom: 3.333vw; }
  .tournament-landing .section-yellow strong {
    font-weight: 900; }
  .tournament-landing .section-yellow-body {
    margin: -1px 0;
    padding: 3.646vw 0 5.208vw;
    background: #ffa524;
    font-size: 1.979vw;
    font-family: "Noto Sans Display", sans-serif;
    color: #1d1c1c;
    line-height: 1.5;
    text-align: center; }
    .device-mobile .tournament-landing .section-yellow-body {
      padding: 20px 0 35px; }
    .device-tablet .tournament-landing .section-yellow-body {
      padding: 3.646vw 0 5.208vw; }
    .tournament-landing .section-yellow-body p {
      margin: 0 auto;
      max-width: 80vw; }
      .device-mobile .tournament-landing .section-yellow-body p {
        font-size: 16px; }
      .device-tablet .tournament-landing .section-yellow-body p {
        font-size: 1.979vw;
        max-width: 70vw; }
      .tournament-landing .section-yellow-body p.text-lg {
        font-size: 3.021vw;
        line-height: 1.2;
        margin: 2.917vw auto; }
        .device-mobile .tournament-landing .section-yellow-body p.text-lg {
          font-size: 20px; }
        .device-tablet .tournament-landing .section-yellow-body p.text-lg {
          font-size: 3.021vw; }
      .tournament-landing .section-yellow-body p.text-sm {
        font-weight: 400;
        line-height: 1.4;
        font-size: 1.927vw; }
        .device-mobile .tournament-landing .section-yellow-body p.text-sm {
          font-size: 15px; }
        .device-tablet .tournament-landing .section-yellow-body p.text-sm {
          font-size: 1.927vw; }
      .tournament-landing .section-yellow-body p br {
        display: none; }
        .device-desktop .tournament-landing .section-yellow-body p br {
          display: block; }
  .tournament-landing .section-yellow-divider {
    border-radius: 3px;
    background-color: #fcb045;
    box-shadow: 0 4px 10px 1px rgba(2, 3, 2, 0.32);
    height: 1.667vw;
    max-height: 32px;
    margin: 6.146vw 0 7.031vw; }
    .device-mobile .tournament-landing .section-yellow-divider {
      height: 10px;
      margin: 3vw 0 4vw;
      box-shadow: 0 2px 5px 1px rgba(2, 3, 2, 0.15); }
    .tournament-landing .section-yellow-divider .device-tablet {
      height: 1.667vw;
      margin: 6.146vw 0 7.031vw;
      box-shadow: 0 4px 10px 1px rgba(2, 3, 2, 0.32); }
  .tournament-landing .section-yellow .sw-symbol {
    position: absolute;
    opacity: 0.9;
    will-change: transform, opacity; }
  .tournament-landing .section-yellow .s4 {
    max-width: 433px;
    right: -7.9%;
    top: 29.3%;
    width: 22.552vw;
    animation: vibrate-1 12s ease-in-out infinite both; }
  .tournament-landing .section-yellow .s5 {
    bottom: 14%;
    left: 0;
    width: 16.302vw;
    max-width: 313px;
    animation: vibrate-1 10s ease-in-out infinite both; }
    .device-mobile .tournament-landing .section-yellow .s5 {
      left: -2%;
      bottom: 16%;
      opacity: .7; }
    .device-mobile.in-landscape .tournament-landing .section-yellow .s5 {
      opacity: 0; }
    .device-tablet .tournament-landing .section-yellow .s5, .device-tablet.in-landscape .tournament-landing .section-yellow .s5 {
      bottom: 14%;
      left: 0;
      opacity: 1; }
  .tournament-landing .section-yellow .s6, .tournament-landing .section-yellow .s7 {
    bottom: .5%;
    max-width: 339px;
    width: 17.656vw; }
  .tournament-landing .section-yellow .s7 {
    right: 14%;
    animation: vibrate-1 11s ease-in-out infinite both; }
  .tournament-landing .section-yellow .s6 {
    left: 21%;
    animation: vibrate-1 13s ease-in-out infinite both; }
  .tournament-landing .section-yellow .s8 {
    max-width: 420px;
    right: -7.4%;
    top: 7%;
    width: 21.875vw;
    animation: vibrate-1 12s ease-in-out infinite both; }
  .tournament-landing .section-yellow .s9 {
    bottom: -17%;
    max-width: 465px;
    right: -7.5%;
    width: 24.219vw;
    animation: vibrate-1 9s ease-in-out infinite both; }
  .tournament-landing .section-yellow .s10, .tournament-landing .section-yellow .s11 {
    bottom: .5%;
    max-width: 207px;
    width: 10.781vw; }
  .tournament-landing .section-yellow .s10 {
    right: 22%;
    animation: vibrate-1 13s ease-in-out infinite both; }
  .tournament-landing .section-yellow .s11 {
    left: 21%;
    animation: vibrate-1 11s ease-in-out infinite both; }
  .tournament-landing .section-yellow .s12 {
    bottom: 17%;
    max-width: 262px;
    width: 13.646vw; }
    .device-mobile .tournament-landing .section-yellow .s12 {
      bottom: 8%; }
    .device-mobile.in-landscape .tournament-landing .section-yellow .s12 {
      opacity: 0; }
    .device-tablet .tournament-landing .section-yellow .s12, .device-tablet.in-landscape .tournament-landing .section-yellow .s12 {
      bottom: 17%;
      opacity: 1; }
.tournament-landing .sw-green-btn, .tournament-landing .sw-black-btn, .tournament-landing .sw-purple-btn, .tournament-landing .sw-green-line {
  display: inline-block;
  text-transform: uppercase;
  text-align: center;
  color: #fff;
  font-size: 2.813vw;
  line-height: 1.6;
  font-weight: 900;
  margin-bottom: 1.354vw;
  border-radius: 1.563vw;
  padding: 1vw 2vw; }
  .device-mobile .tournament-landing .sw-green-btn,
  .device-mobile .tournament-landing .sw-black-btn,
  .device-mobile .tournament-landing .sw-purple-btn,
  .device-mobile .tournament-landing .sw-green-line {
    font-size: 16px;
    padding: 10px 20px;
    margin-bottom: 1.563vw; }
  .device-tablet .tournament-landing .sw-green-btn, .device-tablet .tournament-landing .sw-black-btn, .device-tablet .tournament-landing .sw-purple-btn, .device-tablet .tournament-landing .sw-green-line {
    font-size: 2.813vw;
    padding: 1vw 2vw;
    margin-bottom: 1.354vw; }
.tournament-landing .sw-green-btn {
  width: 39.583vw;
  max-width: 760px;
  background-color: #255a15;
  margin-bottom: 2.813vw; }
  .device-mobile .tournament-landing .sw-green-btn {
    width: auto;
    margin-bottom: 20px; }
  .device-tablet .tournament-landing .sw-green-btn {
    width: 39.583vw;
    margin-bottom: 2.813vw; }
  .tournament-landing .sw-green-btn.lg {
    font-size: 4.792vw;
    width: 75vw;
    max-width: 1440px;
    border-radius: 3.438vw;
    box-shadow: 0 20px 11px 2px rgba(2, 3, 2, 0.32);
    padding: 2vw;
    margin-bottom: 6.510vw;
    animation: pulsate-bck 9s ease-in-out infinite both; }
    .device-mobile .tournament-landing .sw-green-btn.lg {
      width: auto;
      margin-bottom: 3vw;
      padding: 2vw 3vw;
      box-shadow: 0 5px 2px 1px rgba(2, 3, 2, 0.15); }
    .device-tablet .tournament-landing .sw-green-btn.lg {
      width: 75vw;
      margin-bottom: 6.510vw;
      padding: 2vw;
      box-shadow: 0 20px 11px 2px rgba(2, 3, 2, 0.32); }
.tournament-landing .sw-black-btn {
  font-size: 2.813vw;
  border-radius: 100px;
  min-width: 45.417vw;
  max-width: 60vw;
  border: 5px solid #de7118;
  background-color: #8ed06a;
  box-shadow: inset 1px 1px 3px rgba(255, 255, 255, 0.5), 0 20px 11.18px 1.82px rgba(2, 3, 2, 0.32); }
  .device-mobile .tournament-landing .sw-black-btn {
    max-width: 90vw; }
  .device-tablet .tournament-landing .sw-black-btn {
    max-width: 60vw; }
.tournament-landing .sw-purple-btn {
  font-size: 3.906vw;
  border-radius: 2.083vw;
  min-width: 50vw;
  max-width: 60vw;
  background-color: #8c3891; }
  .device-mobile .tournament-landing .sw-purple-btn {
    max-width: 90vw;
    min-width: 250px;
    margin-bottom: 15px; }
  .device-tablet .tournament-landing .sw-purple-btn {
    max-width: 60vw;
    min-width: 50vw;
    margin-bottom: 2.083vw; }
  .tournament-landing .sw-purple-btn.sm {
    font-size: 2.604vw;
    border-radius: 100px;
    box-shadow: 0 20px 11.18px 1.82px rgba(2, 3, 2, 0.32);
    padding: 1.4vw 2vw;
    min-width: 56vw; }
    .device-mobile .tournament-landing .sw-purple-btn.sm {
      font-size: 14px;
      margin: 20px auto 0;
      padding: 1.4vw 3vw;
      min-width: 250px; }
    .device-tablet .tournament-landing .sw-purple-btn.sm {
      font-size: 2.604vw;
      margin: 20px auto 2.083vw;
      padding: 1.4vw 2vw;
      min-width: 56vw; }
.tournament-landing .sw-green-line {
  background-color: rgba(37, 90, 21, 0.88);
  display: block;
  border-radius: 0;
  margin: 2.708vw 0;
  line-height: 1.5;
  padding: 10px; }
  .device-mobile .tournament-landing .sw-green-line {
    margin: 10px 0; }
  .device-tablet .tournament-landing .sw-green-line {
    margin: 2.708vw 0; }
.tournament-landing .sw-good-luck {
  color: #fff;
  font-weight: 900;
  font-size: 3.906vw; }
  .device-mobile .tournament-landing .sw-good-luck {
    font-size: 18px; }
  .device-tablet .tournament-landing .sw-good-luck {
    font-size: 3.906vw; }
.tournament-landing .sw-list {
  display: flex;
  flex-direction: column;
  margin: 4.167vw auto; }
  .device-mobile .tournament-landing .sw-list {
    margin: 30px auto; }
  .device-tablet .tournament-landing .sw-list {
    margin: 4.167vw auto; }
  .tournament-landing .sw-list-item {
    position: relative;
    border-bottom: 4px solid #de7118;
    color: #fff;
    font-size: 1.953vw;
    padding: 1.250vw;
    text-align: center; }
    .device-mobile .tournament-landing .sw-list-item {
      font-size: 16px;
      padding: 10px;
      border-bottom: 2px solid #de7118; }
    .device-tablet .tournament-landing .sw-list-item {
      font-size: 1.953vw;
      padding: 1.250vw;
      border-bottom-width: 4px; }
    .tournament-landing .sw-list-item:after {
      content: '';
      position: absolute;
      bottom: calc((-1.250vw/2) - 2px);
      left: -1.250vw;
      width: 1.250vw;
      height: 1.250vw;
      border-radius: 50%;
      background: #de7118; }
      .device-mobile .tournament-landing .sw-list-item:after {
        bottom: calc((-1.250vw/2) - 1px); }
      .device-tablet .tournament-landing .sw-list-item:after {
        bottom: calc((-1.250vw/2) - 2px); }
  .tournament-landing .sw-list.simple .sw-list-item {
    color: #cacaca;
    border: none;
    padding: .250vw; }
    .tournament-landing .sw-list.simple .sw-list-item:after {
      display: none; }
.tournament-landing .sw-prizes {
  width: 59.219vw;
  max-width: 1137px;
  margin: 2.083vw 0; }
  .device-mobile .tournament-landing .sw-prizes {
    width: 100%;
    max-width: 80%; }
  .device-tablet .tournament-landing .sw-prizes {
    width: 59.219vw;
    max-width: 1137px; }
  .tournament-landing .sw-prizes.lg {
    width: 61.458vw;
    max-width: 1180px;
    margin: 0 0 3.802vw; }
    .device-tablet .tournament-landing .sw-prizes.lg {
      width: 61.458vw;
      max-width: 1180px; }
.tournament-landing .flip-countdown .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec .card__top, .tournament-landing .flip-countdown .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec .card__bottom {
  min-width: 3.125vw; }
  .device-desktop .tournament-landing .flip-countdown .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec .card__top,
  .device-desktop .tournament-landing .flip-countdown .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec .card__bottom {
    min-width: 60px; }

.device-desktop .tournament-landing .flip-countdown.size-medium .flip-countdown-piece .flip-countdown-title {
  font-size: 2vw; }

.sw-fire .particle {
  animation: rise 1s ease-in infinite;
  background-image: radial-gradient(#ff5000 20%, rgba(255, 80, 0, 0) 70%);
  border-radius: 50%;
  mix-blend-mode: screen;
  opacity: 0;
  position: absolute;
  bottom: 0;
  width: 8vw;
  height: 6vw; }
.sw-fire .particle:nth-of-type(1) {
  animation-delay: 0.2978669529s;
  left: calc((100% - 5vw) * 0); }
.sw-fire .particle:nth-of-type(2) {
  animation-delay: 0.6482163147s;
  left: calc((100% - 5vw) * 0.02); }
.sw-fire .particle:nth-of-type(3) {
  animation-delay: 0.1254242557s;
  left: calc((100% - 5vw) * 0.04); }
.sw-fire .particle:nth-of-type(4) {
  animation-delay: 0.7286764127s;
  left: calc((100% - 5vw) * 0.06); }
.sw-fire .particle:nth-of-type(5) {
  animation-delay: 0.4249997055s;
  left: calc((100% - 5vw) * 0.08); }
.sw-fire .particle:nth-of-type(6) {
  animation-delay: 0.3791831866s;
  left: calc((100% - 5vw) * 0.1); }
.sw-fire .particle:nth-of-type(7) {
  animation-delay: 0.113537683s;
  left: calc((100% - 5vw) * 0.12); }
.sw-fire .particle:nth-of-type(8) {
  animation-delay: 0.8107000566s;
  left: calc((100% - 5vw) * 0.14); }
.sw-fire .particle:nth-of-type(9) {
  animation-delay: 0.7044211728s;
  left: calc((100% - 5vw) * 0.16); }
.sw-fire .particle:nth-of-type(10) {
  animation-delay: 0.5520876879s;
  left: calc((100% - 5vw) * 0.18); }
.sw-fire .particle:nth-of-type(11) {
  animation-delay: 0.2435225772s;
  left: calc((100% - 5vw) * 0.2); }
.sw-fire .particle:nth-of-type(12) {
  animation-delay: 0.7347984972s;
  left: calc((100% - 5vw) * 0.22); }
.sw-fire .particle:nth-of-type(13) {
  animation-delay: 0.7698950079s;
  left: calc((100% - 5vw) * 0.24); }
.sw-fire .particle:nth-of-type(14) {
  animation-delay: 0.7621350564s;
  left: calc((100% - 5vw) * 0.26); }
.sw-fire .particle:nth-of-type(15) {
  animation-delay: 0.7835331397s;
  left: calc((100% - 5vw) * 0.28); }
.sw-fire .particle:nth-of-type(16) {
  animation-delay: 0.1863204711s;
  left: calc((100% - 5vw) * 0.3); }
.sw-fire .particle:nth-of-type(17) {
  animation-delay: 0.882522548s;
  left: calc((100% - 5vw) * 0.32); }
.sw-fire .particle:nth-of-type(18) {
  animation-delay: 0.524897339s;
  left: calc((100% - 5vw) * 0.34); }
.sw-fire .particle:nth-of-type(19) {
  animation-delay: 0.0893295039s;
  left: calc((100% - 5vw) * 0.36); }
.sw-fire .particle:nth-of-type(20) {
  animation-delay: 0.1112894982s;
  left: calc((100% - 5vw) * 0.38); }
.sw-fire .particle:nth-of-type(21) {
  animation-delay: 0.2350502728s;
  left: calc((100% - 5vw) * 0.4); }
.sw-fire .particle:nth-of-type(22) {
  animation-delay: 0.4273660142s;
  left: calc((100% - 5vw) * 0.42); }
.sw-fire .particle:nth-of-type(23) {
  animation-delay: 0.2542263168s;
  left: calc((100% - 5vw) * 0.44); }
.sw-fire .particle:nth-of-type(24) {
  animation-delay: 0.5467992518s;
  left: calc((100% - 5vw) * 0.46); }
.sw-fire .particle:nth-of-type(25) {
  animation-delay: 0.5279011287s;
  left: calc((100% - 5vw) * 0.48); }
.sw-fire .particle:nth-of-type(26) {
  animation-delay: 0.6151815472s;
  left: calc((100% - 5vw) * 0.5); }
.sw-fire .particle:nth-of-type(27) {
  animation-delay: 0.5564357353s;
  left: calc((100% - 5vw) * 0.52); }
.sw-fire .particle:nth-of-type(28) {
  animation-delay: 0.5662248778s;
  left: calc((100% - 5vw) * 0.54); }
.sw-fire .particle:nth-of-type(29) {
  animation-delay: 0.7546163176s;
  left: calc((100% - 5vw) * 0.56); }
.sw-fire .particle:nth-of-type(30) {
  animation-delay: 0.5406056994s;
  left: calc((100% - 5vw) * 0.58); }
.sw-fire .particle:nth-of-type(31) {
  animation-delay: 0.2152847227s;
  left: calc((100% - 5vw) * 0.6); }
.sw-fire .particle:nth-of-type(32) {
  animation-delay: 0.2463678965s;
  left: calc((100% - 5vw) * 0.62); }
.sw-fire .particle:nth-of-type(33) {
  animation-delay: 0.7535113268s;
  left: calc((100% - 5vw) * 0.64); }
.sw-fire .particle:nth-of-type(34) {
  animation-delay: 0.348762432s;
  left: calc((100% - 5vw) * 0.66); }
.sw-fire .particle:nth-of-type(35) {
  animation-delay: 0.346672212s;
  left: calc((100% - 5vw) * 0.68); }
.sw-fire .particle:nth-of-type(36) {
  animation-delay: 0.76217575s;
  left: calc((100% - 5vw) * 0.7); }
.sw-fire .particle:nth-of-type(37) {
  animation-delay: 0.4126767066s;
  left: calc((100% - 5vw) * 0.72); }
.sw-fire .particle:nth-of-type(38) {
  animation-delay: 0.2156161158s;
  left: calc((100% - 5vw) * 0.74); }
.sw-fire .particle:nth-of-type(39) {
  animation-delay: 0.4067539864s;
  left: calc((100% - 5vw) * 0.76); }
.sw-fire .particle:nth-of-type(40) {
  animation-delay: 0.4332445871s;
  left: calc((100% - 5vw) * 0.78); }
.sw-fire .particle:nth-of-type(41) {
  animation-delay: 0.8880111518s;
  left: calc((100% - 5vw) * 0.8); }
.sw-fire .particle:nth-of-type(42) {
  animation-delay: 0.9998599014s;
  left: calc((100% - 5vw) * 0.82); }
.sw-fire .particle:nth-of-type(43) {
  animation-delay: 0.3079801554s;
  left: calc((100% - 5vw) * 0.84); }
.sw-fire .particle:nth-of-type(44) {
  animation-delay: 0.0914599995s;
  left: calc((100% - 5vw) * 0.86); }
.sw-fire .particle:nth-of-type(45) {
  animation-delay: 0.3381472071s;
  left: calc((100% - 5vw) * 0.88); }
.sw-fire .particle:nth-of-type(46) {
  animation-delay: 0.3290850258s;
  left: calc((100% - 5vw) * 0.9); }
.sw-fire .particle:nth-of-type(47) {
  animation-delay: 0.7484752967s;
  left: calc((100% - 5vw) * 0.92); }
.sw-fire .particle:nth-of-type(48) {
  animation-delay: 0.7112399253s;
  left: calc((100% - 5vw) * 0.94); }
.sw-fire .particle:nth-of-type(49) {
  animation-delay: 0.3559621962s;
  left: calc((100% - 5vw) * 0.96); }
.sw-fire .particle:nth-of-type(50) {
  animation-delay: 0.5725174743s;
  left: calc((100% - 5vw) * 0.98); }
@keyframes rise {
  from {
    opacity: 0;
    transform: translateY(0) scale(1); }
  25% {
    opacity: 1; }
  to {
    opacity: 0;
    transform: translateY(-10em) scale(0); } }
.tournament-landing {
  overflow: hidden;
  background-color: #260804; }
  .tournament-landing .sw-bg .container, .tournament-landing header .container, .tournament-landing section .container, .tournament-landing footer .container {
    position: relative;
    max-width: 1920px;
    width: 100%;
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center; }
  .tournament-landing .sw-bg {
    position: relative;
    width: 100%;
    height: 100%; }
  .tournament-landing img {
    width: 100%; }
  .tournament-landing .sw-bg .container > img {
    mask-image: linear-gradient(90deg, transparent 0, #000 10%, #000 90%, transparent);
    width: 100%; }
  .tournament-landing .device-desktop .flip-countdown.size-medium .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec {
    font-size: 6.6vw; }
    @media (min-width: 1920px) {
      .tournament-landing .device-desktop .flip-countdown.size-medium .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec {
        font-size: 128px; } }
  .tournament-landing .device-desktop .flip-countdown.size-medium .flip-countdown-piece .flip-countdown-title {
    font-size: 3vw; }
  .tournament-landing .device-mobile.in-landscape .flip-countdown.size-small .flip-countdown-piece .flip-countdown-card .flip-countdown-card-sec {
    font-size: 6vw; }
  .tournament-landing .device-mobile.in-landscape .flip-countdown.size-small .flip-countdown-piece .flip-countdown-title {
    font-size: 3vw; }

.animation-fade-in-1s {
  animation: fadeIn 1s; }

a {
  color: #8dd363; }
  a:hover {
    color: #fff;
    text-decoration: none; }

.button-main {
  text-align: left;
  background-color: transparent;
  color: #8dd363;
  line-height: 1em;
  padding: 4px;
  margin: 2px 4px;
  border-left: 1px solid rgba(141, 211, 99, 0.5);
  border-top: 1px solid rgba(141, 211, 99, 0.2);
  border-right: 1px solid rgba(141, 211, 99, 0.5);
  border-bottom: 1px solid rgba(141, 211, 99, 0.2); }
  .button-main.active {
    color: #000 !important;
    text-decoration: none !important;
    background-color: #8dd363 !important; }
  .button-main.highlight {
    color: #000 !important;
    text-decoration: none !important;
    background-color: gold !important;
    border: 1px solid gold; }
    .button-main.highlight:hover {
      background-color: gold !important; }
  .button-main:hover {
    color: #181717 !important;
    text-decoration: none !important;
    background-color: #8dd363 !important; }

.text-h6 {
  font-size: 1.25rem;
  font-weight: 500;
  line-height: 1.6;
  letter-spacing: 0.0075em;
  text-align: center; }

.text-h5 {
  margin: 0 auto 15px;
  font-weight: 700;
  line-height: 1.5;
  text-align: center; }

.form-logo {
  width: 100%;
  text-align: center; }
  .form-logo svg {
    margin: 20px auto;
    max-width: 100%;
    max-height: 43px; }

.toolbar-logo {
  width: 140px; }
  .toolbar-logo svg {
    margin: 4px 6px 4px 0;
    max-width: 100%;
    height: 25px; }

.main-container.container-fluid, .main-container.container-sm, .main-container.container-md, .main-container.container-lg, .main-container.container-xl, .main-container.container-xxl {
  padding-left: 0;
  padding-right: 0; }

.main-container {
  display: block;
  margin-top: 60px; }
  .main-container.window-fixed {
    position: fixed;
    width: 100%;
    top: 60px;
    margin-top: 0;
    padding: 0;
    right: 0;
    bottom: 50px;
    left: 0;
    display: flex;
    flex-direction: column; }

.device-desktop #change-password-after-login {
  max-width: 50vw; }

.container {
  max-width: 1920px;
  margin-left: auto;
  margin-right: auto; }

.title {
  font-family: "Oswald", sans-serif;
  text-transform: uppercase;
  display: block;
  font-weight: 400; }
  .title--h1 {
    font-size: 4.62vh;
    line-height: 7.4vh;
    letter-spacing: 0.14em; }
    @media (min-width: 1921px), (min-height: 1081px) {
      .title--h1 {
        font-size: 50px;
        line-height: 80px; } }
    @media (max-width: 767px), (max-width: 900px) and (orientation: landscape) {
      .title--h1 {
        font-size: 40px;
        line-height: 60px;
        text-align: center; } }
  .title--h2 {
    font-size: 35px;
    line-height: 30px;
    letter-spacing: 0.32em;
    padding: 3.385vw 0 2.083vw 0; }
    @media (min-width: 1921px), (min-height: 1081px) {
      .title--h2 {
        padding: 65px 0 40px 0; } }
    @media (max-width: 1024px) {
      .title--h2 {
        letter-spacing: 0.1em; } }
    @media (max-width: 1023px) {
      .title--h2 {
        line-height: 1.2em; } }

.h2a {
  position: relative; }
  .h2a--invert::after {
    left: auto !important;
    right: 7.4vh; }
  .h2a::after {
    position: absolute;
    left: 7.4vh;
    top: -1.85vh;
    content: attr(data-text);
    font-size: 8.958vw;
    line-height: 8.958vw;
    text-transform: none;
    font-weight: 800;
    letter-spacing: normal;
    color: rgba(255, 255, 255, 0.02);
    white-space: nowrap;
    pointer-events: none; }
    @media (min-width: 1921px), (min-height: 1081px) {
      .h2a::after {
        font-size: 172px;
        line-height: 172px;
        left: 80px;
        top: -20px; } }
    @media (max-width: 1024px) {
      .h2a::after {
        left: 3.125vw;
        top: -1vh; } }
    @media (max-width: 1023px) {
      .h2a::after {
        display: none; } }

.btn:focus, button:focus {
  box-shadow: none !important; }

.accordion-button::after,
.accordion-button:not(.collapsed)::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%238dd363'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e"); }

.sw-tournament {
  padding-left: 0 !important;
  padding-right: 0 !important; }

/*# sourceMappingURL=pa.main.css.map */
