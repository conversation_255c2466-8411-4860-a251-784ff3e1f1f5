@import "variables";
@import "tagify";

.device-desktop {
  & .search-banner {
    max-width: 800px;
    max-height: 190px;
    @media (max-width: 1280px) {
      display: none;
    }
  }

  & .search-banner-top {
    @media (min-width: 1279px) {
      display: none;
      height: 0;
    }
  }

  & .sw-search {
    & .sw-search-container {
      //max-width: 800px;
      min-width: 800px;
    }
  }
}

.device-mobile {
  & .search-banner {
    display: none;
    height: 0;
    width: 0;
  }

  & .sw-search-container {
    flex: initial;
  }
}


.search-banner {
  display: block;

  & video {
    width: 100%;
    display: block;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
  }
}

.device-desktop .search-banner {
  & .desktop-video {
    display: block;
  }

  & .mobile-landscape-video {
    display: none;
  }

  & .mobile-portrait-video {
    display: none;
  }
}

.device-mobile.in-landscape {
  & .search-banner {
    & .desktop-video {
      display: none;
    }

    & .mobile-landscape-video {
      display: block;
    }

    & .mobile-portrait-video {
      display: none;
    }
  }
}

.device-mobile.in-portrait {
  & .search-banner {
    & .desktop-video {
      display: none;
    }

    & .mobile-landscape-video {
      display: none;
    }

    & .mobile-portrait-video {
      display: block;
    }
  }
}

#partners-search {
  flex: 1;
  display: flex;
  flex-direction: column;

  .banner-image {
    flex-shrink: 0;
  }

  .device-desktop & {
    @media (min-width: 1281px) {
      .banner-swiper.search-banner {
        margin-left: 0;
      }
      .swiper {
        &:before {
          content:'';
          position: absolute;
          top: 0;
          left: 0;
          bottom: 0;
          width: 1px;
          background: $mainDark;
          z-index: 1000;
        }
      }
    }
  }
}

.sw-search {
  //position: fixed;
  //top: 0;
  //left: 0;
  //  z-index: 1100;
  background-color: rgba(#181717, 0.95);
  width: 100%;
  //  max-width: 1920px;
  //height: 100%;
  transition: $tr2;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: stretch;
  flex-shrink: 0;

  & .banner-image {
    flex: .5;
    margin-right: 20px;
  }

  & .search-col {

  }


  &__input {
    display: block;
    text-transform: uppercase;
    color: #fff;
    background-color: #1e1d1d;
    border-radius: 18px;
    width: 100%;
    height: 100%;
    margin: 2px 0 2px 2px;
    font-size: 14px;
    font-weight: 700;
    letter-spacing: 0.012em;
    flex-shrink: 0;
    border: 2px solid transparent;
    padding: 10px 85px 10px 20px;

    @media (max-width: 480px) {
      margin: 0;
      border-color: $secondaryColor;
      //width btn + 20px
      padding: 17px 80px 17px 20px;
    }
  }

  &__close {
    flex-shrink: 0;
    width: 70px;
    height: 57px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    @media (max-width: 480px) {
      width: 60px;
      height: 57px;
      svg text {
        fill: $secondaryColor;
      }
    }
  }

  &__btn {
    cursor: pointer;
    width: 60px;
    height: 50px;
    position: absolute;
    right: 0;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;

    @media (max-width: 480px) {
      top: 3px;
      height: 53px;
    }
  }
}

//del after test - test btn
#openResult {
  visibility: hidden;

  position: relative;
  left: 50%;
  top: 5px;
  transform: translateX(-50%);
  z-index: 99999;
  border: none;
  outline: none;
  text-transform: uppercase;
  background-color: firebrick;
  font-weight: 700;
  font-size: 16px;
  margin-bottom: 40px;
  width: 200px;
  cursor: pointer;

  &.on {
    visibility: visible;
  }
}

.sw-search-container {
  width: 100%;
  height: 100%;
  // max-width: 1000px;
  padding: 4px;
  //  padding-bottom: 70px;
  // margin: auto;
  //display: flex;
  flex-direction: column;
  overflow-x: hidden;
  overflow-y: auto;
  //flex: 0.9;

  @media (max-width: 1199px) {
    padding: 4px;
  }

  @media (max-width: 480px) {
    padding-bottom: 4px;
  }

  //hide scrollbar
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    width: 0;
    background: transparent;
  }

  &__menu {
    width: 100%;
    margin-bottom: auto;
    position: relative;

    &.open {
      @media (max-width: 480px) {
        .sw-search__close text {
          fill: #1e1d1d;
        }
      }
    }
  }

  &__result {
    margin-top: 10px;
    margin-bottom: auto;
    width: 100%;
  }
}

.sw-search-control {
  border-radius: 18px;
  margin-top: auto;
  width: 100%;
  background-color: $secondaryColor;
  transition: $tr2;
  position: relative;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;

  & .f_filters_btn {
    background-color: $secondaryColor;
    color: #000;
    text-transform: uppercase;
    overflow: hidden;
    font-size: 0.8rem;
    font-weight: bold;
    border: none;
    border-radius: 18px;

    &:hover, &:active, &:focus, &:not(:disabled):not(.disabled):active {
      background-color: $secondaryColor;
      color: #FFFFFF;
    }
  }

  label {
    width: 100%;
    display: flex;
    position: relative;
    margin-bottom: 0;
  }

  @media (max-width: 480px) {
    background-color: transparent;
  }
}

// menu above categories
.sw-search-submenu {
  border-radius: 18px;
  background-color: $secondaryColor;
  width: 100%;
  height: 100%;
  display: none;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  color: #181717;
  padding: 72px 12px 18px;

  &.open {
    display: flex;

    @media (max-width: 480px) {
      padding: 70px 7px 18px;
    }
  }

  &__close {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-width: 100px;
    max-width: 120px;
    flex-shrink: 0;
    border-left: 1px solid rgba(#000, 0.5);
    cursor: pointer;
    margin-left: 4px;

    @media (max-width: 480px) {
      min-width: 88px;
    }

    div {
      text-transform: uppercase;
      font-size: 14px;
      font-weight: bold;
      display: inline-block;
      text-align: center;

      span {
        display: block;
      }
    }
  }

  &__types {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.sw-search-filter {
  position: relative;
  width: 100%;
  height: calc(100% - 57px);
  min-height: 80px;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-top: 26px;
  // flex-wrap: wrap;

  @media (max-width: 1250px) {
    flex-wrap: wrap;
  }

  &__item {
    overflow: hidden;
    border-radius: 18px;
    height: 80px;
    min-width: 66px;
    max-width: 130px;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: $tr2;
    text-align: center;
    position: relative;
    padding: 1px;
    z-index: 1;
    flex-shrink: 0;
    margin: auto;
    //
    //@media (max-width: 1023px) {
    //  width: 120px;
    //}
    //
    //@media (max-width: 480px) {
    //  height: 80px;
    //  width: 80px;
    //}

    &:hover {
      opacity: 0.9;
    }

    &--logo {
      position: relative;
      min-height: 40px;
      max-height: 40px;
      width: 40px;
      margin-bottom: 10px;

      img {
        display: inline-block;
        transition: $tr2;
        filter: grayscale(80%) brightness(50%) contrast(50%);
        margin: auto;
        max-width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    &--text {
      width: 100%;
      text-transform: uppercase;
      color: #fff;
      font-size: 13px;
      font-weight: 700;
      margin: 0 4px;

      @media (max-width: 1024px) {
        font-size: 12px;
      }
    }

    &--close {
      display: none;
      align-items: center;
      justify-content: center;
      position: absolute;
      right: 0;
      top: 0;
      z-index: 2;
      width: 30px;
      height: 30px;
      cursor: pointer;

      &:hover {
        opacity: 0.7;
      }
    }

    &.clear-all-filter {
      opacity: 0.5;
      cursor: auto;
    }

    &.active {
      &.clear-all-filter {
        opacity: 1;
        cursor: pointer;
      }

      .sw-search-filter__item--close {
        display: flex;
        // animation: 0.15s visibleTools 0.15s linear both;
      }

      .sw-search-filter__item--logo {
        img {
          filter: none;
        }
      }
    }
  }
}

.sw-search-submenu-inner {
  display: none;

  &.open {
    display: flex;
    width: 100%;
    padding: 0 15px;

    @media (max-width: 480px) {
      padding: 0 10px;
    }
  }

  div {
    animation: 0.15s visibleTools 0.15s linear both;
  }
}

//tags
.sw-search-tags {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.sw-search-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1e1d1d;
  border: 2px solid #1e1d1d;
  padding: 4px 6px;
  border-radius: 29px;
  min-width: 110px;
  margin: 6px;
  transition: $tr2;
  cursor: pointer;
  text-transform: uppercase;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
  line-height: 1;

  @media (max-width: 480px) {
    /*margin: 15px 10px;*/
    min-width: auto;
  }

  &--small {
    min-width: initial;
  }

  &.active {
    color: #fff;
    background-color: #1e1d1d;
  }
}

//range
.sw-search-range {
  min-width: 210px;
  width: 100%;
  margin: auto auto auto 0;

  @media (min-width: 769px) {
    // max-width: 50%;
  }

  &__slider {
    height: 50px;
  }

  &__values {
    display: flex;
    align-items: center;

    &--from,
    &--to {
      font-weight: bold;
      letter-spacing: 0.12em;
      display: flex;
      align-items: center;
      position: relative;

      &::after {
        content: "%";
      }
    }

    &--to {
      margin-left: auto;
    }
  }

  //overwrite styles
  .irs-line {
    height: 6px;
    border-radius: 0;
    left: 14px;
    width: calc(100% - 29px); //depends on handle width -1
    background-color: rgba(#171616, 0.2);

    @media (max-width: 480px) {
      width: calc(100% - 27px);
    }
  }

  .irs-bar {
    height: 6px;
    background-color: #171616;
  }

  .irs-handle {
    cursor: pointer;
    width: 30px;
    height: 30px;
    top: 13px;

    i:first-child {
      width: 6px;
      height: 26px;
      background-color: #171616;
      margin-left: -3px;
      top: 2px;
    }
  }

  .irs-handle.state_hover > i:first-child,
  .irs-handle:hover > i:first-child {
    background-color: #000;
  }

  .irs-grid-pol {
    background-color: #000;
    width: 2px;
    height: 4px;
    top: auto;
    bottom: 0;
  }

  .irs-grid-text {
    display: none;
  }

  .irs-grid {
    top: 5px;
  }
}

//custom checkbox
.sw-search-checkboxes-group {
  margin: 0 10px;
}

.sw-search-checkbox {
  position: relative;
  display: block;
  margin-bottom: 2px;

  &:last-child {
    margin-bottom: 0;
  }

  & input {
    vertical-align: top;
    width: 18px;
    height: 18px;

    & + label {
      text-transform: uppercase;
      font-size: 16px;
      font-weight: 700;
      cursor: pointer;
    }

    //noinspection CssInvalidHtmlTagReference
    &:not(checked) {
      position: absolute;
      z-index: -1;
      opacity: 0;

      & + label {
        position: relative;
        padding: 0 0 0 28px;

        &::before {
          content: "";
          position: absolute;
          top: 2px;
          left: 0;
          width: 18px;
          height: 18px;
          border: 1px solid #cdd0d8;
          border-radius: 50%;
          background-color: #fff;
        }

        &::after {
          content: "";
          position: absolute;
          top: 7px;
          left: 5px;
          width: 8px;
          height: 8px;
          background: #181717;
          border-radius: 50%;
          opacity: 0;
          transition: 0.2s;
        }
      }
    }

    &:checked + label::before {
      border-color: #181717;
    }

    &:checked + label::after {
      opacity: 1;
    }
  }
}

// search inner
.sw-search--inner {
  display: block;
  position: static;

  .sw-search-container {
    max-width: 100%;
  }
}

//result part
.sw-search-result {
  &__title {
    display: block;
    font-family: $fontOswald;
    color: $secondaryColor;
    text-align: center;
    margin-bottom: 20px;

    strong {
      font-weight: normal;
      text-transform: uppercase;
      font-size: 35px;
      line-height: 30px;
      letter-spacing: 0.32em;

      // number of results
      #search {
        margin-right: 20px;
      }
    }
  }

  &__btnTop {
    margin: 25px auto auto;
    display: flex;
    align-items: center;
    justify-content: center;

    @media (max-width: 480px) {
      margin-top: 30px;
    }

    a {
      text-transform: uppercase;
      font-weight: bold;
      letter-spacing: 0.12em;
      font-size: 15px;
      line-height: 1;
      white-space: nowrap;
      color: $secondaryColor;
      border-radius: 29px;
      border: 2px solid $secondaryColor;
      padding: 21px 76px;
      transition: $tr2;

      &:hover {
        color: #181717;
        background-color: $secondaryColor;
      }
    }
  }

  & .sw-search-result-item {
    &:last-of-type {
      border-bottom-color: transparent;
    }
  }
}

.sw-search-result-item {
  padding: 20px 0;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  border-bottom: 1px solid rgba(#fff, 0.1);
  transition: $tr2;
  position: relative;

  &::after {
    transition: $tr2;
    opacity: 0;
    z-index: -1;
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(270deg, rgba(0, 0, 0, 0) 0%, rgba(249, 189, 33, 0.1) 100%);
  }

  &__img {
    flex-shrink: 0;
    width: 100%;
    max-width: 176px;
    min-height: 100px;
    background-color: #1e1d1d;
    margin-right: 30px;

    @media (max-width: 568px) {
      margin-right: 0;
    }
  }

  &__desc {
    display: flex;
    flex-direction: column;
    height: 100%;
    margin: auto auto auto 0;

    @media (max-width: 568px) {
      margin-top: 20px;
    }
  }

  &:hover {
    opacity: 0.9;

    &::after {
      opacity: 1;
    }
  }
}

.device-desktop .sw-search-result-item {
  flex-wrap: nowrap;
}

.device-type-mobile {
  .sw-search-result-item {
    flex-wrap: nowrap;

    @media (max-width: 767px) {
      flex-wrap: wrap;
    }
  }
}

.search-result-text {
  height: 100%;
  display: flex;
  flex-direction: column;

  &__title {
    text-transform: uppercase;
    font-weight: bold;
    font-size: 14px;
    letter-spacing: 0.12em;
  }

  &__tags {
    margin-top: 20px;

    span {
      color: #aea8a8;
      font-size: 14px;
      letter-spacing: 0.12em;
      display: inline-block;
      margin-right: 15px;
      position: relative;

      &::after {
        content: ";";
      }
    }
  }
}

.sw-search-range.adv-rtp .sw-search-range__values {
  display: none;
}

.sw-search-range.adv-rtp .irs--flat .irs-to,
.sw-search-range.adv-rtp .irs--flat .irs-from,
.sw-search-range.adv-rtp .irs--flat .irs-single,
.sw-search-range.adv-rtp .irs--flat .irs-to,
.sw-search-range.adv-rtp .irs--flat .irs-max,
.sw-search-range.adv-rtp .irs--flat .irs-min {
  font-size: 14px;
  font-weight: 700;
  background-color: transparent;
  letter-spacing: 0.12em;
  color: #000;
  margin-top: -10px;
}

.sw-search-range.adv-rtp .irs--flat .irs-from:before,
.sw-search-range.adv-rtp .irs--flat .irs-single:before,
.sw-search-range.adv-rtp .irs--flat .irs-to:before {
  border-top-color: #000;
}

.sw-search-tags.adv-tags {
  flex: 1;
  width: 100%;
}

.search-item-disabled {
  cursor: not-allowed;
  opacity: 0.4;
}
