@import "variables";

.device-desktop {
  & .footer {
    & .wrap.open {
      overflow: hidden !important;
    }
    &.open {
      height: 400px;
    }
  }
}

.device-mobile {
  & .footer {
    &.open {
      bottom: 0;
      top: $topHeaderHeight;
      height: calc(100vh - 60px);
    }
  }
}


.footer {
  border-top: 2px solid rgba(#fff, 0.1);
  text-transform: uppercase;
  padding-top: 80px;
  padding-bottom: 80px;
  color: #aea8a8;
  transition: all 0.2s ease-in-out;
  background-color: $mainDark;

  &.window-fixed {
    position: fixed;
    width: 100%;
    left: 0;
    right: 0;
    bottom: 0;
    height: 50px;
    padding: 0;
    z-index: 1001;

    &.open {
      padding-top: 80px;
      padding-bottom: 80px;

      .show_all {
        background: none;
      }
    }
  }

  & .wrap.open {
    overflow: auto;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0
  }

  .show_all {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50px;
    display: flex;
    justify-content: space-around;
    background-color: $mainDark;
    z-index: 1040;
    align-items: center;

    .f_title {
      color: $textColor;
      font-size: 14px;
      margin-left: auto;
      display: flex;
//      line-height: 50px;
      vertical-align: middle;
      opacity: 0.6;
      text-transform: initial;
      justify-content: center;
      align-items: center;
    }

    .f_show_btn {
      background-color: $secondaryColor;
      z-index: 1010;
      opacity: 0.5;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      font-size: 14px;
      margin: 5px auto 5px 15px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  a {
    color: inherit;
    outline: 0;
    text-decoration: none;
  }

  &__title {
    color: $textColor;
    font-size: 22px;
    margin-bottom: 2.63vh;
    display: inline-block;
    padding-top: 10px;

    //comment from Vasilij (2/12/19)
    span {
      color: $textColor;
    }

    //@media (min-width: 1921px), (min-height: 1081px) {
    //  margin-bottom: 50px;
    //}
  }

  &__menu {
    @media (min-width: 992px) {
      display: none;
    }
  }

  &__contact, &__connected {
    @media (max-width: 992px) {
      margin-bottom: 4.63vh;
    }

    @media (max-width: 812px) and (orientation: landscape) {
      width: 400px;
    }

    @media (max-width: 480px) {
      width: auto;
    }

    div {
      display: flex;
      justify-content: space-between;
    }

    ul {
      padding: 0;
      margin: 0;
      list-style: none;

      li {
        // white-space: nowrap;
        color: #aea8a8;
        font-size: 14px;
        margin-bottom: 2.31vh;
        text-transform: none;
        transition: $tr2;

        @media (min-width: 1921px), (min-height: 1081px) {
          margin-bottom: 25px;
        }

        &:last-child {
          margin-bottom: 0;
        }

        a {
          color: $textColor;
          transition: $tr2;
          display: flex;
          flex-direction: row;
          align-items: center;

          img {
            width: 20px;
            margin: 0 4px;
            filter: invert(200%) sepia(0%) brightness(119%) contrast(122%);
          }

          &:hover {
            color: rgba($secondaryColor, 1);
          }
        }
      }
    }
  }

  &__connected {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    ul {
      li {
        padding-left: 0;
        margin-bottom: 10px;
      }
    }

    @media (max-width: 992px) {
      align-items: flex-start;
      margin-top: 4.63vh;
      height: auto;
    }

    .lang {
      margin: 0.5em 0;
    }
  }

  &__social {
    opacity: 0.6;
    margin-bottom: 60px;
  }

  &__copyright {
    margin-top: auto;
    white-space: nowrap;

    a {
      display: block;
      text-align: right;

      &:nth-child(2) {
        margin-bottom: 30px;
      }

      //@media (max-width: 992px) {
      //  text-align: left;
      //}
    }

    //@media (max-width: 992px) {
      margin-top: 60px;
    //}
  }
}

.flicenses {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  width: 100%;
  margin: 0 auto;

  img {
    @media (max-width: 480px), (max-width: 812px) and (orientation: landscape) {
      max-width: 100%;
      max-height: 50px;
    }
  }

  &__item {
    margin-right: 2em;
    margin-bottom: 2em;
    display: flex;
    align-items: center;
    opacity: 0.6;
    max-height: 60px;
    max-width: 220px;

    & img {
      width: 100%;
    }

    &:hover {
      filter: sepia(1);
    }

    @media (max-width: 992px) {
      margin: 1em;
    }

    //@media (max-width: 600px) {
    //  width: 40%;
    //  flex-shrink: 0;
    //}
  }
}
