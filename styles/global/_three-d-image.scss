
.three-d-image > .main-img {
  opacity: 0;
}

.three-d-image > .left-img {
  perspective-origin: left center;
  clip-path: polygon(0 0, 50% 0, 50% 100%, 0 100%);
  position: absolute;
  top: 0;
  animation: anim-left-side 2s ease-in-out infinite;
  animation-direction: alternate;
}

.three-d-image > img.right-img {
  perspective-origin: right center;
  clip-path: polygon(50% 0, 100% 0, 100% 100%, 50% 100%);
  position: absolute;
  top: 0;
  animation: anim-right-side 2s ease-in-out infinite;
  animation-direction: alternate;
}

//noinspection CssInvalidFunction
@keyframes anim-left-side {
  0% {
    transform: rotateY(-1deg) scaleX(.92);
  }
  100% {
    transform: rotateY(0deg) scaleX(1);
  }
}

//noinspection CssInvalidFunction
@keyframes anim-right-side {
  0% {
    transform: rotateY(0deg) scaleX(1);
  }
  100% {
    transform: rotateY(-1deg) scaleX(.92);
  }
}
