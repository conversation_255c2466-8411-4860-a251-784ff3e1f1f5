@import "variables";

.loading-spinner {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;

  & .pa-spinner {
    margin: 1rem;
    width: 2rem;
    height: 2rem;
    color: $secondaryColor;
  }

  & .pa-spinner-text {
    padding: 1rem;
    font-size: 1.5rem;
    color: $secondaryColor;
  }
}

//noinspection CssInvalidFunction
@keyframes logoRotate {
  0% {
    transform: rotateZ(0deg);
  }
  100% {
    transform: rotateZ(360deg);
  }
}

.logo-spinner {
  width: 40px;
  animation: logoRotate 4s infinite linear;
}