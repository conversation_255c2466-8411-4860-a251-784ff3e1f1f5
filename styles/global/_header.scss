@import "variables";

// header
.header {
  position: relative;
  min-height: $header-height;

  @media (min-width: 1900px) {
    margin-bottom: 50px;
  }
}

.header-nav {
  display: flex;
  padding: 12px 18px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 61px;
  z-index: 1;

  @media (min-width: 1024px) {
    padding: 7px 22px;
    height: 92px
  }
}

.header-logo {
  display: inline-flex;

  &-img {
    display: block;
    height: 37px;

    @media (min-width: 1024px) {
      height: 77px
    }
  }
}

// header banner
.header-banner {
  position: relative;
  display: flex;
  flex-direction: column;
  margin-left: auto;
  margin-right: auto;
  max-width: 1920px;

  .header-banner {
    margin-bottom: 0;
  }

  //@media (min-width: 768px) {
  //  .device-mobile & {
  //    height: 56.25vw;
  //  }
  //}
  //.device-desktop & {
  //  z-index: 0;
  //  height: calc(100vh - #{$header-height} - 100px);
  //  max-height: 1000px;
  //  min-height: 500px;
  //
  //  .header-banner-figure {
  //    bottom: -100px
  //  }
  //}

  &:empty {
    display: none;
  }
  &-link {
    background: transparent;
    font-size: 0;
    color: transparent;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    cursor: pointer;
  }

  &-shadow {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    display: none;

    @media (min-width: 768px) {
      display: block;
      //background: radial-gradient(circle farthest-corner at left, rgba($mainDark, .95) 0, transparent 40%)
      background: linear-gradient(23deg, $mainDark, transparent 45%)
    }

    &:before,
    &:after {
      content: "";
      position: absolute;
      left: 0;
      right: 0
    }

    &:before {
      top: 0;
      height: 36%;
      background: -webkit-gradient(linear, left bottom, left top, from(transparent), to(rgba($mainDark, .9)));
      background: linear-gradient(0deg, transparent 0, rgba($mainDark, .9));
      display: none;
    }

    &:after {
      bottom: 0;
      height: 30%;
      background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(rgba($mainDark, .98)));
      background: linear-gradient(180deg, transparent 0, rgba($mainDark, .98))
    }

    .show-shadow & {
      @media (min-width: 768px) {
        background: radial-gradient(circle farthest-corner at left, rgba($mainDark, .95) 0, transparent 40%)
      }
      &:after {
        height: 50%;
      }
      &:before {
        display: block;
      }
    }
  }

  &-figure {
    position: relative;
    display: block;
    margin-bottom: 0;

    @media (min-width: 768px) {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      top: 0;
    }

    &-img,
    &-video {
      display: block;
      width: 100%;
      object-fit: cover;
      object-position: top center;

      @media (max-width: 767px) {
        .device-landscape & {
          max-height: 70vh;
        }
      }
      @media (min-width: 768px) {
        height: 100%
      }
    }

    &-container {
      display: inline;
    }
  }

  &-container {
    width: 100%;
    position: relative;
    padding: 20px var(--bs-gutter-x,.75rem);
    display: flex;
    flex-direction: column;
    margin-right: auto;
    margin-left: auto;

    @media (min-width: 768px) {
      padding: 30px 45px;
      max-width: 1920px;
      flex-grow: 1
    }
    @media (min-width: 1440px) {
      padding: 30px 60px
    }
  }

  &-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    margin-bottom: 0;
    z-index: 10;

    @media (min-width: 768px) {
      justify-content: center;
    }

    &-figure {
      display: flex;
      justify-content: center;
      margin-bottom: 15px;

      @media (min-width: 768px) {
        display: flex;
        justify-content: flex-start
      }
      @media (min-width: 1900px) {
        margin-bottom: 25px
      }

      &-img {
        display: block;
        max-width: 100%;
        max-height: 100px;
        cursor: pointer;

        @media (min-width: 768px) {
          max-height: 6.927vw
        }
        @media (min-width: 1900px) {
          max-height: 133px
        }
      }
    }

    &-title {
      font-family: $fontOswald;
      line-height: 1.1;
      letter-spacing: .14em;
      font-weight: bold;
      margin-bottom: 5px;
      text-shadow: 1px 1px 0 $mainDark;
      font-size: $minTitleFontSize;

      @media (min-width: 768px) {
        font-size: $midTitleFontSize;
      }
      @media (min-width: 1200px) {
        font-size: $maxTitleFontSize;
      }
    }

    &-text {
      text-shadow: 1px 1px 0 $mainDark;
      margin-bottom: 0;
      font-size: $minTextFontSize;

      @media (min-width: 768px) {
        font-size: $midTextFontSize;
        max-width: 39.063vw
      }
      @media (min-width: 1024px) {
        max-width: 34.896vw
      }
      @media (min-width: 1200px) {
        font-size: $maxTextFontSize;
        max-width: 670px;
      }
    }
  }

  &-content.bottom {
    justify-content: flex-end !important;
  }

  &-content.static {
    display: inline-flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-end !important;

    @media (min-width: 768px) {
      max-width: 30.208vw;
    }
    @media (min-width: 1900px) {
      max-width: 580px;
      padding: 100px 0;
    }

    .header-banner-content-figure {
      margin-bottom: 10px;
    }

    .header-banner-content-figure-img {
      height: 30.208vw;
      max-height: 115px;
      width: 100%;
      object-fit: contain;
      object-position: 50%;

      @media (min-width: 768px) {
        max-height: 17.552vw;
      }
      @media (min-width: 1900px) {
        max-height: 337px;
      }
    }

    .header-banner-content-title {
      font-family: $fontOswald;
      line-height: 1.1;
      letter-spacing: .14em;
      text-align: left;
      margin-bottom: 5px;
      text-transform: uppercase;
      text-shadow: 1px 1px 0 $mainDark;
      //text-shadow: 2px 2px 0 $mainDark, 8px 8px 15px $mainDark;
    }

    .header-banner-content-text {
      font-family: $fontOswald;
      text-align: left;
      margin-bottom: 15px;
      text-shadow: 1px 1px 0 $mainDark;
      //text-shadow: 2px 2px 0 $mainDark, 8px 8px 15px $mainDark;
    }
  }

  &-description {
    @media (min-width: 768px) {
      width: 50vw;
      max-width: 670px;
    }

    &-static-title {
      font-family: $fontOswald;
      text-transform: uppercase;
      line-height: 0.814;
      text-align: left;
      margin-bottom: 2.396vw;
      letter-spacing: 2px;
      font-size: $maxTitleFontSize;

      & .header-small {
        text-decoration: none;
        text-transform: none;
        font-size: $midTitleFontSize;
        line-height: 1.4em;

        &.smaller {
          max-width: 300px;
          font-size: $minTitleFontSize;
        }
      }

      &.column {
        display: flex;
        flex-direction: column;
      }

      @media (min-width: 768px) {
        font-size: 3em;
      }
      @media (min-width: 1900px) {
        margin-bottom: 46px;
      }

      .accent {
        color: $secondaryColor;
      }
    }

    &-title {
      font-family: $fontRoboto;
      text-transform: uppercase;
      font-weight: 400;
      line-height: 1.4;
      text-shadow: 2px 2px 0 $mainDark, 8px 8px 15px $mainDark;
      margin-bottom: 2.396vw;
      letter-spacing: 0.14em;
      font-size: $minTitleFontSize;

      @media (min-width: 768px) {
        font-size: $midTitleFontSize;
      }
      @media (min-width: 1200px) {
        font-size: $maxTitleFontSize;
        margin-bottom: 46px;
      }

      .accent {
        color: $secondaryColor;
      }
    }

    &-text, &-link {
      font-weight: 700;
      font-size: $minTextFontSize;
      line-height: 1.4;
      text-shadow: 2px 2px 0 $mainDark, 8px 8px 15px $mainDark;

      @media (min-width: 768px) {
        font-size: 20px;
      }
      @media (min-width: 1900px) {
        font-size: 29px;
      }
    }

    &-link {
      color: $secondaryColor;
    }
  }

  &-btn {
    &,
    .dropdown & {
      font-size: $minButtonFontSize;
      min-width: 148px;
      height: 27px;
      padding: 4px;
      margin: 0 16px 18px;
      border: 2px solid $secondaryColor;
      border-radius: 19px;
      color: $textColor;
      font-weight: 700;
      text-transform: uppercase;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      background: $mainDark;
      transition: none;

      &:after {
        display: none;
      }

      @media (min-width: 768px) {
        background: transparent;
        font-size: $midButtonFontSize;
        min-width: 250px;
        height: 40px;
        padding: 8px;
        margin: 0 0 10px;
        justify-content: center;
        border: none
      }
      @media (min-width: 1024px) {
        font-size: $maxButtonFontSize;
        min-width: 296px;
        margin: 0 0 15px;
        height: 50px
      }

      &:hover, &:active, &:focus {
        color: $mainDark;
        background-color: $secondaryColor;
        border-color: $secondaryColor;
        box-shadow: none !important;
      }

      &.accent {
        color: $mainDark;
        border-radius: 19px;
        background-color: $secondaryColor;
        justify-content: center;
      }
    }
  }

  &-game {
    display: flex;
    flex-direction: column;
    height: 100%;

    .header-banner-content {
      @media (min-width: 1440px) {
        margin-bottom: 60px
      }

      &-text {
        margin-bottom: 25px;

        @media (min-width: 1900px) {
          margin-bottom: 35px
        }
      }
    }
  }
}

.dropdown {
  .header-banner-btn-container & {
    margin-bottom: 0;
  }

  .header-banner-btn.dropdown-toggle {
    font-family: $fontOpenSans;
    @media (min-width: 768px) {
      background: transparent;
      border: none;
    }

    & + .dropdown-menu {
      overflow: hidden;
      border-radius: 19px;
      border: 1px solid rgba(141, 211, 99, 0.5);

      @media (min-width: 768px) {
        width: 100%;
      }

      .dropdown-item {
        color: $textColor;

        &:hover {
          color: $secondaryColor;
          background-color: rgba($secondaryColor, .2);
        }
      }
    }
  }

  .header-banner-btn:hover, .header-banner-btn:focus,
  .header-banner-btn.btn-primary:not(:disabled):not(.disabled):active,
  .header-banner-btn.btn-primary:not(:disabled):not(.disabled).active,
  &.show > .header-banner-btn.dropdown-toggle.btn {
    color: $mainDark;
    background-color: $secondaryColor;
    border-color: $secondaryColor;
    box-shadow: none;
  }
}

// header banner: btn list
.header-banner-btn-list {
  display: flex;
  flex-direction: column;
  align-items: center;

  @media (min-width: 768px) {
    align-items: flex-start
  }
}

.header-banner-btn-container {
  display: flex;
  flex-wrap: wrap;
  max-width: calc(148px * 2 + 16px * 4);

  @media (min-width: 768px) {
    flex-direction: column;
    flex-wrap: nowrap;
  }
  @media (max-width: 148px*2 + 16px*4 + 40px - 1px) {
    justify-content: center;
  }
}

// header banner: info
.header-banner-info {
  border: solid $secondaryColor;
  border-width: 2px 0;
  text-shadow: 1px 1px 0 $mainDark;

  @media (min-width: 768px) {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    flex-wrap: wrap;
    border: none;
  }

  &-list {
    display: flex;
    justify-content: flex-start;
    font-size: $minButtonFontSize;
    line-height: 1.3;
    color: $textColor;
    overflow-x: auto;
    overflow-y: hidden;
    //width: 100%;
    max-width: 100%;

    @media (min-width: 768px) {
      width: auto;
      border: none;
      font-size: $minButtonFontSize;
      justify-content: space-between;
    }
    @media (min-width: 1024px) {
      font-size: $midButtonFontSize;
      flex-wrap: nowrap;
    }
    @media (min-width: 1440px) {
      font-size: $maxButtonFontSize;
      min-width: 44vw;
    }
  }

  &-list-container {
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    @media (min-width: 768px) {
      font-size: $minButtonFontSize;
      justify-content: space-between;
      flex-wrap: nowrap;
      flex-direction: row;
    }
  }

  &-item {
    text-align: center;
    white-space: nowrap;
    margin: 10px 14px;

    @media (min-width: 400px) {
      margin: 10px 16px;
    }
    @media (min-width: 500px) {
      margin: 10px 20px;
    }
    @media (min-width: 768px) {
      margin: 6px 10px;

      &:first-child {
        margin-left: 0;
      }
    }
    @media (min-width: 1024px) {
      margin: 6px 12px;
    }
    @media (min-width: 1440px) {
      margin: 12px 20px
    }
  }

  &-val {
    font-weight: 700;
    color: $secondaryColor
  }

  &-label {
    margin-bottom: 7px;

    @media (min-width: 1024px) {
      margin-bottom: 11px
    }
  }

  &-divider {
    background-color: $secondaryColor;
    height: 2px;
    width: 100%;
  }
}

// header banner: tools
.header-banner-tools {
  padding: 10px;

  @media (min-width: 768px) {
    padding: 0;
  }

  &-list {
    display: flex;
    align-items: center;
    justify-content: center;

    @media (min-width: 768px) {
      justify-content: space-between;
    }
    @media (min-width: 1440px) {
      min-width: 406px;
    }
  }

  &-title {
    font-size: $minButtonFontSize;
    text-align: center;
    margin-bottom: 5px;

    @media (min-width: 768px) {
      padding: 4px 10px;
    }
    @media (min-width: 1024px) {
      font-size: $midButtonFontSize;
    }
    @media (min-width: 1440px) {
      font-size: $maxButtonFontSize;
    }
  }

  &-item {
    padding: 4px 12px;

    @media (min-width: 768px) {
      padding: 6px;
    }
    @media (min-width: 1024px) {
      padding: 6px 12px;
    }
    @media (min-width: 1440px) {
      padding: 12px 20px;
    }
  }

  &-figure {
    width: 30px;
    margin: 0;

    @media (min-width: 1024px) {
      width: 35px;
    }
    @media (min-width: 1440px) {
      width: 50px;
    }

    &-img {
      width: 100%;
    }
  }
}

// height modifications
@media (max-height: 650px) and (min-width: 768px) {
  //.header-banner-content-text {
  //  display: none;
  //}
  .header-banner-content-figure {
    transform: scale(.9) !important;
  }
}

//@media (max-height: 550px) and (min-width: 768px) {
//  .header-banner-content-figure {
//    display: none;
//  }
//}

@media (min-height: 400px) and (max-height: 900px) and (min-width: 768px) {
  .header-banner-content {
    margin-bottom: 0;
  }
  //.header-banner-container {
  //  padding: 10px 45px;
  //}
  .header-banner-btn.accent {
    margin-bottom: 10px;
  }
  .header-banner-btn,
  .dropdown .header-banner-btn {
    height: 30px;
    font-size: 14px;
    margin-bottom: 5px;
    min-width: 200px;
  }
  .header-banner-content-figure-img {
    max-height: 75px;
  }
  .header-banner-tools-figure {
    width: 25px;
  }
}

@media (min-height: 500px) and (max-height: 900px) and (min-width: 768px) {
  .header-banner-btn,
  .dropdown .header-banner-btn {
    height: 38px;
    font-size: 15px;
    min-width: 220px;
  }
}

@media (min-height: 600px) and (max-height: 900px) and (min-width: 768px) {
  .header-banner-tools-figure {
    width: 35px;
  }
  //.header-banner-content-text {
    //font-size: 12px;
  //}
}

@media (min-height: 699px) and (max-height: 1100px) and (min-width: 768px) {
  .header-banner-content-text {
    //font-size: 20px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  .header-banner-content {
    margin-bottom: 20px;
  }
}

@media (min-height: 900px) and (max-height: 1100px) and (min-width: 1900px) {
  .header-banner-content-figure-img {
    max-height: 110px;
  }
}

// header animation
.animation-container {
  will-change: transform;
  max-width: 500px;

  @media (min-width: 768px) {
    max-width: none;
  }
}

.animation-figure {
  will-change: transform;
  transform-origin: center bottom;
  transition-duration: 1.3s;

  @media (min-width: 768px) {
    transform-origin: left bottom
  }

  img {
    transition: all .5s ease
  }
}

.animation-text {
  will-change: transform;
  transition-duration: 1.1s
}

// visibility
.is-mobile {
  @media (min-width: 768px) {
    display: none !important
  }
}

.is-tablet-up {
  @media (max-width: 767px) {
    display: none !important
  }
}

// page modifications
.header-news,
.header-banner-content-news-title {
  max-width: 1200px;
  margin: 0 auto 26px;
  position: relative;

  .header-banner {
    min-height: auto;
    margin-bottom: 26px;
  }

  .header-banner-figure {
    margin-bottom: 0;
    position: static;

    &-img, &-video {
      height: auto;
      width: 100%;
      object-position: center;
    }
  }

  .header-banner-shadow {
    background: none;
  }

  .header-banner-container {
    padding-top: 26px;
    padding-bottom: 0;

    @media (min-width: 768px){
      padding-left: calc(var(--bs-gutter-x,.75rem) + 11px/2);
      padding-right: calc(var(--bs-gutter-x,.75rem) + 11px/2);
    }
  }

  .header-banner-content {
    margin-bottom: 0;
    padding-bottom: 0;
  }
  .header-banner-content-text {
    max-width: none;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
.news-header-banner-content .bottom {
  position: absolute;
  left: 20px;
  right: 20px;
  bottom: 20px;
  align-items: center;
  text-align: center;

  @media (max-width: 480px) {
    left: 10px;
    right: 10px;
    bottom: 10px;
  }

  .header-banner-content-title {
    line-height: 1.2;
    margin-bottom: 0;

    @media (max-width: 480px) {
      font-size: 18px;
    }
  }
}