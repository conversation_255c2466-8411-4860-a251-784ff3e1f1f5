@import "variables";

// responsible overlay
.responsible-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1100;
  width: 100%;
  height: 100%;
  transition: $tr2;
  overflow-x: hidden;
  overflow-y: auto;
  background-color: rgba(#181717, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;

  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px 40px;
    text-align: center;
    max-width: 80%;
    height: 45%;
    background-color: $modal-content-bg;
    border-radius: 50px;

    @media (min-width: 768px) {
      padding: 40px 70px;
      max-width: 50%;
    }
  }

  &__title {
    font-size: 28px;
    line-height: 1.5;
    max-width: 85%;
    margin: 0 auto 32px;
    color: $secondaryColor;

    @media (min-width: 480px) {
      font-size: 32px;
    }
  }
}

// responsible list
.responsible-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;

  @media (min-width: 360px) {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  @media (max-width: 767px) {
    margin-bottom: 48px;
  }

  &__container {
    @media (min-width: 768px) {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  &__item {
    margin: 0 12px;

    @media (min-width: 280px) {
      min-width: calc(100% / 3 - 14px);
    }
    @media (min-width: 768px) {
      min-width: auto;
      margin: 0 36px 0 0;
    }
  }

  &__button {
    padding: 19px;
    color: white;
    border-radius: 5px;
    background-color: $menuBGColor;
    border: 1px solid white;
    margin: 10px;

    &.enter {
      border: none;
      background-color: $secondaryColor;
      color: $mainDark;
    }
  }
}
