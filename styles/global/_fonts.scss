@import "variables";

@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 400;
  src: url('/assets/fonts/opensans/open-sans-v16-latin-regular.eot'); /* IE9 Compat Modes */
  src: local('Open Sans Regular'), local('OpenSans-Regular'),
  url('/assets/fonts/opensans/open-sans-v16-latin-regular.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */
  url('/assets/fonts/opensans/open-sans-v16-latin-regular.woff2') format('woff2'),
    /* Super Modern Browsers */
  url('/assets/fonts/opensans/open-sans-v16-latin-regular.woff') format('woff'),
    /* Modern Browsers */
  url('/assets/fonts/opensans/open-sans-v16-latin-regular.ttf') format('truetype'),
    /* Safari, Android, iOS */
  url('/assets/fonts/opensans/open-sans-v16-latin-regular.svg#OpenSans') format('svg'); /* Legacy iOS */
  font-display: swap;
}

@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 700;
  src: url('/assets/fonts/opensans/open-sans-v16-latin-700.eot'); /* IE9 Compat Modes */
  src: local('Open Sans Bold'), local('OpenSans-Bold'),
  url('/assets/fonts/opensans/open-sans-v16-latin-700.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */
  url('/assets/fonts/opensans/open-sans-v16-latin-700.woff2') format('woff2'),
    /* Super Modern Browsers */
  url('/assets/fonts/opensans/open-sans-v16-latin-700.woff') format('woff'),
    /* Modern Browsers */
  url('/assets/fonts/opensans/open-sans-v16-latin-700.ttf') format('truetype'),
    /* Safari, Android, iOS */
  url('/assets/fonts/opensans/open-sans-v16-latin-700.svg#OpenSans') format('svg'); /* Legacy iOS */
  font-display: swap;
}

@font-face {
  font-family: 'Open Sans';
  font-style: normal;
  font-weight: 800;
  src: url('/assets/fonts/opensans/open-sans-v16-latin-800.eot'); /* IE9 Compat Modes */
  src: local('Open Sans ExtraBold'), local('OpenSans-ExtraBold'),
  url('/assets/fonts/opensans/open-sans-v16-latin-800.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */
  url('/assets/fonts/opensans/open-sans-v16-latin-800.woff2') format('woff2'),
    /* Super Modern Browsers */
  url('/assets/fonts/opensans/open-sans-v16-latin-800.woff') format('woff'),
    /* Modern Browsers */
  url('/assets/fonts/opensans/open-sans-v16-latin-800.ttf') format('truetype'),
    /* Safari, Android, iOS */
  url('/assets/fonts/opensans/open-sans-v16-latin-800.svg#OpenSans') format('svg'); /* Legacy iOS */
  font-display: swap;
}

@font-face {
  font-family: 'Oswald';
  font-style: normal;
  font-weight: 400;
  src: url('/assets/fonts/oswald/oswald-v17-latin-regular.eot'); /* IE9 Compat Modes */
  src: local('Oswald Regular'), local('Oswald-Regular'),
  url('/assets/fonts/oswald/oswald-v17-latin-regular.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */
  url('/assets/fonts/oswald/oswald-v17-latin-regular.woff2') format('woff2'),
    /* Super Modern Browsers */
  url('/assets/fonts/oswald/oswald-v17-latin-regular.woff') format('woff'),
    /* Modern Browsers */
  url('/assets/fonts/oswald/oswald-v17-latin-regular.ttf') format('truetype'),
    /* Safari, Android, iOS */
  url('/assets/fonts/oswald/oswald-v17-latin-regular.svg#Oswald') format('svg'); /* Legacy iOS */
  font-display: swap;
}

@font-face {
  font-family: 'Roboto';
  src: local('Roboto Regular'), url('/assets/fonts/roboto/Roboto-Regular.ttf') format('truetype');
}

@font-face {
  font-family: 'Roboto Black';
  src: local('Roboto Black'), url('/assets/fonts/roboto/Roboto-Black.ttf') format('truetype');
}
