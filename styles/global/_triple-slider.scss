.triple-slider {
  width: 100%;
  position: relative;
  overflow: hidden;
  perspective: 1200px;
  padding: 32px 0;

  & .triple-slider-main {
    position: relative;
    z-index: 10;
    box-shadow: 0 0 30px #00000080
  }

  & .triple-slider-prev, & .triple-slider-next {
    opacity: .25;
    position: absolute;
    top: 50%;
    user-select: none;
    cursor: pointer
  }

  & .triple-slider-prev {
    right: 50%;
    transform: translateY(-50%) scale(.75) rotateY(10deg)
  }

  & .triple-slider-next {
    left: 50%;
    transform: translateY(-50%) scale(.75) rotateY(-10deg)
  }

  & .swiper {
    width: 90%;
    max-width: 86vw;
    height: 60vh;
    border-radius: 8px;

    & .swiper-slide {
      border-radius: 8px;
      background: $mainDark;
      // max-width: calc(100% - 48px);
      transform-style: preserve-3d;

      & img {
        width: 100%;
        height: 100%;
        display: block;
        object-fit: cover;
        border-radius: 8px;
        /*-webkit-box-reflect: below 5px -webkit-linear-gradient(bottom, rgba(255, 0, 0, .1) 0%, transparent 32px, transparent 100%)*/
      }

      & .slide-content {
        position: absolute;
        left: 0;
        width: 100%;
        bottom: 0;
        padding: 88px 16px 24px;
        box-sizing: border-box;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, .75));
        border-radius: 0 0 8px 8px
      }

      & .slide-content h2 {
        margin: 0;
        font-weight: bold;
        font-size: 24px;
        line-height: 1.1
      }

      & .slide-content p {
        margin: 8px 0 0;
        opacity: .65;
        font-size: 14px;
        font-weight: 500;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden
      }
    }
  }
}