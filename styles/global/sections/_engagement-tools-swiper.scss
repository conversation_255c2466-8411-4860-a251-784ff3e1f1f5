@import "../variables";
// engagement swiper multi
.engagement-multi-swiper {
  overflow: visible !important;

  .device-desktop & {
    transition: transform 0.2s;

    &:hover {
      .swiper-slide {
        opacity: .5;
      }
    }
  }

  .swiper-slide {
    text-align: center;
    padding: 0 20px;
    transition: transform 0.2s;

    @media (min-width: 600px) {
      width: calc(100% / 2);
    }
    @media (min-width: 1024px) {
      width: calc(100% / 3);
      padding: 0 35px 35px;
    }

    .device-desktop & {
      &:hover {
        cursor: pointer;
        z-index: 3;
        opacity: 1;
        transform: scale(1.1);
      }
    }
  }

  .swiper-slide-title {
    color: $textColor;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    margin-bottom: 16px;
    text-align: center;
    font-weight: bold;
    font-size: $minSectionTitleFontSize;

    @media (min-width: 768px) {
      font-size: $midSectionTitleFontSize;
      margin-bottom: 35px;
    }
    @media (min-width: 1200px) {
      font-size: $maxSectionTitleFontSize;
    }
  }

  .swiper-slide-figure {
    display: block;
    position: relative;

    &-img {
      width: 100%;
      border-radius: 18px;
    }
  }

  &-btns {
    //padding: 0 24px;
    position: relative;

    //@media (min-width: 1440px) {
    //  padding: 0 54px;
    //}

    .swiper-button-prev,
    .swiper-button-next {
      cursor: pointer;
      position: absolute;
      top: 50%;
      height: 24px;
      width: 24px;
      transform: translateY(-50%);
      z-index: 10;
      background: no-repeat 50% 50%;
      background-size: auto 100%;
      background-image: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI1LjQuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHZpZXdCb3g9IjAgMCA0My45NSA3My45NSIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNDMuOTUgNzMuOTU7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPHN0eWxlIHR5cGU9InRleHQvY3NzIj4KCS5zdDB7ZmlsbDojRkZGRkZGO30KPC9zdHlsZT4KPGc+Cgk8cGF0aCBjbGFzcz0ic3QwIiBkPSJNMzguODUsNDIuMDdjLTEuMjYsMC0yLjUyLTAuNDYtMy41LTEuMzlMMS42LDguODFDLTAuNDUsNi44Ny0wLjU0LDMuNjUsMS4zOSwxLjYKCQlDMy4zMy0wLjQ1LDYuNTUtMC41NCw4LjYsMS4zOWwzMy43NSwzMS44N2MyLjA1LDEuOTMsMi4xNCw1LjE2LDAuMjEsNy4yMUM0MS41Niw0MS41NCw0MC4yMSw0Mi4wNywzOC44NSw0Mi4wN3oiLz4KPC9nPgo8Zz4KCTxwYXRoIGNsYXNzPSJzdDAiIGQ9Ik01LjEsNzMuOTVjLTEuMzUsMC0yLjcxLTAuNTQtMy43MS0xLjZjLTEuOTMtMi4wNS0xLjg0LTUuMjgsMC4yMS03LjIxbDMzLjc1LTMxLjg3CgkJYzIuMDUtMS45Myw1LjI4LTEuODQsNy4yMSwwLjIxYzEuOTMsMi4wNSwxLjg0LDUuMjgtMC4yMSw3LjIxTDguNiw3Mi41NkM3LjYyLDczLjQ5LDYuMzYsNzMuOTUsNS4xLDczLjk1eiIvPgo8L2c+Cjwvc3ZnPgo=');
      margin-top: 21px;

      @media (min-width: 1900px) {
        margin-top: 46px;
      }

      &:after {
        display: none;
      }

      @media (min-width: 1440px) {
        height: 52px;
        width: 52px;
      }

      &.swiper-button-prev {
        left: -10px;
        transform: translateY(-50%) scale(-1, -1);

        @media (min-width: 1024px) {
          left: 0;
        }
      }

      &.swiper-button-next {
        right: -10px;

        @media (min-width: 1024px) {
          right: 0;
        }
      }
    }
  }
}
