.news-article {
  font-family: $fontOpenSans;
  //line-height: 1.2;
  max-width: 824px;
  margin-right: auto;
  margin-left: auto;
  position: relative;

  &__headline {
    color: $white;
    display: flex;
    align-items: center;
    line-height: 1;
    margin-bottom: 20px;
    font-size: 14px;

    @media (min-width: 768px) {
      font-size: 16px;
    }
    @media (min-width: 1200px) {
      font-size: 20px;
    }
  }

  &__divider {
    display: inline-flex;
    margin: 0 12px;
    background-color: $secondaryColor;
    width: 1px;
    align-self: stretch;
    flex-shrink: 0;
  }

  &__label {
    text-transform: uppercase;
    color: $secondaryColor;
  }

  &__title {
    //line-height: 1.2;
    position: relative;
    color: $secondaryColor;
    margin-bottom: 10px;
    font-weight: bold;
    font-size: $minTitleFontSize;

    @media (min-width: 768px) {
      font-size: $midTitleFontSize;
    }
    @media (min-width: 1200px) {
      font-size: $maxTitleFontSize;
    }
  }

  &__desc {
    text-align: justify;
    margin-bottom: 26px;
    font-size: $minTextFontSize;

    @media (min-width: 768px) {
      font-size: $midTextFontSize;
    }
    @media (min-width: 1200px) {
      font-size: $maxTextFontSize;
    }

    img {
      max-width: 100%;
    }

    p {
      margin-bottom: 26px;
    }

    a {
      color: $secondaryColor;
      background-image: linear-gradient(rgba($mainDark, 0) calc(99% - 1px), $secondaryColor 1px);
      background-repeat: no-repeat;
      background-size: 0 100%;
      transition: background-size .5s;

      &:hover {
        text-decoration: none;
        background-size: 100% 100%;
      }
    }
  }

  &__link {
    cursor: pointer;
  }

  &__pagination {
    position: relative;
    margin-bottom: 26px;
    display: flex;
    justify-content: space-between;

    &-label {
      margin: 0 10px;
    }

    &-label,
    &-btn-prev:before,
    &-btn-next:after {
      font-size: $minTextFontSize;

      @media (min-width: 768px) {
        font-size: $midTextFontSize
      }
      @media (min-width: 1200px) {
        font-size: $maxTextFontSize
      }
    }

    &-btn-prev,
    &-btn-next {
      color: #fff;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      &:hover {
        color: rgba(#fff,.9);
      }
    }
    &-btn-prev:before,
    &-btn-next:after {
      content: 'prev';
      color: #fff;
      font-family: swiper-icons;
      text-transform: none !important;
      letter-spacing: 0;
      font-variant: initial;
      line-height: 1;
    }
    &-btn-next:after {
      content: 'next';
    }
  }
}
