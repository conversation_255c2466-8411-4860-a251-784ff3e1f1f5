@import "../variables";
// @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap');

// slider
.features-slider {
  position: relative;
  padding: 0 20px;

  .device-desktop & {
    overflow: visible !important;
    transition: transform 0.2s;

    &:hover {
      .features-slide {
        opacity: .5;
      }
    }
  }

  .features-slide {
    padding: 20px 35px;
    max-width: 428px;
    transition: transform 0.2s;

    @media (min-width: 600px) {
      width: calc(100%/2);
      padding: 20px;
    }
    @media (min-width: 1024px) {
      width: calc(100%/3);
      padding: 20px;
    }

    .device-desktop & {
      &:hover {
        cursor: pointer;
        z-index: 3;
        opacity: 1;
        transform: scale(1.1);
      }
    }
  }
  &-title {
    font-size: $minSectionTitleFontSize;
    font-family: $fontRoboto;
    color: #fff;//$secondaryColor;
    font-weight: bold;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    margin-bottom: 16px;
    text-align: center;

    @media (min-width: 768px) {
      font-size: $midSectionTitleFontSize;
    }
    @media (min-width: 1200px) {
      font-size: $maxSectionTitleFontSize;
    }
  }
  &-text {
    font-size: $fontSize;
    font-family: $fontRoboto;

    @media (min-width: 768px) {
      font-size: $midTextFontSize;
    }
    @media (min-width: 1200px) {
      font-size: $maxTextFontSize;
    }
  }
  &-figure {
    transition: transform 0.2s;
    display: block;
    margin: 0 0 16px;
    padding-bottom: 62.54%;
    position: relative;

    &-img {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      top: 0;
      height: 100%;
      width: 100%;
      object-fit: cover;
      object-position: center;
      border-radius: 18px;
    }
  }
  &-nav {
    display: flex;
    justify-content: space-between;
  }
  &-nav-btn,
  .swiper-button-prev,
  .swiper-button-next {
    cursor: pointer;
    position: absolute;
    top: 50%;
    height: 24px;
    width: 24px;
    transform: translateY(-50%);
    z-index: 10;
    background: no-repeat 50% 50%;
    background-size: auto 100%;
    background-image: $arrow_right_white;

    &:after {
      display: none;
    }
    @media (min-width: 1440px) {
      height: 52px;
      width: 52px;
    }

    &.swiper-button-prev {
      left: 0;
      transform: translateY(-50%) scale(-1,-1);
    }
    &.swiper-button-next {
      right: 0;
    }
  }
}
