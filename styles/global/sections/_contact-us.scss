@import "../variables";

.section-contact-us {
  position: relative;
  display: block;
  width: 100%;

  & .wrap {
    display: flex;
    flex-direction: row;
    max-width: $sectionInfoMaxWidth;
    margin: 0 auto;

    & .sw-products {
      display: flex;
      flex-direction: column;
      justify-content: stretch;
      margin: 0 20px 56px 0;
      flex: 0.5;

      &-title {
        display: block;
        width: 100%;
        color: $secondaryColor;
        text-align: left;
        font-size: $minTitleFontSize;

        @media (min-width: 768px) {
          font-size: $midTitleFontSize;
        }
        @media (min-width: 1200px) {
          font-size: $maxTitleFontSize;
        }
      }

      &-description {
        flex-shrink: 0;
        display: block;
        width: 100%;
        color: $textColor;
        text-align: left;
        margin: 1em auto;
        font-size: $minTextFontSize;

        @media (min-width: 768px) {
          font-size: $midTextFontSize;
        }
        @media (min-width: 1200px) {
          font-size: $maxTextFontSize;
        }
      }

      &-items {
        flex-grow: 1;
        align-items: center;
        display: grid;
        grid-template-columns: repeat(3,1fr);
        grid-template-rows: auto auto;
        column-gap: 3px;
        row-gap: 3px;

        & > div, & > a {
          position: relative;
          margin: 6px;
          align-items: center;
          justify-content: flex-start;
          display: flex;
          flex-direction: column;
          flex-wrap: wrap;

          & img {
            width: 60px;
            height: 60px;
            object-fit: contain;
            object-position: center;
          }

          & .item-title {
            font-size: $fontSize;
            white-space: normal;
            text-align: center;
            color: #fff;
            padding: 10px 6px;

            @media (min-width: 768px) {
              font-size: $minTextFontSize;
            }
            @media (min-width: 1440px) {
              font-size: $midTextFontSize;
            }
          }
        }

        a:hover .item-title {
          color: #989898;
        }
      }
    }

    & .sw-contact-us {
      display: flex;
      flex-direction: column;
      margin: 0 auto;
      margin-left: 20px;
      flex: 0.5;
      width: 100%;

      .device-mobile & {
        max-width: 320px;
      }
      .device-tablet & {
        max-width: 100%;
      }

      &-title {
        display: block;
        width: 100%;
        font-size: $midTitleFontSize;
        color: $secondaryColor;
        text-align: left;

        @media (min-width: 768px) {
          font-size: $midTitleFontSize;
        }
        @media (min-width: 1200px) {
          font-size: $maxTitleFontSize;
        }
      }

      &-description {
        display: block;
        width: 100%;
        font-size: $minTextFontSize;
        color: $textColor;
        text-align: left;
        margin: 1em auto;

        @media (min-width: 768px) {
          font-size: $midTextFontSize;
        }
        @media (min-width: 1200px) {
          font-size: $maxTextFontSize;
        }
      }

      &-form {
        .card-body {
          padding: 0;
        }

        .btn {
          width: 100%;
          max-width: 320px;
          margin-left: auto;
          margin-right: auto;
          display: block;
        }
        .form-label {
          color: #b3b3b3;
          font-size: $minTextFontSize;

          @media (min-width: 768px) {
            font-size: $midTextFontSize;
          }
          @media (min-width: 1200px) {
            font-size: $maxTextFontSize;
          }
        }
      }
    }

    @media (max-width: 976px) {
      flex-direction: column;

      & .sw-products {
        margin: 0;
      }

      & .sw-contact-us {
        margin: 0 auto;
      }
    }
  }
}
