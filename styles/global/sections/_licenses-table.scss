@import "../variables";
$secondaryColor: #b3b3b3;

.t-response {
  overflow: hidden;

  @media (max-width: 1100px) {
    overflow-x: inherit;
    overflow-y: hidden;
    margin-right: -15px;

    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      width: 0;
      background: transparent;
    }
  }

  //@media (max-width: 480px), (max-width: 900px) and (orientation: landscape) {
  //  margin-left: -15px;
  //}
}

.table-licenses {
  //margin-top: 7.813vh;
  //margin-bottom: 15.625vh;
  //font-family: $fontRoboto;
  width: 100%;
  border-collapse: collapse;

  //@media (min-width: 1921px), (min-height: 1081px) {
  //  margin-top: 150px;
  //  margin-bottom: 300px;
  //}

  @media (max-width: 1100px) {
    table,
    thead,
    tbody,
    th,
    td,
    tr {
      display: block;
    }
  }

  th,
  tr {
    text-align: left;
    font-size: $minTextFontSize;

    @media (min-width: 768px) {
      font-size: $midTextFontSize;
    }
    @media (min-width: 1200px) {
      font-size: $maxTextFontSize;
    }

  }

  th {
    white-space: pre-line;
  }

  &__headline {
    tr {
      @media (max-width: 1100px) {
        position: absolute;
        top: -9999px;
        left: -9999px;
      }
    }

    th {
      padding: 1em 3.7vh 0.2em;
      //font-family: $fontRobotoBlack;
      color: $secondaryColor;
      text-transform: uppercase;
      background-color: transparent;

      font-size: $minTextFontSize;

      @media (min-width: 768px) {
        font-size: $midTextFontSize;
      }
      @media (min-width: 1200px) {
        font-size: $maxTextFontSize;
      }


      @media (min-width: 1921px), (min-height: 1081px) {
        padding: 20px 40px;
      }
    }
  }

  &__body {
    tr {
      &:nth-child(even) {
        background-color: $mainDark;

        @media (max-width: 1100px) {
          td {
            border-bottom: 1px solid #292929;
          }
        }
      }

      &:nth-child(odd) {
        background-color: rgba($secondaryColor, 0.1);

        @media (max-width: 1100px) {
          td {
            border-bottom: 1px solid $mainDark;

            &::before {
              border-right: 1px solid $mainDark;
            }
          }
        }
      }

      & td:nth-child(3) {
        white-space: nowrap;
      }
    }

    td {
      font-weight: 400;
      padding: 0.6em 1.8vh;

      @media (max-width: 1024px) {
        padding: 9px 25px;
      }

      @media (min-width: 1921px), (min-height: 1081px) {
        padding: 14px 40px;
      }

      @media (max-width: 1100px) {
        padding-left: 35%;
        position: relative;

        &::before {
          position: absolute;
          top: 0;
          left: 0;
          padding-top: 7px;
          padding-left: 10px;
          padding-right: 10px;
          width: 30%;
          height: 100%;
          border-right: 1px solid #292929;
          white-space: nowrap;
          color: #fff;

          @media (max-width: 480px), (max-width: 812px) and (orientation: landscape) {
            padding-top: 9px;
          }

          @media (max-width: 374px) {
            width: 35%;
          }
        }

        &:nth-of-type(1):before {
          content: "JURISDICTION";
        }

        &:nth-of-type(2):before {
          content: "REGULATOR";
        }

        &:nth-of-type(3):before {
          content: "LICENCE NO.";
        }

        &:nth-of-type(4):before {
          content: "";
        }
      }

      @media (max-width: 374px) {
        padding-left: 38%;
      }
    }
  }

  &__link {
    &, & a {
      color: $secondaryColor;
      transition: $tr2;

      a:hover {
        opacity: 0.8;
      }
    }
  }

  &__img {
    width: 20%;

    @media (max-width: 1100px) {
      width: 100%;
    }

    img {
      max-height: 3.7vh;
      max-width: initial;

      @media (min-width: 1921px) {
        max-height: 40px;
      }
    }
  }

  @media (max-width: 1100px) {
    display: none;
  }
}

.table-licenses-mobile {
  //font-family: $fontRoboto;
  //margin-top: 7.813vh;
  //margin-bottom: 15.625vh;
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;

  & tbody {
    //display: flex;
    //flex-direction: column;
  }

  @media (min-width: 1921px), (min-height: 1081px) {
    margin-top: 150px;
    margin-bottom: 300px;
  }

  //@media (max-width: 1100px) {
  //  margin-bottom: 20px;
  //}

  @media (min-width: 1101px) {
    display: none;
  }

  @media (max-width: 1100px) {
    white-space: nowrap;
  }

  tr {
    &:nth-child(3) {
      white-space: normal;
    }

    &:nth-child(even) {
      div {
        background-color: rgba($secondaryColor, 0.1);
      }
    }
  }

  td {
    position: relative;
    width: 390px;
    line-height: 1.2em;
    text-align: center;
    min-height: 80px;

    @media (max-width: 480px), (max-width: 900px) and (orientation: landscape) {
      width: 330px;
      min-height: 70px;
    }

    font-size: $minTextFontSize;

    @media (min-width: 768px) {
      font-size: $midTextFontSize;
    }
    @media (min-width: 1200px) {
      font-size: $maxTextFontSize;
    }

    div {
      min-height: 80px;
      height: 100%;
      display: flex;
      align-items: flex-end;
      justify-content: center;
      margin: 0 15px;
      padding-top: 46px;
      padding-bottom: 14px;

      @media (max-width: 480px), (max-width: 900px) and (orientation: landscape) {
        min-height: 70px;
      }
    }

    img {
      pointer-events: none;
      max-height: 50px;
      max-width: 80%;
      display: block;
      margin: auto;
    }

    &.table-data-label {
      position: relative;
      padding-top: 4px;
      padding-bottom: 4px;

      &::after {
        position: absolute;
        left: 50%;
        top: 10px;
        content: attr(data-label);
        line-height: 1.4;
        font-weight: 700;
        color: rgba($secondaryColor, 0.8);
        text-transform: uppercase;
        transform: translateX(-50%);

        font-size: $minTextFontSize;

        @media (min-width: 768px) {
          font-size: $midTextFontSize;
        }
        @media (min-width: 1200px) {
          font-size: $maxTextFontSize;
        }

      }
    }
  }
}
