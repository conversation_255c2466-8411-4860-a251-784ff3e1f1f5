@-webkit-keyframes visibleOn {
  0% {
    opacity: 1;
  }
  100% {
    visibility: hidden;
    opacity: 0;
  }
}

@keyframes visibleOn {
  0% {
    opacity: 1;
  }
  100% {
    visibility: hidden;
    opacity: 0;
  }
}

#licences {
  margin-bottom: 10vh;
}

@media (min-width: 1921px), (min-height: 1081px) {
  #licences {
    margin-bottom: 50px;
  }
}

@media (max-width: 1024px) {
  #licences {
    margin-bottom: 0;
  }
}

@media (max-width: 480px) {
  #licences {
    background-image: url("/assets/site/images/home/<USER>");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center left;
  }
}

@media (max-width: 400px) {
  #licences {
    background-position-x: 65%;
  }
}

table#licenses_animation {
  border-collapse: collapse;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: justify;
  justify-content: space-between;
  width: 100%;
  height: 85vh;
  background-image: url("/assets/site/images/home/<USER>");
  background-repeat: no-repeat;
  background-position: 30% center;
  background-size: contain;
  margin-left: 3.646vw;
}

@media (min-width: 1921px), (min-height: 1081px) {
  table#licenses_animation {
    margin-left: 70px;
    height: 60vh;
    max-height: 900px;
  }
}

@media (max-width: 1919px) {
  table#licenses_animation {
    background-position: 30% center;
  }
}

@media (max-width: 1365px) {
  table#licenses_animation {
    transform: scale(0.8);
  }
}

@media (max-width: 1024px) {
  table#licenses_animation {
    background-position: -50% center;
    height: auto;
    margin-left: 0;
  }
}

@media (max-width: 768px) {
  table#licenses_animation {
    background-position: 50% 50%;
  }
}

@media (max-width: 812px) and (orientation: landscape) {
  table#licenses_animation {
    height: 100vh;
    transform: scale(0.8) translateY(-40px);
  }
}

@media (max-width: 667px) and (orientation: landscape) {
  table#licenses_animation {
    transform: scale(0.7) translateY(-70px) translateX(-40px);
  }
}

@media (max-width: 480px) {
  table#licenses_animation {
    transform: scale(1);
    height: 90vh;
    -ms-flex-direction: column;
    flex-direction: column;
    background: none;
    min-height: 650px;
  }
}

table#licenses_animation tr {
  vertical-align: middle;
  width: 100%;
  position: relative;
}

table#licenses_animation tr td {
  position: relative;
}

table#licenses_animation thead {
  width: calc(80% / 2);
  flex-shrink: 0;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-left: 3%;
  padding-top: 9.26vh;
}

@media (min-width: 1921px), (min-height: 1081px) {
  table#licenses_animation thead {
    padding-top: 100px;
  }
}

@media (max-width: 1365px) {
  table#licenses_animation thead {
    padding-left: 0;
  }
}

@media (max-width: 768px) {
  table#licenses_animation thead {
    width: 45%;
  }
}

@media (max-width: 480px) {
  table#licenses_animation thead {
    width: 100%;
    padding-top: 40%;
    -webkit-animation: visibleOn 0.5s linear 4s both;
    animation: visibleOn 0.5s linear 4s both;
  }
}

@media (max-width: 812px) and (orientation: landscape) {
  table#licenses_animation thead {
    padding-top: 0;
  }
}

table#licenses_animation thead tr {
  height: 100px;
}

@media (max-width: 480px) {
  table#licenses_animation thead tr {
    height: 80px;
  }
}

table#licenses_animation thead tr:nth-child(1) {
  padding-left: 40%;
}

@media (max-width: 1100px) {
  table#licenses_animation thead tr:nth-child(1) {
    padding-left: 0;
  }
}

@media (max-width: 480px) {
  table#licenses_animation thead tr:nth-child(1) {
    padding-left: 15%;
  }
}

table#licenses_animation thead tr:nth-child(2) {
  padding-left: 15%;
}

@media (max-width: 1100px) {
  table#licenses_animation thead tr:nth-child(2) {
    padding-left: 0;
  }
}

@media (max-width: 480px) {
  table#licenses_animation thead tr:nth-child(2) {
    padding-left: 30%;
  }
}

table#licenses_animation thead tr:nth-child(3) {
  padding-left: 5%;
}

@media (max-width: 480px) {
  table#licenses_animation thead tr:nth-child(3) {
    padding-left: 37%;
  }
}

table#licenses_animation thead tr:nth-child(4) {
  padding-left: 5%;
}

@media (max-width: 1100px) {
  table#licenses_animation thead tr:nth-child(4) {
    padding-left: 0;
  }
}

@media (max-width: 480px) {
  table#licenses_animation thead tr:nth-child(4) {
    padding-left: 25%;
  }
}

table#licenses_animation thead tr:nth-child(5) {
  padding-left: 20%;
}

@media (max-width: 1100px) {
  table#licenses_animation thead tr:nth-child(5) {
    padding-left: 0;
  }
}

@media (max-width: 480px) {
  table#licenses_animation thead tr:nth-child(5) {
    padding-left: 22%;
  }
}

table#licenses_animation thead tr:nth-child(6) {
  padding-left: 35%;
}

@media (max-width: 1100px) {
  table#licenses_animation thead tr:nth-child(6) {
    padding-left: 0;
  }
}

@media (max-width: 480px) {
  table#licenses_animation thead tr:nth-child(6) {
    padding-left: 21%;
  }
}

table#licenses_animation tbody {
  width: 20%;
  flex-shrink: 0;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
  justify-content: center;
  padding-top: 25px;
}

@media (max-width: 768px) {
  table#licenses_animation tbody {
    width: 30%;
  }
}

@media (max-width: 480px) {
  table#licenses_animation tbody {
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
}

@media (max-width: 812px) and (orientation: landscape) {
  table#licenses_animation tbody {
    width: 30%;
    padding-top: 0;
  }
}

@media (max-width: 667px) and (orientation: landscape) {
  table#licenses_animation tbody {
    width: 40%;
  }
}

table#licenses_animation tfoot {
  display: -ms-flexbox;
  display: flex;
  width: calc(80% / 2);
  flex-shrink: 0;
  padding-left: 5%;
  padding-top: 13.88vh;
}

@media (min-width: 1921px), (min-height: 1081px) {
  table#licenses_animation tfoot {
    padding-top: 150px;
  }
}

@media (max-width: 768px) {
  table#licenses_animation tfoot {
    padding-left: 0;
  }
}

@media (max-width: 480px) {
  table#licenses_animation tfoot {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding-top: 50%;
    padding-left: 35%;
  }
}

table#licenses_animation ul {
  padding: 0;
  margin: 0;
  font-size: 1.25em;
  color: #9b9b9b;
}

table#licenses_animation ul li {
  list-style: none;
}


.anim1 {
  width: 260px;
  height: 80px;
  -webkit-animation: sprite_3_gc 1.13s steps(34) 0.5s both;
  animation: sprite_3_gc 1.13s steps(34) 0.5s both;
}

.anim2 {
  width: 260px;
  height: 80px;
  -webkit-animation: sprite_2_mga 1.06s steps(32) 1s both;
  animation: sprite_2_mga 1.06s steps(32) 1s both;
}

.anim3 {
  width: 170px;
  height: 60px;
  -webkit-animation: sprite_5_alger 0.43s steps(13) 1.5s both;
  animation: sprite_5_alger 0.43s steps(13) 1.5s both;
}

.anim4 {
  width: 320px;
  height: 100px;
  -webkit-animation: sprite_1_hm 0.8s steps(24) 2s both;
  animation: sprite_1_hm 0.8s steps(24) 2s both;
}

.anim5 {
  width: 260px;
  height: 80px;
  -webkit-animation: sprite_4_js 1s steps(30) 2.5s both;
  animation: sprite_4_js 1s steps(30) 2.5s both;
}

.anim6 {
  width: 63px;
  height: 63px;
  -webkit-animation: sprite_6_onjn 0.3s linear 3s both;
  animation: sprite_6_onjn 0.3s linear 3s both;
}

@media (max-width: 480px) {
  .anim1,
  .anim2,
  .anim3,
  .anim4,
  .anim5,
  .anim6 {
    transform: scale(0.7);
  }
}

.animTextRotate {
  width: 280px;
  max-width: 100%;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

@media (max-width: 767px) {
  .animTextRotate {
    margin: auto;
  }
}

@media (max-width: 480px) {
  .animTextRotate {
    transform: scale(0.8);
  }
}

.anim7 {
  position: relative;
  height: 150px;
  width: 50%;
  flex-shrink: 0;
}

.anim7 span {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: center;
  justify-content: center;
  color: #f9bc22;
  position: absolute;
  left: 10px;
  top: -30px;
  font-size: 150px;
  width: 90%;
  font-weight: 900;
}

@-webkit-keyframes num_visible {
  100% {
    visibility: visible;
  }
}

@keyframes num_visible {
  100% {
    visibility: visible;
  }
}

@-webkit-keyframes num_hidden {
  100% {
    visibility: hidden;
  }
}

@keyframes num_hidden {
  100% {
    visibility: hidden;
  }
}

@-webkit-keyframes rotate-bottom {
  100% {
    transform: rotate(45deg);
    transform-origin: 0 0;
    opacity: 0;
  }
  0% {
    transform: rotate(0deg);
    transform-origin: 0 0;
    opacity: 1;
  }
}

@keyframes rotate-bottom {
  100% {
    transform: rotate(45deg);
    transform-origin: 0 0;
    opacity: 0;
  }
  0% {
    transform: rotate(0deg);
    transform-origin: 0 0;
    opacity: 1;
  }
}

@-webkit-keyframes invert-rotate-bottom {
  100% {
    transform: rotate(0deg);
    transform-origin: 0 center;
    opacity: 1;
  }
  0% {
    transform: rotate(-45deg);
    transform-origin: 0 center;
    opacity: 0;
  }
}

@keyframes invert-rotate-bottom {
  100% {
    transform: rotate(0deg);
    transform-origin: 0 center;
    opacity: 1;
  }
  0% {
    transform: rotate(-45deg);
    transform-origin: 0 center;
    opacity: 0;
  }
}

.anim7-1 {
  -webkit-animation: num_hidden 0.5s linear 3.5s both;
  animation: num_hidden 0.5s linear 3.5s both;
}

.anim7-2 {
  -webkit-animation: num_visible 0.5s linear 4s both;
  animation: num_visible 0.5s linear 4s both;
  visibility: hidden;
}


.anim8 {
  -webkit-animation: rotate-bottom 0.5s linear 3.7s both;
  animation: rotate-bottom 0.5s linear 3.7s both;
}

.anim9 {
  -webkit-animation: invert-rotate-bottom 0.5s linear 4s both;
  animation: invert-rotate-bottom 0.5s linear 4s both;
}

.anim10 {
  -webkit-animation: fade-in-top 0.7s cubic-bezier(0.39, 0.575, 0.565, 1) both;
  animation: fade-in-top 0.7s cubic-bezier(0.39, 0.575, 0.565, 1) both;
}



.certif {
  width: 50%;
  position: absolute;
  right: 0;
  color: #f9bc22;
  font-size: 1.5em;
  flex-shrink: 0;
}

.ft1,
.ft2,
.ft3,
.ft4,
.ft5,
.ft6,
.ft7,
.ft8,
.ft9 {
  opacity: 0;
  padding: 0.5em 0;
}

.ft1 {
  -webkit-animation-delay: 4.5s;
  animation-delay: 4.5s;
}

.ft2 {
  -webkit-animation-delay: 4.7s;
  animation-delay: 4.7s;
  padding-left: 2.2em;
}
@media (max-width: 480px) {
  .ft2 {
    padding-left: 1.5em;
  }
}

.ft3 {
  -webkit-animation-delay: 4.9s;
  animation-delay: 4.9s;
  padding-left: 4.3em;
}
@media (max-width: 480px) {
  .ft3 {
    padding-left: 2.5em;
  }
}

.ft4 {
  -webkit-animation-delay: 5.1s;
  animation-delay: 5.1s;
  padding-left: 5.5em;
}
@media (max-width: 480px) {
  .ft4 {
    padding-left: 3.5em;
  }
}

.ft5 {
  -webkit-animation-delay: 5.3s;
  animation-delay: 5.3s;
  padding-left: 6.5em;
}
@media (max-width: 480px) {
  .ft5 {
    padding-left: 3.5em;
  }
}

.ft6 {
  -webkit-animation-delay: 5.5s;
  animation-delay: 5.5s;
  padding-left: 7em;
}
@media (max-width: 480px) {
  .ft6 {
    padding-left: 3.5em;
  }
}

.ft7 {
  -webkit-animation-delay: 5.7s;
  animation-delay: 5.7s;
  padding-left: 7.2em;
}
@media (max-width: 480px) {
  .ft7 {
    padding-left: 2.5em;
  }
}

.ft8 {
  -webkit-animation-delay: 5.9s;
  animation-delay: 5.9s;
  padding-left: 7.3em;
}
@media (max-width: 480px) {
  .ft8 {
    padding-left: 1.5em;
  }
}

.ft9 {
  -webkit-animation-delay: 6.2s;
  animation-delay: 6.2s;
  padding-left: 7.4em;
}
@media (max-width: 480px) {
  .ft9 {
    padding-left: 1em;
  }
}
