@import "../variables";

.lc-top-wrap {
  position: relative;
  z-index: 2;
  //max-width: 1440px;
  width: 100%;
  display: flex;
  margin-left: auto;
  margin-right: auto;
  min-height: 200px;
  //margin-bottom: 25vh
  margin-bottom: 80px;

  @media (max-width: 800px), (max-width: 900px) and (orientation: landscape) {
    flex-direction: column;
  }
}

.lc-top-wrap__inner {
  flex-shrink: 0;

  &:nth-child(1) {
    width: 33%;

    @media (max-width: 800px), (max-width: 900px) and (orientation: landscape) {
      width: 100%
    }
  }
  &:nth-child(2) {
    width: calc(100% - 33%);

    @media (max-width: 800px), (max-width: 900px) and (orientation: landscape) {
      width: 100%;
      padding-left: 15px;
      //margin-top: 20px
    }
  }
}

.lc-top-text {
  //padding-left: 15px;
  width: 100%;
  padding-right: 2em;

  @media (max-width: 1366px) {
    padding-right: 4em
  }
  @media (max-width: 800px) {
    padding-right: 1em;
    max-width: 80%
  }
  @media (max-width: 480px) {
    max-width: 95%
  }
}

.lc-top-text__title {
  //font-family: $fontRoboto;
  line-height: 1;
  font-weight: 400;
  letter-spacing: normal;
  text-transform: capitalize;
  margin-bottom: 16px;
  text-align: left;
  font-size: $minTitleFontSize;

  @media (min-width: 768px) {
    font-size: $midTitleFontSize;
    margin-bottom: 1.823vw;
  }
  @media (min-width: 1200px) {
    font-size: $maxTitleFontSize;
    margin-bottom: 35px;
  }
}

.lc-top-text__subtitle {
  font-weight: 400;
  font-size: $minTitleFontSize;
  margin-bottom: 24px;

  @media (min-width: 768px) {
    font-size: $midTitleFontSize;
    margin-bottom: 2.865vw;
  }
  @media (min-width: 1200px) {
    font-size: $maxTitleFontSize;
    margin-bottom: 55px;
  }
}

.lc-top-text__desc {
  font-size: $minTextFontSize;

  @media (min-width: 768px) {
    font-size: $midTextFontSize;
  }
  @media (min-width: 1200px) {
    font-size: $maxTextFontSize;
  }
}

.lc-top-slider {
  width: 100%;

  .swiper-wrapper {
    align-items: stretch;
  }
  .swiper-slide {
    height: auto;
  }
  .swiper-button {
    color: #fff;
    width: 50px;
    height: 50px;
    background: rgba($mainDark,.5);
    border-radius: 4px;
  }
}

.lc-top-slider__slide {
  //width: auto;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: center;
  justify-content: center;
  transition-property: background-color, color;
  transition-duration: .7s;
  transition-timing-function: linear;

  &.swiper-slide {
    width: 300px;

    //@media (min-width: 1024px) {
    //  width: 380px;
    //}
    @media (min-width: 1200px) {
      width: 400px;
    }
  }
}

.lc-top-slider__slide.swiper-slide-prev .lc-top-slide {
  transition: all .2s linear
}

.lc-top-slider .swiper-slide-active {
  .lc-top-slide {
    background: linear-gradient(45deg, $secondaryColor 0, $secondaryColor 100%);
    color: $mainDark
  }
  .lc-top-slide h3 {
    color: $mainDark
  }

  .lc-top-slide img.img {
    display: block
  }
  .lc-top-slide img.img-hover {
    display: none
  }

  .lc-top-slide__offer {
    color: rgba($mainDark, .8)
  }
  .lc-top-slide__offer::before {
    background-color: $mainDark
  }
}

.lc-top-slider__pagination {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 60px;

  @media (min-width: 801px) {
    display: none
  }
  @media (max-width: 900px) and (orientation: landscape) {
    display: block
  }

  .swiper-pagination-bullet {
    background: $secondaryColor;
    margin: 0 4px
  }
}

.lc-top-slide {
  font-size: 20px;
  color: $mainDark;
  height: 100%;
  //max-width: 18em;
  //min-height: 23.5em;
  background: $black;
  border-radius: 10px;
  padding: 1.7em;

  //@media (min-width: 768px) {
    //max-width: 20em;
  //}

  @media (max-width: 768px) {
    //max-width: 330px;
    padding: 1.25em;
  }
}

.lc-top-slide__top {
  width: 100%;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: justify;
  justify-content: space-between;
  min-height: 90px;

  @media (max-width: 480px) {
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
    min-height: 130px;
  }
}

.lc-top-slide__top h3 {
  //font-family: $fontOswald;
  text-transform: capitalize;
  font-weight: 400;
  letter-spacing: .12em;
  max-width: 60%;
  flex-shrink: 0;
  color: $secondaryColor;
  font-size: 19px;

  @media (min-width: 768px) {
    font-size: 1.4vw;
  }
  @media (min-width: 1200px) {
    font-size: 27px;
  }
  @media (max-width: 480px) {
    max-width: none;
  }
}

.lc-top-slide__top img {
  flex-shrink: 0;
  width: 60px;
  max-height: 90px;

  @media (min-width: 768px) {
    width: 80px;
  }
  @media (max-width: 480px) {
    max-height: 50px;
    width: auto;
  }
}

.lc-top-slide__top img.img {
  display: none
}

.lc-top-slide__top img.img-hover {
  display: block
}

.lc-top-slide__offer {
  position: relative;
  margin-top: 3em;
  color: $textColor;
  max-width: 97%;
  font-size: $minTextFontSize;

  @media (min-width: 768px) {
    font-size: $midTextFontSize;
  }
  @media (min-width: 1200px) {
    font-size: $maxTextFontSize;
  }
}

.lc-top-slide__offer::before {
  position: absolute;
  z-index: 1;
  left: 0;
  top: calc(-3em / 2);
  content: "";
  width: 110px;
  height: 2px;
  background-color: $secondaryColor;

  @media (max-width: 480px) {
    width: 100%;
    max-width: 100%
  }
}
