$secondaryColor: #b3b3b3;

.sw-offices-slider {
  text-align: center;
  margin-top: 20px;

  .swiper-wrapper {
    width: auto;
    display: inline-flex;
    margin: auto;
  }

  &__slide {
    max-width: 233px;
    padding-bottom: 25px;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    & .sw-offices-name,
    & .sw-offices-country {
      display: flex;
      align-items: center;
      justify-content: center;
      vertical-align: center;
      color: $textColor;
      font-size: $minTextFontSize;

      @media (min-width: 768px) {
        font-size: $midTextFontSize;
      }
      @media (min-width: 1200px) {
        font-size: $maxTextFontSize;
      }
    }
    & .sw-offices-country {
      font-weight: bold;
      font-size: $minSectionTitleFontSize;

      @media (min-width: 768px) {
        font-size: $midSectionTitleFontSize;
      }
      @media (min-width: 1200px) {
        font-size: $maxSectionTitleFontSize;
      }
    }

    & .sw-offices-slide {
      position: relative;
      display: flex;
      width: 100%;
      height: 290px;
      border-radius: 3px;
      margin-bottom: 12px;

      & img {
        border-radius: 28px;
        width: 100%;
        object-fit: cover;
        object-position: 50% 50%;
      }

      &::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 50%;
        bottom: 0;
        left: 0;
        z-index: 1;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba($mainDark, 0.7) 100%);
        opacity: 1;
      }

      h2 {
        position: relative;
        display: flex;
        margin: auto 0 -22px 30px;
        font-weight: 300;
        letter-spacing: 1px;
        color: $textColor;
        transition: $tr2;
        z-index: 2;
        white-space: nowrap;

        font-size: $minTextFontSize;

        @media (min-width: 768px) {
          font-size: $midTextFontSize;
        }
        @media (min-width: 1200px) {
          font-size: $maxTextFontSize;
        }
      }

      &__address {
        color: $textColor;
        border-radius: inherit;
        opacity: 0;
        padding: 12px;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2;
        line-height: 1.5;
        display: flex;
        flex-direction: column;
        justify-content: center;
        font-size: $minTextFontSize;

        @media (min-width: 768px) {
          font-size: $midTextFontSize;
        }
        @media (min-width: 1200px) {
          font-size: $maxTextFontSize - 1px;
        }

        hr {
          color: $textColor;
          margin: 15px auto;
          border-top: none;
          height: 3px;
          border-radius: 3px;
          width: 76px;
          opacity: 1;
        }
      }
    }

    &.swiper-slide-active {
      color: $secondaryColor;
      h2 {
        color: $secondaryColor;
      }
      //.sw-offices-name,
      .sw-offices-country {
        color: $secondaryColor;
      }
    }

/** &.swiper-slide-active:hover { */
    &:hover {

      //& .sw-offices-name {
      & .sw-offices-country {
        color: $secondaryColor;
      }

      h2 {
        color: $secondaryColor;
      }

      .sw-offices-slide {
        &::after {
          background: rgba($mainDark, 0.6);
          width: inherit;
          height: inherit;
          border-radius: inherit;
        }

        a {
          color: $white;
        }

        &__address {
          opacity: 1;
        }
      }
    }
  }

  &__pagination {
    @media (min-width: 801px) {
      display: none;
    }
    @media (max-width: 900px) and (orientation: landscape) {
      display: block;
    }
    width: fit-content;
    margin-top: 60px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);

    .swiper-pagination-bullet {
      background: rgba($textColor, 1);
      margin: 0 5px;
    }
  }

  &__arrows {
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: auto;
    padding-top: 10px;

    @media (max-width: 1024px) {
      font-size: 8px;
    }

    @media (max-width: 800px), (max-width: 900px) and (orientation: landscape) {
      display: none;
    }

    .arrow-prev,
    .arrow-next {
      outline: none;
      cursor: pointer;
      width: 18.3em;
      height: 11.1em;
      background-color: $mainDark;
      background-repeat: no-repeat;
      background-position: center;
      transition: $tr2;
    }

    .arrow-prev {
      background-color: $textColor;
      background-image: url('/assets/site/images/cur/arrow_left.png');

      &:hover {
        background-position-x: 48%;
      }
    }

    .arrow-next {
      background-image: url('/assets/site/images/cur/arrow_right.png');

      &:hover {
        background-position-x: 52%;
      }
    }
  }
}
