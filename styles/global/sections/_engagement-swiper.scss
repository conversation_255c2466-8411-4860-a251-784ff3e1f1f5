@import "../variables";
// engagement swiper
.engagement-swiper {
  &-section {
    margin: 0 auto 50px;
  }

  &-title {
    font-size: $minTitleFontSize;
    font-weight: bold;
    color: #fff;
    line-height: 1.25;
    margin: 0 auto 8px;
    text-align: center;

    @media (min-width: 768px) {
      font-size: $midTitleFontSize;
      margin-bottom: 18px;
    }
    @media (min-width: 1200px) {
      font-size: $maxTitleFontSize;
      margin-bottom: 85px;
    }
  }

  .swiper-wrapper {
    align-items: center;
  }

  .swiper-slide {
    padding: 0 28px;

    @media (min-width: 480px) {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    @media (min-width: 768px) {
      padding: 0 54px;
    }
    @media (min-width: 1900px) {
      padding: 0 100px;
    }

    &-figure {
      display: block;
      flex-shrink: 0;
      margin-bottom: 20px;

      @media (min-width: 480px) {
        width: 45%;
        margin-right: 3.125vw;
        margin-bottom: 0;
      }
      @media (min-width: 1900px) {
        width: 40.313vw;
        max-width: 774px;
      }

      &-img {
        display: block;
        width: 200px;
        margin: auto;

        @media (min-width: 480px) {
          max-width: 600px;
          max-height: 600px;
          object-fit: contain;
          width: 100%;
          margin: 0 auto;
        }
      }
    }

    &-content {
      text-align: center;
      max-width: 500px;
      margin: auto;

      @media (min-width: 480px) {
        text-align: left;
        max-width: calc(100% - 45% - 3.125vw);
        margin: 0;
      }
    }

    &-title,
    &-subtitle {
      margin-bottom: 10px;
      color: #fff;
      font-weight: bold;
      font-size: $minTitleFontSize;

      @media (min-width: 768px) {
        font-size: $midTitleFontSize;
      }
      @media (min-width: 1200px) {
        font-size: $maxTitleFontSize;
      }
    }

    &-subtitle {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    &-text {
      font-size: $minTextFontSize;

      @media (min-width: 768px) {
        font-size: $midTextFontSize;
      }
      @media (min-width: 1200px) {
        font-size: $maxTextFontSize;
      }

      &.spaced {
        margin-bottom: 30px;

        @media (min-width: 768px) {
          margin-bottom: 40px;
        }
      }
    }
  }

  .swiper-button-prev,
  .swiper-button-next {
    cursor: pointer;
    position: absolute;
    margin: 0;
    top: 50%;
    height: 24px;
    width: 24px;
    transform: translateY(-50%);
    z-index: 10;
    background: no-repeat 50% 50%;
    background-size: auto 100%;
    background-image: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDI1LjQuMSwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPgo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHZpZXdCb3g9IjAgMCA0My45NSA3My45NSIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNDMuOTUgNzMuOTU7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4KPHN0eWxlIHR5cGU9InRleHQvY3NzIj4KCS5zdDB7ZmlsbDojRkZGRkZGO30KPC9zdHlsZT4KPGc+Cgk8cGF0aCBjbGFzcz0ic3QwIiBkPSJNMzguODUsNDIuMDdjLTEuMjYsMC0yLjUyLTAuNDYtMy41LTEuMzlMMS42LDguODFDLTAuNDUsNi44Ny0wLjU0LDMuNjUsMS4zOSwxLjYKCQlDMy4zMy0wLjQ1LDYuNTUtMC41NCw4LjYsMS4zOWwzMy43NSwzMS44N2MyLjA1LDEuOTMsMi4xNCw1LjE2LDAuMjEsNy4yMUM0MS41Niw0MS41NCw0MC4yMSw0Mi4wNywzOC44NSw0Mi4wN3oiLz4KPC9nPgo8Zz4KCTxwYXRoIGNsYXNzPSJzdDAiIGQ9Ik01LjEsNzMuOTVjLTEuMzUsMC0yLjcxLTAuNTQtMy43MS0xLjZjLTEuOTMtMi4wNS0xLjg0LTUuMjgsMC4yMS03LjIxbDMzLjc1LTMxLjg3CgkJYzIuMDUtMS45Myw1LjI4LTEuODQsNy4yMSwwLjIxYzEuOTMsMi4wNSwxLjg0LDUuMjgtMC4yMSw3LjIxTDguNiw3Mi41NkM3LjYyLDczLjQ5LDYuMzYsNzMuOTUsNS4xLDczLjk1eiIvPgo8L2c+Cjwvc3ZnPgo=');

    &:after {
      display: none;
    }

    @media (min-width: 1440px) {
      height: 52px;
      width: 52px;
    }

    &.swiper-button-prev {
      left: 0;
      transform: translateY(-50%) scale(-1, -1);
    }

    &.swiper-button-next {
      right: 0;
    }
  }
}
