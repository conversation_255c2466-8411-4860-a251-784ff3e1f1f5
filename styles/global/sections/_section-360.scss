.section-sw360 {
  position: relative;
  display: block;
  width: 100%;
  margin-bottom: 40px;

  @media (min-width: 768px) {
    margin-top: 20px;
  }

  & .wrap {
    display: flex;
    flex-direction: row;
    max-width: $sectionInfoMaxWidth;
    margin: 0 auto;

    @media (max-width: 976px) {
      flex-direction: column;
    }
  }

  & .sw-360 {
    display: flex;
    flex-direction: column;
    margin-right: 40px;
    flex: 0.5;

    @media (max-width: 976px) {
      margin-right: 0;
    }

    &-title {
      display: block;
      width: 100%;
      color: #fff;
      text-align: left;
      font-size: $minSectionTitleFontSize;
      font-weight: bold;
      margin-bottom: 5px;

      @media (min-width: 768px) {
        font-size: $midSectionTitleFontSize;
      }
      @media (min-width: 1200px) {
        font-size: $maxSectionTitleFontSize;
      }
    }

    &-description {
      display: block;
      width: 100%;
      color: #b3b3b3;
      margin: 0;
      text-align: justify;
      font-size: $minTextFontSize;

      @media (min-width: 768px) {
        font-size: $midTextFontSize;
      }
      @media (min-width: 1200px) {
        font-size: $maxTextFontSize;
      }

      & .accent {
        color: $secondaryColor;
      }

      p {
        margin-bottom: 1em;
      }
    }
  }

  & .sw-360-provides {
    display: flex;
    flex-direction: column;
    margin: 0;
    flex: 0.5;
  }

  .btn {
    width: 100%;
    max-width: 320px;
    margin-right: auto;

    @media (max-width: 976px) {
      margin-left: auto;
    }
  }

  ul {
    margin-bottom: 1em;
  }
}
