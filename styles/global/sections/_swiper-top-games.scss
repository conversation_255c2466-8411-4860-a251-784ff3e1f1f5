@import "../variables";

@keyframes hoverFourGame {
  to {
    transform: translateX(-50%) scale(1);
    opacity: 1;
  }
  from {
    transform: translateX(-50%) scale(0);
    opacity: 0;
  }
}

.sw-news-grid {
  .box-slider::before {
    background: transparent!important;
  }
}

.sw-news__items.news-home {
  overflow: initial;
}

.news-card-home {
  width: 100% !important;
  max-width: 300px;
  height: 100% !important;
  margin: 0 !important;

  .sw-news__items.news-home & {
    max-width: 100%;
  }

  a {
    margin: 0 !important;
    z-index: 5;
  }
}

.swiper.swiper-top-games {
  margin-top: 10px;
  margin-bottom: 50px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  overflow: initial;

  .swiper-wrapper {
    @media (min-width: 1025px) {
      &:hover {
        .swiper-slide {
          opacity: .5;
        }
      }

      .swiper-slide {
        &:last-child {
          margin-right: 0!important;
        }
        &:hover {
          opacity: 1;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .swiper-top-games {
    margin-top: -30px;
    margin-bottom: 30px;
    transform: scale(0.85);
  }
}

.swiper-top-games__card {
  max-width: 360px;
}
.swiper-slide.swiper-top-games__box {
  display: block;
  position: relative;
  width: auto;
  font-weight: normal;
  height: auto;
  transition: transform 0.2s;
  transform-origin: center;
  border-radius: 18px;
  box-shadow: 0 14px 18px 0 rgba($mainDark, 0.8), 0 26px 28px 0 rgba($mainDark, 0.79);
  //max-width: 360px;

  & .box-link {
    color: transparent;
    cursor: pointer;

    img {
      cursor: pointer;
    }
  }
}

//@media (min-width: 1921px) {
//  .swiper-slide.swiper-top-games__box {
//    width: 360px;
//  }
//}

@media only screen and (min-width: 1025px) {
  .swiper-slide.swiper-top-games__box {
    flex-shrink: 1;
  }
}

@media (max-width: 480px) {
  .swiper-slide.swiper-top-games__box {
    flex-shrink: 0;
    max-width: 85%;

    .sw-swiper-home-2 & {
      max-width: 65%;
    }
  }
}

.swiper-slide.swiper-top-games__box.swiper-slide-active {
  position: relative;
  z-index: 3;
}

@media (max-width: 480px) {
  //.swiper-slide.swiper-top-games__box.swiper-slide-active::after {
    //top: 70%;
    //opacity: 1;
    //display: block;
    //box-shadow: 0 0 80px 70px rgba($secondaryColor, 0.8);
    //width: 2%;
    //content: "";
  //}
  .swiper-top-games {
    margin-left: 14px;
  }
  .swiper-slide.swiper-top-games__box.swiper-slide-active {
    transform: scale(1.15);
  }
}

.swiper-slide.swiper-top-games__box.swiper-slide-prev,
.swiper-slide.swiper-top-games__box.swiper-slide-next {
  z-index: 2;
}

//.swiper-slide.swiper-top-games__box:nth-child(1) {
//  margin-right: 0;
//}

//@media only screen and (min-width: 1025px) {
//  .swiper-slide.swiper-top-games__box:nth-child(1) {
//    margin-right: 7px;
//  }
//}

//.swiper-slide.swiper-top-games__box:nth-child(2), .swiper-slide.swiper-top-games__box:nth-child(3) {
//  margin: 0 0;
//}
//
//@media only screen and (min-width: 1025px) {
//  .swiper-slide.swiper-top-games__box:nth-child(2), .swiper-slide.swiper-top-games__box:nth-child(3) {
//    margin: 0 7px;
//  }
//}
//
//.swiper-slide.swiper-top-games__box:nth-child(4) {
//  margin-left: 0;
//}
//
//@media only screen and (min-width: 1025px) {
//  .swiper-slide.swiper-top-games__box:nth-child(4) {
//    margin-left: 7px;
//  }
//}

//@media only screen and (min-width: 1025px) {
//  .swiper-slide.swiper-top-games__box:nth-child(-n+4) {
//    margin: 7px;
//  }
//}

//@media (max-width: 1024px) {
//  .swiper-slide.swiper-top-games__box:nth-child(-n+4) {
//    margin: 0;
//  }
//}

//.swiper-slide.swiper-top-games__box::before {
//  border-radius: 0 0 18px 18px;
//  opacity: 0;
//  z-index: 2;
//  position: absolute;
//  left: 0;
//  top: 50%;
//  bottom: 0;
//  right: 0;
//  content: "";
//  //height: 100%;
//  width: 100%;
//  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, $mainDark 100%);
//}
//
//@media (max-width: 480px) {
//  .swiper-slide.swiper-top-games__box::before {
//    display: none;
//  }
//}

//.swiper-slide.swiper-top-games__box::after {
  //opacity: 0;
  //z-index: -3;
  //position: absolute;
  //content: "";
  //left: 50%;
  //top: 70%;
  //transform: translate(-50%, -70%);
  //height: 1px;
  //width: 1px;
  //border-radius: 50%
  //box-shadow: 0 0 60px 100px rgba($secondaryColor, 0.8);
  //border-radius: 18px;
  //top: 50%;
  //bottom: 0;
  //left: 0;
  //right: 0;
  //box-shadow: 0 0 50px 5px rgba($secondaryColor, 0.8);
//}

//@media (min-width: 1921px) {
//  .swiper-slide.swiper-top-games__box::after {
//    box-shadow: 0 0 100px 170px rgba($secondaryColor, 0.8);
//  }
//}

//@media (max-width: 480px) {
//  .swiper-slide.swiper-top-games__box::after {
//    display: none;
//  }
//}

.swiper-slide.swiper-top-games__box:hover .swiper-top-games__category {
  display: none;
}

@media only screen and (min-width: 1025px) {
  .swiper-slide.swiper-top-games__box:hover::before, .swiper-slide.swiper-top-games__box:hover::after {
    opacity: 1;
  }
  .swiper-slide.swiper-top-games__box:hover {
    transform: scale(1.2);
    z-index: 3;
  }
  .swiper-slide.swiper-top-games__box:hover .game-hover {
    display: flex;
//    transform: translateX(-50%) scale(1);
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .swiper-slide.swiper-top-games__box:hover .game-hover__text {
    transform: scale(0.65);
  }
  //.swiper-slide.swiper-top-games__box:hover .game-hover__btn {
  //  transform: scale(0.65);
  //}
}

.swiper-top-games__img {
  border-radius: 18px;
  display: block;
  width: 100%;
  height: auto;
}

.swiper-top-games__name, .swiper-top-games__category {
  display: inline-block;
  position: absolute;
  text-transform: uppercase;
  z-index: 1;
  color: $textColor;
  text-shadow: 1px 1px $black;
}

.swiper-top-games__name {
  color: $secondaryColor;
  font-size: 18px;
  line-height: 26px;
  letter-spacing: 0.06em;
  left: 37px;
  top: 34px;
}

.swiper-top-games__category {
  font-size: 12px;
  line-height: 26px;
  letter-spacing: 0.8em;
  left: 46px;
  bottom: 27px;
  transform-origin: 0 100%;
  transform: rotate(-90deg);
  z-index: 1;
}

.swiper-top-games__btn {
  margin-top: 85px;
}

@media (max-width: 767px) {
  .swiper-top-games__btn {
    text-align: center !important;
  }
}

@media (max-width: 480px) {
  .swiper-top-games__btn {
    margin-top: 30px;
  }
}

.game-hover {
  display: none;
  z-index: 4;
  position: absolute;
  left: 50%;
  bottom: 0;//4.16vh;
  transition: all 0.2s ease;
  transform: translateX(-50%);
  animation: hoverFourGame 0.3s linear both;
}

@media (min-width: 1921px) {
  .game-hover {
    bottom: 45px;
  }
}

@media (max-width: 1024px) {
  .game-hover {
    display: none !important;
  }
}

.game-hover__text {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin: 0 auto;
  margin-bottom: 20px;
  font-size: $minTextFontSize;

  @media (min-width: 768px) {
    font-size: $midTextFontSize;
  }
  @media (min-width: 1200px) {
    font-size: $maxTextFontSize;
  }
}

.game-hover__text span {
  text-align: center;
  font-family: $fontOswald;
  font-weight: 400;
  letter-spacing: 0.02em;
  display: block;
  font-size: 3.33vh;
  line-height: 1;
  color: $textColor;
  text-shadow: 1px 1px $black;
}

@media (min-width: 1921px), (min-height: 1081px) {
  .game-hover__text span {
    font-size: 36px;
  }
}

.game-hover__text span:nth-child(2) {
  color: $white;
  opacity: 0.8;
  margin-top: 22px;
  white-space: nowrap;
  font-size: $minTitleFontSize;

  @media (min-width: 768px) {
    font-size: $midTitleFontSize;
  }
  @media (min-width: 1200px) {
    font-size: $maxTitleFontSize;
  }
}

.game-hover__text--rtp {
  margin-right: 1.823vw;
}

@media (min-width: 1921px) {
  .game-hover__text--rtp {
    margin-right: 35px;
  }
}

.game-hover__btn {
  padding: 1vw 3vw;

  &.btn {
    color: $black;
    white-space: nowrap;
    word-break: keep-all;
    font-family: $fontRobotoBlack;
  }
}

@media (max-width: 1024px) {
  .game-hover__btn {
    padding: 12px 40px;
  }
}

.swiper .game-label-wrap {
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.swiper .game-label {
  position: absolute;
  z-index: 2;
  top: -45px;
  left: -61px;
  text-align: center;
  transform: rotate(-45deg);
  transform-origin: 100% bottom;
  width: 100%;
  max-width: 210px;
  height: 45px;
  line-height: 45px;
  letter-spacing: .02em;
  text-shadow: 1px 1px 5px $black;
  font-family: $fontOpenSans;
  white-space: nowrap;
  font-weight: 700;
  text-transform: uppercase;
  color: $textColor;
  font-size: $minTextFontSize;

  @media (min-width: 768px) {
    font-size: $midTextFontSize;
  }
  @media (min-width: 1200px) {
    font-size: $maxTextFontSize;
  }
}

@media (max-width: 1024px) {
  .swiper .game-label {
    position: relative;
    left: 0;
    bottom: 24px;
    top: auto;
    transform: rotate(0) scale(1);
    display: block;
    padding: 0;
    width: 100%;
    line-height: 25px;
    height: 24px;
  }
}

.swiper .game-label--soon {
  background-color: #5fb639
}

.swiper .game-label--best {
  background-color: #2a72ad
}

.swiper .game-label--new {
  background-color: #bc0500
}

.game-shadow {
  opacity: 0;
  border-radius: 0 0 18px 18px;
  z-index: 2;
  position: absolute;
  left: 0;
  top: 50%;
  bottom: 0;
  right: 0;
  width: 100%;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, $mainDark 100%);
  transition: all 0.2s ease;

  @media (max-width: 480px) {
    display: none;
  }

  .swiper-slide.swiper-top-games__box:hover & {
    opacity: 1;
  }
}
