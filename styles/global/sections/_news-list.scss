@import "../variables";

.sw-news__items {
  font-size: 20px;
  overflow: auto;
  display: block;
  flex: 1 1;
  max-width: 1942px;
  margin: 0 auto;
  //width: 100%;
  //align-items: stretch;
  //flex-wrap: wrap;
  //width: 100%;
  //max-width: 1670px;
  //margin: auto;
  //max-width: 824px;
  //margin-right: auto;
  //margin-left: auto;

  & .sw-news-grid {
    display: flex;
    align-items: stretch;
    flex-wrap: wrap;
    //width: 100%;
    //max-width: 1670px;

    & .news-div {
      display: flex;
      flex-direction: column;
      width: 100%;

      @media (min-width: 500px) {
        width: 50%;
      }
      @media (min-width: 768px) {
        width: calc(100%/3);
      }
      @media (min-width: 1200px) {
        width: calc(100%/4);
      }

      & .news-item {
        display: flex;
        flex-direction: column;
        margin: 0 11px 15px 11px;
        color: #000;
        text-decoration: none;
        background: #fff;
        height: 100%;

        @media (min-width: 768px) {
          //margin: 15px 11px 65px;
          margin: 0 11px 35px;
        }

        &__img {
          //max-width: 540px;
          //max-height: 300px;
          padding-top: 55.55%;
          width: 100%;
          height: 0;
          position: relative;
          overflow: hidden;

          @media (max-width: 767px) {
            max-width: 100%;
          }

          img {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
            max-width: 100%;
            max-height: 100%;
            transition: $tr2;
            object-fit: cover;
          }
        }

        &:hover {
          color: #000;
          text-decoration: none;

          img {
            transform: scale(1.05);
          }
        }

        &__info {
          font-family: $fontOpenSans;
          line-height: 1.2;
          display: flex;
          flex-direction: column;
          height: 100%;
          padding: 20px 16px 16px;

          @media (min-width: 1024px) {
            padding: 30px 18px 18px;
          }
        }

        &__title {
          font-size: $midTextFontSize;
          flex-grow: 1;
          margin-bottom: 16px;
          word-break: break-word;

          //@media (min-width: 768px) {
          //  font-size: $midTextFontSize;
          //}
          @media (min-width: 1200px) {
            font-size: $maxTextFontSize;
            margin-bottom: 12px;
          }
        }

        &__label-list {
          display: inline-flex;
          flex-wrap: wrap;
          margin-bottom: 16px;

          @media (min-width: 1024px) {
            margin-bottom: 22px;
          }
          &:empty {
            display: none;
          }
        }

        &__label {
          font-weight: bold;
          font-size: 12px;
          line-height: 1;
          display: inline-flex;
          align-self: flex-start;
          color: #fff;
          background: $secondaryColor;
          border-radius: 5px;
          padding: 6px;
          text-transform: uppercase;
          margin: 2px 4px 2px 0;

          @media (min-width: 768px) {
            font-size: 14px;
            padding: 8px 10px;
          }
          @media (min-width: 1024px) {
            font-size: 15px;
          }

          &.live {
            background-color: purple;
          }
          &.partnership,
          &.green {
            background: $secondaryColor;
          }
          &.partnerships {
            background: #4169e1;
          }

          &.markets,
          &.blue {
            background: #6bc1d3;
          }

          &.games,
          &.red {
            background: #f34f4d;
          }
        }

        &__date {
          font-size: 12px;
          color: $secondaryColor;

          @media (min-width: 768px) {
            font-size: 15px;
          }
        }
      }
    }

  }

  //&.window-fixed {
  //  position: absolute;
  //  z-index: 1000;
  //  right: 0;
  //  left: 0;
  //  //margin: 0;
  //  width: 100%;
  //  background-color: $mainDark;
  //  overflow-x: hidden;
  //
  //  > div {
  //    width: 100%;
  //    overflow-x: hidden;
  //  }
  //}
}

