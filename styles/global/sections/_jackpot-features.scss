@import "../variables";

// jp feature section
.jp-feature {
  &-section {
    background: $secondaryColor;
    position: relative;
    margin-top: 18vw;
    padding: 9.375vw 0;

    @media (min-width: 1024px) {
      padding: 130px 0;
      margin-top: 0;
    }

    @media (min-width: 768px) {
      margin-top: -9.479vw;
    }

    &:before {
      content: '';
      position: absolute;
      top: calc(-9.479vw + 1px);
      left: 0;
      right: 0;
      height: 9.479vw;
      background: url('/assets/site/images/misc/mwjp-header-bg.png') no-repeat 100% 0;
      background-size: 100% auto;
      background-position: bottom;
    }

    &-title {
      font-family: $fontRoboto;
      text-align: center;
      font-size: $minTitleFontSize;
      font-weight: bold;
      color: $black;
      margin-bottom: 4.375vw;
      line-height: 1.25;

      @media (min-width: 768px) {
        font-size: $midTitleFontSize;
        margin-bottom: 4.375vw;
      }
      @media (min-width: 1200px) {
        font-size: $maxTitleFontSize;
        margin-bottom: 84px;
      }
    }
  }

  &-list {
    color: $black;
    font-family: $fontRoboto;
    max-width: 1400px;
    margin: auto;

    @media (min-width: 600px) {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
    }

    &-item {
      flex: 1;
      text-align: center;
      margin: 0 0 50px;

      &:last-child {
        margin-bottom: 0;
      }

      @media (min-width: 600px) {
        margin: 0 20px;
      }
    }

    &-figure {
      text-align: center;
      height: 60px;
      margin-bottom: 8px;
      display: flex;
      align-items: flex-end;

      @media (min-width: 768px) {
        height: 8.333vw;
        margin-bottom: 1vw;
      }
      @media (min-width: 1900px) {
        height: 160px;
        margin-bottom: 18px;
      }

      &-img {
        max-height: 100%;
        width: auto;
        display: block;
        margin: auto;
      }
    }

    &-content {
      max-width: 310px;
      margin: auto;
    }

    &-title {
      font-weight: bold;
      font-size: $minTitleFontSize;
      margin-bottom: 10px;

      @media (min-width: 768px) {
        font-size: $midTitleFontSize;
      }
      @media (min-width: 1200px) {
        font-size: $maxTitleFontSize;
        margin-bottom: 15px;
      }

      .accent {
        display: block;
        color: $textColor;
      }
    }

    &-text {
      font-size: $minTextFontSize;

      @media (min-width: 768px) {
        font-size: $midTextFontSize;
      }
      @media (min-width: 1200px) {
        font-size: $maxTextFontSize;
      }
    }
  }
}
