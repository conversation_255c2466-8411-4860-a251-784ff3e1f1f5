@import "variables";

* {
  -webkit-tap-highlight-color: transparent;
  -webkit-font-smoothing: antialiased;
  -webkit-text-size-adjust: 100%;
  box-sizing: border-box;
  outline: none;
}

body {
  font-family: $fontOpenSans;
  background-color: $mainDark;
  font-weight: 400;
  line-height: 1.3;
  padding: 0;
  margin: 0;
  color: $textColor;
  font-size: 14px;
  position: relative;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.main-container {

  &.app-loading {
    display: flex;
    flex-direction: column;
    position: fixed;
    z-index: 1;
    pointer-events: none;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    margin: 0;

    & .loading-logo {
      max-width: 200px;
      opacity: 0.7;
      display: flex;
      flex-direction: row;

      & .logo-name {
        width: 160px;
        margin-left: -30px;
        //overflow: hidden;
        overflow: visible;
      }
    }
  }
}

.accent {
  color: $secondaryColor;
}

.no-scroll {
  overflow: hidden;
}

.heading-wrapper {
  position: relative;
  overflow: hidden;

  h1, .heading-content {
    font-weight: bold;
    position: absolute;
    top: -100%;
    left: -100%;
    //transform: translate(-100%);
  }
}