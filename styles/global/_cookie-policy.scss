.privacy-policy,
.cookie-policy {
  margin-top: 6.94vh;
  margin-bottom: 12.96vh;

  a {
    color: #fff;
    text-decoration: underline;
  }

  @media (min-width: 1921px), (min-height: 1081px) {
    margin-top: 75px;
    margin-bottom: 140px;
  }

  &__wrap {
    margin-top: 12.5vh;

    @media (min-width: 1921px), (min-height: 1081px) {
      margin-top: 135px;
    }

    &.leftside {
      padding-right: 2.604vw;

      @media (min-width: 1921px) {
        padding-right: 50px;
      }
    }

    &.rightside {
      padding-left: 2.604vw;

      @media (min-width: 1921px) {
        padding-left: 50px;
      }
    }

    @media (max-width: 992px) {
      margin-top: 70px;

      &.leftside {
        padding-right: 0;
      }

      &.rightside {
        padding-left: 0;
      }
    }
  }

  &__article {
    margin-bottom: 6.48vh;

    @media (min-width: 1921px), (min-height: 1081px) {
      margin-bottom: 70px;
    }

    &:last-child {
      margin-bottom: 0;
    }

    &--title {
      display: inline-block;
      //font-family: $oswald;
      font-size: 36px;
      line-height: initial;
      margin-bottom: 5.27vh;
      position: relative;

      @media (min-width: 1921px), (min-height: 1081px) {
        font-size: 36px;
        margin-bottom: 57px;
      }

      @media (max-width: 992px) {
        font-size: 24px;
        margin-bottom: 50px;
      }

      &::after {
        content: "";
        position: absolute;
        left: 0;
        bottom: -2.13vh;
        width: 100%;
        height: 2px;
        background-color: $secondaryColor;

        @media (min-width: 1921px), (min-height: 1081px) {
          bottom: -23px;
        }

        @media (max-width: 992px) {
          bottom: -25px;
        }
      }
    }

    &--text {
      font-size: 20px;
      line-height: initial;
      white-space: pre-line;

      @media (min-width: 1921px) {
        font-size: 20px;
      }

      @media (max-width: 992px) {
        font-size: 16px;
        line-height: 36px;
      }

      u {
        display: inline-block;
        margin: 0.5em 0;
      }

      .table-cookie {
        margin: 2em 0;
        white-space: normal;

        table {
          font-size: 16px;
          line-height: 1.4;
          width: 100%;
          border-collapse: collapse;

          thead {
            background-color: #222;
          }

          th,
          tr,
          td {
            border: 1px solid #999;
            vertical-align: middle;
          }

          th {
            text-align: left;
            padding: 0.5em;
            line-height: 1;
          }

          td {
            padding: 0.5em;

            &:nth-child(4) {
              width: 40%;
              @media (max-width: 1300px) {
                width: 100%;
              }
            }

            a {
              color: #fff;
              display: block;
              text-decoration: underline;
              word-break: break-all;
            }
          }

          @media (max-width: 1300px) {
            table,
            tbody,
            th,
            td,
            tr {
              display: block;
            }

            tr {
              border: none;
            }

            thead {
              background-color: transparent;

              tr {
                position: absolute;
                top: -9999px;
                left: -9999px;
              }
            }

            td {
              border-bottom: none;
              position: relative;
              padding-left: 42%;
              min-height: 60px;

              @media (max-width: 480px) {
                padding-left: 35%;
              }

              &::before {
                background-color: #222;
                position: absolute;
                top: 0;
                left: 0;
                width: 40%;
                padding: 0.5em;
                border-right: 1px solid #999;
                height: 100%;

                @media (max-width: 480px) {
                  width: 33%;
                }
              }
              &:nth-of-type(1)::before {
                content: "Cookie";
              }
              &:nth-of-type(2)::before {
                content: "Third Party Provider";
              }
              &:nth-of-type(3)::before {
                content: "Category of Cookie";
              }
              &:nth-of-type(4)::before {
                content: "Description of what Cookie does / Purpose";
              }
              &:nth-of-type(5)::before {
                content: "Duration of cookie";
              }
              &:nth-of-type(6)::before {
                content: "Further information";
              }
            }

            tbody {
              tr:nth-child(2) {
                td {
                  &:last-child {
                    border-left: none;
                    border-right: none;
                    padding: 0;
                    height: 0;

                    &::before {
                      content: none;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
