@import "variables";
.sw-cookie {
  background-color: #000;
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 1040;
  padding: 20px 0;
}

.sw-cookie-disclaimer {
  display: flex;
  align-items: center;
  justify-content: space-between;

  @media (max-width: 992px) {
    flex-direction: column;
  }

  @media (max-width: 600px) {
    justify-content: center;
  }

  &__text {
    display: inline-block;
    font-size: 18px;
    line-height: 36px;

    @media (max-width: 480px), (max-width: 900px) and (orientation: landscape) {
      font-size: 12px;
      line-height: initial;
    }
  }

  &__btns {
    display: flex;
    align-items: center;
    justify-content: space-between;

    @media (max-width: 992px) {
      margin-top: 1.5em;
    }

    @media (max-width: 736px) {
      transform: scale(0.7);
      justify-content: center;
    }

    .button {
      padding: 19px 63px;

      @media (min-width: 1921px) {
        padding: 19px 63px;
      }

      &:nth-child(2) {
        padding: 17px 28.5px;
        margin-left: 30px;

        @media (min-width: 1921px) {
          padding: 17px 28.5px;
        }
      }
    }
  }
}
