@import "variables";

.tagify {
  border: none !important;
}

.tagify--outside {
  border: 0;
  width: 100%;

  @media (min-width: 420px) {
    width: 90%;
  }

  & .tagify__input {
    order: -1;
    flex: 100%;
    border: 2px solid $mainDark;
    margin-bottom: 1em;
    transition: 0.1s;
  }

  & .tagify__input:hover {
    border-color: $mainDark;
  }
}

.tagify__dropdown {
  position: fixed !important;

  &.tagify__dropdown--text {
    background-color: transparent;
    color: $mainDark;
    border: none;
  }

  &__wrapper {
    background-color: transparent;
    border: none;

    & .tagify__dropdown__item {
      background-color: $mainDark;
      color: $secondaryColor;
      border: 1px solid $secondaryColor;
    }
  }
}

.tagify__tag {
  display: flex;
  align-items: center;
  justify-content: center;
  color: $mainDark;
  border: 2px solid $mainDark;
  background-color: transparent;
  padding: 2px 8px;
  border-radius: 29px;
  min-width: initial;
  margin: 2px 4px;
  transition: all 0.2s ease;
  cursor: pointer;
  text-transform: uppercase;
  text-align: center;
  font-size: 14px;
  font-weight: 700;
  line-height: 1;

  & > div::before {
    box-shadow: none !important;
  }

  &:hover:not([readonly]) div::before {
    box-shadow: none !important;
  }
}
