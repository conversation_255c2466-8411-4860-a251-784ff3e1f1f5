@import "variables";

.button-main {
  text-align: left;
  background-color: transparent;
  color: $secondaryColor;
  line-height: 1em;
  padding: 4px;
  margin: 2px 4px;
  border-left: 1px solid rgba(141, 211, 99, 0.5);
  border-top: 1px solid rgba(141, 211, 99, 0.2);
  border-right: 1px solid rgba(141, 211, 99, 0.5);
  border-bottom: 1px solid rgba(141, 211, 99, 0.2);

  &.active {
    color: $mainDark !important;
    text-decoration: none !important;
    background-color: $secondaryColor !important;
  }

  &:hover {
    color: $mainDark !important;
    text-decoration: none !important;
    background-color: $secondaryColor !important;
  }
}

.btn-primary {
  padding: 6px 16px;
  font-size: 0.875rem;
  min-width: 64px;
  box-sizing: border-box;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  font-weight: 500;
  line-height: 1.75;
  border-radius: 4px;
  letter-spacing: 0.02857em;
  text-transform: uppercase;
}