# Install dependencies only when needed
FROM node:16.14.2-alpine AS deps

# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /app
# --no-cache: download package index on-the-fly, no need to cleanup afterwards
# --virtual: bundle packages, remove whole bundle at once, when done
RUN apk add --no-cache --virtual .gyp python3 make g++ git
COPY package.json yarn.lock .yarnrc.yml .yarn ./
COPY .yarn ./.yarn
# install and configure yarn as per doc https://yarnpkg.com/getting-started/install
# Corepack is included by default with all Node.js installs, but is currently opt-in. To enable it, run the following command:
RUN corepack enable
RUN yarn set version 3.x
RUN yarn plugin import workspace-tools
RUN yarn workspaces focus --all --production

# Rebuild the source code only when needed
FROM node:16.14.2-alpine AS builder
WORKDIR /app

COPY . .
COPY --from=deps /app/node_modules ./node_modules
# RUN yarn build && yarn install --production --ignore-scripts --prefer-offline
RUN yarn add --dev typescript && yarn build && rm -rfv .yarn .pnp*

# Production image, copy all the files and run next
FROM node:16.14.2-alpine AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# You only need to copy next.config.js if you are NOT using the default configuration
COPY --from=builder /app/next.config.js ./
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

USER nextjs

EXPOSE 3044

# Next.js collects completely anonymous telemetry data about general usage.
# Learn more here: https://nextjs.org/telemetry
# Uncomment the following line in case you want to disable telemetry.
# ENV NEXT_TELEMETRY_DISABLED 1

CMD ["yarn", "start"]
