@Library('sw_common_library') _

pipeline {
    agent any

    options {
        disableConcurrentBuilds()
        timestamps()
    }

    environment {
        PROJECT = "gcpstg"
        REPO = "skywindgroup"
        REGION = "asia.gcr.io"
        DEV_PROJECT = "sw-dev-qa"
        SERVICE = "sw-partner-area-service"
        REVISION = sh(returnStdout: true, script: 'git rev-parse HEAD').trim()
        BRANCH_NAME_NORMALIZED = "${BRANCH_NAME.toLowerCase().replace("/", "_")}"
    }

    stages {
        stage('Build') {
            when { not { anyOf { branch 'master'; branch 'test-branch' } } }
            steps {
                configFileProvider([configFile(fileId: 'npmInstall', targetLocation: '.npmrc')]) {
                    sh "docker build --no-cache -f Dockerfile.prod -t ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED} ."
                }
            }
        }

        stage('Tag') {
            when { not { anyOf { branch 'master'; branch 'test-branch' } } }
            steps {
                sh "docker tag ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED} ${REGION}/${PROJECT}/${SERVICE}:${BRANCH_NAME_NORMALIZED}"
                sh "docker tag ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED} ${REGION}/${PROJECT}/${SERVICE}:${REVISION}"
            }
        }

        stage('Push') {
            when { not { anyOf { branch 'master'; branch 'test-branch' } } }
            steps {
                sh "docker push ${REGION}/${PROJECT}/${SERVICE}:${BRANCH_NAME_NORMALIZED}"
                sh "docker push ${REGION}/${PROJECT}/${SERVICE}:${REVISION}"
                script {
                    try {
                        GCR_IMAGE = sh(returnStdout: true, script: "docker image inspect '${REGION}/${PROJECT}/${SERVICE}:${REVISION}' | jq .[0] | jq -c '.RepoDigests' | jq .[0]").trim()
                        currentBuild.description = "${GCR_IMAGE}"
                    } catch (Exception e) {
                        echo 'Image inspection failed!'
                    }
                }
            }
        }

        stage('Clean up') {
            when { not { anyOf { branch 'master'; branch 'test-branch' } } }
            steps {
                sh "docker rmi ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED}"
                sh "docker rmi ${REGION}/${PROJECT}/${SERVICE}:${BRANCH_NAME_NORMALIZED}"
                sh "docker rmi ${REGION}/${PROJECT}/${SERVICE}:${REVISION}"
            }
        }

        stage('Deploy Stage') {
           when { branch 'release/develop-env' }
           steps {
               withDockerContainer(image: "gcr.io/google.com/cloudsdktool/cloud-sdk", toolName: 'latest') {
                   withCredentials([file(credentialsId: 'k8s-stage-config', variable: 'SECRET')]) {
                       sh "kubectl --kubeconfig ${SECRET} rollout restart deployment/partner-area-service -n sw-internal"
                    }
                }
            }
        }

        stage('Deploy Prod') {
            when { branch 'release/prod-env' }
            steps {
                withDockerContainer(image: "gcr.io/google.com/cloudsdktool/cloud-sdk", toolName: 'latest') {
                    withCredentials([file(credentialsId: 'romania-k8s-prod', variable: 'SECRET')]) {
                        sh "kubectl --kubeconfig ${SECRET} rollout restart deployment/partner-area-service -n sw-internal"
                    }
                }
            }
        }

        stage('Invalidate cache') {
            when { branch 'release/prod-env' }
            steps {
                withAWS(credentials: 'jenkins-cloudfront', region: 'us-east-1') {
                    cfInvalidate(distribution:'EX9CMP80BVGBD', paths:['/*'], waitForCompletion: true)
                }
            }
        }
    }

    // post {
    //     always {
    //         emailext (
    //           to: '<EMAIL>',
    //           body: "Check console output at $BUILD_URL to view the results.",
    //           subject: 'Build $BUILD_STATUS! - $PROJECT_NAME',
    //           attachLog: true,
    //           compressLog: true
    //         )
    //     }
    // }
}
