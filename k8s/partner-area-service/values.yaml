name: partner-area-service
namespace: sw-internal

labels:
  k8s-app: partner-area-service

replicaCount: 2

image:
  repository: asia.gcr.io/gcpstg/sw-partner-area-service
  tag: release_develop-env
  pullPolicy: Always 

containerPorts:
  - 3044:http

clusterIP: *************
serviceType: ClusterIP
servicePorts:
  - 80:3044:http

hostAliases:
  - elk-kafka-01:***********
  - elk-kafka-02:***********
  - kafka-01:***********
  - kafka-02:***********
  - kafka-03:***********  

serviceAccount: partner-area-service-acc
serviceAccountName: partner-area-service-acc

rollingUpdate:
  maxSurge: 1
  maxUnavailable: 0
  
readinessProbe:
  httpGet:
    path: /
    port: http
  initialDelaySeconds: 15
  periodSeconds: 10

resources:
  limits:
    cpu: "2"
    memory: 4Gi
  requests:
    cpu: 100m
    memory: 2Gi
