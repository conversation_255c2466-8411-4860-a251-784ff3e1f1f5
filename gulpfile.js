const gulp = require("gulp");
const tsc = require("gulp-typescript");
const sourceMaps = require("gulp-sourcemaps");
const git = require("gulp-git");
const fs = require("fs");
const file = require("gulp-file");
const bump = require("gulp-bump");
const debug = require("gulp-debug");
const gulpSass = require("gulp-sass");
const dartSass = require("sass");
const postCss = require("gulp-postcss");
const cssnano = require("cssnano");
const rename = require("gulp-rename");
const autoprefixer = require("autoprefixer");
const sassFn = gulpSass(dartSass);

gulp.task("compile-cypress", () => {
    let errors = false;
    const tsProject = tsc.createProject("cypress/tsconfig.json");
    return gulp
        .src(["cypress/integration/api/**/*.ts"])
        .pipe(sourceMaps.init())
        .pipe(tsProject())
        .on("error", function() {
            errors = true;
        })
        .on("end", function() {
            if (errors) process.exit(1);
        })
        .pipe(sourceMaps.write())
        .pipe(gulp.dest("cypress/integration/api"));
});

gulp.task("version", () => {
    return git.revParse({ args: "--short HEAD" }, function(err, hash) {
        const version = JSON.parse(fs.readFileSync("./package.json")).version;
        const str = JSON.stringify({
            apiVersion: version,
            revision: hash,
            dateTime: new Date().toString(),
            dateTimeIso8601: new Date().toISOString()
        });
        return file("version.json", str, { src: true }).pipe(gulp.dest("src/"));
    });
});

gulp.task("scss", () => {
    return gulp
        .src(["styles/pa/pa.main.scss", "styles/site/site.main.scss"])
        .pipe(sassFn())
        .pipe(sourceMaps.init())
        .pipe(
            postCss([
                autoprefixer({ grid: "autoplace" }),
                cssnano({ preset: ["default", { discardComments: { removeAll: true } }] })
            ])
        )
        .pipe(sourceMaps.write("."))
        .pipe(rename({ suffix: "", prefix: "" }))
        .pipe(debug())
        .pipe(gulp.dest("public/styles"))
        .on("success", function(){ console.log(arguments) });
});

gulp.task("bump", async () => {
    gulp.src("./package.json")
        .pipe(bump({ key: "version" }))
        .pipe(gulp.dest("./"));
});

gulp.task("watch", function() {
    "use strict";

    return gulp.watch("styles/**/*.scss", gulp.series("scss"));
    // Other watchers
});

gulp.task("default", gulp.series("scss", "watch"));
