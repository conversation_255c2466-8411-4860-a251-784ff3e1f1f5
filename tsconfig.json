{
  "compilerOptions": {
    "allowJs": true,
    "baseUrl": "src",
    "paths": {
      "@components/*": [
        "./client/components/*"
      ],
      "@hooks/*": [
        "./client/hooks/*"
      ],
      "@services/*": [
        "./client/services/*"
      ],
      "@stores/*": [
        "./client/stores/*"
      ],
      "@pages/*": [
        "./pages/*"
      ]
    },
    "esModuleInterop": true,
//    "experimentalDecorators": true,
//    "emitDecoratorMetadata": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "module": "esnext",
    "moduleResolution": "node",
    "noEmit": true,
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "strict": true,
    "target": "es5",
    "types": [
      "cypress"
    ],
    "incremental": true
  },
  "ts-node": {
    "compilerOptions": {
      "module": "commonjs"
    }
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx"
  ],
  "exclude": [
    "node_modules"
  ]
}
