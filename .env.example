SERVER_ENV=local
DB_API_WRITE_USER=root
DB_API_WRITE_PASSWORD=
DB_API_WRITE_HOST=127.0.0.1
DB_API_WRITE_PORT=3306
DB_API_WRITE_DATABASE=dev-play-skywindgroup-com
SANITY_CLIENT_TOKEN=skSe86CCpb5t8hesiX8513bR
LOGGING_QUERY=true
JWT_ACCESS_SECRET_KEY=aSUHCCZxZUAYkg2Enid1suAvy3bRCDJBCrtb13ZdIY5Dq9Eb2RaSU
JWT_REFRESH_SECRET_KEY=AYUAYkkg2Enid1s12RaSU3Zg2EniddIYZdIY55id1suDq9Eb9Eb2R
SMTP_HOST=smtp.ethereal.email
SMTP_PORT=587
SMTP_SECURE=false
SMTP_REQUIRE_TLS=true
SMTP_USER=<EMAIL>
SMTP_PASSWORD=8p2buXuNApY4XdjcpR
API_URL=http://localhost:3044/api
CLIENT_URL=http://localhost:3044
GRAYLOG_HOST=127.0.0.1
GRAYLOG_PORT=9200
LOGGING_OUTPUT_TYPE=kafka
KAFKA_LOGGIN_HOST=localhost:9092
KAFKA_LOGGING_TOPIC=sw-logging-public-partner-area-stg