// npx tsx ./scripts/logs.ts

import { createReadStream } from "fs";
import { createInterface } from "readline";
import * as console from "node:console";

const referers: Record<string, Record<string, number>> = {};

function parseNdJson(filePath: string): Promise<void> {
    const ndjsonStream = createReadStream(filePath, { encoding: "utf-8" });
    const readLine = createInterface({
        input: ndjsonStream,
        crlfDelay: Infinity
    });

    return new Promise(resolve => {
        readLine.on("line", (line: string) => {
            const data = JSON.parse(line);
            if (data.body.referer) {
                const url = new URL(data.body.referer);
                if (referers[url.host] === undefined) {
                    referers[url.host] = {};
                }
                if (referers[url.host][data.attributes.sanity.endpoint] === undefined) {
                    referers[url.host][data.attributes.sanity.endpoint] = 0;
                }
                referers[url.host][data.attributes.sanity.endpoint] += 1;
            }
        });
        readLine.on("close", () => {
            console.log("NDJson parsing is complete");
            resolve();
        });
    });
}

(async (): Promise<void> => {
    console.time("Parse ndjson");
    await parseNdJson("./scripts/usfaerfs-2024-07-02-2024-07-09.ndjson");
    console.timeEnd("Parse ndjson");
    console.log(referers);
})();
