{"name": "Sanity GraphQL Schema", "schemaPath": "schema.graphql", "extensions": {"endpoints": {"Default GraphQL Endpoint": {"url": "https://usfaerfs.api.sanity.io/v1/graphql/production/default", "headers": {"user-agent": "JS GraphQL", "Authorization": "Bearer skTxuwWO4V7d4m2RZiKlO6wyaIhuFh1OgCGX3HFZJjikFmdp8mCTdMvZEkf00w22RIStd35ZR9iJlfiX870FFfjHOJptUyeWzO5qszKhGRDNDq5VWVwjJpZ3UdAYOjqT7qxOCgzbr4lokKqKZbkGGOFiPbTEvtof3FLUboHpMw8q3Dk5TTbl"}, "introspect": false}}}}