import appConfig from "../../../../src/appConfig";
import { AppModule } from "./appModule";

export class UsersModule {
    app: AppModule;

    constructor() {
        this.app = new AppModule();
    }
    /*

    /!**
     * @see UsersService.register
     * @see UsersController.register
     *!/
    public register(data?: IUserRegisterRequest): Cypress.Chainable<Cypress.Response<IUserRegisterResponse>> {
        return this.app.post({
            url: appConfig.client.endpoints.api.users.register,
            data: ((data as unknown) || {}) as Record<string, unknown>,
        });
    }

    /!**
     * @see UsersService.login
     * @see UsersController.login
     *!/
    public login(data?: IUserRegisterRequest): Cypress.Chainable<Cypress.Response<IUserRegisterResponse>> {
        return this.app.post({
            url: appConfig.client.endpoints.api.users.login,
            data: ((data as unknown) || {}) as Record<string, unknown>,
        });
    }
*/

    /**
     * @see UsersService.activate
     * @see UsersController.activate
     */
    public activate(activationHash: string, data?: unknown): Cypress.Chainable<Cypress.Response<void>> {
        return this.app.get({
            url: appConfig.client.endpoints.api.users.activate.replace(":activationHash", activationHash),
            data: ((data as unknown) || {}) as Record<string, unknown>,
        });
    }

    /**
     * @see UsersService.logout
     * @see UsersController.logout
     */
    public logout(activationHash: string, data?: unknown): Cypress.Chainable<Cypress.Response<void>> {
        return this.app.delete({
            url: appConfig.client.endpoints.api.users.logout,
            data: ((data as unknown) || {}) as Record<string, unknown>,
        });
    }

    /**
     * @see TokenService.refresh
     * @see UsersController.refresh
     */
    public refresh(data?: unknown): Cypress.Chainable<Cypress.Response<void>> {
        return this.app.put({
            url: appConfig.client.endpoints.api.users.refresh,
            data: ((data as unknown) || {}) as Record<string, unknown>,
        });
    }
}
