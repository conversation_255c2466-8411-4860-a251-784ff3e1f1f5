// import * as faker from "faker";

export interface appRequestParams {
    url?: string;
    data?: Record<string, unknown>;
}

export interface appGetParams {
    url: string;
    data: Record<string, unknown>;
}

export interface appPutParams {
    url: string;
    data: Record<string, unknown>;
}

export interface appPostParams {
    url: string;
    data: Record<string, unknown>;
}

enum EnumRequestMethod {
    get = "get",
    post = "post",
    put = "put",
    delete = "delete",
    head = "head",
}

interface AppModuleConfigInterface {
    apiType: string;
    apiUrl: string;
    apiSelected: boolean;
}

export class AppModule {
    public config: AppModuleConfigInterface = {
        apiType: "local",
        apiUrl: "",
        apiSelected: false,
    };

    public api = [
        {
            type: "local",
            apiUrl: "http://api.localhost:3044/api",
        },
    ];

    public getAppConfig(): AppModuleConfigInterface {
        const apiResult = this.api.filter((obj) => {
            return obj.type === this.config.apiType;
        });
        if (Array.isArray(apiResult) && apiResult.length === 1) {
            this.config.apiUrl = apiResult[0].apiUrl;
            this.config.apiSelected = true;
        } else {
            this.config.apiType = "local";
            this.config.apiUrl = this.api[0].apiUrl;
            this.config.apiSelected = false;
        }

        return this.config;
    }

    public get(params: appGetParams): Cypress.Chainable<Cypress.Response<any>> {
        const cfg = this.getAppConfig();
        return cy.request({
            method: "GET",
            log: true,
            url: cfg.apiUrl + params.url,
            encoding: "utf8",
            gzip: true,
            qs: params.data,
            headers: {},
        });
    }

    public delete(params: appGetParams): Cypress.Chainable<Cypress.Response<any>> {
        const cfg = this.getAppConfig();
        return cy.request({
            method: "DELETE",
            log: true,
            url: cfg.apiUrl + params.url,
            encoding: "utf8",
            gzip: true,
            qs: params.data,
            headers: {},
        });
    }

    public head(params: appGetParams): Cypress.Chainable<Cypress.Response<any>> {
        const cfg = this.getAppConfig();
        return cy.request({
            method: "HEAD",
            log: true,
            url: cfg.apiUrl + params.url,
            encoding: "utf8",
            gzip: true,
            qs: params.data,
            headers: {},
        });
    }

    public put(params: appPutParams): Cypress.Chainable<Cypress.Response<any>> {
        const cfg = this.getAppConfig();
        return cy.request({
            method: "PUT",
            log: true,
            url: cfg.apiUrl + params.url,
            encoding: "utf8",
            gzip: true,
            body: params.data,
            headers: {},
        });
    }

    public post(params: appPostParams): Cypress.Chainable<Cypress.Response<any>> {
        const cfg = this.getAppConfig();
        return cy.request({
            method: "POST",
            log: true,
            url: cfg.apiUrl + params.url,
            encoding: "utf8",
            gzip: true,
            body: params.data,
            headers: {},
        });
    }

    public getFakerDOB(): string {
        return "";
        // const dob = faker.date.past(50, new Date("Sat Sep 20 1992 21:35:02 GMT+0200 (CEST)"));
        // return dob.getFullYear() + "-" + (dob.getMonth() + 1) + "-" + dob.getDate(); // First month is "1"
    }

    public checkGetResponse(response: Cypress.Response<unknown>): void {
        expect(response).to.include.keys("body", "headers", "requestHeaders", "status", "statusText");

        this.checkResponseHeaders(response, EnumRequestMethod.get);
    }

    public checkPutEmptyResponse(response: Cypress.Response<unknown>): void {
        expect(response).to.include.keys("headers", "requestHeaders", "status", "statusText");

        this.checkResponseHeaders(response, EnumRequestMethod.put);
    }

    public checkPutResponse(response: Cypress.Response<unknown>): void {
        expect(response).to.include.keys("body", "headers", "requestHeaders", "status", "statusText");

        this.checkResponseHeaders(response, EnumRequestMethod.put);
    }

    public checkPostResponse(response: Cypress.Response<unknown>): void {
        expect(response).to.include.keys("body", "headers", "requestHeaders", "status", "statusText");

        this.checkResponseHeaders(response, EnumRequestMethod.post);
    }

    public checkHeadResponse(response: Cypress.Response<unknown>): void {
        expect(response).to.include.keys("body", "headers", "requestHeaders", "status", "statusText");

        this.checkResponseHeaders(response, EnumRequestMethod.head);
    }

    public buildQueryParams(data = {}): string {
        const query = new URLSearchParams(data).toString();
        return query.length > 0 ? "?" + query : "";
    }

    public arrayRandom(arr: unknown[]): unknown {
        return arr[Math.floor(Math.random() * arr.length)];
    }

    public checkResponseHeaders(response: Cypress.Response<any>, method: EnumRequestMethod): void {
        // if ("config" in response && "method" in response.config) {
        // console.log(response);
        if (method === EnumRequestMethod.get) {
            expect(response.headers).to.be.a("object");
            expect(response.headers).to.include.keys(/*'content-length',*/ "content-type", "etag");
            // response.headers['content-length'].should.be.a('string');
            expect(response.headers["content-type"]).to.be.a("string");
            expect(response.headers["content-type"]).to.match(/(application\/json)/);
            expect(response.headers["content-type"]).to.match(/(charset=utf-8)/);
            expect(response.headers["etag"]).to.be.a("string");
        } else if (method === EnumRequestMethod.put || method === EnumRequestMethod.post) {
            expect(response.headers).to.be.a("object");
            expect(response.headers).to.have.any.keys(
                /*'content-length',*/
                "content-type",
                "etag"
            );

            if (response.headers["content-type"]) {
                expect(response.headers["content-type"]).to.be.a("string");
                expect(response.headers["content-type"]).to.match(/(application\/json)/);
                expect(response.headers["content-type"]).to.match(/(charset=utf-8)/);
            }
            if (response.headers["etag"]) {
                expect(response.headers["etag"]).to.be.a("string");
            }
        }
        // }
    }
}
