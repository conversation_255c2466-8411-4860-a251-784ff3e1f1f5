// import faker = require("faker");
// import { AppModule } from "./modules/appModule";
// import { UsersModule } from "./modules/usersModule";

// const users = new UsersModule();
// const app = new AppModule();

context("Users", () => {
    // const userLoginPass = faker.internet.email();

    it("Register, activate, login, refresh, logout", () => {
        /*users
            .register({
                email: "", // userLoginPass,
                password: "", // userLoginPass,
                verifyPassword: "", // userLoginPass,
                firstName: "", // faker.name.firstName(),
                lastName: "", // faker.name.lastName(),
                company: "", // faker.company.companyName(),
                companyPosition: "", // faker.name.jobTitle(),
            })
            .should(async (registerResponse) => {
                app.checkPostResponse(registerResponse);
                expect(typeof registerResponse).to.equal("object");
                expect(registerResponse).to.include({ status: 201 });
                const body = registerResponse.body;

                expect(typeof body).to.equal("object");
                // if (body) {
                // }
            });*/
    });
});
