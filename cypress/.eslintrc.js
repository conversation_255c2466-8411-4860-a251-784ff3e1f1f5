module.exports = {
    root: true,
    parser: "@typescript-eslint/parser",
    plugins: ["simple-import-sort", "cypress"],
    parserOptions: {
        ecmaVersion: 2020,
        sourceType: "module",
    },
    settings: {},
    env: {
        browser: true,
        amd: true,
        node: true,
        commonjs: true,
    },
    extends: [
        "eslint:recommended",
        "plugin:@typescript-eslint/eslint-recommended",
        "plugin:@typescript-eslint/recommended",
        "plugin:cypress/recommended",
        // "prettier/@typescript-eslint",
        "plugin:prettier/recommended", // Make sure this is always the last element in the array.
    ],
    rules: {
        "prettier/prettier": [
            "error",
            {
                tabWidth: 4,
                printWidth: 120,
                singleQuote: false,
                bracketSpacing: true,
                parser: "typescript",
                endOfLine: "auto",
            },
            { usePrettierrc: true },
        ],
        "@typescript-eslint/explicit-function-return-type": "off",
        "simple-import-sort/imports": "error",
        "simple-import-sort/exports": "error",
        "no-prototype-builtins": "off",
        "cypress/no-assigning-return-values": "error",
        "cypress/no-unnecessary-waiting": "error",
        "cypress/assertion-before-screenshot": "warn",
        "cypress/no-force": "warn",
        "cypress/no-async-tests": "error",
    },
};
