// const path = require("path");
//
/*
const euSites = [
    { domain: "dev-eu-website.loca.lt" },
    { domain: "eu.skywindgroup.localhost" },
    { domain: "dev-eu-website.skywindgroup.com" },
    { domain: "skywindgroup.com" },
    { domain: "eu.skywindgroup.com" },
    { domain: "www.skywindgroup.com" }
];

const asiaSites = [
    { domain: "asiaskywind.localhost" },
    { domain: "dev-asia-website.skywindgroup.com" },
    { domain: "asiaskywind.com" }
];

const chinaSites = [
    { domain: "asia.skywindgroup.localhost" },
    { domain: "dev-china-website.skywindgroup.com" },
    { domain: "asia.skywindgroup.com" }
];

const pa = [
    { domain: "partnerarea.skywindgroup.localhost" },
    { domain: "dev-partnerarea.skywindgroup.com" },
    { domain: "partnerarea.skywindgroup.com" }
];
*/

/**
 * @type {import("next").NextConfig}
 */
const nextConfig = {
    // ... rest of the configuration.
    // output: 'standalone',
    // time in seconds of no pages generating during static
    // generation before timing out
    // staticPageGenerationTimeout: 60,
    reactStrictMode: true,
    // With Next.js 12, we introduced minification using SWC as part of the Next.js Compiler.
    // Early results showed it was 7x faster than Terser.
    // Minification with SWC is now in Release Candidate (RC) with 12.1 and will become the default in 12.2.
    // swcMinify: true,
    images: {
        domains: [
            "cdn.skywindgroup.com",
            "asia.skywindgroup.com",
            "asiaskywind.com",
            "eu.skywindgroup.com",
            "www.skywindgroup.com",
            "play.skywindgroup.com",
            "dev-partnerarea.skywindgroup.com",
            "partnerarea.skywindgroup.com",
            "partnerarea.skywindgroup.localhost",
            "cdn.sanity.io"
        ],
        minimumCacheTTL: 3600
    },
    i18n: {
        /**
         * Provide the locales you want to support in your application
         */
        locales: ["en-US"],
        /**
         * This is the default locale you want to be used when visiting
         * a non-locale prefixed path.
         */
        defaultLocale: "en-US"
    },
    async rewrites() {
        return [
            {
                source: '/api/local/:path*',
                destination: 'http://web-services.localhost:3080/:path*' // Proxy to Backend
            },
            {
                source: '/api/dev/:path*',
                destination: 'https://dev-web-services.skywindgroup.com/:path*' // Proxy to Backend
            },
            {
                source: '/api/prod/:path*',
                destination: 'https://web-services.skywindgroup.com/:path*' // Proxy to Backend
            }
        ]
    },
    // Prefer loading of ES Modules over CommonJS
    // experimental: { esmExternals: true },
    experimental: {
        // images: {
        //     allowFutureImage: true
        // }
    }
    /*async rewrites() {
        return {
            afterFiles: [
                // {
                //     source: "/api/:path*",
                //     destination: "/api/:path*"
                // },
                {
                    source: "/:path*",
                    has: [
                        {
                            type: "host",
                            value: "(" + euSites.map((s) => s.domain).join(")|(") + ")"
                        }
                    ],
                    destination: "/eu/:path*"
                    //destination: "/site/:path*"
                },
                {
                    source: "/:path*",
                    has: [
                        {
                            type: "host",
                            value: "(" + asiaSites.map((s) => s.domain).join(")|(") + ")"
                        }
                    ],
                    destination: "/asia/:path*"
                },
                {
                    source: "/:path*",
                    has: [
                        {
                            type: "host",
                            value: "(" + chinaSites.map((s) => s.domain).join(")|(") + ")"
                        }
                    ],
                    destination: "/china/:path*"
                },
                {
                    source: "/:path*",
                    has: [
                        {
                            type: "host",
                            value: "(" + pa.map((s) => s.domain).join(")|(") + ")"
                        }
                    ],
                    destination: "/pa/:path*"
                }
            ]
        };
    }*/
    // env: {
    //     // CF_SPACE_ID: process.env.CF_SPACE_ID,
    // },
    // typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    // ignoreBuildErrors: true,
    // }
};

module.exports = nextConfig;