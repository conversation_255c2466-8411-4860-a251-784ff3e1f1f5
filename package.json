{"name": "partner<PERSON>a", "version": "0.6.172", "private": true, "scripts": {"dev:server": "NODE_OPTIONS='--inspect' gulp bump && nodemon", "dev": "NODE_OPTIONS='--inspect' gulp bump && next dev -p 3045", "build": "next build", "start": "next start -p 3044", "build-start": "next build && cross-env DEV=true next dev -p 3044", "compile:cypress": "gulp compile-cypress", "debug": "node --inspect ./node_modules/next/dist/bin/next -p 3044", "export": "next export", "lint": "next lint . --ext '.ts,.tsx,.js,.jsx'", "node:kill:windows": "taskkill /f /im node.exe", "port:check": "netstat -ano | find \"3044\"", "prettier": "prettier --write 'src/**/*.{jsx,js,ts,tsx}'", "prettier:check": "prettier --check 'src/**/*.{jsx,js,ts,tsx}'", "start:cypress": "cypress open --config-file cypress/cypress.json"}, "engines": {"node": ">=16.14.2", "npm": ">=8.5.0"}, "dependencies": {"axios": "^0.27.2", "bcrypt": "^5.1.0", "bole": "^5.0.0", "bole-console": "^0.1.10", "bootstrap": "^5.2.1", "bump": "^0.2.5", "cookie": "^0.5.0", "cookies-next": "^2.1.1", "cors": "^2.8.5", "dayjs": "^1.11.5", "formik": "^2.2.9", "gelf-stream": "^1.1.1", "gulp": "^5.0.0", "gulp-bump": "^3.2.0", "gulp-debug": "^4.0.0", "gulp-git": "^2.10.1", "gulp-sourcemaps": "^3.0.0", "gulp-typescript": "^6.0.0-alpha.1", "hashids": "^2.2.10", "hls.js": "^1.2.4", "jsonwebtoken": "^8.5.1", "kafka-node": "^5.0.0", "mobx": "^6.6.2", "mobx-react-lite": "^3.4.0", "moment": "^2.29.4", "mysql2": "^2.3.3", "next": "12.3.1", "next-seo": "^5.5.0", "next-sitemap": "^3.1.23", "nextjs-google-analytics": "^2.1.0", "path-to-regexp": "^6.2.1", "postcss": "^8.4.16", "rc-slider": "^10.0.1", "react": "18.2.0", "react-bootstrap": "^2.5.0", "react-countup": "^6.3.1", "react-device-detect": "^2.2.2", "react-dom": "18.2.0", "react-hls-player": "^3.0.7", "react-icons": "^4.4.0", "react-phone-number-input": "^3.2.11", "react-tagify": "^0.1.4", "react-toastify": "^9.0.8", "react-virtuoso": "^2.19.0", "request-ip": "^3.3.0", "sass": "^1.55.0", "sequelize": "^6.23.2", "sequelize-auto": "=0.8.8", "sequelize-cli": "^6.5.1", "slugify": "^1.6.5", "swiper": "^8.4.2", "uuid": "^9.0.0", "yup": "^0.32.11", "yup-password": "^0.2.2"}, "devDependencies": {"@openapitools/openapi-generator-cli": "^2.13.4", "@react-icons/all-files": "^4.1.0", "@sanity/block-content-to-react": "^3.0.0", "@sanity/client": "^3.4.1", "@types/bcrypt": "^5.0.0", "@types/cors": "^2.8.12", "@types/express": "^4.17.14", "@types/gtag.js": "^0.0.11", "@types/gulp": "^4.0.9", "@types/hls.js": "^1.0.0", "@types/jsonwebtoken": "^8.5.9", "@types/node": "^18.7.23", "@types/react": "^18.0.21", "@types/react-dom": "^18.0.6", "@types/react-transition-group": "^4.4.5", "@types/request-ip": "^0.0.37", "@types/uuid": "^8.3.4", "@types/validator": "^13.7.7", "@types/yaireo__tagify": "^4.16.0", "@types/yup": "^0.29.14", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "@yaireo/tagify": "^4.16.4", "autoprefixer": "^10.4.12", "cssnano": "^5.1.13", "cypress": "^10.9.0", "eslint": "^8.24.0", "eslint-config-next": "12.3.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-file-progress": "^1.3.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsdoc": "^39.3.6", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-simple-import-sort": "^8.0.0", "graphql": "^15.8.0", "graphql-schema-typescript": "^1.5.2", "gulp-file": "^0.4.0", "gulp-postcss": "^9.0.1", "gulp-rename": "^2.0.0", "gulp-sass": "^5.1.0", "jest": "^29.1.2", "nodemon": "^2.0.20", "postcss-loader": "7.0.1", "prettier": "^2.7.1", "ts-node": "^10.9.1", "typescript": "^4.8.4"}, "prettier": {"tabWidth": 4, "printWidth": 120, "singleQuote": false, "bracketSpacing": true, "parser": "typescript", "endOfLine": "auto"}, "packageManager": "yarn@3.2.0"}