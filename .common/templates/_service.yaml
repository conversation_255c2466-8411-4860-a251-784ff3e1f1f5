{{- define "common.service" -}}
{{- $common := dict "Values" .Values.common -}}
{{- $noCommon := omit .Values "common" -}}
{{- $overrides := dict "Values" $noCommon -}}
{{- $noValues := omit . "Values" -}}
{{- with merge $noValues $overrides $common -}}
apiVersion: v1
kind: Service
{{ template "common.metadata" . }}
spec:
  {{ if .Values.clusterIP }}
  clusterIP: {{ .Values.clusterIP }}
  {{ end }}
  ports:
    {{- range .Values.servicePorts }}
      {{ $parts := split ":" . }}
      {{ if (split ":" .)._3 }}
      # nodePort defined: port:targetPort:nodePort:name
    - name: {{ (printf "%s" $parts._3) }}
      port: {{ (split ":" .)._0 }}
      protocol: TCP
      targetPort: {{ (split ":" .)._1 }}
      nodePort: {{ (split ":" .)._2 }}
      {{ else }}
      # nodePort is missing: port:targetPort:name
    - name: {{ (printf "%s" $parts._2) }}
      port: {{ (split ":" .)._0 }}
      protocol: TCP
      targetPort: {{ (split ":" .)._1 }}
      {{ end }}
    {{- end }}
  {{ if .Values.selector }}  
  selector:
    {{- range $key, $val := .Values.selector }}
    {{ $key }}: {{ $val }}
    {{- end }} 
  {{ else }} 
  selector:
    app: {{ include "common.name" . }}
    app.kubernetes.io/name: {{ include "common.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}     
  {{ end }}    
  {{ if .Values.sessionAffinity }}
  sessionAffinity: [{{ .Values.sessionAffinity }}]
  {{ end }}
  type: {{ .Values.serviceType }}
{{- end -}}
{{- end -}}
