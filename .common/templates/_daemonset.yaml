{{- define "common.daemonset" -}}
{{- $common := dict "Values" .Values.common -}} 
{{- $noCommon := omit .Values "common" -}} 
{{- $overrides := dict "Values" $noCommon -}} 
{{- $noValues := omit . "Values" -}} 
{{- with merge $noValues $overrides $common -}}
apiVersion: apps/v1
kind: DaemonSet
metadata:
  labels:
    daemonset: {{ include "common.name" . }}
  name: {{ include "common.name" . }}
  namespace: {{ include "common.namespace" . }}
spec:
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      daemonset: {{ include "common.name" . }}
  template:
    metadata:
      annotations:
        example.io/scrape_port: "4004"
        example.io/should_be_scraped: "true"
      labels:
        daemonset: {{ include "common.name" . }}
    spec:
      containers:
      - name: {{ include "common.name" . }}  
      {{ if .Values.containerCommand }}
        command: [{{ .Values.containerCommand }}]
      {{ end }}
      {{ if .Values.containerCommandArgs }}
        args: 
          {{- range .Values.containerCommandArgs }}
            - {{ . }} 
            {{- end}}
      {{ end }}
        envFrom:
        - configMapRef:
            name: {{ .Values.name }}
            optional: false
        - secretRef:
            name: {{ .Values.name }}-secrets
            optional: true
      {{ if .Values.image.checksum }}
        image: {{ (printf "%s@%s" .Values.image.repository .Values.image.checksum) }}
      {{ else if .Values.image.tag }}
        image: {{ (printf "%s:%s" .Values.image.repository .Values.image.tag) }}
      {{ end }}
      {{ if .Values.image.pullPolicy }}
        imagePullPolicy: {{ .Values.image.pullPolicy }}
      {{ else }}
        imagePullPolicy: IfNotPresent
      {{ end }}
        ports:
        {{- range .Values.containerPorts }}
          {{ $parts := split ":" . }}
        - containerPort: {{ (split ":" .)._0 }}
          protocol: TCP
          name: {{ (printf "%s" $parts._1) }}
        {{- end }}
        resources:
{{ toYaml .Values.resources | indent 12 }}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      imagePullSecrets:
      - name: docker-config
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
  updateStrategy:
    rollingUpdate:
      maxUnavailable: 1
    type: RollingUpdate
{{- end -}}
{{- end -}}    