{{- define "common.deployment" -}}
{{- $common := dict "Values" .Values.common -}}
{{- $noCommon := omit .Values "common" -}}
{{- $overrides := dict "Values" $noCommon -}}
{{- $noValues := omit . "Values" -}}
{{- with merge $noValues $overrides $common -}}
apiVersion: apps/v1
kind: Deployment
{{ template "common.metadata" . }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "common.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
      app: {{ include "common.name" . }}
  strategy:
    {{ if .Values.rollingUpdate }}
    rollingUpdate:
      {{ if .Values.rollingUpdate.maxSurge }}
      maxSurge: {{ .Values.rollingUpdate.maxSurge }}
      {{ else }}
      maxSurge: 1
      {{ end }}
      {{ if .Values.rollingUpdate.maxUnavailable }}
      maxUnavailable: {{ .Values.rollingUpdate.maxUnavailable }}
      {{ else }}
      maxUnavailable: 1
      {{ end }}
    {{ end }}
    type: RollingUpdate
  template:
    metadata:
      annotations:
        example.io/scrape_port: '4004'
        example.io/should_be_scraped: 'true'
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        {{ if .Values.image.imageUpdaterList }}
        argocd-image-updater.argoproj.io/image-list: {{ .Values.image.imageUpdaterList }}
        {{ end }}
      labels:
        app.kubernetes.io/name: {{ include "common.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
        app: {{ include "common.name" . }}
        {{- range $key, $val := .Values.labels }}
        {{ $key }}: {{ $val }}
        {{- end }}       
    spec:
      containers:
        - name: {{ .Chart.Name }}
          envFrom:
          - configMapRef:
              name: {{ .Values.name }}
              optional: false
          - secretRef:
              name: {{ .Values.name }}-secrets
              optional: true
        {{ if .Values.containerCommand }}
          command: [{{ .Values.containerCommand }}]
        {{ end }}
        {{ if .Values.containerCommandArgs }}
          args:
            {{- range .Values.containerCommandArgs }}
             - {{ . }}
             {{- end}}
        {{ end }}
        {{ if .Values.image.checksum }}
          image: {{ (printf "%s@%s" .Values.image.repository .Values.image.checksum) }}
        {{ else if .Values.image.tag }}
          image: {{ (printf "%s:%s" .Values.image.repository .Values.image.tag) }}
        {{ end }}
        {{ if .Values.image.pullPolicy }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
        {{ else }}
          imagePullPolicy: IfNotPresent
        {{ end }}
        {{ if .Values.containerPorts }}
          ports:
          {{- range .Values.containerPorts }}
            {{ $parts := split ":" . }}
          - containerPort: {{ (split ":" .)._0 }}
            protocol: TCP
            name: {{ (printf "%s" $parts._1) }}
          {{- end }}
        {{ end }}
          {{ if .Values.volume }}
          volumeMounts:
            - name: {{ .Values.volume.volumeMounts.name }}
              mountPath: {{ .Values.volume.volumeMounts.mountPath }}
              {{ if .Values.volume.volumeMounts.readOnly }}
              readOnly: {{ .Values.volume.volumeMounts.readOnly }}
              {{- end }}
          {{- end }}
          resources:
{{ toYaml .Values.resources | indent 12 }}
          readinessProbe:
{{ toYaml .Values.readinessProbe | indent 12 }}
{{- with .Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | indent 8 }}
{{- end }}
{{- with .Values.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
{{- end }}
{{- with .Values.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
{{- end }}
    {{ if .Values.volume }}
      volumes:
        - name: {{ .Values.volume.volumes.name }}
          {{ if .Values.volume.volumes.secret }}
          secret:
            defaultMode: {{ .Values.volume.volumes.secret.defaultMode }}
            secretName: {{ .Values.volume.volumes.secret.secretName }}
          {{ end }}
    {{ end }}
      imagePullSecrets:
      - name: docker-config
    {{ if .Values.serviceAccount }}
      serviceAccount: {{ .Values.serviceAccount }}
      serviceAccountName: {{ .Values.serviceAccountName }}
    {{ end }}
      dnsPolicy: ClusterFirst
      restartPolicy: Always
    {{ if .Values.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
    {{ end }}
    {{ if .Values.hostAliases }}
      hostAliases:
        {{- range .Values.hostAliases }}
        - hostnames:
        {{ $parts := split ":" . }}
          - {{ (printf "%s" $parts._0) }}
          ip: {{ (split ":" .)._1 }}
        {{- end }}
    {{- end -}}
{{- end -}}
{{- end -}}
