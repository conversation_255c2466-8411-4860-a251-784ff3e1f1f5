{{- define "common.cronjob-multi" -}}
{{- range $job, $val := .Values.crons }}
apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: {{ .name }}
  namespace: {{ .namespace }}
spec:
  schedule: "{{ .schedule }}"
{{ if .successfulJobsHistoryLimit }}
  successfulJobsHistoryLimit: {{ .successfulJobsHistoryLimit }}
{{ else }}
  successfulJobsHistoryLimit: 1
{{ end }} 
{{ if .failedJobsHistoryLimit }}
  failedJobsHistoryLimit: {{ .failedJobsHistoryLimit }}
{{ else }}
  failedJobsHistoryLimit: 1
{{ end }} 
{{ if .concurrencyPolicy }}
  concurrencyPolicy: {{ .concurrencyPolicy }}
{{ else }}
  concurrencyPolicy: Replace
{{ end }} 
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: {{ .name }}
            {{ if .image.checksum }}
              image: {{ (printf "%s@%s" .image.repository .image.checksum) }}
            {{ else if .image.tag }}
              image: {{ (printf "%s:%s" .image.repository .image.tag) }}
            {{ end }}
            {{ if .image.pullPolicy }}
              imagePullPolicy: {{ .image.pullPolicy }}
            {{ else }}
              imagePullPolicy: IfNotPresent
            {{ end }}
            {{ if .env }}
              env: 
{{ toYaml .env | indent 18 }}
            {{ end }}
            {{ if .containerCommandArgs }}
              args: 
                {{- range .containerCommandArgs }}
                  - {{ . }} 
                  {{- end}}
            {{ end }}
          restartPolicy: Never
          imagePullSecrets:
            - name: docker-config
---
{{- end}}
{{- end -}}