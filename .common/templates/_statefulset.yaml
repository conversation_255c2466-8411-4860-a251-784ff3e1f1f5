{{- define "common.statefulset" -}}
{{- $common := dict "Values" .Values.common -}} 
{{- $noCommon := omit .Values "common" -}} 
{{- $overrides := dict "Values" $noCommon -}} 
{{- $noValues := omit . "Values" -}} 
{{- with merge $noValues $overrides $common -}}
apiVersion: apps/v1
kind: StatefulSet
{{ template "common.metadata" . }}
spec:
  serviceName: {{ .Values.name }}
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "common.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
      k8s-app: {{ include "common.name" . }}
  volumeClaimTemplates:
  - metadata:
      name: {{ .Values.volumeClaimTemplates.metadata.name }}
    spec:
      accessModes: {{ .Values.volumeClaimTemplates.spec.accessModes }}
      storageClassName: {{ .Values.volumeClaimTemplates.spec.storageClassName }}
      resources:
        requests:
          storage: {{ .Values.volumeClaimTemplates.spec.resources.requests.storage }}
  template:
    metadata:
      annotations:
        example.io/scrape_port: '4004'
        example.io/should_be_scraped: 'true'
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
      labels:
        app.kubernetes.io/name: {{ include "common.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
        k8s-app: {{ include "common.name" . }}
        {{- range $key, $val := .Values.labels }}
        {{ $key }}: {{ $val }}
        {{- end }}        
    spec:
      containers:
        - name: {{ .Chart.Name }}
          envFrom:
          - configMapRef:
              name: {{ .Values.name }}
              optional: false
          - secretRef:
              name: {{ .Values.name }}-secrets
              optional: true
        {{ if .Values.containerCommand }}
          command: [{{ .Values.containerCommand }}]
        {{ end }}
        {{ if .Values.containerCommandArgs }}
          args: 
            {{- range .Values.containerCommandArgs }}
             - {{ . }} 
             {{- end}}
        {{ end }}
        {{ if .Values.image.checksum }}
          image: {{ (printf "%s@%s" .Values.image.repository .Values.image.checksum) }}
        {{ else if .Values.image.tag }}
          image: {{ (printf "%s:%s" .Values.image.repository .Values.image.tag) }}
        {{ end }}
        {{ if .Values.image.pullPolicy }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
        {{ else }}
          imagePullPolicy: IfNotPresent
        {{ end }}
          ports:
          {{- range .Values.containerPorts }}
            {{ $parts := split ":" . }}
          - containerPort: {{ (split ":" .)._0 }}
            protocol: TCP
            name: {{ (printf "%s" $parts._1) }}
          {{- end }}
          resources:
{{ toYaml .Values.resources | indent 12 }}
          volumeMounts:
            - name: {{ .Values.volumeMounts.name }}
              mountPath: {{ .Values.volumeMounts.mountPath }}
          resources:
{{ toYaml .Values.resources | indent 12 }}
          readinessProbe:
{{ toYaml .Values.readinessProbe | indent 12 }}              
{{- with .Values.nodeSelector }}
      nodeSelector:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
{{ toYaml . | indent 8 }}
    {{- end }}
      imagePullSecrets:
      - name: docker-config
    {{ if .Values.serviceAccount }}      
      serviceAccount: {{ .Values.serviceAccount }}
      serviceAccountName: {{ .Values.serviceAccountName }}
    {{ end }}      
      dnsPolicy: ClusterFirst
      restartPolicy: Always      
    {{ if .Values.hostAliases }}
      hostAliases:
        {{- range .Values.hostAliases }}
        - hostnames:
        {{ $parts := split ":" . }}
          - {{ (printf "%s" $parts._0) }}
          ip: {{ (split ":" .)._1 }}
        {{- end }}
    {{- end -}}
{{- end -}}
{{- end -}}